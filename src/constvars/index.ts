export const ver_to_project = {
  8: 'PAC1',
  7: 'FYPUS1',
  6: 'PowerThor',
  5: 'PSC4',
  4: 'PUS4',
  3: 'PUS3',
  2: 'PowerSwap2',
  1: 'PowerSwap',
} as any;

export const projectMap = {
  FYPUS1: {
    name: 'menu.pp_firefly1',
    color: '#2F54EB'
  },
  PUS4: {
    name: 'menu.pp_swap_station4',
    color: '#21819D'
  },
  PUS3: {
    name: 'menu.pp_swap_station3',
    color: '#01A0AC'
  },
  PowerSwap2: {
    name: 'menu.pp_swap_station2',
    color: '#2F9C74'
  },
  PowerSwap: {
    name: 'menu.pp_swap_station1',
    color: '#FD8C08'
  }
} as any

export const backend_url = {
  dev: 'api-welkin.nioint.com',
  test: 'api-welkin-backend-test.nioint.com',
  stg: 'api-welkin-backend-stg.nioint.com',
  prod: 'api-welkin-backend.nioint.com',
  'stg-eu': 'api-welkin-backend-stg-eu.nioint.com',
  eu: 'api-welkin-backend-eu.nioint.com',
};

export const pagination = {
  pageSizes: [5, 10, 20, 50],
  layout: 'total, sizes, prev, pager, next, jumper',
};
