import * as echarts from 'echarts'
type EChartsOption = echarts.EChartsOption
const colors = ['#6697FF', '#5ECFFF', '#67C23A', '#FFC031', '#00BEBE', '#9FDB1D', '#FADC19', '#F7BA1E', '#FF7D00', '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83']
export const chart_options: EChartsOption = {
  color: colors,
  tooltip: {
    trigger: 'axis',
    confine: true,
    backgroundColor: 'rgba(255,255,255,0.5)',
    formatter: function (params: any) {
      let leftSide = ''
      let rightSide = ''
      let middle = Math.ceil(params.length / 2)
      params.forEach((item: any, index: number) => {
        if (index < middle) {
          leftSide += `<span>
          <span class="chart-tooltip-color" style="display: inline-block; background-color: ${item.color}; border-radius: 50%; width: 10px; height: 10px; margin-right: 5px"></span>
          <span> ${item.seriesName} : ${item.data} </span>
          </span>`
        } else {
          rightSide += `<span>
          <span class="chart-tooltip-color" style="display: inline-block; background-color: ${item.color}; border-radius: 50%; width: 10px; height: 10px; margin-right: 5px"></span>
          <span> ${item.seriesName} : ${item.data} </span>
          </span>`
        }
      })
      let tip = `<div>${params[0].axisValueLabel}</div>
      <div style="display: flex; justify-content: flex-between">
                  <div style="display: flex; flex-direction: column">${leftSide}</div>
                  <div style="display: flex; flex-direction: column; margin-left: 20px">${rightSide}</div>
                </div>`
      return tip
    }
  },

  legend: {
    icon: 'circle',
    width: '90%',
    padding: 0,
    right: 0,
    top: 6,
    itemWidth: 10,
    itemHeight: 10,
    type: 'scroll',
    textStyle: {
      color: '#595959'
    }
  },
  grid: {
    top: '40',
    left: '0%',
    right: '2',
    bottom: '35',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisTick: {
      show: false
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#D9D9D9'
      }
    }
  },
  dataZoom: [
    {
      height: 16,
      bottom: 4,
      right: 8,
      start: 0,
      end: 100,
      zoomOnMouseWheel: false,
      fillerColor: 'rgba(0, 190, 190, 0.1)',
      borderColor: 'rgba(0, 190, 190, 0.1)',
      backgroundColor: '#fff',
      moveHandleSize: 0,
      handleSize: 20,
      handleIcon: 'path://M1,0.5h10a1,1,0,0,1,1,1v17a1,1,0,0,1-1,1H1a1,1,0,0,1-1-1V1.5A1,1,0,0,1,1,0.5Z M4.28577 4.16667V15.8333 M7.71436 4.16667V15.8333',
      selectedDataBackground: {
        areaStyle: {
          color: 'rgba(0, 190, 190, 0.1)'
        },
        lineStyle: {
          color: '#01A0AC'
        }
      }
    }
  ]
}

export const stepOption = {
  animation: false,
  color: ['#00bebe'],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    top: '10',
    left: '0%',
    right: '8',
    bottom: '35',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisTick: {
      show: false
    },
    data: [],
    axisLabel: {
      show: true,
      color: '#8C8C8C',
      align: 'left',
      alignMaxLabel: 'right'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    }
  },
  yAxis: {
    type: 'category',
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    }
  },
  dataZoom: [
    {
      height: 16,
      bottom: 4,
      right: 8,
      start: 0,
      end: 100,
      zoomOnMouseWheel: false,
      fillerColor: 'rgba(0, 190, 190, 0.1)',
      borderColor: 'rgba(0, 190, 190, 0.1',
      backgroundColor: '#fff',
      moveHandleSize: 0,
      handleSize: 20,
      handleIcon: 'path://M1,0.5h10a1,1,0,0,1,1,1v17a1,1,0,0,1-1,1H1a1,1,0,0,1-1-1V1.5A1,1,0,0,1,1,0.5Z M4.28577 4.16667V15.8333 M7.71436 4.16667V15.8333',
      selectedDataBackground: {
        areaStyle: {
          color: 'rgba(0, 190, 190, 0.1)'
        },
        lineStyle: {
          color: '#01A0AC'
        }
      }
    }
  ],
  series: [
    {
      data: [] as any,
      step: 'middle',
      type: 'line',
      symbol: 'none'
    }
  ]
}
