export const di_varname = {
  emergency_stop_switch_01: '急停',
  liq_level_warning: '液位报警',
  I_power_st_1: '扩展箱控制供电脱扣报警',
  I_power_st_2: '扩展箱动力供电脱扣报警',
  I_power_st_3: '堆垛机货叉伺服供电脱扣报警',
  I_power_st_4: '堆垛机行走伺服供电脱扣报警',
  I_power_st_5: '堆垛机升降伺服供电脱扣报警',
  I_power_st_6: '刹车开关电源后端配电脱扣报警',
  I_power_st_7: '前轮推杆伺服供电脱扣报警',
  I_power_st_8: '后轮推杆伺服供电脱扣报警',
  I_power_st_9: 'V槽&导向条伺服供电脱扣报警',
  I_power_st_10: '车身定位销伺服供电脱扣报警',
  I_power_st_11: '拧紧枪1 伺服供电脱扣报警',
  I_power_st_12: '拧紧枪2 伺服供电脱扣报警',
  I_power_st_13: '拧紧枪3&4 伺服供电脱扣报警',
  I_power_st_14: '拧紧枪5&6 伺服供电脱扣报警',
  I_power_st_15: '拧紧枪7&8 伺服供电脱扣报警',
  I_power_st_16: '拧紧枪9 伺服供电脱扣报警',
  I_power_st_17: '拧紧枪10 伺服供电脱扣报警',
  I_power_st_18: '拧紧枪11 伺服供电脱扣报警',
  I_power_st_19: '拧紧枪12 伺服供电脱扣报警',
  I_power_st_20: '开阖门伺服供电脱扣报警',
  I_power_st_21: 'RGV伺服供电脱扣报警',
  I_power_st_22: '车辆举升伺服供电脱扣报警',
  I_power_st_23: '接驳位举升伺服供电脱扣报警',
  I_power_st_24: '变频器供电脱扣报警',
  A01_A1_check: 'A01A1检测',
  A01_A2_check: 'A01A2检测',
  A01_A03_check: 'A01A3检测',
  A01_A04_check: 'A01A4检测',
  A01_A05_check: 'A01A5检测',
  A01_A6_check: 'A01A6检测',
  A01_A7_check: 'A01A7检测',
  A01_A8_check: 'A01A8检测',
  A01_A9_check: 'A01A9检测',
  A01_A10_check: 'A01A10检测',
  A02_A1_module_status: 'A02A1模块状态',
  A02_A2_module_status: 'A02A2模块状态',
  A02_A3_module_status: 'A02A3模块状态',
  A02_A4_module_status: 'A02A4模块状态',
  A02_A5_module_status: 'A02A5模块状态',
  A02_A06_module_check: 'A02A6模块状态'
}

export const di_varname_pus4 = {
  emergency_stop_switch_01: '急停',
  trans_fire_stopper_retract: '一键转运消防仓落水',
  maintain_operate_swicth: '维护/运营模式',
  A01_A1_check: 'A01A1检测',
  I_power_st_1: '堆垛机升降伺服供电脱扣报警',
  I_power_st_2: '堆垛机货叉&行走伺服供电脱扣报警',
  I_power_st_3: '拧紧枪1&拧紧枪2&拧紧枪3供电脱扣报警',
  I_power_st_4: '拧紧枪4&拧紧枪5&拧紧枪6供电脱扣报警',
  I_power_st_5: '拧紧枪7&拧紧枪8&拧紧枪9供电脱扣报警',
  I_power_st_6: '拧紧枪10&拧紧枪11&拧紧枪12供电脱扣报警',
  I_power_st_7: '左开合门&右开合门&拧紧枪1升降&拧紧枪2升降&供电脱扣报警',
  I_power_st_8: '拧紧枪9升降&拧紧枪10升降&拧紧枪11升降&拧紧枪12升降供电脱扣报警',
  I_power_st_9: '左前电池定位销&左后电池定位销&前导向条&后导向条&供电脱扣报警',
  I_power_st_10: '左前车身定位销&左后车身定位销&右后车身定位销&供电脱扣报警',
  I_power_st_11: '左前轮推杆伺服&右前轮推杆伺服&左后轮推杆伺服&右后轮推杆伺服&供电脱扣报警',
  I_power_st_12: 'RGV行走&RGV举升供电脱扣报警',
  I_power_st_13: '变频器供电脱扣报警',
  I_water_press_up: '水流量压力值上限',
  I_power_st_14: '消防水泵供电脱扣报警',
  I_power_st_15: '消防伴热带供电脱扣报警',
  I_water_press_down: '水流量压力值下限',
  I_fire_pump_feedback: '消防水泵控制反馈',
  I_fire_heater_feedback: '消防伴热带控制反馈',
  I_fire_D_door: 'D箱消防门检测',
  I_fire_E_door: 'E箱消防门检测',
  I_heater_temp_up: '伴热带温度检测上限',
  I_heater_temp_down: '伴热带温度检测下限',
  I_flow_switch_up: '出口流量开关上限',
  I_flow_switch_down: '出口流量开关下限'
}

export const di_varname_firefly1 = {
  I_power_st_1: '1~3拧紧枪伺服供电脱口报警',
  I_power_st_2: '4~6拧紧枪伺服供电脱口报警',
  I_power_st_3: '7~8拧紧枪伺服供电脱口报警',
  I_power_st_4: '左前/右前/左后四轮推杆伺服供电脱口报警',
  I_power_st_5: '右后四轮推杆&翻转门伺服供电脱口报警',
  I_power_st_6: 'RGV行走伺服供电脱口报警',
  I_power_st_7: 'RGV升降伺服供电脱口报警',
  I_power_st_8: '堆垛机货叉伺服供电脱口报警',
  I_power_st_9: '堆垛机升降伺服供电脱口报警',
  I_power_st_10: '液压油泵电机供电脱口报警',
  I_power_st_11: '加热棒电机供电脱口报警',
  I_power_st_12: 'S值调节供电脱口报警',
  I_power_st_13: '消防水泵电机供电脱扣报警',
  I_power_st_14: '伴热带辅助加热供电脱扣报警',
  I_power_st_15: '烟感供电脱扣报警',
  trans_fire_stopper_retract: '一键落水按钮',
  emergency_stop_switch_01: '急停反馈',
  battery_slot_area_check: '电池仓区域安全反馈',
  stacker_chain_check_1: '堆垛机链条检测前',
  stacker_chain_check_2: '堆垛机链条检测后',
  Maitaindoor_security_1: '维护门开合左侧传感器检测',
  Maitaindoor_security_2: '维护门开合右侧传感器检测',
  scanistor_di_err: '扫描仪报警',
  fire_door_check_1: '消防应急门开合传感器检测前',
  fire_door_check_2: '消防应急门开合传感器检测后',
  fire_spray_flow_nc_feedblack: '消防喷淋管路流量开关(NC)反馈',
  fire_spray_flow_no_feedblack: '消防喷淋管路流量开关(NO)反馈',
  fire_spray_T_limit_up_feedblack: '消防喷淋管路温度开关上限值反馈',
  fire_spray_T_limit_down_feedblack: '消防喷淋管路温度开关下限值反馈',
  fire_spray_P_limit_up_feedblack: '消防喷淋管路压力开关上限值反馈',
  fire_spray_P_limit_down_feedblack: '消防喷淋管路压力开关下限值反馈',
  fire_spray_power_F1_feedblack: '消防喷淋主电源F1断路器反馈',
  fire_spray_power_KM_feedblack: '消防喷淋水泵供电KM反馈',
  scanistor_safe_feedblack: '平台扫描仪运行保护反馈',
  carpet_safe_feedblack: '平台地毯运行保护反馈',
  battery_slot_area_KM_feedblack: '电池仓区域伺服动力电源KM反馈',
  platfrom_area_KM_feedblack: '停车平台伺服动力电源KM反馈',
  maintain_operate_swicth: '运营模式切换',
  platfrom_door_safe_check_feedblack: '停车平台开阖门检测安全反馈',
  hydraulic_cylinder_water_low_err: '液压油缸低液位报警',
  hydraulic_cylinder_water_jam_err: '液压油缸液位堵塞报警',
  I_power_st_16: '消防水槽&储水箱辅助加热供电脱扣报警'
}
