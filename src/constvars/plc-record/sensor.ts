export const sensor_varname = {
  1: {},
  2: {
    pl_lf_clamp_home_sensor: '推杆零点左前轮',
    pl_lf_v_home_sensor: '泊车到位左前',
    pl_rf_clamp_home_sensor: '推杆零点右前轮 ',
    pl_rf_v_home_sensor: '泊车到位右前',
    buffer_battery_safety_sensor: '缓存位电池安全',
    tr_battery_safety_sensor: '接驳位电池安全',
    vehical_l_lift_safety_sensor: '左举车安全',
    vehical_r_lift_safety_sensor: '右举车安全',
    buffer_battery_reached_sensor1: '缓存位电池前到位',
    buffer_battery_reached_sensor2: '缓存位电池后到位',
    buffer_battery_deceleration_sensor: '缓存位电池减速',
    pl_battery_reached_sensor1: '滚筒传送电池前到位',
    pl_battery_reached_sensor2: '滚筒传送电池后到位',
    pl_battery_deceleration_sensor: '滚筒传送电池减速',
    pl_stopper_01_extend_seneor: '滚筒传送电池前阻挡上限位',
    pl_stopper_01_retract_seneor: '滚筒传送电池前阻挡下限位',
    pl_stopper_02_extend_seneor: '滚筒传送电池后阻挡上限位',
    pl_stopper_02_retract_seneor: '滚筒传送电池后阻挡下限位',
    pl_left_car_lift_work_sensor: '车辆左举升工作位',
    pl_right_car_lift_work_sensor: '车辆右举升工作位',
    lr_work_sensor: '加解锁平台举升工作位',
    pl_lf_battery_reached_sensor: '停车位电池左前有',
    pl_lr_battery_reached_sensor: '停车位电池左后有',
    pl_rf_battery_reached_sensor: '停车位电池右前有',
    pl_rr_battery_reached_sensor: '停车位电池右后有',
    pl_f_battery_reached_sensor: '停车位电池前有',
    pl_lf_ev_locating_pin_extend_sensor: '左前车身定位销上到位',
    pl_lf_ev_locating_pin_retract_sensor: '左前车身定位销下到位',
    pl_rr_ev_locating_pin_extend_sensor: '右后车身定位销上到位',
    pl_rr_ev_locating_pin_retract_sensor: '右后车身定位销下到位',
    pl_lr_clamp_home_sensor: '左后轮推杆零点',
    pl_rr_clamp_home_sensor: '右后轮推杆零点',
    pl_door_01_open_sensor: '左开合门开到位',
    pl_door_01_close_sensor: '左开合门合到位',
    pl_door_02_open_sensor: '右开合门开到位',
    pl_door_02_close_sensor: '右开合门合到位',
    bl_l_lift_safety_sensor: '提升机左安全',
    bl_r_lift_safety_sensor: '提升机右安全',
    tr_battery_exist_sensor: '接驳位有电池',
    bc_slot1_ec_retract_sensor: '电池仓1电连接器电推杆上限位',
    bc_slot1_ec_extend_sensor: '电池仓1电连接器电推杆下限位',
    bc_slot1_lc_retract_sensor: '电池仓1水连接器电推杆上限位',
    bc_slot1_lc_extend_sensor: '电池仓1水连接器电推杆下限位',
    bc_slot1_battery_exist_sensor_1: '电池仓1电池前到位  ',
    bc_slot1_battery_exist_sensor_2: '电池仓1电池后到位  ',
    bc_slot1_battery_deceleration_sensor: '电池仓1电池减速',
    bc_slot1_smoke_sensor: '电池仓1烟雾报警',
    bc_slot1_liq_flow_switch_sensor: '电池仓1水冷流量开关',
    bc_slot2_ec_retract_sensor: '电池仓2电连接器电推杆上限位',
    bc_slot2_ec_extend_sensor: '电池仓2电连接器电推杆下限位',
    bc_slot2_lc_retract_sensor: '电池仓2水连接器电推杆上限位',
    bc_slot2_lc_extend_sensor: '电池仓2水连接器电推杆下限位',
    bc_slot2_battery_exist_sensor_1: '电池仓2电池前到位 ',
    bc_slot2_battery_exist_sensor_2: '电池仓2电池后到位 ',
    bc_slot2_battery_deceleration_sensor: '电池仓2电池减速',
    bc_slot2_smoke_sensor: '电池仓2烟雾报警',
    bc_slot2_liq_flow_switch_sensor: '电池仓2水冷流量开关',
    bc_slot3_ec_retract_sensor: '电池仓3电连接器电推杆上限位',
    bc_slot3_ec_extend_sensor: '电池仓3电连接器电推杆下限位',
    bc_slot3_lc_retract_sensor: '电池仓3水连接器电推杆上限位',
    bc_slot3_lc_extend_sensor: '电池仓3水连接器电推杆下限位',
    bc_slot3_battery_exist_sensor_1: '电池仓3电池前到位 ',
    bc_slot3_battery_exist_sensor_2: '电池仓3电池后到位 ',
    bc_slot3_battery_deceleration_sensor: '电池仓3电池减速',
    bc_slot3_smoke_sensor: '电池仓3烟雾报警',
    bc_slot3_liq_flow_switch_sensor: '电池仓3水冷流量开关',
    bc_slot4_ec_retract_sensor: '电池仓4电连接器电推杆上限位',
    bc_slot4_ec_extend_sensor: '电池仓4电连接器电推杆下限位',
    bc_slot4_lc_retract_sensor: '电池仓4水连接器电推杆上限位',
    bc_slot4_lc_extend_sensor: '电池仓4水连接器电推杆下限位',
    bc_slot4_battery_exist_sensor_1: '电池仓4电池前到位',
    bc_slot4_battery_exist_sensor_2: '电池仓4电池后到位',
    bc_slot4_battery_deceleration_sensor: '电池仓4电池减速',
    bc_slot4_smoke_sensor: '电池仓4烟雾报警',
    bc_slot4_liq_flow_switch_sensor: '电池仓4水冷流量开关',
    bc_slot5_ec_retract_sensor: '电池仓5电连接器电推杆上限位',
    bc_slot5_ec_extend_sensor: '电池仓5电连接器电推杆下限位',
    bc_slot5_lc_retract_sensor: '电池仓5水连接器电推杆上限位',
    bc_slot5_lc_extend_sensor: '电池仓5水连接器电推杆下限位',
    bc_slot5_battery_exist_sensor_1: '电池仓5电池前到位',
    bc_slot5_battery_exist_sensor_2: '电池仓5电池后到位',
    bc_slot5_battery_deceleration_sensor: '电池仓5电池减速',
    bc_slot5_smoke_sensor: '电池仓5烟雾报警',
    bc_slot5_liq_flow_switch_sensor: '电池仓5水冷流量开关',
    bc_slot6_ec_retract_sensor: '电池仓6电连接器电推杆上限位',
    bc_slot6_ec_extend_sensor: '电池仓6电连接器电推杆下限位',
    bc_slot6_lc_retract_sensor: '电池仓6水连接器电推杆上限位',
    bc_slot6_lc_extend_sensor: '电池仓6水连接器电推杆下限位',
    bc_slot6_battery_exist_sensor_1: '电池仓6电池前到位',
    bc_slot6_battery_exist_sensor_2: '电池仓6电池后到位',
    bc_slot6_battery_deceleration_sensor: '电池仓6电池减速',
    bc_slot6_smoke_sensor: '电池仓6烟雾报警',
    bc_slot6_liq_flow_switch_sensor: '电池仓6水冷流量开关',
    liq_left_bc_pressure_switch_st: '水冷左仓压力开关',
    bc_slot7_ec_retract_sensor: '电池仓7电连接器电推杆上限位',
    bc_slot7_ec_extend_sensor: '电池仓7电连接器电推杆下限位',
    bc_slot7_lc_retract_sensor: '电池仓7水连接器电推杆上限位',
    bc_slot7_lc_extend_sensor: '电池仓7水连接器电推杆下限位',
    bc_slot7_battery_exist_sensor_1: '电池仓7电池前到位',
    bc_slot7_battery_exist_sensor_2: '电池仓7电池后到位',
    bc_slot7_battery_deceleration_sensor: '电池仓7电池减速',
    bc_slot7_smoke_sensor: '电池仓7烟雾报警',
    bc_slot7_liq_flow_switch_sensor: '电池仓7水冷流量开关',
    bc_lift_get_exchange_station_1_7: '电池仓7提升机电池对接位',
    bc_slot8_ec_retract_sensor: '电池仓8电连接器电推杆上限位',
    bc_slot8_ec_extend_sensor: '电池仓8电连接器电推杆下限位',
    bc_slot8_lc_retract_sensor: '电池仓8水连接器电推杆上限位',
    bc_slot8_lc_extend_sensor: '电池仓8水连接器电推杆下限位',
    bc_slot8_battery_exist_sensor_1: '电池仓8电池前到位',
    bc_slot8_battery_exist_sensor_2: '电池仓8电池后到位',
    bc_slot8_battery_deceleration_sensor: '电池仓8电池减速',
    bc_slot8_smoke_sensor: '电池仓8烟雾报警',
    bc_slot8_liq_flow_switch_sensor: '电池仓8水冷流量开关',
    bc_lift_get_exchange_station_2_8: '电池仓8提升机电池对接位',
    bc_slot9_ec_retract_sensor: '电池仓9电连接器电推杆上限位',
    bc_slot9_ec_extend_sensor: '电池仓9电连接器电推杆下限位',
    bc_slot9_lc_retract_sensor: '电池仓9水连接器电推杆上限位',
    bc_slot9_lc_extend_sensor: '电池仓9水连接器电推杆下限位',
    bc_slot9_battery_exist_sensor_1: '电池仓9电池前到位',
    bc_slot9_battery_exist_sensor_2: '电池仓9电池后到位',
    bc_slot9_battery_deceleration_sensor: '电池仓9电池减速',
    bc_slot9_smoke_sensor: '电池仓9烟雾报警',
    bc_slot9_liq_flow_switch_sensor: '电池仓9水冷流量开关',
    bc_lift_get_exchange_station_3_9: '电池仓9提升机电池对接位',
    bc_slot10_ec_retract_sensor: '电池仓10电连接器电推杆上限位',
    bc_slot10_ec_extend_sensor: '电池仓10电连接器电推杆下限位',
    bc_slot10_lc_retract_sensor: '电池仓10水连接器电推杆上限位',
    bc_slot10_lc_extend_sensor: '电池仓10水连接器电推杆下限位',
    bc_slot10_battery_exist_sensor_1: '电池仓10电池前到位',
    bc_slot10_battery_exist_sensor_2: '电池仓10电池后到位',
    bc_slot10_battery_deceleration_sensor: '电池仓10电池减速',
    bc_slot10_smoke_sensor: '电池仓10烟雾报警',
    bc_slot10_liq_flow_switch_sensor: '电池仓10水冷流量开关',
    bc_lift_get_exchange_station_4_10: '电池仓10提升机电池对接位',
    bc_slot11_ec_retract_sensor: '电池仓11电连接器电推杆上限位',
    bc_slot11_ec_extend_sensor: '电池仓11电连接器电推杆下限位',
    bc_slot11_lc_retract_sensor: '电池仓11水连接器电推杆上限位',
    bc_slot11_lc_extend_sensor: '电池仓11水连接器电推杆下限位',
    bc_slot11_battery_exist_sensor_1: '电池仓11电池前到位',
    bc_slot11_battery_exist_sensor_2: '电池仓11电池后到位',
    bc_slot11_battery_deceleration_sensor: '电池仓11电池减速',
    bc_slot11_smoke_sensor: '电池仓11烟雾报警',
    bc_slot11_liq_flow_switch_sensor: '电池仓11水冷流量开关',
    bc_lift_get_exchange_station_5_11: '电池仓11提升机电池对接位',
    bc_slot12_ec_retract_sensor: '电池仓12电连接器电推杆上限位',
    bc_slot12_ec_extend_sensor: '电池仓12电连接器电推杆下限位',
    bc_slot12_lc_retract_sensor: '电池仓12水连接器电推杆上限位',
    bc_slot12_lc_extend_sensor: '电池仓12水连接器电推杆下限位',
    bc_slot12_battery_exist_sensor_1: '电池仓12电池前到位',
    bc_slot12_battery_exist_sensor_2: '电池仓12电池后到位',
    bc_slot12_battery_deceleration_sensor: '电池仓12电池减速',
    bc_slot12_smoke_sensor: '电池仓12烟雾报警',
    bc_slot12_liq_flow_switch_sensor: '电池仓12水冷流量开关',
    bc_lift_get_exchange_station_6_12: '电池仓12提升机电池对接位',
    bc_slot13_ec_retract_sensor: '电池仓13电连接器电推杆上限位',
    bc_slot13_ec_extend_sensor: '电池仓13电连接器电推杆下限位',
    bc_slot13_lc_retract_sensor: '电池仓13水连接器电推杆上限位',
    bc_slot13_lc_extend_sensor: '电池仓13水连接器电推杆下限位',
    bc_slot13_battery_exist_sensor_1: '电池仓13电池前到位',
    bc_slot13_battery_exist_sensor_2: '电池仓13电池后到位',
    bc_slot13_battery_deceleration_sensor: '电池仓13电池减速',
    bc_slot13_smoke_sensor: '电池仓13烟雾报警',
    bc_slot13_liq_flow_switch_sensor: '电池仓13水冷流量开关',
    bc_lift_get_exchange_station_13: '电池仓13提升机电池对接位',
    liq_right_bc_pressure_switch_st: '右仓水冷压力开关',
    bl_lf_stopper_extend_sensor: '提升机左前电推杆上限位',
    bl_lf_stopper_retract_sensor: '提升机左前电推杆下限位',
    bl_rf_stopper_extend_sensor: '提升机右前电推杆上限位',
    bl_rf_stopper_retract_sensor: '提升机右前电推杆下限位',
    bl_lr_stopper_extend_sensor: '提升机左后电推杆上限位',
    bl_lr_stopper_retract_sensor: '提升机左后电推杆下限位',
    bl_rr_stopper_extend_sensor: '提升机右后电推杆上限位',
    bl_rr_stopper_retract_sensor: '提升机右后电推杆下限位',
    bl_lf_battery_reach_sensor: '提升机左前电池到位',
    bl_rf_battery_reach_sensor: '提升机右前电池到位',
    bl_battery_deceleration_sensor: '提升机电池减速',
    V_l_fixed_extent_sensor: '左V槽固定上限位',
    V_l_fixed_retract_sensor: '左V槽固定下限位',
    V_r_fixed_extent_sensor: '右V槽固定上限位',
    V_r_fixed_retract_sensor: '右V槽固定下限位',
    lr_up_limit: '加解锁平台上限位',
    lr_down_limit: '加解锁平台下限位',
    lr_zero_sensor: '加解锁平台原点',
    l_vehical_lift_up_limit: '左车辆举升机上限位',
    l_vehical_lift_down_limit: '左车辆举升机下限位',
    l_vehical_lift_zero_sensor: '左车辆举升原点',
    r_vehical_lift_up_limit: '右车辆举升上限位',
    r_vehical_lift_down_limit: '右车辆举升下限位',
    r_vehical_lift_zero_sensor: '右车辆举升原点',
    bl_up_limit: '升降机上限位',
    bl_down_limit: '升降机下限位',
    bl_zero_sensor: '升降机原点'
  },
  3: {
    front_left_pressure_transducer: '左前压力传感器',
    right_rear_pressure_transducer: '右后压力传感器',
    roller_door_01_up_limt: '前卷帘门上到位',
    roller_door_01_down_limt: '前卷帘门下到位',
    roller_door_02_up_limt: '后卷帘门上到位',
    roller_door_02_down_limt: '后卷帘门下到位',
    front_roller_door_safety_01: '前卷帘门安全保护1',
    front_roller_door_safety_02: '前卷帘门安全保护2',
    rear_roller_door_safety_01: '后卷帘门安全保护1',
    rear_roller_door_safety_02: '后卷帘门安全保护2',
    maintain_area_safety_01: '维护门安全继电器反馈',
    maintain_area_safety_02: '维护门传感器',
    pl_buffer_dece_sensor_1: '左缓存区电池减速',
    pl_buffer_sensor_f_1: '左缓存区前电池到位',
    pl_buffer_sensor_r_1: '左缓存区后电池到位',
    pl_lf_clamp_home_sensor: '左前轮推杆原点',
    pl_lf_V_check_sensor: '左前轮进槽',
    pl_l_V_lock_extend_sensor: 'V槽左锁止上到位',
    pl_l_V_lock_retract_sensor: 'V槽左锁止下到位',
    pl_rf_clamp_home_sensor: '右前轮推杆原点',
    pl_rf_V_check_sensor: '右前轮进槽',
    pl_r_V_lock_extend_sensor: 'V槽右锁止上到位',
    pl_r_V_lock_retract_sensor: 'V槽右锁止下到位',
    pl_f_guide_work_sensor: '前导向条前到位',
    pl_f_guide_home_sensor: '前导向条后到位',
    pl_r_guide_work_sensor: '后导向条前到位',
    pl_r_guide_home_sensor: '后导向条后到位',
    pl_lr_clamp_home_sensor: '左后轮推杆原点',
    pl_rr_clamp_home_sensor: '右后轮推杆原点',
    pl_door_01_open_sensor: '左开合门开到位',
    pl_door_01_close_sensor: '左开合门关到位',
    pl_door_02_open_sensor: '右开合门开到位',
    pl_door_02_close_sensor: '右开合门关到位',
    pl_door_close_safe_sensor: '开合门闭合安全',
    bc_lift_dece_sensor: '升降仓减速',
    bc_lift_reach_sensor_f: '升降仓前到位',
    bc_lift_reach_sensor_r: '升降仓后到位',
    bc_lift_work_sensor: '升降仓举升工作位',
    pl_buffer_dece_sensor_2: '右缓存区电池减速',
    pl_buffer_sensor_f_2: '右缓存区前电池到位',
    pl_buffer_sensor_r_2: '右缓存区后电池到位',
    buffer_stopper_01_extend_sensor_02: '右缓存区前电池阻挡工作位',
    buffer_stopper_01_retract_sensor_02: '右缓存区前电池阻挡原点位',
    buffer_stopper_02_extend_sensor_02: '右缓存区后电池阻挡工作位',
    buffer_stopper_02_retract_sensor_02: '右缓存区后电池阻挡原点位',
    RGV_bc_reach_sensor_01: '电池平整1',
    RGV_bc_reach_sensor_02: '电池平整2',
    RGV_bc_reach_sensor_03: '电池平整3',
    RGV_bc_reach_sensor_04: '电池平整4',
    RGV_bc_reach_sensor_05: '电池平整5',
    RGV_bc_reach_sensor_06: '电池平整6',
    lf_pin_extend_sensor: '左前车身定位销上到位',
    lf_pin_retract_sensor: '左前车身定位销下到位',
    lf_pin_touch_sensor: '左前车身定位销接触车身',
    rr_pin_extend_sensor: '右后车身定位销上到位',
    rr_pin_retract_sensor: '右后车身定位销下到位',
    rr_pin_touch_sensor: '右后车身定位销接触车身',
    lr_pin_extend_sensor: '左后车身定位销上到位',
    lr_pin_retract_sensor: '左后车身定位销下到位',
    lr_pin_touch_sensor: '左后车身定位销接触车身',
    gun1_lift_work_sensor: '1#升降上到位',
    gun1_lift_home_sensor: '1#升降下到位',
    gun2_lift_work_sensor: '2#升降上到位',
    gun2_lift_home_sensor: '2#升降下到位',
    gun9_move_home_sensor: '9#平移位置1',
    gun9_move_work_sensor: '9#平移位置2',
    gun9_lift_work_sensor: '9#升降上到位',
    gun9_lift_home_sensor: '9#升降下到位',
    gun10_move_home_sensor: '10#平移位置1',
    gun10_move_work_sensor: '10#平移位置2',
    gun10_lift_work_sensor: '10#升降上到位',
    gun10_lift_home_sensor: '10#升降下到位',
    gun11_lift_work_sensor: '11#升降上到位',
    gun11_lift_home_sensor: '11#升降下到位',
    gun12_lift_work_sensor: '12#升降上到位',
    gun12_lift_home_sensor: '12#升降下到位',
    RGV_work_sensor: 'RGV举升工作位',
    RGV_maintain_sensor: 'RGV举升维护位',
    pl_stopper_01_home_sensor: '前电池阻挡升降原点位',
    pl_stopper_01_work_sensor: '前电池阻挡升降工作位',
    pl_stopper_01_reach_sensor: '前电池阻挡电池到位',
    pl_stopper_02_home_sensor: '后电池阻挡升降原点位',
    pl_stopper_02_work_sensor: '后电池阻挡升降工作位',
    pl_stopper_02_reach_sensor: '后电池阻挡电池到位',
    pl_move_work_sensor_1: 'RGV平移加解锁位',
    pl_move_work_sensor_2: 'RGV平移 NPA位',
    pl_move_work_sensor_3: 'RGV平移 NPD位',
    pl_move_work_sensor_4: 'RGV平移备用1',
    pl_move_work_sensor_5: 'RGV平移备用2',
    pl_stopper_01_dece_sensor: 'RGV电池阻挡电池减速',
    bc_slot1_ec_retract_sensor_1: '1仓NPA电插头上到位',
    bc_slot1_ec_extend_sensor_1: '1仓NPA电插头下到位',
    bc_slot1_lc_retract_sensor_1: '1仓NPA水插头上到位',
    bc_slot1_lc_extend_sensor_1: '1仓NPA水插头下到位',
    bc_slot1_ec_retract_sensor_2: '1仓NPD电插头上到位',
    bc_slot1_ec_extend_sensor_2: '1仓NPD电插头下到位',
    bc_slot1_lc_retract_sensor_2: '1仓NPD水插头上到位',
    bc_slot1_lc_extend_sensor_2: '1仓NPD水插头下到位',
    bc_slot1_check_sensor_1: '1仓电池区分NPA',
    bc_slot1_check_sensor_2: '1仓电池区分NPD',
    bc_slot1_reached_sensor: '1仓电池落到位',
    bc_slot1_smoke_sensor: '1仓烟雾报警',
    bc_slot1_liq_flow_switch_st: '1仓水冷流量开关',
    bc_sum_smoke_sensor: '电池仓整体烟雾',
    bc_slot2_ec_retract_sensor_1: '2仓NPA电插头上到位',
    bc_slot2_ec_extend_sensor_1: '2仓NPA电插头下到位',
    bc_slot2_lc_retract_sensor_1: '2仓NPA水插头上到位',
    bc_slot2_lc_extend_sensor_1: '2仓NPA水插头下到位',
    bc_slot2_ec_retract_sensor_2: '2仓NPD电插头上到位',
    bc_slot2_ec_extend_sensor_2: '2仓NPD电插头下到位',
    bc_slot2_lc_retract_sensor_2: '2仓NPD水插头上到位',
    bc_slot2_lc_extend_sensor_2: '2仓NPD水插头下到位',
    bc_slot2_check_sensor_1: '2仓电池区分NPA',
    bc_slot2_check_sensor_2: '2仓电池区分NPD',
    bc_slot2_reached_sensor: '2仓电池落到位',
    bc_slot2_smoke_sensor: '2仓烟雾报警',
    bc_slot2_liq_flow_switch_st: '2仓水冷流量开关',
    bc_slot3_ec_retract_sensor_1: '3仓NPA电插头上到位',
    bc_slot3_ec_extend_sensor_1: '3仓NPA电插头下到位',
    bc_slot3_lc_retract_sensor_1: '3仓NPA水插头上到位',
    bc_slot3_lc_extend_sensor_1: '3仓NPA水插头下到位',
    bc_slot3_ec_retract_sensor_2: '3仓NPD电插头上到位',
    bc_slot3_ec_extend_sensor_2: '3仓NPD电插头下到位',
    bc_slot3_lc_retract_sensor_2: '3仓NPD水插头上到位',
    bc_slot3_lc_extend_sensor_2: '3仓NPD水插头下到位',
    bc_slot3_check_sensor_1: '3仓电池区分NPA',
    bc_slot3_check_sensor_2: '3仓电池区分NPD',
    bc_slot3_reached_sensor: '3仓电池落到位',
    bc_slot3_smoke_sensor: '3仓烟雾报警',
    bc_slot3_liq_flow_switch_st: '3仓水冷流量开关',
    bc_slot4_ec_retract_sensor_1: '4仓NPA电插头上到位',
    bc_slot4_ec_extend_sensor_1: '4仓NPA电插头下到位',
    bc_slot4_lc_retract_sensor_1: '4仓NPA水插头上到位',
    bc_slot4_lc_extend_sensor_1: '4仓NPA水插头下到位',
    bc_slot4_ec_retract_sensor_2: '4仓NPD电插头上到位',
    bc_slot4_ec_extend_sensor_2: '4仓NPD电插头下到位',
    bc_slot4_lc_retract_sensor_2: '4仓NPD水插头上到位',
    bc_slot4_lc_extend_sensor_2: '4仓NPD水插头下到位',
    bc_slot4_check_sensor_1: '4仓电池区分NPA',
    bc_slot4_check_sensor_2: '4仓电池区分NPD',
    bc_slot4_reached_sensor: '4仓电池落到位',
    bc_slot4_smoke_sensor: '4仓烟雾报警',
    bc_slot4_liq_flow_switch_st: '4仓水冷流量开关',
    bc_slot5_ec_retract_sensor_1: '5仓NPA电插头上到位',
    bc_slot5_ec_extend_sensor_1: '5仓NPA电插头下到位',
    bc_slot5_lc_retract_sensor_1: '5仓NPA水插头上到位',
    bc_slot5_lc_extend_sensor_1: '5仓NPA水插头下到位',
    bc_slot5_ec_retract_sensor_2: '5仓NPD电插头上到位',
    bc_slot5_ec_extend_sensor_2: '5仓NPD电插头下到位',
    bc_slot5_lc_retract_sensor_2: '5仓NPD水插头上到位',
    bc_slot5_lc_extend_sensor_2: '5仓NPD水插头下到位',
    bc_slot5_check_sensor_1: '5仓电池区分NPA',
    bc_slot5_check_sensor_2: '5仓电池区分NPD',
    bc_slot5_reached_sensor: '5仓电池落到位',
    bc_slot5_smoke_sensor: '5仓烟雾报警',
    bc_slot5_liq_flow_switch_st: '5仓水冷流量开关',
    bcslot1_5_pressure_switch_st: '1~5仓水冷压力开关',
    bc_slot6_ec_retract_sensor_1: '6仓NPA电插头上到位',
    bc_slot6_ec_extend_sensor_1: '6仓NPA电插头下到位',
    bc_slot6_lc_retract_sensor_1: '6仓NPA水插头上到位',
    bc_slot6_lc_extend_sensor_1: '6仓NPA水插头下到位',
    bc_slot6_ec_retract_sensor_2: '6仓NPD电插头上到位',
    bc_slot6_ec_extend_sensor_2: '6仓NPD电插头下到位',
    bc_slot6_lc_retract_sensor_2: '6仓NPD水插头上到位',
    bc_slot6_lc_extend_sensor_2: '6仓NPD水插头下到位',
    bc_slot6_check_sensor_1: '6仓电池区分NPA',
    bc_slot6_check_sensor_2: '6仓电池区分NPD',
    bc_slot6_reached_sensor: '6仓电池落到位',
    bc_slot6_smoke_sensor: '6仓烟雾报警',
    bc_slot6_liq_flow_switch_st: '6仓水冷流量开关',
    bc_slot7_ec_retract_sensor_1: '7仓NPA电插头上到位',
    bc_slot7_ec_extend_sensor_1: '7仓NPA电插头下到位',
    bc_slot7_lc_retract_sensor_1: '7仓NPA水插头上到位',
    bc_slot7_lc_extend_sensor_1: '7仓NPA水插头下到位',
    bc_slot7_ec_retract_sensor_2: '7仓NPD电插头上到位',
    bc_slot7_ec_extend_sensor_2: '7仓NPD电插头下到位',
    bc_slot7_lc_retract_sensor_2: '7仓NPD水插头上到位',
    bc_slot7_lc_extend_sensor_2: '7仓NPD水插头下到位',
    bc_slot7_check_sensor_1: '7仓电池区分NPA',
    bc_slot7_check_sensor_2: '7仓电池区分NPD',
    bc_slot7_reached_sensor: '7仓电池落到位',
    bc_slot7_smoke_sensor: '7仓烟雾报警',
    bc_slot7_liq_flow_switch_st: '7仓水冷流量开关',
    bc_slot8_ec_retract_sensor_1: '8仓NPA电插头上到位',
    bc_slot8_ec_extend_sensor_1: '8仓NPA电插头下到位',
    bc_slot8_lc_retract_sensor_1: '8仓NPA水插头上到位',
    bc_slot8_lc_extend_sensor_1: '8仓NPA水插头下到位',
    bc_slot8_ec_retract_sensor_2: '8仓NPD电插头上到位',
    bc_slot8_ec_extend_sensor_2: '8仓NPD电插头下到位',
    bc_slot8_lc_retract_sensor_2: '8仓NPD水插头上到位',
    bc_slot8_lc_extend_sensor_2: '8仓NPD水插头下到位',
    bc_slot8_check_sensor_1: '8仓电池区分NPA',
    bc_slot8_check_sensor_2: '8仓电池区分NPD',
    bc_slot8_reached_sensor: '8仓电池落到位',
    bc_slot8_smoke_sensor: '8仓烟雾报警',
    bc_slot8_liq_flow_switch_st: '8仓水冷流量开关',
    bc_slot9_ec_retract_sensor_1: '9仓NPA电插头上到位',
    bc_slot9_ec_extend_sensor_1: '9仓NPA电插头下到位',
    bc_slot9_lc_retract_sensor_1: '9仓NPA水插头上到位',
    bc_slot9_lc_extend_sensor_1: '9仓NPA水插头下到位',
    bc_slot9_ec_retract_sensor_2: '9仓NPD电插头上到位',
    bc_slot9_ec_extend_sensor_2: '9仓NPD电插头下到位',
    bc_slot9_lc_retract_sensor_2: '9仓NPD水插头上到位',
    bc_slot9_lc_extend_sensor_2: '9仓NPD水插头下到位',
    bc_slot9_check_sensor_1: '9仓电池区分NPA',
    bc_slot9_check_sensor_2: '9仓电池区分NPD',
    bc_slot9_reached_sensor: '9仓电池落到位',
    bc_slot9_smoke_sensor: '9仓烟雾报警',
    bc_slot9_liq_flow_switch_st: '9仓水冷流量开关',
    bc_slot10_ec_retract_sensor_1: '10仓NPA电插头上到位',
    bc_slot10_ec_extend_sensor_1: '10仓NPA电插头下到位',
    bc_slot10_lc_retract_sensor_1: '10仓NPA水插头上到位',
    bc_slot10_lc_extend_sensor_1: '10仓NPA水插头下到位',
    bc_slot10_ec_retract_sensor_2: '10仓NPD电插头上到位',
    bc_slot10_ec_extend_sensor_2: '10仓NPD电插头下到位',
    bc_slot10_lc_retract_sensor_2: '10仓NPD水插头上到位',
    bc_slot10_lc_extend_sensor_2: '10仓NPD水插头下到位',
    bc_slot10_check_sensor_1: '10仓电池区分NPA',
    bc_slot10_check_sensor_2: '10仓电池区分NPD',
    bc_slot10_reached_sensor: '10仓电池落到位',
    bc_slot10_smoke_sensor: '10仓烟雾报警',
    bc_slot10_liq_flow_switch_st: '10仓水冷流量开关',
    bcslot6_10_pressure_switch_st: '6~10仓水冷压力开关',
    stacker_low_sensor_1: '堆垛机仓位对接1层',
    stacker_low_sensor_2: '堆垛机仓位对接2层',
    stacker_low_sensor_3: '堆垛机仓位对接3层',
    stacker_low_sensor_4: '堆垛机仓位对接4层',
    stacker_low_sensor_5: '堆垛机仓位对接5层',
    stacker_low_sensor_6: '堆垛机仓位对接6层',
    stacker_low_sensor_0: '堆垛机接驳位对接位',
    stacker_move_f_sensor: '堆垛机行走前仓位',
    stacker_move_r_sensor: '堆垛机行走后仓位',
    stacker_move_RGV_sensor: '堆垛机行走RGV对接位',
    stacker_left_safe_sensor_1: '货叉上层左超程',
    stacker_right_safe_sensor_1: '货叉上层右超程',
    stacker_left_safe_sensor_2: '货叉下层左超程',
    stacker_right_safe_sensor_2: '货叉下层右超程',
    fork_retract_sensor_1: '货叉主叉臂中点',
    fork_retract_sensor_2: '货叉辅叉臂中点',
    fork_left_extend_sensor_1: '货叉叉臂左到位1',
    fork_left_extend_sensor_2: '货叉叉臂左到位2',
    fork_right_extend_sensor_1: '货叉叉臂右到位',
    fork_bc_exist_sensor_1: '货叉有电池1',
    fork_bc_exist_sensor_2: '货叉有电池2',
    vehaicl_l_work_sensor: '左车辆举升工作位',
    vehaicl_l_maintain_sensor: '左车辆举升维护位',
    vehaicl_l_safe_sensor: '左车辆举升安全',
    vehaicl_l_bc_safe_sensor: '左车辆举升电池安全',
    vehaicl_r_work_sensor: '右车辆举升工作位',
    vehaicl_r_maintain_sensor: '右车辆举升维护位',
    vehaicl_r_safe_sensor: '右车辆举升安全',
    vehaicl_r_bc_safe_sensor: '右车辆举升电池安全',
    bc_slot11_ec_retract_sensor_1: '11仓NPA电插头上到位',
    bc_slot11_ec_extend_sensor_1: '11仓NPA电插头下到位',
    bc_slot11_lc_retract_sensor_1: '11仓NPA水插头上到位',
    bc_slot11_lc_extend_sensor_1: '11仓NPA水插头下到位',
    bc_slot11_ec_retract_sensor_2: '11仓NPD电插头上到位',
    bc_slot11_ec_extend_sensor_2: '11仓NPD电插头下到位',
    bc_slot11_lc_retract_sensor_2: '11仓NPD水插头上到位',
    bc_slot11_lc_extend_sensor_2: '11仓NPD水插头下到位',
    bc_slot11_check_sensor_1: '11仓电池区分NPA',
    bc_slot11_check_sensor_2: '11仓电池区分NPD',
    bc_slot11_reached_sensor: '11仓电池落到位',
    bc_slot11_smoke_sensor: '11仓烟雾报警',
    bc_slot11_liq_flow_switch_st: '11仓水冷流量开关',
    bc_slot12_ec_retract_sensor_1: '12仓NPA电插头上到位',
    bc_slot12_ec_extend_sensor_1: '12仓NPA电插头下到位',
    bc_slot12_lc_retract_sensor_1: '12仓NPA水插头上到位',
    bc_slot12_lc_extend_sensor_1: '12仓NPA水插头下到位',
    bc_slot12_ec_retract_sensor_2: '12仓NPD电插头上到位',
    bc_slot12_ec_extend_sensor_2: '12仓NPD电插头下到位',
    bc_slot12_lc_retract_sensor_2: '12仓NPD水插头上到位',
    bc_slot12_lc_extend_sensor_2: '12仓NPD水插头下到位',
    bc_slot12_check_sensor_1: '12仓电池区分NPA',
    bc_slot12_check_sensor_2: '12仓电池区分NPD',
    bc_slot12_reached_sensor: '12仓电池落到位',
    bc_slot12_smoke_sensor: '12仓烟雾报警',
    bc_slot12_liq_flow_switch_st: '12仓水冷流量开关',
    bc_slot13_ec_retract_sensor_1: '13仓NPA电插头上到位',
    bc_slot13_ec_extend_sensor_1: '13仓NPA电插头下到位',
    bc_slot13_lc_retract_sensor_1: '13仓NPA水插头上到位',
    bc_slot13_lc_extend_sensor_1: '13仓NPA水插头下到位',
    bc_slot13_ec_retract_sensor_2: '13仓NPD电插头上到位',
    bc_slot13_ec_extend_sensor_2: '13仓NPD电插头下到位',
    bc_slot13_lc_retract_sensor_2: '13仓NPD水插头上到位',
    bc_slot13_lc_extend_sensor_2: '13仓NPD水插头下到位',
    bc_slot13_check_sensor_1: '13仓电池区分NPA',
    bc_slot13_check_sensor_2: '13仓电池区分NPD',
    bc_slot13_reached_sensor: '13仓电池落到位',
    bc_slot13_smoke_sensor: '13仓烟雾报警',
    bc_slot13_liq_flow_switch_st: '13仓水冷流量开关',
    bc_slot14_ec_retract_sensor_1: '14仓NPA电插头上到位',
    bc_slot14_ec_extend_sensor_1: '14仓NPA电插头下到位',
    bc_slot14_lc_retract_sensor_1: '14仓NPA水插头上到位',
    bc_slot14_lc_extend_sensor_1: '14仓NPA水插头下到位',
    bc_slot14_ec_retract_sensor_2: '14仓NPD电插头上到位',
    bc_slot14_ec_extend_sensor_2: '14仓NPD电插头下到位',
    bc_slot14_lc_retract_sensor_2: '14仓NPD水插头上到位',
    bc_slot14_lc_extend_sensor_2: '14仓NPD水插头下到位',
    bc_slot14_check_sensor_1: '14仓电池区分NPA',
    bc_slot14_check_sensor_2: '14仓电池区分NPD',
    bc_slot14_reached_sensor: '14仓电池落到位',
    bc_slot14_smoke_sensor: '14仓烟雾报警',
    bc_slot14_liq_flow_switch_st: '14仓水冷流量开关',
    bc_slot15_ec_retract_sensor_1: '15仓NPA电插头上到位',
    bc_slot15_ec_extend_sensor_1: '15仓NPA电插头下到位',
    bc_slot15_lc_retract_sensor_1: '15仓NPA水插头上到位',
    bc_slot15_lc_extend_sensor_1: '15仓NPA水插头下到位',
    bc_slot15_ec_retract_sensor_2: '15仓NPD电插头上到位',
    bc_slot15_ec_extend_sensor_2: '15仓NPD电插头下到位',
    bc_slot15_lc_retract_sensor_2: '15仓NPD水插头上到位',
    bc_slot15_lc_extend_sensor_2: '15仓NPD水插头下到位',
    bc_slot15_check_sensor_1: '15仓电池区分NPA',
    bc_slot15_check_sensor_2: '15仓电池区分NPD',
    bc_slot15_reached_sensor: '15仓电池落到位',
    bc_slot15_smoke_sensor: '15仓烟雾报警',
    bc_slot15_liq_flow_switch_st: '15仓水冷流量开关',
    bc_slot16_ec_retract_sensor_1: '16仓NPA电插头上到位',
    bc_slot16_ec_extend_sensor_1: '16仓NPA电插头下到位',
    bc_slot16_lc_retract_sensor_1: '16仓NPA水插头上到位',
    bc_slot16_lc_extend_sensor_1: '16仓NPA水插头下到位',
    bc_slot16_ec_retract_sensor_2: '16仓NPD电插头上到位',
    bc_slot16_ec_extend_sensor_2: '16仓NPD电插头下到位',
    bc_slot16_lc_retract_sensor_2: '16仓NPD水插头上到位',
    bc_slot16_lc_extend_sensor_2: '16仓NPD水插头下到位',
    bc_slot16_check_sensor_1: '16仓电池区分NPA',
    bc_slot16_check_sensor_2: '16仓电池区分NPD',
    bc_slot16_reached_sensor: '16仓电池落到位',
    bc_slot16_smoke_sensor: '16仓烟雾报警',
    bc_slot16_liq_flow_switch_st: '16仓水冷流量开关',
    bc_slot17_ec_retract_sensor_1: '17仓NPA电插头上到位',
    bc_slot17_ec_extend_sensor_1: '17仓NPA电插头下到位',
    bc_slot17_lc_retract_sensor_1: '17仓NPA水插头上到位',
    bc_slot17_lc_extend_sensor_1: '17仓NPA水插头下到位',
    bc_slot17_ec_retract_sensor_2: '17仓NPD电插头上到位',
    bc_slot17_ec_extend_sensor_2: '17仓NPD电插头下到位',
    bc_slot17_lc_retract_sensor_2: '17仓NPD水插头上到位',
    bc_slot17_lc_extend_sensor_2: '17仓NPD水插头下到位',
    bc_slot17_check_sensor_1: '17仓电池区分NPA',
    bc_slot17_check_sensor_2: '17仓电池区分NPD',
    bc_slot17_reached_sensor: '17仓电池落到位',
    bc_slot17_smoke_sensor: '17仓烟雾报警',
    bc_slot17_liq_flow_switch_st: '17仓水冷流量开关',
    bc_slot18_ec_retract_sensor_1: '18仓NPA电插头上到位',
    bc_slot18_ec_extend_sensor_1: '18仓NPA电插头下到位',
    bc_slot18_lc_retract_sensor_1: '18仓NPA水插头上到位',
    bc_slot18_lc_extend_sensor_1: '18仓NPA水插头下到位',
    bc_slot18_ec_retract_sensor_2: '18仓NPD电插头上到位',
    bc_slot18_ec_extend_sensor_2: '18仓NPD电插头下到位',
    bc_slot18_lc_retract_sensor_2: '18仓NPD水插头上到位',
    bc_slot18_lc_extend_sensor_2: '18仓NPD水插头下到位',
    bc_slot18_check_sensor_1: '18仓电池区分NPA',
    bc_slot18_check_sensor_2: '18仓电池区分NPD',
    bc_slot18_reached_sensor: '18仓电池落到位',
    bc_slot18_smoke_sensor: '18仓烟雾报警',
    bc_slot18_liq_flow_switch_st: '18仓水冷流量开关',
    bc_slot19_ec_retract_sensor_1: '19仓NPA电插头上到位',
    bc_slot19_ec_extend_sensor_1: '19仓NPA电插头下到位',
    bc_slot19_lc_retract_sensor_1: '19仓NPA水插头上到位',
    bc_slot19_lc_extend_sensor_1: '19仓NPA水插头下到位',
    bc_slot19_ec_retract_sensor_2: '19仓NPD电插头上到位',
    bc_slot19_ec_extend_sensor_2: '19仓NPD电插头下到位',
    bc_slot19_lc_retract_sensor_2: '19仓NPD水插头上到位',
    bc_slot19_lc_extend_sensor_2: '19仓NPD水插头下到位',
    bc_slot19_check_sensor_1: '19仓电池区分NPA',
    bc_slot19_check_sensor_2: '19仓电池区分NPD',
    bc_slot19_reached_sensor: '19仓电池落到位',
    bc_slot19_smoke_sensor: '19仓烟雾报警',
    bc_slot19_liq_flow_switch_st: '19仓水冷流量开关',
    bc_slot20_ec_retract_sensor_1: '20仓NPA电插头上到位',
    bc_slot20_ec_extend_sensor_1: '20仓NPA电插头下到位',
    bc_slot20_lc_retract_sensor_1: '20仓NPA水插头上到位',
    bc_slot20_lc_extend_sensor_1: '20仓NPA水插头下到位',
    bc_slot20_ec_retract_sensor_2: '20仓NPD电插头上到位',
    bc_slot20_ec_extend_sensor_2: '20仓NPD电插头下到位',
    bc_slot20_lc_retract_sensor_2: '20仓NPD水插头上到位',
    bc_slot20_lc_extend_sensor_2: '20仓NPD水插头下到位',
    bc_slot20_check_sensor_1: '20仓电池区分NPA',
    bc_slot20_check_sensor_2: '20仓电池区分NPD',
    bc_slot20_reached_sensor: '20仓电池落到位',
    bc_slot20_smoke_sensor: '20仓烟雾报警',
    bc_slot20_liq_flow_switch_st: '20仓水冷流量开关',
    bc_slot21_ec_retract_sensor_1: '21仓NPA电插头上到位',
    bc_slot21_ec_extend_sensor_1: '21仓NPA电插头下到位',
    bc_slot21_ec_retract_sensor_2: '21仓NPD电插头上到位',
    bc_slot21_ec_extend_sensor_2: '21仓NPD电插头下到位',
    bc_slot21_check_sensor_1: '21仓电池区分NPA',
    bc_slot21_check_sensor_2: '21仓电池区分NPD',
    bc_slot21_reached_sensor: '21仓电池落到位',
    bc_slot21_smoke_sensor: '21仓烟雾报警',
    bc_slot11_15_pressure_switch_st: '11~15仓 水冷压力开关',
    bc_slot16_20_pressure_switch_st: '16~20仓 水冷压力开关',
    bc_fire_push_retract_sensor_1: '消防接驳前推杆缩回到位',
    bc_fire_push_extend_sensor_1: '消防接驳前推杆伸出到位',
    bc_fire_push_retract_sensor_2: '消防接驳后推杆缩回到位',
    bc_fire_push_extend_sensor_2: '消防接驳后推杆伸出到位',
    fire_liq_check: '消防液位检测',
    fork_X_left_limit_sensor: '堆垛机货叉左限位',
    fork_X_right_limit_sensor: '堆垛机货叉右限位',
    fork_X_home_sensor: '堆垛机货叉原点',
    stacker_move_f_limit_sensor: '堆垛机行走前限位',
    stacker_move_r_limit_sensor: '堆垛机行走后限位',
    stacker_move_home_sensor: '堆垛机行走原点',
    stacker_lift_up_limit_sensor: '堆垛机升降上限位',
    stacker_lift_down_limit_sensor: '堆垛机升降下限位',
    stacker_lift_home_sensor: '堆垛机升降原点',
    pl_move_f_limit_sensor: '加解锁平台平移前限位',
    pl_move_r_limit_sensor: '加解锁平台平移后限位',
    pl_move_home_sensor: '加解锁平台平移原点',
    lr_lift_up_limit_sensor: '加解锁平台升降上限位',
    lr_lift_down_limit_sensor: '加解锁平台升降下限位',
    lr_lift_home_sensor: '加解锁平台升降原点',
    vehical_f_up_limit_sensor: '左车辆举升上限位',
    vehical_f_down_limit_sensor: '左车辆举升下限位',
    vehical_f_home_sensor: '左车辆举升原点',
    vehical_r_up_limit_sensor: '右车辆举升上限位',
    vehical_r_down_limit_sensor: '右车辆举升下限位',
    vehical_r_home_sensor: '右车辆举升原点',
    bc_lift_up_limit_sensor: '升降仓上限位',
    bc_lift_down_limit_sensor: '升降仓下限位',
    bc_lift_home_sensor: '升降仓原点',
    bc_lift_safe_sensor: '接驳位举升电池安全',
    left_buffer_safe_sensor: '左缓存电池安全',
    right_buffer_safe_sensor: '右缓存电池安全',
    bc_slot22_reached_sensor: '消防仓电池落到位',
    bc_lift_exist_sensor: '接驳位上有电池检测',
    RGV_bc_reach_sensor_07: '电池平整7',
    RGV_bc_reach_sensor_08: '电池平整8',
    liq_lift_zero_sensor: '液压举升原点位'
  },
  4: {
    front_left_pressure_transducer: '左前压力传感器',
    right_rear_pressure_transducer: '右后压力传感器',
    bc_slot13_lc_retract_sensor_1: 'A1仓NPA水插头缩回到位',
    bc_slot13_lc_retract_sensor_2:	'A1仓NPD水插头缩回到位',
    bc_slot13_ec_retract_sensor_1:	'A1仓NPA电插头缩回到位',
    bc_slot13_liq_flow_switch_st:	'A1仓水流量开关',
    bc_slot14_lc_retract_sensor_1:	'A2仓NPA水插头缩回到位',
    bc_slot14_lc_retract_sensor_2:	'A2仓NPD水插头缩回到位',
    bc_slot14_ec_retract_sensor_1:	'A2仓NPA电插头缩回到位',
    bc_slot14_liq_flow_switch_st:	'A2仓水流量开关',
    bc_slot15_lc_retract_sensor_1:	'A3仓NPA水插头缩回到位',
    bc_slot15_lc_retract_sensor_2:	'A3仓NPD水插头缩回到位',
    bc_slot15_ec_retract_sensor_1:	'A3仓NPA电插头缩回到位',
    bc_slot15_liq_flow_switch_st:	'A3仓水流量开关',
    bc_slot16_lc_retract_sensor_1:	'A4仓NPA水插头缩回到位',
    bc_slot16_lc_retract_sensor_2:	'A4仓NPD水插头缩回到位',
    bc_slot16_ec_retract_sensor_1:	'A4仓NPA电插头缩回到位',
    bc_slot16_liq_flow_switch_st:	'A4仓水流量开关',
    bc_slot17_lc_retract_sensor_1:	'A5仓NPA水插头缩回到位',
    bc_slot17_lc_retract_sensor_2:	'A5仓NPD水插头缩回到位',
    bc_slot17_ec_retract_sensor_1:	'A5仓NPA电插头缩回到位',
    bc_slot17_liq_flow_switch_st:	'A5仓水流量开关',
    bc_fire_push_extend_sensor_1:	'消防仓前推杆伸出到位',
    bc_fire_push_retract_sensor_1:	'消防仓前推杆缩回到位',
    fire_liq_check:	'消防仓液位检测',
    bc_slot24_reached_sensor:	'消防仓电池落到位',
    bcslot13_17_pressure_switch_st:	'A1-A5仓水压力开关',
    bc_slot13_ec_retract_sensor_2:	'A1仓NPD电插头缩回到位',
    bc_slot13_reached_sensor:	'A1仓电池落到位',
    bc_slot14_ec_retract_sensor_2:	'A2仓NPD电插头缩回到位',
    bc_slot14_reached_sensor:	'A2仓电池落到位',
    bc_slot15_ec_retract_sensor_2:	'A3仓NPD电插头缩回到位',
    bc_slot15_reached_sensor:	'A3仓电池落到位',
    bc_slot16_ec_retract_sensor_2:	'A4仓NPD电插头缩回到位',
    bc_slot16_reached_sensor:	'A4仓电池落到位',
    bc_slot17_ec_retract_sensor_2:	'A5仓NPD电插头缩回到位',
    bc_slot17_reached_sensor:	'A5仓电池落到位',
    bc_slot13_smoke_sensor:	'A1仓烟雾报警',
    bc_slot14_smoke_sensor:	'A2仓烟雾报警',
    bc_slot15_smoke_sensor:	'A3仓烟雾报警',
    bc_slot16_smoke_sensor:	'A4仓烟雾报警',
    bc_slot17_smoke_sensor:	'A5仓烟雾报警',
    bc_slot18_lc_retract_sensor_1:	'A6仓NPA水插头缩回到位',
    bc_slot18_lc_retract_sensor_2:	'A6仓NPD水插头缩回到位',
    bc_slot18_ec_retract_sensor_1:	'A6仓NPA电插头缩回到位',
    bc_slot18_liq_flow_switch_st:	'A6仓水流量开关',
    bc_slot19_lc_retract_sensor_1:	'A7仓NPA水插头缩回到位',
    bc_slot19_lc_retract_sensor_2:	'A7仓NPD水插头缩回到位',
    bc_slot19_ec_retract_sensor_1:	'A7仓NPA电插头缩回到位',
    bc_slot19_liq_flow_switch_st:	'A7仓水流量开关',
    bc_slot20_lc_retract_sensor_1:	'A8仓NPA水插头缩回到位',
    bc_slot20_lc_retract_sensor_2:	'A8仓NPD水插头缩回到位',
    bc_slot20_ec_retract_sensor_1:	'A8仓NPA电插头缩回到位',
    bc_slot20_liq_flow_switch_st:	'A8仓水流量开关',
    bc_slot21_lc_retract_sensor_1:	'A9仓NPA水插头缩回到位',
    bc_slot21_lc_retract_sensor_2:	'A9仓NPD水插头缩回到位',
    bc_slot21_ec_retract_sensor_1:	'A9仓NPA电插头缩回到位',
    bc_slot21_liq_flow_switch_st:	'A9仓水流量开关',
    bc_slot22_lc_retract_sensor_1:	'A10仓NPA水插头缩回到位',
    bc_slot22_lc_retract_sensor_2:	'A10仓NPD水插头缩回到位',
    bc_slot22_ec_retract_sensor_1:	'A10仓NPA电插头缩回到位',
    bc_slot22_liq_flow_switch_st:	'A10仓水流量开关',
    bc_slot23_ec_retract_sensor_1:	'A11仓NPA电插头缩回到位',
    bc_fire_push_extend_sensor_2:	'消防仓后推杆伸出到位',
    bc_fire_push_retract_sensor_2:	'消防仓后推杆缩回到位',
    bcslot18_22_pressure_switch_st:	'A6-A10仓水压力开关',
    bc_slot18_ec_retract_sensor_2:	'A6仓NPD电插头缩回到位',
    bc_slot18_reached_sensor:	'A6仓电池落到位',
    bc_slot19_ec_retract_sensor_2:	'A7仓NPD电插头缩回到位',
    bc_slot19_reached_sensor:	'A7仓电池落到位',
    bc_slot20_ec_retract_sensor_2:	'A8仓NPD电插头缩回到位',
    bc_slot20_reached_sensor:	'A8仓电池落到位',
    bc_slot18_smoke_sensor:	'A6仓烟雾报警',
    bc_slot19_smoke_sensor:	'A7仓烟雾报警',
    bc_slot20_smoke_sensor:	'A8仓烟雾报警',
    bc_slot21_ec_retract_sensor_2:	'A9仓NPD电插头缩回到位',
    bc_slot21_reached_sensor:	'A9仓电池落到位',
    bc_slot22_ec_retract_sensor_2:	'A10仓NPD电插头缩回到位',
    bc_slot22_reached_sensor:	'A10仓电池落到位',
    bc_slot23_ec_retract_sensor_2:	'A11仓NPD电插头缩回到位',
    bc_slot23_reached_sensor:	'A11仓电池落到位',
    bc_slot21_smoke_sensor:	'A9仓烟雾报警',
    bc_slot22_smoke_sensor:	'A10仓烟雾报警',
    bc_slot23_smoke_sensor:	'A11仓烟雾报警',
    bc_slot1_ec_retract_sensor_1:	'C1仓NPA电插头缩回到位',
    bc_slot1_reached_sensor:	'C1仓电池落到位',
    bc_slot2_ec_retract_sensor_1:	'C2仓NPA电插头缩回到位',
    bc_slot2_reached_sensor:	'C2仓电池落到位',
    bc_slot3_ec_retract_sensor_1:	'C3仓NPA电插头缩回到位',
    bc_slot3_reached_sensor:	'C3仓电池落到位',
    bc_slot4_ec_retract_sensor_1:	'C4仓NPA电插头缩回到位',
    bc_slot4_reached_sensor:	'C4仓电池落到位',
    bc_slot5_ec_retract_sensor_1:	'C5仓NPA电插头缩回到位',
    bc_slot5_reached_sensor:	'C5仓电池落到位',
    bc_slot6_ec_retract_sensor_1:	'C6仓NPA电插头缩回到位',
    bc_slot6_reached_sensor:	'C6仓电池落到位',
    bc_slot1_ec_retract_sensor_2:	'C1仓NPD电插头缩回到位',
    bc_slot2_ec_retract_sensor_2:	'C2仓NPD电插头缩回到位',
    bc_slot3_ec_retract_sensor_2:	'C3仓NPD电插头缩回到位',
    bc_slot4_ec_retract_sensor_2:	'C4仓NPD电插头缩回到位',
    bc_slot5_ec_retract_sensor_2:	'C5仓NPD电插头缩回到位',
    bc_slot6_ec_retract_sensor_2:	'C6仓NPD电插头缩回到位',
    bc_slot1_smoke_sensor:	'C1仓烟雾报警',
    bc_slot2_smoke_sensor:	'C2仓烟雾报警',
    bc_slot3_smoke_sensor:	'C3仓烟雾报警',
    bc_slot4_smoke_sensor:	'C4仓烟雾报警',
    bc_slot5_smoke_sensor:	'C5仓烟雾报警',
    bc_slot6_smoke_sensor:	'C6仓烟雾报警',
    bc_slot7_ec_retract_sensor_1:	'C7仓NPA电插头缩回到位',
    bc_slot7_reached_sensor:	'C7仓电池落到位',
    bc_slot8_ec_retract_sensor_1:	'C8仓NPA电插头缩回到位',
    bc_slot8_reached_sensor:	'C8仓电池落到位',
    bc_slot9_ec_retract_sensor_1:	'C9仓NPA电插头缩回到位',
    bc_slot9_reached_sensor:	'C9仓电池落到位',
    bc_slot10_ec_retract_sensor_1:	'C10仓NPA电插头缩回到位',
    bc_slot10_reached_sensor:	'C10仓电池落到位',
    bc_slot11_ec_retract_sensor_1:	'C11仓NPA电插头缩回到位',
    bc_slot11_reached_sensor:	'C11仓电池落到位',
    bc_slot12_ec_retract_sensor_1:	'C12仓NPA电插头缩回到位',
    bc_slot12_reached_sensor:	'C12仓电池落到位',
    bc_slot7_ec_retract_sensor_2:	'C7仓NPD电插头缩回到位',
    bc_slot8_ec_retract_sensor_2:	'C8仓NPD电插头缩回到位',
    bc_slot9_ec_retract_sensor_2:	'C9仓NPD电插头缩回到位',
    bc_slot10_ec_retract_sensor_2:	'C10仓NPD电插头缩回到位',
    bc_slot11_ec_retract_sensor_2:	'C11仓NPD电插头缩回到位',
    bc_slot12_ec_retract_sensor_2:	'C12仓NPD电插头缩回到位',
    bc_slot7_smoke_sensor:	'C7仓烟雾报警',
    bc_slot8_smoke_sensor:	'C8仓烟雾报警',
    bc_slot9_smoke_sensor:	'C9仓烟雾报警',
    bc_slot10_smoke_sensor:	'C10仓烟雾报警',
    bc_slot11_smoke_sensor:	'C11仓烟雾报警',
    bc_slot12_smoke_sensor:	'C12仓烟雾报警',
    RGV_bc_reach_sensor_01:	'电池平整1',
    RGV_bc_reach_sensor_02:	'电池平整2',
    RGV_bc_reach_sensor_04:	'电池平整4',
    RGV_bc_reach_sensor_05:	'电池平整5',
    RGV_bc_reach_sensor_07:	'电池平整7',
    gun1_lift_home_sensor:	'1#枪升降下到位',
    gun2_lift_home_sensor:	'2#枪升降下到位',
    gun9_lift_home_sensor:	'9#枪升降下到位',
    gun10_lift_home_sensor:	'10#枪升降下到位',
    gun11_lift_home_sensor:	'11#枪升降下到位',
    gun12_lift_home_sensor:	'12#枪升降下到位',
    lr_pin_touch_sensor:	'左后车身定位销接触车身',
    lf_pin_retract_sensor:	'左前车身定位销下到位',
    rr_pin_retract_sensor:	'左后车身定位销下到位',
    lr_pin_retract_sensor:	'右后车身定位销下到位',
    pl_stopper_01_work_sensor:	'RGV前阻挡 上到位',
    pl_stopper_01_home_sensor:	'RGV前阻挡 下到位',
    pl_stopper_02_work_sensor:	'RGV后阻挡 上到位',
    pl_stopper_02_home_sensor:	'RGV后阻挡 下到位',
    l_bat_pin_retract_sensor:	'RGV左电池定位销原点位',
    r_bat_pin_retract_sensor:	'RGV右电池定位销原点位',
    pl_stopper_01_reach_sensor:	'RGV前阻挡 电池到位',
    pl_stopper_02_reach_sensor:	'RGV后阻挡 电池到位',
    pl_move_work_sensor_1:	'RGV平移加解锁位',
    pl_stopper_01_dece_sensor:	'RGV传送减速',
    fork_left_extend_sensor_1:	'货叉叉臂左到位1',
    fork_left_extend_sensor_2:	'货叉叉臂左到位2',
    fork_retract_sensor_2:	'货叉辅叉臂中点',
    stacker_move_f_sensor:	'堆垛机行走前仓位',
    stacker_move_r_sensor:	'堆垛机行走后仓位',
    stacker_move_RGV_sensor:	'堆垛机行走接驳位',
    stacker_left_safe_sensor_1:	'货叉上层左超程',
    stacker_right_safe_sensor_1:	'货叉上层右超程',
    stacker_high_sensor_1:	'堆垛机仓位对接',
    fork_right_extend_sensor_1:	'货叉叉臂右到位',
    stacker_bat_sensor_1:	'堆垛机电池区分1',
    stacker_bat_sensor_2:	'堆垛机电池区分2',
    stacker_bat_sensor_3:	'堆垛机电池区分3',
    pl_buffer_dece_sensor_2:	'接驳位电池减速',
    pl_buffer_sensor_f_2:	'接驳位前电池到位',
    buffer_stopper_01_extend_sensor_02:	'接驳位挡板前伸出到位',
    buffer_stopper_01_retract_sensor_02:	'接驳位挡板前缩回到位',
    pl_buffer_sensor_r_2:	'接驳位后电池到位',
    buffer_stopper_02_extend_sensor_02:	'接驳位挡板后伸出到位',
    buffer_stopper_02_retract_sensor_02:	'接驳位挡板后缩回到位',
    pl_f_guide_home_sensor:	'前导向条原点',
    right_buffer_safe_sensor:	'右RGV举升电池安全',
    liq_level_warning:	'平台液位检测',
    pl_door_01_close_sensor:	'左开合门关到位',
    pl_door_02_close_sensor:	'右开合门关到位',
    pl_door_01_open_sensor:	'左开合门开到位',
    pl_door_02_open_sensor:	'右开合门开到位',
    maintain_area_safety_01:	'维护门安全继电器反馈',
    maintain_area_safety_02:	'维护门传感器',
    left_buffer_safe_sensor:	'左RGV举升电池安全',
    pl_r_guide_home_sensor:	'后导向条原点',
    pl_buffer_dece_sensor_1:	'左缓存位电池减速',
    pl_buffer_sensor_f_1:	'左缓存位前电池到位',
    pl_buffer_sensor_r_1:	'左缓存位后电池到位',
    stacker_lift_up_limit_sensor:	'堆垛机升降上限位',
    stacker_lift_down_limit_sensor:	'堆垛机升降下限位',
    lr_lift_up_limit_sensor:	'加解锁平台升降上限位',
    lr_lift_home_sensor:	'加解锁平台升降原点',
    stacker_move_f_limit_sensor:	'堆垛机行走前限位',
    stacker_move_r_limit_sensor:	'堆垛机行走后限位'
  },
  7: {
    bc_slot1_reached_sensor: 'C1仓前电池落到位',
    bc_slot2_reached_sensor: 'C2仓前电池落到位',
    bc_slot3_reached_sensor: 'C3仓前电池落到位',
    bc_slot1_smoke_sensor: 'C1仓烟感报警',
    bc_slot2_smoke_sensor: 'C2仓烟感报警',
    bc_slot3_smoke_sensor: 'C3仓烟感报警',
    stacker_home_brake: '堆垛机防坠器回零',
    bc_slot4_reached_sensor: 'C4仓前电池落到位',
    bc_slot5_reached_sensor: 'C5仓前电池落到位',
    bc_slot6_reached_sensor: 'C6仓前电池落到位',
    bc_slot4_smoke_sensor: 'C4仓烟感报警',
    bc_slot5_smoke_sensor: 'C5仓烟感报警',
    bc_slot6_smoke_sensor: 'C6仓烟感报警',
    bc_slot10_lc_retract_sensor: 'A4仓水插头缩回到位',
    bc_slot11_lc_retract_sensor: 'A5仓水插头缩回到位',
    bc_slot12_lc_retract_sensor: 'A6仓水插头缩回到位',
    bc_slot10_reached_sensor: 'A4仓前电池落到位',
    bc_slot11_reached_sensor: 'A5仓前电池落到位',
    bc_slot12_reached_sensor: 'A6仓前电池落到位',
    bc_slot10_lc_cooling_swicth: 'A4仓水冷流量开关',
    bc_slot11_lc_cooling_swicth: 'A5仓水冷流量开关',
    bc_slot12_lc_cooling_swicth: 'A6仓水冷流量开关',
    Fire_bunker_1_extend_sensor: '消防前推杆伸出到位',
    Fire_bunker_1_retract_sensor: '消防前推杆缩回到位',
    bc_slot11_smoke_sensor: 'A5仓烟感报警',
    bc_slot12_smoke_sensor: 'A6仓烟感报警',
    bc_slot7_lc_retract_sensor: 'A1仓水插头缩回到位',
    bc_slot8_lc_retract_sensor: 'A2仓水插头缩回到位',
    bc_slot9_lc_retract_sensor: 'A3仓水插头缩回到位',
    bc_slot7_reached_sensor: 'A1仓前电池落到位',
    bc_slot8_reached_sensor: 'A2仓前电池落到位',
    bc_slot9_reached_sensor: 'A3仓前电池落到位',
    bc_slot7_lc_cooling_swicth: 'A1仓水冷流量开关',
    bc_slot8_lc_cooling_swicth: 'A2仓水冷流量开关',
    bc_slot9_lc_cooling_swicth: 'A3仓水冷流量开关',
    bc_slot7_smoke_sensor: 'A1仓烟感报警',
    bc_slot8_smoke_sensor: 'A2仓烟感报警',
    bc_slot9_smoke_sensor: 'A3仓烟感报警',
    bc_slot10_smoke_sensor: 'A4仓烟感报警',
    baffle_1_extend_sensor: '前端接驳上层伸出到位',
    baffle_1_retract_sensor: '前端接驳上层缩回到位',
    baffle_3_extend_sensor: '前端接驳下层伸出到位',
    baffle_3_retract_sensor: '前端接驳下层缩回到位',
    baffle_up_exist_sensor_f: '前端接驳上层电池有无',
    baffle_down_exist_sensor_f: '前端接驳下层电池有无',
    bc_lift_home_sensor_1: '前端接驳举升零点位',
    bc_lift_work_sensor_1: '前端接驳举升工作位',
    platfrom_water_check_1: '平台前段水位检测',
    platfrom_water_check_2: '平台后段水位检测',
    vehical_lift_lf_home_sensor: '左前磁栅尺零点位',
    RGV_S_home_sensor: 'S值调节零点位',
    vehical_lift_rf_home_sensor: '右前磁栅尺零点位',
    S_l_pin_work_sensor: 'S值左定位销伸出到位',
    vehical_lift_lr_home_sensor: '左后磁栅尺零点位',
    S_l_pin_home_sensor: 'S值左定位销缩回到位',
    vehical_lift_lf_home_brake: '左前防坠器零点到位',
    S_r_pin_work_sensor: 'S值右定位销伸出到位',
    vehical_lift_rf_home_brake: '右前防坠器零点到位',
    S_r_pin_home_sensor: 'S值右定位销缩回到位',
    vehical_lift_lr_home_brake: '左后防坠器零点到位',
    vehical_lift_rr_home_brake: '右后防坠器零点到位',
    RGV_lift_home_sensor: 'RGV升降零点位',
    RGV_move_work_sensor: 'RGV行走平台到位',
    RGV_move_home_sensor: 'RGV行走接驳位到位',
    RGV_reach_sensor_1: '左前电池平整',
    RGV_reach_sensor_2: '右前电池平整',
    RGV_reach_sensor_3: '左后电池平整',
    RGV_reach_sensor_4: '右后电池平整',
    lf_pin_home_sensor: '左前车身定位销下降到位',
    lf_pin_touch_sensor: '左前车身定位销接触车身',
    rr_pin_home_sensor: '右后车身定位销下降到位',
    rr_pin_touch_sensor: '右后车身定位销接触车身',
    bc_slot1_ec_retract_sensor: 'C1仓电插头缩回到位',
    bc_slot2_ec_retract_sensor: 'C2仓电插头缩回到位',
    bc_slot3_ec_retract_sensor: 'C3仓电插头缩回到位',
    bc_slot4_ec_retract_sensor: 'C4仓电插头缩回到位',
    bc_slot5_ec_retract_sensor: 'C5仓电插头缩回到位',
    bc_slot6_ec_retract_sensor: 'C6仓电插头缩回到位',
    door_open_sensor: '翻转门打开到位',
    door_close_sensor: '翻转门关闭到位',
    vehical_lift_rr_home_sensor: '右后磁栅尺零点位',
    baffle_2_extend_sensor: '后端接驳上层伸出到位',
    baffle_2_retract_sensor: '后端接驳上层缩回到位',
    baffle_4_extend_sensor: '后端接驳下层伸出到位',
    baffle_4_retract_sensor: '后端接驳下层缩回到位',
    baffle_up_exist_sensor_r: '后端接驳上层电池有无',
    baffle_down_exist_sensor_r: '后端接驳下层电池有无',
    bc_lift_home_sensor_2: '后端接驳举升零点位',
    bc_lift_work_sensor_2: '后端接驳举升工作位',
    stacker_lift_1_sensor: '堆垛机层仓对接1层',
    stacker_lift_2_sensor: '堆垛机层仓对接2层',
    stacker_lift_3_sensor: '堆垛机层仓对接3层',
    stacker_lift_4_sensor: '堆垛机层仓对接4层',
    stacker_lift_5_sensor: '堆垛机层仓对接5层',
    stacker_lift_6_sensor: '堆垛机层仓对接6层',
    stacker_lift_home_sensor: '堆垛机零点位',
    stacker_pin_1_sensor: '堆垛机维护插销1',
    stacker_pin_2_sensor: '堆垛机维护插销2',
    fork_work_sensor_1: '货叉叉臂左到位',
    fork_work_sensor_3: '货叉叉臂右到位',
    fork_home_sensor_1: '货叉主叉臂中点',
    fork_home_sensor_2: '货叉辅叉臂中点',
    fork_exist_sensor_1: '货叉有电池检测1',
    fork_exist_sensor_2: '货叉有电池检测2',
    bc_safe_sensor_1: '货叉上层左超程',
    bc_safe_sensor_2: '货叉上层右超程',
    bc_slot7_ec_retract_sensor: 'A1仓电插头缩回到位',
    bc_slot8_ec_retract_sensor: 'A2仓电插头缩回到位',
    bc_slot9_ec_retract_sensor: 'A3仓电插头缩回到位',
    bc_fire_water_check_1: '消防储水箱液位检测',
    bc_slot10_ec_retract_sensor: 'A4仓电插头缩回到位',
    bc_slot11_ec_retract_sensor: 'A5仓电插头缩回到位',
    bc_slot12_ec_retract_sensor: 'A6仓电插头缩回到位',
    Fire_bunker_2_extend_sensor: '消防仓后段电推杆伸出到位',
    Fire_bunker_2_retract_sensor: '消防仓后段电推杆缩回到位',
    bc_slot13_reached_sensor: '消防仓接驳电池落到位',
    bc_fire_water_check_2: '消防水槽液位检测',
    bc_fire_water_T_check: '消防水槽温度检测',
    pl_move_f_limit_sensor: 'RGV行走伺服前限位',
    pl_move_r_limit_sensor: 'RGV行走伺服后限位',
    lr_lift_up_limit_sensor: 'RGV举升伺服上限位',
    lr_lift_down_limit_sensor: 'RGV举升伺服下限位',
    fork_X_left_limit_sensor: '堆垛机货叉伺服左限位',
    fork_X_right_limit_sensor: '堆垛机货叉伺服右限位',
    stacker_lift_up_limit_sensor: '堆垛机升降伺服上限位',
    stacker_lift_down_limit_sensor: '堆垛机升降伺服下限位',
    maintain_area_safety_01: '左维护门安全检测',
    maintain_area_safety_02: '右维护门安全检测',
    fire_door_check_1: '消防应急门开合传感器检测前',
    fire_door_check_2: '消防应急门开合传感器检测后'
  }
} as any
