export const platform_step = {
  1: {
    1: '1 - 初始化自检',
    2: '2 - 推杆定位，车身抬升到接触位',
    3: '3 - 停车平台视觉判断',
    4: '4 - 车身抬升到工作位',
    5: '5 - RGV到停车平台',
    6: '6 - 旋转平台到90度位置',
    7: '7 - RGV视觉判断',
    8: '8 - RGV抬升到工作位，电磁铁吸合',
    9: '9 - 解锁',
    10: '10 - RGV低速下降到水电插头位',
    11: '11 - RGV高速下降到卡销位',
    12: '12 - 旋转平台到0度位置，RGV下降到原位',
    13: '13 - RGV行走到电池仓',
    14: '14 - 新旧电池更换',
    15: '15 - RGV行走到停车平台',
    16: '16 - 旋转平台到90度位置，车身定位销伸出',
    17: '17 - RGV视觉判断',
    18: '18 - RGV抬升至销子位，车身定位销伸出',
    19: '19 - RGV抬升至工作位，车身定位销缩回',
    20: '20 - 加锁',
    21: '21 - RGV下降到中间位，车身定位销缩回',
    22: '22 - 旋转平台到0度位置',
    23: '23 - RGV行走到电池仓',
    24: '24 - 车身下降，四轮定位退回',
  },
  2: {
    1: '1 - 车轮推杆夹紧，开合门打开',
    2: '2 - 车辆举升_车有电池',
    3: '3 - RGV举升_无电池',
    4: '4 - 解锁',
    5: '5 - RGV下降_有电池',
    6: '6 - 车辆下降_车无电池',
    7: '7 - 亏电池到缓冲位',
    8: '8 - 车辆举升_车无电池',
    9: '9 - RGV举升_有电池,定位销伸出+部分缩回',
    10: '10 - 加锁',
    11: '11 - RGV下降_无电池,车轮推杆缩回',
    12: '12 - 车辆下降_车有电池, 定位销缩回',
    13: '13 - 亏电池从缓冲位出来，开合门关闭',
    14: '14 - 开合门关闭',
  },
  3: {
    1: '1 - 推杆一次定位',
    2: '2 - 推杆二次定位&开合门打开',
    3: '3 - RGV举升至工作位',
    4: '4 - 电磁铁吸合&解锁',
    5: '5 - 电磁铁释放&RGV下降',
    6: '6 - 开合门关闭&RGV平移至电池对接位',
    7: '7 - 前后导向条伸出',
    8: '8 - 电池从停车平台流转至左缓存位',
    9: '9 - 接驳机下降零点位&RGV挡块伸出&右缓存位挡块缩回',
    10: '10 - 电池从接驳机流转至停车平台',
    11: '11 - 前后导向条缩回&开合门打开',
    12: '12 - RGV平移至加解锁位',
    13: '13 - RGV举升至卡销位',
    14: '14 - RGV举升至销子位&车身定位销伸出&平台阻挡块缩回',
    15: '15 - RGV举升至工作位&车身定位销缩回',
    16: '16 - 电磁铁吸合&加锁',
    17: '17 - 电磁铁释放&RGV下降至原点位&车身定位销缩回',
    18: '18 - 推杆至零点位&开合门关闭&RGV平移至电池流转位',
  },
  4: {
    1: '1 - 推杆一次定位',
    2: '2 - 推杆二次定位&开合门打开',
    3: '3 - RGV举升至工作位',
    4: '4 - 电磁铁吸合&解锁',
    5: '5 - 电磁铁释放&RGV下降',
    6: '6 - 开合门关闭&RGV平移至电池对接位',
    7: '7 - 前后导向条伸出',
    8: '8 - 电池从停车平台流转至左缓存位',
    10: '10 - 电池从接驳位流转至停车平台',
    11: '11 - 前后导向条缩回&开合门打开',
    12: '12 - RGV平移至加解锁位',
    13: '13 - RGV举升至卡销位',
    14: '14 - RGV举升至销子位&车身定位销伸出&平台阻挡块缩回',
    15: '15 - RGV举升至工作位&车身定位销缩回',
    16: '16 - 电磁铁吸合&加锁',
    17: '17 - 电磁铁释放&RGV下降至原点位&车身定位销缩回',
    18: '18 - 推杆至零点位&开合门关闭&RGV平移至电池流转位',
  },
  7: {
    1: '1 - 四轮推杆至工作位',
    2: '2 - 翻转门打开',
    3: '3 - 车辆举升至工作位',
    4: '4 - RGV空载行走到停车平台',
    5: '5 - 车身定位销伸出&RGV空载举升至销子位',
    6: '6 - 电磁铁释放，RGV举升至工作&车身定位销同步缩回',
    7: '7 - 电磁铁吸合&解锁',
    8: '8 - 解锁完成RGV下降至插头位',
    9: '9 - RGV下降至零点位',
    10: '10 - 电磁铁吸合&RGV行走至接驳位',
    11: '11 - 接驳机上层挡块伸出',
    12: '12 - 接驳机举升至工作位',
    13: '13 - 接驳机下层挡块伸出',
    14: '14 - 货叉满电电池放到接驳机',
    15: '15 - 接驳机举升至中间位',
    16: '16 - 接驳机下层挡块缩回',
    17: '17 - 电磁铁吸合&RGV带载行走至停车平台',
    18: '18 - 车身定位销伸出&RGV带载举升至销子位',
    19: '19 - 电磁铁释放&RGV举升至工作位&车身定位销同步缩回',
    20: '20 - 电磁铁吸合&车身定位销缩回',
    21: '21 - 加锁',
    22: '22 - RGV下降至销子位',
    23: '23 - RGV下降至零点位',
    24: '24 - 货叉取亏电电池完成',
    25: '25 - 电磁铁吸合&RGV空载行走至接驳位',
    26: '26 - 电磁铁释放&四轮推杆至零点&车辆举升下降至零点&翻转门关闭'
  }
} as any

export const battery_step = {
  1: {
    1: '1 - 初始化',
    2: '2 - 升降机空载升目标仓位',
    3: '3 - 目标仓电池出仓',
    4: '4 - 升降机带载降6仓位',
    5: '5 - 电池进6仓',
    6: '6 - 升降机升至对接位',
    7: '7 - 电池传送至升降机',
    8: '8 - 升降机带载升目标仓位',
    9: '9 - 电池进目标仓位',
    10: '10 - 升降机空载降6仓位',
    11: '11 - 6仓电池出仓',
    12: '12 - 升降机升至对接位',
    13: '13 - 电池转移到RGV',
  },
  2: {
    1: '1 - 升降机空载升到目标仓高度',
    2: '2 - 升降机带载降到对接位高度',
    // 3: '3 - 暂无 (不显示)',
    4: '4 - 服务电池出升降机到平台',
    5: '5 - 升降机带载升到目标仓高度',
    6: '6 - 升降机空载降到对接位高度',
  },
  3: {
    1: '1 - 水电插头缩回&堆垛机至目标仓对接位&接驳机举升',
    2: '2 - 货叉目标仓伸出&堆垛机升至高位&货叉缩回',
    3: '3 - 堆垛机至接驳机对接位',
    4: '4 - 货叉接驳机伸出&接驳机升至工作位&货叉缩回',
    5: '5 - 接驳机降至原点位&平台挡块缩回',
    6: '6 - 前后导向条伸出&电池从左缓存位至接驳机',
    7: '7 - 接驳机举升至工作位',
    8: '8 - 货叉接驳机伸出&接驳机至原点位&货叉缩回',
    9: '9 - 堆垛机至目标仓&货叉高位伸出&堆垛机至低位&货叉缩回',
    10: '10 - 水电插头伸出',
    11: '11 - 堆垛机从目标仓至初始位',
  },
  4: {
    1: '1 - 水电插头缩回&堆垛机至目标仓对接位',
    2: '2 - 货叉目标仓伸出&堆垛机升至高位&货叉缩回',
    3: '3 - 堆垛机至接驳位',
    4: '4 - 货叉伸出&货叉下降&货叉缩回',
    5: '5 - 平台挡块缩回',
    6: '6 - 前后导向条伸出&电池从缓存位至接驳位',
    8: '8 - 货叉伸出&货叉抬升&货叉缩回',
    9: '9 - 堆垛机至目标仓&货叉高位伸出&堆垛机至低位&货叉缩回',
    10: '10 - 水电插头伸出',
    11: '11 - 堆垛机从目标仓至初始位',
  },
  7: {
    1: '1 - 水电插头缩回&堆垛机升降至目标仓低位',
    2: '2 - 货叉在目标仓伸出',
    3: '3 - 堆垛机升降至目标仓高位',
    4: '4 - 货叉在目标仓高位缩回',
    5: '5 - 堆垛机升降至接驳高位',
    6: '6 - 货叉带满电电池准备伸出',
    7: '7 - 货叉在接驳机高位伸出',
    8: '8 - 堆垛机至接驳机低位',
    9: '9 - 货叉在接驳机低位缩回',
    10: '10 - 货叉在接驳机低位准备取亏电电池',
    11: '11 - 货叉在接驳机低位伸出',
    12: '12 - 接驳举升中位至零点位',
    13: '13 - 货叉在接驳机低位带亏电电池缩回',
    14: '14 - 堆垛机至目标仓高位接驳上层托板缩回',
    15: '15 - 货叉在目标仓高位伸出',
    16: '16 - 堆垛机至目标仓低位',
    17: '17 - 货叉在目标仓低位缩回',
    18: '18 - 目标仓水电插头伸出',
    19: '19 - 堆垛机至初始位'
  }
} as any
