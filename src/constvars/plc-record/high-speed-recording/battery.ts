import { axis } from './axis';
import { battery_step } from '../step';

const battery_step_axis_7 = {
  1: [17, 18],
  2: [17, 18],
  3: [17, 18],
  4: [17, 18],
  5: [17, 18],
  6: [17, 18],
  7: [17, 18],
  8: [17, 18],
  9: [17, 18],
  10: [17, 18],
  11: [17, 18],
  12: [25, 26],
  13: [17, 18],
  14: [17, 18],
  15: [17, 18],
  16: [17, 18],
  17: [17, 18],
  19: [17, 18]
}

const battery_step_axis_4 = {
  1: [2, 3],
  2: [1, 3],
  3: [2, 3],
  4: [1],
  5: [],
  6: [33, 34],
  7: [],
  8: [1],
  9: [1, 2, 3],
  10: [],
  11: [2, 3],
};

const battery_step_axis_3 = {
  1: [2, 3, 40],
  2: [1, 3],
  3: [2, 3],
  4: [1, 40],
  5: [40],
  6: [31, 35],
  7: [],
  8: [40],
  9: [1, 2, 3],
  10: [],
  11: [2, 3],
};

const battery_step_axis_2 = {
  1: [23],
  2: [23],
  4: [23],
  5: [23],
  6: [23],
};

const battery_step_axis_1 = {
  1: [13],
  2: [13],
  3: [13],
  4: [13],
  5: [13],
  6: [13],
  7: [13],
  8: [13],
  9: [13],
  10: [13],
  11: [13],
  12: [13],
  13: [13],
};

export const battery = {
  1: {
    step: battery_step[1],
    axis: axis[1],
    step_axis: battery_step_axis_1,
  },
  2: {
    step: battery_step[2],
    axis: axis[2],
    step_axis: battery_step_axis_2,
  },
  3: {
    step: battery_step[3],
    axis: axis.battery3,
    step_axis: battery_step_axis_3,
  },
  4: {
    step: battery_step[4],
    axis: axis.battery4,
    step_axis: battery_step_axis_4,
  },
  7: {
    step: battery_step[7],
    axis: axis.battery_firefly_1,
    step_axis: battery_step_axis_7,
  }
};
