// 一代站轴名称
const axis_1 = {
  1: '加解锁1',
  2: '加解锁2',
  3: '加解锁3',
  4: '加解锁4',
  5: '加解锁5',
  6: '加解锁6',
  7: '加解锁7',
  8: '加解锁8',
  9: '加解锁9',
  10: '加解锁10',
  11: 'RGV抬升',
  12: 'RGV行走',
  13: '升降机',
  14: '转盘',
  15: '左前推杆',
  16: '右前推杆',
  17: '左后推杆',
  18: '右后推杆',
  19: '左前立柱',
  20: '右前立柱',
  21: '左后立柱',
  22: '右后立柱'
}

// 二代站轴名称
const axis_2 = {
  1: '加解锁1',
  2: '加解锁2',
  3: '加解锁3',
  4: '加解锁4',
  5: '加解锁5',
  6: '加解锁6',
  7: '加解锁7',
  8: '加解锁8',
  9: '加解锁9',
  10: '加解锁10',
  11: '左前定位销',
  12: '右后定位销',
  13: '左前4轮推杆',
  14: '右前4轮推杆',
  15: '左后4轮推杆',
  16: '右后4轮推杆',
  17: 'V型槽',
  18: '左开合门',
  19: '右开合门',
  20: '车辆举升左',
  21: '车辆举升右',
  22: '加解锁举升',
  23: '提升机'
}

// 三代站平台侧轴名称
const axis_plat_3 = {
  4: '枪1',
  5: '枪2',
  6: '枪3',
  7: '枪4',
  8: '枪5',
  9: '枪6',
  10: '枪7',
  11: '枪8',
  12: '枪9',
  13: '枪10',
  14: "枪1'",
  15: "枪2'",
  16: '左开合门',
  17: '右开合门',
  18: '1号枪头升降',
  19: '2号枪头升降',
  20: '9号枪头平移',
  21: '9号枪头升降',
  22: '10号枪头平移',
  23: '10号枪头升降',
  24: "1'号枪头升降",
  25: "2'号枪头升降",
  26: '左前车身定位销',
  27: '左后车身定位销',
  28: '右后车身定位销',
  29: '左前推杆',
  30: '右前推杆',
  31: '前导向条',
  32: 'V型槽',
  33: '左后推杆',
  34: '右后推杆',
  35: '后导向条',
  36: '加解锁平台平移',
  37: '加解锁平台举升',
  40: '接驳机'
}

// 四代站平台侧轴名称
const axis_plat_4 = {
  4: '枪1',
  5: '枪2',
  6: '枪3',
  7: '枪4',
  8: '枪5',
  9: '枪6',
  10: '枪7',
  11: '枪8',
  12: '枪9',
  13: '枪10',
  14: '枪11',
  15: '枪12',
  16: '左开合门',
  17: '右开合门',
  18: '1号枪头升降',
  19: '2号枪头升降',
  20: '9号枪头升降',
  21: '10号枪头升降',
  22: '11号枪头升降',
  23: '12号枪头升降',
  26: '左前车身定位销',
  27: '左后车身定位销',
  28: '右后车身定位销',
  29: '左前推杆',
  30: '右前推杆',
  31: '左后推杆',
  32: '右后推杆',
  33: '前导向条',
  34: '后导向条',
  35: '加解锁平台平移',
  36: '加解锁平台举升'
}

const axis_plat_firefly_1 = {
  1: '拧紧枪1伺服',
  2: '拧紧枪2伺服',
  3: '拧紧枪3伺服',
  4: '拧紧枪4伺服',
  5: '拧紧枪5伺服',
  6: '拧紧枪6伺服',
  7: '拧紧枪7伺服',
  8: '拧紧枪8伺服',
  9: '左前车身定位销伺服',
  10: '右后车身定位销伺服',
  11: '左前车轮推杆伺服',
  12: '右前车轮推杆伺服',
  13: '左后车轮推杆伺服',
  14: '右后车轮推杆伺服',
  15: 'RGV行走伺服',
  16: 'RGV举升伺服',
  // 17: '堆垛机货叉伺服',
  // 18: '堆垛机升降伺服',
  19: '翻转门伺服',
  20: 'RGV平移伺服',
  21: '左前车辆举升',
  22: '右前车辆举升',
  23: '左后车辆举升',
  24: '右后车辆举升'
  // 25: '前接驳举升',
  // 26: '后接驳举升'
}

// 三代站电池仓轴名称
const axis_battery_3 = {
  1: '货叉',
  2: '堆垛机平移',
  3: '堆垛机升降',
  31: '前导向条',
  35: '后导向条',
  40: '接驳机'
}

// 四代站电池仓轴名称
const axis_battery_4 = {
  1: '货叉',
  2: '堆垛机平移',
  3: '堆垛机升降',
  33: '前导向条',
  34: '后导向条'
}

const axis_battery_firefly_1 = {
  17: '堆垛机货叉伺服',
  18: '堆垛机升降伺服',
  25: '前接驳举升',
  26: '后接驳举升'
}

export const axis = {
  1: axis_1,
  2: axis_2,
  plat3: axis_plat_3,
  plat4: axis_plat_4,
  firefly1: axis_plat_firefly_1,
  battery3: axis_battery_3,
  battery4: axis_battery_4,
  battery_firefly_1: axis_battery_firefly_1
}
