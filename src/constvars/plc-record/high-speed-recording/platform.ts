import { getSeqArray } from '~/utils'
import { axis } from './axis'
import { platform_step } from '../step'

const platform_step_axis_7 = {
  1: getSeqArray(11, 14),
  2: [19],
  3: getSeqArray(21, 24),
  4: [15],
  5: [9, 10, 16],
  6: [9, 10, 16],
  7: get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(1, 8),
  8: [16],
  9: [16],
  10: [15],
  14: [17],
  15: [25, 26],
  17: [15],
  18: [9, 10, 16],
  19: [9, 10, 16],
  20: [9, 10],
  22: [16],
  23: [16],
  24: [17],
  25: [15],
  26: [11, 12, 13, 14, 19, 21, 22, 23, 24]
}

const platform_step_axis_4 = {
  1: [29, 30, 31, 32],
  2: [29, 30, 31, 32, 16, 17],
  3: [36],
  4: getS<PERSON><PERSON><PERSON><PERSON><PERSON>(4, 15),
  5: [36],
  6: [16, 17, 35],
  7: [33, 34],
  11: [33, 34, 16, 17],
  12: [35],
  13: [36],
  14: [36, 26, 27, 28],
  15: [36, 26, 27, 28],
  16: getSe<PERSON><PERSON><PERSON><PERSON>(4, 15),
  17: [36, 26, 27, 28],
  18: [29, 30, 31, 32, 16, 17, 35]
}

const platform_step_axis_3 = {
  1: [29, 30, 33, 34],
  2: [29, 30, 33, 34, 16, 17],
  3: [37],
  4: getSeq<PERSON><PERSON>y(4, 15),
  5: [37],
  6: [16, 17, 36],
  7: [31, 35],
  // 8:无伺服, 有变频器(1,2)
  // 8: [20, 21],
  9: [40],
  // 10:有变频器(1,2)
  // 10: [40],
  11: [31, 35, 16, 17],
  12: [36],
  13: [37],
  14: [37, 26, 27, 28],
  15: [37, 26, 27, 28],
  16: getSeqArray(4, 15),
  17: [36, 26, 27, 28],
  18: [29, 30, 33, 34, 16, 17, 36]
}

const platform_step_axis_2 = {
  1: [13, 14, 15, 16, 18, 19],
  2: [20, 21],
  3: [22],
  4: getSeqArray(1, 10),
  5: [22],
  6: [20, 21],
  // 7: [],
  8: [20, 21],
  9: [22, 11, 12],
  10: getSeqArray(1, 10),
  11: [22, 11, 12],
  12: [20, 21],
  // 13: [],
  14: [13, 14, 15, 16, 18, 19]
}

const platform_step_axis_1 = {
  1: [],
  2: [13, ...getSeqArray(15, 22)],
  3: [13, 19, 20, 21, 22],
  4: [13, 19, 20, 21, 22],
  5: [11, 12, 13],
  6: [13, 14],
  7: [11, 13],
  8: [11, 13],
  9: [...getSeqArray(1, 10), 13],
  10: [11, 13],
  11: [11, 13, 14],
  12: [11, 12, 13, 14],
  13: [12, 13],
  14: [11, 13],
  15: [12],
  16: [14],
  17: [],
  18: [11],
  19: [11],
  20: getSeqArray(1, 10),
  21: [11],
  22: [14],
  23: [12],
  24: getSeqArray(15, 22)
}

export const platform = {
  1: {
    step: platform_step[1],
    step_axis: platform_step_axis_1,
    axis: axis[1]
  },
  2: {
    step: platform_step[2],
    step_axis: platform_step_axis_2,
    axis: axis[2]
  },
  3: {
    step: platform_step[3],
    step_axis: platform_step_axis_3,
    axis: axis.plat3
  },
  4: {
    step: platform_step[4],
    step_axis: platform_step_axis_4,
    axis: axis.plat4
  },
  7: {
    step: platform_step[7],
    step_axis: platform_step_axis_7,
    axis: axis.firefly1
  }
}
