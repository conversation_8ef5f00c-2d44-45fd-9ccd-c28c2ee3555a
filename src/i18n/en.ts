import * as map from './en/index';

const common = {
  pp_remove_success: 'Unfavorite Successfully',
  pp_remove: 'Unfavorite',
  pp_stars_success: 'Favorite Successfully',
  pp_stars: 'Favorite',
  pp_edit_success: 'Edit Successfully',
  pp_analysis_success: 'Analysis Successfully',

  pp_unnamed_device: 'Unnamed Device',
  pp_regional_company: 'Regional Company',
  pp_region: 'Region',

  pp_time: 'Time',
  pp_date: 'Date',
  pp_time_frame: 'Time Frame',
  pp_select_time: 'Select Time',
  pp_please_select: 'Select ',
  pp_please_input: 'Enter ',
  pp_start_time: 'Start Time',
  pp_end_time: 'End Time',
  pp_to: 'To',

  pp_empty: 'No Data',
  pp_no_data: 'No Data',
  pp_loading: 'Loading',

  pp_operation: 'Operation',
  pp_reset: 'Reset',
  pp_cancel: 'Cancel',
  pp_confirm: 'Confirm',
  pp_search: 'Search',
  pp_restore: 'Restore',
  pp_edit: 'Edit',
  pp_delete: 'Delete',
  pp_download: 'Download',
  pp_download_data: 'Download',
  pp_downloading: 'Downloading',
  pp_create: 'Create',
  pp_recreate: 'Recreate',
  pp_view_details: 'View Details',
  pp_logout: 'Logout',
  pp_view: 'View',
  pp_push: 'Push',
  pp_revoke: 'Revoke',
  pp_close: 'Close',
  pp_filter: 'Search',

  pp_last_hour: 'Last Hour',
  pp_last_24_hours: 'Last 24 Hours',
  pp_last_day: 'Last Day',
  pp_last_7_days: 'Last 7 Days',
  pp_last_30_days: 'Last 30 Days',
  pp_last_week: 'Last Week',
  pp_last_month: 'Last Month',
  pp_last_2_month: 'Last 2 Months',
  pp_last_3_month: 'Last 3 Months',
  pp_last_6_month: 'Last 6 Months',
  pp_last_year: 'Last Year',
  pp_today: 'Today',
  pp_yesterday: 'Yesterday',
  pp_week_ago: 'A week ago',

  pp_submit_success: 'You have successfully submitted',
  pp_confirm_submit: 'Submit ',

  pp_yes: 'Yes',
  pp_no: 'No',
  pp_normal: 'Normal',
  pp_abnormal: 'Abnormal',

  pp_download_message: "Download will start soon, please don't click repeatedly!",
  pp_lack_permission: 'Sorry! You do not have permission to access this page, please contact junji.ma or liu.yang4 to open permissions',

  pp_lack_image: 'No Image',
  pp_copy_success: 'Successfully copied to clipboard',
  pp_look_forward: 'No data available yet, please look forward to it~',

  pp_start: 'Start',
  pp_end: 'End',
  pp_back: 'Back',
  pp_reminder: 'Reminder',
  pp_device: 'Device',
  pp_device_id: 'Device ID',
  pp_device_name: 'Device Name',
  pp_resource_id: 'Virtual Device ID',
  pp_jump: 'Jump',
  pp_enter: 'Enter Device ID Or Name',
  pp_import: 'Import',
  pp_path_change: 'The page address has changed, you have been redirected to the latest address',
  pp_pss1: 'PSS 1.0',
  pp_pss2: 'PSS 2.0',
  pp_pss3: 'PSS 3.0',
  pp_pss4: 'PSS 4.0',
  pp_psc1: '120kW Integrated Machine 1.0',
  pp_psc2: 'PSC 2.0',
  pp_psc3: 'PSC 3.0',
  pp_psc4: 'PSC 4.0',
  pp_status: 'Status',
  pp_start_date: 'Start Date',
  pp_end_date: 'End Date',
  pp_expand: 'Expand',
  pp_collapse: 'Collapse',
  pp_minute: 'm',
  pp_second: 's',
  pp_vehicle_brand: 'Brand',
  pp_device_type: 'Device Type',
  pp_order_id: 'Order ID',
  pp_vehicle_platform: 'Vehicle Platform',
  pp_vehicle_id: 'Vehicle ID',
  pp_order_start_time: 'Order Start Time',
  pp_order_end_time: 'Order End Time',
  pp_detail: 'Detail',
  pp_yuan: 'Yuan',
  pp_ten_thousand: 'Ten thousand',
  pp_billion: 'Billion',
  pp_order_status: 'Order Status',
  pp_urban_company: 'Urban Company',
  pp_company_division: 'Company Division',
  pp_image_empty: 'No Image Data',
  pp_set_up: 'Set Up',
  pp_please_search: 'Please Search',
  pp_prompt: 'Prompt',
  pp_channel: 'Channel',
  pp_vin: 'Vehicle VIN',
  pp_service_id: 'Service ID',
  pp_alarm_info: 'Alarm Info',
  pp_remark: 'Remark',
  pp_device_select: 'Device',
};

const menu = {
  pp_platform: 'Equipment Monitoring Platform',
  pp_charge_pile: 'PSC',
  pp_swap_station1: 'PSS 1.0',
  pp_swap_station2: 'PSS 2.0',
  pp_swap_station3: 'PSS 3.0',
  pp_swap_station4: 'PSS 4.0',
  pp_cloud: 'Edge Cloud',
  pp_power_grid: 'Power Grid',
  pp_order_list: 'Order List',
  pp_cluster_list: 'Cluster List',
  pp_app_list: 'APP List',
  pp_data_version: 'Data Version',
  pp_version_management: 'Version No.',
  // pp_temperature_feature: 'Temperature Feature',
  pp_temperature_feature: 'Gun Failures',
  pp_data_fetch: 'Data Fetch',
  pp_camera_management: 'Camera Check',
  pp_image_management: 'Image',
  pp_battery_image: 'Battery Image',
  pp_faq: 'FAQ',
  pp_flow_chart: 'AI Flowchart',
  pp_owl: 'OWL',
  pp_home: 'Home',
  pp_data_analysis: 'Data Analysis',
  pp_factory_management: 'Ex-factory',
  pp_user_management: 'User List',
  pp_edge_management: 'Edge',
  pp_pending_page: 'Pending Page',
  // pp_supercharged_pile: 'Supercharged Pile',
  pp_supercharged_pile: 'PSC',
  pp_single_service_list: 'Single Service List',
  pp_single_image_list: 'Single Image List',
  pp_current_data: 'Current Data',
  pp_history_data: 'History Data',
  pp_charge_module: 'Charging Module',
  pp_alarm_list: 'Alarm List',
  pp_alarm_analysis: 'Alarm Analysis',
  pp_log_transfer: 'Log Transfer',
  pp_warning_list: 'Alarms',
  pp_operation_log: 'Operation Log',
  pp_log_export: 'Log Export',
  pp_algorithm_visualization: 'AI Portrait',

  pp_device_management: 'Device List',
  pp_device_detail: 'Device Detail',

  pp_service_list: 'Service List',
  pp_service_detail: 'Service Detail',

  pp_image_list: 'Image List',
  pp_log_list: 'Log List',

  pp_tank_trans_list: 'Tank List',
  pp_tank_trans_detail: 'Tank Detail',

  pp_order_image: 'AI Snapshot',

  pp_fault_list: 'Fault List',
  pp_fault_diagnosis: 'Fault Diagnosis',
  pp_snapshot_diagnosis: 'Snapshot Diagnosis',
  pp_recording_diagnosis: 'Recording Diagnosis',
  pp_remote_diagnosis: 'Remote Diagnosis',
  pp_service_trace: 'Service Trace',
  pp_battery_swap: 'Battery Swap',
  pp_grid_info: 'Dashboard',
  pp_license_match: 'License Match',
  pp_log_analysis: 'Log Analysis',
  pp_afrr_dashboard: 'Afrr Dashboard',
  pp_process_error: 'Process Error',
  pp_fire_alarm: 'Fire Alarm',
  pp_diagnosis_info: 'Diagnosis Info',
  pp_parking_occupancy: 'Parking Occupancy',
  pp_stuck_analysis: 'Stuck Analysis',

  pp_device_simulation: 'Device Simulation',
  pp_run_list: 'Run List',
  pp_configuration_management: 'Configuration Management',
  pp_device_health: 'Device Health',
  pp_config_list: 'Config List',
  pp_flow_monitoring: 'Flow Monitoring',
  pp_info_trace: 'Info Trace',
  pp_satisfaction_analysis: 'Satisfaction',
  pp_flash_log: 'Battery Flash',
  pp_bluetooth_disconnection: 'Bluetooth',
  pp_portrait_list: 'Portrait List',
  pp_station_management: 'Station Management',
  pp_data_overview: 'Data Overview',
  pp_energy_list: 'Energy List',
  pp_revenue_list: 'Revenue List',
  pp_firefly1: 'Firefly 1.0',
  pp_device_version: 'Device Version',
  pp_charge_list: 'Charge List',
  pp_bi_dashboard: 'BI Dashboard',
};

const home = {
  pp_star_list: 'Star',
  pp_project: 'Device Type',
  pp_empty_description: 'There is nothing here yet, please go ahead and add devices to your stars',
};

const station = {
  pp_collect_the_device: 'Favorite The Device',
  pp_time_frame: 'Time Frame',
  pp_image_type: 'Image Type',
  pp_image_name: 'Image Name',
  pp_expand: 'Expand',
  pp_collapse: 'Collapse',
  pp_select_image_type: 'Select Image Type',
  pp_select_image_status: 'Select Image Status',

  pp_service_start_time: 'Service Start Time',
  pp_service_end_time: 'Service End Time',
  pp_service_duration: 'Service Duration',
  pp_service_battery_id: 'Service Battery ID',
  pp_input_service_battery_id: 'Enter Service Battery ID',
  pp_car_battery_id: 'Car Battery ID',
  pp_input_car_battery_id: 'Enter Car Battery ID',
  pp_car_id: 'Car ID',
  pp_input_car_id: 'Enter Car ID',

  pp_battery_id: 'Battery ID',
  pp_service_id: 'Service ID',
  pp_order_id: 'Order ID',

  pp_abnormal_image: 'Abnormal Image',
  pp_image_status: 'Image Status',
  pp_all: 'All',
  pp_include: 'Include',
  pp_exclude: 'Exclude',
  pp_warning: 'Warning',

  pp_view_image: 'View Image',
  pp_view_normal_image: 'View Normal Image',
  pp_view_abnormal_picture: 'View Abnormal Image',
  pp_view_flash: 'View Battery Flash',
  pp_create_ts: 'Create Time',
  pp_image_size: 'Image Size',
  pp_image_preview: 'Image Preview',
  pp_normal: 'normal',
  pp_abnormal: 'abnormal',
  pp_unknown: 'unknown',
  pp_unfinished: 'unfinished',
  pp_status_select: 'Status',
  pp_stuck: 'Stuck Car',
  pp_flash_result: 'Flash Result',
  pp_flash_success: 'Flash Success',
  pp_flash_fail: 'Flash Fail',
  pp_flash_overdue: 'Command Expires',
  pp_battery_capacity: 'Battery Capacity',
  pp_trigger_mode: 'Trigger Mode',
  pp_ui: 'UI Trigger',
  pp_select_battery: 'Select Battery',
  pp_preselect_battery: 'Preselect Battery',
  pp_cloud_push: 'Cloud Push',
  pp_aes_key: 'AES_KEY',
  pp_target_version: 'Target Version',
  pp_status: 'Status',
  pp_flash_start_time: 'Flash Start Time',
  pp_flash_end_time: 'Flash End Time',
  pp_flash_battery_id: 'Flash Battery ID',
  pp_flash_battery_capacity: 'Battery Capacity',
  pp_flash_target_version: 'Target Version',
  pp_battery_branch_ownership: 'Battery Branch',
  pp_detail: 'Detail',
  pp_authentication_start: 'Authentication start',
  pp_cloud_specified_battery_id: 'Cloud-specified Battery ID',
  pp_cloud_specified_battery_capacity: 'Cloud-specified Battery Capacity',
  pp_request_id: 'Request ID',
};

const deviceManagement = {
  pp_province: 'Province',
  pp_city: 'City',
  pp_device: 'Device',
  pp_device_name: 'Device Name',
  pp_device_id: 'Device ID',
  pp_name: 'Name',

  pp_select_province: 'Select Province',
  pp_select_city: 'Select City',
  pp_select_device_id: 'Enter Device Name or ID',
  pp_device_select: 'Device',
};

const serviceDetail = {
  pp_service_duration: 'Service Duration',
  pp_service_time_frame: 'Service Time',
  pp_service_battery_id: 'Service Battery ID',
  pp_car_identifier: 'Car Identifier',
  pp_service_id: 'Service ID',
  pp_car_battery_id: 'Car Battery ID',
  pp_high_speed_recording: 'High Speed Recording',
  pp_sensor: 'Sensor',
  pp_converter: 'Converter',
  pp_operation_log: 'Operation Log',
  pp_service_status: 'Service Status',
  pp_alarm_list: 'Alarm List',
  pp_state_machine: 'State Machine',
  pp_standby_mode: 'Standby Mode',
  pp_platform_side: 'Platform Side',
  pp_battery_side: 'Battery Side',
  pp_platform_step: 'Platform Step',
  pp_serial_number: 'Serial Number',
  pp_battery_step: 'Battery Step',
  pp_axis_name: 'Axis Name',
  pp_data_point_name: 'Data Point Name',
  pp_full_csv_download: 'Full CSV Download',

  pp_speed_empty: 'Search Above To View High Speed Recording Charts',
  pp_speed_archived: 'High Speed Recording Data Archived, Please Click Restore Button to Restore',
  pp_speed_restoring: 'High Speed Recording Data Restoring, Estimated Time: 1-2 Minutes, \nPlease Refresh the Page to View the Restoring Result',
  pp_sensor_empty: 'Search Above To View Sensor Charts',
  pp_converter_empty: 'Search Above To View Converter Charts',
  pp_di: 'DI',
  pp_di_empty: 'Search Above To View DI Charts',
  pp_operation_trigger_time: 'Trigger Time',
  pp_operation_interface: 'Operation Interface',
  pp_input_parameters: 'Enter Params',
  pp_return_data: 'Return Data',
  pp_operation_description: 'Description',
  pp_operation_people: 'Operator',
  pp_state_automatic: 'Automatic',
  pp_state_manual: 'Manual',
  pp_state_fault: 'Fault',
  pp_state_shutdown: 'Shutdown',
  pp_state_non_automatic: 'Non-automatic',
  pp_limit_warn: 'The time range cannot exceed one day',
  pp_log_local: 'Local',
  pp_log_remote: 'Remote',
  pp_log_module: 'Module',
  pp_log_time: 'Command Time',
  pp_log_name: 'Command Name',
  pp_log_status: 'Command Status',
  pp_log_reason: 'Fail Reason',
  pp_log_params: 'View Params',
  pp_log_success: 'Success',
  pp_log_fail: 'Fail',
  pp_params_set: 'Command Params',
  pp_params_code: 'Params Code',
  pp_params_value: 'Params Value',
  pp_unknown: 'Unknown',
  pp_attended: 'Attended',
  pp_unattended: 'Unattended',
  pp_abnormal_tip: 'Data anomaly, includes values outside of the mapping table',
};

const userManagement = {
  pp_login_authority: 'Login Authority',
  pp_user_name: 'Username',
  pp_domain_account: 'Domain',
  pp_role: 'Role',
  pp_enter_user_name: 'Enter Username',
  pp_not_space: 'Do not enter spaces',
  pp_enter_domain_account: 'Enter Domain',
  pp_select_role: 'Select Role',
  pp_create_time: 'Create Time',

  pp_batch_add: 'Batch Add',
  pp_add_user: 'Add User',

  pp_download_batch_template: 'Download Batch Templates',
  pp_batch_add_user: 'Batch Add Users',
  pp_upload_normal_text: 'Drop file here or ',
  pp_upload_light_text: 'click to upload',
  pp_upload_normal_tip1: 'Please ',
  pp_upload_normal_tip2: ', Upload After Filling In User Information',
  pp_edit_user: 'Edit User',
  pp_delete_user: 'Delete User',
  pp_delete_info: 'Are you sure to delete',

  pp_specialist: 'Specialist',
  pp_maintenance: 'Maintenance',
  pp_factory: 'Factory',
  pp_development: 'Development',
  pp_brown_dragon: 'Brown Dragon',
  pp_brown_dragon_temporary: 'Brown Dragon(temporary)',
};

const logExport = {
  pp_view_wf: 'My Approval',
  pp_log_access: 'Log Access',
  pp_select_power_swap: 'Select Device',

  pp_file_path: 'File Path',
  pp_status: 'Status',
  pp_request_time: 'Request Time',

  pp_log_catalog: 'Log Catalog',
  pp_select_log_catalog: 'Select Log Catalog',
  pp_update_time: 'Last Updated',
  pp_updating: 'Loading',
  pp_update_catalog: 'Update',
  pp_update_timeout: 'Timeout',
  pp_done: 'Done',
  pp_transmitting: 'Loading',
  pp_no_generated: 'Not generated',
  pp_failed: 'Failed',
  pp_return: 'Return',
  pp_transfer_log_files: 'Transfer Log Files',
  pp_confirm_one: 'Will the log files',
  pp_confirm_two: 'be transferred from the device to the Welkin platform?',
  pp_confirm_three: 'The transfer status can be checked on the log list page, and the log file can be downloaded after the transfer is completed.',
  pp_confirm_four: 'The logs are saved for ',
  pp_confirm_five: '7 days',
  pp_confirm_six: ', please download them in time.',

  pp_confirm: 'Confirm',
  pp_confirm_button: 'Confirm',
  pp_confirming: 'Loading',
  pp_tree_loading: 'Loading...',
  pp_tooltip: 'Please confirm whether the device is online, if the device is offline, the file directory cannot be obtained.',
  pp_seaching: 'Loading...',
  pp_batch_download: 'Batch Download',
  pp_batch_download_tooltip: 'Please select a device first',
  pp_approval: 'Send Approval',
  pp_reason: 'Reason',
  pp_enter_reason: 'Enter Application Reason',

  pp_approve_success: 'Approval successfully created',
  pp_approve_fail: 'Failed to create approval',
  pp_file_name: 'File Name',
  pp_md5_check: 'Md5 Check Pass',
};

const historyData = {
  pp_history_data: 'History Data',
  pp_parameters: 'Parameters',
  pp_parameters_select: 'Please Select Parameters',
  pp_over_parameters: 'If there are more than 5 parameter points, the data is displayed in a table format',
  pp_over_time: 'If the time range exceeds 24 hours, the data is displayed in a table format',
  pp_over_hundred_parameters: 'Parameter Points Should Not Exceed 100',
  pp_selected_parameters: 'Selected Parameters:',
  pp_tip_text: 'Non-numeric data points cannot be displayed',
};

const edgeCloud = {
  pp_cloud: 'Edge Cloud',
  pp_cluster: 'Cluster',
  pp_cluster_list: 'Cluster List',
  pp_app_list: 'APP List',
  pp_app_detail: 'APP Detail',
  pp_cluster_detail: 'Cluster Detail',
  pp_edge_resource: 'Edge Resource',
  pp_select_cluster: 'Select Cluster',
  pp_select_id: 'Select Cluster ID',
  pp_select_app: 'Select APP',
  pp_select_creator: 'Select Creator',
  pp_select_project: 'Select Project',
  pp_select_type: 'Select Type',
  pp_select_status: 'Select Status',
  pp_select_time: 'Select Update Time',
  pp_select_instance: 'Select Instance',
  pp_select_subSystem: 'Select SubSystem',
  pp_select_image: 'Select Image',
  pp_input_number: 'Enter Number',
  pp_input_name: 'Enter Name',
  pp_input_key: 'Enter Key',
  pp_input_value: 'Enter Value',
  pp_add_container: 'Please add at least one container',
  pp_cluster_id: 'Cluster ID',
  pp_node_id: 'Node ID',
  pp_instance_id: 'Instance ID',
  pp_cluster_name: 'Cluster Name',
  pp_node_number: 'Number of Nodes in the Cluster',
  pp_stop_schedule: 'Stop Scheduling',
  pp_node_name: 'Node Name',
  pp_sub_system: 'Subsystem',
  pp_status: 'Status',
  pp_cpu_usage: 'CPU Usage',
  pp_memory_usage: 'Memory Usage',
  pp_allocated_cpu: 'Allocated CPU',
  pp_cpu_reservation: 'CPU Reserve',
  pp_memory_reservation: 'Memory Reserve',
  pp_allocated_memory: 'Allocated Memory',
  pp_app_name: 'APP',
  pp_creator: 'Creator',
  pp_create_time: 'Create Time',
  pp_belonging_project: 'Project',
  pp_project: 'Project',
  pp_running_status: 'Status',
  pp_image: 'Image',
  pp_port_info: 'Port',
  pp_port: 'Port',
  pp_port_name: 'Port Name',
  pp_port_protocol: 'Port Protocol',
  pp_instance: 'Instance',
  pp_update_time: 'Update Time',
  pp_create_app: 'Create APP',
  pp_edit_app: 'Edit APP',
  pp_info: 'Basic Info',
  pp_name: 'Name',
  pp_type: 'Type',
  pp_name_limit: 'The name can only contain letters, numbers, and hyphen (-), must start with a letter, and be up to 30 characters long',
  pp_container_set: 'Container',
  pp_container_name: 'Container Name',
  pp_container_image: 'Container Image',
  pp_container_number: 'Container Number',
  pp_click_add: 'Click to Add Container',
  pp_number: 'Number',
  pp_box_name: 'Name',
  pp_memory_limit: 'Memory Limit',
  pp_cpu_limit: 'CPU Limit',
  pp_add_port: 'Add Port',
  pp_upload: 'Click or Drag to Add Configuration File',
  pp_tag: 'Node',
  pp_key: 'Key',
  pp_value: 'Value',
  pp_delete_tag: 'Delete Tag',
  pp_specify_tag: 'Specify Node',
  pp_memory: 'Memory',
  pp_add_tag: 'Add Node Selector',
  pp_delete_container: 'Are you Sure to delete container',
  pp_delete_app: 'Are you sure to delete APP',
  pp_stop_success: 'Stop scheduling successfully',
  pp_stop_error: 'Failed to stop scheduling',
  pp_instance_detail: 'Instance Details',
  pp_resource_usage: 'Resource Usage',
  pp_resource_allocation: 'Resource Allocated',
  pp_instance_status: 'Instance Status',
  pp_details: 'Detail',
  pp_event: 'Event',
  pp_log: 'Log',
  pp_restart: 'Restart',
  pp_sure_restart: 'Are you Sure to restart instance',
  pp_restart_success: 'Restart Successfully',
  pp_files: 'View Files',
  pp_configuration_file: 'Configuration File',
  pp_fill_port: 'Please fill in the complete port protocol and port number',
  pp_fill_key: 'Please fill in the complete key-value pair',
};

const aiPortrait = {
  pp_yesterday_rate: 'Yesterday Algorithm Pass Rate (%)',
  pp_click_column: 'Please click on the column to view the details',
  pp_ftt: 'FTT',
  pp_non_ftt: 'Non-FTT',
  pp_update_time: 'Updated Time',
  pp_backhaul: 'Data Backhaul Volume',
  pp_upload_sites: 'Image Upload Volume',
  pp_aec_effective: 'AEC Call Rate',
  pp_ai_rate: 'AI Running Rate',
  pp_aec_return: 'AEC orders with results returned (including abnormal operation)',
  pp_total_order: 'Yesterday total orders',
  pp_ai_return: 'AEC orders with valid algorithm output results',
  pp_vehicle_model: 'Vehicle Model',
  pp_battery_type: 'Battery Type',
  pp_all_day: 'All Day',
  pp_day: 'Day',
  pp_night: 'Night',
  pp_pass_rate: 'Algorithm Pass Rate',
  pp_total: 'total',
  pp_trend: 'Pass Rate Trend',
  pp_sites: 'Station',
  pp_last_week: 'Last Week',
  pp_last_month: 'Last Month',
  pp_pass: 'Pass Rate',
  pp_top_10: 'Lowest Pass Rate Top 10 Stations',
  pp_block_sites: 'Blocked Stations',
  pp_ftt_trend: 'Algorithm FTT Trend (%)',
  pp_target: 'Target Value',
  pp_theoretical: 'Theoretical Value',
  pp_error: 'Request Error, Please Contact liu.yang4',
  pp_cpu: 'CPU Utilization Rate (%)',
  pp_risk: 'Risk Threshold',
  pp_last_ftt: 'Yesterday Top 10 Worst FTT Stations',
  pp_order: 'order',
  pp_open_sites: 'Open Site (Stations:',
  pp_full_calculation: 'Full Calculation (Stations:',
  pp_parameter_recipe: 'Parameter Recipe',
};

const orderList = {
  pp_order_name: 'Order Name',
  pp_order_pushtime: 'Push Time',
  pp_order_status: 'Order Status',
  pp_order_list: 'Order List',
  pp_power_grid: 'Power Grid Trading',
  pp_battery_reservation: 'Battery Reservation',
  pp_battery_res_time: 'Reservation Time',
  pp_battery_res_num: ' Number',
  pp_order_push: 'Push Order',
  pp_order_create_time: 'Create Time',
  pp_order_finish_time: 'Finish Time',
  pp_order_user: 'User',
  pp_enter_order_name: 'Enter',
  pp_select_order_status: 'Select',
  pp_order_device: 'Device',
  pp_battery_type: 'Battery Type',
  pp_ms_required: 'Please enter order name',
  pp_device_required: 'Please select device',
  pp_reserver_t_required: 'Please select reservation time',
  pp_max_capacity: 'Max Capacity',
  pp_price: 'Price',
  pp_battery_required: 'Please select battery type',
  pp_max_capacity_required: 'Please enter max capacity',
  pp_price_required: 'Please enter price',
  pp_stat_1: 'Created',
  pp_stat_2: 'Pushed Failed',
  pp_stat_3: 'Pushing',
  pp_stat_4: 'Pushed Successfully',
  pp_stat_5: 'Canceled Failed',
  pp_stat_6: 'Canceling',
  pp_stat_7: 'Canceled Successfully',
  pp_res_t_start: 'Reservation Start Time',
  pp_res_t_end: 'Reservation End Time',

  pp_push_confirm: 'Are you sure to push this order: ',
  pp_revoke_confirm: 'Are you sure to revoke this order: ',

  pp_err_nullnum: 'Please enter a number',

  pp_bid_order: 'Bid Order',
  pp_reserve_order: 'Reserve Order',
  pp_order_type: 'Order Type',
  pp_piece: 'piece',
  pp_devices: 'Devices',
  pp_create_success: 'Create Successfully',
  pp_create_failed: 'Create Failed',
  pp_edit_success: 'Edit Successfully',
  pp_edit_failed: 'Edit Failed',
  pp_push_success: 'Pushing',
  pp_push_failed: 'Push Failed',
  pp_revoke_success: 'Revoking',
  pp_revoke_failed: 'Revoke Failed',
  pp_revoke_start: 'Revoke Start Time',
  pp_revoke_end: 'Revoke Finish Time',
  pp_fail_reason: 'Fail Reason',
  pp_read_only: 'Non-command creators only have read-only permissions',
};

const camera = {
  pp_camera: 'Camera',
  pp_sites: 'Station',
  pp_status: 'Manual Result',
  pp_index: 'Index',
  pp_station_name: 'Station Name',
  pp_station_id: 'Station ID',
  pp_shooting_time: 'Shooting Time',
  pp_unverified: 'unverified',
  pp_qualified: 'qualified',
  pp_unqualified: 'unqualified',
  pp_judgment_time: 'Judgment Time',
  pp_judgement_result: 'Judgment Result',
  pp_judge: 'Judge',
  pp_operator: 'Operator',
  pp_export: 'Export',
  pp_image_preview: 'Image Preview',
  pp_imaging_qualified: 'correct',
  pp_imaging_distortion: 'skew',
  pp_with_text: 'Invalid text',
  pp_imaging_blur: 'blurring',
  pp_incorrect_resolution: 'Incorrect resolution ratio',
  pp_camera_form: 'Camera Acceptance Form',
  pp_algorithm_results: 'Algorithm Result',
  pp_empty: 'Empty',
  pp_pass: 'Pass',
  pp_fail: 'Fail',
  pp_exception: 'Exception',
  pp_algorithmic_result: 'Algorithm Result',
  pp_algorithmic_status: 'Algorithm Status',
  pp_invalid: 'Invalid',
  pp_normal: 'Normal',
  pp_restore: 'Batch Restore Algorithm State',
  pp_confirm: 'Has the algorithm been updated',
  pp_restore_success: 'Restored Successfully',
  pp_pass_rate: 'Pass Rate',
  pp_pending_acceptance: 'Pending Accept',
  pp_dialog_title: 'Notify The Person In Charge Via Lark',
  pp_error_text: 'Existing camera not verified',
  pp_initiator_of_acceptance: 'Initiator of Acceptance',
  pp_third_party_personnel: 'Third-party Personnel',
  pp_judgment_result: 'Judgment Result',
  pp_acceptance_passed: 'Acceptance Passed',
  pp_no_image: 'No Image',
  pp_remark: 'Remark',
  pp_send_success: 'Send Success',
};

const chargeModule = {
  pp_power_swap: 'swap',
  pp_charge: 'charge',
  pp_predict: 'predict',
  pp_staggered: 'stagger',
  pp_order: 'order',
  pp_nio_pss: 'NIO PSS',
  pp_charge_module: 'Charging Module',
  pp_total_power: 'power',
  pp_remaining: 'surplus',
  pp_total_module: 'module (piece)',
  pp_free: 'free',
  pp_total_battery: 'battery (piece)',
  pp_full: 'full',
  pp_a_full: 'A full',
  pp_c_full: 'C full',
  pp_index: 'index',
  pp_type: 'type',
  pp_order_time: 'order time',
  pp_position: 'position',
  pp_module: 'module',
  pp_select_device: 'please select device',
  pp_battery_empty: 'Empty',
  pp_pile: 'pile',
  pp_pile_empty: 'Empty',
  pp_car: 'Car',
  pp_bms_request: 'BMS request',
  pp_output: 'output',
  pp_module_empty: 'Empty',
  pp_tooltip: 'The most recent data will be traced back based on the selected time, limited to 1 minute. Data points that have not been uploaded within 1 minute will be displayed as empty',
};

const alarmList = {
  pp_alarm_list: 'Alarm List',
  pp_basic_alarm: 'Basic Alarm',
  pp_battery_alarm: 'Battery Alarm',
  pp_unknown_alarm: 'Unknown Alarm',
  pp_first_level: 'First Level',
  pp_second_level: 'Second Level',
  pp_third_level: 'Third Level',
  pp_alarming: 'Created',
  pp_cleared: 'Cleared',
  pp_unknown: 'Unknown',
  pp_alarm_time: 'Alarm Time',
  pp_alarm_type: 'Alarm Type',
  pp_alarm_level: 'Alarm Level',
  pp_alarm_status: 'Alarm Status',
  pp_alarm_description: 'Alarm Point',
  pp_alarm_id: 'Alarm ID',
  pp_device: 'Device',
  pp_battery_id: 'Battery ID',
  pp_expand: 'Expand',
  pp_collapse: 'Collapse',
  pp_create_time: 'Create Time',
  pp_clear_time: 'Clear Time',
  pp_device_name: 'Device Name',
  pp_device_id: 'Device ID',
  pp_keywords: 'Enter ID or Description',
  pp_fault_id: 'Fault ID',
  pp_native_code: 'Native Code',
  pp_real_code: 'Real Code',
  pp_fault_name: 'Fault Name',
  pp_status: 'Status',
  pp_type: 'Type',
  pp_alarm_analysis: 'Analysis',
  pp_alarm_download: 'Download',
  pp_dialog_title: 'Select Alarm Analysis Conditions',
  pp_device_method: 'Select Device Mode',
  pp_direct_selection: 'Direct Selection',
  pp_csv_upload: 'CSV Import',
  pp_device_select: 'Devices',
  pp_empty_text: 'No corresponding content was found, please select the analysis conditions',
  pp_condition_selection: 'Condition Selection',
  pp_analysis_method: 'Select Analysis Method',
  pp_based_on_time: 'Based on Time',
  pp_based_on_version: 'Based on Version',
  pp_time: 'Time:',
  pp_time_tip: 'The analysis results will display the ratio of the number of alarms occurring between two intervals, which is Interval Two divided by Interval One',
  pp_interval_one: 'Interval One',
  pp_interval_two: 'Interval Two',
  pp_create_analysis: 'Create',
  pp_alarm_id_or_name: 'Alarm ID/Name',
  pp_interval_1_count: 'Interval1 Count',
  pp_interval_2_count: 'Interval2 Count',
  pp_interval_divide: 'Interval2 / Interval1',
  pp_device_selected: 'Devices',
  pp_time_frame: 'Time',
  pp_threshold: 'Occurrence Threshold',
  pp_threshold_tip: 'The occurrence times in both Interval One and Interval Two must not be less than this threshold',
  pp_in_service: 'In Service',
  pp_form_tip: 'Only alarms that occur during the battery swap process will display the corresponding vehicle brand/vehicle platform',
};

const batteryHistory = {
  input_battery_id: 'please enter battery id',
  service_id: 'Service ID',
  unnamed_station: 'unnamed station',
  preview_image: 'preview image',
  download_image: 'download image',
  full_download: 'Full Download',
};

const serviceTrace = {
  pp_alarm_info: 'Alarm List',
  pp_lack_alarm: 'No alarm',
  pp_car_vin: 'Car ID',
  pp_validate: 'At least one of the battery ID and car ID must be filled in!',
  pp_abnormal_images: 'Abnormal Images',
  service_end_normally: 'Finish Normally',
  service_ended_normally: 'service finished normally',
  service_ended_abnormally: 'service finished abnormally',
  no_service_info: 'service finished status unknown',
  service_battery_id: 'Service Battery ID (switched to the car)',
  car_battery_id: 'Car Battery ID (switched to the station)',
  swap_station_type: 'Station Type',
};

const gridInfo = {
  pp_device_list: 'Device List',
  pp_setting: 'Config',
  pp_battery_slot: 'Battery Slot Infomation',
  pp_reserve_75: 'ReservedB75',
  pp_reserve_100: 'ReservedB100',
  pp_mode: 'OpsMode',
  pp_control_enable: 'CtrlState',
  pp_protocol_version: 'Protocol',
  pp_power_limit: 'CapLimit',
  pp_charge_power_max: 'LimitDown',
  pp_charge_power_min: 'LimitUp',
  pp_charge_energy_max: 'MaxChargeEng',
  pp_discharge_energy_max: 'MaxDischargeEng',
  pp_station_use_power: 'Power',
  pp_base_line_power: 'Baseline',
  pp_remote_distribute_power: 'SetPoint',
  pp_min_power_coe: 'RedFactor',
  pp_bid_capactiy: 'Bid Capactiy',
  pp_bid_price: 'Bid Price',
  pp_gird_frequency: 'Gird Frequency',
  pp_EPrice: 'EPrice',
  pp_min_price: 'MinDownPrice',
  pp_max_price: 'MaxUpPrice',
  pp_reserve: 'Reserve',
  pp_request: 'Request',
  pp_distribute: 'Distribute',
  pp_actual: 'Actual',
  pp_limit: 'Limit',
  pp_selected_device: 'Selected Device',
  pp_message: 'Please select at least one device',
  pp_last_hour: 'Last Hour',
  pp_last_2_hours: 'Last 2 Hours',
  pp_last_6_hours: 'Last 6 Hours',
  pp_last_24_hours: 'Last 24 Hours',
  pp_off_peak: 'Off-Peak Charging',
  pp_a_frr: 'a-FRR',
  pp_peak_shaving: 'Peak Shaving',
  pp_normal_mode: 'Normal Mode',
  pp_fcr_d: 'FCR-D',
  pp_vdb_mode: 'VDB Mode',
  pp_standard_version: 'Standard',
  pp_huaneng_version: 'PowerGrid',
  pp_battery_0: '50 50Ah',
  pp_battery_1: '70 102Ah',
  pp_battery_2: '84',
  pp_battery_3: '70 102Ah X',
  pp_battery_4: '84 Lock',
  pp_battery_5: '70 LFP',
  pp_battery_6: '100 NCM',
  pp_battery_7: '100 Lock70',
  pp_battery_8: '75',
  pp_battery_9: '100 Lock84',
  pp_battery_10: '150',
  pp_battery_11: '100 Lock75',
  pp_battery_13: '100B',
  pp_battery_14: '100B Lock75',
  pp_d_1_price: 'd-1-price',
  pp_d_2_price: 'd-2-price',
  pp_ideal_bid: 'Ideal Bid Capacity',
  pp_actual_bid: 'Actual Bid Capacity',
  pp_ideal_revenue: 'Ideal Revenue',
  pp_actual_revenue: 'Actual Revenue',
  pp_bid: 'Bid Capacity',
  pp_revenue: 'Revenue',
};

const batterySwap = {
  pp_slot: 'Slot',
  pp_reason: 'Reason',
  pp_visualization: 'Visualization',
  pp_swap_time: 'Swap Time',
  pp_unknown: 'Unknown',
  pp_battery_id: 'Battery ID',
  pp_device_id: 'Device ID',
  pp_swap_method: 'Method',
  pp_related_id: 'Related ID',
  pp_service_id: 'Service ID',
  pp_turnover_id: 'TurnOver ID',
  pp_car_id: 'Car ID',
  pp_manual: 'Manual',
  pp_authentication_swap: 'Authentication swap',
  pp_non_authenticated: 'Non-authenticated swap',
  pp_slot_turnover: 'Slot turnover during swap',
  pp_slot_outside: 'Slot turnover outside of swap',
  pp_transfer: 'Firefighting transfer',
  pp_fall_water: 'Firefighting water drop',
  pp_enter_slot: 'Enter Slot',
  pp_exit_slot: 'Exit Slot',
  pp_firefighting_slot: 'Firefighting slot',
  pp_tip: 'Currently, only pss 2.0 and pss 3.0 are supported.',
  pp_swap_station2: 'PSS 2.0',
  pp_swap_station3: 'PSS 3.0',
};

const licenseMatch = {
  pp_ds_plateno: 'DS Plateno',
  pp_lpr_plateno: 'LPR Plateno',
  pp_car_id: 'Car ID',
  pp_vin: 'VIN',
  pp_equity_status: 'Equity Status',
  pp_equity_name: 'Equity Name',
  pp_purchase_method: 'Purchase Method',
  pp_registration_time: 'Registration Time',
  pp_service_id: 'Service ID',
  pp_device_id: 'Device ID',
  pp_device_name: 'Device Name',
  pp_start_time: 'Service Start Time',
  pp_end_time: 'Service End Time',
  pp_view_image: 'View Image',
  pp_full_download: 'Full Download',
  pp_unavailable: 'unavailable',
  pp_available: 'available',
  pp_used: 'used',
  pp_expired: 'expired',
  pp_transferred: 'transferred',
  pp_original_owner: 'original owner',
  pp_benefits_owner: 'certified used car',
  pp_no_benefits_owner: 'non-certified used car',
};

const logAnalysis = {
  pp_create_title: 'Send log analysis event',
  pp_event_id: 'Event ID',
  pp_no_log: 'No Log',
};

const processError = {
  pp_station_2: 'PSS 2.0',
  pp_station_3: 'PSS 3.0',
  pp_station_4: 'PSS 4.0',
  pp_process_id: 'Process ID',
  pp_process_name: 'Process Name',
  pp_error_reason: 'Error Reason',
  pp_error_level: 'Error Level',
  pp_collapse: 'Collapse',
  pp_cpu_high: 'High CPU usage',
  pp_memory_high: 'High Memory usage',
  pp_level_one: 'Level 1',
  pp_level_two: 'Level 2',
  pp_level_three: 'Level 3',
  pp_create_ts: 'Occurrence Time',
  pp_module: 'Module',
};

const fireAlarm = {
  pp_create_ts: 'Occurrence Time',
  pp_upload_progress: 'Progress',
  pp_update_time: 'Update Time',
  pp_finish: 'Completed',
  pp_view_file: 'View Files',
  pp_view_report: 'View Report',
  pp_document_list: 'Fire Alarm File List',
  pp_alarm_time: 'Alarm Time',
  pp_order: 'Number',
  pp_document_name: 'File Name',
  pp_upload_time: 'Upload Time',
  pp_device_name: 'Device Name',
  pp_not_uploaded: 'Not Uploaded',
  // Detail page additions
  pp_basic_info: 'Basic Info',
  pp_alarm_list: 'Fire Related Alarms',
  pp_no_fire_alarm: 'Non-fire Related Alarms',
  pp_transfer_process: 'Transfer Process',
  pp_battery_cabin_info: 'Battery Cabin Info',
  pp_alarm_type: 'Alarm Type',
  pp_alarm_level: 'Alarm Level',
  pp_alarm_desc: 'Alarm Description',
  pp_step_1: 'System Start Transfer',
  pp_step_2: 'Plug Pull Out Failed',
  pp_step_3: 'Battery Out to Stacker Complete',
  pp_step_4: 'Battery Transfer to Fire Cabin High Dock Complete',
  pp_step_5: 'Start Water Drop',
  pp_step_6: 'Water Drop Complete',
  pp_battery_status_voltage: 'Battery Charge Status / Voltage',
  pp_battery_temp_insulation: 'Cell Temp / Insulation',
};

const diagnosisInfo = {
  pp_keywords: 'Enter ID',
  pp_time_frame: 'Time Frame',
  pp_diagnosis_level: 'Diagnosis Level',
  pp_diagnosis_type: 'Diagnosis Type',
  pp_timeout_command: 'TimeoutCommand',
  pp_scheduled_time_not_start: 'ScheduledTimeNotStart',
  pp_fail_uploading_service: 'FailUploadingService',
  pp_start_without_s2_close: 'StartWithoutS2Close',
  pp_cp_status: 'CPStatus',
  pp_text: 'TextProperties',
  pp_upload_time: 'Upload Time',
  pp_device_id: 'Device ID',
  pp_upload_mode: 'Upload Mode',
  pp_detail: 'View Detail',
  pp_periodic_upload: 'Periodic Upload',
  pp_variable_upload: 'Variable Upload',
  pp_diagnosis_detail: 'Diagnosis Detail',
  pp_control_command: 'Control Command',
  pp_command_time: 'Command Time',
  pp_restart_count: 'Restart Count',
  pp_the_last_restart_time: 'Final Restart Time',
  pp_associated_order: 'Associated Order',
  pp_diagnosis_result: 'Diagnosis Result',
  pp_reservation_mode: 'Reservation Mode',
  pp_authentication_success_time: 'Auth Success Time',
  pp_authentication_type: 'Auth Type',
  pp_successfully_authenticated: 'Is Auth Successful',
  pp_start_service_time: 'Start Service',
  pp_service_end_time: 'End Service',
  pp_output_PWM_signals: 'Output PWM Signal',
  pp_PWM_output_time: 'PWM Output Time',
  pp_vehicle_side_S2_closed: 'Vehicle Side S2 Closed',
  pp_S2_close_time: 'S2 Close Time',
  pp_current_status_or_sample_value: 'CP Current Status Or Sample',
  pp_auto_auth: 'Auto Auth',
  pp_manual_auth: 'Manual Auth',
  pp_none_auth: 'None Auth',
};

const parkingOccupancy = {
  pp_select_device: 'Select Device',
  pp_parking_alarm_time: 'Alarm Time',
  pp_device_name: 'Device Name',
  pp_device_id: 'Device ID',
  pp_city_company: 'Regional Company',
  pp_view_image: 'View Image',
  pp_alarm_image_review: 'Alarm Image Review',
  pp_alarm_list: 'Alarm List',
  pp_fire_detail: 'Alarm Detail',
  pp_occupancy_status_dashboard: 'Occupancy Status Dashboard',
  pp_occupancy_status: 'Occupancy Status',
  pp_order_concentration: 'Busy Order Proportion',
  pp_tip: 'The data from the previous day is updated daily at 12 p.m.',
  pp_total_order_num: 'Total Order',
  pp_cancel_order_num: 'Cancel Order',
  pp_finish_order_num: 'Finish Order',
  pp_interrupt_num: 'Parking Interruption',
  pp_voc_num: 'The current station affects the number of user parking VOCs.',
};

const stuckAnalysis = {
  pp_dashboard_overview: 'Dashboard Overview',
  pp_order_dimension: 'Order Dimension',
  pp_alarm_detail: 'Alarm Detail',
  pp_alarm_list: 'Alarm List',
  pp_powerswap_step: 'PowerSwap Step',
  pp_swap_station2: 'PSS 2.0',
  pp_swap_station3: 'PSS 3.0',
  pp_swap_station4: 'PSS 4.0',
  pp_time: 'Time',
  pp_average_stuck_rate: 'Average Stuck Rate',
  pp_stuck_order_quantity: 'Stuck Order Quantity',
  pp_total_order_quantity: 'Total Order Quantity',
  pp_selected_days: 'Selected Days',
  pp_orders: 'orders',
  pp_days: 'days',
  pp_line_empty: 'No Line Chart Data',
  pp_stuck_alarm_ranking: 'Stuck Alarm Ranking',
  pp_alarm_name: 'Alarm Name',
  pp_alarm_id: 'Alarm ID',
  pp_alarm_type: 'Alarm Type',
  pp_alarm_level: 'Alarm Level',
  pp_alarm_status: 'Alarm Status',
  pp_create_time: 'Alarm Create Time',
  pp_clear_time: 'Alarm Clear Time',
  pp_stuck_times: 'Stuck Times',
  pp_total_times: 'Total Times',
  pp_alarm_rate: 'Alarm Rate',
  pp_device_stuck_ranking: 'Device Stuck Ranking',
  pp_more: 'More',
  pp_stuck_orders: 'Stuck Orders',
  pp_total_orders: 'Total Orders',
  pp_device_id: 'Device ID',
  pp_device_name: 'Device',
  pp_device_type: 'Device Type',
  pp_stuck_rate: 'Stuck Rate',
  pp_stuck_order: 'Stuck Orders',
  pp_om_owner: 'O&M owner',
  pp_car_id: 'Car ID',
  pp_duty_status: 'Duty Status',
  pp_analysis_status: 'Analysis Status',
  pp_analyst: 'Analyst',
  pp_unknown: 'unknown',
  pp_somebody: 'somebody',
  pp_nobody: 'nobody',
  pp_unanalyzed: 'unanalyzed',
  pp_analyzed: 'analyzed',
  pp_edit_analyst: 'Edit Analyst',
  pp_change_analyst: 'Change Analyst',
  pp_detail: 'Detail',
  pp_analysis: 'Analysis',
  pp_order_detail: 'Order Detail',
  pp_order_analysis: 'Order Analysis',
  pp_hardware_design: 'Relevant Hardware Function Design',
  pp_software_design: 'Relevant Software Function Design',
  pp_station_name: 'Device Name',
  pp_station_id: 'Device ID',
  pp_software_version: 'Software Version',
  pp_car_departure_time: 'Car Departure Time',
  pp_car_brand: 'Car Brand',
  pp_car_model: 'Car Model',
  pp_car_platform: 'Car Platform',
  pp_duty_person: 'Duty Person',
  pp_device_manager: 'Device Manager',
  pp_alarm_location: 'Alarm Location',
  pp_alarm_step: 'Alarm Step',
  pp_current_alarm: 'Current Alarm',
  pp_is_stuck: 'Is Stuck',
  pp_stuck_alarm: 'Stuck Alarm',
  pp_non_stuck_alarm: 'None-Stuck Alarm',
  pp_alarm_clear: 'Cleared',
  pp_is_aware: 'Aware',
  pp_associated_bug: 'Associated Bug',
  pp_associated_story: 'Associated Story',
  pp_input_result: 'Enter the analysis results',
  pp_select_aware: 'Select whether it is aware',
  pp_close_tip: 'Exiting will not save any content entered. Do you wish to exit?',
  pp_prompt: 'Prompt',
  pp_device_search: 'Device',
  pp_alarm_search: 'Alarm',
  pp_order_search: 'Service',
  pp_alarm_tip: 'Enter Alarm ID or Name',
  pp_service_id: 'Service ID',
  pp_service_start_time: 'Service Start Time',
  pp_service_end_time: 'Service End Time',
  pp_alarm_stuck: 'Stuck Rate',
  pp_software_owner: 'software owner',
  pp_hardware_owner: 'hardware owner',
  pp_single_alarm_distribution: 'Single Alarm Distribution',
  pp_alarm_times: 'Alarm Times',
  pp_related_fault_orders: 'Fault Orders',
  pp_alarm_empty: 'No Alarm Distribution Data Available',
  pp_alarm_orders: 'Alarm Orders',
  pp_proportion: 'Proportion',
  pp_alarm_occurrence_proportion: 'Alarm Occurrence Proportion',
  pp_stuck_proportion: 'Stuck Proportion',
  pp_jira_placeholder: 'Enter Jira Name or ID',
  pp_user_placeholder: 'Enter the domain account or Chinese name',
  pp_import_file: 'Import File',
  pp_download_template: 'Download Template',
  pp_please_upload: 'Please Upload',
  pp_csv: 'CSV File',
  pp_exceed_100: 'The number of valid devices cannot exceed 100',
  pp_number_0: 'The number of valid devices is 0',
  pp_import_success: 'Import',
  pp_device_number: 'Devices',
};

const deviceSimulation = {
  pp_tasks_name: 'Task Name',
  pp_running_status: 'Running Status',
  pp_creator: 'Creator',
  pp_tasks_id: 'Task ID',
  pp_search: 'Search',
  pp_clear: 'Clear',
  pp_create_simulation_task: 'New Task',
  pp_remarks: 'Remarks',
  pp_device_type: 'Type',
  pp_status: 'Status',
  pp_run_detail: 'Detail',
  pp_create_time: 'Create Time',
  pp_update_time: 'Update Time',
  pp_run: 'Running',
  pp_finished: 'Finished',
  pp_not_start: 'Unstarted',
  pp_stopped: 'Stopped',
  pp_failed: 'Failed',

  pp_enter_task: 'Please Enter',
  pp_select_account: 'Please Select',
  pp_button_stop: 'Stop',

  pp_create_method: 'Creation Method',
  pp_by_existing_formulas: 'By Existing Formulas',
  pp_by_real_data: 'By Real Data Simulation',

  pp_station_capacity: 'Station',
  pp_line1_capacity: 'Line 1',
  pp_line2_capacity: 'Line 2',
  pp_step_size: 'Step',
  pp_simulation_period: 'Period',
  pp_switch: 'EPS Switch',
  pp_select_station: 'Station Selection',
  pp_select_device: 'Device Selection',
  pp_import: 'Batch Import',
  pp_prev_step: 'Previous',
  pp_next_step: 'Next',
  pp_cancel_create: 'Cancel',
  pp_seconds: 'Seconds',
  pp_open: 'On',
  pp_close: 'Off',

  pp_basic_settings: 'Basic Settings',
  pp_real_basic_des: 'Import recipes from station data, simulate sequentially',
  pp_basic_des: 'Import recipes from configured data, simulate sequentially',
  pp_opti_config: 'Optimization Config',
  pp_opti_des: `'Find the 'best' parameter by toggling switches and iterating'`,
  pp_finish_creating: 'Finish Creation',
  pp_finish_des: 'Finish creating and start simulation',

  pp_open_config: 'Optimization',
  pp_select_config: 'Optimization Method',
  pp_battery_function: 'Battery Ratio Method',
  pp_battery: 'Battery Ratio',
  pp_custom: 'Custom Ratio',
  pp_enumeration: 'Enumeration Ratio',
  pp_blcok: 'Block',
  pp_add: 'Add',

  pp_task_name: 'Task Name',
  pp_remark: 'Rermark',
  pp_finish: 'Finish & Start Simulation',

  pp_cancel_giveup: 'Cancel Abandon',
  pp_confirm_giveup: 'Confirm Abandon',
  pp_prompt: 'Prompt',
  pp_prompt_content: 'Abandon will not save current content. Confirm?',
  pp_station_id: 'Station ID',

  pp_swap_station1: 'Station 1.0',
  pp_swap_station2: 'Station 2.0',
  pp_swap_station3: 'Station 3.0',
  pp_swap_station4: 'Station 4.0',

  pp_create: 'Created',
  pp_running: 'Running',
  pp_run_success: 'Successfully',
  pp_run_fail: 'Failed',

  pp_upload: 'Please Upload',
  pp_csv: 'CSV File',
  pp_import_file: 'Import File',
  pp_download: 'Template Download',
  pp_device: 'Valid Device',
  pp_preview_enum: 'Enumeration Result Preview',
  pp_total: 'Total',
  pp_items: 'Items',
  pp_no: 'No.',
  pp_50: '50kWh/block',
  pp_75: '70kWh/block',
  pp_100: '100kWh/block',
  pp_150: '150kWh/block',
  pp_input: 'Please Enter Simulation Task Name',
  pp_detail: 'Simulation Details',

  pp_simulation_status: 'Current Simulation Status',
  pp_charging_time: 'Charging Duration',
  pp_queue_time: 'Waiting Duration',
  pp_progress_rate: 'Overall Station Utilization Rate',
  pp_service_count: '换电订单量',
  pp_simulation_id: '配方ID',

  pp_stop_success: '停止成功',
  pp_stop_failed: '停止失败',

  pp_device_download: '设备类原始数据下载',
  pp_battery_download: '电池类原始数据下载',
  pp_order_download: '订单类原始数据下载',

  pp_date_prompt: '请选择时间',
  pp_date_rangeprompt: '请选择大于5分钟小于1天的时间',
  pp_total_prompt: '请输入整站容量',
  pp_totalcap_prompt: '请输入0-650范围内的数字',
  pp_capacity_prompt: '整站容量应大于线1容量',
  pp_step_prompt: '请输入0-120范围内的数字',
  pp_upload_device: '请选择设备',
  pp_over: '超过限制范围',
  pp_number: '请输入数字',
  pp_greater_zero: '请输入大于0的数字',
  pp_plus: '最小范围相加应小于电池仓数量',
  pp_min_max: '左侧应小于右侧',
  pp_smaller: '请输入少于电池仓数量的数字',
  pp_form_fail: '表单校验失败',
  pp_enum_detail: '平台根据各种电池类型的所选范围，生成距离参考配比最近的240个配比',
  pp_enum: '枚举方式',
  pp_enum_result: '预览枚举结果',
  pp_nan_download: '没有数据可以下载',
  pp_to: '至',
  pp_notin_list: '上传设备不在列表中',
  pp_create_success: '创建成功',
  pp_config_id: 'Recipe Set ID',
  pp_50_battery: '50kWh Battery',
  pp_60_battery: '60kWh Battery',
  pp_75_battery: '75kWh Battery',
  pp_85_battery: '85kWh Battery',
  pp_100_battery: '100kWh Battery',
  pp_150_battery: '150kWh Battery',
  pp_simulate_id: 'Simulation ID',
};

const configList = {
  pp_create: 'Create',
  pp_config_name: 'Config Name',
  pp_config_id: 'Config ID',
  pp_creator: 'Creator',
  pp_remark: 'Remark',
  pp_device_type: 'Device Type',
  pp_create_time: 'Create Time',
  pp_update_time: 'Update Time',
  pp_create_method: 'Method',
  pp_single_formula: 'Single Formula',
  pp_batch_formula: 'Batch Formula',
  pp_step1_title: 'PSS Config',
  pp_step1_subtitle: 'Configure the basic information of the battery swap station, such as the total capacity of the entire station, etc',
  pp_step2_title: 'Battery Config',
  pp_step2_subtitle: 'Configure the information of each initial battery in the battery compartment',
  pp_step3_title: 'Order Config',
  pp_step3_subtitle: 'Configure the sequence of charging and swapping users, currently only support the configuration of swapping users',
  pp_step4_title: 'Config Summary',
  pp_step4_subtitle: 'View all information of the current configuration',
  pp_base_real: 'Based on actual device simulation',
  pp_time: 'Period',
  pp_select_time: 'Please select the simulation period',
  pp_date_rangeprompt: 'Please select a time greater than 5 minutes and less than 1 day',
  pp_tip: 'Modifying the simulation duration will affect the verification of other data, please modify it with caution',
  pp_station_capacity: 'Station Capacity',
  pp_circuit1_capacity: 'Circuit1 Capacity',
  pp_circuit2_capacity: 'Circuit2 Capacity',
  pp_enter_total: 'Please enter station capacity',
  pp_enter_line1: 'Please enter circuit1 capacity',
  pp_enter_line2: 'Please enter circuit2 capacity',
  pp_switch: 'EPS Policy Switch',
  pp_open: 'Open',
  pp_close: 'Close',
  pp_price_mode: 'Tariff Mode',
  pp_select_mode: 'Please select tariff mode',
  pp_one_price: 'One Price',
  pp_time_of_use: 'Time-of-use Tariff',
  pp_yuan: 'RMB',
  pp_price_detail: 'Tariff Detail',
  pp_enter_price: 'Please enter tariff',
  pp_enter_start: 'Please select start time',
  pp_enter_end: 'Please select end time',
  pp_device: 'Device',
  pp_select_device: 'Please select device',
};

const flowMonitoring = {
  pp_time: 'Time',
  pp_used_site: 'The list of sites with exhausted flow',
  pp_upload_volume: 'Upload Volume',
  pp_usage_rate: 'Usage Rate',
  pp_duration: 'Duration',
  pp_total: 'total',
  pp_day: 'days',
};

const satisfaction = {
  pp_evaluation_time: 'Comment Time',
  pp_diagnostic_label: 'Diagnosis Label',
  pp_user_label: 'User Label',
  pp_device_select: 'Select Device',
  pp_rating_level: 'Score Level',
  pp_is_valid: 'Is Valid',
  pp_valid: 'Valid',
  pp_invalid: 'Invalid',
  pp_all: 'All',
  pp_service_duration: 'Service Duration',
  pp_queue_duration: 'Queue Duration',
  pp_score: 'Score',
  pp_vehicle_id: 'Vehicle ID',
  pp_vehicle_battery_id: 'Vehicle Battery ID',
  pp_detail: 'Detail',
  pp_diagnosis_card: 'Diagnosis Card',
  pp_detail_info: 'Detail Info',
  pp_log: 'Log',
  pp_diagnosis_result: 'Diagnosis Result',
  pp_long_battery_swap_time: 'Long Battery Swap Time',
  pp_user_start_battery_swap_time: 'User Start Battery Swap Time:',
  pp_user_end_battery_swap_time: 'User End Battery Swap Time:',
  pp_swap_fail: 'Battery Swap Trailer/Failure',
  pp_swap_fail_info1: 'Reported battery swap failure',
  pp_swap_fail_info2: 'There is a trailer warning',
  pp_view_more: 'View More',
  pp_swap_queue_time: 'Long Battery Swap Queue Time',
  pp_user_order_time: 'User Order Time:',
  pp_user_call_time: 'User Call Time:',
  pp_preorder_time: 'Long Preorder Time',
  pp_preorder_fail: 'Pre-Order Trailer Failure',
  pp_multiple_orders: 'Multiple Orders',
  pp_current: 'Current',
  pp_order_start_time: 'Order Start Time',
  pp_order_end_time: 'Order End Time',
  pp_order_duration: 'Order Duration',
  pp_order_id: 'Order ID',
  pp_service_id: 'Service ID',
  pp_comment_id: 'Comment ID',
  pp_order_times: 'The number of orders placed by this user at this station within 6 hours：',
  pp_times: 'times',
  pp_swap_normal: 'Normal Service',
  pp_swap_reversed: 'Reversed Service',
  pp_swap_automated: 'Automated Service',
  pp_hidden1: 'There are',
  pp_hidden2: 'user orders have been hidden...',
  pp_order_price: 'Order Price',
  pp_equity_type: 'Equity Type',
  pp_billing_degree: 'Billing Degree',
  pp_degree: 'Degree',
  pp_old_battery_soc: 'Old Battery SOC',
  pp_new_battery_soc: 'New Battery SOC',
  pp_electricity_unit_price: 'Electricity Unit Price',
  pp_old_battery_degree: 'Old Battery',
  pp_new_battery_degree: 'New Battery',
  pp_user_perception: 'User Perception Information',
  pp_service_information: 'Service Information',
  pp_comment_information: 'Comment Information',
  pp_rating_stars: 'Comment Stars',
  pp_comment_tag: 'Comment Tag',
  pp_comment_text: 'Comment Text',
  pp_low_score_reason: 'Reason',
  pp_solution: 'Solution',
  pp_order_time: 'Order Time',
  pp_car_owner: 'Car Owner',
  pp_other_user: 'Other User',
  pp_user: 'User',
  pp_order_list: 'Order',
  pp_dashboard: 'Dashboard',
  pp_l3_label: 'L3-Issue',
  pp_l2_label: 'L2-Product',
  pp_l1_label: 'L1-Business',
  pp_report_status: 'Report Adopted',
  pp_adopt: 'Adopt',
  pp_unadopt: 'Reject',
  pp_warn: 'Please select whether to adopt it in the report',
  pp_chart1_title: 'Low volume (L1-related business)',
  pp_chart2_title: 'Satisfaction loss (L1-related business)',
  pp_chart3_title: 'Low volume (L2-related products)',
  pp_chart4_title: 'Low average score (L2-related products)',
  pp_chart5_title: 'Satisfaction loss (L2-related products)',
  pp_label_detail: 'Customized Label',
  pp_time_error: 'The download time range cannot exceed one week',
  pp_proportion: 'Proportion',
};

const bluetooth = {
  pp_bluetooth_disconnection: 'Bluetooth Disconnection',
  pp_is_service: 'Is Service',
  pp_average_daily: 'Average Daily Occurrences Per Station',
  pp_related_alarm: 'Total Number Of Disconnection Related Alarm',
  pp_alarm_in_service: 'Alarm In Service',
  pp_total_times: 'Total Alarm',
  pp_device_rank: 'Device Disconnection Alarm Ranking',
  pp_times: 'times',
};

const swapPortrait = {
  pp_device_type: 'Device Type',
  pp_vehicle_platform: 'Vehicle Platform',
  pp_vehicle_machine: 'Vehicle Version',
  pp_software_version: 'Software Version',
  pp_order_detail: 'Order Detail',
  pp_order_start_time: 'Start Time',
  pp_order_end_time: 'End Time',
  pp_detail_info: 'Detail Info',
  pp_battery_info: 'Battery Info',
  pp_new_battery_id: 'New Battery ID',
  pp_old_battery_id: 'Old Battery ID',
  pp_experience_tag: 'Experience Tag',
  pp_swap_reversed: 'Reversed',
  pp_swap_automated: 'Automated',
  pp_on_duty: 'On Duty',
  pp_multiple_orders: 'Multiple Orders',
  pp_details: 'Details',
  pp_human_intervention: 'Human Intervention',
  pp_order_time: 'Order Time',
  pp_service_time: 'Service Time',
  pp_dialog_title: 'Multiple Orders Detail',
  pp_time_consuming: 'Time Consuming',
  pp_user: 'User',
  pp_mobile_app: 'Mobile APP',
  pp_vehicle: 'Vehicle',
  pp_automotive_cloud: 'Automotive Cloud',
  pp_power_cloud: 'Power Cloud',
  pp_pss: 'Power Station',
  pp_battery: 'Battery',
  pp_step_consuming: 'This step takes a total time of',
  pp_run: 'Running',
  pp_cancel: 'Cancel',
  pp_success: 'Success',
  pp_fail: 'Fail',
  pp_unknown: 'Unknown',
  pp_total_consuming: 'Time Consuming',
  pp_stuck_status: 'Stuck Status',
  pp_not_stuck: 'Not Stuck',
  pp_stuck: 'Stuck',
  pp_swap_step: 'Swap Steps',
  pp_second_alarm: 'Second Alarm',
  pp_third_alarm: 'Third Alarm',
  pp_duration: 'Duration',
  pp_tip: 'Jump to the service details to use high-speed recording, sensors, and inverters',
  pp_process: 'Process',
  pp_time: 'Time',
  pp_alarm: 'Alarm',
  pp_empty_text: 'No Data',
  pp_jump_warn: 'Battery swapping has not started yet, and the service ID is missing',
  pp_order: 'Order',
  pp_vdp_diagnosis_result: 'VDP Diagnosis Result',
};

const stationManagement = {
  pp_year_cumulative: 'Cumulative number of battery swaps this year',
  pp_station: 'Power Swap Station',
  pp_station_unit: 'Station',
  pp_daily_order: 'and daily average volume',
  pp_order: 'Order',
  pp_station_energy: 'Energy efficiency / Station',
  pp_station_health: 'Health / Station',
  pp_station_revenue: 'Revenue / Station',
  pp_yuan_day: 'yuan / day',
  pp_single_revenue: 'Revenue / Station',
  pp_revenue_update: 'Revenue Update',
  pp_energy_update: 'Energy Update',
  pp_energy_overview: 'Energy Overview',
  pp_energy_detail: 'Energy Detail',
  pp_energy_efficiency: 'Energy Efficiency',
  pp_energy_distribution: 'Energy Efficiency Distribution',
  pp_energy_target: 'Efficiency Target',
  pp_energy_proportion: 'Energy efficiency proportion under different volumes',
  pp_tail_device: 'Tail Station TOP',
  pp_energy_proportion1: 'Energy Efficiency Proportion',
  pp_order_volume: 'Order Volume',
  pp_total_consumption: 'Total Consumption',
  pp_charge_consumption: 'Charge Consumption',
  pp_water_consumption: 'Water Consumption',
  pp_operation_consumption: 'Operation Consumption',
  pp_mechanical_consumption: 'Mechanical Consumption',
  pp_light_consumption: 'Light Consumption',
  pp_ups_consumption: 'UPS Consumption',
  pp_revenue_overview: 'Revenue Overview',
  pp_revenue_detail: 'Revenue Detail',
  pp_annualized_revenue: 'Annualized Revenue',
  pp_expected_annualized: 'Expected',
  pp_device_daily_revenue: 'Device Daily Revenue',
  pp_progress: 'Progress',
  pp_update_date: 'Update',
  pp_ytd_revenue: 'YTD Revenue',
  pp_ytd_off_peak_revenue: 'YTD Off-Peak Revenue',
  pp_ytd_energy_revenue: 'YTD Energy Revenue',
  pp_battery_maintenance_revenue: 'YTD Battery Revenue',
  pp_revenue_of_interval: 'Revenue of the selected interval',
  pp_revenue_of_interval1: 'Revenue of the selected interval',
  pp_off_peak_revenue: 'Off-Peak Revenue',
  pp_energy_revenue: 'Energy Revenue',
  pp_battery_revenue: 'Battery Revenue',
  pp_revenue_trend: 'Revenue Trend',
  pp_revenue: 'Revenue',
  pp_off_peak: 'Off-Peak Revenue',
  pp_energy: 'Energy Revenue',
  pp_battery: 'Battery Revenue',
  pp_max_revenue_rate: 'Max Revenue Rate',
  pp_total_revenue: 'Total Revenue',
  pp_battery_maintenance_times: 'Battery Maintenance Number',
  pp_today: 'today',
  pp_non_charge_consumption: 'Non-Charge Consumption',
  pp_station_suggestion: 'Station Suggestion',
  pp_energy_trend: 'Energy Efficiency Trend',
  pp_no_data_from_yesterday: 'No data yesterday',
  pp_diagram_explanation: 'Charge Energy Consumption Diagram Explanation',
  pp_state_grid: 'State Grid',
  pp_transformer: 'Transformer',
  pp_consumption: 'Consumption',
  pp_detail_station: 'Station',
  pp_module_output: 'Module Output',
  pp_battery_input: 'Battery Input',
  pp_actual_charged: 'Actual Charged',
  pp_pile_consumption: 'Pile Consumption',
  pp_ups: 'UPS',
  pp_circuit_consumption: 'Circuit Consumption',
  pp_water_cooling: 'Water Cooling',
  pp_cooling_fan: 'Cooling Fan',
  pp_mechanical: 'Mechanical',
  pp_operation: 'Operation',
  pp_light: 'Light',
  pp_power_module: 'Power Module',
  pp_pile: 'Pile',
  pp_car_battery: 'Car Battery',
  pp_station_battery: 'Station Battery',
  pp_breakdown: 'Breakdown of charging energy:',
  pp_station_consumption: 'Station Energy Saving',
};

const deviceVersion = {
  pp_version_dashboard: 'Version Dashboard',
  pp_version_list: 'Version List',
  pp_piece: 'Piece',
  pp_device_activation: 'Activation Status',
  pp_link_status: 'Link Status',
  pp_activation: 'Activation Status',
  pp_in_blacklist: 'In Blacklist',
  pp_add: 'Add',
  pp_add_filter: 'Add Filter',
  pp_select_filter: 'Select Filter',
  pp_batch_editing: 'Batch Editing',
  pp_template_import: 'Template Import',
  pp_selected: 'Selected',
  pp_issue_work_order: 'Issue Work Order',
  pp_add_to_blacklist: 'Add to Blacklist',
  pp_remove_from_blacklist: 'Remove from Blacklist',
  pp_on_line: 'Online',
  pp_off_line: 'Offline',
  pp_blacklist: 'In the blacklist',
  pp_out_blacklist: 'Outside the blacklist',
  pp_send_history: 'Send History',
  pp_download_the_full_table: 'Download the full table',
  pp_please_select_device: 'Please select a device first',
  pp_issue_successful: 'Issue successful',
  pp_download_successful: 'Download successful',
  pp_download_failed: 'Download failed',
  pp_template_title: 'Import the template and issue the work order',
  pp_target_version: 'Expected upgrade target version',
  pp_import_file: 'Import File',
  pp_download_template: 'Download Template',
  pp_error_tip: 'Please select expected upgrade target version',
  pp_confirm_issuance: 'Confirm Issuance',
  pp_target_dialog_tip1: 'The following',
  pp_target_dialog_tip2: 'devices are already within the expected upgrade target version and the corresponding work order will not be sent!',
  pp_order_dialog_tip1: 'A total of',
  pp_order_dialog_tip2: 'devices have been selected, among which',
  pp_order_dialog_tip3: 'devices are on the blacklist. After confirmation, upgrade work orders will be issued to the remaining',
  pp_order_dialog_tip4: 'devices!',
  pp_template_dialog_tip1: 'A total of',
  pp_blacklist_dialog_tip1: 'A total of',
  pp_blacklist_dialog_tip2: 'devices have been selected. Confirm to add them to the blacklist in batch?',
  pp_blacklist_dialog_tip3: 'devices have been selected. Confirm to remove them from the blacklist in batch?',
  pp_blacklist_dialog_tip4: 'Confirm to add the device',
  pp_blacklist_dialog_tip5: 'to the blacklist?',
  pp_blacklist_dialog_tip6: 'Confirm to remove the device',
  pp_blacklist_dialog_tip7: 'from the blacklist?',
  pp_blacklist_result_tip1: 'Successfully ',
  pp_blacklist_result_tip2: 'added to ',
  pp_blacklist_result_tip3: 'removed from ',
  pp_blacklist_result_tip4: 'the blacklist',
  pp_jump_tip: 'Please manually switch to the 【Software Corresponding Disposal Work Order】 category after the jump',
  pp_version_tab: 'Version List',
  pp_dashboard_tip: 'Versions',
  pp_select_version: 'Please Select Version',
};

const chargeList = {
  pp_realtime_data: 'Realtime Data',
  pp_stop_reason: 'Stop Reason',
  pp_event_info: 'Event Info',
};

const biDashboard = {
  pp_time_error: 'The time range cannot exceed 31 days',
  pp_subscribe: 'Subscribe',
  pp_basic_info: 'Basic Info',
  pp_basic_info_tip: ' (The basic information shall be based on the latest date selected)',
  pp_high_way: 'Highway',
  pp_device_basic_info: 'Device Basic Info',
  pp_no_pie_chart: 'No Pie Chart Data',
  pp_no_bar_chart: 'No Bar Chart Data',
};

export default {
  menu,
  deviceManagement,
  station,
  home,
  serviceDetail,
  userManagement,
  logExport,
  common,
  historyData,
  edgeCloud,
  aiPortrait,
  orderList,
  camera,
  chargeModule,
  alarmList,
  batteryHistory,
  serviceTrace,
  gridInfo,
  batterySwap,
  licenseMatch,
  logAnalysis,
  processError,
  fireAlarm,
  diagnosisInfo,
  parkingOccupancy,
  stuckAnalysis,
  deviceSimulation,
  configList,
  flowMonitoring,
  satisfaction,
  bluetooth,
  swapPortrait,
  stationManagement,
  deviceVersion,
  chargeList,
  biDashboard,
  ...map,
};
