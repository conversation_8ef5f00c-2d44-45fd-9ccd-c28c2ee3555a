export const faultDiagnosis = {
  pp_fault_module: 'Fault Module',
  pp_fault_type: 'Fault Type',
  pp_can_cause_shutdown: 'Whether To Stop',

  pp_fault_id: 'Fault ID',
  pp_request_id: 'Request ID',

  pp_snapshot_instant_data: 'Snapshot Instant Data',
  pp_mode: 'Mode',
  pp_charging_elapsed: 'Charging Elapsed',
  pp_integral_electricity: 'Integral Electricity',
  pp_meter_electricity: 'Meter Electricity',
  pp_limit_power: 'Limit Power',
  pp_status_of_charge: 'Status Of Charge',
  pp_status_of_pcu: 'Status Of Pcu',
  pp_communication_count_of_pcu: 'Communication Count Of Pcu',
  pp_raw_request_voltage_of_bms: 'Raw Request Voltage Of Bms',
  pp_raw_request_current_of_bms: 'Raw Request Current Of Bms',
  pp_limited_request_voltage: 'Limited Request Voltage',
  pp_limited_request_current: 'Limited Request Current',
  pp_input_pcu_voltage_of_SCT: 'Input Pcu Voltage Of SCT',
  pp_input_pcu_current_of_SCT: 'Input Pcu Current Of SCT',
  pp_output_pcu_voltage_of_SCT: 'Output Pcu Voltage Of SCT',
  pp_output_pcu_current_of_SCT: 'Output Pcu Current Of SCT',
  pp_measure_voltage_of_insulation_module:
    'Measure Voltage Of Insulation Module',
  pp_measure_current_of_insulation_module:
    'Measure Current Of Insulation Module',
  pp_measure_voltage_of_decm_module: 'Measure Voltage Of Decm Module',
  pp_measure_current_of_decm_module: 'Measure Current Of Decm Module',
  pp_temperature_of_gun_head_plus: 'Temperature Of Gun Head Plus',
  pp_temperature_of_gun_head_minus: 'Temperature Of Gun Head Minus',
  pp_inlet_temperature: 'Inlet Temperature',
  pp_outlet_temperature: 'Outlet Temperature',
  pp_temperature_of_gun_tail_plus: 'Temperature Of Gun Tail Plus',
  pp_temperature_of_gun_tail_minus: 'Temperature Of Gun Tail Minus',
  pp_temperature_of_bronze_medal_plus: 'Temperature Of Bronze Medal Plus',
  pp_temperature_of_bronze_medal_minus: 'Temperature Of Bronze Medal Minus',
  pp_air_inlet_temperature: 'Air Inlet Temperature',
  pp_ambient_temperature: 'Ambient Temperature',
  pp_coolant_pressure: 'Coolant Pressure',
  pp_cooling_fan_duty_cycle: 'Cooling Fan Duty Cycle',
  pp_rotate_speed_of_fan1: 'Rotate Speed Of Fan1',
  pp_rotate_speed_of_fan2: 'Rotate Speed Of Fan2',
  pp_pump_duty_cycle: 'Pump Duty Cycle',
  pp_rotate_speed_of_pump: 'Rotate Speed Of Pump',

  pp_snapshot_result: 'Snapshot Diagnosis Result',
  pp_diagnosis_result: 'Result',
  pp_device_log: 'Device Log',

  diagnosisMode: {
    0: 'Available',
    1: 'Configuration',
    3: 'In charging',
    4: 'Charging completed',
    5: 'Charging failure',
  },
  state_of_pcu: {
    0: 'Normal',
    1: 'Fault',
  },
};
