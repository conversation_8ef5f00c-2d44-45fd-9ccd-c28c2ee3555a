export const factoryManagement = {
  pp_factory_test_report: 'Factory Test Report',
  pp_report_gen_time: 'Report Inspection Time',
  pp_manufacturer: 'Manufacturer',
  pp_fail_count: 'Number of Failed',
  pp_reporter: 'Reportor',
  pp_test_type: 'Test Type',
  pp_success_rate: 'Passing Rate',

  pp_factory_torque_report: 'Factory Torque Report',
  pp_service_count: 'Service Count',
  pp_service_valid_count: 'Valid Service Count',

  pp_drop_station_acceptance_report: 'Construction Acceptance Report',
  pp_drop_station_torque_report: 'Construction Torque Report',

  pp_edit_devide_status: 'Device Status',
  pp_valid_device_id: 'Valid Device ID',
  pp_manual_generation: 'Manual Generation',
  pp_device: 'Device',
  pp_time: 'Time',
  pp_preview: 'Preview',
  pp_has_highspeed: 'Exist High Speed',
  pp_select_device: 'Select Device',
  pp_select_time: 'Select Time',
  pp_select_status: 'Select Status',
  pp_dialog_title: 'Manual Generation (generate a torque report by manually selecting the site and time range)',

  deviceState: {
    // 笔译团队
    // 1: 'In stock',
    // 2: 'Shipped',
    // 3: 'Installed',
    // 马哥
    1: 'In production',
    2: 'Manufactured',
    3: 'Constructed',
  },
};
