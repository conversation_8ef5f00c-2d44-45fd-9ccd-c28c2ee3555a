export const tankTrans = {
  pp_releated_slot: 'Related Tank',
  pp_trans_duration: 'Duration',
  pp_full_charged_slot: 'Fully Tank',
  pp_drianed_slot: 'Depleted Tank',
  pp_empty_slot: 'Empty Tank',
  pp_tank_id: 'Tank ID',

  tank_step: {
    1: 'Initiate reverse',
    2: 'Transfer the battery from the fully charged tank',
    3: 'Transfer the battery from the fully charged battery tank to the discharged tank, and connect the charging plug to the discharged tank',
    4: 'Transfer the battery out of the discharged tank',
    5: 'Transfer the battery from the discharged tank to the fully charged battery tank, and connect the Liquid & Electric plug to the fully charged battery tank',
    6: 'Initialize the stacker and finish the reverse',
  },

  tank_axis: {
    1: 'Fork',
    2: '<PERSON><PERSON><PERSON> moves backward and forward',
    3: '<PERSON><PERSON><PERSON> moves upward and downward',
  },
};
