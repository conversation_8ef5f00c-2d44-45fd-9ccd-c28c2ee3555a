export const imageList = {
  imageType: {
    1: 'Battery Bottom Surface Abnomaly Detection Algorithm (full SOC)',
    2: 'Battery Top Surface Abnomaly Detection Algorithm (insufficient SOC)',
    3: 'Battery Bottom Surface Abnomaly Detection Algorithm (insufficient SOC)',
    4: 'Battery Top Surface Abnomaly Detection Algorithm (full SOC)',
    5: 'Image of loosening battery after locating; Image of loosening battery calibration after locating (including problematic images)',
    6: 'Image of loosening battery after locating; Image of loosening battery calibration after locating (including problematic images)',
    7: 'Image of tightening battery before locating; Image of tightening battery calibration before locating (including problematic images)',
    8: 'Image of tightening battery after locating; Image of tightening battery calibration after locating (including problematic images)',
    9: 'Wheel locate algorithm',
    10: 'Vehicle in platform algorithm',
    11: 'Pedestrian in platform algorithm',
    12: 'Screw missing detection algorithm',
    13: 'Pedestrian in platform parking algorithm',
    14: 'Wheel locate camera calibration algorithm',
    15: 'Battery upper surface camera calibration algorithm',
    16: 'Vehicle orientation recognition',
    17: 'Vehicle arrival alert algorithm',
    18: 'Rolling shut door verification algorithm',
    19: 'Rolling shut door status identification algorithm',
    20: 'WL Image of the V-slot wheel locating (operation)',
    21: 'Safe autonomous parking alert algorithm',
    22: 'Battery bottom camera calibration algorithm',
    23: 'WL Image of the V-slot wheel locating (enable power swap)',
  },

  imageStatus: {
    0: 'Normal',
    1: 'Abnormal',
  },
};
