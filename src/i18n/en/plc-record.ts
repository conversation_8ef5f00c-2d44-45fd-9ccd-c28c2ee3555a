export const plcRecord = {
  otherAxis: 'Other axes',
  noAxis: 'No corresponding axis',
  chartTitle: {
    high_speed: {
      speed: 'Speed（rpm）',
      position: 'Position（mm）',
      torque: 'Torque（%）',
    },
    di: {
      value: 'DI',
    },
    sensor: {
      value: 'Sensor',
    },
    converter: {
      err_code: 'Error Code',
      power: 'Power',
      frequency: 'Frequency',
      current: 'Current',
    },
  },
  platformStep: {
    1: {
      '-1': 'All Steps',
      1: '1 - Initialized self-test',
      2: '2- Position the clamps and lift the vehicle to C-Posi',
      3: '3- Check parking platform visually',
      4: '4- Lift the vehicle to W-Posi',
      5: '5- RG<PERSON> arrives at parking platform',
      6: '6- Rotation platform is rotated 90 degrees around the origin',
      7: '7- Check RGV visually',
      8: '8- Lift RGV to W-posi; solenoid electromagnet engaged',
      9: '9- Unlock',
      10: '10- RGV slowly descends to Liquid & Electric Connector Position',
      11: '11- RGV rapidly descends to M1-Posi',
      12: '12- Rotation platform back to the origin; RGV descends to H-Posi',
      13: '13- RG<PERSON> walks to battery tank',
      14: '14- Replace the old battery with the new one',
      15: '15- RGV walks to parking platform',
      16: '16- Rotation platform is rotated 90 degrees around the origin; extend vehicle positioning pin',
      17: '17- Check RGV visually',
      18: '18- Lift RGV to M1-Posi; extend vehicle positioning pin',
      19: '19- Lift RGV to W-Posi; retract vehicle positioning pin',
      20: '20- Lock',
      21: '21- RGV descends to the middle; retract vehicle positioning pin',
      22: '22- Rotation platform back to the origin',
      23: '23- RGV walks to battery tank',
      24: '24- Vehicle descends; retract four-wheel positioning mechanism',
    },
    2: {
      '-1': 'All Steps',
      '1': "1- Fix the four clamps to the car's four wheels; sliding gate opens ",
      '2': '2- Vehicle lift---vehicle with battery',
      '3': '3- RGV lift---without battery',
      '4': '4- Unlock',
      '5': '5- RGV descends---with battery',
      '6': '6- Vehicle descends---vehicle without battery',
      '7': '7- Power-loss battery moves to buffer zone',
      '8': '8- Vehicle lift---vehicle without battery',
      '9': '9- RGV lift---with battery; extend the positioning pin + retract a part of the positioning pin',
      '10': '10- Lock',
      '11': '11- RGV descends---without battery; retract the clamps',
      '12': '12-Vehile descends---vehicle with battery; retract the positioning pin',
      '13': '13- Power-loss battery moves out of the buffer zone; sliding gate closes',
      '14': '14- Sliding gate closes',
    },
    3: {
      '-1': 'All Steps',
      '1': '1- Position the clamps for the first time',
      '2': '2- Position the clamps for the second time &the sliding gate opens',
      '3': '3- Lift RGV to W-posi',
      '4': '4- Solenoid electromagnet engaged & unlock',
      '5': '5- Solenoid electromagnet disengaged & RGV descends',
      '6': '6- Sliding gate closes & RGV pans to battery docking position',
      '7': '7- Extend front and rear guide bar',
      '8': '8- Transfer battery from the parking platform to the left buffer zone',
      '9': '9- Transfer machine descends to H-Posi & Extend RGV block',
      '10': '10- Battery moves from the transfer machine to the parking platform',
      '11': '11- Retract front and rear guide bar & Sliding gate opens',
      '12': '12- RGV pans to bayobolt position',
      '13': '13- Lift RGV to M1-Posi',
      '14': '14- Lift RGV to M1-Posi & Extend vehicle positioning pin & Retract the platform block',
      '15': '15- Lift RGV to W-Posi & Retract vehicle positioning pin',
      '16': '16- Solenoid electromagnet engaged & Lock',
      '17': '17- Solenoid electromagnet disengaged & RGV descends to H-Posi & Retract vehicle positioning pin',
      '18': '18- Clamps back to H-Posi & Sliding gate closes & RGV pans to battery transfer position',
    },
    4: {
      '-1': 'All Steps',
      '1': '1- Position the clamps for the first time',
      '2': '2- Position the clamps for the second time &the sliding gate opens',
      '3': '3- Lift RGV to W-posi',
      '4': '4- Solenoid electromagnet engaged & unlock',
      '5': '5- Solenoid electromagnet disengaged & RGV descends',
      '6': '6- Sliding gate closes & RGV pans to battery docking position',
      '7': '7- Extend front and rear guide bar',
      '8': '8- Transfer battery from the parking platform to the left buffer zone',
      '10': '10- Battery moves from the docking position to the parking platform',
      '11': '11- Retract front and rear guide bar & Sliding gate opens',
      '12': '12- RGV pans to bayobolt position',
      '13': '13- Lift RGV to M1-Posi',
      '14': '14- Lift RGV to M1-Posi & Extend vehicle positioning pin & Retract the platform block',
      '15': '15- Lift RGV to W-Posi & Retract vehicle positioning pin',
      '16': '16- Solenoid electromagnet engaged & Lock',
      '17': '17- Solenoid electromagnet disengaged & RGV descends to H-Posi & Retract vehicle positioning pin',
      '18': '18- Clamps back to H-Posi & Sliding gate closes & RGV pans to battery transfer position',
    },
    7: {
      '-1': 'All Steps',
      1: '1 - Four-wheel push rod to working position',
      2: '2 - Flip door opens',
      3: '3 - Vehicle lifted to working position',
      4: '4 - RGV travels unloaded to parking platform',
      5: '5 - Body locating pin extends & RGV lifts unloaded to the pin position',
      6: '6 - Electromagnet releases, RGV lifts to work & body locating pin retracts synchronously',
      7: '7 - Electromagnet attracts & unlocks',
      8: '8 - Unlocking completed, RGV descends to the plug position',
      9: '9 - RGV descends to the zero point position',
      10: '10 - Electromagnet attracts & RGV moves to the docking position',
      11: '11 - The upper stopper on the docking machine extends',
      12: '12 - The transfer machine lifts to the working position',
      13: '13 - The lower stopper on the docking machine extends',
      14: '14 - The fork full of batteries is placed on the transfer machine',
      15: '15 - The transfer machine lifts to the middle position',
      16: '16 - The lower stopper on the docking machine retracts',
      17: '17 - The electromagnet engages & RGV proceeds with its load to the parking platform',
      18: '18 - The body positioning pin extends & RGV lifts with its load to the pin position',
      19: '19 - The electromagnet releases & RGV lifts to the working position & body positioning pin retracts synchronously',
      20: '20 - Electromagnet engages and body locating pin retracts',
      21: '21 - Locking',
      22: '22 - RGV descends to the pin position',
      23: '23 - RGV descends to the zero position',
      24: '24 - Forklift picks up the depleted battery completed',
      25: '25 - Electromagnet engages & RGV travels unloaded to the transfer position',
      26: '26 - Electromagnet releases & four-wheel push rods to zero point & vehicle lift descends to zero point & the flip door closes'
    }
  },

  batteryStep: {
    1: {
      '-1': 'All Steps',
      '1': '1- Initialized self-test',
      '2': '2- Zero-load lift rises to the target tank',
      '3': '3- Battery exits the target tank',
      '4': '4- Loaded lift descends to tank 6',
      '5': '5- Battery enters tank 6',
      '6': '6- Lift rises to docking position',
      '7': '7- Transfer battery to the lift',
      '8': '8- Loaded lift rises to the target tank',
      '9': '9- Battery enters the target tank',
      '10': '10- Zero-load lift descends to tank 6',
      '11': '11- Battery exits tank 6',
      '12': '12- Lift rises to docking position',
      '13': '13- Transfer battery to RGV',
    },
    2: {
      '-1': 'All Steps',
      '1': '1- Zero-load lift rises to the level of target tank',
      '2': '2- Loaded lift descends to the level of the docking position',
      // "3": " ",
      '4': '4- Battery exits the lift and arrives at the platform',
      '5': '5- Loaded lift rises to the level of target tank',
      '6': '6- Zero-load lift descends to the level of the docking position',
    },
    3: {
      '-1': 'All Steps',
      '1': '1- Retract Liquid & Electric Connector & Stacker moves to the docking position of the target tank & Transfer machine lifts',
      '2': '2- Extend fork target tank & Stacker rises to higher level & Retract fork',
      '3': '3- Stacker moves to the docking position of the transfer machine',
      '4': '4- Extend the fork transfer machine & Transfer machine rises to W-Posi & Retract fork',
      '5': '5- Transfer machine descends to H-Posi & Retract platform block',
      '6': '6- Extend front and rear guide bar & Battery moves from left buffer zone to transfer machine',
      '7': '7- Transfer machine rises to W-Posi',
      '8': '8- Extend the fork transfer machine & Transfer machine moves to H-Posi & Retract fork',
      '9': '9- Stacker moves to target tank & Fork extends at a high level & Stacker descends to a lower level & Retract fork',
      '10': '10- Extend Liquid & Electric Connector',
      '11': '11- Stacker moves from target tank to H-Posi',
    },
    4: {
      '-1': 'All Steps',
      '1': '1- Retract Liquid & Electric Connector & Stacker moves to the docking position of the target tank',
      '2': '2- Extend fork target tank & Stacker rises to higher level & Retract fork',
      '3': '3- Stacker moves to the docking position',
      '4': '4- Extend the fork & Fork lowering & Retract fork',
      '5': '5- Retract platform block',
      '6': '6- Extend front and rear guide bar & Battery moves from left buffer zone to docking position',
      '8': '8- Extend the fork & Forklift elevation & Retract fork',
      '9': '9- Stacker moves to target tank & Fork extends at a high level & Stacker descends to a lower level & Retract fork',
      '10': '10- Extend Liquid & Electric Connector',
      '11': '11- Stacker moves from target tank to H-Posi',
    },
    7: {
      '-1': 'All Steps',
      1: '1 - Hydraulic plug retracts & stacker to target low',
      2: '2 - Fork extends in target bay',
      3: '3 - Stacker crane ascends/descends to the high position of the target warehouse',
      4: '4 - Fork retracts at the high position in the target bay',
      5: '5 - Stacker crane ascends/descends to the high transfer position',
      6: '6 - Fork with fully charged battery ready to extend',
      7: '7 - Fork extends at the high position in the transfer bay',
      8: '8 - Stacker crane to the low position of the transfer machine',
      9: '9 - Fork retracts at the low position of the transfer machine',
      10: '10 - Fork at the low position of the transfer machine ready to pick up depleted battery',
      11: '11 - Fork extends at the low position of the transfer machine',
      12: '12 - Transfer lift mid-position to zero position',
      13: '13 - Fork retracts at the low position of the transfer machine with a depleted battery',
      14: '14 - Stacker crane to the high position of the target warehouse, retracting the upper layer pallet at the transfer',
      15: '15 - Fork extends at target high bay',
      16: '16 - Stacker to target low bay',
      17: '17 - Fork retracts at target low bay',
      18: '18 - Target bay hydraulic and electrical plug extends',
      19: '19 - Stacker to initial position'
    }
  },

  highSpeed: {
    1: {
      '1': '1#Bayobolt',
      '2': '2#Bayobolt',
      '3': '3#Bayobolt',
      '4': '4#Bayobolt',
      '5': '5#Bayobolt',
      '6': '6#Bayobolt',
      '7': '7#Bayobolt',
      '8': '8#Bayobolt',
      '9': '9#Bayobolt',
      '10': '10#Bayobolt',
      '11': 'RGV lift',
      '12': 'RGV walk',
      '13': 'Lift',
      '14': 'Turntable',
      '15': 'Front left clamp',
      '16': 'Front right clamp',
      '17': 'Rear left clamp',
      '18': 'Rear right clamp',
      '19': 'Front left pillar',
      '20': 'Front right pillar',
      '21': 'Rear left pillar',
      '22': 'Rear right pillar',
      'noAxis': 'No corresponding axis'
    },
    2: {
      '1': '1#Bayobolt',
      '2': '2#Bayobolt',
      '3': '3#Bayobolt',
      '4': '4#Bayobolt',
      '5': '5#Bayobolt',
      '6': '6#Bayobolt',
      '7': '7#Bayobolt',
      '8': '8#Bayobolt',
      '9': '9#Bayobolt',
      '10': '10#Bayobolt',
      '11': 'Front left positioning pin',
      '12': 'Rear right positioning pin',
      '13': 'Front left four-wheel clamp',
      '14': 'Front right four-wheel clamp',
      '15': 'Rear left four-wheel clamp',
      '16': 'Rear right four-wheel clamp',
      '17': 'V-slot',
      '18': 'Left sliding gate',
      '19': 'Right sliding gate',
      '20': 'Vehicle lift (left)',
      '21': 'Vehicle lift (right)',
      '22': 'Bayobolt lift',
      '23': 'Lift',
      'noAxis': 'No corresponding axis'
    },
    3: {
      '1': 'Fork',
      '2': 'Stacker move',
      '3': 'Stacker lift',
      '4': '1# Gun',
      '5': '2# Gun',
      '6': '3# Gun',
      '7': '4# Gun',
      '8': '5# Gun',
      '9': '6# Gun',
      '10': '7# Gun',
      '11': '8# Gun',
      '12': '9# Gun',
      '13': '10# Gun',
      '14': "1'# Gun ",
      '15': "2'# Gun ",
      '16': 'Left sliding gate',
      '17': 'Right sliding gate',
      '18': '1# gun head moves up and down',
      '19': '2# gun head moves up and down',
      '20': '9# gun head moves backward and forward',
      '21': '9# gun head moves up and down',
      '22': '10# gun head moves backward and forward',
      '23': '10# gun head moves up and down',
      '24': "1'# gun head moves up and down ",
      '25': "2'# gun head moves up and down ",
      '26': 'Front left vehicle position pin',
      '27': 'Rear left vehicle position pin',
      '28': 'Rear right vehicle position pin',
      '29': 'Front left clamp',
      '30': 'Front right clamp',
      '31': 'Front guide bar',
      '32': 'V-slot',
      '33': 'Rear left clamp',
      '34': 'Rear right clamp',
      '35': 'Rear guide bar',
      '36': 'Bayobolt platform moves backward and forward',
      '37': 'Lift of the Bayobolt platform',
      '40': 'Transfer machine',
      'noAxis': 'No corresponding axis'
    },
    4: {
      '1': 'Fork',
      '2': 'Stacker move',
      '3': 'Stacker lift',
      '4': '1# Gun',
      '5': '2# Gun',
      '6': '3# Gun',
      '7': '4# Gun',
      '8': '5# Gun',
      '9': '6# Gun',
      '10': '7# Gun',
      '11': '8# Gun',
      '12': '9# Gun',
      '13': '10# Gun',
      '14': "11# Gun ",
      '15': "12# Gun ",
      '16': 'Left sliding gate',
      '17': 'Right sliding gate',
      '18': '1# gun head moves up and down',
      '19': '2# gun head moves up and down',
      '20': '9# gun head moves up and down',
      '21': '10# gun head moves up and down',
      '22': '11# gun head moves up and down',
      '23': '12# gun head moves up and down',
      '26': 'Front left vehicle position pin',
      '27': 'Rear left vehicle position pin',
      '28': 'Rear right vehicle position pin',
      '29': 'Front left clamp',
      '30': 'Front right clamp',
      '31': 'Rear left clamp',
      '32': 'Rear right clamp',
      '33': 'Front guide bar',
      '34': 'Rear guide bar',
      '35': 'Bayobolt platform moves backward and forward',
      '36': 'Lift of the Bayobolt platform',
      'noAxis': 'No corresponding axis'
    },
    7: {
      1: 'Torque wrench 1 servo',
      2: 'Torque wrench 2 servo',
      3: 'Torque wrench 3 servo',
      4: 'Torque wrench 4 servo',
      5: 'Torque wrench 5 servo',
      6: 'Torque wrench 6 servo',
      7: 'Torque wrench 7 servo',
      8: 'Torque wrench 8 servo',
      9: 'Left front body locating pin servo',
      10: 'Right rear body locating pin servo',
      11: 'Left front wheel push rod servo',
      12: 'Right front wheel push rod servo',
      13: 'Left rear wheel push rod servo',
      14: 'Right rear wheel push rod servo',
      15: 'RGV travel servo',
      16: 'RGV lifting servo',
      17: 'Stacker crane fork servo',
      18: 'Stacker crane lifting servo',
      19: 'Flip door servo',
      20: 'RGV horizontal transfer servo',
      21: 'Left front vehicle lift',
      22: 'Right front vehicle lift',
      23: 'Left rear vehicle lift',
      24: 'Right rear vehicle lift',
      25: 'Front transfer lift',
      26: 'Rear transfer lift',
      'noAxis': 'No corresponding axis'
    }
  },

  sensorVarname: {
    1: {},
    2: {
      pl_lf_clamp_home_sensor: 'Left front wheel of clamp H-Posi',
      pl_lf_v_home_sensor: 'Left front parking position',
      pl_rf_clamp_home_sensor: 'Right front wheel of clamp H-Posi',
      pl_rf_v_home_sensor: 'Right front parking position',
      buffer_battery_safety_sensor: 'Battery safety at Buff-Posi',
      tr_battery_safety_sensor: 'Battery safety at Trans-Posi',
      vehical_l_lift_safety_sensor: 'Left vehicle lift safety',
      vehical_r_lift_safety_sensor: 'Right vehicle lift safety',
      buffer_battery_reached_sensor1: 'Front limit of battery at Buff-Posi',
      buffer_battery_reached_sensor2: 'Back limit of battery at Buff-Posi',
      buffer_battery_deceleration_sensor: 'Battery deceleration at Buff-Posi',
      pl_battery_reached_sensor1: 'Front limit of roller transfer battery',
      pl_battery_reached_sensor2: 'Back limit of roller transfer battery',
      pl_battery_deceleration_sensor: 'Roller transfer battery deceleration',
      pl_stopper_01_extend_seneor:
        'Up limit of roller transfer battery front stopper',
      pl_stopper_01_retract_seneor:
        'Down limit of roller transfer battery front stopper',
      pl_stopper_02_extend_seneor:
        'Up limit of roller transfer battery back stopper',
      pl_stopper_02_retract_seneor:
        'Down limit of roller transfer battery back stopper',
      pl_left_car_lift_work_sensor: 'W-Posi of left vehicle lift',
      pl_right_car_lift_work_sensor: 'W-Posi of right vehicle lift',
      lr_work_sensor: 'W-Posi of bayobolt plat lift',
      pl_lf_battery_reached_sensor:
        "There's xxx on the left front of the parking position battery",
      pl_lr_battery_reached_sensor:
        "There's xxx on the left rear of the parking position battery",
      pl_rf_battery_reached_sensor:
        "There's xxx on the right front of the parking position battery",
      pl_rr_battery_reached_sensor:
        "There's xxx on the right rear of the parking position battery",
      pl_f_battery_reached_sensor:
        "There's xxx in front of the parking position battery",
      pl_lf_ev_locating_pin_extend_sensor:
        'Up limit of left front car position pin',
      pl_lf_ev_locating_pin_retract_sensor:
        'Down limit of left front car position pin',
      pl_rr_ev_locating_pin_extend_sensor:
        'Up limit of right back car position pin',
      pl_rr_ev_locating_pin_retract_sensor:
        'Down limit of right back car position pin',
      pl_lr_clamp_home_sensor: 'H-Posi of left front wheel clamp',
      pl_rr_clamp_home_sensor: 'H-Posi of right front wheel clamp',
      pl_door_01_open_sensor: 'Left sliding door is fully opened',
      pl_door_01_close_sensor: 'Left sliding door is fully closed',
      pl_door_02_open_sensor: 'Right sliding door is fully opened',
      pl_door_02_close_sensor: 'Right sliding door is fully closed',
      bl_l_lift_safety_sensor: 'Lift left safety',
      bl_r_lift_safety_sensor: 'Lift right safety',
      tr_battery_exist_sensor: 'Battery at Trans-posi',
      bc_slot1_ec_retract_sensor:
        'Tank 1 electrical connector electrical clamp up limit',
      bc_slot1_ec_extend_sensor:
        'Tank 1 electrical connector electrical clamp down limit',
      bc_slot1_lc_retract_sensor:
        'Tank 1 liquid connector electrical clamp up limit',
      bc_slot1_lc_extend_sensor:
        'Tank 1 liquid connector electrical clamp down limit',
      bc_slot1_battery_exist_sensor_1: 'Tank 1 battery front limit',
      bc_slot1_battery_exist_sensor_2: 'Tank 1 battery back limit',
      bc_slot1_battery_deceleration_sensor: 'Tank 1 battery deceleration',
      bc_slot1_smoke_sensor: 'Tank 1 smoke sensor',
      bc_slot1_liq_flow_switch_sensor: 'Tank 1 water-cooling flow switch',
      bc_slot2_ec_retract_sensor:
        'Tank 2 electrical connector electrical clamp up limit',
      bc_slot2_ec_extend_sensor:
        'Tank 2 electrical connector electrical clamp down limit',
      bc_slot2_lc_retract_sensor:
        'Tank 2 liquid connector electrical clamp up limit',
      bc_slot2_lc_extend_sensor:
        'Tank 2 liquid connector electrical clamp down limit',
      bc_slot2_battery_exist_sensor_1: 'Tank 2 battery front limit',
      bc_slot2_battery_exist_sensor_2: 'Tank 2 battery back limit',
      bc_slot2_battery_deceleration_sensor: 'Tank 2 battery deceleration',
      bc_slot2_smoke_sensor: 'Tank 2 smoke sensor',
      bc_slot2_liq_flow_switch_sensor: 'Tank 2 water-cooling flow switch',
      bc_slot3_ec_retract_sensor:
        'Tank 3 electrical connector electrical clamp up limit',
      bc_slot3_ec_extend_sensor:
        'Tank 3 electrical connector electrical clamp down limit',
      bc_slot3_lc_retract_sensor:
        'Tank 3 liquid connector electrical clamp up limit',
      bc_slot3_lc_extend_sensor:
        'Tank 3 liquid connector electrical clamp down limit',
      bc_slot3_battery_exist_sensor_1: 'Tank 3 battery front limit',
      bc_slot3_battery_exist_sensor_2: 'Tank 3 battery back limit',
      bc_slot3_battery_deceleration_sensor: 'Tank 3 battery deceleration',
      bc_slot3_smoke_sensor: 'Tank 3 smoke sensor',
      bc_slot3_liq_flow_switch_sensor: 'Tank 3 water-cooling flow switch',
      bc_slot4_ec_retract_sensor:
        'Tank 4 electrical connector electrical clamp up limit',
      bc_slot4_ec_extend_sensor:
        'Tank 4 electrical connector electrical clamp down limit',
      bc_slot4_lc_retract_sensor:
        'Tank 4 liquid connector electrical clamp up limit',
      bc_slot4_lc_extend_sensor:
        'Tank 4 liquid connector electrical clamp down limit',
      bc_slot4_battery_exist_sensor_1: 'Tank 4 battery front limit',
      bc_slot4_battery_exist_sensor_2: 'Tank 4 battery back limit',
      bc_slot4_battery_deceleration_sensor: 'Tank 4 battery deceleration',
      bc_slot4_smoke_sensor: 'Tank 4 smoke sensor',
      bc_slot4_liq_flow_switch_sensor: 'Tank 4 water-cooling flow switch',
      bc_slot5_ec_retract_sensor:
        'Tank 5 electrical connector electrical clamp up limit',
      bc_slot5_ec_extend_sensor:
        'Tank 5 electrical connector electrical clamp down limit',
      bc_slot5_lc_retract_sensor:
        'Tank 5 liquid connector electrical clamp up limit',
      bc_slot5_lc_extend_sensor:
        'Tank 5 liquid connector electrical clamp down limit',
      bc_slot5_battery_exist_sensor_1: 'Tank 5 battery front limit',
      bc_slot5_battery_exist_sensor_2: 'Tank 5 battery back limit',
      bc_slot5_battery_deceleration_sensor: 'Tank 5 battery deceleration',
      bc_slot5_smoke_sensor: 'Tank 5 smoke sensor',
      bc_slot5_liq_flow_switch_sensor: 'Tank 5 water-cooling flow switch',
      bc_slot6_ec_retract_sensor:
        'Tank 6 electrical connector electrical clamp up limit',
      bc_slot6_ec_extend_sensor:
        'Tank 6 electrical connector electrical clamp down limit',
      bc_slot6_lc_retract_sensor:
        'Tank 6 liquid connector electrical clamp up limit',
      bc_slot6_lc_extend_sensor:
        'Tank 6 liquid connector electrical clamp down limit',
      bc_slot6_battery_exist_sensor_1: 'Tank 6 battery front limit',
      bc_slot6_battery_exist_sensor_2: 'Tank 6 battery back limit',
      bc_slot6_battery_deceleration_sensor: 'Tank 6 battery deceleration',
      bc_slot6_smoke_sensor: 'Tank 6 smoke sensor',
      bc_slot6_liq_flow_switch_sensor: 'Tank 6 water-cooling flow switch',
      liq_left_bc_pressure_switch_st: 'Left tank water-cooling pressure switch',
      bc_slot7_ec_retract_sensor:
        'Tank 7 electrical connector electrical clamp up limit',
      bc_slot7_ec_extend_sensor:
        'Tank 7 electrical connector electrical clamp down limit',
      bc_slot7_lc_retract_sensor:
        'Tank 7 liquid connector electrical clamp up limit',
      bc_slot7_lc_extend_sensor:
        'Tank 7 liquid connector electrical clamp down limit',
      bc_slot7_battery_exist_sensor_1: 'Tank 7 battery front limit',
      bc_slot7_battery_exist_sensor_2: 'Tank 7 battery back limit',
      bc_slot7_battery_deceleration_sensor: 'Tank 7 battery deceleration',
      bc_slot7_smoke_sensor: 'Tank 7 smoke sensor',
      bc_slot7_liq_flow_switch_sensor: 'Tank 7 water-cooling flow switch',
      bc_lift_get_exchange_station_1_7:
        'Tank 7 docking position of the lift battery',
      bc_slot8_ec_retract_sensor:
        'Tank 8 electrical connector electrical clamp up limit',
      bc_slot8_ec_extend_sensor:
        'Tank 8 electrical connector electrical clamp down limit',
      bc_slot8_lc_retract_sensor:
        'Tank 8 liquid connector electrical clamp up limit',
      bc_slot8_lc_extend_sensor:
        'Tank 8 liquid connector electrical clamp down limit',
      bc_slot8_battery_exist_sensor_1: 'Tank 8 battery front limit',
      bc_slot8_battery_exist_sensor_2: 'Tank 8 battery back limit',
      bc_slot8_battery_deceleration_sensor: 'Tank 8 battery deceleration',
      bc_slot8_smoke_sensor: 'Tank 8 smoke sensor',
      bc_slot8_liq_flow_switch_sensor: 'Tank 8 water-cooling flow switch',
      bc_lift_get_exchange_station_2_8:
        'Tank 8 docking position of the lift battery',
      bc_slot9_ec_retract_sensor:
        'Tank 9 electrical connector electrical clamp up limit',
      bc_slot9_ec_extend_sensor:
        'Tank 9 electrical connector electrical clamp down limit',
      bc_slot9_lc_retract_sensor:
        'Tank 9 liquid connector electrical clamp up limit',
      bc_slot9_lc_extend_sensor:
        'Tank 9 liquid connector electrical clamp down limit',
      bc_slot9_battery_exist_sensor_1: 'Tank 9 battery front limit',
      bc_slot9_battery_exist_sensor_2: 'Tank 9 battery back limit',
      bc_slot9_battery_deceleration_sensor: 'Tank 9 battery deceleration',
      bc_slot9_smoke_sensor: 'Tank 9 smoke sensor',
      bc_slot9_liq_flow_switch_sensor: 'Tank 9 water-cooling flow switch',
      bc_lift_get_exchange_station_3_9:
        'Tank 9 docking position of the lift battery',
      bc_slot10_ec_retract_sensor:
        'Tank 10 electrical connector electrical clamp up limit',
      bc_slot10_ec_extend_sensor:
        'Tank 10 electrical connector electrical clamp down limit',
      bc_slot10_lc_retract_sensor:
        'Tank 10 liquid connector electrical clamp up limit',
      bc_slot10_lc_extend_sensor:
        'Tank 10 liquid connector electrical clamp down limit',
      bc_slot10_battery_exist_sensor_1: 'Tank 10 battery front limit',
      bc_slot10_battery_exist_sensor_2: 'Tank 10 battery back limit',
      bc_slot10_battery_deceleration_sensor: 'Tank 10 battery deceleration',
      bc_slot10_smoke_sensor: 'Tank 10 smoke sensor',
      bc_slot10_liq_flow_switch_sensor: 'Tank 10 water-cooling flow switch',
      bc_lift_get_exchange_station_4_10:
        'Tank 10 docking position of the lift battery',
      bc_slot11_ec_retract_sensor:
        'Tank 11 electrical connector electrical clamp up limit',
      bc_slot11_ec_extend_sensor:
        'Tank 11 electrical connector electrical clamp down limit',
      bc_slot11_lc_retract_sensor:
        'Tank 11 liquid connector electrical clamp up limit',
      bc_slot11_lc_extend_sensor:
        'Tank 11 liquid connector electrical clamp down limit',
      bc_slot11_battery_exist_sensor_1: 'Tank 11 battery front limit',
      bc_slot11_battery_exist_sensor_2: 'Tank 11 battery back limit',
      bc_slot11_battery_deceleration_sensor: 'Tank 11 battery deceleration',
      bc_slot11_smoke_sensor: 'Tank 11 smoke sensor',
      bc_slot11_liq_flow_switch_sensor: 'Tank 11 water-cooling flow switch',
      bc_lift_get_exchange_station_5_11:
        'Tank 11 docking position of the lift battery',
      bc_slot12_ec_retract_sensor:
        'Tank 12 electrical connector electrical clamp up limit',
      bc_slot12_ec_extend_sensor:
        'Tank 12 electrical connector electrical clamp down limit',
      bc_slot12_lc_retract_sensor:
        'Tank 12 liquid connector electrical clamp up limit',
      bc_slot12_lc_extend_sensor:
        'Tank 12 liquid connector electrical clamp down limit',
      bc_slot12_battery_exist_sensor_1: 'Tank 12 battery front limit',
      bc_slot12_battery_exist_sensor_2: 'Tank 12 battery back limit',
      bc_slot12_battery_deceleration_sensor: 'Tank 12 battery deceleration',
      bc_slot12_smoke_sensor: 'Tank 12 smoke sensor',
      bc_slot12_liq_flow_switch_sensor: 'Tank 12 water-cooling flow switch',
      bc_lift_get_exchange_station_6_12:
        'Tank 12 docking position of the lift battery',
      bc_slot13_ec_retract_sensor:
        'Tank 13 electrical connector electrical clamp up limit',
      bc_slot13_ec_extend_sensor:
        'Tank 13 electrical connector electrical clamp down limit',
      bc_slot13_lc_retract_sensor:
        'Tank 13 liquid connector electrical clamp up limit',
      bc_slot13_lc_extend_sensor:
        'Tank 13 liquid connector electrical clamp down limit',
      bc_slot13_battery_exist_sensor_1: 'Tank 13 battery front limit',
      bc_slot13_battery_exist_sensor_2: 'Tank 13 battery back limit',
      bc_slot13_battery_deceleration_sensor: 'Tank13 battery deceleration',
      bc_slot13_smoke_sensor: 'Tank 13 smoke sensor',
      bc_slot13_liq_flow_switch_sensor: 'Tank 13 water-cooling flow switch',
      bc_lift_get_exchange_station_13:
        'Tank 13 docking position of the lift battery',
      liq_right_bc_pressure_switch_st:
        'Right tank water-cooling pressure switch',
      bl_lf_stopper_extend_sensor:
        'Fixed extension limit of left front electrical clamp of lift',
      bl_lf_stopper_retract_sensor:
        'Fixed retraction limit of left front electrical clamp of lift',
      bl_rf_stopper_extend_sensor:
        'Fixed extension limit of right front electrical clamp of lift',
      bl_rf_stopper_retract_sensor:
        'Fixed retraction limit of right front electrical clamp of lift',
      bl_lr_stopper_extend_sensor:
        'Fixed extension limit of left back electrical clamp of lift',
      bl_lr_stopper_retract_sensor:
        'Fixed retraction limit of left back electrical clamp of lift',
      bl_rr_stopper_extend_sensor:
        'Fixed extension limit of right back electrical clamp of lift',
      bl_rr_stopper_retract_sensor:
        'Fixed retraction limit of right back electrical clamp of lift',
      bl_lf_battery_reach_sensor: 'Left front battery of lift in position',
      bl_rf_battery_reach_sensor: 'Right front battery of lift in position',
      bl_battery_deceleration_sensor: 'Lift battery deceleration',
      V_l_fixed_extent_sensor: 'Fixed extension limit of left V-slot',
      V_l_fixed_retract_sensor: 'Fixed retraction limit of left V-slot',
      V_r_fixed_extent_sensor: 'Fixed extension limit of right V-slot',
      V_r_fixed_retract_sensor: 'Fixed retraction limit of right V-slot',
      lr_up_limit: 'Up limit of bayobolt plat',
      lr_down_limit: 'Down limit of bayobolt plat',
      lr_zero_sensor: 'H-Posi of bayobolt plat',
      l_vehical_lift_up_limit: 'Up limit of left vehicle lift',
      l_vehical_lift_down_limit: 'Down limit of left vehicle lift',
      l_vehical_lift_zero_sensor: 'H-Posi of left vehicle lift',
      r_vehical_lift_up_limit: 'Up limit of right vehicle lift',
      r_vehical_lift_down_limit: 'Down limit of right vehicle lift',
      r_vehical_lift_zero_sensor: 'H-Posi of right vehicle lift',
      bl_up_limit: 'Lift up limit',
      bl_down_limit: 'Lift down limit',
      bl_zero_sensor: 'Lift H-Posi',
    },
    3: {
      front_left_pressure_transducer: 'Left front pressure sensor',
      right_rear_pressure_transducer: 'Right rear pressure sensor',
      roller_door_01_up_limt: 'Front rolling door up limit',
      roller_door_01_down_limt: 'Front rolling door down limit',
      roller_door_02_up_limt: 'Rear rolling door up limit',
      roller_door_02_down_limt: 'Rear rolling door down limit',
      front_roller_door_safety_01: 'Front rolling door safety protection1',
      front_roller_door_safety_02: 'Front rolling door safety protection2',
      rear_roller_door_safety_01: 'Rear rolling door safety protection1',
      rear_roller_door_safety_02: 'Rear rolling door safety protection2',
      maintain_area_safety_01: 'Maintenance door safety relay feedback',
      maintain_area_safety_02: 'Maintenance door sensor',
      pl_buffer_dece_sensor_1: 'Left buffer battery deceleration',
      pl_buffer_sensor_f_1: 'Left buffer front battery positioning',
      pl_buffer_sensor_r_1: 'Left buffer rear battery positioning',
      pl_lf_clamp_home_sensor: 'H-Posi of left front wheel clamp',
      pl_lf_V_check_sensor: 'Left front wheel in-groove',
      pl_l_V_lock_extend_sensor: 'V-slot left locking extension limit',
      pl_l_V_lock_retract_sensor: 'V-slot left locking retraction limit',
      pl_rf_clamp_home_sensor: 'H-Posi of right front wheel clamp',
      pl_rf_V_check_sensor: 'Right front wheel in-groove',
      pl_r_V_lock_extend_sensor: 'V-slot right locking extension limit',
      pl_r_V_lock_retract_sensor: 'V-slot right locking retraction limit',
      pl_f_guide_work_sensor: 'Front guide bar front limit',
      pl_f_guide_home_sensor: 'Front guide bar back limit',
      pl_r_guide_work_sensor: 'Rear guide bar front limit',
      pl_r_guide_home_sensor: 'Rear guide bar back limit',
      pl_lr_clamp_home_sensor: 'H-Posi of rear left wheel clamp',
      pl_rr_clamp_home_sensor: 'H-Posi of rear right wheel clamp',
      pl_door_01_open_sensor: 'Left sliding door open limit',
      pl_door_01_close_sensor: 'Left sliding door close limit',
      pl_door_02_open_sensor: 'Right sliding door open limit',
      pl_door_02_close_sensor: 'Right sliding door close limit',
      pl_door_close_safe_sensor: 'Sliding door closure safety',
      bc_lift_dece_sensor: 'Lift cabinet deceleration',
      bc_lift_reach_sensor_f: 'Lift cabinet front limit',
      bc_lift_reach_sensor_r: 'Lift cabinet back limit',
      bc_lift_work_sensor: 'Lift cabinet rises to W-Posi',
      pl_buffer_dece_sensor_2: 'Right buffer battery deceleration',
      pl_buffer_sensor_f_2: 'Right buffer front battery positioning',
      pl_buffer_sensor_r_2: 'Right buffer rear battery positioning',
      buffer_stopper_01_extend_sensor_02:
        'H-Posi of right buffer front battery stopper',
      buffer_stopper_01_retract_sensor_02:
        'H-Posi of right buffer front battery stopper',
      buffer_stopper_02_extend_sensor_02:
        'W-Posi of right buffer rear battery stopper',
      buffer_stopper_02_retract_sensor_02:
        'H-Posi of right buffer rear battery stopper',
      RGV_bc_reach_sensor_01: 'Battery leveling 1',
      RGV_bc_reach_sensor_02: 'Battery leveling 2',
      RGV_bc_reach_sensor_03: 'Battery leveling 3',
      RGV_bc_reach_sensor_04: 'Battery leveling 4',
      RGV_bc_reach_sensor_05: 'Battery leveling 5',
      RGV_bc_reach_sensor_06: 'Battery leveling 6',
      lf_pin_extend_sensor: 'Left front vehicle position pin extension limit',
      lf_pin_retract_sensor: 'Left front vehicle position pin retraction limit',
      lf_pin_touch_sensor:
        'Left front vehicle position pin touches vehicle body',
      rr_pin_extend_sensor: 'Right rear vehicle position pin extension limit',
      rr_pin_retract_sensor: 'Right rear vehicle position pin retraction limit',
      rr_pin_touch_sensor:
        'Right rear vehicle position pin touches vehicle body',
      lr_pin_extend_sensor: 'Left rear vehicle position pin extension limit',
      lr_pin_retract_sensor: 'Left rear vehicle position pin retraction limit',
      lr_pin_touch_sensor:
        'Left rear vehicle position pin touches vehicle body',
      gun1_lift_work_sensor: '1#lift up limit',
      gun1_lift_home_sensor: '1#lift down limit',
      gun2_lift_work_sensor: '2#lift up limit',
      gun2_lift_home_sensor: '2#lift down limit',
      gun9_move_home_sensor: '9#translation position1',
      gun9_move_work_sensor: '9#translation position2',
      gun9_lift_work_sensor: '9#lift up limit',
      gun9_lift_home_sensor: '9#lift down limit',
      gun10_move_home_sensor: '10#translation position1',
      gun10_move_work_sensor: '10#translation position2',
      gun10_lift_work_sensor: '10#lift up limit',
      gun10_lift_home_sensor: '10#lift down limit',
      gun11_lift_work_sensor: '11#lift up limit',
      gun11_lift_home_sensor: '11#lift down limit',
      gun12_lift_work_sensor: '12#lift up limit',
      gun12_lift_home_sensor: '12#lift down limit',
      RGV_work_sensor: 'RGV lift W-Posi',
      RGV_maintain_sensor: 'RGV lift M4-Posi',
      pl_stopper_01_home_sensor: 'Front battery stopper lift H-Posi',
      pl_stopper_01_work_sensor: 'Front battery stopper lift W-Posi',
      pl_stopper_01_reach_sensor: 'Front battery stopper battery positioning',
      pl_stopper_02_home_sensor: 'Rear battery stopper lift H-Posi',
      pl_stopper_02_work_sensor: 'Rear battery stopper lift W-Posi',
      pl_stopper_02_reach_sensor: 'Rear battery stopper battery positioning',
      pl_move_work_sensor_1: 'RGV moves to bayobolt position',
      pl_move_work_sensor_2: 'RGV moves to NPA position',
      pl_move_work_sensor_3: 'RGV moves to NPD position',
      pl_move_work_sensor_4: 'RGV moves to backup1',
      pl_move_work_sensor_5: 'RGV moves to backup2',
      pl_stopper_01_dece_sensor: 'RGV stopper battery deceleration',
      bc_slot1_ec_retract_sensor_1: 'Slot 1 NPA electric plug retraction limit',
      bc_slot1_ec_extend_sensor_1: 'Slot 1 NPA electric plug extension limit',
      bc_slot1_lc_retract_sensor_1: 'Slot 1 NPA water plug retraction limit',
      bc_slot1_lc_extend_sensor_1: 'Slot 1 NPA water plug extension limit',
      bc_slot1_ec_retract_sensor_2: 'Slot 1 NPD electric plug retraction limit',
      bc_slot1_ec_extend_sensor_2: 'Slot 1 NPD electric plug extension limit',
      bc_slot1_lc_retract_sensor_2: 'Slot 1 NPD water plug retraction limit',
      bc_slot1_lc_extend_sensor_2: 'Slot 1 NPD water plug extension limit',
      bc_slot1_check_sensor_1: 'Slot 1 battery checks NPA',
      bc_slot1_check_sensor_2: 'Slot 1 battery checks NPD',
      bc_slot1_reached_sensor: 'Slot 1 battery reached position.',
      bc_slot1_smoke_sensor: 'Slot 1 smoke alarm',
      bc_slot1_liq_flow_switch_st: 'Slot 1 liquid cooling flow switch',
      bc_sum_smoke_sensor: 'Overall smoke of battery Slot',
      bc_slot2_ec_retract_sensor_1: 'Slot 2 NPA electric plug retraction limit',
      bc_slot2_ec_extend_sensor_1: 'Slot 2 NPA electric plug extension limit',
      bc_slot2_lc_retract_sensor_1: 'Slot 2 NPA water plug retraction limit',
      bc_slot2_lc_extend_sensor_1: 'Slot 2 NPA water plug extension limit',
      bc_slot2_ec_retract_sensor_2: 'Slot 2 NPD electric plug retraction limit',
      bc_slot2_ec_extend_sensor_2: 'Slot 2 NPD electric plug extension limit',
      bc_slot2_lc_retract_sensor_2: 'Slot 2 NPD water plug retraction limit',
      bc_slot2_lc_extend_sensor_2: 'Slot 2 NPD water plug extension limit',
      bc_slot2_check_sensor_1: 'Slot 2 battery checks NPA',
      bc_slot2_check_sensor_2: 'Slot 2 battery checks NPD',
      bc_slot2_reached_sensor: 'Slot 2 battery reached position.',
      bc_slot2_smoke_sensor: 'Slot 2 smoke alarm',
      bc_slot2_liq_flow_switch_st: 'Slot 2 liquid cooling flow switch',
      bc_slot3_ec_retract_sensor_1: 'Slot 3 NPA electric plug retraction limit',
      bc_slot3_ec_extend_sensor_1: 'Slot 3 NPA electric plug extension limit',
      bc_slot3_lc_retract_sensor_1: 'Slot 3 NPA water plug retraction limit',
      bc_slot3_lc_extend_sensor_1: 'Slot 3 NPA water plug extension limit',
      bc_slot3_ec_retract_sensor_2: 'Slot 3 NPD electric plug retraction limit',
      bc_slot3_ec_extend_sensor_2: 'Slot 3 NPD electric plug extension limit',
      bc_slot3_lc_retract_sensor_2: 'Slot 3 NPD water plug retraction limit',
      bc_slot3_lc_extend_sensor_2: 'Slot 3 NPD water plug extension limit',
      bc_slot3_check_sensor_1: 'Slot 3 battery checks NPA',
      bc_slot3_check_sensor_2: 'Slot 3 battery checks NPD',
      bc_slot3_reached_sensor: 'Slot 3 battery reached position.',
      bc_slot3_smoke_sensor: 'Slot 3 smoke alarm',
      bc_slot3_liq_flow_switch_st: 'Slot 3 liquid cooling flow switch',
      bc_slot4_ec_retract_sensor_1: 'Slot 4 NPA electric plug retraction limit',
      bc_slot4_ec_extend_sensor_1: 'Slot 4 NPA electric plug extension limit',
      bc_slot4_lc_retract_sensor_1: 'Slot 4 NPA water plug retraction limit',
      bc_slot4_lc_extend_sensor_1: 'Slot 4 NPA water plug extension limit',
      bc_slot4_ec_retract_sensor_2: 'Slot 4 NPD electric plug retraction limit',
      bc_slot4_ec_extend_sensor_2: 'Slot 4 NPD electric plug extension limit',
      bc_slot4_lc_retract_sensor_2: 'Slot 4 NPD water plug retraction limit',
      bc_slot4_lc_extend_sensor_2: 'Slot 4 NPD water plug extension limit',
      bc_slot4_check_sensor_1: 'Slot 4 battery checks NPA',
      bc_slot4_check_sensor_2: 'Slot 4 battery checks NPD',
      bc_slot4_reached_sensor: 'Slot 4 battery reached position.',
      bc_slot4_smoke_sensor: 'Slot 4 smoke alarm',
      bc_slot4_liq_flow_switch_st: 'Slot 4 liquid cooling flow switch',
      bc_slot5_ec_retract_sensor_1: 'Slot 5 NPA electric plug retraction limit',
      bc_slot5_ec_extend_sensor_1: 'Slot 5 NPA electric plug extension limit',
      bc_slot5_lc_retract_sensor_1: 'Slot 5 NPA water plug retraction limit',
      bc_slot5_lc_extend_sensor_1: 'Slot 5 NPA water plug extension limit',
      bc_slot5_ec_retract_sensor_2: 'Slot 5 NPD electric plug retraction limit',
      bc_slot5_ec_extend_sensor_2: 'Slot 5 NPD electric plug extension limit',
      bc_slot5_lc_retract_sensor_2: 'Slot 5 NPD water plug retraction limit',
      bc_slot5_lc_extend_sensor_2: 'Slot 5 NPD water plug extension limit',
      bc_slot5_check_sensor_1: 'Slot 5 battery checks NPA',
      bc_slot5_check_sensor_2: 'Slot 5 battery checks NPD',
      bc_slot5_reached_sensor: 'Slot 5 battery reached position.',
      bc_slot5_smoke_sensor: 'Slot 5 smoke alarm',
      bc_slot5_liq_flow_switch_st: 'Slot 5 liquid cooling flow switch',
      bcslot1_5_pressure_switch_st: 'Slot 1~5 liquid cooling pressure switch',
      bc_slot6_ec_retract_sensor_1: 'Slot 6 NPA electric plug retraction limit',
      bc_slot6_ec_extend_sensor_1: 'Slot 6 NPA electric plug extension limit',
      bc_slot6_lc_retract_sensor_1: 'Slot 6 NPA water plug retraction limit',
      bc_slot6_lc_extend_sensor_1: 'Slot 6 NPA water plug extension limit',
      bc_slot6_ec_retract_sensor_2: 'Slot 6 NPD electric plug retraction limit',
      bc_slot6_ec_extend_sensor_2: 'Slot 6 NPD electric plug extension limit',
      bc_slot6_lc_retract_sensor_2: 'Slot 6 NPD water plug retraction limit',
      bc_slot6_lc_extend_sensor_2: 'Slot 6 NPD water plug extension limit',
      bc_slot6_check_sensor_1: 'Slot 6 battery checks NPA',
      bc_slot6_check_sensor_2: 'Slot 6 battery checks NPD',
      bc_slot6_reached_sensor: 'Slot 6 battery reached position.',
      bc_slot6_smoke_sensor: 'Slot 6 smoke alarm',
      bc_slot6_liq_flow_switch_st: 'Slot 6 liquid cooling flow switch',
      bc_slot7_ec_retract_sensor_1: 'Slot 7 NPA electric plug retraction limit',
      bc_slot7_ec_extend_sensor_1: 'Slot 7 NPA electric plug extension limit',
      bc_slot7_lc_retract_sensor_1: 'Slot 7 NPA water plug retraction limit',
      bc_slot7_lc_extend_sensor_1: 'Slot 7 NPA water plug extension limit',
      bc_slot7_ec_retract_sensor_2: 'Slot 7 NPD electric plug retraction limit',
      bc_slot7_ec_extend_sensor_2: 'Slot 7 NPD electric plug extension limit',
      bc_slot7_lc_retract_sensor_2: 'Slot 7 NPD water plug retraction limit',
      bc_slot7_lc_extend_sensor_2: 'Slot 7 NPD water plug extension limit',
      bc_slot7_check_sensor_1: 'Slot 7 battery checks NPA',
      bc_slot7_check_sensor_2: 'Slot 7 battery checks NPD',
      bc_slot7_reached_sensor: 'Slot 7 battery reached position.',
      bc_slot7_smoke_sensor: 'Slot 7 smoke alarm',
      bc_slot7_liq_flow_switch_st: 'Slot 7 liquid cooling flow switch',
      bc_slot8_ec_retract_sensor_1: 'Slot 8 NPA electric plug retraction limit',
      bc_slot8_ec_extend_sensor_1: 'Slot 8 NPA electric plug extension limit',
      bc_slot8_lc_retract_sensor_1: 'Slot 8 NPA water plug retraction limit',
      bc_slot8_lc_extend_sensor_1: 'Slot 8 NPA water plug extension limit',
      bc_slot8_ec_retract_sensor_2: 'Slot 8 NPD electric plug retraction limit',
      bc_slot8_ec_extend_sensor_2: 'Slot 8 NPD electric plug extension limit',
      bc_slot8_lc_retract_sensor_2: 'Slot 8 NPD water plug retraction limit',
      bc_slot8_lc_extend_sensor_2: 'Slot 8 NPD water plug extension limit',
      bc_slot8_check_sensor_1: 'Slot 8 battery checks NPA',
      bc_slot8_check_sensor_2: 'Slot 8 battery checks NPD',
      bc_slot8_reached_sensor: 'Slot 8 battery reached position.',
      bc_slot8_smoke_sensor: 'Slot 8 smoke alarm',
      bc_slot8_liq_flow_switch_st: 'Slot 8 liquid cooling flow switch',
      bc_slot9_ec_retract_sensor_1: 'Slot 9 NPA electric plug retraction limit',
      bc_slot9_ec_extend_sensor_1: 'Slot 9 NPA electric plug extension limit',
      bc_slot9_lc_retract_sensor_1: 'Slot 9 NPA water plug retraction limit',
      bc_slot9_lc_extend_sensor_1: 'Slot 9 NPA water plug extension limit',
      bc_slot9_ec_retract_sensor_2: 'Slot 9 NPD electric plug retraction limit',
      bc_slot9_ec_extend_sensor_2: 'Slot 9 NPD electric plug extension limit',
      bc_slot9_lc_retract_sensor_2: 'Slot 9 NPD water plug retraction limit',
      bc_slot9_lc_extend_sensor_2: 'Slot 9 NPD water plug extension limit',
      bc_slot9_check_sensor_1: 'Slot 9 battery checks NPA',
      bc_slot9_check_sensor_2: 'Slot 9 battery checks NPD',
      bc_slot9_reached_sensor: 'Slot 9 battery reached position.',
      bc_slot9_smoke_sensor: 'Slot 9 smoke alarm',
      bc_slot9_liq_flow_switch_st: 'Slot 9 liquid cooling flow switch',
      bc_slot10_ec_retract_sensor_1:
        'Slot 10 NPA electric plug retraction limit',
      bc_slot10_ec_extend_sensor_1: 'Slot 10 NPA electric plug extension limit',
      bc_slot10_lc_retract_sensor_1: 'Slot 10 NPA water plug retraction limit',
      bc_slot10_lc_extend_sensor_1: 'Slot 10 NPA water plug extension limit',
      bc_slot10_ec_retract_sensor_2:
        'Slot 10 NPD electric plug retraction limit',
      bc_slot10_ec_extend_sensor_2: 'Slot 10 NPD electric plug extension limit',
      bc_slot10_lc_retract_sensor_2: 'Slot 10 NPD water plug retraction limit',
      bc_slot10_lc_extend_sensor_2: 'Slot 10 NPD water plug extension limit',
      bc_slot10_check_sensor_1: 'Slot 10 battery checks NPA',
      bc_slot10_check_sensor_2: 'Slot 10 battery checks NPD',
      bc_slot10_reached_sensor: 'Slot 10 battery reached position.',
      bc_slot10_smoke_sensor: 'Slot 10 smoke alarm',
      bc_slot10_liq_flow_switch_st: 'Slot 10 liquid cooling flow switch',
      bcslot6_10_pressure_switch_st: 'Slot 6~10 liquid cooling pressure switch',
      stacker_low_sensor_1: 'Docking level 1 of the stacker tank',
      stacker_low_sensor_2: 'Docking level 2 of the stacker tank',
      stacker_low_sensor_3: 'Docking level 3 of the stacker tank',
      stacker_low_sensor_4: 'Docking level 4 of the stacker tank',
      stacker_low_sensor_5: 'Docking level 5 of the stacker tank',
      stacker_low_sensor_6: 'Docking level 6 of the stacker tank',
      stacker_low_sensor_0: 'Trans-posi and docking posi of the stacker',
      stacker_move_f_sensor: 'Front tank of a walking stacker',
      stacker_move_r_sensor: 'Back tank of a walking stacker',
      stacker_move_RGV_sensor: 'RGV docking position of a walking stacker',
      stacker_left_safe_sensor_1:
        'Left overtravel of the upper layer of the fork',
      stacker_right_safe_sensor_1:
        'Right overtravel of the upper layer of the fork',
      stacker_left_safe_sensor_2:
        'Left overtravel of the lower layer of the fork',
      stacker_right_safe_sensor_2:
        'Right overtravel of the lower layer of the fork',
      fork_retract_sensor_1: 'Midpoint of main fork arm',
      fork_retract_sensor_2: 'Midpoint of assistant fork arm',
      fork_left_extend_sensor_1: 'Left limit 1 of fork arm',
      fork_left_extend_sensor_2: 'Left limit 2 of fork arm',
      fork_right_extend_sensor_1: 'Right limit of fork arm',
      fork_bc_exist_sensor_1: 'Fork with battery 1',
      fork_bc_exist_sensor_2: 'Fork with battery 2',
      vehaicl_l_work_sensor: 'W-Posi of left vehicle lift',
      vehaicl_l_maintain_sensor: 'M4-Posi of left vehicle lift',
      vehaicl_l_safe_sensor: 'Left vehicle lift safety',
      vehaicl_l_bc_safe_sensor: 'Left vehicle lift battery safety',
      vehaicl_r_work_sensor: 'W-Posi of right vehicle lift',
      vehaicl_r_maintain_sensor: 'M4-Posi of right vehicle lift',
      vehaicl_r_safe_sensor: 'Right vehicle lift safety',
      vehaicl_r_bc_safe_sensor: 'RIght vehicle lift battery safety',
      bc_slot11_ec_retract_sensor_1:
        'Slot 11 NPA electric plug retraction limit',
      bc_slot11_ec_extend_sensor_1: 'Slot 11 NPA electric plug extension limit',
      bc_slot11_lc_retract_sensor_1: 'Slot 11 NPA water plug retraction limit',
      bc_slot11_lc_extend_sensor_1: 'Slot 11 NPA water plug extension limit',
      bc_slot11_ec_retract_sensor_2:
        'Slot 11 NPD electric plug retraction limit',
      bc_slot11_ec_extend_sensor_2: 'Slot 11 NPD electric plug extension limit',
      bc_slot11_lc_retract_sensor_2: 'Slot 11 NPD water plug retraction limit',
      bc_slot11_lc_extend_sensor_2: 'Slot 11 NPD water plug extension limit',
      bc_slot11_check_sensor_1: 'Slot 11 battery checks NPA',
      bc_slot11_check_sensor_2: 'Slot 11 battery checks NPD',
      bc_slot11_reached_sensor: 'Slot 11 battery reached position.',
      bc_slot11_smoke_sensor: 'Slot 11 smoke alarm',
      bc_slot11_liq_flow_switch_st: 'Slot 11 liquid cooling flow switch',
      bc_slot12_ec_retract_sensor_1:
        'Slot 12 NPA electric plug retraction limit',
      bc_slot12_ec_extend_sensor_1: 'Slot 12 NPA electric plug extension limit',
      bc_slot12_lc_retract_sensor_1: 'Slot 12 NPA water plug retraction limit',
      bc_slot12_lc_extend_sensor_1: 'Slot 12 NPA water plug extension limit',
      bc_slot12_ec_retract_sensor_2:
        'Slot 12 NPD electric plug retraction limit',
      bc_slot12_ec_extend_sensor_2: 'Slot 12 NPD electric plug extension limit',
      bc_slot12_lc_retract_sensor_2: 'Slot 12 NPD water plug retraction limit',
      bc_slot12_lc_extend_sensor_2: 'Slot 12 NPD water plug extension limit',
      bc_slot12_check_sensor_1: 'Slot 12 battery checks NPA',
      bc_slot12_check_sensor_2: 'Slot 12 battery checks NPD',
      bc_slot12_reached_sensor: 'Slot 12 battery reached position.',
      bc_slot12_smoke_sensor: 'Slot 12 smoke alarm',
      bc_slot12_liq_flow_switch_st: 'Slot 12 liquid cooling flow switch',
      bc_slot13_ec_retract_sensor_1:
        'Slot 13 NPA electric plug retraction limit',
      bc_slot13_ec_extend_sensor_1: 'Slot 13 NPA electric plug extension limit',
      bc_slot13_lc_retract_sensor_1: 'Slot 13 NPA water plug retraction limit',
      bc_slot13_lc_extend_sensor_1: 'Slot 13 NPA water plug extension limit',
      bc_slot13_ec_retract_sensor_2:
        'Slot 13 NPD electric plug retraction limit',
      bc_slot13_ec_extend_sensor_2: 'Slot 13 NPD electric plug extension limit',
      bc_slot13_lc_retract_sensor_2: 'Slot 13 NPD water plug retraction limit',
      bc_slot13_lc_extend_sensor_2: 'Slot 13 NPD water plug extension limit',
      bc_slot13_check_sensor_1: 'Slot 13 battery checks NPA',
      bc_slot13_check_sensor_2: 'Slot 13 battery checks NPD',
      bc_slot13_reached_sensor: 'Slot 13 battery reached position.',
      bc_slot13_smoke_sensor: 'Slot 13 smoke alarm',
      bc_slot13_liq_flow_switch_st: 'Slot 13 liquid cooling flow switch',
      bc_slot14_ec_retract_sensor_1:
        'Slot 14 NPA electric plug retraction limit',
      bc_slot14_ec_extend_sensor_1: 'Slot 14 NPA electric plug extension limit',
      bc_slot14_lc_retract_sensor_1: 'Slot 14 NPA water plug retraction limit',
      bc_slot14_lc_extend_sensor_1: 'Slot 14 NPA water plug extension limit',
      bc_slot14_ec_retract_sensor_2:
        'Slot 14 NPD electric plug retraction limit',
      bc_slot14_ec_extend_sensor_2: 'Slot 14 NPD electric plug extension limit',
      bc_slot14_lc_retract_sensor_2: 'Slot 14 NPD water plug retraction limit',
      bc_slot14_lc_extend_sensor_2: 'Slot 14 NPD water plug extension limit',
      bc_slot14_check_sensor_1: 'Slot 14 battery checks NPA',
      bc_slot14_check_sensor_2: 'Slot 14 battery checks NPD',
      bc_slot14_reached_sensor: 'Slot 14 battery reached position.',
      bc_slot14_smoke_sensor: 'Slot 14 smoke alarm',
      bc_slot14_liq_flow_switch_st: 'Slot 14 liquid cooling flow switch',
      bc_slot15_ec_retract_sensor_1:
        'Slot 15 NPA electric plug retraction limit',
      bc_slot15_ec_extend_sensor_1: 'Slot 15 NPA electric plug extension limit',
      bc_slot15_lc_retract_sensor_1: 'Slot 15 NPA water plug retraction limit',
      bc_slot15_lc_extend_sensor_1: 'Slot 15 NPA water plug extension limit',
      bc_slot15_ec_retract_sensor_2:
        'Slot 15 NPD electric plug retraction limit',
      bc_slot15_ec_extend_sensor_2: 'Slot 15 NPD electric plug extension limit',
      bc_slot15_lc_retract_sensor_2: 'Slot 15 NPD water plug retraction limit',
      bc_slot15_lc_extend_sensor_2: 'Slot 15 NPD water plug extension limit',
      bc_slot15_check_sensor_1: 'Slot 15 battery checks NPA',
      bc_slot15_check_sensor_2: 'Slot 15 battery checks NPD',
      bc_slot15_reached_sensor: 'Slot 15 battery reached position.',
      bc_slot15_smoke_sensor: 'Slot 15 smoke alarm',
      bc_slot15_liq_flow_switch_st: 'Slot 15 liquid cooling flow switch',
      bc_slot16_ec_retract_sensor_1:
        'Slot 16 NPA electric plug retraction limit',
      bc_slot16_ec_extend_sensor_1: 'Slot 16 NPA electric plug extension limit',
      bc_slot16_lc_retract_sensor_1: 'Slot 16 NPA water plug retraction limit',
      bc_slot16_lc_extend_sensor_1: 'Slot 16 NPA water plug extension limit',
      bc_slot16_ec_retract_sensor_2:
        'Slot 16 NPD electric plug retraction limit',
      bc_slot16_ec_extend_sensor_2: 'Slot 16 NPD electric plug extension limit',
      bc_slot16_lc_retract_sensor_2: 'Slot 16 NPD water plug retraction limit',
      bc_slot16_lc_extend_sensor_2: 'Slot 16 NPD water plug extension limit',
      bc_slot16_check_sensor_1: 'Slot 16 battery checks NPA',
      bc_slot16_check_sensor_2: 'Slot 16 battery checks NPD',
      bc_slot16_reached_sensor: 'Slot 16 battery reached position.',
      bc_slot16_smoke_sensor: 'Slot 16 smoke alarm',
      bc_slot16_liq_flow_switch_st: 'Slot 16 liquid cooling flow switch',
      bc_slot17_ec_retract_sensor_1:
        'Slot 17 NPA electric plug retraction limit',
      bc_slot17_ec_extend_sensor_1: 'Slot 17 NPA electric plug extension limit',
      bc_slot17_lc_retract_sensor_1: 'Slot 17 NPA water plug retraction limit',
      bc_slot17_lc_extend_sensor_1: 'Slot 17 NPA water plug extension limit',
      bc_slot17_ec_retract_sensor_2:
        'Slot 17 NPD electric plug retraction limit',
      bc_slot17_ec_extend_sensor_2: 'Slot 17 NPD electric plug extension limit',
      bc_slot17_lc_retract_sensor_2: 'Slot 17 NPD water plug retraction limit',
      bc_slot17_lc_extend_sensor_2: 'Slot 17 NPD water plug extension limit',
      bc_slot17_check_sensor_1: 'Slot 17 battery checks NPA',
      bc_slot17_check_sensor_2: 'Slot 17 battery checks NPD',
      bc_slot17_reached_sensor: 'Slot 17 battery reached position.',
      bc_slot17_smoke_sensor: 'Slot 17 smoke alarm',
      bc_slot17_liq_flow_switch_st: 'Slot 17 liquid cooling flow switch',
      bc_slot18_ec_retract_sensor_1:
        'Slot 18 NPA electric plug retraction limit',
      bc_slot18_ec_extend_sensor_1: 'Slot 18 NPA electric plug extension limit',
      bc_slot18_lc_retract_sensor_1: 'Slot 18 NPA water plug retraction limit',
      bc_slot18_lc_extend_sensor_1: 'Slot 18 NPA water plug extension limit',
      bc_slot18_ec_retract_sensor_2:
        'Slot 18 NPD electric plug retraction limit',
      bc_slot18_ec_extend_sensor_2: 'Slot 18 NPD electric plug extension limit',
      bc_slot18_lc_retract_sensor_2: 'Slot 18 NPD water plug retraction limit',
      bc_slot18_lc_extend_sensor_2: 'Slot 18 NPD water plug extension limit',
      bc_slot18_check_sensor_1: 'Slot 18 battery checks NPA',
      bc_slot18_check_sensor_2: 'Slot 18 battery checks NPD',
      bc_slot18_reached_sensor: 'Slot 18 battery reached position.',
      bc_slot18_smoke_sensor: 'Slot 18 smoke alarm',
      bc_slot18_liq_flow_switch_st: 'Slot 18 liquid cooling flow switch',
      bc_slot19_ec_retract_sensor_1:
        'Slot 19 NPA electric plug retraction limit',
      bc_slot19_ec_extend_sensor_1: 'Slot 19 NPA electric plug extension limit',
      bc_slot19_lc_retract_sensor_1: 'Slot 19 NPA water plug retraction limit',
      bc_slot19_lc_extend_sensor_1: 'Slot 19 NPA water plug extension limit',
      bc_slot19_ec_retract_sensor_2:
        'Slot 19 NPD electric plug retraction limit',
      bc_slot19_ec_extend_sensor_2: 'Slot 19 NPD electric plug extension limit',
      bc_slot19_lc_retract_sensor_2: 'Slot 19 NPD water plug retraction limit',
      bc_slot19_lc_extend_sensor_2: 'Slot 19 NPD water plug extension limit',
      bc_slot19_check_sensor_1: 'Slot 19 battery checks NPA',
      bc_slot19_check_sensor_2: 'Slot 19 battery checks NPD',
      bc_slot19_reached_sensor: 'Slot 19 battery reached position.',
      bc_slot19_smoke_sensor: 'Slot 19 smoke alarm',
      bc_slot19_liq_flow_switch_st: 'Slot 19 liquid cooling flow switch',
      bc_slot20_ec_retract_sensor_1:
        'Slot 20 NPA electric plug retraction limit',
      bc_slot20_ec_extend_sensor_1: 'Slot 20 NPA electric plug extension limit',
      bc_slot20_lc_retract_sensor_1: 'Slot 20 NPA water plug retraction limit',
      bc_slot20_lc_extend_sensor_1: 'Slot 20 NPA water plug extension limit',
      bc_slot20_ec_retract_sensor_2:
        'Slot 20 NPD electric plug retraction limit',
      bc_slot20_ec_extend_sensor_2: 'Slot 20 NPD electric plug extension limit',
      bc_slot20_lc_retract_sensor_2: 'Slot 20 NPD water plug retraction limit',
      bc_slot20_lc_extend_sensor_2: 'Slot 20 NPD water plug extension limit',
      bc_slot20_check_sensor_1: 'Slot 20 battery checks NPA',
      bc_slot20_check_sensor_2: 'Slot 20 battery checks NPD',
      bc_slot20_reached_sensor: 'Slot 20 battery reached position.',
      bc_slot20_smoke_sensor: 'Slot 20 smoke alarm',
      bc_slot20_liq_flow_switch_st: 'Slot 20 liquid cooling flow switch',
      bc_slot21_ec_retract_sensor_1:
        'Slot 21 NPA electric plug retraction limit',
      bc_slot21_ec_extend_sensor_1: 'Slot 21 NPA electric plug extension limit',
      bc_slot21_ec_retract_sensor_2:
        'Slot 21 NPD electric plug retraction limit',
      bc_slot21_ec_extend_sensor_2: 'Slot 21 NPD electric plug extension limit',
      bc_slot21_check_sensor_1: 'Slot 21 battery checks NPA',
      bc_slot21_check_sensor_2: 'Slot 21 battery checks NPD',
      bc_slot21_reached_sensor: 'Slot 21 battery reached position.',
      bc_slot21_smoke_sensor: 'Slot 21 smoke alarm',
      bc_slot11_15_pressure_switch_st:
        'Slot 11~15 liquid cooling pressure switch',
      bc_slot16_20_pressure_switch_st:
        'Slot 16~20 liquid cooling pressure switch',
      bc_fire_push_retract_sensor_1:
        'Front fire protection shuttle push rod retracts in place',
      bc_fire_push_extend_sensor_1:
        'Front fire protection shuttle push rod extends in place',
      bc_fire_push_retract_sensor_2:
        'Rear fire protection shuttle push rod retracts in place',
      bc_fire_push_extend_sensor_2:
        'Rear fire protection shuttle push rod extends in place',
      fire_liq_check: 'Liquid level measurement for fire protection',
      fork_X_left_limit_sensor: 'Left limit of the stacker fork',
      fork_X_right_limit_sensor: 'Right limit of the stacker fork',
      fork_X_home_sensor: 'H-Posi of the stacker fork',
      stacker_move_f_limit_sensor: 'Front limit of a walking stacker',
      stacker_move_r_limit_sensor: 'Back limit of a walking stacker',
      stacker_move_home_sensor: 'H-Posi of a walking stacker',
      stacker_lift_up_limit_sensor: 'Up limit of the stacker moving vertically',
      stacker_lift_down_limit_sensor:
        'Down limit of the stacker moving vertically',
      stacker_lift_home_sensor: 'H-Posi of the stakcer moving vertically',
      pl_move_f_limit_sensor:
        'Front limit of bayobolt platform moving horizontally',
      pl_move_r_limit_sensor:
        'Back limit of bayobolt platform moving horizontally',
      pl_move_home_sensor: 'H-Posi of bayobolt platform moving horizontally',
      lr_lift_up_limit_sensor:
        'Up limit of bayobolt platform moving vertically',
      lr_lift_down_limit_sensor:
        'Down limit of bayobolt platform moving vertically',
      lr_lift_home_sensor: 'H-Posi of bayobolt platform moving vertically',
      vehical_f_up_limit_sensor: 'Up limit of left vehicle lift',
      vehical_f_down_limit_sensor: 'Down limit of left vehicle lift',
      vehical_f_home_sensor: 'H-Posi of left vehicle lift',
      vehical_r_up_limit_sensor: 'Up limit of right vehicle lift',
      vehical_r_down_limit_sensor: 'Down limit of right vehicle lift',
      vehical_r_home_sensor: 'H-Posi of right vehicle lift',
      bc_lift_up_limit_sensor: 'Up limit of lift cabinet',
      bc_lift_down_limit_sensor: 'Down limit of lift cabinet',
      bc_lift_home_sensor: 'H-Posi of lift cabinet',
      bc_lift_safe_sensor: 'Trans-posi lift battery safety',
      left_buffer_safe_sensor: 'Left buffer battry safety',
      right_buffer_safe_sensor: 'Right buffer battery safety',
      bc_slot22_reached_sensor:
        'Battery in the fire protection tank reaches position.',
      bc_lift_exist_sensor: 'Battery test at Trans-posi',
      RGV_bc_reach_sensor_07: 'Battery leveling 7',
      RGV_bc_reach_sensor_08: 'Battery leveling 8',
      liq_lift_zero_sensor: 'H-Posi of hydraulic lift',
    },
    4: {
      front_left_pressure_transducer: 'Left front pressure sensor',
      right_rear_pressure_transducer: 'Right rear pressure sensor',
      bc_slot13_lc_retract_sensor_1: 'A1 NPA water plug retracted',
      bc_slot13_lc_retract_sensor_2:	'A1 NPD water plug retracted',
      bc_slot13_ec_retract_sensor_1:	'A1 NPA electric plug retracted',
      bc_slot13_liq_flow_switch_st:	'A1 water flow switch',
      bc_slot14_lc_retract_sensor_1:	'A2 NPA water plug retracted',
      bc_slot14_lc_retract_sensor_2:	'A2 NPD water plug retracted',
      bc_slot14_ec_retract_sensor_1:	'A2 NPA electric plug retracted',
      bc_slot14_liq_flow_switch_st:	'A2 water flow switch',
      bc_slot15_lc_retract_sensor_1:	'A3 NPA water plug retracted',
      bc_slot15_lc_retract_sensor_2:	'A3 NPD water plug retracted',
      bc_slot15_ec_retract_sensor_1:	'A3 NPA electric plug retracted',
      bc_slot15_liq_flow_switch_st:	'A3 water flow switch',
      bc_slot16_lc_retract_sensor_1:	'A4 NPA water plug retracted',
      bc_slot16_lc_retract_sensor_2:	'A4 NPD water plug retracted',
      bc_slot16_ec_retract_sensor_1:	'A4 NPA electric plug retracted',
      bc_slot16_liq_flow_switch_st:	'A4 water flow switch',
      bc_slot17_lc_retract_sensor_1:	'A5 NPA water plug retracted',
      bc_slot17_lc_retract_sensor_2:	'A5 NPD water plug retracted',
      bc_slot17_ec_retract_sensor_1:	'A5 NPA electric plug retracted',
      bc_slot17_liq_flow_switch_st:	'A5 water flow switch',
      bc_fire_push_extend_sensor_1:	'Firefighting warehouse front push rod extended',
      bc_fire_push_retract_sensor_1:	'Firefighting warehouse front push rod retracted',
      fire_liq_check:	'Firefighting warehouse liquid level detection',
      bc_slot24_reached_sensor:	'Firefighting warehouse battery in place',
      bcslot13_17_pressure_switch_st:	'A1-A5 Water Pressure Switch',
      bc_slot13_ec_retract_sensor_2:	'A1 NPD electric plug retracted',
      bc_slot13_reached_sensor:	'A1 battery in place',
      bc_slot14_ec_retract_sensor_2:	'A2 NPD electric plug retracted',
      bc_slot14_reached_sensor:	'A2 battery in place',
      bc_slot15_ec_retract_sensor_2:	'A3 NPD electric plug retracted',
      bc_slot15_reached_sensor:	'A3 battery in place',
      bc_slot16_ec_retract_sensor_2:	'A4 NPD electric plug retracted',
      bc_slot16_reached_sensor:	'A4 battery in place',
      bc_slot17_ec_retract_sensor_2:	'A5 NPD electric plug retracted',
      bc_slot17_reached_sensor:	'A5 battery in place',
      bc_slot13_smoke_sensor:	'A1 smoke alarm',
      bc_slot14_smoke_sensor:	'A2 smoke alarm',
      bc_slot15_smoke_sensor:	'A3 smoke alarm',
      bc_slot16_smoke_sensor:	'A4 smoke alarm',
      bc_slot17_smoke_sensor:	'A5 smoke alarm',
      bc_slot18_lc_retract_sensor_1:	'A6 NPA water plug retracted',
      bc_slot18_lc_retract_sensor_2:	'A6 NPD water plug retracted',
      bc_slot18_ec_retract_sensor_1:	'A6 NPA electric plug retracted',
      bc_slot18_liq_flow_switch_st:	'A6 water flow switch',
      bc_slot19_lc_retract_sensor_1:	'A7 NPA water plug retracted',
      bc_slot19_lc_retract_sensor_2:	'A7 NPD water plug retracted',
      bc_slot19_ec_retract_sensor_1:	'A7 NPA electric plug retracted',
      bc_slot19_liq_flow_switch_st:	'A7 water flow switch',
      bc_slot20_lc_retract_sensor_1:	'A8 NPA water plug retracted',
      bc_slot20_lc_retract_sensor_2:	'A8 NPD water plug retracted',
      bc_slot20_ec_retract_sensor_1:	'A8 NPA electric plug retracted',
      bc_slot20_liq_flow_switch_st:	'A8 water flow switch',
      bc_slot21_lc_retract_sensor_1:	'A9 NPA water plug retracted',
      bc_slot21_lc_retract_sensor_2:	'A9 NPD water plug retracted',
      bc_slot21_ec_retract_sensor_1:	'A9 NPA electric plug retracted',
      bc_slot21_liq_flow_switch_st:	'A9 water flow switch',
      bc_slot22_lc_retract_sensor_1:	'A10 NPA water plug retracted',
      bc_slot22_lc_retract_sensor_2:	'A10 NPD water plug retracted',
      bc_slot22_ec_retract_sensor_1:	'A10 NPA electric plug retracted',
      bc_slot22_liq_flow_switch_st:	'A10 water flow switch',
      bc_slot23_ec_retract_sensor_1:	'A11 NPA electric plug retracted',
      bc_fire_push_extend_sensor_2:	'Firefighting warehouse rear push rod extended',
      bc_fire_push_retract_sensor_2:	'Firefighting warehouse rear push rod retracted',
      bcslot18_22_pressure_switch_st:	'A6-A10 Water Pressure Switch',
      bc_slot18_ec_retract_sensor_2:	'A6 NPD electric plug retracted',
      bc_slot18_reached_sensor:	'A6 battery in place',
      bc_slot19_ec_retract_sensor_2:	'A7 NPD electric plug retracted',
      bc_slot19_reached_sensor:	'A7 battery in place',
      bc_slot20_ec_retract_sensor_2:	'A8 NPD electric plug retracted',
      bc_slot20_reached_sensor:	'A8 battery in place',
      bc_slot18_smoke_sensor:	'A6 smoke alarm',
      bc_slot19_smoke_sensor:	'A7 smoke alarm',
      bc_slot20_smoke_sensor:	'A8 smoke alarm',
      bc_slot21_ec_retract_sensor_2:	'A9 NPD electric plug retracted',
      bc_slot21_reached_sensor:	'A9 battery in place',
      bc_slot22_ec_retract_sensor_2:	'A10 NPD electric plug retracted',
      bc_slot22_reached_sensor:	'A10 battery in place',
      bc_slot23_ec_retract_sensor_2:	'A11 NPD electric plug retracted',
      bc_slot23_reached_sensor:	'A11 battery in place',
      bc_slot21_smoke_sensor:	'A9 smoke alarm',
      bc_slot22_smoke_sensor:	'A10 smoke alarm',
      bc_slot23_smoke_sensor:	'A11 smoke alarm',
      bc_slot1_ec_retract_sensor_1:	'C1 NPA electric plug retracted',
      bc_slot1_reached_sensor:	'C1 battery in place',
      bc_slot2_ec_retract_sensor_1:	'C2 NPA electric plug retracted',
      bc_slot2_reached_sensor:	'C2 battery in place',
      bc_slot3_ec_retract_sensor_1:	'C3 NPA electric plug retracted',
      bc_slot3_reached_sensor:	'C3 battery in place',
      bc_slot4_ec_retract_sensor_1:	'C4 NPA electric plug retracted',
      bc_slot4_reached_sensor:	'C4 battery in place',
      bc_slot5_ec_retract_sensor_1:	'C5 NPA electric plug retracted',
      bc_slot5_reached_sensor:	'C5 battery in place',
      bc_slot6_ec_retract_sensor_1:	'C6 NPA electric plug retracted',
      bc_slot6_reached_sensor:	'C6 battery in place',
      bc_slot1_ec_retract_sensor_2:	'C1 NPD electric plug retracted',
      bc_slot2_ec_retract_sensor_2:	'C2 NPD electric plug retracted',
      bc_slot3_ec_retract_sensor_2:	'C3 NPD electric plug retracted',
      bc_slot4_ec_retract_sensor_2:	'C4 NPD electric plug retracted',
      bc_slot5_ec_retract_sensor_2:	'C5 NPD electric plug retracted',
      bc_slot6_ec_retract_sensor_2:	'C6 NPD electric plug retracted',
      bc_slot1_smoke_sensor:	'C1 smoke alarm',
      bc_slot2_smoke_sensor:	'C2 smoke alarm',
      bc_slot3_smoke_sensor:	'C3 smoke alarm',
      bc_slot4_smoke_sensor:	'C4 smoke alarm',
      bc_slot5_smoke_sensor:	'C5 smoke alarm',
      bc_slot6_smoke_sensor:	'C6 smoke alarm',
      bc_slot7_ec_retract_sensor_1:	'C7 NPA electric plug retracted',
      bc_slot7_reached_sensor:	'C7 battery in place',
      bc_slot8_ec_retract_sensor_1:	'C8 NPA electric plug retracted',
      bc_slot8_reached_sensor:	'C8 battery in place',
      bc_slot9_ec_retract_sensor_1:	'C9 NPA electric plug retracted',
      bc_slot9_reached_sensor:	'C9 battery in place',
      bc_slot10_ec_retract_sensor_1:	'C10 NPA electric plug retracted',
      bc_slot10_reached_sensor:	'C10 battery in place',
      bc_slot11_ec_retract_sensor_1:	'C11 NPA electric plug retracted',
      bc_slot11_reached_sensor:	'C11 battery in place',
      bc_slot12_ec_retract_sensor_1:	'C12 NPA electric plug retracted',
      bc_slot12_reached_sensor:	'C12 battery in place',
      bc_slot7_ec_retract_sensor_2:	'C7 NPD electric plug retracted',
      bc_slot8_ec_retract_sensor_2:	'C8 NPD electric plug retracted',
      bc_slot9_ec_retract_sensor_2:	'C9 NPD electric plug retracted',
      bc_slot10_ec_retract_sensor_2:	'C10 NPD electric plug retracted',
      bc_slot11_ec_retract_sensor_2:	'C11 NPD electric plug retracted',
      bc_slot12_ec_retract_sensor_2:	'C12 NPD electric plug retracted',
      bc_slot7_smoke_sensor:	'C7 smoke alarm',
      bc_slot8_smoke_sensor:	'C8 smoke alarm',
      bc_slot9_smoke_sensor:	'C9 smoke alarm',
      bc_slot10_smoke_sensor:	'C10 smoke alarm',
      bc_slot11_smoke_sensor:	'C11 smoke alarm',
      bc_slot12_smoke_sensor:	'C12 smoke alarm',
      RGV_bc_reach_sensor_01:	'Battery flattening 1',
      RGV_bc_reach_sensor_02:	'Battery flattening 2',
      RGV_bc_reach_sensor_04:	'Battery flattening 4',
      RGV_bc_reach_sensor_05:	'Battery flattening 5',
      RGV_bc_reach_sensor_07:	'Battery flattening 7',
      gun1_lift_home_sensor:	'1# gun lifts down in place',
      gun2_lift_home_sensor:	'2# gun lifts down in place',
      gun9_lift_home_sensor:	'9# gun lifts down in place',
      gun10_lift_home_sensor:	'10# gun lifts down in place',
      gun11_lift_home_sensor:	'11# gun lifts down in place',
      gun12_lift_home_sensor:	'12# gun lifts down in place',
      lr_pin_touch_sensor:	'Left rear body positioning pin contacts the body',
      lf_pin_retract_sensor:	'Left front body positioning pin lowers down in place',
      rr_pin_retract_sensor:	'Left rear body positioning pin lowers down in place',
      lr_pin_retract_sensor:	'Right rear body positioning pin lowers down in place',
      pl_stopper_01_work_sensor:	'RGV front stopper up in place',
      pl_stopper_01_home_sensor:	'RGV front stopper down in place',
      pl_stopper_02_work_sensor:	'RGV rear stopper up in place',
      pl_stopper_02_home_sensor:	'RGV rear stopper down in place',
      l_bat_pin_retract_sensor:	'RGV left battery positioning pin in original position',
      r_bat_pin_retract_sensor:	'RGV right battery positioning pin in original position',
      pl_stopper_01_reach_sensor:	'RGV front stopper, battery in place',
      pl_stopper_02_reach_sensor:	'RGV rear stopper, battery in place',
      pl_move_work_sensor_1:	'RGV translation plus unlocking position',
      pl_stopper_01_dece_sensor:	'RGV transfer deceleration',
      fork_left_extend_sensor_1:	'Left in-place of the fork arm 1',
      fork_left_extend_sensor_2:	'Left in-place of the fork arm 2',
      fork_retract_sensor_2:	'Midpoint of the auxiliary fork arm',
      stacker_move_f_sensor:	'Forward bin of the stacker crane',
      stacker_move_r_sensor:	'Rear bin of the stacker crane',
      stacker_move_RGV_sensor:	'Stacker crane traveling connection position',
      stacker_left_safe_sensor_1:	'Upper left overtravel of the fork',
      stacker_right_safe_sensor_1:	'Upper right overtravel of the fork',
      stacker_high_sensor_1:	'Stacker crane bin docking',
      fork_right_extend_sensor_1:	'Right in-place of the fork arm',
      stacker_bat_sensor_1:	'Stacker crane battery zone 1',
      stacker_bat_sensor_2:	'Stacker crane battery zone 2',
      stacker_bat_sensor_3:	'Stacker crane battery zone 3',
      pl_buffer_dece_sensor_2:	'Battery deceleration at the docking position',
      pl_buffer_sensor_f_2:	'Front battery in place at the docking position',
      buffer_stopper_01_extend_sensor_02:	'Front extension in place of the baffle at the docking position',
      buffer_stopper_01_retract_sensor_02:	'Front retraction in place of the baffle at the docking position',
      pl_buffer_sensor_r_2:	'Rear battery in place at the docking position',
      buffer_stopper_02_extend_sensor_02:	'Rear extension in place of the baffle at the docking position',
      buffer_stopper_02_retract_sensor_02:	'Rear retraction in place of the baffle at the docking position',
      pl_f_guide_home_sensor:	'Front guide bar origin',
      right_buffer_safe_sensor:	'Right RGV lifts the battery safely',
      liq_level_warning:	'Platform liquid level detection',
      pl_door_01_close_sensor:	'Left door closed in place',
      pl_door_02_close_sensor:	'Right door closed in place',
      pl_door_01_open_sensor:	'Left door opened in place',
      pl_door_02_open_sensor:	'Right door opened in place',
      maintain_area_safety_01:	'Feedback from the safety relay of the maintenance door',
      maintain_area_safety_02:	'Maintenance door sensor',
      left_buffer_safe_sensor:	'Left RGV lifts the battery safely',
      pl_r_guide_home_sensor:	'Rear guide bar origin',
      pl_buffer_dece_sensor_1:	'Battery deceleration at the left buffer position',
      pl_buffer_sensor_f_1:	'Front battery in place at the left buffer position',
      pl_buffer_sensor_r_1:	'Rear battery in place at the left buffer position',
      stacker_lift_up_limit_sensor:	'Upper limit position for the stacker lifting',
      stacker_lift_down_limit_sensor:	'Lower limit position for the stacker lifting',
      lr_lift_up_limit_sensor:	'Upper limit position for the lifting of the unlocking platform',
      lr_lift_home_sensor:	'Origin of the lifting of the unlocking platform',
      stacker_move_f_limit_sensor:	'Front limit position for the stacker travel',
      stacker_move_r_limit_sensor:	'Rear limit position for the stacker travel'
    },
    7: {
      bc_slot1_reached_sensor: 'C1 battery in place',
      bc_slot2_reached_sensor: 'C2 battery in place',
      bc_slot3_reached_sensor: 'C3 battery in place',
      bc_slot1_smoke_sensor: 'C1 smoke alarm',
      bc_slot2_smoke_sensor: 'C2 smoke alarm',
      bc_slot3_smoke_sensor: 'C3 smoke alarm',
      stacker_home_brake: 'Stacker home brake',
      bc_slot4_reached_sensor: 'C4 battery in place',
      bc_slot5_reached_sensor: 'C5 battery in place',
      bc_slot6_reached_sensor: 'C6 battery in place',
      bc_slot4_smoke_sensor: 'C4 smoke alarm',
      bc_slot5_smoke_sensor: 'C5 smoke alarm',
      bc_slot6_smoke_sensor: 'C6 smoke alarm',
      bc_slot10_lc_retract_sensor: 'A4 water plug retracted',
      bc_slot11_lc_retract_sensor: 'A5 water plug retracted',
      bc_slot12_lc_retract_sensor: 'A6 water plug retracted',
      bc_slot10_reached_sensor: 'A4 battery in place',
      bc_slot11_reached_sensor: 'A5 battery in place',
      bc_slot12_reached_sensor: 'A6 battery in place',
      bc_slot10_lc_cooling_swicth: 'A4 cooling switch',
      bc_slot11_lc_cooling_swicth: 'A5 cooling switch',
      bc_slot12_lc_cooling_swicth: 'A6 cooling switch',
      Fire_bunker_1_extend_sensor: 'Fire bunker 1 extended',
      Fire_bunker_1_retract_sensor: 'Fire bunker 1 retracted',
      bc_slot11_smoke_sensor: 'A5 smoke alarm',
      bc_slot12_smoke_sensor: 'A6 smoke alarm',
      bc_slot7_lc_retract_sensor: 'A1 water plug retracted',
      bc_slot8_lc_retract_sensor: 'A2 water plug retracted',
      bc_slot9_lc_retract_sensor: 'A3 water plug retracted',
      bc_slot7_reached_sensor: 'A1 battery in place',
      bc_slot8_reached_sensor: 'A2 battery in place',
      bc_slot9_reached_sensor: 'A3 battery in place',
      bc_slot7_lc_cooling_swicth: 'A1 cooling switch',
      bc_slot8_lc_cooling_swicth: 'A2 cooling switch',
      bc_slot9_lc_cooling_swicth: 'A3 cooling switch',
      bc_slot7_smoke_sensor: 'A1 smoke alarm',
      bc_slot8_smoke_sensor: 'A2 smoke alarm',
      bc_slot9_smoke_sensor: 'A3 smoke alarm',
      bc_slot10_smoke_sensor: 'A4 smoke alarm',
      baffle_1_extend_sensor: 'Front upper baffle extended',
      baffle_1_retract_sensor: 'Front upper baffle retracted',
      baffle_3_extend_sensor: 'Front lower baffle extended',
      baffle_3_retract_sensor: 'Front lower baffle retracted',
      baffle_up_exist_sensor_f: 'Front upper battery presence',
      baffle_down_exist_sensor_f: 'Front lower battery presence',
      bc_lift_home_sensor_1: 'Front lift home position',
      bc_lift_work_sensor_1: 'Front lift work position',
      platfrom_water_check_1: 'Platform front water check',
      platfrom_water_check_2: 'Platform rear water check',
      vehical_lift_lf_home_sensor: 'Left front lift home position',
      RGV_S_home_sensor: 'RGV S home position',
      vehical_lift_rf_home_sensor: 'Right front lift home position',
      S_l_pin_work_sensor: 'S left pin work position',
      vehical_lift_lr_home_sensor: 'Left rear lift home position',
      S_l_pin_home_sensor: 'S left pin home position',
      vehical_lift_lf_home_brake: 'Left front lift brake home',
      S_r_pin_work_sensor: 'S right pin work position',
      vehical_lift_rf_home_brake: 'Right front lift brake home',
      S_r_pin_home_sensor: 'S right pin home position',
      vehical_lift_lr_home_brake: 'Left rear lift brake home',
      vehical_lift_rr_home_brake: 'Right rear lift brake home',
      RGV_lift_home_sensor: 'RGV lift home position',
      RGV_move_work_sensor: 'RGV move work position',
      RGV_move_home_sensor: 'RGV move home position',
      RGV_reach_sensor_1: 'Left front battery leveling',
      RGV_reach_sensor_2: 'Right front battery leveling',
      RGV_reach_sensor_3: 'Left rear battery leveling',
      RGV_reach_sensor_4: 'Right rear battery leveling',
      lf_pin_home_sensor: 'Left front pin home position',
      lf_pin_touch_sensor: 'Left front pin touch',
      rr_pin_home_sensor: 'Right rear pin home position',
      rr_pin_touch_sensor: 'Right rear pin touch',
      bc_slot1_ec_retract_sensor: 'C1 electric plug retracted',
      bc_slot2_ec_retract_sensor: 'C2 electric plug retracted',
      bc_slot3_ec_retract_sensor: 'C3 electric plug retracted',
      bc_slot4_ec_retract_sensor: 'C4 electric plug retracted',
      bc_slot5_ec_retract_sensor: 'C5 electric plug retracted',
      bc_slot6_ec_retract_sensor: 'C6 electric plug retracted',
      door_open_sensor: 'Door open',
      door_close_sensor: 'Door close',
      vehical_lift_rr_home_sensor: 'Right rear lift home position',
      baffle_2_extend_sensor: 'Rear upper baffle extended',
      baffle_2_retract_sensor: 'Rear upper baffle retracted',
      baffle_4_extend_sensor: 'Rear lower baffle extended',
      baffle_4_retract_sensor: 'Rear lower baffle retracted',
      baffle_up_exist_sensor_r: 'Rear upper battery presence',
      baffle_down_exist_sensor_r: 'Rear lower battery presence',
      bc_lift_home_sensor_2: 'Rear lift home position',
      bc_lift_work_sensor_2: 'Rear lift work position',
      stacker_lift_1_sensor: 'Stacker lift level 1',
      stacker_lift_2_sensor: 'Stacker lift level 2',
      stacker_lift_3_sensor: 'Stacker lift level 3',
      stacker_lift_4_sensor: 'Stacker lift level 4',
      stacker_lift_5_sensor: 'Stacker lift level 5',
      stacker_lift_6_sensor: 'Stacker lift level 6',
      stacker_lift_home_sensor: 'Stacker lift home position',
      stacker_pin_1_sensor: 'Stacker pin 1',
      stacker_pin_2_sensor: 'Stacker pin 2',
      fork_work_sensor_1: 'Fork arm left in place 1',
      fork_work_sensor_3: 'Fork arm right in place',
      fork_home_sensor_1: 'Fork main arm midpoint',
      fork_home_sensor_2: 'Fork auxiliary arm midpoint',
      fork_exist_sensor_1: 'Fork battery presence 1',
      fork_exist_sensor_2: 'Fork battery presence 2',
      bc_safe_sensor_1: 'Fork upper left overtravel',
      bc_safe_sensor_2: 'Fork upper right overtravel',
      bc_slot7_ec_retract_sensor: 'A1 electric plug retracted',
      bc_slot8_ec_retract_sensor: 'A2 electric plug retracted',
      bc_slot9_ec_retract_sensor: 'A3 electric plug retracted',
      bc_fire_water_check_1: 'Fire water level check 1',
      bc_slot10_ec_retract_sensor: 'A4 electric plug retracted',
      bc_slot11_ec_retract_sensor: 'A5 electric plug retracted',
      bc_slot12_ec_retract_sensor: 'A6 electric plug retracted',
      Fire_bunker_2_extend_sensor: 'Fire bunker 2 extended',
      Fire_bunker_2_retract_sensor: 'Fire bunker 2 retracted',
      bc_slot13_reached_sensor: 'Fire bunker battery in place',
      bc_fire_water_check_2: 'Fire water level check 2',
      bc_fire_water_T_check: 'Fire water temperature check',
      pl_move_f_limit_sensor: 'RGV move front limit',
      pl_move_r_limit_sensor: 'RGV move rear limit',
      lr_lift_up_limit_sensor: 'RGV lift upper limit',
      lr_lift_down_limit_sensor: 'RGV lift lower limit',
      fork_X_left_limit_sensor: 'Stacker fork left limit',
      fork_X_right_limit_sensor: 'Stacker fork right limit',
      stacker_lift_up_limit_sensor: 'Stacker lift upper limit',
      stacker_lift_down_limit_sensor: 'Stacker lift lower limit',
      maintain_area_safety_01: 'Left maintenance door safety',
      maintain_area_safety_02: 'Right maintenance door safety',
      fire_door_check_1: 'Fire door check front',
      fire_door_check_2: 'Fire door check rear'
    }
  },

  diVarname: {
    all: 'All points',
    emergency_stop_switch_01: 'Emergency stop switch',
    liq_level_warning: 'Liquid level warning',
    I_power_st_1:
      'Power supply trip for the control box of the extended tank warning',
    I_power_st_2:
      'Power supply trip for the motor of the extended tank warning',
    I_power_st_3: 'Power supply trip for the stacker fork servo warning',
    I_power_st_4: 'Stacker walking servo power supply trip warning',
    I_power_st_5: 'Stacker lift servo power supply trip warning',
    I_power_st_6:
      'Brake switch power supply rear-end power distribution trip warning',
    I_power_st_7: 'Power supply trip of the front wheel clamp warning',
    I_power_st_8: 'Power supply trip of the rear wheel clamp warning',
    I_power_st_9: 'V-slot&guiding bar power supply trip warning',
    I_power_st_10: 'Vehicle position pin servo power supply trip warning',
    I_power_st_11: '1# Tightening gun-servo power supply trip warning',
    I_power_st_12: '2# Tightening gun-servo power supply trip warning',
    I_power_st_13: '3# & 4# Tightening gun-servo power supply trip warning',
    I_power_st_14: '5# & 6# Tightening gun-servo power supply trip warning',
    I_power_st_15: '7# & 8# Tightening gun-servo power supply trip warning',
    I_power_st_16: '9# Tightening gun-servo power supply trip warning',
    I_power_st_17: '10# Tightening gun-servo power supply trip warning',
    I_power_st_18: '11# Tightening gun-servo power supply trip warning',
    I_power_st_19: '12# Tightening gun-servo power supply trip warning',
    I_power_st_20: 'Sliding gate servo power supply trip warning',
    I_power_st_21: 'RGV servo power supply trip warning',
    I_power_st_22: 'Vehicle lift servo power supply trip warning',
    I_power_st_23: 'Trans-posi lift servo power supply trip warning',
    I_power_st_24: 'Frequency converter power supply tripping warning',
    A01_A1_check: 'A01A1 check',
    A01_A2_check: 'A01A2 check',
    A01_A03_check: 'A01A3 check',
    A01_A04_check: 'A01A4 check',
    A01_A05_check: 'A01A5 check',
    A01_A6_check: 'A01A6 check',
    A01_A7_check: 'A01A7 check',
    A01_A8_check: 'A01A8 check',
    A01_A9_check: 'A01A9 check',
    A01_A10_check: 'A01A10 check',
    A02_A1_module_status: 'A02A1 module status',
    A02_A2_module_status: 'A02A2 module status',
    A02_A3_module_status: 'A02A3 module status',
    A02_A4_module_status: 'A02A4 module status',
    A02_A5_module_status: 'A02A5 module status',
    A02_A06_module_check: 'A02A6 module status',
  },

  diVarnamePus4: {
    all: 'All points',
    emergency_stop_switch_01: 'Emergency stop switch',
    trans_fire_stopper_retract: 'One-click transfer of the firefighting warehouse water leakage',
    maintain_operate_swicth: 'Maintenance / operation mode',
    A01_A1_check: 'Detection of A01A1',
    I_power_st_1: 'Stacker lifting servo power supply trip alarm',
    I_power_st_2: 'Stacker fork & travel servo power supply trip alarm',
    I_power_st_3: 'Tightening gun 1 & 2 & 3 power supply trip alarm',
    I_power_st_4: 'Tightening gun 4 & 5 & 6 power supply trip alarm',
    I_power_st_5: 'Tightening gun 7 & 8 & 9 power supply trip alarm',
    I_power_st_6: 'Tightening gun 10 & 11 & 12 power supply trip alarm',
    I_power_st_7: 'Left & right opening/closing door, tightening gun 1 & 2 lifting, power supply trip alarm',
    I_power_st_8: 'Tightening gun 9 & 10 & 11 & 12 lifting, power supply trip alarm',
    I_power_st_9: 'Left front & rear battery locating pins, front & rear guide bars, power supply trip alarm',
    I_power_st_10: 'Left front/rear & right rear body locating pins, power supply trip alarm',
    I_power_st_11: 'Left & right front/rear wheel push rod servo, power supply trip alarm',
    I_power_st_12: 'RGV travel & RGV lift power supply trip alarm',
    I_power_st_13: 'Inverter power supply trip alarm',
    I_water_press_up: 'Upper limit of water flow pressure value',
    I_power_st_14: 'Fire pump power supply trip alarm',
    I_power_st_15: 'Fireproof heating cable power supply trip alarm',
    I_water_press_down: 'Lower limit of water flow pressure value',
    I_fire_pump_feedback: 'Fire pump control feedback',
    I_fire_heater_feedback: 'Fireproof heating cable control feedback',
    I_fire_D_door: 'Detection of fire door in Box D',
    I_fire_E_door: 'Detection of fire door in Box E',
    I_heater_temp_up: 'Upper limit of heating cable temperature detection',
    I_heater_temp_down: 'Lower limit of heating cable temperature detection',
    I_flow_switch_up: 'Upper limit of outlet flow switch',
    I_flow_switch_down: 'Lower limit of outlet flow switch'
  },

  diVarnameFirefly1: {
    all: 'All points',
    I_power_st_1: '1~3 tightening gun servo power trip alarm',
    I_power_st_2: '4~6 tightening gun servo power trip alarm',
    I_power_st_3: '7~8 tightening gun servo power trip alarm',
    I_power_st_4: 'Front/rear wheel push rod servo power trip alarm',
    I_power_st_5: 'Rear wheel push rod & door servo power trip alarm',
    I_power_st_6: 'RGV travel servo power trip alarm',
    I_power_st_7: 'RGV lift servo power trip alarm',
    I_power_st_8: 'Stacker fork servo power trip alarm',
    I_power_st_9: 'Stacker lift servo power trip alarm',
    I_power_st_10: 'Hydraulic pump motor power trip alarm',
    I_power_st_11: 'Heater rod motor power trip alarm',
    I_power_st_12: 'S value adjustment power trip alarm',
    I_power_st_13: 'Fire pump motor power trip alarm',
    I_power_st_14: 'Heating belt auxiliary heating power trip alarm',
    I_power_st_15: 'Smoke sensor power trip alarm',
    trans_fire_stopper_retract: 'One-click water drop button',
    emergency_stop_switch_01: 'Emergency stop feedback',
    battery_slot_area_check: 'Battery slot area safety feedback',
    stacker_chain_check_1: 'Stacker chain check front',
    stacker_chain_check_2: 'Stacker chain check rear',
    Maitaindoor_security_1: 'Maintenance door left sensor check',
    Maitaindoor_security_2: 'Maintenance door right sensor check',
    scanistor_di_err: 'Scanner alarm',
    fire_door_check_1: 'Fire emergency door sensor check front',
    fire_door_check_2: 'Fire emergency door sensor check rear',
    fire_spray_flow_nc_feedblack: 'Fire spray flow switch (NC) feedback',
    fire_spray_flow_no_feedblack: 'Fire spray flow switch (NO) feedback',
    fire_spray_T_limit_up_feedblack: 'Fire spray temperature switch upper limit feedback',
    fire_spray_T_limit_down_feedblack: 'Fire spray temperature switch lower limit feedback',
    fire_spray_P_limit_up_feedblack: 'Fire spray pressure switch upper limit feedback',
    fire_spray_P_limit_down_feedblack: 'Fire spray pressure switch lower limit feedback',
    fire_spray_power_F1_feedblack: 'Fire spray main power F1 breaker feedback',
    fire_spray_power_KM_feedblack: 'Fire spray pump power KM feedback',
    scanistor_safe_feedblack: 'Platform scanner safety feedback',
    carpet_safe_feedblack: 'Platform carpet safety feedback',
    battery_slot_area_KM_feedblack: 'Battery slot area servo power KM feedback',
    platfrom_area_KM_feedblack: 'Parking platform servo power KM feedback',
    maintain_operate_swicth: 'Operation mode switch',
    platfrom_door_safe_check_feedblack: 'Parking platform door safety feedback',
    hydraulic_cylinder_water_low_err: 'Hydraulic cylinder low water level alarm',
    hydraulic_cylinder_water_jam_err: 'Hydraulic cylinder water jam alarm',
    I_power_st_16: 'Fire tank & water tank auxiliary heating power trip alarm'
  },

  converter: {
    1: {},
    2: {
      '1': 'Roller frequency converter',
      '2': 'Lift frequency converter',
      '3': 'Left tank frequency converter',
      '4': 'Right tank frequency converter',
      '5': 'Trans-posi & buffer-posi frequency converter',
    },
    3: {
      '1': 'Trans-posi frequency converter',
      '2': 'Right buffer-posi frequency converter of the parking platform',
      '3': 'Roller frequency converter of the parking platform',
      '4': 'Left buffer-posi frequency converter of the parking platform',
    },
    4: {
      1: 'Connection position chain frequency converter',
      2: 'Parking platform drum frequency converter',
      3: 'Buffer position chain frequency converter'
    }
  },
};
