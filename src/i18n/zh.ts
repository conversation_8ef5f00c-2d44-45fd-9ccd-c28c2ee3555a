import * as map from './zh/index';

const common = {
  pp_remove_success: '取消收藏成功',
  pp_remove: '已取消收藏',
  pp_stars_success: '收藏成功',
  pp_stars: '已收藏',
  pp_edit_success: '编辑成功',
  pp_analysis_success: '分析成功',

  pp_unnamed_device: '未命名设备',
  pp_regional_company: '区域公司',
  pp_region: '区域',

  pp_time: '时间',
  pp_date: '日期',
  pp_time_frame: '时间范围',
  pp_select_time: '请选择时间',
  pp_please_select: '请选择',
  pp_please_input: '请输入',
  pp_start_time: '开始时间',
  pp_end_time: '结束时间',
  pp_to: '至',

  pp_empty: '暂无数据',
  pp_no_data: '暂无',
  pp_loading: '筛选中...',
  pp_downloading: '下载中...',

  pp_operation: '操作',
  pp_reset: '重置',
  pp_cancel: '取消',
  pp_confirm: '确认',
  pp_search: '筛选',
  pp_restore: '立即恢复',
  pp_edit: '编辑',
  pp_delete: '删除',
  pp_download: '下载',
  pp_download_data: '下载数据',
  pp_create: '创建',
  pp_recreate: '重新创建',
  pp_view_details: '查看详情',
  pp_logout: '退出登录',
  pp_view: '查看',
  pp_push: '下发',
  pp_revoke: '撤销',
  pp_close: '关闭',
  pp_filter: '搜索',

  pp_last_hour: '最近1小时',
  pp_last_24_hours: '最近24小时',
  pp_last_day: '最近1天',
  pp_last_7_days: '最近7天',
  pp_last_30_days: '最近30天',
  pp_last_week: '最近1周',
  pp_last_month: '最近1个月',
  pp_last_2_month: '最近2个月',
  pp_last_3_month: '最近3个月',
  pp_last_6_month: '最近6个月',
  pp_last_year: '最近1年',
  pp_today: '今天',
  pp_yesterday: '昨天',
  pp_week_ago: '一周前',

  pp_submit_success: '您已成功提交！',
  pp_confirm_submit: '确认提交',

  pp_yes: '是',
  pp_no: '否',
  pp_normal: '正常',
  pp_abnormal: '异常',

  pp_download_message: '即将开始下载，请勿重复点击！',
  pp_lack_permission: '抱歉！您没有权限访问该页面，请联系 junji.ma 或 liu.yang4 开通权限',

  pp_lack_image: '暂无图片',
  pp_copy_success: '已成功复制到剪贴板',
  pp_look_forward: '暂无数据, 尽情期待哦～',

  pp_start: '开始',
  pp_end: '结束',
  pp_back: '返回',
  pp_reminder: '提醒',
  pp_device: '设备',
  pp_device_id: '设备ID',
  pp_device_name: '设备名称',
  pp_resource_id: '虚拟设备ID',
  pp_jump: '跳转',
  pp_enter: '请输入设备ID或名称',
  pp_import: '批量导入',
  pp_path_change: '页面地址发生变化，已为您跳转到最新地址',
  pp_pss1: '一代站',
  pp_pss2: '二代站',
  pp_pss3: '三代站',
  pp_pss4: '四代站',
  pp_psc1: '120kW一体机1.0',
  pp_psc2: '二代超充桩',
  pp_psc3: '三代超充桩',
  pp_psc4: '四代超充桩',
  pp_status: '状态',
  pp_start_date: '开始日期',
  pp_end_date: '结束日期',
  pp_expand: '展开',
  pp_collapse: '收起',
  pp_minute: '分',
  pp_second: '秒',
  pp_vehicle_brand: '车辆品牌',
  pp_device_type: '设备类型',
  pp_order_id: '订单ID',
  pp_vehicle_platform: '车辆平台',
  pp_vehicle_id: '车辆ID',
  pp_order_start_time: '订单开始时间',
  pp_order_end_time: '订单结束时间',
  pp_detail: '详情',
  pp_yuan: '元',
  pp_ten_thousand: '万',
  pp_billion: '亿',
  pp_order_status: '订单状态',
  pp_urban_company: '城市公司',
  pp_company_division: '公司组别',
  pp_image_empty: '暂无图像数据',
  pp_set_up: '设置',
  pp_please_search: '请搜索',
  pp_prompt: '提示',
  pp_channel: '下单渠道',
  pp_vin: '车辆VIN',
  pp_service_id: '服务ID',
  pp_alarm_info: '告警信息',
  pp_remark: '备注',
  pp_device_select: '设备选择',
};

const menu = {
  pp_platform: '设备监测平台',
  pp_charge_pile: '超充桩',
  pp_swap_station1: '换电站 1.0',
  pp_swap_station2: '换电站 2.0',
  pp_swap_station3: '换电站 3.0',
  pp_swap_station4: '换电站 4.0',
  pp_cloud: '云边平台',
  pp_power_grid: '电网互动',
  pp_order_list: '指令列表',
  pp_cluster_list: '集群列表',
  pp_app_list: 'APP列表',
  pp_data_version: '数据版本',
  pp_version_management: '版本号管理',
  pp_temperature_feature: '枪温劣化特征',
  pp_data_fetch: '图像数据',
  pp_camera_management: '摄像头管理',
  pp_image_management: '图像管理',
  pp_battery_image: '电池图像下载',
  pp_faq: 'FAQ',
  pp_flow_chart: '算法流程图',
  pp_owl: 'OWL',
  pp_home: '首页',
  pp_data_analysis: '数据分析',
  pp_factory_management: '出厂管理',
  pp_user_management: '用户管理',
  pp_edge_management: '边缘管理',
  pp_pending_page: '待定页面',
  pp_supercharged_pile: '超充桩',
  pp_single_service_list: '单站服务列表',
  pp_single_image_list: '单站图片列表',
  pp_current_data: '实时数据',
  pp_history_data: '历史数据',
  pp_charge_module: '充电模块',
  pp_alarm_list: '告警管理',
  pp_alarm_analysis: '告警分析',
  pp_log_transfer: 'LOG按需调用',
  pp_warning_list: '告警列表',
  pp_operation_log: '操作日志',
  pp_log_export: '日志导出',
  pp_algorithm_visualization: '算法画像',

  pp_device_management: '设备管理',
  pp_device_detail: '设备详情',
  pp_service_list: '服务列表',
  pp_service_detail: '服务详情',

  pp_image_list: '图片列表',
  pp_log_list: '日志列表',

  pp_tank_trans_list: '倒仓列表',
  pp_tank_trans_detail: '倒仓详情',

  pp_order_image: '算法快照',

  pp_fault_list: '故障列表',
  pp_fault_diagnosis: '故障诊断',
  pp_snapshot_diagnosis: '快照诊断',
  pp_recording_diagnosis: '录播诊断',
  pp_remote_diagnosis: '远程诊断',
  pp_service_trace: '服务追溯',
  pp_battery_swap: '电池流转',
  pp_grid_info: '电网信息',
  pp_license_match: '车牌匹配',
  pp_log_analysis: '日志分析',
  pp_afrr_dashboard: '荷兰电网',
  pp_process_error: '进程错误',
  pp_fire_alarm: '消防告警',
  pp_diagnosis_info: '诊断信息',
  pp_parking_occupancy: '泊车占位',
  pp_stuck_analysis: '挂车分析',

  pp_device_simulation: '设备仿真',
  pp_run_list: '运行列表',
  pp_configuration_management: '配置管理',

  pp_device_health: '设备健康度',
  pp_health: '健康度总览',
  pp_overview: '页面总览',
  pp_detail: '查询明细',
  pp_config_list: '配置管理',
  pp_flow_monitoring: '流量监测',
  pp_info_trace: '信息追溯',
  pp_satisfaction_analysis: '满意度分析',
  pp_flash_log: '电池刷写',
  pp_bluetooth_disconnection: '蓝牙断连',
  pp_portrait_list: '换电画像',
  pp_station_management: '单站综合管理',
  pp_data_overview: '数据概览',
  pp_energy_list: '单站能效管理',
  pp_revenue_list: '单站收益管理',
  pp_firefly1: '萤火虫 1.0',
  pp_device_version: '设备版本管理',
  pp_charge_list: '充电画像',
  pp_bi_dashboard: '双向站看板',
};

const home = {
  pp_star_list: '收藏列表',
  pp_project: '设备类型',
  pp_empty_description: '这里还什么都没有呢～快去收藏设备吧',
};

const station = {
  pp_collect_the_device: '收藏该设备',
  pp_time_frame: '时间范围',
  pp_image_type: '图片类型',
  pp_image_name: '图片名称',
  pp_expand: '展开',
  pp_collapse: '收起',
  pp_select_image_type: '请选择图片类型',
  pp_select_image_status: '请选择图片状态',

  pp_service_start_time: '服务开始时间',
  pp_service_end_time: '服务结束时间',
  pp_service_duration: '服务时长',
  pp_service_battery_id: '服务电池ID',
  pp_input_service_battery_id: '请输入服务电池ID',
  pp_car_battery_id: '车辆电池ID',
  pp_input_car_battery_id: '请输入车辆电池ID',
  pp_car_id: '车辆ID',
  pp_input_car_id: '请输入车辆ID',

  pp_battery_id: '电池ID',
  pp_service_id: '服务ID',
  pp_order_id: '订单ID',

  pp_abnormal_image: '异常图片',
  pp_image_status: '图片状态',
  pp_all: '所有',
  pp_include: '有异常图片',
  pp_exclude: '无异常图片',
  pp_warning: '有无告警',

  pp_view_image: '查看图片',
  pp_view_normal_image: '查看正常图片',
  pp_view_abnormal_picture: '查看异常图片',
  pp_view_flash: '查看电池刷写',
  pp_create_ts: '图片生成时间',
  pp_image_size: '图片大小',
  pp_image_preview: '图片预览',
  pp_normal: '正常',
  pp_abnormal: '异常',
  pp_unknown: '未知',
  pp_unfinished: '未结束',
  pp_status_select: '状态选择',
  pp_stuck: '是否挂车',
  pp_flash_result: '刷写结果',
  pp_flash_success: '刷写成功',
  pp_flash_fail: '刷写失败',
  pp_flash_overdue: '命令超出有效期',
  pp_battery_capacity: '电池容量',
  pp_trigger_mode: '触发方式',
  pp_ui: 'UI触发',
  pp_select_battery: '选电池',
  pp_preselect_battery: '预选电池',
  pp_cloud_push: '云端下发',
  pp_aes_key: 'AES_KEY',
  pp_target_version: '目标版本',
  pp_status: '状态',
  pp_flash_start_time: '刷写开始时间',
  pp_flash_end_time: '刷写结束时间',
  pp_flash_battery_id: '刷写电池ID',
  pp_flash_battery_capacity: '刷写电池容量',
  pp_flash_target_version: '刷写目标版本',
  pp_battery_branch_ownership: '电池所属支路',
  pp_detail: '详情信息',
  pp_authentication_start: '鉴权开始时间',
  pp_cloud_specified_battery_id: '云端指定电池ID',
  pp_cloud_specified_battery_capacity: '云端指定电池容量',
  pp_request_id: '请求ID',
};

const deviceManagement = {
  pp_province: '省份',
  pp_city: '城市',
  pp_device: '设备',
  pp_device_id: '设备ID',
  pp_device_name: '设备名称',
  pp_name: '名称',

  pp_select_province: '请选择省份',
  pp_select_city: '请选择城市',
  pp_select_device_id: '请输入设备名称或ID',
  pp_device_select: '设备选择',
};

const serviceDetail = {
  pp_service_duration: '服务时长',
  pp_service_time_frame: '服务时间段',
  pp_service_battery_id: '服务电池ID',
  pp_car_identifier: '车辆识别码',
  pp_service_id: '服务ID',
  pp_car_battery_id: '车辆电池ID',
  pp_high_speed_recording: '高速录播',
  pp_sensor: '传感器',
  pp_converter: '变频器',
  pp_operation_log: '操作日志',
  pp_service_status: '服务状态',
  pp_alarm_list: '告警列表',
  pp_state_machine: '状态机',
  pp_standby_mode: '值守状态',
  pp_platform_side: '平台侧',
  pp_battery_side: '电池仓',
  pp_platform_step: '平台侧步骤号',
  pp_serial_number: '连续的序号',
  pp_battery_step: '电池仓步骤号',
  pp_axis_name: '轴名称',
  pp_data_point_name: '数据点名称',
  pp_full_csv_download: '全量CSV下载',

  pp_speed_empty: '上方进行搜索，以查看高速录播图表',
  pp_speed_archived: '高速录播数据已归档，点击',
  pp_speed_restoring: '高速录播数据恢复中，预计需要1-2分钟，\n刷新页面查看恢复结果',
  pp_sensor_empty: '上方进行搜索，以查看传感器图表',
  pp_converter_empty: '上方进行搜索，以查看变频器图表',
  pp_di: 'DI类',
  pp_di_empty: '上方进行搜索，以查看DI类图表',
  pp_operation_trigger_time: '操作触发时间',
  pp_operation_interface: '操作界面',
  pp_input_parameters: '输入参数',
  pp_return_data: '返回数据',
  pp_operation_description: '操作描述',
  pp_operation_people: '操作人',
  pp_state_automatic: '自动',
  pp_state_manual: '手动',
  pp_state_fault: '故障',
  pp_state_shutdown: '停机',
  pp_state_non_automatic: '非自动',
  pp_limit_warn: '时间范围不能超过一天',
  pp_log_local: '本地',
  pp_log_remote: '云端',
  pp_log_module: '模块',
  pp_log_time: '指令触发时间',
  pp_log_name: '指令名称',
  pp_log_status: '指令状态',
  pp_log_reason: '失败原因',
  pp_log_params: '查看参数',
  pp_log_success: '成功',
  pp_log_fail: '失败',
  pp_params_set: '指令参数',
  pp_params_code: '参数点',
  pp_params_value: '参数值',
  pp_unknown: '未知',
  pp_attended: '有人值守',
  pp_unattended: '无人值守',
  pp_abnormal_tip: '数据异常，包含映射表之外的值',
};

const userManagement = {
  pp_login_authority: '登陆权限',
  pp_user_name: '用户名',
  pp_domain_account: '域账号',
  pp_role: '角色',
  pp_enter_user_name: '请输入用户名',
  pp_not_space: '请勿输入空格',
  pp_enter_domain_account: '请输入域账号',
  pp_select_role: '请选择角色',
  pp_create_time: '创建时间',

  pp_batch_add: '批量添加',
  pp_add_user: '添加用户',

  pp_download_batch_template: '下载批量模板',
  pp_batch_add_user: '批量添加用户',
  pp_upload_normal_text: '将文件拖到此处, 或',
  pp_upload_light_text: '点击上传',
  pp_upload_normal_tip1: '请',
  pp_upload_normal_tip2: ', 填写用户信息后上传',
  pp_edit_user: '编辑用户',
  pp_delete_user: '删除用户',
  pp_delete_info: '确认删除用户',

  pp_specialist: '专员',
  pp_maintenance: '维护',
  pp_factory: '出厂',
  pp_development: '研发',
  pp_brown_dragon: '棕龙烧写',
  pp_brown_dragon_temporary: '棕龙烧写(临时)',
};

const logExport = {
  pp_view_wf: '查看我的申请',
  pp_log_access: '日志获取',
  pp_select_power_swap: '请选择设备',

  pp_file_path: '文件路径',
  pp_status: '状态',
  pp_request_time: '文件请求时间',

  pp_log_catalog: '日志目录',
  pp_select_log_catalog: '请选择日志目录',
  pp_update_time: '近次更新时间',
  pp_updating: '更新中',
  pp_update_catalog: '更新目录',
  pp_update_timeout: '更新超时',
  pp_done: '已上传',
  pp_transmitting: '生成中',
  pp_no_generated: '未生成',
  pp_failed: '传输失败',
  pp_return: '返回',
  pp_transfer_log_files: '传输日志文件',
  pp_confirm_one: '是否将日志文件',
  pp_confirm_two: '从设备端传输到天宫平台？',
  pp_confirm_three: '传输状态可在日志列表页查看，传输完成后可下载日志文件。',
  pp_confirm_four: '日志保存时间为',
  pp_confirm_five: '7天',
  pp_confirm_six: '，请及时下载。',

  pp_confirm: '确认传输',
  pp_confirm_button: '确认',
  pp_confirming: '下发中...',
  pp_tree_loading: '指令下发中...',
  pp_tooltip: '请确认设备是否在线, 若设备不在线, 无法获取文件目录。',
  pp_seaching: '筛选中',
  pp_batch_download: '批量下载',
  pp_batch_download_tooltip: '请先选择一个设备',
  pp_approval: '发起审批',
  pp_reason: '申请理由',
  pp_enter_reason: '请输入申请理由',

  pp_approve_success: '成功发起审批！',
  pp_approve_fail: '发起审批失败！',
  pp_file_name: '文件名',
  pp_md5_check: 'md5校验通过',
};

const historyData = {
  pp_history_data: '历史数据',
  pp_parameters: '参数点',
  pp_parameters_select: '请选择参数点',
  pp_over_parameters: '参数点大于5个，数据以表格形式展示',
  pp_over_time: '时间范围超过24小时，数据以表格形式展示',
  pp_over_hundred_parameters: '参数点不能超过100个',
  pp_selected_parameters: '已选参数：',
  pp_tip_text: '非数值类型数据点无法显示',
};

const edgeCloud = {
  pp_cloud: '云边平台',
  pp_cluster: '集群',
  pp_cluster_list: '集群列表',
  pp_app_list: 'APP列表',
  pp_app_detail: 'APP详情',
  pp_cluster_detail: '集群详情',
  pp_edge_resource: '边缘资源',
  pp_select_cluster: '请选择集群',
  pp_select_id: '请选择集群ID',
  pp_select_app: '请选择APP',
  pp_select_creator: '请选择创建人',
  pp_select_project: '请选择项目',
  pp_select_type: '请选择类型',
  pp_select_status: '请选择运行状态',
  pp_select_time: '请选择更新时间',
  pp_select_instance: '请选择实例',
  pp_select_subSystem: '请选择子系统',
  pp_select_image: '请选择容器镜像',
  pp_input_number: '请输入数字',
  pp_input_name: '请输入名称',
  pp_input_key: '请输入键',
  pp_input_value: '请输入值',
  pp_add_container: '请至少添加一个容器',
  pp_cluster_id: '集群ID',
  pp_node_id: '节点ID',
  pp_instance_id: '实例ID',
  pp_cluster_name: '集群名称',
  pp_node_number: '集群内节点数',
  pp_stop_schedule: '停止调度',
  pp_node_name: '节点名称',
  pp_sub_system: '所属子系统',
  pp_status: '状态',
  pp_cpu_usage: 'CPU用量',
  pp_memory_usage: '内存用量',
  pp_allocated_cpu: '已分配CPU',
  pp_cpu_reservation: 'CPU预留',
  pp_memory_reservation: '内存预留',
  pp_allocated_memory: '已分配内存',
  pp_app_name: 'APP名称',
  pp_creator: '创建人',
  pp_create_time: '创建时间',
  pp_belonging_project: '所属项目',
  pp_project: '项目',
  pp_running_status: '运行状态',
  pp_image: '镜像',
  pp_port_info: '端口',
  pp_port: '端口号',
  pp_port_name: '端口名称',
  pp_port_protocol: '端口协议',
  pp_instance: '实例名称',
  pp_update_time: '更新时间',
  pp_create_app: '创建APP',
  pp_edit_app: '编辑APP',
  pp_info: '基本信息',
  pp_name: '名称',
  pp_type: '类型',
  pp_name_limit: '名称只能包含字母、数字和短横线（-），必须以字母开头，最长 30 个字符',
  pp_container_set: '容器组设置',
  pp_container_name: '容器名称',
  pp_container_image: '容器镜像',
  pp_container_number: '容器组副本数量',
  pp_click_add: '点击添加容器',
  pp_number: '容器数量',
  pp_box_name: '容器名称',
  pp_memory_limit: '内存限制',
  pp_cpu_limit: 'CPU限制',
  pp_add_port: '添加端口',
  pp_upload: '点击或拖拽添加配置文件',
  pp_tag: '选择节点',
  pp_key: '键',
  pp_value: '值',
  pp_delete_tag: '删除标签',
  pp_specify_tag: '指定节点',
  pp_memory: '内存',
  pp_add_tag: '添加节点选择器',
  pp_delete_container: '确认删除容器组',
  pp_delete_app: '确认删除APP',
  pp_stop_success: '停止调度成功',
  pp_stop_error: '停止调度失败',
  pp_instance_detail: '实例详情',
  pp_resource_usage: '资源使用量',
  pp_resource_allocation: '资源分配量',
  pp_instance_status: '实例运行状态',
  pp_details: '详情',
  pp_event: '事件',
  pp_log: '日志',
  pp_restart: '重启',
  pp_sure_restart: '确认重启实例',
  pp_restart_success: '重启成功',
  pp_files: '查看配置文件',
  pp_configuration_file: '配置文件',
  pp_fill_port: '请填写完整的端口协议和端口号',
  pp_fill_key: '请填写完整的键值对',
};

const aiPortrait = {
  pp_yesterday_rate: '昨日算法通过率（%）',
  pp_click_column: '请点击柱子查看每个算法详情',
  pp_ftt: 'FTT算法',
  pp_non_ftt: '非FTT算法',
  pp_update_time: '更新时间',
  pp_backhaul: '数据回传量',
  pp_upload_sites: '图片上传站点数',
  pp_aec_effective: 'AEC有效调用率',
  pp_ai_rate: '算法正常运行率',
  pp_aec_return: 'AEC运行并有结果返回(包含异常运行)的订单量',
  pp_total_order: '昨日订单总量',
  pp_ai_return: 'AEC正确运行至算法输出有效结果的订单量',
  pp_vehicle_model: '车型',
  pp_battery_type: '电池类型',
  pp_all_day: '全天',
  pp_day: '白天',
  pp_night: '夜晚',
  pp_pass_rate: '算法通过率',
  pp_total: '运行单数',
  pp_trend: '通过率变化趋势',
  pp_sites: '站点',
  pp_last_week: '最近一周',
  pp_last_month: '最近一个月',
  pp_pass: '通过率',
  pp_top_10: '通过率最低TOP10站点',
  pp_block_sites: '屏蔽站点',
  pp_ftt_trend: '算法FTT趋势（%）',
  pp_target: '目标值',
  pp_theoretical: '理论值',
  pp_error: '请求出错啦, 请联系 liu.yang4',
  pp_cpu: 'CPU占用率（%）',
  pp_risk: '风险阈值',
  pp_last_ftt: '昨日TOP 10最差FTT站点',
  pp_order: '单',
  pp_open_sites: '打开站点（站点数：',
  pp_full_calculation: '全量计算（站点数：',
  pp_parameter_recipe: '参数配方',
};

const orderList = {
  pp_order_name: '指令名称',
  pp_order_pushtime: '下发时间',
  pp_order_status: '指令状态',
  pp_order_list: '指令列表',
  pp_power_grid: '电网互动',
  pp_battery_reservation: '电池预留',
  pp_battery_res_time: '预留时间',
  pp_battery_res_num: ' 预留数量',
  pp_order_push: '创建指令',
  pp_order_create_time: '创建时间',
  pp_order_finish_time: '完成时间',
  pp_order_user: '创建人',
  pp_enter_order_name: '请输入',
  pp_select_order_status: '请选择',
  pp_order_device: '下发设备',
  pp_ms_required: '请输入指令名称',
  pp_device_required: '请选择下发设备',
  pp_reserver_t_required: '请选择预留时间',
  pp_battery_required: '请选择电池型号',
  pp_max_capacity: '竞标上限容量',
  pp_price: '竞标价格',
  pp_battery_type: '电池型号',
  pp_max_capacity_required: '请输入竞标上限容量',
  pp_price_required: '请输入竞标价格',
  pp_stat_1: '已创建',
  pp_stat_2: '下发失败',
  pp_stat_3: '下发中',
  pp_stat_4: '下发成功',
  pp_stat_5: '撤销失败',
  pp_stat_6: '撤销中',
  pp_stat_7: '撤销成功',
  pp_res_t_start: '预留开始时间',
  pp_res_t_end: '预留结束时间',

  pp_push_confirm: '确认下发当前指令：',
  pp_revoke_confirm: '确认撤销当前指令：',

  pp_err_nullnum: '数量不能为空',

  pp_bid_order: '竞标指令',
  pp_reserve_order: '预留指令',
  pp_order_type: '指令类型',
  pp_piece: '块',
  pp_devices: '下发设备',
  pp_create_success: '创建成功',
  pp_create_failed: '创建失败',
  pp_edit_success: '编辑成功',
  pp_edit_failed: '编辑失败',
  pp_push_success: '开始下发',
  pp_push_failed: '下发失败',
  pp_revoke_success: '开始撤销',
  pp_revoke_failed: '撤销失败',
  pp_revoke_start: '撤销开始时间',
  pp_revoke_end: '撤销完成时间',
  pp_fail_reason: '失败原因',
  pp_read_only: '非指令创建人仅有查看权限',
};

const camera = {
  pp_camera: '摄像头',
  pp_sites: '站点',
  pp_status: '人工结果',
  pp_index: '序号',
  pp_station_name: '站点名称',
  pp_station_id: '站点ID',
  pp_shooting_time: '拍摄时间',
  pp_unverified: '未验证',
  pp_qualified: '合格',
  pp_unqualified: '不合格',
  pp_judgment_time: '人工判定时间',
  pp_judgement_result: '判定结果',
  pp_judge: '判定人',
  pp_operator: '操作人',
  pp_export: '一键导出',
  pp_image_preview: '图片预览',
  pp_imaging_qualified: '成像合格',
  pp_imaging_distortion: '成像歪斜',
  pp_with_text: '有文字',
  pp_imaging_blur: '成像模糊',
  pp_incorrect_resolution: '分辨率不对',
  pp_camera_form: '摄像头验收表格',
  pp_algorithm_results: '算法结果',
  pp_empty: '空',
  pp_pass: '合格',
  pp_fail: '不合格',
  pp_exception: '异常',
  pp_algorithmic_result: '算法结果',
  pp_algorithmic_status: '算法状态',
  pp_invalid: '失效',
  pp_normal: '正常',
  pp_restore: '批量恢复算法状态',
  pp_confirm: '算法是否已更新',
  pp_restore_success: '恢复成功',
  pp_pass_rate: '合格率',
  pp_pending_acceptance: '待验收',
  pp_dialog_title: '飞书通知负责人',
  pp_error_text: '存在摄像头未验证',
  pp_initiator_of_acceptance: '验收发起人',
  pp_third_party_personnel: '三方',
  pp_judgment_result: '判断结果',
  pp_acceptance_passed: '验收通过',
  pp_no_image: '没有照片',
  pp_remark: '备注',
  pp_send_success: '发送成功',
};

const chargeModule = {
  pp_power_swap: '换电',
  pp_charge: '充电',
  pp_predict: '预测',
  pp_staggered: '错峰',
  pp_order: '总订单',
  pp_nio_pss: '蔚来换电站',
  pp_charge_module: '充电模块',
  pp_total_power: '总功率',
  pp_remaining: '剩余',
  pp_total_module: '总模块（块）',
  pp_free: '空闲',
  pp_total_battery: '总电池（块）',
  pp_full: '满电',
  pp_a_full: 'A仓满电',
  pp_c_full: 'C仓满电',
  pp_index: '排序',
  pp_type: '类型',
  pp_order_time: '下单时间',
  pp_position: '位置',
  pp_module: '模块编号',
  pp_select_device: '请选择设备',
  pp_battery_empty: '暂无电池',
  pp_pile: '桩',
  pp_pile_empty: '暂无充电桩',
  pp_car: '车',
  pp_bms_request: 'BMS请求',
  pp_output: '实际输出',
  pp_module_empty: '暂无数据',
  pp_tooltip: '「将根据所选时间，往前追溯最近的数据，限制为1分钟，在1分钟内都没上传过的数据点将显示为空」',
};

const alarmList = {
  pp_alarm_list: '告警列表',
  pp_basic_alarm: '基础告警',
  pp_battery_alarm: '电池告警',
  pp_unknown_alarm: '未知告警',
  pp_first_level: '一级告警',
  pp_second_level: '二级告警',
  pp_third_level: '三级告警',
  pp_alarming: '告警产生',
  pp_cleared: '告警清除',
  pp_unknown: '未知状态',
  pp_alarm_time: '告警时间',
  pp_alarm_type: '告警类型',
  pp_alarm_level: '告警级别',
  pp_alarm_status: '告警状态',
  pp_alarm_description: '告警描述',
  pp_alarm_id: '告警ID',
  pp_device: '设备名称',
  pp_battery_id: '电池ID',
  pp_expand: '展开',
  pp_collapse: '收起',
  pp_create_time: '告警产生时间',
  pp_clear_time: '告警消除时间',
  pp_device_name: '设备名称',
  pp_device_id: '设备ID',
  pp_keywords: '请输入ID或描述进行搜索',
  pp_fault_id: '故障ID',
  pp_native_code: '底层故障码',
  pp_real_code: '真实故障码',
  pp_fault_name: '故障名称',
  pp_status: '告警状态',
  pp_type: '告警类型',
  pp_alarm_analysis: '告警分析',
  pp_alarm_download: '下载数据',
  pp_dialog_title: '选择告警分析条件',
  pp_device_method: '选择设备方式',
  pp_direct_selection: '直接选择',
  pp_csv_upload: 'csv导入',
  pp_device_select: '设备选择',
  pp_empty_text: '没有找到相应的内容，请选择分析条件',
  pp_condition_selection: '条件选择',
  pp_analysis_method: '选择分析方式',
  pp_based_on_time: '根据时间选择',
  pp_based_on_version: '根据版本选择',
  pp_time: '时间范围选择：',
  pp_time_tip: '分析结果将展示两个区间内告警发生数量的比值，为区间二/区间一',
  pp_interval_one: '区间一',
  pp_interval_two: '区间二',
  pp_create_analysis: '创建分析',
  pp_alarm_id_or_name: '告警ID/名称',
  pp_interval_1_count: '区间一发生次数',
  pp_interval_2_count: '区间二发生次数',
  pp_interval_divide: '区间二/区间一',
  pp_device_selected: '所选设备',
  pp_time_frame: '时间范围',
  pp_threshold: '发生次数阈值',
  pp_threshold_tip: '区间一、区间二的发生次数均需不小于该阈值',
  pp_in_service: '是否换电中',
  pp_form_tip: '只有换电过程中发生的告警才会显示对应车辆品牌/车辆平台',
};

const batteryHistory = {
  input_battery_id: '请输入电池ID',
  service_id: '服务单号',
  unnamed_station: '未命名站点',
  preview_image: '预览图片',
  download_image: '下载图片',
  full_download: '全量下载',
};

const serviceTrace = {
  pp_alarm_info: '告警信息',
  pp_lack_alarm: '暂无告警',
  pp_car_vin: '车辆ID',
  pp_validate: '电池ID、车辆ID至少填一个！',
  pp_abnormal_images: '是否有异常图片',
  service_end_normally: '服务是否正常结束',
  service_ended_normally: '服务正常结束',
  service_ended_abnormally: '服务异常结束',
  no_service_info: '服务结束状态未知',
  service_battery_id: '服务电池ID（换到车上）',
  car_battery_id: '车辆电池ID（换到站内）',
  swap_station_type: '换电站类型',
};

const gridInfo = {
  pp_device_list: '设备列表',
  pp_setting: '设置',
  pp_battery_slot: '电池仓信息',
  pp_reserve_75: '70kWh电池预留数',
  pp_reserve_100: '100kWh电池预留数',
  pp_mode: '站运行模式',
  pp_control_enable: '换电可控状态',
  pp_protocol_version: '通信协议版本',
  pp_power_limit: '场站容量限值',
  pp_charge_power_max: '最大可充功率',
  pp_charge_power_min: '最小需充功率',
  pp_charge_energy_max: '最大可充电量',
  pp_discharge_energy_max: '最大可放电量',
  pp_station_use_power: '站实时功率',
  pp_base_line_power: '站基线功率',
  pp_remote_distribute_power: '遥调功率',
  pp_min_power_coe: '降容系数',
  pp_bid_capactiy: '中标容量',
  pp_bid_price: '中标价格',
  pp_gird_frequency: '电网频率',
  pp_EPrice: '电价',
  pp_min_price: '最低价格',
  pp_max_price: '最高价格',
  pp_reserve: '预留',
  pp_request: '请求',
  pp_distribute: '分配',
  pp_actual: '实际',
  pp_limit: '限流',
  pp_selected_device: '当前所选设备',
  pp_message: '请至少选择一个设备',
  pp_last_hour: '最近1小时',
  pp_last_2_hours: '最近2小时',
  pp_last_6_hours: '最近6小时',
  pp_last_24_hours: '最近24小时',
  pp_off_peak: '错峰充电',
  pp_a_frr: '二次调频',
  pp_peak_shaving: '电网调峰',
  pp_normal_mode: '常规模式',
  pp_fcr_d: '一次调频',
  pp_vdb_mode: 'VDB模式',
  pp_standard_version: '标准版本',
  pp_huaneng_version: '电网互动',
  pp_battery_0: '50 50Ah',
  pp_battery_1: '70 102Ah',
  pp_battery_2: '84',
  pp_battery_3: '70 102Ah X',
  pp_battery_4: '84锁电',
  pp_battery_5: '70 LFP',
  pp_battery_6: '100 NCM',
  pp_battery_7: '100 锁电70',
  pp_battery_8: '75',
  pp_battery_9: '100 锁电84',
  pp_battery_10: '150',
  pp_battery_11: '100 锁电75',
  pp_battery_13: '100 B',
  pp_battery_14: '100 B锁75',
  pp_d_1_price: '1天前竞标价格',
  pp_d_2_price: '2天前竞标价格',
  pp_ideal_bid: '期望竞标容量',
  pp_actual_bid: '实际竞标容量',
  pp_ideal_revenue: '期望收益',
  pp_actual_revenue: '实际收益',
  pp_bid: '竞标容量',
  pp_revenue: '收益',
};

const batterySwap = {
  pp_slot: '流转仓位',
  pp_reason: '流转原因',
  pp_visualization: '可视化',
  pp_swap_time: '流转事件时间',
  pp_unknown: '未知',
  pp_battery_id: '电池ID',
  pp_device_id: '设备ID',
  pp_swap_method: '流转方式',
  pp_related_id: '关联ID',
  pp_service_id: '服务ID',
  pp_turnover_id: '倒仓ID',
  pp_car_id: '车辆ID',
  pp_manual: '手动',
  pp_authentication_swap: '鉴权换电',
  pp_non_authenticated: '非鉴权换电',
  pp_slot_turnover: '换电中倒仓',
  pp_slot_outside: '换电外倒仓',
  pp_transfer: '消防转运',
  pp_fall_water: '消防落水',
  pp_enter_slot: '入仓',
  pp_exit_slot: '出仓',
  pp_firefighting_slot: '消防仓',
  pp_tip: '目前只支持二、三代站',
  pp_swap_station2: '二代站',
  pp_swap_station3: '三代站',
};

const licenseMatch = {
  pp_ds_plateno: '数仓记录车牌',
  pp_lpr_plateno: '换电站检测车牌',
  pp_car_id: '车辆ID',
  pp_vin: 'VIN码',
  pp_equity_status: '权益状态',
  pp_equity_name: '权益名称',
  pp_purchase_method: '购车方式',
  pp_registration_time: '上牌时间',
  pp_service_id: '服务ID',
  pp_device_id: '设备ID',
  pp_device_name: '设备名称',
  pp_start_time: '换电开始时间',
  pp_end_time: '换电结束时间',
  pp_view_image: '查看图片',
  pp_full_download: '全量下载',
  pp_unavailable: '不可用',
  pp_available: '可用',
  pp_used: '已用',
  pp_expired: '已过期',
  pp_transferred: '已转移',
  pp_original_owner: '首任车主',
  pp_benefits_owner: '认证二手车',
  pp_no_benefits_owner: '非认证二手车',
};

const logAnalysis = {
  pp_create_title: '下发日志分析事件',
  pp_event_id: '事件ID',
  pp_no_log: '暂无log',
};

const processError = {
  pp_station_2: '换电站 2.0',
  pp_station_3: '换电站 3.0',
  pp_station_4: '换电站 4.0',
  pp_process_id: '进程ID',
  pp_process_name: '进程名称',
  pp_error_reason: '错误原因',
  pp_error_level: '错误等级',
  pp_collapse: '崩溃',
  pp_cpu_high: 'CPU占用过高',
  pp_memory_high: '内存占用过高',
  pp_level_one: '一级',
  pp_level_two: '二级',
  pp_level_three: '三级',
  pp_create_ts: '错误发生时间',
  pp_module: '模块',
};

const fireAlarm = {
  pp_create_ts: '告警发生时间',
  pp_upload_progress: '上传进度',
  pp_update_time: '更新时间',
  pp_finish: '已完成',
  pp_view_file: '查看文件',
  pp_view_report: '查看报告',
  pp_document_list: '消防告警文件列表',
  pp_alarm_time: '告警时间',
  pp_order: '序号',
  pp_document_name: '文件名称',
  pp_upload_time: '上传时间',
  pp_device_name: '设备名称',
  pp_not_uploaded: '未上传',
  pp_basic_info: '基本信息',
  pp_alarm_list: '消防相关告警（前后3分钟）',
  pp_no_fire_alarm: '非消防相关告警（前后3分钟）',
  pp_fire_detail: '告警详情',
  pp_transfer_process: '转运流程',
  pp_battery_cabin_info: '电池仓信息',
  pp_alarm_type: '告警类型',
  pp_alarm_level: '告警等级',
  pp_alarm_desc: '告警描述',
  pp_step_1: '系统启动转运',
  pp_step_2: '插头拔出失败',
  pp_step_3: '电池出仓到堆垛机完成',
  pp_step_4: '电池转运到消防仓对接高位完成',
  pp_step_5: '启动落水',
  pp_step_6: '落水完成',
  pp_battery_status_voltage: '电池充电状态/电池电压',
  pp_battery_temp_insulation: '电芯温度/绝缘值',
};

const diagnosisInfo = {
  pp_keywords: '请输入ID进行搜索',
  pp_time_frame: '时间范围',
  pp_diagnosis_level: '诊断等级',
  pp_diagnosis_type: '诊断类型',
  pp_timeout_command: '命令超时',
  pp_scheduled_time_not_start: '预约充电未启动',
  pp_fail_uploading_service: '上传服务小结失败',
  pp_start_without_s2_close: '启动时S2未闭合',
  pp_cp_status: 'CP信号状态异常',
  pp_text: '日志文本',
  pp_upload_time: '上传时间',
  pp_device_id: '设备ID',
  pp_upload_mode: '上传模式',
  pp_detail: '查看详情',
  pp_periodic_upload: '周期上传',
  pp_variable_upload: '变化上传',
  pp_diagnosis_detail: '诊断详情',
  pp_control_command: '控制命令',
  pp_command_time: '命令时间',
  pp_restart_count: '重启次数',
  pp_the_last_restart_time: '最后一次重启时间',
  pp_associated_order: '关联订单号',
  pp_diagnosis_result: '诊断结果',
  pp_reservation_mode: '是否处于预约模式',
  pp_authentication_success_time: '鉴权成功时间',
  pp_authentication_type: '鉴权类型',
  pp_successfully_authenticated: '是否成功鉴权',
  pp_start_service_time: '启动服务时间',
  pp_service_end_time: '服务结束时间',
  pp_output_PWM_signals: '是否输出PWM信号',
  pp_PWM_output_time: 'PWM输出时间',
  pp_vehicle_side_S2_closed: '车辆一侧是否闭合S2',
  pp_S2_close_time: 'S2闭合时间',
  pp_current_status_or_sample_value: 'CP当前状态或采样值',
  pp_auto_auth: '自动鉴权',
  pp_manual_auth: '手动鉴权',
  pp_none_auth: '无鉴权',
};

const parkingOccupancy = {
  pp_select_device: '请选择设备',
  pp_parking_alarm_time: '告警时间',
  pp_device_name: '设备名称',
  pp_device_id: '设备ID',
  pp_city_company: '区域公司',
  pp_view_image: '查看图片',
  pp_alarm_image_review: '告警图片查看',
  pp_alarm_list: '告警列表',
  pp_occupancy_status_dashboard: '占位情况看板',
  pp_occupancy_status: '占位情况',
  pp_order_concentration: '繁忙订单占比',
  pp_tip: '每天12点更新前一天数据',
  pp_total_order_num: '总订单',
  pp_cancel_order_num: '取消订单',
  pp_finish_order_num: '完成订单',
  pp_interrupt_num: '泊车中断次数',
  pp_voc_num: '当前场站影响用户泊车VOC数量',
};

const stuckAnalysis = {
  pp_dashboard_overview: '看板总览',
  pp_order_dimension: '订单维度',
  pp_alarm_detail: '告警详情',
  pp_alarm_list: '挂车告警列表',
  pp_powerswap_step: '换电步骤挂车率',
  pp_swap_station2: '二代站',
  pp_swap_station3: '三代站',
  pp_swap_station4: '四代站',
  pp_time: '时间范围',
  pp_average_stuck_rate: '平均挂车率',
  pp_stuck_order_quantity: '挂车订单数',
  pp_total_order_quantity: '总订单数',
  pp_selected_days: '共选择天数',
  pp_orders: '单',
  pp_days: '天',
  pp_line_empty: '暂无挂车率折线图数据',
  pp_stuck_alarm_ranking: '挂车告警排行',
  pp_alarm_name: '告警名称',
  pp_alarm_id: '告警ID',
  pp_alarm_type: '告警类型',
  pp_alarm_level: '告警级别',
  pp_alarm_status: '告警状态',
  pp_create_time: '告警产生时间',
  pp_clear_time: '告警消除时间',
  pp_stuck_times: '挂车次数',
  pp_total_times: '总次数',
  pp_alarm_rate: '挂车告警率',
  pp_device_stuck_ranking: '设备挂车率排行',
  pp_more: '更多',
  pp_stuck_orders: '挂车订单数',
  pp_total_orders: '总订单数',
  pp_device_id: '设备ID',
  pp_device_name: '设备名称',
  pp_device_type: '设备类型',
  pp_stuck_rate: '挂车率',
  pp_stuck_order: '挂车订单',
  pp_om_owner: '运维owner',
  pp_car_id: '车辆ID',
  pp_duty_status: '值守状态',
  pp_analysis_status: '分析状态',
  pp_analyst: '分析人',
  pp_unknown: '未知',
  pp_somebody: '有人',
  pp_nobody: '无人',
  pp_unanalyzed: '未分析',
  pp_analyzed: '已分析',
  pp_edit_analyst: '编辑分析人',
  pp_change_analyst: '更改分析人',
  pp_detail: '详情',
  pp_analysis: '分析',
  pp_order_detail: '挂车订单详情',
  pp_order_analysis: '挂车订单分析',
  pp_hardware_design: '相关硬件功能设计',
  pp_software_design: '相关软件功能设计',
  pp_station_name: '站名称',
  pp_station_id: '站ID',
  pp_software_version: '站软件版本',
  pp_car_departure_time: '车驶离时间',
  pp_car_brand: '车辆品牌',
  pp_car_model: '车型',
  pp_car_platform: '车平台',
  pp_duty_person: '值守专员',
  pp_device_manager: '设备负责人',
  pp_alarm_location: '告警部位',
  pp_alarm_step: '告警步骤',
  pp_current_alarm: '当前告警',
  pp_is_stuck: '是否挂车',
  pp_stuck_alarm: '挂车告警',
  pp_non_stuck_alarm: '非挂车告警',
  pp_alarm_clear: '告警清除',
  pp_is_aware: '是否已知',
  pp_associated_bug: '关联BUG',
  pp_associated_story: '关联需求',
  pp_input_result: '请输入分析结果',
  pp_select_aware: '请选择是否已知',
  pp_close_tip: '退出后填写内容都不会保存， 是否退出？',
  pp_prompt: '提示',
  pp_device_search: '设备名称',
  pp_alarm_search: '告警搜索',
  pp_order_search: '订单搜索',
  pp_alarm_tip: '请输入告警ID或名称',
  pp_service_id: '所属订单ID',
  pp_service_start_time: '所属订单开始时间',
  pp_service_end_time: '所属订单结束时间',
  pp_alarm_stuck: '告警挂车率',
  pp_software_owner: '软件owner',
  pp_hardware_owner: '硬件owner',
  pp_single_alarm_distribution: '单一告警分布',
  pp_alarm_times: '告警发生次数',
  pp_related_fault_orders: '相关故障单数',
  pp_alarm_empty: '暂无告警分布数据',
  pp_alarm_orders: '告警单量',
  pp_proportion: '总占比',
  pp_alarm_occurrence_proportion: '告警发生占比',
  pp_stuck_proportion: '挂车占比',
  pp_jira_placeholder: '请输入Jira名称或ID',
  pp_user_placeholder: '请输入域账户或中文名',
  pp_import_file: '导入文件',
  pp_download_template: '模板下载',
  pp_please_upload: '请上传',
  pp_csv: 'CSV文件',
  pp_exceed_100: '有效设备数量不能超过100个',
  pp_number_0: '有效设备数量为0',
  pp_import_success: '成功导入',
  pp_device_number: '个有效设备',
};

const deviceSimulation = {
  pp_tasks_name: '任务名称',
  pp_config_name: '配方名称',
  pp_config_id: '配方ID',
  pp_running_status: '运行状态',
  pp_creator: '创建人',
  pp_tasks_id: '任务ID',
  pp_search: '搜索',
  pp_clear: '清空',
  pp_create_simulation_task: '新建仿真任务',
  pp_create_simulation_step: '仿真步长',
  pp_remarks: '备注',
  pp_device_type: '设备类型',
  pp_status: '状态',
  pp_run_detail: '运行详情',
  pp_create_time: '创建时间',
  pp_update_time: '更新时间',
  pp_run: '运行中',
  pp_finished: '已完成',
  pp_not_start: '未开始',
  pp_stopped: '已停止',
  pp_failed: '运行失败',

  pp_enter_task: '请输入',
  pp_select_account: '请选择',
  pp_button_stop: '停止',

  pp_create_method: '新建方式',
  pp_by_existing_formulas: '通过已有配方',
  pp_by_real_data: '通过设备真实数据模拟',

  pp_station_capacity: '整站容量',
  pp_line1_capacity: '线1容量',
  pp_line2_capacity: '线2容量',
  pp_step_size: '仿真步长',
  pp_simulation_period: '仿真时段',
  pp_switch: 'EPS策略开关',
  pp_select_station: '换电站选择',
  pp_select_device: '设备选择',
  pp_import: '批量导入',
  pp_prev_step: '上一步',
  pp_next_step: '下一步',
  pp_cancel_create: '放弃创建',
  pp_seconds: '秒',
  pp_open: '开',
  pp_close: '关',

  pp_basic_settings: '选择配方',
  pp_real_basic_des: '基于一个或多个配方，开始仿真之旅吧',
  pp_basic_des: '通过已配置信息导入配方，依次仿真',
  pp_opti_config: '寻优配置',
  pp_opti_des: '通过开关控制，遍历参数，找出"最合适"的那一种',
  pp_finish_creating: '完成创建',
  pp_finish_des: '完成创建并开始仿真',

  pp_open_config: '是否开启寻优',
  pp_select_config: '选择寻优方式',
  pp_battery_function: '电池配比方式',
  pp_battery: '电池配比',
  pp_custom: '自定义配比',
  pp_enumeration: '枚举方式配比',
  pp_blcok: '块',
  pp_add: '新增电池配比',

  pp_task_name: '仿真任务名称',
  pp_remark: '填写备注',
  pp_finish: '完成创建并开始仿真',

  pp_cancel_giveup: '取消放弃',
  pp_confirm_giveup: '确认放弃',
  pp_prompt: '提示',
  pp_prompt_content: '放弃后当前内容都不会被保存，是否放弃？',
  pp_station_id: '站ID',

  pp_swap_station1: '换电站 1.0',
  pp_swap_station2: '换电站 2.0',
  pp_swap_station3: '换电站 3.0',
  pp_swap_station4: '换电站 4.0',

  pp_create: '已创建',
  pp_running: '正在运行',
  pp_run_success: '运行成功',
  pp_run_fail: '运行失败',

  pp_upload: '请上传',
  pp_csv: 'CSV文件',
  pp_import_file: '导入文件',
  pp_download: '模版下载',
  pp_device: '有效设备',
  pp_preview_enum: '枚举结果预览',
  pp_total: '共',
  pp_items: '项',
  pp_no: 'No.',
  pp_50: '50kWh/块',
  pp_75: '70kWh/块',
  pp_100: '100kWh/块',
  pp_150: '150kWh/块',
  pp_input: '请输入仿真任务名称',
  pp_detail: '仿真详情',

  pp_simulation_status: '当前仿真状态',
  pp_charging_time: '充电时长',
  pp_queue_time: '等待时长',
  pp_progress_rate: '容量利用率',
  pp_service_count: '换电订单量',
  pp_simulation_id: '配方ID',

  pp_stop_success: '停止成功',
  pp_stop_failed: '停止失败',

  pp_device_download: '设备类原始数据下载',
  pp_battery_download: '电池类原始数据下载',
  pp_order_download: '订单类原始数据下载',

  pp_date_prompt: '请选择时间',
  pp_date_rangeprompt: '请选择大于5分钟小于1天的时间',
  pp_total_prompt: '请输入整站容量',
  pp_totalcap_prompt: '请输入0-650范围内的数字',
  pp_capacity_prompt: '整站容量应大于线1容量',
  pp_step_prompt: '请输入0-120范围内的数字',
  pp_upload_device: '请选择设备',
  pp_over: '超过限制范围',
  pp_number: '请输入数字',
  pp_greater_zero: '请输入大于0的数字',
  pp_plus: '最大范围相加应小于电池仓数量',
  pp_min_max: '左侧应小于右侧',
  pp_smaller: '请输入少于电池仓数量的数字',
  pp_form_fail: '表单校验失败',
  pp_enum_detail: '平台根据各种电池类型的所选范围，生成距离参考配比最近的240个配比',
  pp_enum: '枚举方式',
  pp_enum_result: '预览枚举结果',
  pp_nan_download: '没有数据可以下载',
  pp_to: '至',
  pp_notin_list: '上传设备不在列表中',
  pp_create_success: '创建成功',
  pp_50_battery: '50kWh电池数量',
  pp_60_battery: '60kWh电池数量',
  pp_75_battery: '75kWh电池数量',
  pp_85_battery: '85kWh电池数量',
  pp_100_battery: '100kWh电池数量',
  pp_150_battery: '150kWh电池数量',
  pp_simulate_id: '仿真ID',
};

const deviceHealth = {
  pp_last_week: '一周前',
  pp_two_weeds: '两周前',
  pp_three_weeds: '三周前',
  pp_staion: '换电站类型',
  pp_date: '周级选择',
  pp_staion_health: '换电站健康度',
  pp_health: '健康度',
  pp_overall_health: '总体健康度',
  pp_FTT_health: 'FTT健康度',
  pp_sensor_health: '传感器健康度',
  pp_servo_health: '伺服健康度',
  pp_charge_health: '充电健康度',
  pp_on_duty: '有人值守',
  pp_duty: '无人值守',
  pp_duty_status: '值守状态',
  pp_day: '日期',
  pp_device_id: '设备ID',
  pp_device_name: '设备名称',
  pp_region: '区域公司',
  pp_downdload: '下载',
  pp_health_increase: '健康度环比上周',
  pp_health_increase_rate: '健康度环比上周增长率',
  pp_total_health: '总体健康度趋势',
  pp_update_date: '最新更新日期',
  pp_station: '站点数',
  pp_partof_health: '各部分健康度',
  pp_servo: '伺服',
  pp_charge: '充电模块',
  pp_sensor: '传感器',
  pp_increase: '环比上周',
  pp_rate: '环比上周增长率',
  pp_FTT_health_line: 'FTT健康度趋势',
  pp_servo_health_line: '伺服健康度趋势',
  pp_charge_health_line: '充电模块健康度趋势',
  pp_sensor_health_line: '传感器健康度趋势',
  pp_device_health: '设备健康度',
};

const configList = {
  pp_create: '新建配置',
  pp_config_name: '配置名称',
  pp_config_id: '配置ID',
  pp_creator: '创建人',
  pp_remark: '备注',
  pp_device_type: '设备类型',
  pp_create_time: '创建时间',
  pp_update_time: '更新时间',
  pp_create_method: '新建方式',
  pp_single_formula: '单配方',
  pp_batch_formula: '批量生成配方',
  pp_step1_title: '换电站配置',
  pp_step1_subtitle: '配置换电站的基本信息，比如整站容量等',
  pp_step2_title: '电池配置',
  pp_step2_subtitle: '配置电池仓内初始每块电池的信息',
  pp_step3_title: '订单配置',
  pp_step3_subtitle: '配置充换电用户序列，目前只支持换电用户配置',
  pp_step4_title: '配置总览',
  pp_step4_subtitle: '查看当前配置的所有信息',
  pp_base_real: '基于真实设备模拟',
  pp_time: '仿真时段',
  pp_select_time: '请选择仿真时段',
  pp_date_rangeprompt: '请选择大于5分钟且小于1天的时间',
  pp_tip: '仿真时长修改会影响其他数据的校验，请谨慎修改',
  pp_station_capacity: '整站容量',
  pp_circuit1_capacity: '线1容量',
  pp_circuit2_capacity: '线2容量',
  pp_enter_total: '请输入整站容量',
  pp_enter_line1: '请输入线1容量',
  pp_enter_line2: '请输入线2容量',
  pp_switch: 'EPS策略开关',
  pp_open: '开',
  pp_close: '关',
  pp_price_mode: '电价模式',
  pp_select_mode: '请选择电价模式',
  pp_one_price: '一口价',
  pp_time_of_use: '分时电价',
  pp_yuan: '元',
  pp_price_detail: '电价详情',
  pp_enter_price: '请输入电价',
  pp_enter_start: '请选择起始时间',
  pp_enter_end: '请选择结束时间',
  pp_device: '设备选择',
  pp_select_device: '请选择设备',
};

const flowMonitoring = {
  pp_time: '时间',
  pp_used_site: '流量用完站点',
  pp_upload_volume: '上传总量',
  pp_usage_rate: '使用率',
  pp_duration: '上榜天数',
  pp_total: '共',
  pp_day: '天',
};

const satisfaction = {
  pp_evaluation_time: '评价时间',
  pp_diagnostic_label: '诊断标签',
  pp_user_label: '用户标签',
  pp_device_select: '设备选择',
  pp_rating_level: '评分等级',
  pp_is_valid: '是否有效',
  pp_valid: '有效',
  pp_invalid: '无效',
  pp_all: '全部',
  pp_service_duration: '换电时长',
  pp_queue_duration: '排队时长',
  pp_score: '评分',
  pp_vehicle_id: '车辆ID',
  pp_vehicle_battery_id: '车辆电池ID',
  pp_detail: '详情',
  pp_diagnosis_card: '诊断卡片',
  pp_detail_info: '详细信息',
  pp_log: '日志',
  pp_diagnosis_result: '诊断结果',
  pp_long_battery_swap_time: '换电时间长',
  pp_user_start_battery_swap_time: '用户开始换电时间：',
  pp_user_end_battery_swap_time: '用户结束换电时间：',
  pp_swap_fail: '换电挂车/失败',
  pp_swap_fail_info1: '上报换电失败',
  pp_swap_fail_info2: '存在挂车告警',
  pp_view_more: '查看更多',
  pp_swap_queue_time: '换电排队时间长',
  pp_user_order_time: '用户下单时间：',
  pp_user_call_time: '用户叫号时间：',
  pp_preorder_time: '前序订单时间长',
  pp_preorder_fail: '前序订单挂车/失败',
  pp_multiple_orders: '多次下单',
  pp_current: '当前',
  pp_order_start_time: '订单开始时间',
  pp_order_end_time: '订单结束时间',
  pp_order_duration: '订单时长',
  pp_order_id: '订单ID',
  pp_service_id: '服务ID',
  pp_comment_id: '评价ID',
  pp_order_times: '该用户6小时内在该站下单次数：',
  pp_times: '次',
  pp_swap_normal: '是否正常换电',
  pp_swap_reversed: '是否反向换电',
  pp_swap_automated: '是否自助换电',
  pp_hidden1: '已隐藏其他',
  pp_hidden2: '个用户订单',
  pp_order_price: '订单价格',
  pp_equity_type: '权益类型',
  pp_billing_degree: '计费度数',
  pp_degree: '度',
  pp_old_battery_soc: '旧电池SOC',
  pp_new_battery_soc: '新电池SOC',
  pp_electricity_unit_price: '电费单价',
  pp_old_battery_degree: '旧电池度数',
  pp_new_battery_degree: '新电池度数',
  pp_user_perception: '用户感知信息',
  pp_service_information: '服务信息',
  pp_comment_information: '评价信息',
  pp_rating_stars: '评价星级',
  pp_comment_tag: '评价标签',
  pp_comment_text: '评价内容',
  pp_low_score_reason: '低分原因',
  pp_solution: '解决方案',
  pp_order_time: '下单时间',
  pp_car_owner: '本单车主',
  pp_other_user: '其他用户',
  pp_user: '用户',
  pp_order_list: '订单列表',
  pp_dashboard: '数据看板',
  pp_l3_label: 'L3-低分问题',
  pp_l2_label: 'L2-相关产品',
  pp_l1_label: 'L1-相关业务',
  pp_report_status: '是否在报告内采用',
  pp_adopt: '已采用',
  pp_unadopt: '不采用',
  pp_warn: '请选择是否在报告内采用',
  pp_chart1_title: '低分数量（L1-相关业务）',
  pp_chart2_title: '满意度损失（L1-相关业务）',
  pp_chart3_title: '低分数量（L2-相关产品）',
  pp_chart4_title: '低分均分（L2-相关产品）',
  pp_chart5_title: '满意度损失（L2-相关产品）',
  pp_label_detail: '定制标签',
  pp_time_error: '下载时间范围不能超过一周',
  pp_proportion: '占比',
};

const bluetooth = {
  pp_bluetooth_disconnection: '蓝牙断连',
  pp_is_service: '是否换电中',
  pp_average_daily: '单站日均发生次数',
  pp_related_alarm: '断连相关告警总次数',
  pp_alarm_in_service: '换电中发生次数',
  pp_total_times: '总发生次数',
  pp_device_rank: '设备断连告警排行',
  pp_times: '次',
};

const swapPortrait = {
  pp_device_type: '设备类型',
  pp_vehicle_platform: '车辆平台',
  pp_vehicle_machine: '车机全量版本',
  pp_software_version: '软件版本',
  pp_order_detail: '订单详情',
  pp_order_start_time: '订单开始时间',
  pp_order_end_time: '订单结束时间',
  pp_detail_info: '详细信息',
  pp_battery_info: '电池信息',
  pp_new_battery_id: '新电池ID',
  pp_old_battery_id: '旧电池ID',
  pp_experience_tag: '体验标签',
  pp_swap_reversed: '是否反向换电',
  pp_swap_automated: '是否自助换电',
  pp_on_duty: '是否有人值守',
  pp_multiple_orders: '是否多次下单',
  pp_details: '明细',
  pp_human_intervention: '是否人工介入',
  pp_order_time: '订单时长',
  pp_service_time: '服务时长',
  pp_dialog_title: '多次下单详情',
  pp_time_consuming: '耗时',
  pp_user: '用户',
  pp_mobile_app: '手机APP',
  pp_vehicle: '车辆',
  pp_automotive_cloud: '车企云',
  pp_power_cloud: '能源云',
  pp_pss: '换电站',
  pp_battery: '电池',
  pp_step_consuming: '该步骤共耗时',
  pp_run: '进行中',
  pp_cancel: '取消',
  pp_success: '成功',
  pp_fail: '失败',
  pp_unknown: '未知',
  pp_total_consuming: '总耗时',
  pp_stuck_status: '挂车状态',
  pp_not_stuck: '未挂车',
  pp_stuck: '挂车',
  pp_swap_step: '换电步骤',
  pp_second_alarm: '二级告警',
  pp_third_alarm: '三级告警',
  pp_duration: '历时',
  pp_tip: '跳转至服务详情使用高速录播、传感器和变频器',
  pp_process: '流程',
  pp_time: '时间',
  pp_alarm: '告警',
  pp_empty_text: '暂无流程数据',
  pp_jump_warn: '换电还没开始，缺少服务ID',
  pp_order: '订单',
  pp_vdp_diagnosis_result: '车端诊断标签',
};

const stationManagement = {
  pp_year_cumulative: '今年累计换电次数',
  pp_station: '换电站',
  pp_station_unit: '座',
  pp_daily_order: '及日均单量',
  pp_order: '单',
  pp_station_energy: '单站能效',
  pp_station_health: '单站健康度',
  pp_station_revenue: '单站经营收益',
  pp_yuan_day: '元/天',
  pp_single_revenue: '单站收益',
  pp_revenue_update: '收益更新',
  pp_energy_update: '能效更新',
  pp_energy_overview: '能效总览',
  pp_energy_detail: '能效明细',
  pp_energy_efficiency: '能效',
  pp_energy_distribution: '能效分布',
  pp_energy_target: '能效目标值',
  pp_energy_proportion: '不同单量下的能效占比',
  pp_tail_device: '尾部站点TOP',
  pp_energy_proportion1: '能效百分比',
  pp_order_volume: '订单数量',
  pp_total_consumption: '总耗能',
  pp_charge_consumption: '充电耗能',
  pp_water_consumption: '水冷耗能',
  pp_operation_consumption: '运营耗能',
  pp_mechanical_consumption: '机械耗能',
  pp_light_consumption: '灯光耗能',
  pp_ups_consumption: 'UPS耗能',
  pp_revenue_overview: '收益总览',
  pp_revenue_detail: '收益明细',
  pp_annualized_revenue: '年化收益情况',
  pp_expected_annualized: '预计年化收益',
  pp_device_daily_revenue: '单站日均收益',
  pp_progress: '达成进度',
  pp_update_date: '更新日期',
  pp_ytd_revenue: 'YTD收益（元）',
  pp_ytd_off_peak_revenue: 'YTD错峰收益（元）',
  pp_ytd_energy_revenue: 'YTD能效收益（元）',
  pp_battery_maintenance_revenue: 'YTD电池保养收益（元）',
  pp_revenue_of_interval: '所选区间收益情况',
  pp_revenue_of_interval1: '所选区间收益（元）',
  pp_off_peak_revenue: '错峰收益（元）',
  pp_energy_revenue: '能效收益（元）',
  pp_battery_revenue: '电池保养收益（元）',
  pp_revenue_trend: '收益变化趋势',
  pp_revenue: '收益',
  pp_off_peak: '错峰收益',
  pp_energy: '能效收益',
  pp_battery: '电池保养收益',
  pp_max_revenue_rate: '极限收益达成率',
  pp_total_revenue: '总收益',
  pp_battery_maintenance_times: '电池保养块数',
  pp_today: '当日',
  pp_non_charge_consumption: '非充电耗能',
  pp_station_suggestion: '站点建议',
  pp_energy_trend: '能效变化趋势',
  pp_no_data_from_yesterday: '无昨日数据',
  pp_diagram_explanation: '充电能耗图释',
  pp_state_grid: '国家电网',
  pp_transformer: '变压器',
  pp_consumption: '总耗能',
  pp_detail_station: '换电站',
  pp_module_output: '模块输出电量',
  pp_battery_input: '输入电池电量',
  pp_actual_charged: '实际充入电量',
  pp_pile_consumption: '充电桩总耗能',
  pp_ups: 'UPS用电',
  pp_circuit_consumption: '配电回路用电',
  pp_water_cooling: '水冷',
  pp_cooling_fan: '散热风机',
  pp_mechanical: '机械耗能',
  pp_operation: '运营耗能',
  pp_light: '照明',
  pp_power_module: '功率模块',
  pp_pile: '充电桩',
  pp_car_battery: '车辆电池',
  pp_station_battery: '站内电池',
  pp_breakdown: '充电能耗拆解：',
  pp_station_consumption: '换电站节约能耗',
};

const deviceVersion = {
  pp_version_dashboard: '版本分布看版',
  pp_version_list: '设备版本列表',
  pp_piece: '台',
  pp_device_activation: '激活状态',
  pp_link_status: '链接状态',
  pp_activation: '设备激活状态',
  pp_in_blacklist: '是否在黑名单',
  pp_add: '新增',
  pp_add_filter: '新增筛选项',
  pp_select_filter: '请选择筛选字段',
  pp_batch_editing: '批量编辑',
  pp_template_import: '模板导入',
  pp_selected: '已选',
  pp_issue_work_order: '下发工单',
  pp_add_to_blacklist: '加入黑名单',
  pp_remove_from_blacklist: '移出黑名单',
  pp_on_line: '在线',
  pp_off_line: '离线',
  pp_blacklist: '黑名单中',
  pp_out_blacklist: '黑名单外',
  pp_send_history: '发送历史',
  pp_download_the_full_table: '下载全表',
  pp_please_select_device: '请先选择设备',
  pp_issue_successful: '下发成功',
  pp_download_successful: '下载成功',
  pp_download_failed: '下载失败',
  pp_template_title: '导入模版并下发工单',
  pp_target_version: '期望升级的目标版本',
  pp_import_file: '导入文件',
  pp_download_template: '下载模版',
  pp_error_tip: '请选择期望升级的目标版本',
  pp_confirm_issuance: '确认下发',
  pp_target_dialog_tip1: '以下',
  pp_target_dialog_tip2: '座设备已在期望升级的目标版本内，将不会发送对应工单！',
  pp_order_dialog_tip1: '共选择',
  pp_order_dialog_tip2: '座设备，其中',
  pp_order_dialog_tip3: '座在黑名单内，确认后将对剩余',
  pp_order_dialog_tip4: '座设备下发升级工单！',
  pp_template_dialog_tip1: '共上传',
  pp_blacklist_dialog_tip1: '共选定',
  pp_blacklist_dialog_tip2: '座设备，确认批量加入黑名单？',
  pp_blacklist_dialog_tip3: '座设备，确认批量移出黑名单？',
  pp_blacklist_dialog_tip4: '确认将',
  pp_blacklist_dialog_tip5: '设备加入黑名单？',
  pp_blacklist_dialog_tip6: '确认将',
  pp_blacklist_dialog_tip7: '设备移出黑名单？',
  pp_blacklist_result_tip1: '已成功',
  pp_blacklist_result_tip2: '加入',
  pp_blacklist_result_tip3: '移出',
  pp_blacklist_result_tip4: '黑名单',
  pp_jump_tip: '跳转后请手动切至【软件对应处置工单】类目',
  pp_version_tab: '版本列表',
  pp_dashboard_tip: '个版本',
  pp_select_version: '请选择版本',
};

const chargeList = {
  pp_realtime_data: '实时数据',
  pp_stop_reason: '结束原因',
  pp_event_info: '事件信息',
};

const biDashboard = {
  pp_time_error: '时间范围不能超过31天',
  pp_subscribe: '订阅看板',
  pp_basic_info: '基本信息',
  pp_basic_info_tip: '（基本信息以日期选择的最晚时间为准）',
  pp_high_way: '高速',
  pp_device_basic_info: '设备基本信息',
  pp_no_pie_chart: '暂无饼图数据',
  pp_no_bar_chart: '暂无柱状图数据',
};

export default {
  menu,
  deviceManagement,
  station,
  home,
  serviceDetail,
  userManagement,
  logExport,
  common,
  historyData,
  edgeCloud,
  aiPortrait,
  orderList,
  camera,
  chargeModule,
  alarmList,
  batteryHistory,
  serviceTrace,
  gridInfo,
  batterySwap,
  licenseMatch,
  logAnalysis,
  processError,
  fireAlarm,
  diagnosisInfo,
  parkingOccupancy,
  stuckAnalysis,
  deviceSimulation,
  deviceHealth,
  configList,
  flowMonitoring,
  satisfaction,
  bluetooth,
  swapPortrait,
  stationManagement,
  deviceVersion,
  chargeList,
  biDashboard,
  ...map,
};
