import { App } from 'vue';
import { type I18n, createI18n } from 'vue-i18n';

// element-plus国际化
import enLocale from 'element-plus/lib/locale/lang/en';
import zhLocale from 'element-plus/lib/locale/lang/zh-cn';

// 引入语言包
import en from './en';
import zh from './zh';
import { getLocale, getUserId } from '~/utils';

// const locale = getLocale();
const locale: string = localStorage.getItem('locale')
  ? String(localStorage.getItem('locale'))
  : getLocale();

//注册i8n实例并引入语言文件
export const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  global: true,
  // locale: 'zh',
  locale: locale,
  fallbackLocale: 'en', // 默认语言
  messages: {
    zh: {
      ...zh,
      ...zhLocale,
    },
    en: {
      ...en,
      ...enLocale,
    },
  },
});

export function useI18n(app: App) {
  app.use(i18n);
}
