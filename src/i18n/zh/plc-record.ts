export const plcRecord = {
  otherAxis: '其余运动轴',
  noAxis: '暂无对应轴号',
  chartTitle: {
    high_speed: {
      speed: '速度（rpm）',
      position: '位置（mm）',
      torque: '扭矩（%）',
    },
    di: {
      value: 'DI类',
    },
    sensor: {
      value: '传感器',
    },
    converter: {
      err_code: '故障码',
      power: '功率',
      frequency: '频率',
      current: '电流',
    },
  },

  // 平台侧步骤号
  platformStep: {
    1: {
      '-1': '所有步骤',
      1: '1 - 初始化自检',
      2: '2 - 推杆定位，车身抬升到接触位',
      3: '3 - 停车平台视觉判断',
      4: '4 - 车身抬升到工作位',
      5: '5 - RGV到停车平台',
      6: '6 - 旋转平台到90度位置',
      7: '7 - RGV视觉判断',
      8: '8 - RGV抬升到工作位，电磁铁吸合',
      9: '9 - 解锁',
      10: '10 - RGV低速下降到水电插头位',
      11: '11 - RGV高速下降到卡销位',
      12: '12 - 旋转平台到0度位置，RGV下降到原位',
      13: '13 - RGV行走到电池仓',
      14: '14 - 新旧电池更换',
      15: '15 - RGV行走到停车平台',
      16: '16 - 旋转平台到90度位置，车身定位销伸出',
      17: '17 - RGV视觉判断',
      18: '18 - RGV抬升至销子位，车身定位销伸出',
      19: '19 - RGV抬升至工作位，车身定位销缩回',
      20: '20 - 加锁',
      21: '21 - RGV下降到中间位，车身定位销缩回',
      22: '22 - 旋转平台到0度位置',
      23: '23 - RGV行走到电池仓',
      24: '24 - 车身下降，四轮定位退回',
    },
    2: {
      '-1': '所有步骤',
      1: '1 - 车轮推杆夹紧，开合门打开',
      2: '2 - 车辆举升_车有电池',
      3: '3 - RGV举升_无电池',
      4: '4 - 解锁',
      5: '5 - RGV下降_有电池',
      6: '6 - 车辆下降_车无电池',
      7: '7 - 亏电池到缓冲位',
      8: '8 - 车辆举升_车无电池',
      9: '9 - RGV举升_有电池,定位销伸出+部分缩回',
      10: '10 - 加锁',
      11: '11 - RGV下降_无电池,车轮推杆缩回',
      12: '12 - 车辆下降_车有电池, 定位销缩回',
      13: '13 - 亏电池从缓冲位出来，开合门关闭',
      14: '14 - 开合门关闭',
    },
    3: {
      '-1': '所有步骤',
      1: '1 - 推杆一次定位',
      2: '2 - 推杆二次定位&开合门打开',
      3: '3 - RGV举升至工作位',
      4: '4 - 电磁铁吸合&解锁',
      5: '5 - 电磁铁释放&RGV下降',
      6: '6 - 开合门关闭&RGV平移至电池对接位',
      7: '7 - 前后导向条伸出',
      8: '8 - 电池从停车平台流转至左缓存位',
      9: '9 - 接驳机下降零点位&RGV挡块伸出',
      10: '10 - 电池从接驳机流转至停车平台',
      11: '11 - 前后导向条缩回&开合门打开',
      12: '12 - RGV平移至加解锁位',
      13: '13 - RGV举升至卡销位',
      14: '14 - RGV举升至销子位&车身定位销伸出&平台阻挡块缩回',
      15: '15 - RGV举升至工作位&车身定位销缩回',
      16: '16 - 电磁铁吸合&加锁',
      17: '17 - 电磁铁释放&RGV下降至原点位&车身定位销缩回',
      18: '18 - 推杆至零点位&开合门关闭&RGV平移至电池流转位',
    },
    4: {
      '-1': '所有步骤',
      1: '1 - 推杆一次定位',
      2: '2 - 推杆二次定位&开合门打开',
      3: '3 - RGV举升至工作位',
      4: '4 - 电磁铁吸合&解锁',
      5: '5 - 电磁铁释放&RGV下降',
      6: '6 - 开合门关闭&RGV平移至电池对接位',
      7: '7 - 前后导向条伸出',
      8: '8 - 电池从停车平台流转至左缓存位',
      10: '10 - 电池从接驳位流转至停车平台',
      11: '11 - 前后导向条缩回&开合门打开',
      12: '12 - RGV平移至加解锁位',
      13: '13 - RGV举升至卡销位',
      14: '14 - RGV举升至销子位&车身定位销伸出&平台阻挡块缩回',
      15: '15 - RGV举升至工作位&车身定位销缩回',
      16: '16 - 电磁铁吸合&加锁',
      17: '17 - 电磁铁释放&RGV下降至原点位&车身定位销缩回',
      18: '18 - 推杆至零点位&开合门关闭&RGV平移至电池流转位'
    },
    7: {
      '-1': '所有步骤',
      1: '1 - 四轮推杆至工作位',
      2: '2 - 翻转门打开',
      3: '3 - 车辆举升至工作位',
      4: '4 - RGV空载行走到停车平台',
      5: '5 - 车身定位销伸出&RGV空载举升至销子位',
      6: '6 - 电磁铁释放，RGV举升至工作&车身定位销同步缩回',
      7: '7 - 电磁铁吸合&解锁',
      8: '8 - 解锁完成RGV下降至插头位',
      9: '9 - RGV下降至零点位',
      10: '10 - 电磁铁吸合&RGV行走至接驳位',
      11: '11 - 接驳机上层挡块伸出',
      12: '12 - 接驳机举升至工作位',
      13: '13 - 接驳机下层挡块伸出',
      14: '14 - 货叉满电电池放到接驳机',
      15: '15 - 接驳机举升至中间位',
      16: '16 - 接驳机下层挡块缩回',
      17: '17 - 电磁铁吸合&RGV带载行走至停车平台',
      18: '18 - 车身定位销伸出&RGV带载举升至销子位',
      19: '19 - 电磁铁释放&RGV举升至工作位&车身定位销同步缩回',
      20: '20 - 电磁铁吸合&车身定位销缩回',
      21: '21 - 加锁',
      22: '22 - RGV下降至销子位',
      23: '23 - RGV下降至零点位',
      24: '24 - 货叉取亏电电池完成',
      25: '25 - 电磁铁吸合&RGV空载行走至接驳位',
      26: '26 - 电磁铁释放&四轮推杆至零点&车辆举升下降至零点&翻转门关闭'
    }
  },

  // 电池仓步骤号
  batteryStep: {
    1: {
      '-1': '所有步骤',
      1: '1 - 初始化',
      2: '2 - 升降机空载升目标仓位',
      3: '3 - 目标仓电池出仓',
      4: '4 - 升降机带载降6仓位',
      5: '5 - 电池进6仓',
      6: '6 - 升降机升至对接位',
      7: '7 - 电池传送至升降机',
      8: '8 - 升降机带载升目标仓位',
      9: '9 - 电池进目标仓位',
      10: '10 - 升降机空载降6仓位',
      11: '11 - 6仓电池出仓',
      12: '12 - 升降机升至对接位',
      13: '13 - 电池转移到RGV',
    },
    2: {
      '-1': '所有步骤',
      1: '1 - 升降机空载升到目标仓高度',
      2: '2 - 升降机带载降到对接位高度',
      // 3: '3 - 暂无 (不显示)',
      4: '4 - 服务电池出升降机到平台',
      5: '5 - 升降机带载升到目标仓高度',
      6: '6 - 升降机空载降到对接位高度',
    },
    3: {
      '-1': '所有步骤',
      1: '1 - 水电插头缩回&堆垛机至目标仓对接位&接驳机举升',
      2: '2 - 货叉目标仓伸出&堆垛机升至高位&货叉缩回',
      3: '3 - 堆垛机至接驳机对接位',
      4: '4 - 货叉接驳机伸出&接驳机升至工作位&货叉缩回',
      5: '5 - 接驳机降至原点位&平台挡块缩回',
      6: '6 - 前后导向条伸出&电池从左缓存位至接驳机',
      7: '7 - 接驳机举升至工作位',
      8: '8 - 货叉接驳机伸出&接驳机至原点位&货叉缩回',
      9: '9 - 堆垛机至目标仓&货叉高位伸出&堆垛机至低位&货叉缩回',
      10: '10 - 水电插头伸出',
      11: '11 - 堆垛机从目标仓至初始位',
    },
    4: {
      '-1': '所有步骤',
      1: '1 - 水电插头缩回&堆垛机至目标仓对接位',
      2: '2 - 货叉目标仓伸出&堆垛机升至高位&货叉缩回',
      3: '3 - 堆垛机至接驳位',
      4: '4 - 货叉伸出&货叉下降&货叉缩回',
      5: '5 - 平台挡块缩回',
      6: '6 - 前后导向条伸出&电池从缓存位至接驳位',
      8: '8 - 货叉伸出&货叉抬升&货叉缩回',
      9: '9 - 堆垛机至目标仓&货叉高位伸出&堆垛机至低位&货叉缩回',
      10: '10 - 水电插头伸出',
      11: '11 - 堆垛机从目标仓至初始位'
    },
    7: {
      '-1': '所有步骤',
      1: '1 - 水电插头缩回&堆垛机升降至目标仓低位',
      2: '2 - 货叉在目标仓伸出',
      3: '3 - 堆垛机升降至目标仓高位',
      4: '4 - 货叉在目标仓高位缩回',
      5: '5 - 堆垛机升降至接驳高位',
      6: '6 - 货叉带满电电池准备伸出',
      7: '7 - 货叉在接驳机高位伸出',
      8: '8 - 堆垛机至接驳机低位',
      9: '9 - 货叉在接驳机低位缩回',
      10: '10 - 货叉在接驳机低位准备取亏电电池',
      11: '11 - 货叉在接驳机低位伸出',
      12: '12 - 接驳举升中位至零点位',
      13: '13 - 货叉在接驳机低位带亏电电池缩回',
      14: '14 - 堆垛机至目标仓高位接驳上层托板缩回',
      15: '15 - 货叉在目标仓高位伸出',
      16: '16 - 堆垛机至目标仓低位',
      17: '17 - 货叉在目标仓低位缩回',
      18: '18 - 目标仓水电插头伸出',
      19: '19 - 堆垛机至初始位'
    }
  },

  // 轴号
  highSpeed: {
    1: {
      1: '加解锁1',
      2: '加解锁2',
      3: '加解锁3',
      4: '加解锁4',
      5: '加解锁5',
      6: '加解锁6',
      7: '加解锁7',
      8: '加解锁8',
      9: '加解锁9',
      10: '加解锁10',
      11: 'RGV抬升',
      12: 'RGV行走',
      13: '升降机',
      14: '转盘',
      15: '左前推杆',
      16: '右前推杆',
      17: '左后推杆',
      18: '右后推杆',
      19: '左前立柱',
      20: '右前立柱',
      21: '左后立柱',
      22: '右后立柱',
      'noAxis': '暂无对应轴号'
    },
    2: {
      1: '加解锁1',
      2: '加解锁2',
      3: '加解锁3',
      4: '加解锁4',
      5: '加解锁5',
      6: '加解锁6',
      7: '加解锁7',
      8: '加解锁8',
      9: '加解锁9',
      10: '加解锁10',
      11: '左前定位销',
      12: '右后定位销',
      13: '左前4轮推杆',
      14: '右前4轮推杆',
      15: '左后4轮推杆',
      16: '右后4轮推杆',
      17: 'V型槽',
      18: '左开合门',
      19: '右开合门',
      20: '车辆举升左',
      21: '车辆举升右',
      22: '加解锁举升',
      23: '提升机',
      'noAxis': '暂无对应轴号'
    },
    3: {
      1: '货叉',
      2: '堆垛机平移',
      3: '堆垛机升降',
      4: '枪1',
      5: '枪2',
      6: '枪3',
      7: '枪4',
      8: '枪5',
      9: '枪6',
      10: '枪7',
      11: '枪8',
      12: '枪9',
      13: '枪10',
      14: "枪1'",
      15: "枪2'",
      16: '左开合门',
      17: '右开合门',
      18: '1号枪头升降',
      19: '2号枪头升降',
      20: '9号枪头平移',
      21: '9号枪头升降',
      22: '10号枪头平移',
      23: '10号枪头升降',
      24: "1'号枪头升降",
      25: "2'号枪头升降",
      26: '左前车身定位销',
      27: '左后车身定位销',
      28: '右后车身定位销',
      29: '左前推杆',
      30: '右前推杆',
      31: '前导向条',
      32: 'V型槽',
      33: '左后推杆',
      34: '右后推杆',
      35: '后导向条',
      36: '加解锁平台平移',
      37: '加解锁平台举升',
      40: '接驳机',
      'noAxis': '暂无对应轴号'
    },
    4: {
      1: '货叉',
      2: '堆垛机平移',
      3: '堆垛机升降',
      4: '枪1',
      5: '枪2',
      6: '枪3',
      7: '枪4',
      8: '枪5',
      9: '枪6',
      10: '枪7',
      11: '枪8',
      12: '枪9',
      13: '枪10',
      14: "枪11",
      15: "枪12",
      16: '左开合门',
      17: '右开合门',
      18: '1号枪头升降',
      19: '2号枪头升降',
      20: '9号枪头升降',
      21: '10号枪头升降',
      22: "11号枪头升降",
      23: "12号枪头升降",
      26: '左前车身定位销',
      27: '左后车身定位销',
      28: '右后车身定位销',
      29: '左前推杆',
      30: '右前推杆',
      31: '左后推杆',
      32: '右后推杆',
      33: '前导向条',
      34: '后导向条',
      35: '加解锁平台平移',
      36: '加解锁平台举升',
      'noAxis': '暂无对应轴号'
    },
    7: {
      1: '拧紧枪1伺服',
      2: '拧紧枪2伺服',
      3: '拧紧枪3伺服',
      4: '拧紧枪4伺服',
      5: '拧紧枪5伺服',
      6: '拧紧枪6伺服',
      7: '拧紧枪7伺服',
      8: '拧紧枪8伺服',
      9: '左前车身定位销伺服',
      10: '右后车身定位销伺服',
      11: '左前车轮推杆伺服',
      12: '右前车轮推杆伺服',
      13: '左后车轮推杆伺服',
      14: '右后车轮推杆伺服',
      15: 'RGV行走伺服',
      16: 'RGV举升伺服',
      17: '堆垛机货叉伺服',
      18: '堆垛机升降伺服',
      19: '翻转门伺服',
      20: 'RGV平移伺服',
      21: '左前车辆举升',
      22: '右前车辆举升',
      23: '左后车辆举升',
      24: '右后车辆举升',
      25: '前接驳举升',
      26: '后接驳举升',
      'noAxis': '暂无对应轴号'
    }
  },

  sensorVarname: {
    1: {},
    2: {
      pl_lf_clamp_home_sensor: '推杆零点左前轮',
      pl_lf_v_home_sensor: '泊车到位左前',
      pl_rf_clamp_home_sensor: '推杆零点右前轮 ',
      pl_rf_v_home_sensor: '泊车到位右前',
      buffer_battery_safety_sensor: '缓存位电池安全',
      tr_battery_safety_sensor: '接驳位电池安全',
      vehical_l_lift_safety_sensor: '左举车安全',
      vehical_r_lift_safety_sensor: '右举车安全',
      buffer_battery_reached_sensor1: '缓存位电池前到位',
      buffer_battery_reached_sensor2: '缓存位电池后到位',
      buffer_battery_deceleration_sensor: '缓存位电池减速',
      pl_battery_reached_sensor1: '滚筒传送电池前到位',
      pl_battery_reached_sensor2: '滚筒传送电池后到位',
      pl_battery_deceleration_sensor: '滚筒传送电池减速',
      pl_stopper_01_extend_seneor: '滚筒传送电池前阻挡上限位',
      pl_stopper_01_retract_seneor: '滚筒传送电池前阻挡下限位',
      pl_stopper_02_extend_seneor: '滚筒传送电池后阻挡上限位',
      pl_stopper_02_retract_seneor: '滚筒传送电池后阻挡下限位',
      pl_left_car_lift_work_sensor: '车辆左举升工作位',
      pl_right_car_lift_work_sensor: '车辆右举升工作位',
      lr_work_sensor: '加解锁平台举升工作位',
      pl_lf_battery_reached_sensor: '停车位电池左前有',
      pl_lr_battery_reached_sensor: '停车位电池左后有',
      pl_rf_battery_reached_sensor: '停车位电池右前有',
      pl_rr_battery_reached_sensor: '停车位电池右后有',
      pl_f_battery_reached_sensor: '停车位电池前有',
      pl_lf_ev_locating_pin_extend_sensor: '左前车身定位销上到位',
      pl_lf_ev_locating_pin_retract_sensor: '左前车身定位销下到位',
      pl_rr_ev_locating_pin_extend_sensor: '右后车身定位销上到位',
      pl_rr_ev_locating_pin_retract_sensor: '右后车身定位销下到位',
      pl_lr_clamp_home_sensor: '左后轮推杆零点',
      pl_rr_clamp_home_sensor: '右后轮推杆零点',
      pl_door_01_open_sensor: '左开合门开到位',
      pl_door_01_close_sensor: '左开合门合到位',
      pl_door_02_open_sensor: '右开合门开到位',
      pl_door_02_close_sensor: '右开合门合到位',
      bl_l_lift_safety_sensor: '提升机左安全',
      bl_r_lift_safety_sensor: '提升机右安全',
      tr_battery_exist_sensor: '接驳位有电池',
      bc_slot1_ec_retract_sensor: '电池仓1电连接器电推杆上限位',
      bc_slot1_ec_extend_sensor: '电池仓1电连接器电推杆下限位',
      bc_slot1_lc_retract_sensor: '电池仓1水连接器电推杆上限位',
      bc_slot1_lc_extend_sensor: '电池仓1水连接器电推杆下限位',
      bc_slot1_battery_exist_sensor_1: '电池仓1电池前到位  ',
      bc_slot1_battery_exist_sensor_2: '电池仓1电池后到位  ',
      bc_slot1_battery_deceleration_sensor: '电池仓1电池减速',
      bc_slot1_smoke_sensor: '电池仓1烟雾报警',
      bc_slot1_liq_flow_switch_sensor: '电池仓1水冷流量开关',
      bc_slot2_ec_retract_sensor: '电池仓2电连接器电推杆上限位',
      bc_slot2_ec_extend_sensor: '电池仓2电连接器电推杆下限位',
      bc_slot2_lc_retract_sensor: '电池仓2水连接器电推杆上限位',
      bc_slot2_lc_extend_sensor: '电池仓2水连接器电推杆下限位',
      bc_slot2_battery_exist_sensor_1: '电池仓2电池前到位 ',
      bc_slot2_battery_exist_sensor_2: '电池仓2电池后到位 ',
      bc_slot2_battery_deceleration_sensor: '电池仓2电池减速',
      bc_slot2_smoke_sensor: '电池仓2烟雾报警',
      bc_slot2_liq_flow_switch_sensor: '电池仓2水冷流量开关',
      bc_slot3_ec_retract_sensor: '电池仓3电连接器电推杆上限位',
      bc_slot3_ec_extend_sensor: '电池仓3电连接器电推杆下限位',
      bc_slot3_lc_retract_sensor: '电池仓3水连接器电推杆上限位',
      bc_slot3_lc_extend_sensor: '电池仓3水连接器电推杆下限位',
      bc_slot3_battery_exist_sensor_1: '电池仓3电池前到位 ',
      bc_slot3_battery_exist_sensor_2: '电池仓3电池后到位 ',
      bc_slot3_battery_deceleration_sensor: '电池仓3电池减速',
      bc_slot3_smoke_sensor: '电池仓3烟雾报警',
      bc_slot3_liq_flow_switch_sensor: '电池仓3水冷流量开关',
      bc_slot4_ec_retract_sensor: '电池仓4电连接器电推杆上限位',
      bc_slot4_ec_extend_sensor: '电池仓4电连接器电推杆下限位',
      bc_slot4_lc_retract_sensor: '电池仓4水连接器电推杆上限位',
      bc_slot4_lc_extend_sensor: '电池仓4水连接器电推杆下限位',
      bc_slot4_battery_exist_sensor_1: '电池仓4电池前到位',
      bc_slot4_battery_exist_sensor_2: '电池仓4电池后到位',
      bc_slot4_battery_deceleration_sensor: '电池仓4电池减速',
      bc_slot4_smoke_sensor: '电池仓4烟雾报警',
      bc_slot4_liq_flow_switch_sensor: '电池仓4水冷流量开关',
      bc_slot5_ec_retract_sensor: '电池仓5电连接器电推杆上限位',
      bc_slot5_ec_extend_sensor: '电池仓5电连接器电推杆下限位',
      bc_slot5_lc_retract_sensor: '电池仓5水连接器电推杆上限位',
      bc_slot5_lc_extend_sensor: '电池仓5水连接器电推杆下限位',
      bc_slot5_battery_exist_sensor_1: '电池仓5电池前到位',
      bc_slot5_battery_exist_sensor_2: '电池仓5电池后到位',
      bc_slot5_battery_deceleration_sensor: '电池仓5电池减速',
      bc_slot5_smoke_sensor: '电池仓5烟雾报警',
      bc_slot5_liq_flow_switch_sensor: '电池仓5水冷流量开关',
      bc_slot6_ec_retract_sensor: '电池仓6电连接器电推杆上限位',
      bc_slot6_ec_extend_sensor: '电池仓6电连接器电推杆下限位',
      bc_slot6_lc_retract_sensor: '电池仓6水连接器电推杆上限位',
      bc_slot6_lc_extend_sensor: '电池仓6水连接器电推杆下限位',
      bc_slot6_battery_exist_sensor_1: '电池仓6电池前到位',
      bc_slot6_battery_exist_sensor_2: '电池仓6电池后到位',
      bc_slot6_battery_deceleration_sensor: '电池仓6电池减速',
      bc_slot6_smoke_sensor: '电池仓6烟雾报警',
      bc_slot6_liq_flow_switch_sensor: '电池仓6水冷流量开关',
      liq_left_bc_pressure_switch_st: '水冷左仓压力开关',
      bc_slot7_ec_retract_sensor: '电池仓7电连接器电推杆上限位',
      bc_slot7_ec_extend_sensor: '电池仓7电连接器电推杆下限位',
      bc_slot7_lc_retract_sensor: '电池仓7水连接器电推杆上限位',
      bc_slot7_lc_extend_sensor: '电池仓7水连接器电推杆下限位',
      bc_slot7_battery_exist_sensor_1: '电池仓7电池前到位',
      bc_slot7_battery_exist_sensor_2: '电池仓7电池后到位',
      bc_slot7_battery_deceleration_sensor: '电池仓7电池减速',
      bc_slot7_smoke_sensor: '电池仓7烟雾报警',
      bc_slot7_liq_flow_switch_sensor: '电池仓7水冷流量开关',
      bc_lift_get_exchange_station_1_7: '电池仓7提升机电池对接位',
      bc_slot8_ec_retract_sensor: '电池仓8电连接器电推杆上限位',
      bc_slot8_ec_extend_sensor: '电池仓8电连接器电推杆下限位',
      bc_slot8_lc_retract_sensor: '电池仓8水连接器电推杆上限位',
      bc_slot8_lc_extend_sensor: '电池仓8水连接器电推杆下限位',
      bc_slot8_battery_exist_sensor_1: '电池仓8电池前到位',
      bc_slot8_battery_exist_sensor_2: '电池仓8电池后到位',
      bc_slot8_battery_deceleration_sensor: '电池仓8电池减速',
      bc_slot8_smoke_sensor: '电池仓8烟雾报警',
      bc_slot8_liq_flow_switch_sensor: '电池仓8水冷流量开关',
      bc_lift_get_exchange_station_2_8: '电池仓8提升机电池对接位',
      bc_slot9_ec_retract_sensor: '电池仓9电连接器电推杆上限位',
      bc_slot9_ec_extend_sensor: '电池仓9电连接器电推杆下限位',
      bc_slot9_lc_retract_sensor: '电池仓9水连接器电推杆上限位',
      bc_slot9_lc_extend_sensor: '电池仓9水连接器电推杆下限位',
      bc_slot9_battery_exist_sensor_1: '电池仓9电池前到位',
      bc_slot9_battery_exist_sensor_2: '电池仓9电池后到位',
      bc_slot9_battery_deceleration_sensor: '电池仓9电池减速',
      bc_slot9_smoke_sensor: '电池仓9烟雾报警',
      bc_slot9_liq_flow_switch_sensor: '电池仓9水冷流量开关',
      bc_lift_get_exchange_station_3_9: '电池仓9提升机电池对接位',
      bc_slot10_ec_retract_sensor: '电池仓10电连接器电推杆上限位',
      bc_slot10_ec_extend_sensor: '电池仓10电连接器电推杆下限位',
      bc_slot10_lc_retract_sensor: '电池仓10水连接器电推杆上限位',
      bc_slot10_lc_extend_sensor: '电池仓10水连接器电推杆下限位',
      bc_slot10_battery_exist_sensor_1: '电池仓10电池前到位',
      bc_slot10_battery_exist_sensor_2: '电池仓10电池后到位',
      bc_slot10_battery_deceleration_sensor: '电池仓10电池减速',
      bc_slot10_smoke_sensor: '电池仓10烟雾报警',
      bc_slot10_liq_flow_switch_sensor: '电池仓10水冷流量开关',
      bc_lift_get_exchange_station_4_10: '电池仓10提升机电池对接位',
      bc_slot11_ec_retract_sensor: '电池仓11电连接器电推杆上限位',
      bc_slot11_ec_extend_sensor: '电池仓11电连接器电推杆下限位',
      bc_slot11_lc_retract_sensor: '电池仓11水连接器电推杆上限位',
      bc_slot11_lc_extend_sensor: '电池仓11水连接器电推杆下限位',
      bc_slot11_battery_exist_sensor_1: '电池仓11电池前到位',
      bc_slot11_battery_exist_sensor_2: '电池仓11电池后到位',
      bc_slot11_battery_deceleration_sensor: '电池仓11电池减速',
      bc_slot11_smoke_sensor: '电池仓11烟雾报警',
      bc_slot11_liq_flow_switch_sensor: '电池仓11水冷流量开关',
      bc_lift_get_exchange_station_5_11: '电池仓11提升机电池对接位',
      bc_slot12_ec_retract_sensor: '电池仓12电连接器电推杆上限位',
      bc_slot12_ec_extend_sensor: '电池仓12电连接器电推杆下限位',
      bc_slot12_lc_retract_sensor: '电池仓12水连接器电推杆上限位',
      bc_slot12_lc_extend_sensor: '电池仓12水连接器电推杆下限位',
      bc_slot12_battery_exist_sensor_1: '电池仓12电池前到位',
      bc_slot12_battery_exist_sensor_2: '电池仓12电池后到位',
      bc_slot12_battery_deceleration_sensor: '电池仓12电池减速',
      bc_slot12_smoke_sensor: '电池仓12烟雾报警',
      bc_slot12_liq_flow_switch_sensor: '电池仓12水冷流量开关',
      bc_lift_get_exchange_station_6_12: '电池仓12提升机电池对接位',
      bc_slot13_ec_retract_sensor: '电池仓13电连接器电推杆上限位',
      bc_slot13_ec_extend_sensor: '电池仓13电连接器电推杆下限位',
      bc_slot13_lc_retract_sensor: '电池仓13水连接器电推杆上限位',
      bc_slot13_lc_extend_sensor: '电池仓13水连接器电推杆下限位',
      bc_slot13_battery_exist_sensor_1: '电池仓13电池前到位',
      bc_slot13_battery_exist_sensor_2: '电池仓13电池后到位',
      bc_slot13_battery_deceleration_sensor: '电池仓13电池减速',
      bc_slot13_smoke_sensor: '电池仓13烟雾报警',
      bc_slot13_liq_flow_switch_sensor: '电池仓13水冷流量开关',
      bc_lift_get_exchange_station_13: '电池仓13提升机电池对接位',
      liq_right_bc_pressure_switch_st: '右仓水冷压力开关',
      bl_lf_stopper_extend_sensor: '提升机左前电推杆上限位',
      bl_lf_stopper_retract_sensor: '提升机左前电推杆下限位',
      bl_rf_stopper_extend_sensor: '提升机右前电推杆上限位',
      bl_rf_stopper_retract_sensor: '提升机右前电推杆下限位',
      bl_lr_stopper_extend_sensor: '提升机左后电推杆上限位',
      bl_lr_stopper_retract_sensor: '提升机左后电推杆下限位',
      bl_rr_stopper_extend_sensor: '提升机右后电推杆上限位',
      bl_rr_stopper_retract_sensor: '提升机右后电推杆下限位',
      bl_lf_battery_reach_sensor: '提升机左前电池到位',
      bl_rf_battery_reach_sensor: '提升机右前电池到位',
      bl_battery_deceleration_sensor: '提升机电池减速',
      V_l_fixed_extent_sensor: '左V槽固定上限位',
      V_l_fixed_retract_sensor: '左V槽固定下限位',
      V_r_fixed_extent_sensor: '右V槽固定上限位',
      V_r_fixed_retract_sensor: '右V槽固定下限位',
      lr_up_limit: '加解锁平台上限位',
      lr_down_limit: '加解锁平台下限位',
      lr_zero_sensor: '加解锁平台原点',
      l_vehical_lift_up_limit: '左车辆举升机上限位',
      l_vehical_lift_down_limit: '左车辆举升机下限位',
      l_vehical_lift_zero_sensor: '左车辆举升原点',
      r_vehical_lift_up_limit: '右车辆举升上限位',
      r_vehical_lift_down_limit: '右车辆举升下限位',
      r_vehical_lift_zero_sensor: '右车辆举升原点',
      bl_up_limit: '升降机上限位',
      bl_down_limit: '升降机下限位',
      bl_zero_sensor: '升降机原点',
    },
    3: {
      front_left_pressure_transducer: '左前压力传感器',
      right_rear_pressure_transducer: '右后压力传感器',
      roller_door_01_up_limt: '前卷帘门上到位',
      roller_door_01_down_limt: '前卷帘门下到位',
      roller_door_02_up_limt: '后卷帘门上到位',
      roller_door_02_down_limt: '后卷帘门下到位',
      front_roller_door_safety_01: '前卷帘门安全保护1',
      front_roller_door_safety_02: '前卷帘门安全保护2',
      rear_roller_door_safety_01: '后卷帘门安全保护1',
      rear_roller_door_safety_02: '后卷帘门安全保护2',
      maintain_area_safety_01: '维护门安全继电器反馈',
      maintain_area_safety_02: '维护门传感器',
      pl_buffer_dece_sensor_1: '左缓存区电池减速',
      pl_buffer_sensor_f_1: '左缓存区前电池到位',
      pl_buffer_sensor_r_1: '左缓存区后电池到位',
      pl_lf_clamp_home_sensor: '左前轮推杆原点',
      pl_lf_V_check_sensor: '左前轮进槽',
      pl_l_V_lock_extend_sensor: 'V槽左锁止上到位',
      pl_l_V_lock_retract_sensor: 'V槽左锁止下到位',
      pl_rf_clamp_home_sensor: '右前轮推杆原点',
      pl_rf_V_check_sensor: '右前轮进槽',
      pl_r_V_lock_extend_sensor: 'V槽右锁止上到位',
      pl_r_V_lock_retract_sensor: 'V槽右锁止下到位',
      pl_f_guide_work_sensor: '前导向条前到位',
      pl_f_guide_home_sensor: '前导向条后到位',
      pl_r_guide_work_sensor: '后导向条前到位',
      pl_r_guide_home_sensor: '后导向条后到位',
      pl_lr_clamp_home_sensor: '左后轮推杆原点',
      pl_rr_clamp_home_sensor: '右后轮推杆原点',
      pl_door_01_open_sensor: '左开合门开到位',
      pl_door_01_close_sensor: '左开合门关到位',
      pl_door_02_open_sensor: '右开合门开到位',
      pl_door_02_close_sensor: '右开合门关到位',
      pl_door_close_safe_sensor: '开合门闭合安全',
      bc_lift_dece_sensor: '升降仓减速',
      bc_lift_reach_sensor_f: '升降仓前到位',
      bc_lift_reach_sensor_r: '升降仓后到位',
      bc_lift_work_sensor: '升降仓举升工作位',
      pl_buffer_dece_sensor_2: '右缓存区电池减速',
      pl_buffer_sensor_f_2: '右缓存区前电池到位',
      pl_buffer_sensor_r_2: '右缓存区后电池到位',
      buffer_stopper_01_extend_sensor_02: '右缓存区前电池阻挡工作位',
      buffer_stopper_01_retract_sensor_02: '右缓存区前电池阻挡原点位',
      buffer_stopper_02_extend_sensor_02: '右缓存区后电池阻挡工作位',
      buffer_stopper_02_retract_sensor_02: '右缓存区后电池阻挡原点位',
      RGV_bc_reach_sensor_01: '电池平整1',
      RGV_bc_reach_sensor_02: '电池平整2',
      RGV_bc_reach_sensor_03: '电池平整3',
      RGV_bc_reach_sensor_04: '电池平整4',
      RGV_bc_reach_sensor_05: '电池平整5',
      RGV_bc_reach_sensor_06: '电池平整6',
      lf_pin_extend_sensor: '左前车身定位销上到位',
      lf_pin_retract_sensor: '左前车身定位销下到位',
      lf_pin_touch_sensor: '左前车身定位销接触车身',
      rr_pin_extend_sensor: '右后车身定位销上到位',
      rr_pin_retract_sensor: '右后车身定位销下到位',
      rr_pin_touch_sensor: '右后车身定位销接触车身',
      lr_pin_extend_sensor: '左后车身定位销上到位',
      lr_pin_retract_sensor: '左后车身定位销下到位',
      lr_pin_touch_sensor: '左后车身定位销接触车身',
      gun1_lift_work_sensor: '1#升降上到位',
      gun1_lift_home_sensor: '1#升降下到位',
      gun2_lift_work_sensor: '2#升降上到位',
      gun2_lift_home_sensor: '2#升降下到位',
      gun9_move_home_sensor: '9#平移位置1',
      gun9_move_work_sensor: '9#平移位置2',
      gun9_lift_work_sensor: '9#升降上到位',
      gun9_lift_home_sensor: '9#升降下到位',
      gun10_move_home_sensor: '10#平移位置1',
      gun10_move_work_sensor: '10#平移位置2',
      gun10_lift_work_sensor: '10#升降上到位',
      gun10_lift_home_sensor: '10#升降下到位',
      gun11_lift_work_sensor: '11#升降上到位',
      gun11_lift_home_sensor: '11#升降下到位',
      gun12_lift_work_sensor: '12#升降上到位',
      gun12_lift_home_sensor: '12#升降下到位',
      RGV_work_sensor: 'RGV举升工作位',
      RGV_maintain_sensor: 'RGV举升维护位',
      pl_stopper_01_home_sensor: '前电池阻挡升降原点位',
      pl_stopper_01_work_sensor: '前电池阻挡升降工作位',
      pl_stopper_01_reach_sensor: '前电池阻挡电池到位',
      pl_stopper_02_home_sensor: '后电池阻挡升降原点位',
      pl_stopper_02_work_sensor: '后电池阻挡升降工作位',
      pl_stopper_02_reach_sensor: '后电池阻挡电池到位',
      pl_move_work_sensor_1: 'RGV平移加解锁位',
      pl_move_work_sensor_2: 'RGV平移 NPA位',
      pl_move_work_sensor_3: 'RGV平移 NPD位',
      pl_move_work_sensor_4: 'RGV平移备用1',
      pl_move_work_sensor_5: 'RGV平移备用2',
      pl_stopper_01_dece_sensor: 'RGV电池阻挡电池减速',
      bc_slot1_ec_retract_sensor_1: '1仓NPA电插头上到位',
      bc_slot1_ec_extend_sensor_1: '1仓NPA电插头下到位',
      bc_slot1_lc_retract_sensor_1: '1仓NPA水插头上到位',
      bc_slot1_lc_extend_sensor_1: '1仓NPA水插头下到位',
      bc_slot1_ec_retract_sensor_2: '1仓NPD电插头上到位',
      bc_slot1_ec_extend_sensor_2: '1仓NPD电插头下到位',
      bc_slot1_lc_retract_sensor_2: '1仓NPD水插头上到位',
      bc_slot1_lc_extend_sensor_2: '1仓NPD水插头下到位',
      bc_slot1_check_sensor_1: '1仓电池区分NPA',
      bc_slot1_check_sensor_2: '1仓电池区分NPD',
      bc_slot1_reached_sensor: '1仓电池落到位',
      bc_slot1_smoke_sensor: '1仓烟雾报警',
      bc_slot1_liq_flow_switch_st: '1仓水冷流量开关',
      bc_sum_smoke_sensor: '电池仓整体烟雾',
      bc_slot2_ec_retract_sensor_1: '2仓NPA电插头上到位',
      bc_slot2_ec_extend_sensor_1: '2仓NPA电插头下到位',
      bc_slot2_lc_retract_sensor_1: '2仓NPA水插头上到位',
      bc_slot2_lc_extend_sensor_1: '2仓NPA水插头下到位',
      bc_slot2_ec_retract_sensor_2: '2仓NPD电插头上到位',
      bc_slot2_ec_extend_sensor_2: '2仓NPD电插头下到位',
      bc_slot2_lc_retract_sensor_2: '2仓NPD水插头上到位',
      bc_slot2_lc_extend_sensor_2: '2仓NPD水插头下到位',
      bc_slot2_check_sensor_1: '2仓电池区分NPA',
      bc_slot2_check_sensor_2: '2仓电池区分NPD',
      bc_slot2_reached_sensor: '2仓电池落到位',
      bc_slot2_smoke_sensor: '2仓烟雾报警',
      bc_slot2_liq_flow_switch_st: '2仓水冷流量开关',
      bc_slot3_ec_retract_sensor_1: '3仓NPA电插头上到位',
      bc_slot3_ec_extend_sensor_1: '3仓NPA电插头下到位',
      bc_slot3_lc_retract_sensor_1: '3仓NPA水插头上到位',
      bc_slot3_lc_extend_sensor_1: '3仓NPA水插头下到位',
      bc_slot3_ec_retract_sensor_2: '3仓NPD电插头上到位',
      bc_slot3_ec_extend_sensor_2: '3仓NPD电插头下到位',
      bc_slot3_lc_retract_sensor_2: '3仓NPD水插头上到位',
      bc_slot3_lc_extend_sensor_2: '3仓NPD水插头下到位',
      bc_slot3_check_sensor_1: '3仓电池区分NPA',
      bc_slot3_check_sensor_2: '3仓电池区分NPD',
      bc_slot3_reached_sensor: '3仓电池落到位',
      bc_slot3_smoke_sensor: '3仓烟雾报警',
      bc_slot3_liq_flow_switch_st: '3仓水冷流量开关',
      bc_slot4_ec_retract_sensor_1: '4仓NPA电插头上到位',
      bc_slot4_ec_extend_sensor_1: '4仓NPA电插头下到位',
      bc_slot4_lc_retract_sensor_1: '4仓NPA水插头上到位',
      bc_slot4_lc_extend_sensor_1: '4仓NPA水插头下到位',
      bc_slot4_ec_retract_sensor_2: '4仓NPD电插头上到位',
      bc_slot4_ec_extend_sensor_2: '4仓NPD电插头下到位',
      bc_slot4_lc_retract_sensor_2: '4仓NPD水插头上到位',
      bc_slot4_lc_extend_sensor_2: '4仓NPD水插头下到位',
      bc_slot4_check_sensor_1: '4仓电池区分NPA',
      bc_slot4_check_sensor_2: '4仓电池区分NPD',
      bc_slot4_reached_sensor: '4仓电池落到位',
      bc_slot4_smoke_sensor: '4仓烟雾报警',
      bc_slot4_liq_flow_switch_st: '4仓水冷流量开关',
      bc_slot5_ec_retract_sensor_1: '5仓NPA电插头上到位',
      bc_slot5_ec_extend_sensor_1: '5仓NPA电插头下到位',
      bc_slot5_lc_retract_sensor_1: '5仓NPA水插头上到位',
      bc_slot5_lc_extend_sensor_1: '5仓NPA水插头下到位',
      bc_slot5_ec_retract_sensor_2: '5仓NPD电插头上到位',
      bc_slot5_ec_extend_sensor_2: '5仓NPD电插头下到位',
      bc_slot5_lc_retract_sensor_2: '5仓NPD水插头上到位',
      bc_slot5_lc_extend_sensor_2: '5仓NPD水插头下到位',
      bc_slot5_check_sensor_1: '5仓电池区分NPA',
      bc_slot5_check_sensor_2: '5仓电池区分NPD',
      bc_slot5_reached_sensor: '5仓电池落到位',
      bc_slot5_smoke_sensor: '5仓烟雾报警',
      bc_slot5_liq_flow_switch_st: '5仓水冷流量开关',
      bcslot1_5_pressure_switch_st: '1~5仓水冷压力开关',
      bc_slot6_ec_retract_sensor_1: '6仓NPA电插头上到位',
      bc_slot6_ec_extend_sensor_1: '6仓NPA电插头下到位',
      bc_slot6_lc_retract_sensor_1: '6仓NPA水插头上到位',
      bc_slot6_lc_extend_sensor_1: '6仓NPA水插头下到位',
      bc_slot6_ec_retract_sensor_2: '6仓NPD电插头上到位',
      bc_slot6_ec_extend_sensor_2: '6仓NPD电插头下到位',
      bc_slot6_lc_retract_sensor_2: '6仓NPD水插头上到位',
      bc_slot6_lc_extend_sensor_2: '6仓NPD水插头下到位',
      bc_slot6_check_sensor_1: '6仓电池区分NPA',
      bc_slot6_check_sensor_2: '6仓电池区分NPD',
      bc_slot6_reached_sensor: '6仓电池落到位',
      bc_slot6_smoke_sensor: '6仓烟雾报警',
      bc_slot6_liq_flow_switch_st: '6仓水冷流量开关',
      bc_slot7_ec_retract_sensor_1: '7仓NPA电插头上到位',
      bc_slot7_ec_extend_sensor_1: '7仓NPA电插头下到位',
      bc_slot7_lc_retract_sensor_1: '7仓NPA水插头上到位',
      bc_slot7_lc_extend_sensor_1: '7仓NPA水插头下到位',
      bc_slot7_ec_retract_sensor_2: '7仓NPD电插头上到位',
      bc_slot7_ec_extend_sensor_2: '7仓NPD电插头下到位',
      bc_slot7_lc_retract_sensor_2: '7仓NPD水插头上到位',
      bc_slot7_lc_extend_sensor_2: '7仓NPD水插头下到位',
      bc_slot7_check_sensor_1: '7仓电池区分NPA',
      bc_slot7_check_sensor_2: '7仓电池区分NPD',
      bc_slot7_reached_sensor: '7仓电池落到位',
      bc_slot7_smoke_sensor: '7仓烟雾报警',
      bc_slot7_liq_flow_switch_st: '7仓水冷流量开关',
      bc_slot8_ec_retract_sensor_1: '8仓NPA电插头上到位',
      bc_slot8_ec_extend_sensor_1: '8仓NPA电插头下到位',
      bc_slot8_lc_retract_sensor_1: '8仓NPA水插头上到位',
      bc_slot8_lc_extend_sensor_1: '8仓NPA水插头下到位',
      bc_slot8_ec_retract_sensor_2: '8仓NPD电插头上到位',
      bc_slot8_ec_extend_sensor_2: '8仓NPD电插头下到位',
      bc_slot8_lc_retract_sensor_2: '8仓NPD水插头上到位',
      bc_slot8_lc_extend_sensor_2: '8仓NPD水插头下到位',
      bc_slot8_check_sensor_1: '8仓电池区分NPA',
      bc_slot8_check_sensor_2: '8仓电池区分NPD',
      bc_slot8_reached_sensor: '8仓电池落到位',
      bc_slot8_smoke_sensor: '8仓烟雾报警',
      bc_slot8_liq_flow_switch_st: '8仓水冷流量开关',
      bc_slot9_ec_retract_sensor_1: '9仓NPA电插头上到位',
      bc_slot9_ec_extend_sensor_1: '9仓NPA电插头下到位',
      bc_slot9_lc_retract_sensor_1: '9仓NPA水插头上到位',
      bc_slot9_lc_extend_sensor_1: '9仓NPA水插头下到位',
      bc_slot9_ec_retract_sensor_2: '9仓NPD电插头上到位',
      bc_slot9_ec_extend_sensor_2: '9仓NPD电插头下到位',
      bc_slot9_lc_retract_sensor_2: '9仓NPD水插头上到位',
      bc_slot9_lc_extend_sensor_2: '9仓NPD水插头下到位',
      bc_slot9_check_sensor_1: '9仓电池区分NPA',
      bc_slot9_check_sensor_2: '9仓电池区分NPD',
      bc_slot9_reached_sensor: '9仓电池落到位',
      bc_slot9_smoke_sensor: '9仓烟雾报警',
      bc_slot9_liq_flow_switch_st: '9仓水冷流量开关',
      bc_slot10_ec_retract_sensor_1: '10仓NPA电插头上到位',
      bc_slot10_ec_extend_sensor_1: '10仓NPA电插头下到位',
      bc_slot10_lc_retract_sensor_1: '10仓NPA水插头上到位',
      bc_slot10_lc_extend_sensor_1: '10仓NPA水插头下到位',
      bc_slot10_ec_retract_sensor_2: '10仓NPD电插头上到位',
      bc_slot10_ec_extend_sensor_2: '10仓NPD电插头下到位',
      bc_slot10_lc_retract_sensor_2: '10仓NPD水插头上到位',
      bc_slot10_lc_extend_sensor_2: '10仓NPD水插头下到位',
      bc_slot10_check_sensor_1: '10仓电池区分NPA',
      bc_slot10_check_sensor_2: '10仓电池区分NPD',
      bc_slot10_reached_sensor: '10仓电池落到位',
      bc_slot10_smoke_sensor: '10仓烟雾报警',
      bc_slot10_liq_flow_switch_st: '10仓水冷流量开关',
      bcslot6_10_pressure_switch_st: '6~10仓水冷压力开关',
      stacker_low_sensor_1: '堆垛机仓位对接1层',
      stacker_low_sensor_2: '堆垛机仓位对接2层',
      stacker_low_sensor_3: '堆垛机仓位对接3层',
      stacker_low_sensor_4: '堆垛机仓位对接4层',
      stacker_low_sensor_5: '堆垛机仓位对接5层',
      stacker_low_sensor_6: '堆垛机仓位对接6层',
      stacker_low_sensor_0: '堆垛机接驳位对接位',
      stacker_move_f_sensor: '堆垛机行走前仓位',
      stacker_move_r_sensor: '堆垛机行走后仓位',
      stacker_move_RGV_sensor: '堆垛机行走RGV对接位',
      stacker_left_safe_sensor_1: '货叉上层左超程',
      stacker_right_safe_sensor_1: '货叉上层右超程',
      stacker_left_safe_sensor_2: '货叉下层左超程',
      stacker_right_safe_sensor_2: '货叉下层右超程',
      fork_retract_sensor_1: '货叉主叉臂中点',
      fork_retract_sensor_2: '货叉辅叉臂中点',
      fork_left_extend_sensor_1: '货叉叉臂左到位1',
      fork_left_extend_sensor_2: '货叉叉臂左到位2',
      fork_right_extend_sensor_1: '货叉叉臂右到位',
      fork_bc_exist_sensor_1: '货叉有电池1',
      fork_bc_exist_sensor_2: '货叉有电池2',
      vehaicl_l_work_sensor: '左车辆举升工作位',
      vehaicl_l_maintain_sensor: '左车辆举升维护位',
      vehaicl_l_safe_sensor: '左车辆举升安全',
      vehaicl_l_bc_safe_sensor: '左车辆举升电池安全',
      vehaicl_r_work_sensor: '右车辆举升工作位',
      vehaicl_r_maintain_sensor: '右车辆举升维护位',
      vehaicl_r_safe_sensor: '右车辆举升安全',
      vehaicl_r_bc_safe_sensor: '右车辆举升电池安全',
      bc_slot11_ec_retract_sensor_1: '11仓NPA电插头上到位',
      bc_slot11_ec_extend_sensor_1: '11仓NPA电插头下到位',
      bc_slot11_lc_retract_sensor_1: '11仓NPA水插头上到位',
      bc_slot11_lc_extend_sensor_1: '11仓NPA水插头下到位',
      bc_slot11_ec_retract_sensor_2: '11仓NPD电插头上到位',
      bc_slot11_ec_extend_sensor_2: '11仓NPD电插头下到位',
      bc_slot11_lc_retract_sensor_2: '11仓NPD水插头上到位',
      bc_slot11_lc_extend_sensor_2: '11仓NPD水插头下到位',
      bc_slot11_check_sensor_1: '11仓电池区分NPA',
      bc_slot11_check_sensor_2: '11仓电池区分NPD',
      bc_slot11_reached_sensor: '11仓电池落到位',
      bc_slot11_smoke_sensor: '11仓烟雾报警',
      bc_slot11_liq_flow_switch_st: '11仓水冷流量开关',
      bc_slot12_ec_retract_sensor_1: '12仓NPA电插头上到位',
      bc_slot12_ec_extend_sensor_1: '12仓NPA电插头下到位',
      bc_slot12_lc_retract_sensor_1: '12仓NPA水插头上到位',
      bc_slot12_lc_extend_sensor_1: '12仓NPA水插头下到位',
      bc_slot12_ec_retract_sensor_2: '12仓NPD电插头上到位',
      bc_slot12_ec_extend_sensor_2: '12仓NPD电插头下到位',
      bc_slot12_lc_retract_sensor_2: '12仓NPD水插头上到位',
      bc_slot12_lc_extend_sensor_2: '12仓NPD水插头下到位',
      bc_slot12_check_sensor_1: '12仓电池区分NPA',
      bc_slot12_check_sensor_2: '12仓电池区分NPD',
      bc_slot12_reached_sensor: '12仓电池落到位',
      bc_slot12_smoke_sensor: '12仓烟雾报警',
      bc_slot12_liq_flow_switch_st: '12仓水冷流量开关',
      bc_slot13_ec_retract_sensor_1: '13仓NPA电插头上到位',
      bc_slot13_ec_extend_sensor_1: '13仓NPA电插头下到位',
      bc_slot13_lc_retract_sensor_1: '13仓NPA水插头上到位',
      bc_slot13_lc_extend_sensor_1: '13仓NPA水插头下到位',
      bc_slot13_ec_retract_sensor_2: '13仓NPD电插头上到位',
      bc_slot13_ec_extend_sensor_2: '13仓NPD电插头下到位',
      bc_slot13_lc_retract_sensor_2: '13仓NPD水插头上到位',
      bc_slot13_lc_extend_sensor_2: '13仓NPD水插头下到位',
      bc_slot13_check_sensor_1: '13仓电池区分NPA',
      bc_slot13_check_sensor_2: '13仓电池区分NPD',
      bc_slot13_reached_sensor: '13仓电池落到位',
      bc_slot13_smoke_sensor: '13仓烟雾报警',
      bc_slot13_liq_flow_switch_st: '13仓水冷流量开关',
      bc_slot14_ec_retract_sensor_1: '14仓NPA电插头上到位',
      bc_slot14_ec_extend_sensor_1: '14仓NPA电插头下到位',
      bc_slot14_lc_retract_sensor_1: '14仓NPA水插头上到位',
      bc_slot14_lc_extend_sensor_1: '14仓NPA水插头下到位',
      bc_slot14_ec_retract_sensor_2: '14仓NPD电插头上到位',
      bc_slot14_ec_extend_sensor_2: '14仓NPD电插头下到位',
      bc_slot14_lc_retract_sensor_2: '14仓NPD水插头上到位',
      bc_slot14_lc_extend_sensor_2: '14仓NPD水插头下到位',
      bc_slot14_check_sensor_1: '14仓电池区分NPA',
      bc_slot14_check_sensor_2: '14仓电池区分NPD',
      bc_slot14_reached_sensor: '14仓电池落到位',
      bc_slot14_smoke_sensor: '14仓烟雾报警',
      bc_slot14_liq_flow_switch_st: '14仓水冷流量开关',
      bc_slot15_ec_retract_sensor_1: '15仓NPA电插头上到位',
      bc_slot15_ec_extend_sensor_1: '15仓NPA电插头下到位',
      bc_slot15_lc_retract_sensor_1: '15仓NPA水插头上到位',
      bc_slot15_lc_extend_sensor_1: '15仓NPA水插头下到位',
      bc_slot15_ec_retract_sensor_2: '15仓NPD电插头上到位',
      bc_slot15_ec_extend_sensor_2: '15仓NPD电插头下到位',
      bc_slot15_lc_retract_sensor_2: '15仓NPD水插头上到位',
      bc_slot15_lc_extend_sensor_2: '15仓NPD水插头下到位',
      bc_slot15_check_sensor_1: '15仓电池区分NPA',
      bc_slot15_check_sensor_2: '15仓电池区分NPD',
      bc_slot15_reached_sensor: '15仓电池落到位',
      bc_slot15_smoke_sensor: '15仓烟雾报警',
      bc_slot15_liq_flow_switch_st: '15仓水冷流量开关',
      bc_slot16_ec_retract_sensor_1: '16仓NPA电插头上到位',
      bc_slot16_ec_extend_sensor_1: '16仓NPA电插头下到位',
      bc_slot16_lc_retract_sensor_1: '16仓NPA水插头上到位',
      bc_slot16_lc_extend_sensor_1: '16仓NPA水插头下到位',
      bc_slot16_ec_retract_sensor_2: '16仓NPD电插头上到位',
      bc_slot16_ec_extend_sensor_2: '16仓NPD电插头下到位',
      bc_slot16_lc_retract_sensor_2: '16仓NPD水插头上到位',
      bc_slot16_lc_extend_sensor_2: '16仓NPD水插头下到位',
      bc_slot16_check_sensor_1: '16仓电池区分NPA',
      bc_slot16_check_sensor_2: '16仓电池区分NPD',
      bc_slot16_reached_sensor: '16仓电池落到位',
      bc_slot16_smoke_sensor: '16仓烟雾报警',
      bc_slot16_liq_flow_switch_st: '16仓水冷流量开关',
      bc_slot17_ec_retract_sensor_1: '17仓NPA电插头上到位',
      bc_slot17_ec_extend_sensor_1: '17仓NPA电插头下到位',
      bc_slot17_lc_retract_sensor_1: '17仓NPA水插头上到位',
      bc_slot17_lc_extend_sensor_1: '17仓NPA水插头下到位',
      bc_slot17_ec_retract_sensor_2: '17仓NPD电插头上到位',
      bc_slot17_ec_extend_sensor_2: '17仓NPD电插头下到位',
      bc_slot17_lc_retract_sensor_2: '17仓NPD水插头上到位',
      bc_slot17_lc_extend_sensor_2: '17仓NPD水插头下到位',
      bc_slot17_check_sensor_1: '17仓电池区分NPA',
      bc_slot17_check_sensor_2: '17仓电池区分NPD',
      bc_slot17_reached_sensor: '17仓电池落到位',
      bc_slot17_smoke_sensor: '17仓烟雾报警',
      bc_slot17_liq_flow_switch_st: '17仓水冷流量开关',
      bc_slot18_ec_retract_sensor_1: '18仓NPA电插头上到位',
      bc_slot18_ec_extend_sensor_1: '18仓NPA电插头下到位',
      bc_slot18_lc_retract_sensor_1: '18仓NPA水插头上到位',
      bc_slot18_lc_extend_sensor_1: '18仓NPA水插头下到位',
      bc_slot18_ec_retract_sensor_2: '18仓NPD电插头上到位',
      bc_slot18_ec_extend_sensor_2: '18仓NPD电插头下到位',
      bc_slot18_lc_retract_sensor_2: '18仓NPD水插头上到位',
      bc_slot18_lc_extend_sensor_2: '18仓NPD水插头下到位',
      bc_slot18_check_sensor_1: '18仓电池区分NPA',
      bc_slot18_check_sensor_2: '18仓电池区分NPD',
      bc_slot18_reached_sensor: '18仓电池落到位',
      bc_slot18_smoke_sensor: '18仓烟雾报警',
      bc_slot18_liq_flow_switch_st: '18仓水冷流量开关',
      bc_slot19_ec_retract_sensor_1: '19仓NPA电插头上到位',
      bc_slot19_ec_extend_sensor_1: '19仓NPA电插头下到位',
      bc_slot19_lc_retract_sensor_1: '19仓NPA水插头上到位',
      bc_slot19_lc_extend_sensor_1: '19仓NPA水插头下到位',
      bc_slot19_ec_retract_sensor_2: '19仓NPD电插头上到位',
      bc_slot19_ec_extend_sensor_2: '19仓NPD电插头下到位',
      bc_slot19_lc_retract_sensor_2: '19仓NPD水插头上到位',
      bc_slot19_lc_extend_sensor_2: '19仓NPD水插头下到位',
      bc_slot19_check_sensor_1: '19仓电池区分NPA',
      bc_slot19_check_sensor_2: '19仓电池区分NPD',
      bc_slot19_reached_sensor: '19仓电池落到位',
      bc_slot19_smoke_sensor: '19仓烟雾报警',
      bc_slot19_liq_flow_switch_st: '19仓水冷流量开关',
      bc_slot20_ec_retract_sensor_1: '20仓NPA电插头上到位',
      bc_slot20_ec_extend_sensor_1: '20仓NPA电插头下到位',
      bc_slot20_lc_retract_sensor_1: '20仓NPA水插头上到位',
      bc_slot20_lc_extend_sensor_1: '20仓NPA水插头下到位',
      bc_slot20_ec_retract_sensor_2: '20仓NPD电插头上到位',
      bc_slot20_ec_extend_sensor_2: '20仓NPD电插头下到位',
      bc_slot20_lc_retract_sensor_2: '20仓NPD水插头上到位',
      bc_slot20_lc_extend_sensor_2: '20仓NPD水插头下到位',
      bc_slot20_check_sensor_1: '20仓电池区分NPA',
      bc_slot20_check_sensor_2: '20仓电池区分NPD',
      bc_slot20_reached_sensor: '20仓电池落到位',
      bc_slot20_smoke_sensor: '20仓烟雾报警',
      bc_slot20_liq_flow_switch_st: '20仓水冷流量开关',
      bc_slot21_ec_retract_sensor_1: '21仓NPA电插头上到位',
      bc_slot21_ec_extend_sensor_1: '21仓NPA电插头下到位',
      bc_slot21_ec_retract_sensor_2: '21仓NPD电插头上到位',
      bc_slot21_ec_extend_sensor_2: '21仓NPD电插头下到位',
      bc_slot21_check_sensor_1: '21仓电池区分NPA',
      bc_slot21_check_sensor_2: '21仓电池区分NPD',
      bc_slot21_reached_sensor: '21仓电池落到位',
      bc_slot21_smoke_sensor: '21仓烟雾报警',
      bc_slot11_15_pressure_switch_st: '11~15仓 水冷压力开关',
      bc_slot16_20_pressure_switch_st: '16~20仓 水冷压力开关',
      bc_fire_push_retract_sensor_1: '消防接驳前推杆缩回到位',
      bc_fire_push_extend_sensor_1: '消防接驳前推杆伸出到位',
      bc_fire_push_retract_sensor_2: '消防接驳后推杆缩回到位',
      bc_fire_push_extend_sensor_2: '消防接驳后推杆伸出到位',
      fire_liq_check: '消防液位检测',
      fork_X_left_limit_sensor: '堆垛机货叉左限位',
      fork_X_right_limit_sensor: '堆垛机货叉右限位',
      fork_X_home_sensor: '堆垛机货叉原点',
      stacker_move_f_limit_sensor: '堆垛机行走前限位',
      stacker_move_r_limit_sensor: '堆垛机行走后限位',
      stacker_move_home_sensor: '堆垛机行走原点',
      stacker_lift_up_limit_sensor: '堆垛机升降上限位',
      stacker_lift_down_limit_sensor: '堆垛机升降下限位',
      stacker_lift_home_sensor: '堆垛机升降原点',
      pl_move_f_limit_sensor: '加解锁平台平移前限位',
      pl_move_r_limit_sensor: '加解锁平台平移后限位',
      pl_move_home_sensor: '加解锁平台平移原点',
      lr_lift_up_limit_sensor: '加解锁平台升降上限位',
      lr_lift_down_limit_sensor: '加解锁平台升降下限位',
      lr_lift_home_sensor: '加解锁平台升降原点',
      vehical_f_up_limit_sensor: '左车辆举升上限位',
      vehical_f_down_limit_sensor: '左车辆举升下限位',
      vehical_f_home_sensor: '左车辆举升原点',
      vehical_r_up_limit_sensor: '右车辆举升上限位',
      vehical_r_down_limit_sensor: '右车辆举升下限位',
      vehical_r_home_sensor: '右车辆举升原点',
      bc_lift_up_limit_sensor: '升降仓上限位',
      bc_lift_down_limit_sensor: '升降仓下限位',
      bc_lift_home_sensor: '升降仓原点',
      bc_lift_safe_sensor: '接驳位举升电池安全',
      left_buffer_safe_sensor: '左缓存电池安全',
      right_buffer_safe_sensor: '右缓存电池安全',
      bc_slot22_reached_sensor: '消防仓电池落到位',
      bc_lift_exist_sensor: '接驳位上有电池检测',
      RGV_bc_reach_sensor_07: '电池平整7',
      RGV_bc_reach_sensor_08: '电池平整8',
      liq_lift_zero_sensor: '液压举升原点位',
    },
    4: {
      front_left_pressure_transducer: '左前压力传感器',
      right_rear_pressure_transducer: '右后压力传感器',
      bc_slot13_lc_retract_sensor_1: 'A1仓NPA水插头缩回到位',
      bc_slot13_lc_retract_sensor_2:	'A1仓NPD水插头缩回到位',
      bc_slot13_ec_retract_sensor_1:	'A1仓NPA电插头缩回到位',
      bc_slot13_liq_flow_switch_st:	'A1仓水流量开关',
      bc_slot14_lc_retract_sensor_1:	'A2仓NPA水插头缩回到位',
      bc_slot14_lc_retract_sensor_2:	'A2仓NPD水插头缩回到位',
      bc_slot14_ec_retract_sensor_1:	'A2仓NPA电插头缩回到位',
      bc_slot14_liq_flow_switch_st:	'A2仓水流量开关',
      bc_slot15_lc_retract_sensor_1:	'A3仓NPA水插头缩回到位',
      bc_slot15_lc_retract_sensor_2:	'A3仓NPD水插头缩回到位',
      bc_slot15_ec_retract_sensor_1:	'A3仓NPA电插头缩回到位',
      bc_slot15_liq_flow_switch_st:	'A3仓水流量开关',
      bc_slot16_lc_retract_sensor_1:	'A4仓NPA水插头缩回到位',
      bc_slot16_lc_retract_sensor_2:	'A4仓NPD水插头缩回到位',
      bc_slot16_ec_retract_sensor_1:	'A4仓NPA电插头缩回到位',
      bc_slot16_liq_flow_switch_st:	'A4仓水流量开关',
      bc_slot17_lc_retract_sensor_1:	'A5仓NPA水插头缩回到位',
      bc_slot17_lc_retract_sensor_2:	'A5仓NPD水插头缩回到位',
      bc_slot17_ec_retract_sensor_1:	'A5仓NPA电插头缩回到位',
      bc_slot17_liq_flow_switch_st:	'A5仓水流量开关',
      bc_fire_push_extend_sensor_1:	'消防仓前推杆伸出到位',
      bc_fire_push_retract_sensor_1:	'消防仓前推杆缩回到位',
      fire_liq_check:	'消防仓液位检测',
      bc_slot24_reached_sensor:	'消防仓电池落到位',
      bcslot13_17_pressure_switch_st:	'A1-A5仓水压力开关',
      bc_slot13_ec_retract_sensor_2:	'A1仓NPD电插头缩回到位',
      bc_slot13_reached_sensor:	'A1仓电池落到位',
      bc_slot14_ec_retract_sensor_2:	'A2仓NPD电插头缩回到位',
      bc_slot14_reached_sensor:	'A2仓电池落到位',
      bc_slot15_ec_retract_sensor_2:	'A3仓NPD电插头缩回到位',
      bc_slot15_reached_sensor:	'A3仓电池落到位',
      bc_slot16_ec_retract_sensor_2:	'A4仓NPD电插头缩回到位',
      bc_slot16_reached_sensor:	'A4仓电池落到位',
      bc_slot17_ec_retract_sensor_2:	'A5仓NPD电插头缩回到位',
      bc_slot17_reached_sensor:	'A5仓电池落到位',
      bc_slot13_smoke_sensor:	'A1仓烟雾报警',
      bc_slot14_smoke_sensor:	'A2仓烟雾报警',
      bc_slot15_smoke_sensor:	'A3仓烟雾报警',
      bc_slot16_smoke_sensor:	'A4仓烟雾报警',
      bc_slot17_smoke_sensor:	'A5仓烟雾报警',
      bc_slot18_lc_retract_sensor_1:	'A6仓NPA水插头缩回到位',
      bc_slot18_lc_retract_sensor_2:	'A6仓NPD水插头缩回到位',
      bc_slot18_ec_retract_sensor_1:	'A6仓NPA电插头缩回到位',
      bc_slot18_liq_flow_switch_st:	'A6仓水流量开关',
      bc_slot19_lc_retract_sensor_1:	'A7仓NPA水插头缩回到位',
      bc_slot19_lc_retract_sensor_2:	'A7仓NPD水插头缩回到位',
      bc_slot19_ec_retract_sensor_1:	'A7仓NPA电插头缩回到位',
      bc_slot19_liq_flow_switch_st:	'A7仓水流量开关',
      bc_slot20_lc_retract_sensor_1:	'A8仓NPA水插头缩回到位',
      bc_slot20_lc_retract_sensor_2:	'A8仓NPD水插头缩回到位',
      bc_slot20_ec_retract_sensor_1:	'A8仓NPA电插头缩回到位',
      bc_slot20_liq_flow_switch_st:	'A8仓水流量开关',
      bc_slot21_lc_retract_sensor_1:	'A9仓NPA水插头缩回到位',
      bc_slot21_lc_retract_sensor_2:	'A9仓NPD水插头缩回到位',
      bc_slot21_ec_retract_sensor_1:	'A9仓NPA电插头缩回到位',
      bc_slot21_liq_flow_switch_st:	'A9仓水流量开关',
      bc_slot22_lc_retract_sensor_1:	'A10仓NPA水插头缩回到位',
      bc_slot22_lc_retract_sensor_2:	'A10仓NPD水插头缩回到位',
      bc_slot22_ec_retract_sensor_1:	'A10仓NPA电插头缩回到位',
      bc_slot22_liq_flow_switch_st:	'A10仓水流量开关',
      bc_slot23_ec_retract_sensor_1:	'A11仓NPA电插头缩回到位',
      bc_fire_push_extend_sensor_2:	'消防仓后推杆伸出到位',
      bc_fire_push_retract_sensor_2:	'消防仓后推杆缩回到位',
      bcslot18_22_pressure_switch_st:	'A6-A10仓水压力开关',
      bc_slot18_ec_retract_sensor_2:	'A6仓NPD电插头缩回到位',
      bc_slot18_reached_sensor:	'A6仓电池落到位',
      bc_slot19_ec_retract_sensor_2:	'A7仓NPD电插头缩回到位',
      bc_slot19_reached_sensor:	'A7仓电池落到位',
      bc_slot20_ec_retract_sensor_2:	'A8仓NPD电插头缩回到位',
      bc_slot20_reached_sensor:	'A8仓电池落到位',
      bc_slot18_smoke_sensor:	'A6仓烟雾报警',
      bc_slot19_smoke_sensor:	'A7仓烟雾报警',
      bc_slot20_smoke_sensor:	'A8仓烟雾报警',
      bc_slot21_ec_retract_sensor_2:	'A9仓NPD电插头缩回到位',
      bc_slot21_reached_sensor:	'A9仓电池落到位',
      bc_slot22_ec_retract_sensor_2:	'A10仓NPD电插头缩回到位',
      bc_slot22_reached_sensor:	'A10仓电池落到位',
      bc_slot23_ec_retract_sensor_2:	'A11仓NPD电插头缩回到位',
      bc_slot23_reached_sensor:	'A11仓电池落到位',
      bc_slot21_smoke_sensor:	'A9仓烟雾报警',
      bc_slot22_smoke_sensor:	'A10仓烟雾报警',
      bc_slot23_smoke_sensor:	'A11仓烟雾报警',
      bc_slot1_ec_retract_sensor_1:	'C1仓NPA电插头缩回到位',
      bc_slot1_reached_sensor:	'C1仓电池落到位',
      bc_slot2_ec_retract_sensor_1:	'C2仓NPA电插头缩回到位',
      bc_slot2_reached_sensor:	'C2仓电池落到位',
      bc_slot3_ec_retract_sensor_1:	'C3仓NPA电插头缩回到位',
      bc_slot3_reached_sensor:	'C3仓电池落到位',
      bc_slot4_ec_retract_sensor_1:	'C4仓NPA电插头缩回到位',
      bc_slot4_reached_sensor:	'C4仓电池落到位',
      bc_slot5_ec_retract_sensor_1:	'C5仓NPA电插头缩回到位',
      bc_slot5_reached_sensor:	'C5仓电池落到位',
      bc_slot6_ec_retract_sensor_1:	'C6仓NPA电插头缩回到位',
      bc_slot6_reached_sensor:	'C6仓电池落到位',
      bc_slot1_ec_retract_sensor_2:	'C1仓NPD电插头缩回到位',
      bc_slot2_ec_retract_sensor_2:	'C2仓NPD电插头缩回到位',
      bc_slot3_ec_retract_sensor_2:	'C3仓NPD电插头缩回到位',
      bc_slot4_ec_retract_sensor_2:	'C4仓NPD电插头缩回到位',
      bc_slot5_ec_retract_sensor_2:	'C5仓NPD电插头缩回到位',
      bc_slot6_ec_retract_sensor_2:	'C6仓NPD电插头缩回到位',
      bc_slot1_smoke_sensor:	'C1仓烟雾报警',
      bc_slot2_smoke_sensor:	'C2仓烟雾报警',
      bc_slot3_smoke_sensor:	'C3仓烟雾报警',
      bc_slot4_smoke_sensor:	'C4仓烟雾报警',
      bc_slot5_smoke_sensor:	'C5仓烟雾报警',
      bc_slot6_smoke_sensor:	'C6仓烟雾报警',
      bc_slot7_ec_retract_sensor_1:	'C7仓NPA电插头缩回到位',
      bc_slot7_reached_sensor:	'C7仓电池落到位',
      bc_slot8_ec_retract_sensor_1:	'C8仓NPA电插头缩回到位',
      bc_slot8_reached_sensor:	'C8仓电池落到位',
      bc_slot9_ec_retract_sensor_1:	'C9仓NPA电插头缩回到位',
      bc_slot9_reached_sensor:	'C9仓电池落到位',
      bc_slot10_ec_retract_sensor_1:	'C10仓NPA电插头缩回到位',
      bc_slot10_reached_sensor:	'C10仓电池落到位',
      bc_slot11_ec_retract_sensor_1:	'C11仓NPA电插头缩回到位',
      bc_slot11_reached_sensor:	'C11仓电池落到位',
      bc_slot12_ec_retract_sensor_1:	'C12仓NPA电插头缩回到位',
      bc_slot12_reached_sensor:	'C12仓电池落到位',
      bc_slot7_ec_retract_sensor_2:	'C7仓NPD电插头缩回到位',
      bc_slot8_ec_retract_sensor_2:	'C8仓NPD电插头缩回到位',
      bc_slot9_ec_retract_sensor_2:	'C9仓NPD电插头缩回到位',
      bc_slot10_ec_retract_sensor_2:	'C10仓NPD电插头缩回到位',
      bc_slot11_ec_retract_sensor_2:	'C11仓NPD电插头缩回到位',
      bc_slot12_ec_retract_sensor_2:	'C12仓NPD电插头缩回到位',
      bc_slot7_smoke_sensor:	'C7仓烟雾报警',
      bc_slot8_smoke_sensor:	'C8仓烟雾报警',
      bc_slot9_smoke_sensor:	'C9仓烟雾报警',
      bc_slot10_smoke_sensor:	'C10仓烟雾报警',
      bc_slot11_smoke_sensor:	'C11仓烟雾报警',
      bc_slot12_smoke_sensor:	'C12仓烟雾报警',
      RGV_bc_reach_sensor_01:	'电池平整1',
      RGV_bc_reach_sensor_02:	'电池平整2',
      RGV_bc_reach_sensor_04:	'电池平整4',
      RGV_bc_reach_sensor_05:	'电池平整5',
      RGV_bc_reach_sensor_07:	'电池平整7',
      gun1_lift_home_sensor:	'1#枪升降下到位',
      gun2_lift_home_sensor:	'2#枪升降下到位',
      gun9_lift_home_sensor:	'9#枪升降下到位',
      gun10_lift_home_sensor:	'10#枪升降下到位',
      gun11_lift_home_sensor:	'11#枪升降下到位',
      gun12_lift_home_sensor:	'12#枪升降下到位',
      lr_pin_touch_sensor:	'左后车身定位销接触车身',
      lf_pin_retract_sensor:	'左前车身定位销下到位',
      rr_pin_retract_sensor:	'左后车身定位销下到位',
      lr_pin_retract_sensor:	'右后车身定位销下到位',
      pl_stopper_01_work_sensor:	'RGV前阻挡 上到位',
      pl_stopper_01_home_sensor:	'RGV前阻挡 下到位',
      pl_stopper_02_work_sensor:	'RGV后阻挡 上到位',
      pl_stopper_02_home_sensor:	'RGV后阻挡 下到位',
      l_bat_pin_retract_sensor:	'RGV左电池定位销原点位',
      r_bat_pin_retract_sensor:	'RGV右电池定位销原点位',
      pl_stopper_01_reach_sensor:	'RGV前阻挡 电池到位',
      pl_stopper_02_reach_sensor:	'RGV后阻挡 电池到位',
      pl_move_work_sensor_1:	'RGV平移加解锁位',
      pl_stopper_01_dece_sensor:	'RGV传送减速',
      fork_left_extend_sensor_1:	'货叉叉臂左到位1',
      fork_left_extend_sensor_2:	'货叉叉臂左到位2',
      fork_retract_sensor_2:	'货叉辅叉臂中点',
      stacker_move_f_sensor:	'堆垛机行走前仓位',
      stacker_move_r_sensor:	'堆垛机行走后仓位',
      stacker_move_RGV_sensor:	'堆垛机行走接驳位',
      stacker_left_safe_sensor_1:	'货叉上层左超程',
      stacker_right_safe_sensor_1:	'货叉上层右超程',
      stacker_high_sensor_1:	'堆垛机仓位对接',
      fork_right_extend_sensor_1:	'货叉叉臂右到位',
      stacker_bat_sensor_1:	'堆垛机电池区分1',
      stacker_bat_sensor_2:	'堆垛机电池区分2',
      stacker_bat_sensor_3:	'堆垛机电池区分3',
      pl_buffer_dece_sensor_2:	'接驳位电池减速',
      pl_buffer_sensor_f_2:	'接驳位前电池到位',
      buffer_stopper_01_extend_sensor_02:	'接驳位挡板前伸出到位',
      buffer_stopper_01_retract_sensor_02:	'接驳位挡板前缩回到位',
      pl_buffer_sensor_r_2:	'接驳位后电池到位',
      buffer_stopper_02_extend_sensor_02:	'接驳位挡板后伸出到位',
      buffer_stopper_02_retract_sensor_02:	'接驳位挡板后缩回到位',
      pl_f_guide_home_sensor:	'前导向条原点',
      right_buffer_safe_sensor:	'右RGV举升电池安全',
      liq_level_warning:	'平台液位检测',
      pl_door_01_close_sensor:	'左开合门关到位',
      pl_door_02_close_sensor:	'右开合门关到位',
      pl_door_01_open_sensor:	'左开合门开到位',
      pl_door_02_open_sensor:	'右开合门开到位',
      maintain_area_safety_01:	'维护门安全继电器反馈',
      maintain_area_safety_02:	'维护门传感器',
      left_buffer_safe_sensor:	'左RGV举升电池安全',
      pl_r_guide_home_sensor:	'后导向条原点',
      pl_buffer_dece_sensor_1:	'左缓存位电池减速',
      pl_buffer_sensor_f_1:	'左缓存位前电池到位',
      pl_buffer_sensor_r_1:	'左缓存位后电池到位',
      stacker_lift_up_limit_sensor:	'堆垛机升降上限位',
      stacker_lift_down_limit_sensor:	'堆垛机升降下限位',
      lr_lift_up_limit_sensor:	'加解锁平台升降上限位',
      lr_lift_home_sensor:	'加解锁平台升降原点',
      stacker_move_f_limit_sensor:	'堆垛机行走前限位',
      stacker_move_r_limit_sensor:	'堆垛机行走后限位'
    },
    7: {
      bc_slot1_reached_sensor: 'C1仓前电池落到位',
      bc_slot2_reached_sensor: 'C2仓前电池落到位',
      bc_slot3_reached_sensor: 'C3仓前电池落到位',
      bc_slot1_smoke_sensor: 'C1仓烟感报警',
      bc_slot2_smoke_sensor: 'C2仓烟感报警',
      bc_slot3_smoke_sensor: 'C3仓烟感报警',
      stacker_home_brake: '堆垛机防坠器回零',
      bc_slot4_reached_sensor: 'C4仓前电池落到位',
      bc_slot5_reached_sensor: 'C5仓前电池落到位',
      bc_slot6_reached_sensor: 'C6仓前电池落到位',
      bc_slot4_smoke_sensor: 'C4仓烟感报警',
      bc_slot5_smoke_sensor: 'C5仓烟感报警',
      bc_slot6_smoke_sensor: 'C6仓烟感报警',
      bc_slot10_lc_retract_sensor: 'A4仓水插头缩回到位',
      bc_slot11_lc_retract_sensor: 'A5仓水插头缩回到位',
      bc_slot12_lc_retract_sensor: 'A6仓水插头缩回到位',
      bc_slot10_reached_sensor: 'A4仓前电池落到位',
      bc_slot11_reached_sensor: 'A5仓前电池落到位',
      bc_slot12_reached_sensor: 'A6仓前电池落到位',
      bc_slot10_lc_cooling_swicth: 'A4仓水冷流量开关',
      bc_slot11_lc_cooling_swicth: 'A5仓水冷流量开关',
      bc_slot12_lc_cooling_swicth: 'A6仓水冷流量开关',
      Fire_bunker_1_extend_sensor: '消防前推杆伸出到位',
      Fire_bunker_1_retract_sensor: '消防前推杆缩回到位',
      bc_slot11_smoke_sensor: 'A5仓烟感报警',
      bc_slot12_smoke_sensor: 'A6仓烟感报警',
      bc_slot7_lc_retract_sensor: 'A1仓水插头缩回到位',
      bc_slot8_lc_retract_sensor: 'A2仓水插头缩回到位',
      bc_slot9_lc_retract_sensor: 'A3仓水插头缩回到位',
      bc_slot7_reached_sensor: 'A1仓前电池落到位',
      bc_slot8_reached_sensor: 'A2仓前电池落到位',
      bc_slot9_reached_sensor: 'A3仓前电池落到位',
      bc_slot7_lc_cooling_swicth: 'A1仓水冷流量开关',
      bc_slot8_lc_cooling_swicth: 'A2仓水冷流量开关',
      bc_slot9_lc_cooling_swicth: 'A3仓水冷流量开关',
      bc_slot7_smoke_sensor: 'A1仓烟感报警',
      bc_slot8_smoke_sensor: 'A2仓烟感报警',
      bc_slot9_smoke_sensor: 'A3仓烟感报警',
      bc_slot10_smoke_sensor: 'A4仓烟感报警',
      baffle_1_extend_sensor: '前端接驳上层伸出到位',
      baffle_1_retract_sensor: '前端接驳上层缩回到位',
      baffle_3_extend_sensor: '前端接驳下层伸出到位',
      baffle_3_retract_sensor: '前端接驳下层缩回到位',
      baffle_up_exist_sensor_f: '前端接驳上层电池有无',
      baffle_down_exist_sensor_f: '前端接驳下层电池有无',
      bc_lift_home_sensor_1: '前端接驳举升零点位',
      bc_lift_work_sensor_1: '前端接驳举升工作位',
      platfrom_water_check_1: '平台前段水位检测',
      platfrom_water_check_2: '平台后段水位检测',
      vehical_lift_lf_home_sensor: '左前磁栅尺零点位',
      RGV_S_home_sensor: 'S值调节零点位',
      vehical_lift_rf_home_sensor: '右前磁栅尺零点位',
      S_l_pin_work_sensor: 'S值左定位销伸出到位',
      vehical_lift_lr_home_sensor: '左后磁栅尺零点位',
      S_l_pin_home_sensor: 'S值左定位销缩回到位',
      vehical_lift_lf_home_brake: '左前防坠器零点到位',
      S_r_pin_work_sensor: 'S值右定位销伸出到位',
      vehical_lift_rf_home_brake: '右前防坠器零点到位',
      S_r_pin_home_sensor: 'S值右定位销缩回到位',
      vehical_lift_lr_home_brake: '左后防坠器零点到位',
      vehical_lift_rr_home_brake: '右后防坠器零点到位',
      RGV_lift_home_sensor: 'RGV升降零点位',
      RGV_move_work_sensor: 'RGV行走平台到位',
      RGV_move_home_sensor: 'RGV行走接驳位到位',
      RGV_reach_sensor_1: '左前电池平整',
      RGV_reach_sensor_2: '右前电池平整',
      RGV_reach_sensor_3: '左后电池平整',
      RGV_reach_sensor_4: '右后电池平整',
      lf_pin_home_sensor: '左前车身定位销下降到位',
      lf_pin_touch_sensor: '左前车身定位销接触车身',
      rr_pin_home_sensor: '右后车身定位销下降到位',
      rr_pin_touch_sensor: '右后车身定位销接触车身',
      bc_slot1_ec_retract_sensor: 'C1仓电插头缩回到位',
      bc_slot2_ec_retract_sensor: 'C2仓电插头缩回到位',
      bc_slot3_ec_retract_sensor: 'C3仓电插头缩回到位',
      bc_slot4_ec_retract_sensor: 'C4仓电插头缩回到位',
      bc_slot5_ec_retract_sensor: 'C5仓电插头缩回到位',
      bc_slot6_ec_retract_sensor: 'C6仓电插头缩回到位',
      door_open_sensor: '翻转门打开到位',
      door_close_sensor: '翻转门关闭到位',
      vehical_lift_rr_home_sensor: '右后磁栅尺零点位',
      baffle_2_extend_sensor: '后端接驳上层伸出到位',
      baffle_2_retract_sensor: '后端接驳上层缩回到位',
      baffle_4_extend_sensor: '后端接驳下层伸出到位',
      baffle_4_retract_sensor: '后端接驳下层缩回到位',
      baffle_up_exist_sensor_r: '后端接驳上层电池有无',
      baffle_down_exist_sensor_r: '后端接驳下层电池有无',
      bc_lift_home_sensor_2: '后端接驳举升零点位',
      bc_lift_work_sensor_2: '后端接驳举升工作位',
      stacker_lift_1_sensor: '堆垛机层仓对接1层',
      stacker_lift_2_sensor: '堆垛机层仓对接2层',
      stacker_lift_3_sensor: '堆垛机层仓对接3层',
      stacker_lift_4_sensor: '堆垛机层仓对接4层',
      stacker_lift_5_sensor: '堆垛机层仓对接5层',
      stacker_lift_6_sensor: '堆垛机层仓对接6层',
      stacker_lift_home_sensor: '堆垛机零点位',
      stacker_pin_1_sensor: '堆垛机维护插销1',
      stacker_pin_2_sensor: '堆垛机维护插销2',
      fork_work_sensor_1: '货叉叉臂左到位',
      fork_work_sensor_3: '货叉叉臂右到位',
      fork_home_sensor_1: '货叉主叉臂中点',
      fork_home_sensor_2: '货叉辅叉臂中点',
      fork_exist_sensor_1: '货叉有电池检测1',
      fork_exist_sensor_2: '货叉有电池检测2',
      bc_safe_sensor_1: '货叉上层左超程',
      bc_safe_sensor_2: '货叉上层右超程',
      bc_slot7_ec_retract_sensor: 'A1仓电插头缩回到位',
      bc_slot8_ec_retract_sensor: 'A2仓电插头缩回到位',
      bc_slot9_ec_retract_sensor: 'A3仓电插头缩回到位',
      bc_fire_water_check_1: '消防储水箱液位检测',
      bc_slot10_ec_retract_sensor: 'A4仓电插头缩回到位',
      bc_slot11_ec_retract_sensor: 'A5仓电插头缩回到位',
      bc_slot12_ec_retract_sensor: 'A6仓电插头缩回到位',
      Fire_bunker_2_extend_sensor: '消防仓后段电推杆伸出到位',
      Fire_bunker_2_retract_sensor: '消防仓后段电推杆缩回到位',
      bc_slot13_reached_sensor: '消防仓接驳电池落到位',
      bc_fire_water_check_2: '消防水槽液位检测',
      bc_fire_water_T_check: '消防水槽温度检测',
      pl_move_f_limit_sensor: 'RGV行走伺服前限位',
      pl_move_r_limit_sensor: 'RGV行走伺服后限位',
      lr_lift_up_limit_sensor: 'RGV举升伺服上限位',
      lr_lift_down_limit_sensor: 'RGV举升伺服下限位',
      fork_X_left_limit_sensor: '堆垛机货叉伺服左限位',
      fork_X_right_limit_sensor: '堆垛机货叉伺服右限位',
      stacker_lift_up_limit_sensor: '堆垛机升降伺服上限位',
      stacker_lift_down_limit_sensor: '堆垛机升降伺服下限位',
      maintain_area_safety_01: '左维护门安全检测',
      maintain_area_safety_02: '右维护门安全检测',
      fire_door_check_1: '消防应急门开合传感器检测前',
      fire_door_check_2: '消防应急门开合传感器检测后'
    }
  },

  diVarname: {
    all: '所有点',
    emergency_stop_switch_01: '急停',
    liq_level_warning: '液位报警',
    I_power_st_1: '扩展箱控制供电脱扣报警',
    I_power_st_2: '扩展箱动力供电脱扣报警',
    I_power_st_3: '堆垛机货叉伺服供电脱扣报警',
    I_power_st_4: '堆垛机行走伺服供电脱扣报警',
    I_power_st_5: '堆垛机升降伺服供电脱扣报警',
    I_power_st_6: '刹车开关电源后端配电脱扣报警',
    I_power_st_7: '前轮推杆伺服供电脱扣报警',
    I_power_st_8: '后轮推杆伺服供电脱扣报警',
    I_power_st_9: 'V槽&导向条伺服供电脱扣报警',
    I_power_st_10: '车身定位销伺服供电脱扣报警',
    I_power_st_11: '拧紧枪1 伺服供电脱扣报警',
    I_power_st_12: '拧紧枪2 伺服供电脱扣报警',
    I_power_st_13: '拧紧枪3&4 伺服供电脱扣报警',
    I_power_st_14: '拧紧枪5&6 伺服供电脱扣报警',
    I_power_st_15: '拧紧枪7&8 伺服供电脱扣报警',
    I_power_st_16: '拧紧枪9 伺服供电脱扣报警',
    I_power_st_17: '拧紧枪10 伺服供电脱扣报警',
    I_power_st_18: '拧紧枪11 伺服供电脱扣报警',
    I_power_st_19: '拧紧枪12 伺服供电脱扣报警',
    I_power_st_20: '开阖门伺服供电脱扣报警',
    I_power_st_21: 'RGV伺服供电脱扣报警',
    I_power_st_22: '车辆举升伺服供电脱扣报警',
    I_power_st_23: '接驳位举升伺服供电脱扣报警',
    I_power_st_24: '变频器供电脱扣报警',
    A01_A1_check: 'A01A1检测',
    A01_A2_check: 'A01A2检测',
    A01_A03_check: 'A01A3检测',
    A01_A04_check: 'A01A4检测',
    A01_A05_check: 'A01A5检测',
    A01_A6_check: 'A01A6检测',
    A01_A7_check: 'A01A7检测',
    A01_A8_check: 'A01A8检测',
    A01_A9_check: 'A01A9检测',
    A01_A10_check: 'A01A10检测',
    A02_A1_module_status: 'A02A1模块状态',
    A02_A2_module_status: 'A02A2模块状态',
    A02_A3_module_status: 'A02A3模块状态',
    A02_A4_module_status: 'A02A4模块状态',
    A02_A5_module_status: 'A02A5模块状态',
    A02_A06_module_check: 'A02A6模块状态',
  },

  diVarnamePus4: {
    all: '所有点',
    emergency_stop_switch_01: '急停',
    trans_fire_stopper_retract: '一键转运消防仓落水',
    maintain_operate_swicth: '维护/运营模式',
    A01_A1_check: 'A01A1检测',
    I_power_st_1: '堆垛机升降伺服供电脱扣报警',
    I_power_st_2: '堆垛机货叉&行走伺服供电脱扣报警',
    I_power_st_3: '拧紧枪1&拧紧枪2&拧紧枪3供电脱扣报警',
    I_power_st_4: '拧紧枪4&拧紧枪5&拧紧枪6供电脱扣报警',
    I_power_st_5: '拧紧枪7&拧紧枪8&拧紧枪9供电脱扣报警',
    I_power_st_6: '拧紧枪10&拧紧枪11&拧紧枪12供电脱扣报警',
    I_power_st_7: '左开合门&右开合门&拧紧枪1升降&拧紧枪2升降&供电脱扣报警',
    I_power_st_8: '拧紧枪9升降&拧紧枪10升降&拧紧枪11升降&拧紧枪12升降供电脱扣报警',
    I_power_st_9: '左前电池定位销&左后电池定位销&前导向条&后导向条&供电脱扣报警',
    I_power_st_10: '左前车身定位销&左后车身定位销&右后车身定位销&供电脱扣报警',
    I_power_st_11: '左前轮推杆伺服&右前轮推杆伺服&左后轮推杆伺服&右后轮推杆伺服&供电脱扣报警',
    I_power_st_12: 'RGV行走&RGV举升供电脱扣报警',
    I_power_st_13: '变频器供电脱扣报警',
    I_water_press_up: '水流量压力值上限',
    I_power_st_14: '消防水泵供电脱扣报警',
    I_power_st_15: '消防伴热带供电脱扣报警',
    I_water_press_down: '水流量压力值下限',
    I_fire_pump_feedback: '消防水泵控制反馈',
    I_fire_heater_feedback: '消防伴热带控制反馈',
    I_fire_D_door: 'D箱消防门检测',
    I_fire_E_door: 'E箱消防门检测',
    I_heater_temp_up: '伴热带温度检测上限',
    I_heater_temp_down: '伴热带温度检测下限',
    I_flow_switch_up: '出口流量开关上限',
    I_flow_switch_down: '出口流量开关下限'
  },

  diVarnameFirefly1: {
    all: '所有点',
    I_power_st_1: '1~3拧紧枪伺服供电脱口报警',
    I_power_st_2: '4~6拧紧枪伺服供电脱口报警',
    I_power_st_3: '7~8拧紧枪伺服供电脱口报警',
    I_power_st_4: '左前/右前/左后四轮推杆伺服供电脱口报警',
    I_power_st_5: '右后四轮推杆&翻转门伺服供电脱口报警',
    I_power_st_6: 'RGV行走伺服供电脱口报警',
    I_power_st_7: 'RGV升降伺服供电脱口报警',
    I_power_st_8: '堆垛机货叉伺服供电脱口报警',
    I_power_st_9: '堆垛机升降伺服供电脱口报警',
    I_power_st_10: '液压油泵电机供电脱口报警',
    I_power_st_11: '加热棒电机供电脱口报警',
    I_power_st_12: 'S值调节供电脱口报警',
    I_power_st_13: '消防水泵电机供电脱扣报警',
    I_power_st_14: '伴热带辅助加热供电脱扣报警',
    I_power_st_15: '烟感供电脱扣报警',
    trans_fire_stopper_retract: '一键落水按钮',
    emergency_stop_switch_01: '急停反馈',
    battery_slot_area_check: '电池仓区域安全反馈',
    stacker_chain_check_1: '堆垛机链条检测前',
    stacker_chain_check_2: '堆垛机链条检测后',
    Maitaindoor_security_1: '维护门开合左侧传感器检测',
    Maitaindoor_security_2: '维护门开合右侧传感器检测',
    scanistor_di_err: '扫描仪报警',
    fire_door_check_1: '消防应急门开合传感器检测前',
    fire_door_check_2: '消防应急门开合传感器检测后',
    fire_spray_flow_nc_feedblack: '消防喷淋管路流量开关(NC)反馈',
    fire_spray_flow_no_feedblack: '消防喷淋管路流量开关(NO)反馈',
    fire_spray_T_limit_up_feedblack: '消防喷淋管路温度开关上限值反馈',
    fire_spray_T_limit_down_feedblack: '消防喷淋管路温度开关下限值反馈',
    fire_spray_P_limit_up_feedblack: '消防喷淋管路压力开关上限值反馈',
    fire_spray_P_limit_down_feedblack: '消防喷淋管路压力开关下限值反馈',
    fire_spray_power_F1_feedblack: '消防喷淋主电源F1断路器反馈',
    fire_spray_power_KM_feedblack: '消防喷淋水泵供电KM反馈',
    scanistor_safe_feedblack: '平台扫描仪运行保护反馈',
    carpet_safe_feedblack: '平台地毯运行保护反馈',
    battery_slot_area_KM_feedblack: '电池仓区域伺服动力电源KM反馈',
    platfrom_area_KM_feedblack: '停车平台伺服动力电源KM反馈',
    maintain_operate_swicth: '运营模式切换',
    platfrom_door_safe_check_feedblack: '停车平台开阖门检测安全反馈',
    hydraulic_cylinder_water_low_err: '液压油缸低液位报警',
    hydraulic_cylinder_water_jam_err: '液压油缸液位堵塞报警',
    I_power_st_16: '消防水槽&储水箱辅助加热供电脱扣报警'
  },

  converter: {
    1: {},
    2: {
      1: '滚筒变频器',
      2: '提升机变频器',
      3: '左仓变频器',
      4: '右仓变频器',
      5: '接驳 & 缓存位变频器',
    },
    3: {
      1: '接驳位滚筒变频器',
      2: '停车平台右缓存位变频器',
      3: '停车平台滚筒变频器',
      4: '停车平台左缓存位变频器',
    },
    4: {
      1: '接驳位链条变频器',
      2: '停车平台滚筒变频器',
      3: '缓存位链条变频器'
    }
  },
};
