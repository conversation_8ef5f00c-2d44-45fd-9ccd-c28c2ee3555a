export const tankTrans = {
  pp_releated_slot: '相关仓位',
  pp_trans_duration: '倒仓时长',
  pp_full_charged_slot: '满电仓位号',
  pp_drianed_slot: '亏电仓位号',
  pp_empty_slot: '空电仓位号',
  pp_tank_id: '倒仓ID',

  tank_step: {
    1: '启动倒仓',
    2: '满电仓电池出仓',
    3: '电池从满电仓进无电池仓,无电池仓插电插头',
    4: '亏电仓电池出仓',
    5: '电池从亏电仓进满电仓,满电仓插水电插头',
    6: '堆垛机初始化,倒仓结束',
  },

  tank_axis: {
    1: '货叉',
    2: '堆垛机平移',
    3: '堆垛机升降',
  },
};
