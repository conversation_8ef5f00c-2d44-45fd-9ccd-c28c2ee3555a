export const faultDiagnosis = {
  pp_fault_module: '故障模块',
  pp_fault_type: '故障类型',
  pp_can_cause_shutdown: '是否引起停机',

  pp_fault_id: '故障ID',
  pp_request_id: '请求ID',

  pp_snapshot_instant_data: '快照瞬时数据',
  pp_mode: '工作模式',
  pp_charging_elapsed: '充电时间',
  pp_integral_electricity: '积分电量',
  pp_meter_electricity: '电表电量',
  pp_limit_power: '限制功率',
  pp_status_of_charge: '电池SOC',
  pp_status_of_pcu: 'pcu状态',
  pp_communication_count_of_pcu: 'pcu通讯计数',
  pp_raw_request_voltage_of_bms: 'bms原始请求电压',
  pp_raw_request_current_of_bms: 'bms原始请求电流',
  pp_limited_request_voltage: '限制后请求电压',
  pp_limited_request_current: '限制后请求电流',
  pp_input_pcu_voltage_of_SCT: 'SCT写PCU电压',
  pp_input_pcu_current_of_SCT: 'SCT写PCU电流',
  pp_output_pcu_voltage_of_SCT: 'SCT读PCU电压',
  pp_output_pcu_current_of_SCT: 'SCT读PCU电流',
  pp_measure_voltage_of_insulation_module: '绝缘模块测量电压',
  pp_measure_current_of_insulation_module: '绝缘模块测量电流',
  pp_measure_voltage_of_decm_module: '电表测量电压',
  pp_measure_current_of_decm_module: '电表测量电流',
  pp_temperature_of_gun_head_plus: '枪头+温度',
  pp_temperature_of_gun_head_minus: '枪头-温度',
  pp_inlet_temperature: '进液口温度',
  pp_outlet_temperature: '出液口温度',
  pp_temperature_of_gun_tail_plus: '枪尾+温度',
  pp_temperature_of_gun_tail_minus: '枪尾-温度',
  pp_temperature_of_bronze_medal_plus: '铜牌+温度',
  pp_temperature_of_bronze_medal_minus: '铜牌-温度',
  pp_air_inlet_temperature: '进风口温度',
  pp_ambient_temperature: '环境温度',
  pp_coolant_pressure: '冷却液压力',
  pp_cooling_fan_duty_cycle: '散热风扇占空比',
  pp_rotate_speed_of_fan1: '风扇1转速',
  pp_rotate_speed_of_fan2: '风扇2转速',
  pp_pump_duty_cycle: '水泵占空比',
  pp_rotate_speed_of_pump: '水泵转速',

  pp_snapshot_result: '快照诊断结论',
  pp_diagnosis_result: '诊断结果',
  pp_device_log: '设备日志',

  diagnosisMode: {
    0: '空闲',
    1: '配置',
    3: '充电中',
    4: '充电结束',
    5: '充电故障',
  },

  state_of_pcu: {
    0: '正常',
    1: '故障',
  },
};
