<!--
 * @Author: zhenxing.chen
 * @Date: 2023-01-29 18:36:41
 * @LastEditors: zhenxing.chen <EMAIL>
 * @LastEditTime: 2024-07-30 23:03:55
 * @Email: <EMAIL>
 * @Description: 请填写简介
-->
<template>
  <div class="app-layout">
    <Logout v-if="showOut"></Logout>
    <!-- <div v-if="updateAuth" class="app-layout-container"> -->
    <div class="app-layout-header">
      <Header />
    </div>
    <div class="app-layout-container">
      <Side />
      <el-config-provider :locale="currentLocale">
        <!-- 加入路由动画开始 -->
        <router-view
          v-slot="{ Component }"
          class="app-content-container clearfix"
          :class="{ 'is-collapse': isCollapse }"
        >
          <transition :name="'el-fade-in'">
            <keep-alive :include="[]">
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
        <!-- 路由动画结束 -->
      </el-config-provider>
    </div>
    <!-- <div v-else-if="!auth" class="app-layout-container">
      <div class="no-auth-tips">
        权限校验失败， 请联系黄玺元xiyuan.huang申请
      </div>
    </div>
    <div v-else class="app-layout-container">
      <div class="no-auth-tips">权限验证中...</div>
    </div> -->
    <WelkinBackTop />
  </div>
</template>

<script lang="ts" setup>
import { NioApmPlus } from '@nio-fe/nio-apm-plus'; // 埋点监控
import { computed, onBeforeMount, watch, ref } from 'vue';
import { ElConfigProvider } from 'element-plus';
import enLocale from 'element-plus/lib/locale/lang/en';
import zhLocale from 'element-plus/lib/locale/lang/zh-cn';
import { getLocale, getUserId } from './utils';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import Logout from '~/views/components/logout.vue';
const store = useStore();

const isCollapse = ref(computed(() => store.state.menus.collapse));
// element-plus国际化
const { locale } = useI18n({ useScope: 'global' });
let lang = getLocale();
let currentLocale = lang == 'zh' ? ref(zhLocale) : ref(enLocale);

watch(
  () => locale.value,
  (newValue, oldValue) => {
    console.log('newValue, oldValue', newValue, oldValue);
    if (newValue === 'en') {
      currentLocale.value = enLocale;
    } else {
      currentLocale.value = zhLocale;
    }
  }
);

const showOut = computed(() => store.state.user.showLogout);

const transition = ref('el-fade-in');
onBeforeMount(() => {
  // 统一埋点&性能监控
  const user_id = getUserId();
  if (location.host !== 'pp-welkin.nioint.com') return;
  NioApmPlus.createDefaultInstance({
    aid: 690599833, // apm app id，类型为 number !!!
    token: 'bb4d1c85d6f14eb68cf934104adc457e', // apm app token
    userId: user_id || '',
    plugins: {
      pageview: {
        routeMode: 'history', // 默认监听 history 变动
      },
      // 注意: 多个 APM 实例会导致重复上报!!!
      autoReportClickEvent: {
        enable: true,
      },
    },
  });
});

// let updateAuth = true;
// let auth = true;
</script>

<style lang="scss">
@import './app.scss';
.app-layout {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.app-layout-header {
  width: 100%;
  height: 48px;
  border-bottom: solid 1px var(--el-menu-border-color);
  display: flex;
  justify-content: space-between;
}

.app-layout-container {
  width: 100%;
  height: calc(100% - 48px);
}

.app-content-container {
  overflow-y: auto;
  // width: 100%;
  // padding: 30px 30px;
  // background-color: #f5f7fa;
  // flex: 1;
  float: left;
  height: 100%;
  width: calc(100% - 210px);
}
.is-collapse {
  width: calc(100% - 64px);
}

.no-auth-tips {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>
