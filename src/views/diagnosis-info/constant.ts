import {i18n} from '~/i18n'


export const getShortcuts = () => {
  const shortcuts = [
    {
      text: i18n.global.t('common.pp_last_hour'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_24_hours'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    }
  ]
  return shortcuts
}

export const levelOptions = [
  {
    label: 'P1',
    value: '1'
  },
  {
    label: 'P2',
    value: '2'
  },
  {
    label: 'P3',
    value: '3'
  }
]

export const levelMap = {
  1: {
    name: 'P1',
    color: '#FF0000'
  },
  2: {
    name: 'P2',
    color: '#FFA500'
  },
  3: {
    name: 'P3',
    color: '#0000FF'
  }
}

export const modeMap = {
  0: 'diagnosisInfo.pp_periodic_upload',
  1: 'diagnosisInfo.pp_variable_upload'
}

export const typeOptions = [
  {
    label: 'diagnosisInfo.pp_timeout_command',
    value: 'timeout_command'
  },
  {
    label: 'diagnosisInfo.pp_scheduled_time_not_start',
    value: 'scheduled_time_not_start'
  },
  {
    label: 'diagnosisInfo.pp_fail_uploading_service',
    value: 'fail_uploading_service'
  },
  {
    label: 'diagnosisInfo.pp_start_without_s2_close',
    value: 'start_without_s2_close'
  },
  {
    label: 'diagnosisInfo.pp_cp_status',
    value: 'cP_status'
  },
  {
    label: 'diagnosisInfo.pp_text',
    value: 'text'
  }
]

export const typeMap = {
  timeout_command: {
    label: 'diagnosisInfo.pp_timeout_command',
    value: 'TimeoutCommand'
  },
  scheduled_time_not_start: {
    label: 'diagnosisInfo.pp_scheduled_time_not_start',
    value: 'ScheduledTimeNotStart'
  },
  fail_uploading_service: {
    label: 'diagnosisInfo.pp_fail_uploading_service',
    value: 'FailUploadingService'
  },
  start_without_s2_close: {
    label: 'diagnosisInfo.pp_start_without_s2_close',
    value: 'StartWithoutS2Close'
  },
  cP_status: {
    label: 'diagnosisInfo.pp_cp_status',
    value: 'CPStatus'
  },
  text: {
    label: 'diagnosisInfo.pp_text',
    value: 'TextProperties'
  }
}

export const authTypeMap = {
  0: 'diagnosisInfo.pp_auto_auth',
  1: 'diagnosisInfo.pp_manual_auth',
  2: 'diagnosisInfo.pp_none_auth'
}

export const commandMap = {
  0: 'SHUTDOWN',
  1: 'REBOOT',
  2: 'ENABLE_CONNECTOR',
  3: 'DISABLE_CONNECTOR',
  4: 'START_CHARGING',
  5: 'STOP_CHARGING',
  6: 'STARTUP',
  7: 'POWER_OFF',
  8: 'RESET',
  9: 'CLEAR_ALARM',
  10: 'MANUAL_AUTHENTICATION',
  11: 'ENABLE_BATTERY',
  12: 'DISABLE_BATTERY',
  13: 'REVERSE_BATTERY',
  14: 'RELEASE_BATTERY'
}
