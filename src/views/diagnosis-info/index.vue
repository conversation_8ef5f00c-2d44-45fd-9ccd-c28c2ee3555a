<template>
  <div class="diagnosis-info-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_info_trace') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_diagnosis_info') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_diagnosis_info') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" inline style="width: 100%">
          <el-row :gutter="20">
            <el-col :span="9">
              <el-form-item :label="$t('diagnosisInfo.pp_time_frame')" class="upper-form-item width-full">
                <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item :label="$t('deviceManagement.pp_device')" class="upper-form-item width-full">
                <el-select v-model="form.device_id" clearable filterable remote class="width-full" :placeholder="$t('diagnosisInfo.pp_keywords')" :remote-method="remoteMethod" :loading="remoteLoading">
                  <el-option v-for="item in deviceOptions" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('diagnosisInfo.pp_diagnosis_level')" class="upper-form-item width-full">
                <el-select v-model="form.levels" clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in levelOptions" :key="item.value" :value="item.value" :label="item.label" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="9">
              <el-form-item :label="$t('diagnosisInfo.pp_diagnosis_type')" class="upper-form-item width-full">
                <el-select v-model="form.diagnosis_type" clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in typeOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="15">
              <el-form-item class="upper-form-item width-full">
                <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
                <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="swap-table-container">
        <el-table :data="list" style="width: 100%" :header-cell-style="{fontSize: '14px', color: '#292C33', cursor: 'auto'}" v-loading="loading">
          <el-table-column prop="timestamp" :label="$t('diagnosisInfo.pp_upload_time')" min-width="170" show-overflow-tooltip>
            <template #default="{row}">
              <span>{{ formatTime(row.timestamp) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="device_id" :label="$t('diagnosisInfo.pp_device_id')" min-width="200">
            <template #default="{row}">
              <WelkinCopyBoard :text="row.device_id" />
            </template>
          </el-table-column>

          <el-table-column prop="level" :label="$t('diagnosisInfo.pp_diagnosis_level')" min-width="90" show-overflow-tooltip>
            <template #default="{row}">
              <div class="status-container">
                <Icon icon="ep:warn-triangle-filled" :style="{fontSize: '18px', color: levelMap[row.level].color}"></Icon>
                <span class="status-text">{{ levelMap[row.level].name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="mode" :label="$t('diagnosisInfo.pp_upload_mode')" min-width="110" show-overflow-tooltip>
            <template #default="{row}">
              <span>{{ $t(modeMap[row.mode]) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="diagnosis_type" :label="$t('diagnosisInfo.pp_diagnosis_type')" min-width="260" show-overflow-tooltip>
            <template #default="{row}">
              <span>{{ row.diagnosis_type ? getDiagnosisType(row.diagnosis_type) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" :label="$t('diagnosisInfo.pp_detail')" min-width="100" class-name="operation-column">
            <template #default="{row}">
              <el-icon color="#00bebe" class="operation-icon" @click="handleClickView(row)"><View /></el-icon>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>

      <el-dialog v-model="detailDialogVisible" width="700px" align-center :close-on-press-escape="false" :show-close="false">
        <template #header>
          <div class="flex-box flex_j_c-space-between flex_a_i-center">
            <span>{{ $t('diagnosisInfo.pp_diagnosis_detail') }}</span>
            <div class="status-container">
              <Icon icon="ep:warn-triangle-filled" :style="{fontSize: '18px', color: levelMap[detailList.level].color}"></Icon>
              <span class="status-text">{{ levelMap[detailList.level].name }}</span>
            </div>
          </div>
        </template>

        <el-row :gutter="20" class="margin_b-20">
          <el-col :span="12">
            <span class="title margin_r-20">{{ $t('diagnosisInfo.pp_device_id') }}</span>
            <span>{{ detailList.device_id }}</span>
          </el-col>
          <el-col :span="12">
            <span class="title margin_r-20">{{ $t('diagnosisInfo.pp_upload_time') }}</span>
            <span>{{ formatTime(detailList.timestamp) }}</span>
          </el-col>
        </el-row>

        <div class="flex-box flex_d-column gap_15">
          <el-card v-for="item in detailList.diagnosis_type">
            <div class="title font-weight-bold margin_b-10">
              <span>{{ $t(typeMap[item].label) }}&nbsp;</span>
              <span v-if="lang == 'zh'">({{ typeMap[item].value }})</span>
            </div>

            <!-- 命令超时 -->
            <div v-if="item == 'timeout_command'" class="flex-box flex_d-column gap_8">
              <el-row :gutter="20">
                <el-col :span="6">{{ $t('diagnosisInfo.pp_control_command') }}</el-col>
                <el-col :span="5">{{ commandMap[detailList['timeout_command_properties'].remote_control_command] }}</el-col>
                <el-col :span="6">{{ $t('diagnosisInfo.pp_command_time') }}</el-col>
                <el-col :span="7">{{ formatTime(detailList['timeout_command_properties'].command_timestamp) }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">{{ $t('diagnosisInfo.pp_restart_count') }}</el-col>
                <el-col :span="5">{{ detailList['timeout_command_properties'].reboot_count }}</el-col>
                <el-col :span="6">{{ $t('diagnosisInfo.pp_the_last_restart_time') }}</el-col>
                <el-col :span="7">{{ formatTime(detailList['timeout_command_properties'].reboot_timestamp) }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">{{ $t('diagnosisInfo.pp_associated_order') }}</el-col>
                <el-col :span="18">{{ detailList['timeout_command_properties'].order_id }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">{{ $t('diagnosisInfo.pp_diagnosis_result') }}</el-col>
                <el-col :span="18">{{ detailList['timeout_command_properties'].result }}</el-col>
              </el-row>
            </div>

            <!-- 预约充电未启动 -->
            <div v-else-if="item == 'scheduled_time_not_start'" class="flex-box flex_d-column gap_8">
              <el-row :gutter="20">
                <el-col :span="6">{{ $t('diagnosisInfo.pp_reservation_mode') }}</el-col>
                <el-col :span="5">{{ detailList['scheduled_time_not_start_properties'].is_order_mode_charging }}</el-col>
                <el-col :span="6">{{ $t('diagnosisInfo.pp_authentication_success_time') }}</el-col>
                <el-col :span="7">{{ formatTime(detailList['scheduled_time_not_start_properties'].auth_ok_timestamp) }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">{{ $t('diagnosisInfo.pp_authentication_type') }}</el-col>
                <el-col :span="5">{{ $t(authTypeMap[detailList['scheduled_time_not_start_properties'].auth_type]) }}</el-col>
                <el-col :span="6">{{ $t('diagnosisInfo.pp_successfully_authenticated') }}</el-col>
                <el-col :span="7">{{ detailList['scheduled_time_not_start_properties'].has_auth_msg }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">{{ $t('diagnosisInfo.pp_diagnosis_result') }}</el-col>
                <el-col :span="18">{{ detailList['scheduled_time_not_start_properties'].result }}</el-col>
              </el-row>
            </div>

            <!-- 上传服务小结失败 -->
            <div v-else-if="item == 'fail_uploading_service'" class="flex-box flex_d-column gap_8">
              <el-row :gutter="20">
                <el-col :span="5">{{ $t('diagnosisInfo.pp_start_service_time') }}</el-col>
                <el-col :span="7">{{ formatTime(detailList['fail_uploading_service_properties'].start_report_finish_timestamp) }}</el-col>
                <el-col :span="5">{{ $t('diagnosisInfo.pp_service_end_time') }}</el-col>
                <el-col :span="7">{{ formatTime(detailList['fail_uploading_service_properties'].over_report_finish_timestamp) }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="5">{{ $t('diagnosisInfo.pp_diagnosis_result') }}</el-col>
                <el-col :span="19">{{ detailList['fail_uploading_service_properties'].result }}</el-col>
              </el-row>
            </div>

            <!-- 启动时S2未闭合 -->
            <div v-else-if="item == 'start_without_s2_close'" class="flex-box flex_d-column gap_8">
              <el-row :gutter="20">
                <el-col :span="7">{{ $t('diagnosisInfo.pp_output_PWM_signals') }}</el-col>
                <el-col :span="4">{{ detailList['start_without_s2_close_properties'].is_pwm_out }}</el-col>
                <el-col :span="6">{{ $t('diagnosisInfo.pp_PWM_output_time') }}</el-col>
                <el-col :span="7">{{ formatTime(detailList['start_without_s2_close_properties'].pwm_out_timestamp) }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="7">{{ $t('diagnosisInfo.pp_vehicle_side_S2_closed') }}</el-col>
                <el-col :span="4">{{ detailList['start_without_s2_close_properties'].is_s2_close }}</el-col>
                <el-col :span="6">{{ $t('diagnosisInfo.pp_S2_close_time') }}</el-col>
                <el-col :span="7">{{ formatTime(detailList['start_without_s2_close_properties'].s2_close_timestamp) }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="7">{{ $t('diagnosisInfo.pp_diagnosis_result') }}</el-col>
                <el-col :span="17">{{ detailList['start_without_s2_close_properties'].result }}</el-col>
              </el-row>
            </div>

            <!-- CP信号状态异常 -->
            <div v-else-if="item == 'cP_status'" class="flex-box flex_d-column gap_8">
              <el-row :gutter="20">
                <el-col :span="8">{{ $t('diagnosisInfo.pp_current_status_or_sample_value') }}</el-col>
                <el-col :span="16">{{ detailList['cP_status_properties'].cp_status }}</el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">{{ $t('diagnosisInfo.pp_diagnosis_result') }}</el-col>
                <el-col :span="16">{{ detailList['cP_status_properties'].result }}</el-col>
              </el-row>
            </div>

            <!-- 日志文本 -->
            <div v-else-if="item == 'text'" class="flex-box flex_d-column gap_8">
              <el-row :gutter="20">
                <el-col :span="24">{{ detailList['text_properties'].text }}</el-col>
              </el-row>
            </div>
          </el-card>

          <div class="flex-box flex_j_c-center">
            <el-button @click="detailDialogVisible = false" class="welkin-primary-button">{{ $t('common.pp_close') }}</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onBeforeMount} from 'vue'
import {useI18n} from 'vue-i18n'
import {useRoute, useRouter} from 'vue-router'
import {getShortcuts, levelOptions, typeOptions, levelMap, modeMap, typeMap, authTypeMap, commandMap} from './constant'
import {ElMessage} from 'element-plus'
import {View} from '@element-plus/icons-vue'
import {Icon} from '@iconify/vue/dist/iconify'
import {debounce} from 'lodash-es'
import {page} from '~/constvars/page'
import {apiGetDiagnosisList, apiGetGrootList} from '~/apis/diagnosis-info'
import {getDisabledDate, formatTime, clearJson, removeNullKeys} from '~/utils'
import _ from 'lodash'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const ruleFormRef = ref()
const loading = ref(false)
const remoteLoading = ref(false)
const detailDialogVisible = ref(false)
const lang = ref('' as any)
const deviceOptions = ref([] as any)
const list = ref([] as any)
const detailList = ref({} as any)
const pages = ref(_.cloneDeep(page))
const datePicker = ref([new Date().getTime() - 3600 * 1000, new Date().getTime()] as any)
const form = ref({
  start_time: new Date().getTime() - 3600 * 1000,
  end_time: new Date().getTime(),
  device_id: '',
  levels: '',
  diagnosis_type: ''
})
const searchForm = ref({})

/**
 * @description: 点击查看详情
 * @param {*} row
 * @return {*}
 */
const handleClickView = (row: any) => {
  detailList.value = _.cloneDeep(row)
  lang.value = localStorage.getItem('locale')
  detailDialogVisible.value = true
}

/**
 * @description: 表格中的诊断类型映射
 * @param {*} typeArr
 * @return {*}
 */
const getDiagnosisType = (typeArr: any) => {
  const matchArr = typeArr.map((item: any) => t(typeOptions.filter((type: any) => type.value == item)[0].label))
  return matchArr.join('、')
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = _.cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  removeNullKeys(formData)
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: formData
    })
  }
  loading.value = true
  try {
    const res = await apiGetDiagnosisList(formData)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = {keyword: val, limit: 100}
    const res = await apiGetGrootList(params)
    deviceOptions.value = res.data
    remoteLoading.value = false
  }
}, 500)

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  datePicker.value = [new Date().getTime() - 3600 * 1000, new Date().getTime()]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = _.cloneDeep(form.value)
  getList()
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value[0] = form.value.start_time
    datePicker.value[1] = form.value.end_time
  }
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
  form.value.levels = !!initParams.levels ? initParams.levels : ''
  form.value.diagnosis_type = !!initParams.diagnosis_type ? initParams.diagnosis_type : ''
  searchForm.value = _.cloneDeep(form.value)
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.diagnosis-info-container {
  font-family: 'Noto Sans';
  .header-title {
    font-weight: bold !important;
  }
  .search-form-container {
    padding-bottom: 5px;
    :deep(.upper-form-item) {
      margin-bottom: 15px;
    }
  }
  .swap-table-container {
    :deep(.pagination-container .el-pagination) {
      margin-bottom: 20px;
    }
    .status-container {
      display: flex;
      align-items: center;
      line-height: 17px;
      .status-text {
        padding-left: 8px;
      }
    }
  }
  :deep(.el-dialog__header) {
    font-size: 16px;
    color: #22252b;
    font-weight: bold;
  }
  :deep(.el-dialog__body) {
    color: #828d9e;
    padding-top: 10px;
    padding-bottom: 20px;
    .el-card__body {
      padding: 15px 20px;
      .el-row {
        color: #828d9e;
      }
    }
    .title {
      color: #22252b;
    }
  }
}
</style>
