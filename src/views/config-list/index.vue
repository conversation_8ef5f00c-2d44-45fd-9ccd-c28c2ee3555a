<template>
  <div class="config-list-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_config_list') }}</div>
      <el-button type="primary" @click="handleClickAdd" class="width-108 height-32" style="padding: 5px 16px">
        <AddIcon />
        <span class="margin_l-4">{{ $t('configList.pp_create') }}</span>
      </el-button>
    </div>
    <div class="content-container">
      <div :class="['search-container', { 'loading-search-container': loading }]">
        <el-form :model="form" inline>
          <el-form-item :label="$t('configList.pp_config_name')">
            <el-input v-model="form.config_name" :placeholder="$t('common.pp_please_input')" class="width-full" clearable />
          </el-form-item>
          <el-form-item :label="$t('configList.pp_config_id')">
            <el-input v-model="form.config_id" :placeholder="$t('common.pp_please_input')" class="width-full" clearable />
          </el-form-item>
          <el-form-item :label="$t('configList.pp_creator')">
            <el-select v-model="form.creator" clearable filterable remote :placeholder="$t('stuckAnalysis.pp_user_placeholder')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
              <el-option v-for="item in userOptions" :key="item.employee_id" :value="item.worker_user_id" :label="item.name">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.worker_user_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button :loading="loading" type="primary" @click="handleSearch">{{ $t('common.pp_filter') }}</el-button>
            <el-button class="cancel-button" @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="table-container">
        <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column prop="config_name" :label="$t('configList.pp_config_name')" min-width="240" show-overflow-tooltip fixed>
            <template #default="{ row }">
              <span class="light-column" @click="handleClickName(row)">{{ row.config_name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="config_id" :label="$t('configList.pp_config_id')" min-width="180" show-overflow-tooltip />
          <el-table-column prop="remark" :label="$t('configList.pp_remark')" :min-width="remarkList.length > 0 ? 100 : 80" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.remark || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="project" :label="$t('configList.pp_device_type')" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span :style="{ color: projectMap[row.project].color }">{{ $t(projectMap[row.project].name) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="基于换电站" min-width="220" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.is_real_device">{{ row.description ? row.description : $t('common.pp_unnamed_device') }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="creator" :label="$t('configList.pp_creator')" :min-width="flexColumnWidth(creatorList, 100, 7)" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="flex-box flex_a_i-center">
                <span v-if="!row.creator">-</span>
                <div class="flex-box flex_a_i-center avatar-container" v-else>
                  <el-avatar size="small" :src="row.creator_avatar" />
                  <span class="margin_l-8">{{ row.creator }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="create_ts" :label="$t('configList.pp_create_time')" min-width="180" show-overflow-tooltip>
            <template #default="{ row }">
              <span> {{ formatTime(row.create_ts) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="update_ts" :label="$t('configList.pp_update_time')" min-width="180" show-overflow-tooltip>
            <template #default="{ row }">
              <span> {{ formatTime(row.update_ts) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.pp_operation')" width="140" fixed="right" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="flex-box flex_a_i-center gap_10">
                <el-tooltip content="编辑" placement="bottom">
                  <EditIcon @click="handleClickEdit(row)" :color="row.creator == user ? '#01A0AC' : '#BFBFBF'" :style="{ cursor: row.creator == user ? 'pointer' : 'not-allowed' }" />
                </el-tooltip>
                <el-tooltip content="删除" placement="bottom">
                  <DeleteIcon @click="handleClickDelete(row)" :color="row.creator == user ? '#01A0AC' : '#BFBFBF'" :style="{ cursor: row.creator == user ? 'pointer' : 'not-allowed' }" />
                </el-tooltip>
                <el-tooltip content="克隆" placement="bottom">
                  <CloneIcon @click="handleClickClone(row)" :color="row.creator == user ? '#01A0AC' : '#BFBFBF'" :style="{ cursor: row.creator == user ? 'pointer' : 'not-allowed' }" />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-box">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>

      <!-- 新建配置 -->
      <el-dialog v-model="createDialogVisible" :title="$t('configList.pp_create')" width="503px" class="create-dialog" align-center>
        <div class="flex-box flex_a_i-center margin_b-11">
          <div class="dialog-label">{{ $t('configList.pp_create_method') }}</div>
          <el-radio-group v-model="createMethod">
            <el-radio label="single">{{ $t('configList.pp_single_formula') }}</el-radio>
            <el-radio label="batch">{{ $t('configList.pp_batch_formula') }}</el-radio>
          </el-radio-group>
        </div>
        <div class="dialog-label margin_b-4">{{ $t('configList.pp_device_type') }}</div>
        <div class="flex-box flex_a_i-center gap_16">
          <SelectedPss2 v-if="createProject == 'PowerSwap2'" />
          <UnselectedPss2 v-else @click="createProject = 'PowerSwap2'" class="cursor-pointer" />

          <SelectedPss3 v-if="createProject == 'PUS3'" />
          <UnselectedPss3 v-else @click="createProject = 'PUS3'" class="cursor-pointer" />

          <SelectedPss4 v-if="createProject == 'PUS4'" />
          <UnselectedPss4 v-else @click="createProject = 'PUS4'" class="cursor-pointer" />
        </div>
        <div class="flex-box flex_j_c-flex-end margin_t-16">
          <el-button @click="createDialogVisible = false" class="text-button">{{ $t('common.pp_cancel') }}</el-button>
          <el-button @click="handleJumpToCreate" type="primary" style="margin-left: 4px">{{ $t('common.pp_confirm') }}</el-button>
        </div>
      </el-dialog>

      <!-- 删除配置 -->
      <el-dialog v-model="deleteDialogVisible" title="提示" @close="deleteDialogVisible = false" class="delete-dialog" width="360px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
        <div class="flex-box flex_a_i-flex-start gap_8 margin_b-16">
          <WarningIcon style="width: 14px; height: 14px; margin-top: 4px" />
          <span class="dialog-content">
            确认删除 <span style="color: #00bebe; font-weight: bold">{{ deleteConfigRow.config_name }}</span> 的仿真配置 ？
          </span>
        </div>
        <div class="flex-box flex_j_c-flex-end flex_a_i-center">
          <el-button @click="deleteDialogVisible = false" class="text-button">取消</el-button>
          <el-button type="primary" style="margin-left: 4px" :loading="deleteLoading" @click="handleConfirmDelete">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { useRoute, useRouter } from 'vue-router'
import { getUserId, flexColumnWidth, formatTime, removeNullProp, clearJson } from '~/utils'
import { apiGetConfigList, apiDeleteConfig } from '~/apis/config-list'
import { apiGetUserList } from '~/apis/run-list'
import { projectMap } from './components/constant'
import { ElMessage } from 'element-plus'
import AddIcon from '~/assets/svg/add.vue'
import EditIcon from './components/icon/edit.vue'
import CloneIcon from './components/icon/clone.vue'
import DeleteIcon from '~/assets/svg/delete.vue'
import SelectedPss2 from './components/icon/selected-pss2.vue'
import SelectedPss3 from './components/icon/selected-pss3.vue'
import SelectedPss4 from './components/icon/selected-pss4.vue'
import UnselectedPss2 from './components/icon/unselected-pss2.vue'
import UnselectedPss3 from './components/icon/unselected-pss3.vue'
import UnselectedPss4 from './components/icon/unselected-pss4.vue'
import WarningIcon from '../create-config/components/icon/warning-icon.vue'
import { debounce } from 'lodash-es'
import _ from 'lodash'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const remoteLoading = ref(false)
const deleteLoading = ref(false)
const createDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const createMethod = ref('single')
const createProject = ref('PowerSwap2')
const userOptions = ref([] as any)
const list = ref([] as any)
const remarkList = ref([] as any)
const creatorList = ref([] as any)
const form = ref({
  config_name: '',
  config_id: '',
  creator: '' as any
})
const searchForm = ref({} as any)
const deleteConfigRow = ref({} as any)
const pages = ref(_.cloneDeep(page))
const user = getUserId()

/**
 * @description: 点击克隆
 * @param {*} row
 * @return {*}
 */
const handleClickClone = (row: any) => {
  if (row.creator != user) return
  router.push({
    path: `/device-simulation/config-list/create-config`,
    query: { type: 'clone', method: 'single', project: row.project, configID: row.config_id, configName: row.config_name }
  })
}

/**
 * @description: 点击查看配置总览
 * @param {*} row
 * @return {*}
 */
const handleClickName = (row: any) => {
  router.push({
    path: `/device-simulation/config-list/create-config`,
    query: { type: 'view', method: 'single', project: row.project, configID: row.config_id, configName: row.config_name }
  })
}

/**
 * @description: 点击新增
 * @return {*}
 */
const handleClickAdd = () => {
  createMethod.value = 'single'
  createProject.value = 'PowerSwap2'
  createDialogVisible.value = true
}

/**
 * @description: 确认新增
 * @return {*}
 */
const handleJumpToCreate = () => {
  router.push({
    path: `/device-simulation/config-list/create-config`,
    query: { type: 'add', method: createMethod.value, project: createProject.value }
  })
}

/**
 * @description: 点击编辑
 * @param {*} row
 * @return {*}
 */
const handleClickEdit = (row: any) => {
  if (row.creator != user) return
  router.push({
    path: `/device-simulation/config-list/create-config`,
    query: { type: 'edit', method: 'single', project: row.project, configID: row.config_id, configName: row.config_name }
  })
}

/**
 * @description: 点击删除
 * @param {*} row
 * @return {*}
 */
const handleClickDelete = (row: any) => {
  if (row.creator != user) return
  deleteConfigRow.value = row
  deleteDialogVisible.value = true
}

/**
 * @description: 确认删除
 * @return {*}
 */
const handleConfirmDelete = async () => {
  deleteLoading.value = true
  try {
    const res = await apiDeleteConfig(deleteConfigRow.value.config_id)
    deleteLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      ElMessage.success('删除成功')
      deleteDialogVisible.value = false
      getList()
    }
  } catch (error) {
    deleteLoading.value = false
  }
}

/**
 * @description: 获取配置列表
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = _.cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullProp(formData), creator: formData.creator ? formData.creator : 'all' }
    })
  }
  loading.value = true
  try {
    const res = await apiGetConfigList(formData)
    loading.value = false
    list.value = res.data || []
    pages.value.total = res.total
    remarkList.value = res.data.filter((item: any) => item.remark)
    creatorList.value = [...new Set(res.data.map((item: any) => item.creator))]
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  form.value.creator = getUserId()
  getInitUser()
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = _.cloneDeep(form.value)
  getList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 远程搜索用户
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchUserList(query)
}
const searchUserList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { fuzzy_name: val }
    const res = await apiGetUserList(params)
    remoteLoading.value = false
    userOptions.value = res.data.people_list
  }
}, 500)

/**
 * @description: 获取用户列表
 * @return {*}
 */
const getInitUser = async () => {
  const params = { fuzzy_name: form.value.creator }
  const res = await apiGetUserList(params)
  userOptions.value = res.data.people_list
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.config_name = !!initParams.config_name ? initParams.config_name : ''
  form.value.config_id = !!initParams.config_id ? initParams.config_id : ''
  form.value.creator = initParams.creator == 'all' ? '' : initParams.creator ? initParams.creator : getUserId()
  searchForm.value = _.cloneDeep(form.value)
  if (form.value.creator) getInitUser()
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.config-list-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    background-color: #fff;
    :deep(.el-input__inner) {
      color: #262626;
    }
    .search-container {
      margin-bottom: 20px;
      :deep(.el-form) {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 128px;
        column-gap: 24px;
        .el-form-item {
          margin: 0;
          .el-form-item__label {
            font-size: 14px;
            color: #595959;
            padding-right: 8px;
          }
          .el-button + .el-button {
            margin-left: 8px;
            color: #01a0ac;
            border: 1px solid #00bebe;
          }
        }
      }
    }
    .loading-search-container {
      :deep(.el-form) {
        grid-template-columns: 1fr 1fr 1fr 148px;
      }
    }
    .table-container {
      :deep(.avatar-container) {
        border-radius: 16px;
        padding: 3px 12px 3px 4px;
        background: #e5f9f9;
      }
      :deep(.pagination-box) {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        .el-pagination__total {
          color: #595959;
        }
        .el-input__inner {
          color: #595959;
        }
        .el-pager li {
          background: #f0f0f0;
          color: #595959;
        }
        .el-pager li.is-active {
          background: #00bebe;
          color: #fff;
        }
        .el-pagination__jump {
          color: #595959;
        }
      }
    }
  }
  :deep(.create-dialog) {
    .el-dialog__header {
      padding: 24px 24px 11px;
      .el-dialog__title {
        color: #1f1f1f;
      }
    }
    .el-dialog__body {
      padding: 0 24px 24px;
    }
    .el-dialog__headerbtn .el-dialog__close {
      font-size: 20px;
      color: #262626;
    }
    .dialog-label {
      font-size: 14px;
      line-height: 22px;
      color: #595959;
      &:after {
        content: '*';
        color: #fd434a;
        margin-left: 4px;
      }
    }
    .el-radio {
      margin-left: 32px;
      margin-right: 0;
      .el-radio__label {
        color: #262626;
        font-weight: normal;
      }
    }
  }
  :deep(.delete-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
    .dialog-content {
      font-size: 14px;
      line-height: 22px;
      color: #262626;
      flex: 1;
    }
  }
  :deep(.el-button) {
    font-weight: 420;
  }
  :deep(.text-button) {
    color: #595959;
    border: none;
    &:hover {
      color: #595959 !important;
      background-color: #f5f5f5 !important;
    }
    &:focus {
      color: #595959 !important;
      background-color: #f0f0f0 !important;
    }
  }
  :deep(.el-button--primary:not(:disabled):hover) {
    background: #21ccc6 !important;
    color: #fff !important;
    border-color: #21ccc6;
  }
  :deep(.el-button--primary:focus) {
    background: #009499 !important;
    color: #fff !important;
    border-color: #009499;
  }
  :deep(.cancel-button:not(.is-disabled)) {
    color: #01a0ac;
    border: 1px solid #00bebe;
    &:hover {
      color: #01a0ac !important;
      background-color: #e5f9f9 !important;
      border: 1px solid #00bebe;
    }
    &:focus {
      color: #01a0ac !important;
      background-color: #9cebe7 !important;
      border: 1px solid #00bebe;
    }
  }
  :deep(.el-dialog__headerbtn .el-dialog__close) {
    font-size: 20px;
    color: #262626;
  }
}
</style>
