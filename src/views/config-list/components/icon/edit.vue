<template>
  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.23806 19.0031C7.89984 19.0868 7.59384 18.781 7.67723 18.4428L8.44334 14.4629L17.0515 5.93424C17.4137 5.57434 17.9988 5.57545 18.3596 5.93672L20.7558 8.33608C21.1163 8.69707 21.117 9.28164 20.7573 9.64345L12.2034 18.2183L8.23806 19.0031ZM9.34927 17.3226L11.4862 16.9749L19.4887 8.99827L17.6997 7.21092L9.70173 15.1783L9.34927 17.3226Z" :fill="color" />
    <path d="M21.8719 22.335C22.6392 22.335 23.2611 21.7131 23.2611 20.9459H4.73889C4.73889 21.7131 5.36084 22.335 6.12806 22.335H21.8719Z" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
defineProps({
  color: {
    type: String,
    default: '#01A0AC'
  }
})
</script>