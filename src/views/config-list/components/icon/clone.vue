<template>
  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22.67 6.17776V18.8372C22.67 19.2115 22.3666 19.5149 21.9922 19.5149H19.9558C19.9568 18.7666 20.5631 18.1591 21.3114 18.1577L21.3151 18.1577V6.85538H10.0238V7.02419C10.0238 7.77251 9.41719 8.37914 8.66887 8.37914V6.17776C8.66887 5.80344 8.97232 5.5 9.34664 5.5H21.9922C22.3666 5.5 22.67 5.80344 22.67 6.17776Z" :fill="color" />
    <path d="M19.0495 9.78494V21.9922C19.0495 22.3666 18.746 22.67 18.3717 22.67H6.17776C5.80344 22.67 5.5 22.3666 5.5 21.9922V9.78494C5.5 9.41062 5.80344 9.10718 6.17776 9.10718H18.3717C18.746 9.10718 19.0495 9.41062 19.0495 9.78494ZM17.6945 10.4635H6.85495V21.3137H17.6945V10.4635Z" :fill="color" />
    <path d="M11.7353 15.2588H8.6629C8.6629 16.0075 9.26979 16.6144 10.0184 16.6144H11.7353V19.5063C12.484 19.5063 13.0909 18.8994 13.0909 18.1508V16.6144H14.5368C15.2855 16.6144 15.8924 16.0075 15.8924 15.2588H13.0909V13.6324C13.0909 12.8837 12.484 12.2769 11.7353 12.2769V15.2588Z" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
defineProps({
  color: {
    type: String,
    default: '#01A0AC'
  }
})
</script>