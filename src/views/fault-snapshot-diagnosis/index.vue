<template>
  <div>
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <WelkinStationBreadcrumb :version="project.version" />
          <el-breadcrumb-item @click="changeToPre">
            {{ $t('menu.pp_fault_list') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item
            >{{ $t('menu.pp_snapshot_diagnosis') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_snapshot_diagnosis') }}</div>
      </div>
    </div>

    <div class="swap-page-container">
      <!-- <div class="card-container" style="padding-top: 10px">
        <el-table :data="descData.data_ids">
          <el-table-column
            prop="data_id"
            :label="`${$t('faultDiagnosis.pp_fault_id')}`"
          >
          </el-table-column>
          <el-table-column
            prop="can_cause_shutdown"
            :label="`${$t('faultDiagnosis.pp_can_cause_shutdown')}`"
          >
          </el-table-column>
        </el-table>
      </div> -->

      <div class="card-container">
        <el-descriptions
          :title="`${$t('faultDiagnosis.pp_fault_id')}`"
          :column="12"
          :border="true"
          direction="vertical"
        >
          <span v-for="(item, index) in data_ids_list">
            <el-descriptions-item
              v-if="index % 11 == 0"
              :label="`${$t('faultDiagnosis.pp_fault_id')}`"
            >
              {{ `${$t('faultDiagnosis.pp_can_cause_shutdown')}` }}
            </el-descriptions-item>
            <el-descriptions-item :key="index" :label="item.data_id">
              <span class="user-role-tag">
                <el-tag
                  :color="item.can_cause_shutdown ? '#be2900' : ''"
                  effect="dark"
                  disable-transitions
                  round
                >
                  {{ item.can_cause_shutdown ? 'Yes' : 'No' }}
                </el-tag>
              </span>
            </el-descriptions-item>
          </span>
          <el-descriptions-item v-if="!(data_ids_list?.length > 0)">
            {{ `${$t('common.pp_empty')}` }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="card-container">
        <el-descriptions
          :title="`${$t('faultDiagnosis.pp_snapshot_instant_data')}`"
          :border="true"
          direction="vertical"
          v-if="!data?._id"
        >
          <el-descriptions-item v-if="!(data?.data_ids?.length > 0)">
            {{ `${$t('common.pp_empty')}` }}
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions
          :title="`${$t('faultDiagnosis.pp_snapshot_instant_data')}`"
          :column="12"
          :border="true"
          direction="vertical"
          v-else
        >
          <!-- <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_request_id')}`"
          >
            {{ data.request_id }}
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_fault_type')}`"
          >
            {{ data.type }}
          </el-descriptions-item> -->
          <el-descriptions-item :label="`${$t('faultDiagnosis.pp_mode')}`">
            {{ t(`faultDiagnosis.diagnosisMode.${data.mode}`) }}
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_charging_elapsed')}`"
          >
            {{ data.charging_elapsed }}min
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_integral_electricity')}`"
          >
            {{ data.integral_electricity }}kWh
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_meter_electricity')}`"
          >
            {{ data.meter_electricity }}kWh
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_limit_power')}`"
          >
            {{ data.limit_power }}kw
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_status_of_charge')}`"
          >
            {{ data.status_of_charge }}%
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_status_of_pcu')}`"
          >
            {{ t(`faultDiagnosis.state_of_pcu.${data.status_of_pcu}`) }}
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_communication_count_of_pcu')}`"
          >
            {{ data.communication_count_of_pcu }}
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_raw_request_voltage_of_bms')}`"
          >
            {{ data.raw_request_voltage_of_bms }}V
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_raw_request_current_of_bms')}`"
          >
            {{ data.raw_request_current_of_bms }}A
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_limited_request_voltage')}`"
          >
            {{ data.limited_request_voltage }}V
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_limited_request_current')}`"
          >
            {{ data.limited_request_current }}A
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_input_pcu_voltage_of_SCT')}`"
          >
            {{ data.input_pcu_voltage_of_SCT }}V
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_input_pcu_current_of_SCT')}`"
          >
            {{ data.input_pcu_current_of_SCT }}A
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_output_pcu_voltage_of_SCT')}`"
          >
            {{ data.output_pcu_voltage_of_SCT }}V
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_output_pcu_current_of_SCT')}`"
          >
            {{ data.output_pcu_current_of_SCT }}A
          </el-descriptions-item>

          <el-descriptions-item
            :label="`${$t(
              'faultDiagnosis.pp_measure_voltage_of_insulation_module'
            )}`"
          >
            {{ data.measure_voltage_of_insulation_module }}V
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t(
              'faultDiagnosis.pp_measure_current_of_insulation_module'
            )}`"
          >
            {{ data.measure_current_of_insulation_module }}A
          </el-descriptions-item>

          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_measure_voltage_of_decm_module')}`"
          >
            {{ data.measure_voltage_of_decm_module }}V
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_measure_current_of_decm_module')}`"
          >
            {{ data.measure_current_of_decm_module }}A
          </el-descriptions-item>

          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_temperature_of_gun_head_plus')}`"
          >
            {{ data.temperature_of_gun_head_plus }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_temperature_of_gun_head_minus')}`"
          >
            {{ data.temperature_of_gun_head_minus }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_inlet_temperature')}`"
          >
            {{ data.inlet_temperature }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_outlet_temperature')}`"
          >
            {{ data.outlet_temperature }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_temperature_of_gun_tail_plus')}`"
          >
            {{ data.temperature_of_gun_tail_plus }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_temperature_of_gun_tail_minus')}`"
          >
            {{ data.temperature_of_gun_tail_minus }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t(
              'faultDiagnosis.pp_temperature_of_bronze_medal_plus'
            )}`"
          >
            {{ data.temperature_of_bronze_medal_plus }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t(
              'faultDiagnosis.pp_temperature_of_bronze_medal_minus'
            )}`"
          >
            {{ data.temperature_of_bronze_medal_minus }}°C
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_air_inlet_temperature')}`"
          >
            {{ data.air_inlet_temperature }}°C
          </el-descriptions-item>
          <!-- <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_ambient_temperature')}`"
          >
            {{ data.ambient_temperature }}°C
          </el-descriptions-item> -->
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_coolant_pressure')}`"
          >
            {{ data.coolant_pressure }}bar
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_cooling_fan_duty_cycle')}`"
          >
            {{ data.cooling_fan_duty_cycle }}/1000
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_rotate_speed_of_fan1')}`"
          >
            {{ data.rotate_speed_of_fan1 }}RPM
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_rotate_speed_of_fan2')}`"
          >
            {{ data.rotate_speed_of_fan2 }}RPM
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_pump_duty_cycle')}`"
          >
            {{ data.pump_duty_cycle }}/1000
          </el-descriptions-item>
          <el-descriptions-item
            :label="`${$t('faultDiagnosis.pp_rotate_speed_of_pump')}`"
          >
            {{ data.rotate_speed_of_pump }}RPM
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="card-container">
        <el-descriptions
          :title="`${$t('faultDiagnosis.pp_snapshot_result')}`"
        />
        <el-form :model="resultForm" label-width="110px" style="width: 100%">
          <el-form-item :label="`${$t('faultDiagnosis.pp_diagnosis_result')}`">
            <el-input
              v-model="resultForm.diagnosis_result"
              :placeholder="
                isEditing
                  ? `${
                      $t('common.pp_please_input') +
                      $t('faultDiagnosis.pp_diagnosis_result')
                    }`
                  : ''
              "
              clearable
              type="textarea"
              :rows="3"
              :disabled="!editResultAuth || !isEditing"
            />
          </el-form-item>
          <el-form-item :label="`${$t('faultDiagnosis.pp_device_log')}`">
            <el-input
              v-model="resultForm.device_log"
              :placeholder="
                isEditing
                  ? `${
                      $t('common.pp_please_select') +
                      $t('faultDiagnosis.pp_device_log')
                    }`
                  : ''
              "
              clearable
              type="textarea"
              :rows="2"
              :disabled="!editResultAuth || !isEditing"
            />
          </el-form-item>
          <!-- </div> -->
        </el-form>
        <div class="collapse-search-container" v-if="editResultAuth">
          <div class="button-container">
            <el-button
              class="welkin-primary-button"
              @click="isEditing = true"
              v-if="!isEditing"
            >
              {{ $t('common.pp_edit') }}
            </el-button>
            <el-button v-if="isEditing" @click="cancelSubmit" class="welkin-secondary-button">
              {{ $t('common.pp_cancel') }}
            </el-button>
            <el-button
              v-if="isEditing"
              class="welkin-primary-button"
              :loading="loading"
              @click="submitResult"
            >
              {{ $t('common.pp_confirm') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onBeforeMount } from 'vue';
import { formatLocaleDate } from '~/utils';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import {
  apiGetSnapshotDiagnosis,
  apiPutSnapshotDiagnosisResult,
} from '~/apis/fault-diagnosis';
import { hasPermission } from '~/auth';
import { ElMessage, ElMessageBox } from 'element-plus';

const { t } = useI18n();
const $store = useStore();
const $route = useRoute();
const $router = useRouter();
const project = ref(computed(() => $store.state.project));

const editResultAuth = hasPermission(
  `powerSwap${project.value.version}:fault-snapshot-diagnosis:result`
);
const isEditing = ref(false);

const data = ref({} as any);
const data_ids_list = ref([] as any[]);
const resultForm = reactive({
  diagnosis_result: '',
  device_log: '',
});

const loading = ref(false);

// 重置
const cancelSubmit = () => {
  isEditing.value = false;

  resultForm.diagnosis_result = data.value?.diagnosis_result;
  resultForm.device_log = data.value?.device_log;
};
// 发布诊断结果
const submitResult = () => {
  console.log('发布诊断结果', resultForm);
  ElMessageBox.confirm(
    `${
      t('common.pp_confirm_submit') + t('faultDiagnosis.pp_snapshot_result')
    }?`,
    'Warning',
    {
      confirmButtonText: t('common.pp_confirm'),
      cancelButtonText: t('common.pp_cancel'),
      type: 'warning',
    }
  )
    .then(() => {
      apiPutSnapshotDiagnosisResult(
        project.value.project,
        $route.params.deviceId,
        data.value._id,
        resultForm
      ).then((res) => {
        isEditing.value = false;
        ElMessage.success(t('common.pp_submit_success'));
      });
    })
    .catch(() => {});
};

// 回到上级页面
const changeToPre = () => {
  let query: any = sessionStorage.getItem('fault-list');
  $router.push({
    path: `/${project.value.route}/fault-list`,
    query: JSON.parse(query),
  });
};

onBeforeMount(() => {
  apiGetSnapshotDiagnosis(
    project.value.project,
    $route.params.deviceId,
    $route.params.requestId,
    $route.query
  ).then((res) => {
    data.value = res.data;
    data_ids_list.value = res.data_ids_list;
    resultForm.diagnosis_result = data.value?.diagnosis_result;
    resultForm.device_log = data.value?.device_log;
  });
});
</script>

<style lang="scss" scoped>
.card-container {
  background-color: #fff;
  margin-bottom: 20px;
  padding: 20px;
}
</style>
