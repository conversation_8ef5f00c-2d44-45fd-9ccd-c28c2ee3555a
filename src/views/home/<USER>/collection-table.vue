<template>
  <div class="swap-table-container">
    <el-table
      :data="tableData"
      :header-cell-style="{
        fontSize: '14px',
        color: '#292C33',
        cursor: 'auto',
      }"
      v-loading="loading"
      v-if="tableData"
    >
      <el-table-column
        prop="description"
        :label="`${$t('deviceManagement.pp_device_name')}`"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          <div class="pointer-column ellipse">
            <span class="point-span" @click="handleClickDescription(scope.row)">
              {{ scope.row.description }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="device_id"
        :label="`${$t('deviceManagement.pp_device_id')}`"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.device_id" />
        </template>
      </el-table-column>
      <el-table-column :label="`${$t('home.pp_project')}`" width="150">
        <template #default="scope">
          <div class="flex-box flex_a_i-center">
            <span :style="`color: ${projectMap[scope.row.project].color}`">{{$t(projectMap[scope.row.project].name)
            }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="city_company"
        :label="`${$t('common.pp_regional_company')}`"
        show-overflow-tooltip
        min-width="120"
      />
      <el-table-column
        prop="region"
        :label="`${$t('common.pp_region')}`"
        show-overflow-tooltip
        min-width="160"
      />
      <el-table-column
        :label="`${$t('common.pp_operation')}`"
        width="100"
        class-name="operation-column"
      >
        <template #default="scope">
          <el-icon
            class="operation-icon"
            @click="cancelCollectDevice(scope.$index, scope.row)"
          >
            <Icon color="#00bebe" :icon="iconMap['favorite']" />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>
    <el-empty v-if="!tableData" :description="`${$t('home.pp_empty_description')}`" />
    <div class="pagination-container" v-if="tableData">
      <el-pagination
        v-model:currentPage="searchForm.page"
        v-model:page-size="searchForm.size"
        :page-sizes="pagination.pageSizes"
        :layout="pagination.layout"
        :total="totalNumber"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onBeforeMount, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { iconMap } from '~/auth';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex'
import { pagination, projectMap } from '~/constvars';
import { removeNullProp } from '~/utils';
import { Icon } from '@iconify/vue/dist/iconify';
import { ElNotification } from 'element-plus';
import { apiGetFavorites, apiPutFavorites } from '~/apis/home';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const store = useStore()
const totalNumber = ref(10);
const loading = ref(false);
const tableData = ref([] as any);
const queryProjectMap = ref(computed(() => store.state.vehicle.pathMap))
const searchForm = reactive({
  page: 1,
  size: 10,
  descending: true,
});
const routerQuery = reactive({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  page: 1,
  size: 10,
  descending: true,
  tab: 'service',
});

/**
 * @description: 搜索函数
 * @return {*}
 */
const publicSearch = async () => {
  loading.value = true;
  router.push({
    path: route.path,
    query: {
      ...removeNullProp(searchForm),
      tab: 'star',
    },
  });
  const res = await apiGetFavorites(searchForm);
  if(!res.data && searchForm.page > 1) {
    searchForm.page -= 1
    publicSearch()
  } else {
    loading.value = false;
    totalNumber.value = res.total;
    tableData.value = res.data;
  }
};

/**
 * @description: 取消收藏站点
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const cancelCollectDevice = async (index: number, row: any) => {
  row.favorite = false;
  ElNotification.closeAll();
  ElNotification({
    title: `${t('common.pp_remove_success')}`,
    message: `${t('common.pp_remove')} ${row.description}${row.device_id}`,
    type: 'success',
    duration: 2000,
  });
  await apiPutFavorites(row);
  await publicSearch();
};

/**
 * @description: 改变每页显示条目个数
 * @param {*} val
 * @return {*}
 */
const handleSizeChange = (val: number) => {
  searchForm.size = val;
  searchForm.page = 1;
  publicSearch();
};

/**
 * @description: 改变当前页
 * @param {*} val
 * @return {*}
 */
const handleCurrentChange = (val: number) => {
  searchForm.page = val;
  publicSearch();
};

const handleClickDescription = (row: any) => {
  console.log(row);
  router.push({
    path: `/${queryProjectMap.value[row.project]}/device-management/single-station/${
      row.device_id
    }`,
    query: { ...removeNullProp(routerQuery) },
  });
};

onBeforeMount(() => {
  publicSearch();
});
</script>
