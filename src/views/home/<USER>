<!--
 * @Author: zhenxing.chen
 * @Date: 2022-11-04 19:45:30
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2022-11-30 10:03:24
 * @Email: <EMAIL>
 * @Description: 
-->
<template>
  <div class="home">
    <div class="swap-page-header height-60 padding_b-10">
      <div class="header-left">
        <div class="header-title">{{ $t('menu.pp_home') }}</div>
      </div>
    </div>
    <div class="tab-container padding-n-20">
      <el-tabs v-model="activeTab">
        <el-tab-pane
          :label="`${$t('home.pp_star_list')}`"
          name="star"
        >
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="swap-page-container">
      <div v-if="activeTab === 'star'">
        <CollectionTable />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import CollectionTable from './components/collection-table.vue';

const store = useStore();
const project = ref(computed(() => store.state.project));
const activeTab = ref('star');
</script>

<style></style>
