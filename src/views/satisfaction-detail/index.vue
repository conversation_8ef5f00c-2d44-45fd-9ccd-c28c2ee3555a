<template>
  <div class="satisfaction-detail-container">
    <div class="header-container">
      <div class="flex-box flex_a_i-center font-size-20 line-height-28 margin_b-12">
        <BackIcon @click="changeToPre" class="cursor-pointer" />
        <span style="color: #8c8c8c; font-weight: 480; margin-left: 4px">{{ $t('menu.pp_satisfaction_analysis') }} /</span>
        <span style="color: #1f1f1f; font-weight: 420">&nbsp;{{ deviceInfo.description || $t('common.pp_unnamed_device') }}</span>
      </div>
    </div>

    <div class="content-container">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane :label="$t('satisfaction.pp_diagnosis_card')" name="diagnosis">
          <DiagnosisCard v-if="activeTab == 'diagnosis'" :deviceInfo="deviceInfo" :tagMap="tagMap" />
        </el-tab-pane>
        <el-tab-pane :label="$t('satisfaction.pp_detail_info')" name="detail">
          <DetailInfo v-if="activeTab == 'detail'" />
        </el-tab-pane>
        <el-tab-pane :label="$t('satisfaction.pp_log')" name="log">
          <LogInfo v-if="activeTab == 'log'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { apiGetServiceInfo, apiGetDiagnosisTag } from '~/apis/satisfaction-analysis'
import { ElMessage } from 'element-plus'
import DiagnosisCard from './components/diagnosis-card.vue'
import DetailInfo from './components/detail-info.vue'
import LogInfo from './components/log-info.vue'
import BackIcon from './components/icon/back-icon.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const activeTab = ref('diagnosis' as any)
const deviceInfo = ref({} as any)
const tagMap = ref({} as any)

/**
 * @description: 切换tab
 * @param {*} name
 * @return {*}
 */
const handleTabChange = (name: any) => {
  router.push({
    path: location.pathname,
    query: { ...route.query, tab: name }
  })
}

/**
 * @description: 返回上一级页面
 * @return {*}
 */
const changeToPre = () => {
  const query: any = sessionStorage.getItem('satisfaction-analysis')
  router.push({
    path: `/fault-diagnosis/satisfaction-analysis`,
    query: JSON.parse(query)
  })
}

/**
 * @description: 根据订单id查询服务订单基本信息
 * @return {*}
 */
const getDeviceInfo = async () => {
  const res = await apiGetServiceInfo(route.query.project, route.query.order)
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    deviceInfo.value = res.data
  }
}

/**
 * @description: 获取诊断标签列表
 * @return {*}
 */
const getDiagnosisTag = async () => {
  const res = await apiGetDiagnosisTag()
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    tagMap.value = res.data.reduce((acc: any, item: any) => {
      acc[item.tag] = item.description
      return acc
    }, {})
  }
}

onBeforeMount(() => {
  activeTab.value = route.query.tab || 'diagnosis'
  getDiagnosisTag()
  getDeviceInfo()
})
</script>

<style lang="scss" scoped>
.satisfaction-detail-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  .header-container {
    padding: 24px 24px 0px;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
  }
  .content-container {
    flex: 1;
    padding: 0px 24px 24px;
    background: #f8f8f8;
    :deep(.el-tabs) {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__content) {
      flex: 1;
      padding-top: 20px;
      .el-tab-pane {
        height: 100%;
      }
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
  }
}
</style>
