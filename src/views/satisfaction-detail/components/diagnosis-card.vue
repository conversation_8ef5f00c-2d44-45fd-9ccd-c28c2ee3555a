<template>
  <div class="diagnosis-card-container" v-if="!loading && !isEmpty">
    <div v-if="list.diagnosis_result" class="label-container">
      <div class="flex-box flex_a_i-center margin_b-12">
        <ResultIcon class="margin_r-6" />
        <span class="card-title">{{ $t('satisfaction.pp_diagnosis_result') }}</span>
      </div>
      <el-descriptions :column="3" border>
        <el-descriptions-item :label="$t('satisfaction.pp_l1_label')" label-class-name="diagnosis-label" class-name="diagnosis-content">
          {{ list.diagnosis_result.l1_label }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('satisfaction.pp_l2_label')" label-class-name="diagnosis-label" class-name="diagnosis-content">
          {{ list.diagnosis_result.l2_label }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('satisfaction.pp_l3_label')" label-class-name="diagnosis-label" class-name="diagnosis-content">
          {{ list.diagnosis_result.l3_label }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="list.diagnosis_label" class="label-container">
      <div class="flex-box flex_a_i-center margin_b-12">
        <LabelIcon class="margin_r-6" />
        <span class="card-title">{{ $t('satisfaction.pp_diagnostic_label') }}</span>
      </div>
      <el-descriptions :column="3" border>
        <el-descriptions-item v-for="item in list.diagnosis_label" :label="item.label" label-class-name="diagnosis-label" class-name="diagnosis-content">
          <div class="flex-box flex_w-wrap gap_8">
            <div class="value" v-for="value in item.value.slice(0, 3)">{{ value }}</div>
            <el-tooltip effect="light" placement="top" v-if="item.value.length > 3">
              <template #content>
                <span>{{ item.value.slice(3).join(',') }}</span>
              </template>
              <div class="value">+{{ item.value.length - 3 }}</div>
            </el-tooltip>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="hasCustomLabel" class="label-container">
      <div class="flex-box flex_a_i-center">
        <TaskIcon class="margin_r-6" />
        <span class="margin_r-24 card-title">{{ $t('satisfaction.pp_label_detail') }}</span>
        <span v-for="(value, key) in list" class="margin_r-12 tag-container" :style="{ display: ['diagnosis_result', 'diagnosis_label'].includes(key.toString()) ? 'none' : 'flex' }">{{ tagMap[key] }}</span>
      </div>
    </div>

    <!-- 换电时间长 -->
    <div class="swap-time-card" v-if="list.swap_time">
      <div class="left-card">
        <div class="card-title margin_b-12">{{ $t('satisfaction.pp_long_battery_swap_time') }}</div>
        <div class="font-size-14 line-height-22 flex-box">
          <div style="flex: 1; white-space: nowrap">
            <span class="color-59 margin_r-24 font-weight-bold">{{ $t('satisfaction.pp_user_start_battery_swap_time') }}</span>
            <span class="color-26 margin_r-160">{{ formatTime(list.swap_time.service_start_time) }}</span>
          </div>
          <div style="flex: 1; white-space: nowrap">
            <span class="color-59 margin_r-24 font-weight-bold">{{ $t('satisfaction.pp_user_end_battery_swap_time') }}</span>
            <span class="color-26">{{ formatTime(list.swap_time.service_end_time) }}</span>
          </div>
        </div>
      </div>
      <div class="right-card">
        <div class="font-size-16 line-height-25 color-26">{{ $t('satisfaction.pp_service_duration') }}</div>
        <div class="font-size-28 line-height-44 color-26">{{ getDuration(list.swap_time.service_start_time, list.swap_time.service_end_time, true) }}</div>
        <img src="./icon/timer.png" alt="" class="time-icon" />
      </div>
    </div>

    <!-- 换电挂车/失败 -->
    <div class="swap-fail-card" v-if="list.swap_fail">
      <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-12">
        <div class="flex-box flex_a_i-center">
          <div class="card-title margin_r-16">{{ $t('satisfaction.pp_swap_fail') }}</div>
          <InfoIcon class="margin_r-4" v-if="list.swap_fail.finish_result === 0 || list.swap_fail.is_stuck" />
          <div class="font-size-16 line-height-24" style="color: #fd8c08">
            <span v-if="list.swap_fail.finish_result === 0">{{ $t('satisfaction.pp_swap_fail_info1') }}</span>
            <span v-if="list.swap_fail.finish_result === 0 && list.swap_fail.is_stuck">、</span>
            <span v-if="list.swap_fail.is_stuck">{{ $t('satisfaction.pp_swap_fail_info2') }}</span>
          </div>
        </div>
        <div class="flex-box flex_a_i-center cursor-pointer" @click="handleViewMoreAlarm">
          <EyeIcon class="margin_r-4" />
          <span class="font-size-14 line-height-22" style="color: #01a0ac">{{ $t('satisfaction.pp_view_more') }}</span>
        </div>
      </div>

      <el-table :data="list.swap_fail.alarm_list.slice((swapFailPage.current - 1) * swapFailPage.size, swapFailPage.current * swapFailPage.size)" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="225" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.data_id_description || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="90" show-overflow-tooltip />
        <el-table-column prop="is_stuck" :label="$t('station.pp_stuck')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span :style="{ color: row.is_stuck ? '#FD4348' : '#262626' }">{{ row.is_stuck ? $t('common.pp_yes') : $t('common.pp_no') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarm_type" :label="$t('alarmList.pp_alarm_type')" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ $t(alarmTypeMap[row.alarm_type]) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="125" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="alarm-level-container" v-if="alarmLevelMap[row.alarm_level]" :style="{ color: alarmLevelMap[row.alarm_level].color, background: alarmLevelMap[row.alarm_level].background }">
              {{ $t(alarmLevelMap[row.alarm_level].name) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" min-width="125" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="alarm-status-container" :style="{ background: alarmStatusMap[row.state].background }">
              <ClearIcon v-if="row.state == 1" />
              <CreateIcon v-else-if="row.state == 2" />
              <UnknownIcon v-else />
              <span>{{ $t(alarmStatusMap[row.state].name) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.create_ts) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.clear_ts) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination">
        <Page :page="swapFailPage" @change="handleSwapFailPageChange" layout="total, prev, pager, next, jumper" />
      </div>
    </div>

    <!-- 换电排队时间长 -->
    <div class="queue-time-card" v-if="list.swap_queue_time">
      <div class="left-card">
        <div class="card-title margin_b-12">{{ $t('satisfaction.pp_swap_queue_time') }}</div>
        <div class="font-size-14 line-height-22 flex-box">
          <div style="flex: 1; white-space: nowrap">
            <span class="color-59 margin_r-24 font-weight-bold">{{ $t('satisfaction.pp_user_order_time') }}</span>
            <span class="color-26 margin_r-160">{{ formatTime(list.swap_queue_time.order_time) }}</span>
          </div>
          <div style="flex: 1; white-space: nowrap">
            <span class="color-59 margin_r-24 font-weight-bold">{{ $t('satisfaction.pp_user_call_time') }}</span>
            <span class="color-26">{{ formatTime(list.swap_queue_time.call_time) }}</span>
          </div>
        </div>
      </div>
      <div class="right-card">
        <div class="font-size-16 line-height-25 color-26">{{ $t('satisfaction.pp_queue_duration') }}</div>
        <div class="font-size-28 line-height-44 color-26">{{ getDuration(list.swap_queue_time.order_time, list.swap_queue_time.call_time, true) }}</div>
        <img src="./icon/timer.png" alt="" class="time-icon" />
      </div>
    </div>

    <!-- 前序订单时间长 -->
    <div class="preorder-time-card" v-if="list.preorder_swap_time">
      <div class="card-title margin_b-12">{{ $t('satisfaction.pp_preorder_time') }}</div>
      <el-table :data="list.preorder_swap_time" :row-class-name="tableRowClassName" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="id" label="No." :width="locale == 'zh' ? 80 : 100" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.id !== 0">{{ row.id }}</div>
            <div v-else class="flex-box flex_a_i-center">
              <CurrentIcon />
              <span>{{ $t('satisfaction.pp_current') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_id" :label="$t('satisfaction.pp_user')" min-width="105" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.vehicle_id == deviceInfo.vehicle_id" class="car-owner">{{ $t('satisfaction.pp_car_owner') }}</div>
            <div v-else class="other-owner">{{ $t('satisfaction.pp_other_user') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="order_start_time" :label="$t('satisfaction.pp_order_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.order_start_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_start_time" :label="$t('station.pp_service_start_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.service_start_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_end_time" :label="$t('station.pp_service_end_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.service_end_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('satisfaction.pp_order_duration')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span :style="{ color: row.order_end_time - row.order_start_time > deviceInfo.order_time_threshold ? '#FD4348' : '#262626' }">{{ getDuration(row.order_start_time, row.order_end_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('station.pp_service_duration')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span :style="{ color: row.service_end_time - row.service_start_time > deviceInfo.service_time_threshold ? '#FD4348' : '#262626' }">{{ getDuration(row.service_start_time, row.service_end_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_id" :label="$t('satisfaction.pp_order_id')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.order_id" :showIcon="false" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="service_id" :label="$t('satisfaction.pp_service_id')" min-width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.service_id" :showIcon="false" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('satisfaction.pp_detail')" width="70" fixed="right" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <DetailIcon @click="handleClickDetail(row)" class="cursor-pointer" />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 前序服务挂车/失败 -->
    <div class="preorder-fail-card" v-if="list.preorder_swap_fail">
      <div class="card-title" v-if="list.preorder_swap_fail.length > 1" style="margin-bottom: -8px">{{ $t('satisfaction.pp_preorder_fail') }}</div>
      <div v-for="(item, index) in list.preorder_swap_fail">
        <div class="flex-box flex_a_i-center flex_j_c-space-between margin_b-12">
          <div class="flex-box flex_a_i-center">
            <div class="card-title margin_r-16" v-if="list.preorder_swap_fail.length == 1">{{ $t('satisfaction.pp_preorder_fail') }}</div>
            <div class="font-size-16 line-height-24 margin_r-16 font-weight-bold color-59" v-else-if="locale == 'zh'">订单{{ numberToChinese(index + 1) }}</div>
            <div class="font-size-16 line-height-24 margin_r-16 font-weight-bold color-59" v-else>Order {{ index + 1 }}</div>
            <InfoIcon class="margin_r-4" v-if="item.finish_result === 0 || item.is_stuck" />
            <div class="font-size-16 line-height-24" style="color: #fd8c08">
              <span v-if="item.finish_result === 0">{{ $t('satisfaction.pp_swap_fail_info1') }}</span>
              <span v-if="item.finish_result === 0 && item.is_stuck">、</span>
              <span v-if="item.is_stuck">{{ $t('satisfaction.pp_swap_fail_info2') }}</span>
            </div>
          </div>

          <div class="flex-box flex_a_i-center gap_4 font-size-14 line-height-22 color-26">
            <div v-if="item.vehicle_id == deviceInfo.vehicle_id" class="car-owner margin_r-8">{{ $t('satisfaction.pp_car_owner') }}</div>
            <span class="color-59 font-weight-bold">{{ $t('satisfaction.pp_service_id') }}</span>
            <WelkinCopyBoard :text="item.service_id" direction="rtl" class="width-340" />
          </div>
        </div>

        <el-table :data="item.alarm_list.slice((paginationStates[index].current - 1) * paginationStates[index].size, paginationStates[index].current * paginationStates[index].size)" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="225" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.data_id_description || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="90" show-overflow-tooltip />
          <el-table-column prop="is_stuck" :label="$t('station.pp_stuck')" min-width="90" show-overflow-tooltip>
            <template #default="{ row }">
              <span :style="{ color: row.is_stuck ? '#FD4348' : '#262626' }">{{ row.is_stuck ? $t('common.pp_yes') : $t('common.pp_no') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarm_type" :label="$t('alarmList.pp_alarm_type')" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ $t(alarmTypeMap[row.alarm_type]) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="125" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="alarm-level-container" v-if="alarmLevelMap[row.alarm_level]" :style="{ color: alarmLevelMap[row.alarm_level].color, background: alarmLevelMap[row.alarm_level].background }">
                {{ $t(alarmLevelMap[row.alarm_level].name) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" min-width="125" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="alarm-status-container" :style="{ background: alarmStatusMap[row.state].background }">
                <ClearIcon v-if="row.state == 1" />
                <CreateIcon v-else-if="row.state == 2" />
                <UnknownIcon v-else />
                <span>{{ $t(alarmStatusMap[row.state].name) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="170" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ formatTime(row.create_ts) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="170" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ formatTime(row.clear_ts) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="common-pagination">
          <Page :page="paginationStates[index]" @change="($event: any) => handlePageChange($event, index)" layout="total, prev, pager, next, jumper" />
        </div>
      </div>
    </div>

    <!-- 多次下单 -->
    <div class="multiple-swap-card" v-if="list.multiple_swap">
      <div class="flex-box flex_a_i-center margin_b-16">
        <div class="card-title margin_r-16">{{ $t('satisfaction.pp_multiple_orders') }}</div>
        <InfoIcon class="margin_r-4" />
        <div class="font-size-16 line-height-24" style="color: #fd8c08">
          {{ $t('satisfaction.pp_order_times') }}<span class="font-weight-bold">{{ list.multiple_swap.length + $t('satisfaction.pp_times') }}</span>
        </div>
      </div>

      <div v-for="(item, index) in list.multiple_swap" class="flex-box flex_w-wrap" style="column-gap: 12px">
        <div class="flex-box flex_d-column flex_a_i-center margin_t-6 vertical-line">
          <DotIcon />
        </div>
        <div class="flex-box flex_d-column flex-item_f-1 gap_12 margin_b-40">
          <div class="row-container">
            <span class="label-text">{{ $t('satisfaction.pp_order_start_time') }}</span>
            <span class="value-text">{{ formatTime(item.order_start_time) }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_order_end_time') }}</span>
            <span class="value-text">{{ formatTime(item.order_end_time) }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_order_id') }}</span>
            <span class="value-text">
              <WelkinCopyBoard :text="item.order_id" direction="rtl" />
            </span>
          </div>
          <div class="row-container">
            <span class="label-text">{{ $t('satisfaction.pp_order_duration') }}</span>
            <span class="value-text">{{ getDuration(item.order_start_time, item.order_end_time) }}</span>
            <span class="label-text">{{ $t('station.pp_service_start_time') }}</span>
            <span class="value-text">{{ formatTime(item.service_start_time) }}</span>
            <span class="label-text">{{ $t('station.pp_service_end_time') }}</span>
            <span class="value-text">{{ formatTime(item.service_end_time) }}</span>
          </div>
          <div class="row-container">
            <span class="label-text">{{ $t('station.pp_service_duration') }}</span>
            <span class="value-text">{{ getDuration(item.service_start_time, item.service_end_time) }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_swap_normal') }}</span>
            <span class="value-text">{{ [1, 0, -1].includes(item.finish_result) ? $t(statusNameMap[item.finish_result]) : '-' }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_service_id') }}</span>
            <span class="value-text">
              <WelkinCopyBoard :text="item.service_id" direction="rtl" />
            </span>
          </div>
          <div @click="handleClickDetail(item)" class="flex-box flex_a_i-center gap_4 cursor-pointer" :class="locale == 'zh' ? 'width-76' : 'width-103'" v-if="index !== 0">
            <LogIcon />
            <span class="font-size-14 line-height-22" style="color: #01a0ac">{{ $t('common.pp_view_details') }}</span>
          </div>
        </div>
        <div class="flex-box gap_12" style="flex: 100%; width: 100%" v-if="item.interval">
          <div class="flex-box flex_d-column flex_a_i-center margin_t-6 ellipsis-vertical-line">
            <DotIcon />
          </div>
          <div class="font-size-14 line-height-22 color-8c margin_b-58">{{ $t('satisfaction.pp_hidden1') + item.interval + $t('satisfaction.pp_hidden2') }}</div>
        </div>
      </div>
    </div>
  </div>
  <div v-else-if="loading">
    <el-skeleton :rows="15" animated />
  </div>
  <div v-else class="height-full">
    <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
      <template #image>
        <GrayEmptyDataIcon />
      </template>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import { miniPage } from '~/constvars/page';
import { useRoute } from 'vue-router';
import { alarmTypeMap, statusNameMap } from './constant';
import { alarmLevelMap, alarmStatusMap } from '~/views/single-station/constant';
import { formatTime, getDuration, numberToChinese } from '~/utils';
import { apiGetDiagnosisCard } from '~/apis/satisfaction-analysis';
import { ElMessage } from 'element-plus';
import TaskIcon from './icon/task-icon.vue';
import InfoIcon from './icon/info-icon.vue';
import EyeIcon from './icon/eye-icon.vue';
import CurrentIcon from './icon/current-icon.vue';
import DotIcon from './icon/dot-icon.vue';
import LogIcon from './icon/log-icon.vue';
import ResultIcon from './icon/diagnosis-result.vue';
import LabelIcon from './icon/diagnosis-label.vue';
import GrayEmptyDataIcon from '~/assets/svg/gray-empty-data.vue';
import ClearIcon from '~/views/single-station/icon/alarm-clear.vue';
import CreateIcon from '~/views/single-station/icon/alarm-create.vue';
import UnknownIcon from '~/views/single-station/icon/alarm-unknown.vue';
import DetailIcon from '~/views/satisfaction-analysis/components/icon/detail-icon.vue';
import { cloneDeep } from 'lodash-es';

const props = defineProps({
  deviceInfo: {
    type: Object,
    default: {},
  },
  tagMap: {
    type: Object,
    default: {},
  },
});
const { locale } = useI18n({ useScope: 'global' });

const route = useRoute();
const store = useStore();
const loading = ref(false);
const isEmpty = ref(false);
const hasCustomLabel = ref(false);
const list = ref({} as any);
const swapFailPage = ref(cloneDeep(miniPage));
const paginationStates = ref([] as any);
const queryProjectMap = ref(computed(() => store.state.vehicle.pathMap));

/**
 * @description: 前序服务挂车/失败分页
 * @param {*} argPage
 * @param {*} index
 * @return {*}
 */
const handlePageChange = (argPage: any, index: any) => {
  paginationStates.value[index].current = argPage.current;
  paginationStates.value[index].size = argPage.size;
};

/**
 * @description: 高亮最后一行
 * @param {*} data
 * @return {*}
 */
const tableRowClassName = (data: any) => {
  if (data.rowIndex === list.value.preorder_swap_time.length - 1) return 'highlight-row';
  return '';
};

/**
 * @description: 前序订单时间长/详情跳转
 * @param {*} row
 * @return {*}
 */
const handleClickDetail = (row: any) => {
  window.open(`/fault-diagnosis/satisfaction-analysis/satisfaction-detail?project=${row.project}&order=${row.order_id}&service=${row.service_id}`);
};

/**
 * @description: 查看更多告警
 * @return {*}
 */
const handleViewMoreAlarm = () => {
  const { project, device_id, service_id, service_start_time, service_end_time } = props.deviceInfo;
  window.open(`/${queryProjectMap.value[project]}/service-list/service-detail/${device_id}/${service_id}?start_time=${service_start_time}&end_time=${service_end_time}&plc=alarm_list`);
};

/**
 * @description: 换电挂车/失败告警列表分页
 * @param {*} argPage
 * @return {*}
 */
const handleSwapFailPageChange = (argPage: any) => {
  swapFailPage.value.current = argPage.current;
  swapFailPage.value.size = argPage.size;
};

/**
 * @description: 获取数据
 * @return {*}
 */
const getList = async () => {
  const params = {
    project: route.query.project,
    order_id: route.query.order,
  };
  try {
    loading.value = true;
    const res = await apiGetDiagnosisCard(params);
    loading.value = false;
    if (res.err_code) {
      ElMessage.error(res.message);
    } else {
      isEmpty.value = Object.keys(res.data).length == 0;
      if (isEmpty.value) return;
      hasCustomLabel.value = Object.keys(res.data).some((key) => key !== 'diagnosis_result' && key !== 'diagnosis_label');
      list.value = res.data;
      if (list.value.swap_fail) {
        swapFailPage.value.total = list.value.swap_fail.alarm_list.length;
        const stuckIndex = list.value.swap_fail.alarm_list.findIndex((item: any) => item.is_stuck);
        swapFailPage.value.current = stuckIndex == -1 ? 1 : Math.floor(stuckIndex / 5) + 1;
      }
      if (list.value.preorder_swap_fail) {
        list.value.preorder_swap_fail.forEach((item: any) => {
          item.alarm_list = item.alarm_list || [];
          const stuckIndex = item.alarm_list.findIndex((alarm: any) => alarm.is_stuck);
          paginationStates.value.push({
            current: stuckIndex == -1 ? 1 : Math.floor(stuckIndex / 5) + 1,
            size: 5,
            total: item.alarm_list.length,
            sizes: [5, 10, 20, 50],
          });
        });
      }
      if (list.value.diagnosis_label) {
        list.value.diagnosis_label = Object.entries(list.value.diagnosis_label).map(([label, value]) => ({
          label,
          value,
        }));
      }
    }
  } catch (error) {
    loading.value = false;
  }
};

onBeforeMount(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.diagnosis-card-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  :deep(.common-pagination) {
    margin-top: 16px;
    .el-pagination {
      padding: 0;
      height: 28px;
    }
    .el-pager li {
      min-width: 28px;
      height: 28px;
    }
    .el-pagination__editor.el-input {
      min-width: 40px;
      height: 28px;
    }
    .btn-prev,
    .btn-next {
      min-width: 28px;
      height: 28px;
    }
  }
  .label-container {
    padding: 24px;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    background-color: #fff;
    :deep(.diagnosis-label) {
      white-space: nowrap;
      width: 160px;
      padding: 11px !important;
      font-weight: 400;
      line-height: 22px;
      color: #262626;
      background-color: #e5f9f9;
    }
    :deep(.diagnosis-content) {
      padding: 11px !important;
      color: #262626;
      .value {
        white-space: nowrap;
        font-size: 12px;
        line-height: 18px;
        color: #262626;
        border-radius: 2px;
        padding: 2px 7px;
        border: 1px solid #d9d9d9;
        background-color: #fff;
      }
    }
  }
  .card-title {
    font-size: 16px;
    line-height: 24px;
    color: #262626;
    font-weight: bold;
    white-space: nowrap;
  }
  .tag-container {
    height: 32px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    font-size: 14px;
    line-height: 22px;
    color: #262626;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    padding: 3px 12px;
    background-color: #fff;
  }
  .swap-time-card,
  .queue-time-card {
    height: 98px;
    display: flex;
    gap: 16px;
    .left-card {
      flex: 1;
      padding: 20px 24px;
      border: 1px solid #c6efef;
      border-radius: 4px;
      background-color: #fbfefe;
    }
    .right-card {
      width: 232px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      border: 1px solid #c6efef;
      border-radius: 4px;
      background: linear-gradient(132.7deg, #fbfefe, #f3fdfd);
      .time-icon {
        position: absolute;
        left: 139px;
      }
    }
  }
  .swap-fail-card,
  .preorder-fail-card,
  .multiple-swap-card {
    padding: 24px;
    border: 1px solid #dcf2f3;
    border-radius: 4px;
    background-color: #fff;
    .alarm-level-container {
      display: inline-block;
      height: 26px;
      padding: 2px 8px;
      border-radius: 2px;
    }
    .alarm-status-container {
      height: 24px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
      padding: 2px 10px;
      margin-top: 2px;
      border-radius: 14px;
      font-size: 13px;
      line-height: 20px;
      color: #fff;
    }
  }
  .preorder-time-card {
    padding: 24px;
    border: 1px solid #dcf2f3;
    border-radius: 4px;
    background-color: #fff;
    :deep(.highlight-row) {
      background-color: #f5f5f5;
      .el-table-fixed-column--right {
        background-color: #f5f5f5;
      }
    }
  }
  .preorder-fail-card {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .multiple-swap-card {
    .vertical-line,
    .ellipsis-vertical-line {
      position: relative;
    }
    .vertical-line::before,
    .ellipsis-vertical-line::before {
      content: '';
      position: absolute;
      top: 9px;
      left: 4px;
      width: 1px;
      height: 94%;
      background-color: #d9d9d9;
    }
    .ellipsis-vertical-line::before {
      height: 88%;
    }
    .row-container {
      display: grid;
      grid-template-columns: 120px 1fr 120px 1fr 120px 1fr;
      .label-text {
        font-size: 14px;
        line-height: 22px;
        color: #8c8c8c;
        white-space: nowrap;
        overflow: hidden;
      }
      .value-text {
        font-size: 14px;
        line-height: 22px;
        color: #262626;
        overflow: hidden;
      }
    }
  }
  .car-owner,
  .other-owner {
    height: 22px;
    padding: 2px 8px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 18px;
    display: inline-flex;
    color: #2f9c74;
    background-color: #e8fcea;
  }
  .other-owner {
    color: #595959;
    background-color: #ebecee;
  }
}
:deep(.el-empty__description) {
  margin-left: 40px;
}
</style>
