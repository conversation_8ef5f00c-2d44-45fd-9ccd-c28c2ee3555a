<template>
  <div class="log-info-container" v-if="!loading && !isEmpty">
    <div v-for="item in list" class="flex-box flex_w-wrap" style="column-gap: 12px">
      <div class="flex-box flex_d-column flex_a_i-center vertical-line">
        <LogCircle />
      </div>
      <div class="flex-box flex_d-column flex-item_f-1 gap_4 margin_b-32">
        <span class="time-text">{{ formatTime(item.timestamp) }}</span>
        <span class="title-text">{{ item.event }}</span>
      </div>
    </div>
  </div>
  <div v-else-if="loading">
    <el-skeleton :rows="15" animated />
  </div>
  <div v-else class="height-full">
    <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
      <template #image>
        <GrayEmptyDataIcon />
      </template>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { formatTime } from '~/utils'
import { apiGetLogInfo } from '~/apis/satisfaction-analysis'
import { ElMessage } from 'element-plus'
import GrayEmptyDataIcon from '~/assets/svg/gray-empty-data.vue'
import LogCircle from './icon/log-circle.vue'

const route = useRoute()
const loading = ref(false)
const isEmpty = ref(false)
const list = ref([] as any)

/**
 * @description: 获取数据
 * @return {*}
 */
const getList = async () => {
  const params = {
    project: route.query.project,
    service_id: route.query.service
  }
  try {
    loading.value = true
    const res = await apiGetLogInfo(params)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      isEmpty.value = !(list.value && list.value.length > 0)
    }
  } catch (error) {
    loading.value = false
  }
}

onBeforeMount(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.log-info-container {
  padding: 24px;
  border-radius: 4px;
  border: 1px solid #dcf2f3;
  background-color: #fff;
  .vertical-line {
    position: relative;
  }
  .vertical-line::before {
    content: '';
    position: absolute;
    top: 24px;
    left: 8px;
    width: 1px;
    height: 46px;
    background-color: #d9d9d9;
  }
  .time-text {
    font-size: 12px;
    line-height: 18px;
    color: #8c8c8c;
  }
  .title-text {
    font-size: 14px;
    line-height: 22px;
    font-weight: bold;
    color: #262626;
  }
}
:deep(.el-empty__description) {
  margin-left: 40px;
}
</style>
