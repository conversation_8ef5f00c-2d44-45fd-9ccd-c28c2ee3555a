<template>
  <div class="detail-info-container" v-if="!loading">
    <!-- 服务信息 -->
    <div class="detail-card" v-if="serviceInfo">
      <div class="flex-box flex_a_i-center gap_8">
        <CarIcon />
        <span class="card-title">{{ $t('satisfaction.pp_service_information') }}</span>
      </div>
      <div class="row-container">
        <span class="label-text">{{ $t('common.pp_device_name') }}</span>
        <span class="value-text"><WelkinCopyBoard :text="serviceInfo.description || $t('common.pp_unnamed_device')" :showIcon="false" /></span>
        <span class="label-text">{{ $t('satisfaction.pp_order_start_time') }}</span>
        <span class="value-text">{{ formatTime(serviceInfo.order_start_time) }}</span>
        <span class="label-text">{{ $t('satisfaction.pp_order_end_time') }}</span>
        <span class="value-text" style="margin-right: 0">{{ formatTime(serviceInfo.order_end_time) }}</span>
      </div>
      <div class="row-container">
        <span class="label-text">{{ $t('common.pp_device_id') }}</span>
        <span class="value-text"><WelkinCopyBoard :text="serviceInfo.device_id" direction="rtl" /></span>
        <span class="label-text">{{ $t('station.pp_service_start_time') }}</span>
        <span class="value-text">{{ formatTime(serviceInfo.service_start_time) }}</span>
        <span class="label-text">{{ $t('station.pp_service_end_time') }}</span>
        <span class="value-text" style="margin-right: 0">{{ formatTime(serviceInfo.service_end_time) }}</span>
      </div>
      <div class="row-container">
        <span class="label-text">{{ $t('satisfaction.pp_order_id') }}</span>
        <span class="value-text"><WelkinCopyBoard :text="serviceInfo.order_id" direction="rtl" /></span>
        <span class="label-text">{{ $t('satisfaction.pp_service_id') }}</span>
        <span class="value-text"><WelkinCopyBoard :text="serviceInfo.service_id" direction="rtl" /></span>
        <span class="label-text">{{ $t('satisfaction.pp_comment_id') }}</span>
        <span class="value-text" style="margin-right: 0"><WelkinCopyBoard :text="serviceInfo.comment_id" direction="rtl" /></span>
      </div>
      <div class="row-container">
        <span class="label-text">{{ $t('satisfaction.pp_vehicle_id') }}</span>
        <span class="value-text"><WelkinCopyBoard :text="serviceInfo.vehicle_id" direction="rtl" /></span>
        <span class="label-text">{{ $t('station.pp_service_battery_id') }}</span>
        <span class="value-text"><WelkinCopyBoard :text="serviceInfo.service_battery_id" direction="rtl" /></span>
        <span class="label-text">{{ $t('satisfaction.pp_vehicle_battery_id') }}</span>
        <span class="value-text" style="margin-right: 0"><WelkinCopyBoard :text="serviceInfo.vehicle_battery_id" direction="rtl" /></span>
      </div>
      <div class="row-container">
        <span class="label-text">{{ $t('common.pp_vehicle_brand') }}</span>
        <span class="value-text">{{ serviceInfo.ev_brand ? serviceInfo.ev_brand : '' }}-{{ serviceInfo.ev_type ? serviceInfo.ev_type : '' }}</span>
        <span class="label-text">{{ $t('satisfaction.pp_swap_reversed') }}</span>
        <span class="value-text">{{ serviceInfo.is_reverse_swap ? $t('common.pp_yes') : $t('common.pp_no') }}</span>
        <span class="label-text">{{ $t('satisfaction.pp_swap_automated') }}</span>
        <span class="value-text">{{ serviceInfo.is_automated_swap ? $t('common.pp_yes') : $t('common.pp_no') }}</span>
      </div>
    </div>
    <div class="detail-card" v-else>
      <div class="flex-box flex_a_i-center gap_8">
        <CarIcon />
        <span class="card-title">{{ $t('satisfaction.pp_service_information') }}</span>
      </div>
      <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>

    <!-- 用户感知类信息 -->
    <div class="detail-card" v-if="userExperienceInfo">
      <div class="flex-box flex_a_i-center gap_8">
        <UserIcon />
        <span class="card-title">{{ $t('satisfaction.pp_user_perception') }}</span>
      </div>
      <div class="row-container">
        <span class="label-text">{{ $t('satisfaction.pp_billing_degree') }}</span>
        <span class="value-text">{{ userExperienceInfo.electricity_kwh + $t('satisfaction.pp_degree') }}</span>
        <span class="label-text">{{ $t('satisfaction.pp_old_battery_soc') }}</span>
        <span class="value-text">{{ `${userExperienceInfo.vehicle_battery_soc}%（${userExperienceInfo.vehicle_battery_soc_oss}%）` }}</span>
        <span class="label-text">{{ $t('satisfaction.pp_new_battery_soc') }}</span>
        <span class="value-text">{{ `${userExperienceInfo.service_battery_soc}%（${userExperienceInfo.service_battery_soc_oss}%）` }}</span>
      </div>
      <div class="row-container">
        <span class="label-text">{{ $t('satisfaction.pp_old_battery_degree') }}</span>
        <span class="value-text">{{ userExperienceInfo.vehicle_battery_capacity + $t('satisfaction.pp_degree') }}</span>
        <span class="label-text">{{ $t('satisfaction.pp_new_battery_degree') }}</span>
        <span class="value-text">{{ userExperienceInfo.service_battery_capacity + $t('satisfaction.pp_degree') }}</span>
      </div>
    </div>
    <div class="detail-card" v-else>
      <div class="flex-box flex_a_i-center gap_8">
        <UserIcon />
        <span class="card-title">{{ $t('satisfaction.pp_user_perception') }}</span>
      </div>
      <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>

    <!-- 评价信息 -->
    <div class="detail-card" v-if="commentInfo">
      <div class="flex-box flex_a_i-center gap_8">
        <CommentIcon />
        <span class="card-title">{{ $t('satisfaction.pp_comment_information') }}</span>
      </div>
      <div class="row-container two-column">
        <span class="label-text">{{ $t('satisfaction.pp_rating_stars') }}</span>
        <div class="flex-box gap_8 value-text">
          <StarIcon v-for="item in commentInfo.score" v-if="commentInfo.score > 0" />
          <NonStarIcon v-for="item in 5 - commentInfo.score" v-if="commentInfo.score < 5" />
        </div>
        <span class="label-text">{{ $t('satisfaction.pp_comment_tag') }}</span>
        <span class="value-text" style="margin-right: 0">{{ commentInfo.user_tag && commentInfo.user_tag.length > 0 ? commentInfo.user_tag.join('、') : '-' }}</span>
      </div>
      <div class="row-container one-column">
        <span class="label-text">{{ $t('satisfaction.pp_comment_text') }}</span>
        <span class="value-text" style="margin-right: 0">{{ commentInfo.comment || '-' }}</span>
      </div>
      <div class="row-container one-column">
        <span class="label-text">{{ $t('satisfaction.pp_low_score_reason') }}</span>
        <span class="value-text" style="margin-right: 0">{{ commentInfo.reason || '-' }}</span>
      </div>
      <div class="row-container one-column">
        <span class="label-text">{{ $t('satisfaction.pp_solution') }}</span>
        <span class="value-text" style="margin-right: 0">{{ commentInfo.solution || '-' }}</span>
      </div>
    </div>
    <div class="detail-card" v-else>
      <div class="flex-box flex_a_i-center gap_8">
        <CommentIcon />
        <span class="card-title">{{ $t('satisfaction.pp_comment_information') }}</span>
      </div>
      <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>
  </div>
  <div v-else>
    <el-skeleton :rows="15" animated />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { formatTime } from '~/utils'
import { apiGetDetailInfo } from '~/apis/satisfaction-analysis'
import { ElMessage } from 'element-plus'
import CarIcon from './icon/car-icon.vue'
import UserIcon from './icon/user-icon.vue'
import CommentIcon from './icon/comment-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'
import StarIcon from '~/views/satisfaction-analysis/components/icon/star-icon.vue'
import NonStarIcon from '~/views/satisfaction-analysis/components/icon/non-star.vue'

const route = useRoute()
const loading = ref(false)
const serviceInfo = ref({} as any)
const userExperienceInfo = ref({} as any)
const commentInfo = ref({} as any)
const list = ref({} as any)

/**
 * @description: 获取数据
 * @return {*}
 */
const getList = async () => {
  const params = {
    project: route.query.project,
    order_id: route.query.order
  }
  try {
    loading.value = true
    const res = await apiGetDetailInfo(params)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      serviceInfo.value = list.value.service_info
      userExperienceInfo.value = list.value.user_experience_info
      commentInfo.value = list.value.comment_info
    }
  } catch (error) {
    loading.value = false
    serviceInfo.value = null
    userExperienceInfo.value = null
    commentInfo.value = null
  }
}

onBeforeMount(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.detail-info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  .detail-card {
    padding: 24px;
    border: 1px solid #dcf2f3;
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 12px;
    :deep(.el-empty) {
      padding: 0;
      .el-empty__image {
        width: 100px;
      }
      .el-empty__description {
        margin-left: 20px;
      }
    }
    .card-title {
      font-size: 16px;
      line-height: 24px;
      font-weight: bold;
      color: #262626;
    }
    .row-container {
      display: grid;
      grid-template-columns: 120px 3fr 120px 3fr 120px 2fr;
      .label-text {
        font-size: 14px;
        line-height: 22px;
        color: #8c8c8c;
        white-space: nowrap;
        overflow: hidden;
      }
      .value-text {
        font-size: 14px;
        line-height: 22px;
        color: #262626;
        overflow: hidden;
        margin-right: 60px;
      }
    }
    .two-column {
      display: grid;
      grid-template-columns: 120px 3fr 120px 5fr 120px;
    }
    .one-column {
      display: grid;
      grid-template-columns: 120px 1fr;
    }
  }
}
</style>
