<template>
  <div class="cluster-detail-container">
    <!-- header -->
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item @click="handleJump">
            {{ $t('edgeCloud.pp_cloud') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item @click="handleJump">
            {{ $t('edgeCloud.pp_cluster_list') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t('edgeCloud.pp_cluster_detail') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="single-station-header-title">
          <div class="header-title">
            {{ $t('edgeCloud.pp_cluster_detail') }}
          </div>
          <div class="title-device margin_r-20 margin_t-5">
            <span class="station-device">
              {{ clusterInfo[0]?.description }}
            </span>
            <span class="device-id">
              {{ clusterInfo[0]?.device_id }}
            </span>
          </div>
          <div class="tag-container">
            <el-tag effect="dark" :disable-transitions="true" round>
              <div class="tag-info">
                <powerswap2-logo v-if="clusterInfo[0]?.project == 'PowerSwap2'" />
                <powerswap3-logo v-if="clusterInfo[0]?.project == 'PUS3'" />
                <span class="tag-title">
                  {{ clusterInfo[0]?.project == 'PowerSwap2' ? $t(`menu.pp_swap_station2`) : $t(`menu.pp_swap_station3`) }}
                </span>
              </div>
            </el-tag>
          </div>
        </div>
      </div>
      <!-- <div class="header-right">
        <el-button class="add-user-button">{{ $t('edgeCloud.pp_stop_schedule') }}</el-button>
      </div> -->
    </div>

    <!-- body -->
    <div class="swap-page-container">
      <div class="search-container">
        <span class="search-item-label">
          {{ $t('edgeCloud.pp_app_name') }}
        </span>
        <el-select v-model="searchForm.app_name" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 50%; margin-right: 20px">
          <el-option v-for="item in appOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <span class="search-item-label">
          {{ $t('edgeCloud.pp_project') }}
        </span>
        <el-select v-model="searchForm.namespace" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 50%; margin-right: 20px">
          <el-option v-for="item in projectOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <span class="search-item-label white-space-nowrap">{{ $t('edgeCloud.pp_instance_status') }}</span>
        <el-select v-model="searchForm.status" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 50%; margin-right: 20px">
          <el-option v-for="item in statusOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div class="search-button">
          <el-button @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
          <el-button @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
        </div>
      </div>
      <div class="swap-table-container">
        <el-table
          :data="tableData"
          row-key="app_name"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
          @expand-change="handleExpandTable"
        >
          <el-table-column type="expand">
            <template #default="scope">
              <div class="inner-container">
                <el-table border :data="scope.row.instances" row-key="instance_name">
                  <el-table-column prop="instance_name" :label="`${$t('edgeCloud.pp_instance')}`" min-width="200" fixed show-overflow-tooltip />
                  <el-table-column prop="status" :label="`${$t('edgeCloud.pp_running_status')}`" min-width="100" show-overflow-tooltip />
                  <el-table-column prop="subsystem" :label="`${$t('edgeCloud.pp_sub_system')}`" min-width="100" show-overflow-tooltip />
                  <el-table-column :label="`${$t('edgeCloud.pp_resource_usage')}`" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                      <div>
                        <span>CPU：</span>
                        <span>{{ scope.row.cpu_usage + ' / ' + scope.row.cpu_request }}</span>
                      </div>
                      <div>
                        <span>{{$t('edgeCloud.pp_memory')}}：</span>
                        <span>{{ scope.row.memory_usage + ' / ' + scope.row.memory_request }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="`${$t('common.pp_operation')}`" min-width="140" show-overflow-tooltip>
                    <template #default="scope">
                      <span @click="handleViewDetails(scope.$index, scope.row)" class="edit-text margin_r-10">{{ $t('edgeCloud.pp_details') }}</span>
                      <span @click="handleViewEvents(scope.$index, scope.row)" class="edit-text margin_r-10">{{ $t('edgeCloud.pp_event') }}</span>
                      <span @click="handleViewLogs(scope.$index, scope.row)" class="edit-text margin_r-10">{{ $t('edgeCloud.pp_log') }}</span>
                      <span @click="handleDeleteInstance(scope.$index, scope.row)" class="edit-text">{{ $t('edgeCloud.pp_restart') }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <!-- <div class="pagination-container children-page">
                <Page v-if="(scope.row.nodeList && scope.row.nodeList.length > 0) || !scope.row.nodeList" :page="scope.row.page" @change="(val) => handleChildPageChange(val, scope.row)" />
              </div> -->
            </template>
          </el-table-column>
          <el-table-column prop="app_name" :label="`${$t('edgeCloud.pp_app_name')}`" show-overflow-tooltip />
          <el-table-column prop="status" label="READY">
            <template #default="scope">
              <span :class="scope.row.available_replicas == scope.row.replicas ? 'ready-status' : 'unready-status'">{{ scope.row.available_replicas + '/' + scope.row.replicas }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="namespace" :label="`${$t('edgeCloud.pp_project')}`" />
          <el-table-column :label="`${$t('edgeCloud.pp_create_time')}`" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.create_ts) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <Page v-if="(tableData && tableData.length > 0) || !tableData" :page="pages" @change="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- 实例详情 -->
    <el-dialog v-model="detailDialogVisible" :title="`${$t('edgeCloud.pp_instance_detail')}`" width="600px" align-center>
      <el-descriptions :column="1">
        <el-descriptions-item :label="`${$t('edgeCloud.pp_instance')}`">{{ detailInfo.instance_name }}</el-descriptions-item>
        <el-descriptions-item :label="`${$t('edgeCloud.pp_create_time')}`">{{ formatTime(detailInfo.create_ts) }}</el-descriptions-item>
        <el-descriptions-item :label="`${$t('edgeCloud.pp_image')}`">{{ detailInfo.image }}</el-descriptions-item>
        <el-descriptions-item :label="`${$t('edgeCloud.pp_port_info')}`">
          <el-table
            :data="detailInfo.ports"
            style="width: 80%"
            :header-cell-style="{
              fontSize: '14px',
              color: '#292C33',
              cursor: 'auto'
            }"
            border
          >
            <el-table-column prop="protocol" :label="`${$t('edgeCloud.pp_port_protocol')}`" show-overflow-tooltip/>
            <el-table-column prop="name" :label="`${$t('edgeCloud.pp_port_name')}`" show-overflow-tooltip/>
            <el-table-column prop="port" :label="`${$t('edgeCloud.pp_port')}`" show-overflow-tooltip/>
          </el-table>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 事件详情 -->
    <el-dialog v-model="eventDialogVisible" :title="`${$t('edgeCloud.pp_event')}`" width="600px" align-center>
      <el-table
        :data="eventInfo"
        style="width: 100%"
        :header-cell-style="{
          fontSize: '14px',
          color: '#292C33',
          cursor: 'auto'
        }"
        border
      >
        <el-table-column prop="type" label="type" show-overflow-tooltip/>
        <el-table-column prop="reason" label="reason" show-overflow-tooltip/>
        <el-table-column prop="age" label="age" show-overflow-tooltip/>
        <el-table-column prop="from" label="from" show-overflow-tooltip/>
        <el-table-column prop="message" label="message" show-overflow-tooltip/>
      </el-table>
    </el-dialog>

    <!-- 日志详情 -->
    <el-dialog v-model="logDialogVisible" :title="`${$t('edgeCloud.pp_log')}`" width="750px" align-center>
      <div v-html="logInfo" class="log-container"></div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {ref, onBeforeMount, onBeforeUnmount, watch} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {removeNullProp, formatTime} from '~/utils'
import {page} from '~/constvars/page'
import {useI18n} from 'vue-i18n'
import {ElMessage, ElMessageBox} from 'element-plus'
import {apiGetDeviceList} from '~/apis/device-management'
import {apiGetAppWorkloadList, apiGetAppOptions, apiGetNamespaceOptions, apiDeleteInstance, apiGetEventList, apiGetlogList} from '~/apis/edge-cloud'
import Powerswap3Logo from '../components/powerswap3-logo.vue'
import Powerswap2Logo from '../components/powerswap2-logo.vue'
import _ from 'lodash'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const getListTimeout = ref()
const loading = ref(false)
const detailDialogVisible = ref(false)
const eventDialogVisible = ref(false)
const logDialogVisible = ref(false)
const namespace = ref('')
const logInfo = ref('' as any)
const pages = ref(_.cloneDeep(page))
const tableData = ref([])
const clusterInfo = ref([] as any)
const appOptions = ref([])
const projectOptions = ref([])
const statusOptions = ref(['Running', 'Pending', 'Succeeded', 'Failed', 'Waiting', 'Terminated', 'Unknown'])
const eventInfo = ref([] as any)
const detailInfo = ref({} as any)
const searchForm = ref({
  app_name: '',
  namespace: '',
  status: '',
  device_type: route.query.device_type as string,
  page: 1,
  size: 10
})
const form = ref({
  app_name: '',
  namespace: '',
  status: '',
  device_type: route.query.device_type as string,
  page: 1,
  size: 10
})

/**
 * @description: 跳转回集群列表
 * @return {*}
 */
const handleJump = () => {
  router.push({
    path: `/cloud/cluster-list`,
    query: JSON.parse(sessionStorage.getItem('cloud-list')!)
  })
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  searchForm.value.app_name = ''
  searchForm.value.namespace = ''
  searchForm.value.status = ''
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  pages.value.size = 10
  form.value = {...searchForm.value}
  getDeviceList(true, 2)
}

/**
 * @description: 搜索
 * @param {*} updateRoute
 * @return {*}
 */
const getDeviceList = async (updateRoute = true, status: any) => {
  if (loading.value) return
  clearTimeout(getListTimeout.value)
  let formData = {} as any
  if (status === 2) {
    formData = {...form.value}
  } else {
    formData = {...searchForm.value}
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  if (updateRoute) {
    router.push({
      path: `/cloud/cluster-list/cluster-detail/${route.params.clusterId}`,
      query: {...removeNullProp(formData)}
    })
  }
  loading.value = true
  try {
    const res = await apiGetAppWorkloadList(formData.device_type, route.params.clusterId as string, formData)
    pages.value.total = res.total
    tableData.value = res.data
    getListTimeout.value = setTimeout(() => {
      getDeviceList(false, 2)
    }, 5000)
  } catch (error) {}
  loading.value = false
}

/**
 * @description: 外层分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getDeviceList(true, 2)
}

/**
 * @description: 内层分页
 * @param {*} argPage
 * @param {*} row
 * @return {*}
 */
// const handleChildPageChange = (argPage: any, row: any) => {
//   row.page.current = argPage.current
//   row.page.size = argPage.size
// }

/**
 * @description: 查看详情
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleViewDetails = (index: number, row: any) => {
  detailInfo.value = row
  detailDialogVisible.value = true
}

/**
 * @description: 查看事件
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleViewEvents = async (index: number, row: any) => {
  const params = {namespace: namespace.value}
  try {
    const res = await apiGetEventList(searchForm.value.device_type, row.instance_name, params)
    eventInfo.value = res.event_list
  } catch (error) {}
  eventDialogVisible.value = true
}

/**
 * @description: 获取实例运行时Log
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleViewLogs = async (index: number, row: any) => {
  const params = {namespace: namespace.value}
  try {
    const res = await apiGetlogList(searchForm.value.device_type, row.instance_name, params)
    logInfo.value = res.message.replace(/\n/g,"<br/>")
  } catch (error) {}
  logDialogVisible.value = true
}

/**
 * @description: 重启实例
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleDeleteInstance = async (index: number, row: any) => {
  ElMessageBox.confirm(`${t('edgeCloud.pp_sure_restart')} ${row.instance_name}？`, {
    confirmButtonText: t('common.pp_confirm'),
    cancelButtonText: t('common.pp_cancel'),
    type: 'warning'
  }).then(async () => {
    const params = {namespace: namespace.value}
    try {
      const res = await apiDeleteInstance(searchForm.value.device_type, row.instance_name, params)
      if (!res.err_code) ElMessage.success(t('edgeCloud.pp_restart_success'))
    } catch (error) {}
    getDeviceList(false, 2)
  })
}

/**
 * @description: 展开行
 * @param {*} row
 * @return {*}
 */
const handleExpandTable = async (row: any) => {
  namespace.value = row.namespace
}

/**
 * @description: 获取APP名称筛选项
 * @return {*}
 */
const getAppOptions = async () => {
  const res = await apiGetAppOptions(searchForm.value.device_type)
  appOptions.value = res.app_name_list
}

/**
 * @description: 筛选项-项目列表
 * @return {*}
 */
const getNamespaceOptions = async () => {
  const res = await apiGetNamespaceOptions(searchForm.value.device_type)
  projectOptions.value = res.data
}

/**
 * @description: 初始化筛选条件
 * @return {*}
 */
const initWeb = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      searchForm.value.app_name = !!initParams.app_name ? initParams.app_name : ''
      searchForm.value.namespace = !!initParams.namespace ? initParams.namespace : ''
      searchForm.value.status = !!initParams.status ? initParams.status : ''
      form.value = {...searchForm.value}
      getDeviceList(false, 2)
    }
  },
  {immediate: true}
)

/**
 * @description: 组件销毁卸载轮询，定时器清空
 * @return {*}
 */
onBeforeUnmount(() => {
  clearTimeout(getListTimeout.value)
})

onBeforeMount(() => {
  initWeb()
  getAppOptions()
  getNamespaceOptions()
  const deviceParams = {
    device: route.params.clusterId,
    page_no: 1,
    page_size: 10
  }
  apiGetDeviceList(deviceParams, route.query.device_type).then((res) => {
    clusterInfo.value = res.data
  })
})
</script>

<style lang="scss" scoped>
.cluster-detail-container {
  .edit-text {
    color: #00bebe;
    &:hover {
      cursor: pointer;
      color: #66d2d2;
    }
  }
  .children-page {
    width: auto;
    margin: 8px 50px 10px;
    ::v-deep(.el-pagination) {
      margin: 0;
    }
  }
  .log-container {
    background: black;
    color: #b0bec5;
    padding: 10px;
  }
  .inner-container {
    padding: 20px 50px;
  }
  .ready-status {
    color: #00bebe;
  }
  .unready-status {
    color: #ff6d00;
  }
  ::v-deep(.el-descriptions__cell) {
    display: flex;
  }
  ::v-deep(.el-descriptions__label) {
    color: #22252b !important;
    white-space: nowrap;
    font-weight: bold;
    font-family: 'Noto Sans';
  }
  ::v-deep(.el-descriptions__content) {
    color: #22252b !important;
    font-family: 'Noto Sans';
    .el-table {
      .cell {
        color: #22252b;
        font-family: 'Noto Sans';
      }
    }
  }
  ::v-deep(.el-dialog__body) {
    padding-top: 15px;
    padding-bottom: 15px;
    .el-table {
      .cell {
        color: #22252b;
        font-family: 'Noto Sans';
      }
    }
  }
}
</style>
