<template>
  <div >
      <div class="swap-page-header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>{{ $t('menu.pp_device_simulation') }}</el-breadcrumb-item>
            <el-breadcrumb-item>{{ $t('menu.pp_configuration_management') }}</el-breadcrumb-item>
          </el-breadcrumb>
          <div class="header-title">{{ $t('menu.pp_configuration_management') }}</div>
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
import {useI18n} from 'vue-i18n'
</script>

<style lang="scss" scoped>

</style>
