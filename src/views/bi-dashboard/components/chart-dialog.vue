<template>
  <div class="chart-dialog">
    <el-dialog :title="drillDownData.name" v-model="chartDialogVisible" width="1200px" align-center @close="handleCloseDialog">
      <div class="font-size-14 line-height-22 color-26">输出功率</div>
      <div style="width: 100%; height: 200px" class="flex-box flex_j_c-center flex_a_i-center">
        <MyChart :option="drillDownData.option" v-if="drillDownData.show" class="cursor-default-canvas" />
        <el-empty v-else :description="$t('common.pp_empty')">
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'

const props = defineProps({
  chartDialogVisible: {
    type: Boolean,
    default: false
  },
  drillDownData: {
    type: Object,
    default: {}
  }
})
const emits = defineEmits(['update:chartDialogVisible'])

const handleCloseDialog = () => {
  emits('update:chartDialogVisible', false)
}
</script>

<style lang="scss" scoped>
.chart-dialog {
  :deep(.el-dialog__header) {
    padding: 24px 24px 16px;
    .el-dialog__title {
      color: #1f1f1f;
      font-size: 18px;
    }
  }
  :deep(.el-dialog__body) {
    padding: 0 24px 24px;
  }
}
</style>
