<template>
  <div class="drag-card">
    <div class="flex-box flex_j_c-space-between flex_a_i-center">
      <div class="flex-box flex_a_i-center gap_8 mover cursor-move">
        <DragIcon />
        <span class="font-size-16 line-height-24 color-26 font-weight-500">功率分配</span>
      </div>
      <div class="flex-box flex_a_i-center gap_4 cursor-pointer" @click="handleDownload('power_distribution')">
        <DownloadIcon />
        <span class="font-size-14 line-height-22 color-a0">数据下载</span>
      </div>
    </div>

    <div class="flex-box flex_d-column gap_16">
      <div class="width-full">
        <div class="font-size-14 line-height-22 color-26">不同时间整站实际输出功率</div>
        <div style="width: 100%; height: 200px" class="flex-box flex_j_c-center flex_a_i-center">
          <MyChart :option="chartOption3" v-if="hasChart3" class="cursor-default-canvas" />
          <el-empty v-else :description="$t('common.pp_empty')">
            <template #image>
              <WhiteEmptyDataIcon />
            </template>
          </el-empty>
        </div>
      </div>
      <div class="width-full">
        <div class="font-size-14 line-height-22 color-26">不同时间模块使用个数</div>
        <div style="width: 100%; height: 200px" class="flex-box flex_j_c-center flex_a_i-center">
          <MyChart :option="chartOption4" v-if="hasChart4" class="cursor-default-canvas" />
          <el-empty v-else :description="$t('common.pp_empty')">
            <template #image>
              <WhiteEmptyDataIcon />
            </template>
          </el-empty>
        </div>
      </div>
      <div class="width-full">
        <div class="font-size-14 line-height-22 color-26">输出功率</div>
        <div style="width: 100%; height: 200px" class="flex-box flex_j_c-center flex_a_i-center">
          <MyChart :option="chartOption5" v-if="hasChart5" @handleClickChart="handleClickChart" />
          <el-empty v-else :description="$t('common.pp_empty')">
            <template #image>
              <WhiteEmptyDataIcon />
            </template>
          </el-empty>
        </div>
        <ChartDialog v-model:chartDialogVisible="chartDialogVisible" :drillDownData="drillDownData" v-if="chartDialogVisible" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { barOptionMock1 } from '../constant'
import ChartDialog from '../chart-dialog.vue'
import DragIcon from '../icons/drag-icon.vue'
import DownloadIcon from '../icons/download-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'

const props = defineProps<{
  list: any
  chartOption3: any
  chartOption4: any
  chartOption5: any
  hasChart3: boolean
  hasChart4: boolean
  hasChart5: boolean
}>()
const emits = defineEmits(['handleDownload'])

const chartDialogVisible = ref(false)
const drillDownData = ref({} as any)
const drillDownOption = cloneDeep(barOptionMock1)

const handleDownload = (type: string) => {
  emits('handleDownload', type)
}

/**
 * @description: 输出功率下钻
 * @param {*} params
 * @return {*}
 */
const handleClickChart = (params: any) => {
  const moduleData = props.list.power_distribution.module_output_power.find((item: any) => item.module === params.name)
  if (moduleData.hourly_value && moduleData.hourly_value.length > 0) {
    drillDownOption.color = ['#68C789']
    drillDownOption.grid.top = '16'
    drillDownOption.dataZoom = []
    drillDownOption.series[0].barMaxWidth = 16
    drillDownOption.series[0].tooltip = {
      valueFormatter: (value: any) => value + 'kW'
    }
    drillDownOption.xAxis.data = moduleData.hourly_value.map((item: any) => item.hour.toString().padStart(2, '0') + ':00')
    drillDownOption.series[0].data = moduleData.hourly_value.map((item: any) => item.value)
    drillDownData.value = {
      name: params.name,
      show: true,
      option: drillDownOption
    }
  } else {
    drillDownData.value = {
      name: params.name,
      show: false
    }
  }
  chartDialogVisible.value = true
}
</script>
