<template>
  <div class="drag-card">
    <div class="flex-box flex_j_c-space-between flex_a_i-center bottom-line">
      <div class="flex-box flex_a_i-center gap_8 mover cursor-move">
        <DragIcon />
        <span class="font-size-16 line-height-24 color-26 font-weight-500">可靠性</span>
      </div>
      <div class="flex-box flex_a_i-center gap_4 cursor-pointer" @click="handleDownload('reliability')">
        <DownloadIcon />
        <span class="font-size-14 line-height-22 color-a0">数据下载</span>
      </div>
    </div>

    <div class="flex-box flex_j_c-space-between" v-if="list.reliability">
      <div class="flex-box flex_d-column flex_j_c-space-around gap_16">
        <div>
          <div class="font-size-14 line-height-22 color-59 font-weight-bold">整站FTT</div>
          <div class="font-size-20 color-26 margin_t-8">{{ list.reliability.swap_ftt ?? '-' }} %</div>
        </div>
        <div>
          <div class="font-size-14 line-height-22 color-59 font-weight-bold">充电FTT</div>
          <div class="font-size-20 color-26 margin_t-8">{{ list.reliability.charge_ftt ?? '-' }} %</div>
        </div>
      </div>
      <div style="width: 35%; height: 210px" class="flex-box flex_j_c-center flex_a_i-center">
        <MyChart :option="chartOption1" v-if="hasChart1" class="cursor-default-canvas" />
        <el-empty v-else :description="$t('biDashboard.pp_no_pie_chart')">
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>
      <div style="width: 55%; height: 210px" class="flex-box flex_j_c-center flex_a_i-center">
        <MyChart :option="chartOption2" v-if="hasChart2" class="cursor-default-canvas" />
        <el-empty v-else :description="$t('biDashboard.pp_no_bar_chart')">
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>
    </div>

    <div v-else class="width-full height-180 flex-box flex_j_c-center flex_a_i-center">
      <el-empty :description="$t('common.pp_empty')">
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import DragIcon from '../icons/drag-icon.vue'
import DownloadIcon from '../icons/download-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'

const props = defineProps<{
  list: any
  chartOption1: any
  chartOption2: any
  hasChart1: boolean
  hasChart2: boolean
}>()
const emits = defineEmits(['handleDownload'])

const handleDownload = (type: string) => {
  emits('handleDownload', type)
}
</script>
