<template>
  <div class="drag-card">
    <div class="flex-box flex_j_c-space-between flex_a_i-center">
      <div class="flex-box flex_a_i-center gap_8 mover cursor-move">
        <DragIcon />
        <span class="font-size-16 line-height-24 color-26 font-weight-500">能源管理收益</span>
      </div>
      <div class="flex-box flex_a_i-center gap_4 cursor-pointer" @click="handleDownload('energy_management_revenue')">
        <DownloadIcon />
        <span class="font-size-14 line-height-22 color-a0">数据下载</span>
      </div>
    </div>

    <div class="flex-box flex_d-column gap_16">
      <div class="width-full">
        <div class="font-size-14 line-height-22 color-26" v-if="!hasChart6">充电电量&充电成本预估</div>
        <div style="width: 100%; height: 200px" class="flex-box flex_j_c-center flex_a_i-center relative">
          <div class="absolute" style="left: 0px; top: 0px; color: #8c8c8c" v-show="hasChart6">充电电量（kWh）</div>
          <div class="absolute" style="right: 0px; top: 0px; color: #8c8c8c" v-show="hasChart6">充电成本预估（元）</div>
          <MyChart :option="chartOption6" v-if="hasChart6" class="cursor-default-canvas" />
          <el-empty v-else :description="$t('common.pp_empty')">
            <template #image>
              <WhiteEmptyDataIcon />
            </template>
          </el-empty>
        </div>
      </div>
      <div class="width-full">
        <div class="font-size-14 line-height-22 color-26" v-if="!hasChart7">放电电量&放电成本预估</div>
        <div style="width: 100%; height: 200px" class="flex-box flex_j_c-center flex_a_i-center relative">
          <div class="absolute" style="left: 0px; top: 0px; color: #8c8c8c" v-show="hasChart7">放电电量（kWh）</div>
          <div class="absolute" style="right: 0px; top: 0px; color: #8c8c8c" v-show="hasChart7">放电成本预估（元）</div>
          <MyChart :option="chartOption7" v-if="hasChart7" class="cursor-default-canvas" />
          <el-empty v-else :description="$t('common.pp_empty')">
            <template #image>
              <WhiteEmptyDataIcon />
            </template>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DragIcon from '../icons/drag-icon.vue'
import DownloadIcon from '../icons/download-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'

const props = defineProps<{
  list: any
  chartOption6: any
  chartOption7: any
  hasChart6: boolean
  hasChart7: boolean
}>()
const emits = defineEmits(['handleDownload'])

const handleDownload = (type: string) => {
  emits('handleDownload', type)
}
</script>
