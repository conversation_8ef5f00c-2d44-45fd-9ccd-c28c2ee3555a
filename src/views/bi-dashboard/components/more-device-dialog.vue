<template>
  <div class="more-device-dialog">
    <el-dialog :title="$t('biDashboard.pp_device_basic_info')" v-model="moreDeviceVisible" width="1152px" align-center @open="handleOpenDialog" @close="handleCloseDialog">
      <el-form :model="form" class="common-form">
        <el-form-item :label="$t('common.pp_device_select')">
          <el-select v-model="form.device_ids" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-378">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
              <span style="float: left">{{ item.description }}</span>
              <span :style="{ float: 'right', color: projectMap[item.project].color, fontSize: '13px' }">{{ $t(projectMap[item.project].name) }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleSearch" class="welkin-primary-button">{{ $t('common.pp_search') }}</el-button>
        </el-form-item>
      </el-form>

      <div class="card-container">
        <DeviceCard v-for="item in filterBasicInfo.slice((pages.current - 1) * pages.size, pages.current * pages.size)" :cardInfo="item" />
      </div>

      <div class="common-pagination">
        <Page :page="pages" :pagerCount="5" :layout="'prev, pager, next'" @change="handlePageChange" class="mini-page" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { projectMap } from './constant'
import { cloneDeep } from 'lodash-es'
import { page } from '~/constvars/page'
import DeviceCard from './device-card.vue'

interface Props {
  moreDeviceVisible: boolean
  basicInfo: any[]
  deviceOptions: any[]
}

const props = defineProps<Props>()
const emits = defineEmits(['update:moreDeviceVisible'])

const form = ref({
  device_ids: [] as string[]
})
const filterBasicInfo = ref([] as any)
const pages = ref(cloneDeep(page))

const handleSearch = () => {
  pages.value.current = 1
  filterBasicInfo.value = form.value.device_ids.length === 0 ? props.basicInfo.slice() : props.basicInfo.filter((item: any) => form.value.device_ids.includes(item.device_id))
  pages.value.total = filterBasicInfo.value.length
}

const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
}

const handleCloseDialog = () => {
  emits('update:moreDeviceVisible', false)
}

const handleOpenDialog = () => {
  form.value.device_ids = []
  filterBasicInfo.value = cloneDeep(props.basicInfo)
  pages.value.current = 1
  pages.value.size = 8
  pages.value.total = filterBasicInfo.value.length
}
</script>

<style lang="scss" scoped>
.more-device-dialog {
  :deep(.el-dialog__header) {
    padding: 24px 24px 16px;
    .el-dialog__title {
      color: #1f1f1f;
      font-size: 18px;
    }
  }
  :deep(.el-dialog__body) {
    padding: 0 24px 24px;
    .common-form {
      margin-bottom: 16px !important;
    }
    .card-container {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .common-pagination {
      margin-top: 16px !important;
      .mini-page.is-background .el-pager li {
        width: 28px;
        height: 28px;
      }
    }
  }
}
</style>
