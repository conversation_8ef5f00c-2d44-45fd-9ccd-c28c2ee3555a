<template>
  <div class="device-card" :class="{'one-device-card': isOneDevice}">
    <div class="flex-box flex_j_c-space-between flex_a_i-center with-bottom-border">
      <div class="flex-box flex_a_i-center gap_6 color-26 font-size-14 white-space-nowrap" style="width: calc(100% - 100px)">
        <WelkinCopyBoard :text="cardInfo.description" :showIcon="false" class="font-weight-500" style="max-width: 50%" />
        <WelkinCopyBoard :text="cardInfo.device_id" :showIcon="false" style="max-width: 30%" />
        <div class="highway-tag" v-if="cardInfo.is_highway_swap">{{ $t('biDashboard.pp_high_way') }}</div>
      </div>
      <div class="project-tag" :style="{ color: projectMap[cardInfo.project].color, backgroundColor: projectMap[cardInfo.project].background }">
        <Pss3Icon v-if="cardInfo.project === 'PUS3'" />
        <Pss4Icon v-if="cardInfo.project === 'PUS4'" />
        <span class="font-size-14 line-height-22">{{ $t(projectMap[cardInfo.project].name) }}</span>
      </div>
    </div>

    <div class="flex-box padding_t-12 card-item-box">
      <div class="with-right-border card-item">
        <span class="key">70/75kWh电池数量</span>
        <span class="value">{{ getValueFromKey(cardInfo.battery_count, '70/75kWh') }}</span>
      </div>
      <div class="with-right-border card-item">
        <span class="key">100kWh电池数量</span>
        <span class="value">{{ getValueFromKey(cardInfo.battery_count, '100kWh') }}</span>
      </div>
      <div class="with-right-border card-item">
        <span class="key">充电桩数量</span>
        <span class="value">{{ cardInfo.charging_pile_count ?? '-' }}</span>
      </div>
      <div class="card-item">
        <span class="key">站桩共建数量</span>
        <span class="value">{{ cardInfo.add_pile_count ?? '-' }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { projectMap, getValueFromKey } from './constant'
import Pss3Icon from './icons/pss3-icon.vue'
import Pss4Icon from './icons/pss4-icon.vue'

const props = defineProps({
  cardInfo: {
    type: Object,
    default: {}
  },
  isOneDevice: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
.device-card {
  width: calc((100% - 16px) / 2);
  padding: 16px 24px;
  border-radius: 4px;
  border: 1px solid #dcf2f3;
  background-color: #fff;
  .with-bottom-border {
    border-bottom: 1px solid #e6e7ec;
    padding-bottom: 12px;
  }
  .card-item-box {
    display: grid;
    grid-template-columns: 3fr 3fr 3fr 2fr;
    .with-right-border {
      border-right: 1px solid #e6e7ec;
      margin-right: 16px;
    }
    .card-item {
      display: flex;
      flex-direction: column;
      gap: 2px;
      .key {
        font-size: 12px;
        line-height: 18px;
        color: #262626;
      }
      .value {
        font-size: 16px;
        line-height: 24px;
        color: #01a0ac;
        font-weight: bold;
      }
    }
  }
  .highway-tag {
    height: 22px;
    border-radius: 2px;
    padding: 2px 6px;
    border: 1px solid #e83030;
    color: #e83030;
    font-size: 12px;
  }
  .project-tag {
    height: 26px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    border-radius: 2px;
    padding: 2px 8px;
    white-space: nowrap;
  }
}
.one-device-card {
  width: 100%;
}
</style>
