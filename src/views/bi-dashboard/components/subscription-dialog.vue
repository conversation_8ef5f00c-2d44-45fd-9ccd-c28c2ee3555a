<template>
  <div class="subscription-dialog">
    <el-dialog title="订阅看版" v-model="subscriptionVisible" width="836px" align-center @open="handleOpenDialog" @close="handleCloseDialog" :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="flex-box gap_16">
        <ReliabilityChosen class="cursor-pointer" v-if="checkReliability" @click="checkReliability = !checkReliability" />
        <ReliabilityUnchosen class="cursor-pointer" v-else @click="checkReliability = !checkReliability" />

        <PowerChosen class="cursor-pointer" v-if="checkPowerDistribution" @click="checkPowerDistribution = !checkPowerDistribution" />
        <PowerUnchosen class="cursor-pointer" v-else @click="checkPowerDistribution = !checkPowerDistribution" />

        <EnergyChosen class="cursor-pointer" v-if="checkEnergyManagementRevenue" @click="checkEnergyManagementRevenue = !checkEnergyManagementRevenue" />
        <EnergyUnchosen class="cursor-pointer" v-else @click="checkEnergyManagementRevenue = !checkEnergyManagementRevenue" />
      </div>
      <div class="flex-box flex_j_c-flex-end margin_t-16">
        <el-button class="welkin-text-button" @click="handleCloseDialog">{{ $t('common.pp_cancel') }}</el-button>
        <el-button class="welkin-primary-button" @click="handleConfirm" style="margin-left: 4px" :loading="loading">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { apiPostSubscription } from '~/apis/bi-dashboard'
import { ElMessage } from 'element-plus'
import ReliabilityChosen from './icons/reliability-chosen.vue'
import ReliabilityUnchosen from './icons/reliability-unchosen.vue'
import PowerChosen from './icons/power-chosen.vue'
import PowerUnchosen from './icons/power-unchosen.vue'
import EnergyChosen from './icons/energy-chosen.vue'
import EnergyUnchosen from './icons/energy-unchosen.vue'

interface Props {
  subscriptionVisible: boolean
  subscriptionList: any[]
}

const props = defineProps<Props>()
const emits = defineEmits(['update:subscriptionVisible', 'updateSubscriptionList'])

const loading = ref(false)
const checkReliability = ref(false)
const checkPowerDistribution = ref(false)
const checkEnergyManagementRevenue = ref(false)

const handleConfirm = async () => {
  const selectedSubscriptions = []
  if (checkReliability.value) selectedSubscriptions.push('reliability')
  if (checkPowerDistribution.value) selectedSubscriptions.push('power_distribution')
  if (checkEnergyManagementRevenue.value) selectedSubscriptions.push('energy_management_revenue')
  if (selectedSubscriptions.length === 0) {
    ElMessage.warning('请至少选择一个订阅选项')
    return
  }
  loading.value = true
  try {
    const res = await apiPostSubscription({ subscribe_module: selectedSubscriptions })
    loading.value = false
    if (res.err_code === 0) {
      ElMessage.success('订阅成功')
      emits('updateSubscriptionList', selectedSubscriptions)
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    loading.value = false
  }
}

const handleOpenDialog = () => {
  checkReliability.value = props.subscriptionList.includes('reliability')
  checkPowerDistribution.value = props.subscriptionList.includes('power_distribution')
  checkEnergyManagementRevenue.value = props.subscriptionList.includes('energy_management_revenue')
}

const handleCloseDialog = () => {
  emits('update:subscriptionVisible', false)
}
</script>

<style lang="scss" scoped>
.subscription-dialog {
  :deep(.el-dialog__header) {
    padding: 24px 24px 16px;
    .el-dialog__title {
      color: #1f1f1f;
      font-size: 18px;
    }
  }
  :deep(.el-dialog__body) {
    padding: 0 24px 24px;
  }
}
</style>
