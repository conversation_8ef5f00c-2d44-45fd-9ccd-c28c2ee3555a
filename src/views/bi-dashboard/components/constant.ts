export const projectMap = {
  PUS4: {
    name: 'common.pp_pss4',
    color: '#21819D',
    background: '#E6F8FF'
  },
  PUS3: {
    name: 'common.pp_pss3',
    color: '#01A0AC',
    background: '#E5F9F9'
  },
  PowerSwap2: {
    name: 'common.pp_pss2',
    color: '#2F9C74',
    background: '#E8FCEA'
  }
} as any

export const getValueFromKey = (arr: any, key: string) => {
  if(!arr) return '-'
  let obj = arr.find((item: any) => item.battery_type === key)
  return obj ? obj.count : '-'
}

export const pieOptionMock = {
  color: ['#01A0AC', '#39BEAE', '#72D896', '#55D7DB', '#8DE0F8', '#B5EEFF', '#C6EFEF', '#DCF2F3', '#00BEBE', '#68C789'],
  animation: false,
  tooltip: {
    trigger: 'item',
    formatter: function (params: any) {
      return params.marker + '  ' + params.name + '<br />' + params.value + '<br />' + '(' + params.percent + '%)'
    },
    textStyle: {
      color: '#262626',
      align: 'center'
    }
  },
  series: [
    {
      type: 'pie',
      center: ['50%', '50%'],
      radius: ['40%', '70%'],
      label: {
        show: true,
        color: '#8C8C8C',
        formatter: (params: any) => `{a|${params.name}:} {b|${params.percent}%}`,
        rich: {
          a: {
            color: '#8c8c8c',
            fontSize: 12
          },
          b: {
            color: '#262626',
            fontSize: 12
          }
        }
      },
      labelLine: {
        length2: 0
      },
      itemStyle: {
        borderRadius: 2,
        borderColor: '#fff',
        borderWidth: 2
      },
      cursor: 'default',
      data: [] as any
    }
  ]
}

export const barOptionMock1 = {
  animation: false,
  color: ['#00BEBE'],
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#8C8C8C',
      fontSize: 12,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    textStyle: {
      color: '#262626',
      fontWeight: 400
    }
  },
  grid: {
    top: '32',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    data: []
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: ['#D9D9D9']
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    }
  },
  series: [
    {
      type: 'bar',
      barMaxWidth: 24,
      data: []
    }
  ],
  dataZoom: [
    {
      type: 'inside',
      startValue: 0,
      endValue: 9
    }
  ]
} as any

export const barOptionMock2 = {
  animation: false,
  color: ['#00BEBE', '#FFC031'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    textStyle: {
      color: '#262626',
      fontWeight: 400
    },
    className: 'echarts-tooltip',
  },
  grid: {
    top: '32',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    data: [],
    axisPointer: {
      type: 'shadow'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    }
  },
  yAxis: [
    {
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    },
    {
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    }
  ],
  series: [
    {
      name: '充电电量',
      type: 'bar',
      tooltip: {
        valueFormatter: (value: any) => value + 'kWh'
      },
      barMaxWidth: 16,
      data: []
    },
    {
      name: '充电成本预估',
      type: 'line',
      tooltip: {
        valueFormatter: (value: any) => value + '元'
      },
      symbol: 'none',
      data: [],
      lineStyle: {
        width: 1.5
      },
      yAxisIndex: 1
    }
  ]
} as any

export const formatTimeNonYear = (timestamp: any) => {
  if (!timestamp) return '-'
  const timeDate = new Date(timestamp)
  const m = timeDate.getMonth() + 1
  const d = timeDate.getDate()
  const x = (m < 10 ? '0' + m : m) + '/' + (d < 10 ? '0' + d : d)
  return x
}
