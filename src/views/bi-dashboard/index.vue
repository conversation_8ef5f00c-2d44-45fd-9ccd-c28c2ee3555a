<template>
  <div class="bi-dashboard-container">
    <div class="title">{{ $t('menu.pp_bi_dashboard') }}</div>
    <el-form :model="form" class="common-form">
      <el-form-item :label="$t('common.pp_device_select')">
        <el-select v-model="form.device_ids" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
            <span style="float: left">{{ item.description }}</span>
            <span :style="{ float: 'right', color: projectMap[item.project].color, fontSize: '13px' }">{{ $t(projectMap[item.project].name) }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_time_frame')">
        <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" @calendar-change="handleCalendarChange" unlink-panels :shortcuts="getYesterdayShortcuts()" :clearable="false" :disabledDate="disabledDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <div class="width-full flex-box flex_j_c-space-between">
          <div>
            <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
          </div>
          <el-button class="welkin-secondary-button" @click="subscriptionVisible = true">
            <SubscribeIcon />
            <span class="margin_l-4">{{ $t('biDashboard.pp_subscribe') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <div class="loading-container" v-if="loading">
      <div class="loader-17"></div>
    </div>

    <div v-else-if="!list" class="empty-container">
      <el-empty :description="$t('common.pp_empty')">
        <template #image>
          <GrayEmptyDataIcon />
        </template>
      </el-empty>
    </div>

    <div v-else>
      <div class="margin_b-12">
        <span class="font-size-16 line-height-24 color-26 font-weight-500">{{ $t('biDashboard.pp_basic_info') }}</span>
        <span class="font-size-14 line-height-22 color-8c">{{ $t('biDashboard.pp_basic_info_tip') }}</span>
      </div>
      <div class="card-container" v-if="list.basic_info && list.basic_info.length > 0">
        <DeviceCard v-for="item in list.basic_info.slice(0, 4)" :cardInfo="item" :isOneDevice="list.basic_info.length === 1" />
        <div class="view-more" @click="moreDeviceVisible = true" v-if="list.basic_info && list.basic_info.length > 4">查看剩余{{ list.basic_info.length - 4 }}个设备...</div>
      </div>
      <div v-else class="basic-info-empty">
        <el-empty :description="$t('common.pp_empty')">
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>
      <MoreDeviceDialog v-model:moreDeviceVisible="moreDeviceVisible" :basicInfo="list.basic_info || []" :deviceOptions="deviceOptions" />

      <!-- 用户订阅看板 -->
      <div v-if="subscriptionList && subscriptionList.length > 0" class="margin_t-20">
        <draggable v-model="subscriptionList" drag-class="drag-class" :item-key="(element: string) => element" handle=".mover" @end="handleDragEnd" class="flex-box flex_d-column gap_20">
          <template #item="{ element }">
            <div>
              <KeepAlive>
                <component :is="componentMap[element]" v-bind="componentParams[element]" :list="list" @handleDownload="handleDownload" />
              </KeepAlive>
            </div>
          </template>
        </draggable>
      </div>

      <!-- 用户没有订阅看板 -->
      <div v-else class="no-subscriptions-box">
        <NoSubscriptionsIcon class="margin_b-24" />
        <div class="margin_b-2">当前暂无看板</div>
        <div>如需订阅请点击右上角‘订阅看板’以进行配置</div>
      </div>
    </div>

    <SubscriptionDialog v-model:subscriptionVisible="subscriptionVisible" :subscriptionList="subscriptionList" @updateSubscriptionList="updateSubscriptionList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, shallowRef, h, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { cloneDeep } from 'lodash-es'
import { ElMessage } from 'element-plus'
import { projectMap, pieOptionMock, barOptionMock1, barOptionMock2, formatTimeNonYear } from './components/constant'
import { apiGetBiInfo, apiGetBiDeviceList, apiGetBiSubscription, apiDownload, apiPostSubscription } from '~/apis/bi-dashboard'
import { initYesterdayStartTime, initYesterdayEndTime, getStartTimeOfDay, getFinalEndTime, getYesterdayShortcuts, clearJson, isEmptyData } from '~/utils'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import GrayEmptyDataIcon from '~/assets/svg/gray-empty-data.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'
import SubscribeIcon from './components/icons/subscribe-icon.vue'
import NoSubscriptionsIcon from './components/icons/no-subscriptions.vue'
import DeviceCard from './components/device-card.vue'
import MoreDeviceDialog from './components/more-device-dialog.vue'
import SubscriptionDialog from './components/subscription-dialog.vue'
import ReliabilityComponent from './components/drag-card/reliability-component.vue'
import PowerDistributionComponent from './components/drag-card/power-distribution-component.vue'
import EnergyRevenueComponent from './components/drag-card/energy-revenue-component.vue'
import draggable from 'vuedraggable'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const minDate = ref()
const loading = ref(false)
const moreDeviceVisible = ref(false)
const subscriptionVisible = ref(false)
const hasChart1 = ref(false)
const hasChart2 = ref(false)
const hasChart3 = ref(false)
const hasChart4 = ref(false)
const hasChart5 = ref(false)
const hasChart6 = ref(false)
const hasChart7 = ref(false)
const list = ref(null as any)
const form = ref({
  device_ids: [],
  start_time: initYesterdayStartTime,
  end_time: initYesterdayEndTime
})
const searchForm = ref({} as any)
const deviceOptions = ref([] as any)
const subscriptionList = ref([] as any)
const datePicker = ref([initYesterdayStartTime, initYesterdayEndTime] as any)
const disabledDate = (time: any) => {
  return new Date(time).getTime() > initYesterdayEndTime || new Date(time).getTime() <= minDate.value - 3600 * 1000 * 24 * 31 || new Date(time).getTime() >= minDate.value + 3600 * 1000 * 24 * 31
}
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const chartOption1 = cloneDeep(pieOptionMock)
const chartOption2 = cloneDeep(barOptionMock1)
const chartOption3 = cloneDeep(barOptionMock1)
const chartOption4 = cloneDeep(barOptionMock1)
const chartOption5 = cloneDeep(barOptionMock1)
const chartOption6 = cloneDeep(barOptionMock2)
const chartOption7 = cloneDeep(barOptionMock2)

const componentMap = {
  reliability: ReliabilityComponent,
  power_distribution: PowerDistributionComponent,
  energy_management_revenue: EnergyRevenueComponent
} as any

const componentParams = computed(() => {
  return {
    reliability: {
      list: list.value,
      hasChart1: hasChart1.value,
      hasChart2: hasChart2.value,
      chartOption1,
      chartOption2
    },
    power_distribution: {
      list: list.value,
      hasChart3: hasChart3.value,
      hasChart4: hasChart4.value,
      hasChart5: hasChart5.value,
      chartOption3,
      chartOption4,
      chartOption5
    },
    energy_management_revenue: {
      list: list.value,
      hasChart6: hasChart6.value,
      hasChart7: hasChart7.value,
      chartOption6,
      chartOption7
    }
  }
}) as any

const updateSubscriptionList = (selectedSubscriptions: string[]) => {
  subscriptionList.value = selectedSubscriptions
  subscriptionVisible.value = false
  if (form.value.device_ids.length > 0) handleSearch(false)
}

/**
 * @description: 下载数据
 * @param {*} module
 * @return {*}
 */
const handleDownload = (module: string) => {
  const params = {
    device_ids: searchForm.value.device_ids.join(','),
    start_time: getStartTimeOfDay(searchForm.value.start_time),
    end_time: getFinalEndTime(searchForm.value.end_time),
    module: module
  }
  try {
    apiDownload(params)
  } catch (error: any) {
    ElMessage.error(error.message)
  }
}

/**
 * @description: 拖拽改变看板顺序
 * @return {*}
 */
const handleDragEnd = async () => {
  try {
    const res = await apiPostSubscription({ subscribe_module: subscriptionList.value })
    if (res.err_code) {
      ElMessage.error(res.message)
    }
  } catch (error) {}
}

const handleCalendarChange = (val: any) => {
  minDate.value = new Date(val[0]).getTime()
  if (val[1]) minDate.value = undefined
}

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  datePicker.value = [initYesterdayStartTime, initYesterdayEndTime]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  list.value = null
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = (flag = true) => {
  if (form.value.device_ids.length === 0) {
    ElMessage.warning(t('deviceVersion.pp_please_select_device'))
    return
  } else if (form.value.end_time - form.value.start_time > 31 * 24 * 60 * 60 * 1000) {
    ElMessage.warning(t('biDashboard.pp_time_error'))
    return
  }
  searchForm.value = cloneDeep(form.value)
  getList(flag)
}

/**
 * @description: 处理数据
 * @return {*}
 */
const formatData = () => {
  // 可靠性饼图
  if (list.value.reliability && list.value.reliability.fault_detail && list.value.reliability.fault_detail.length > 0) {
    hasChart1.value = true
    chartOption1.series[0].data = list.value.reliability.fault_detail.map((item: any) => {
      if (item.fault_count > 0) {
        return {
          name: item.description,
          value: item.fault_count
        }
      }
    })
  } else {
    hasChart1.value = false
  }

  // 可靠性柱状图
  if (list.value.reliability && list.value.reliability.fault_trend && list.value.reliability.fault_trend.length > 0) {
    hasChart2.value = true
    chartOption2.title.text = '重点故障数量（个）'
    chartOption2.xAxis.data = list.value.reliability.fault_trend.map((item: any) => formatTimeNonYear(item.day))
    chartOption2.series[0].data = list.value.reliability.fault_trend.map((item: any) => item.count)
  } else {
    hasChart2.value = false
  }

  // 功率分配 不同时间整站实际输出功率
  if (list.value.power_distribution && list.value.power_distribution.real_output_power && list.value.power_distribution.real_output_power.length > 0) {
    hasChart3.value = true
    chartOption3.grid.top = '16'
    chartOption3.dataZoom = []
    chartOption3.series[0].barMaxWidth = 16
    chartOption3.series[0].tooltip = {
      valueFormatter: (value: any) => value + 'kW'
    }
    chartOption3.xAxis.data = list.value.power_distribution.real_output_power.map((item: any) => item.hour.toString().padStart(2, '0') + ':00')
    chartOption3.series[0].data = list.value.power_distribution.real_output_power.map((item: any) => item.value)
  } else {
    hasChart3.value = false
  }

  // 功率分配 不同时间模块使用个数
  if (list.value.power_distribution && list.value.power_distribution.module_use && list.value.power_distribution.module_use.length > 0) {
    hasChart4.value = true
    chartOption4.color = ['#78D9EF']
    chartOption4.grid.top = '16'
    chartOption4.dataZoom = []
    chartOption4.series[0].barMaxWidth = 16
    chartOption4.tooltip.formatter = (params: any) => {
      let result = `
        <div style="font-size: 14px; color: #262626">
          <span>${params[0].name} 模块使用个数</span>&nbsp;&nbsp;&nbsp;
          <span style="font-weight: bold">${isEmptyData(params[0].value) ? '-' : params[0].value + '个'}</span>
        </div>`
      let hour = parseInt(params[0].name.split(':')[0], 10)
      let detailInfo = list.value.power_distribution.module_use.find((item: any) => item.hour === hour)
      if (detailInfo && detailInfo.detail && detailInfo.detail.length > 0) {
        result += `
          <div style="font-size: 14px; color: #262626; width: 180px; white-space: wrap; margin-top: 8px">${detailInfo.detail.join('、')}</div>`
      }
      return result
    }
    chartOption4.xAxis.data = list.value.power_distribution.module_use.map((item: any) => item.hour.toString().padStart(2, '0') + ':00')
    chartOption4.series[0].data = list.value.power_distribution.module_use.map((item: any) => item.value)
  } else {
    hasChart4.value = false
  }

  // 功率分配 输出功率
  if (list.value.power_distribution && list.value.power_distribution.module_output_power && list.value.power_distribution.module_output_power.length > 0) {
    hasChart5.value = true
    chartOption5.color = ['#68C789']
    chartOption5.grid.top = '16'
    chartOption5.dataZoom = []
    chartOption5.series[0].tooltip = {
      valueFormatter: (value: any) => value + 'kW'
    }
    chartOption5.xAxis.data = list.value.power_distribution.module_output_power.map((item: any) => item.module)
    chartOption5.series[0].data = list.value.power_distribution.module_output_power.map((item: any) => item.value)
  } else {
    hasChart5.value = false
  }

  // 能源管理收益 充电电量&充电成本预估
  if (list.value.energy_management_revenue && list.value.energy_management_revenue.charge_info && list.value.energy_management_revenue.charge_info.length > 0) {
    hasChart6.value = true
    chartOption6.xAxis.data = list.value.energy_management_revenue.charge_info.map((item: any) => item.hour.toString().padStart(2, '0') + ':00')
    chartOption6.series[0].data = list.value.energy_management_revenue.charge_info.map((item: any) => item.value)
    chartOption6.series[1].data = list.value.energy_management_revenue.charge_info.map((item: any) => item.cost_estimate)
  } else {
    hasChart6.value = false
  }

  // 能源管理收益 放电电量&放电成本预估
  if (list.value.energy_management_revenue && list.value.energy_management_revenue.discharge_info && list.value.energy_management_revenue.discharge_info.length > 0) {
    hasChart7.value = true
    chartOption7.color = ['#78D9EF', '#68C789']
    chartOption7.series[0].name = '放电电量'
    chartOption7.series[1].name = '放电成本预估'
    chartOption7.xAxis.data = list.value.energy_management_revenue.discharge_info.map((item: any) => item.hour.toString().padStart(2, '0') + ':00')
    chartOption7.series[0].data = list.value.energy_management_revenue.discharge_info.map((item: any) => item.value)
    chartOption7.series[1].data = list.value.energy_management_revenue.discharge_info.map((item: any) => item.cost_estimate)
  } else {
    hasChart7.value = false
  }
}

/**
 * @description: 获取数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.start_time = getStartTimeOfDay(searchForm.value.start_time)
  formData.end_time = getFinalEndTime(searchForm.value.end_time)
  formData.device_ids = searchForm.value.device_ids.join(',')
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: formData
    })
  }
  loading.value = true
  try {
    const res = await apiGetBiInfo(formData)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      formatData()
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 获取设备列表
 * @return {*}
 */
const getDeviceOptions = async () => {
  try {
    const res = await apiGetBiDeviceList()
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      deviceOptions.value = res.data
    }
  } catch (error: any) {}
}

/**
 * @description: 获取用户订阅信息
 * @return {*}
 */
const getSubscription = async () => {
  try {
    const res = await apiGetBiSubscription()
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      subscriptionList.value = res.data || []
    }
  } catch (error: any) {}
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (initParams.start_time) {
    datePicker.value = [Number(initParams.start_time), Number(initParams.end_time)]
    form.value.start_time = datePicker.value[0]
    form.value.end_time = datePicker.value[1]
  }
  form.value.device_ids = initParams.device_ids ? initParams.device_ids.split(',') : []
  getDeviceOptions()
  getSubscription()
  if (form.value.device_ids.length > 0) handleSearch(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.bi-dashboard-container {
  font-family: 'Blue Sky Standard';
  padding: 24px;
  background: linear-gradient(180deg, #e2f9f9 -6.51%, #f8f8f8 12.89%);
  .title {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    color: #1f1f1f;
    margin-bottom: 20px;
  }
  .loading-container,
  .empty-container {
    height: calc(100vh - 200px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px 16px;
    .view-more {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #dcf2f3;
      color: #01a0ac;
      font-size: 14px;
      font-weight: 450;
      cursor: pointer;
    }
  }
  %empty {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
  }
  .basic-info-empty {
    height: 200px;
    @extend %empty;
  }
  .no-subscriptions-box {
    @extend %empty;
    height: 320px;
    font-size: 14px;
    color: #8c8c8c;
    margin-top: 20px;
    flex-direction: column;
  }
  .drag-class {
    border: 1px solid #00bebe;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important;
    .drag-card {
      background-color: #f7fdfd !important;
    }
  }
  :deep(.drag-card) {
    width: 100%;
    padding: 24px;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .bottom-line {
      padding-bottom: 8px;
      border-bottom: 1px solid #e6e7ec;
    }
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  :deep(.el-form) {
    grid-template-columns: 1fr 1fr 260px;
  }
  :deep(.el-empty) {
    padding: 0;
    .el-empty__image {
      width: 100px;
    }
    .el-empty__description {
      margin-left: 20px;
    }
  }
}
</style>
