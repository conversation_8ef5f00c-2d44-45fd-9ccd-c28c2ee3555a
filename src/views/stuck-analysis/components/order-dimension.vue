<template>
  <div class="order-dimension-container">
    <el-form :model="form" ref="ruleFormRef" inline class="margin_b-2">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="$t('stuckAnalysis.pp_time')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-date-picker v-model="datePicker" type="daterange" class="width-full" @change="handleDateChange" unlink-panels :shortcuts="getTodayShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledTodayDate" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('stuckAnalysis.pp_device_name')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-select v-model="form.device_id" filterable remote clearable :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('stuckAnalysis.pp_car_id')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-input v-model="form.vehicle_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('stuckAnalysis.pp_analyst')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-select v-model="form.analysis_user" clearable filterable remote class="width-full" :placeholder="$t('stuckAnalysis.pp_user_placeholder')" :remote-method="remoteMethod" :loading="remoteLoading">
              <el-option v-for="item in userOptions" :key="item.employee_id" :value="item.worker_user_id" :label="item.name">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.worker_user_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('common.pp_vehicle_brand')" class="width-full normal-form">
            <el-cascader v-model="brand" :options="brandOptions" :props="cascaderProps" separator="-" collapse-tags clearable @change="handleBrandChange" class="brand-cascader" :placeholder="t('common.pp_please_select')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('stuckAnalysis.pp_device_type')">
            <el-select v-model="form.project" clearable filterable @visible-change="handleDeviceVisibleChange" :class="[form.project !== '' || deviceShow ? 'long-select' : 'short-select', 'remove-border']" :placeholder="$t('common.pp_please_select')">
              <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('stuckAnalysis.pp_analysis_status')">
            <el-select v-model="form.analysis_status" clearable filterable @visible-change="handleStatusVisibleChange" :class="[form.analysis_status !== '' || statusShow ? 'long-select' : 'short-select', 'remove-border']" :placeholder="$t('common.pp_please_select')">
              <el-option v-for="item in analysisStatusOptions" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>
          <el-form-item class="normal-form">
            <el-button type="primary" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
            <el-button class="reset-button" style="margin-left: 8px" @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="float: right" class="normal-form">
            <el-button @click="batchImportVisible = true" class="reset-button width-108 height-32" style="padding: 5px 16px">
              <UploadIcon />
              <span class="margin_l-4">{{ $t('common.pp_import') }}</span>
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="list" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="loading">
      <el-table-column prop="service_start_time" :label="$t('common.pp_start_time')" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ formatTime(row.service_start_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="service_end_time" :label="$t('common.pp_end_time')" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ formatTime(row.service_end_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="service_duration" :label="$t('station.pp_service_duration')" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.service_duration }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" :label="$t('parkingOccupancy.pp_device_name')" min-width="250" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="cursor-pointer" style="color: #01a0ac" @click="handleJumpServiceDetail(row)">{{ row.description || $t('common.pp_unnamed_device') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_id" :label="$t('parkingOccupancy.pp_device_id')" min-width="250">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.device_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="project" :label="$t('stuckAnalysis.pp_device_type')" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span :style="{ color: projectMap[row.project].color }">{{ $t(projectMap[row.project].name) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="ev_brand" :label="$t('common.pp_vehicle_brand')" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.ev_brand ? row.ev_brand : '' }}-{{ row.ev_type ? row.ev_type : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="service_id" :label="$t('station.pp_service_id')" min-width="250">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.service_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="vehicle_id" :label="$t('station.pp_car_id')" min-width="250">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.vehicle_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="analysis_status" :label="$t('stuckAnalysis.pp_analysis_status')" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="duty-status width-74" :style="{ background: analysisStatusMap[row.analysis_status].background, color: analysisStatusMap[row.analysis_status].color }">
            <Analyzed v-if="row.analysis_status === 1" />
            <Unanalyzed v-else />
            <span class="margin_l-4">{{ $t(analysisStatusMap[row.analysis_status].name) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="analysis_user" :label="$t('stuckAnalysis.pp_analyst')" :width="flexColumnWidth(userList, 110, 7)">
        <template #default="{ row }">
          <div class="flex-box flex_a_i-center">
            <div class="flex-box flex_a_i-center avatar-container" v-if="row.analysis_user">
              <el-avatar size="small" :src="row.analysis_user_avatar_url" />
              <span class="margin_l-8">{{ row.analysis_user }}</span>
            </div>
            <Edit class="margin_l-8 cursor-pointer" @click="handleClickUser(row)" v-if="hasPermission('function:dashboard-overview:change-analyst')" />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.pp_operation')" width="100" fixed="right">
        <template #default="{ row }">
          <span class="operation-button" @click="handleClickDetail(row)">{{ $t('stuckAnalysis.pp_detail') }}</span>
          <span class="operation-button margin_l-10" @click="handleClickAnalysis(row)" v-if="user == row.analysis_user || hasPermission('function:dashboard-overview:analysis-order')">{{ $t('stuckAnalysis.pp_analysis') }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="margin_t-20 flex-box flex_j_c-flex-end">
      <Page :page="pages" @change="handlePageChange" />
    </div>

    <!-- 编辑分析人 -->
    <el-dialog :title="$t('stuckAnalysis.pp_edit_analyst')" v-model="editAnalystVisible" :width="'400px'" align-center @close="editAnalystVisible = false" :close-on-click-modal="false" :close-on-press-escape="false" class="edit-dialog">
      <div style="color: #595959" class="margin_b-8">{{ $t('stuckAnalysis.pp_change_analyst') }}</div>
      <el-select v-model="goalAnalyst" filterable remote class="width-full" :placeholder="$t('stuckAnalysis.pp_user_placeholder')" :remote-method="remoteMethod" :loading="remoteLoading">
        <el-option v-for="item in userOptions" :key="item.employee_id" :value="item.worker_user_id" :label="item.name">
          <span style="float: left">{{ item.name }}</span>
          <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.worker_user_id }}</span>
        </el-option>
      </el-select>
      <div class="flex-box flex_a_i-center flex_j_c-flex-end margin_t-16">
        <el-button class="text-button" @click="editAnalystVisible = false">{{ $t('common.pp_cancel') }}</el-button>
        <el-button type="primary" :loading="editAnalystLoading" @click="handleEditAnalyst" style="margin-left: 4px">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>

    <!-- 详情 / 分析 -->
    <el-dialog :title="dialogType == 'detail' ? $t('stuckAnalysis.pp_order_detail') : $t('stuckAnalysis.pp_order_analysis')" v-model="operationVisible" class="analysis-dialog" :width="'1120px'" align-center :before-close="handleBeforeClose" @close="handleCloseOperation" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-row :gutter="20" class="margin_b-14">
        <el-col :span="2" class="dialog-title">{{ $t('stuckAnalysis.pp_station_name') }}</el-col>
        <el-col :span="6" class="dialog-content">
          <WelkinCopyBoard :text="rowInfo.description || $t('common.pp_unnamed_device')" class="dialog-content height-full" />
        </el-col>
        <el-col :span="2" :offset="1" class="dialog-title">{{ $t('stuckAnalysis.pp_station_id') }}</el-col>
        <el-col :span="6">
          <WelkinCopyBoard :text="rowInfo.device_id" direction="rtl" class="dialog-content height-full" />
        </el-col>
        <el-col :span="2" :offset="1" class="dialog-title">{{ $t('stuckAnalysis.pp_device_manager') }}</el-col>
        <el-col :span="4">
          <div class="flex-box flex_a_i-center absolute" v-if="rowInfo.device_manager">
            <el-avatar size="small" class="width-20 height-20" :src="rowInfo.device_manager_avatar_url" />
            <span class="margin_l-8 dialog-content">{{ rowInfo.device_manager }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="margin_b-14">
        <el-col :span="2" class="dialog-title">{{ $t('common.pp_start_time') }}</el-col>
        <el-col :span="6" class="dialog-content center-col">{{ formatTime(rowInfo.service_start_time) }}</el-col>
        <el-col :span="2" :offset="1" class="dialog-title">{{ $t('common.pp_end_time') }}</el-col>
        <el-col :span="6" class="dialog-content center-col">{{ formatTime(rowInfo.service_end_time) }}</el-col>
        <el-col :span="2" :offset="1" class="dialog-title">{{ $t('stuckAnalysis.pp_alarm_step') }}</el-col>
        <el-col :span="4" class="dialog-content center-col">
          <WelkinCopyBoard :text="rowInfo.alarm_step_name" :showIcon="false" class="dialog-content height-full width-170" />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="margin_b-14">
        <el-col :span="2" class="dialog-title">{{ $t('station.pp_car_id') }}</el-col>
        <el-col :span="6">
          <WelkinCopyBoard :text="rowInfo.vehicle_id" direction="rtl" class="dialog-content height-full" />
        </el-col>
        <el-col :span="2" :offset="1" class="dialog-title">{{ $t('station.pp_service_id') }}</el-col>
        <el-col :span="6">
          <WelkinCopyBoard :text="rowInfo.service_id" direction="rtl" class="dialog-content height-full" />
        </el-col>
      </el-row>
      <el-divider />
      <el-form :model="detailForm" inline class="margin_b-6">
        <el-form-item :label="$t('stuckAnalysis.pp_time')" style="margin-right: 24px">
          <el-date-picker v-model="dialogDatePicker" type="datetimerange" :disabledDate="disabledDate" @calendar-change="handleCalendarChange" @change="handleDialogDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleResetTime">{{ $t('stuckAnalysis.pp_current_alarm') }}</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="alarmList" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="dialogLoading">
        <el-table-column prop="data_id_description" :label="$t('stuckAnalysis.pp_alarm_name')" min-width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.data_id_description ? row.data_id_description : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="data_id" :label="$t('stuckAnalysis.pp_alarm_id')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="operation-button" @click="handleViewAlarm(row.data_id)">{{ row.data_id }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarm_type" :label="$t('stuckAnalysis.pp_alarm_type')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ $t(alarmTypeMap[row.alarm_type]) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarm_level" :label="$t('stuckAnalysis.pp_alarm_level')" min-width="110" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="alarm-level-container" v-if="alarmLevelMap[row.alarm_level]" :style="{ background: alarmLevelMap[row.alarm_level].background, color: alarmLevelMap[row.alarm_level].color }">
              <span>{{ $t(alarmLevelMap[row.alarm_level].name) }}</span>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('stuckAnalysis.pp_alarm_status')" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="duty-status width-94" :style="{ background: stateMap[row.state].background, color: '#fff' }">
              <Complete v-if="row.state == 1" />
              <Remind v-else-if="row.state == 2" />
              <Time v-else />
              <span class="margin_l-4">{{ $t(stateMap[row.state].name) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_ts" :label="$t('stuckAnalysis.pp_create_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.create_ts) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="clear_ts" :label="$t('stuckAnalysis.pp_clear_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span> {{ formatTime(row.clear_ts) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="is_stuck" :label="$t('stuckAnalysis.pp_is_stuck')" min-width="110" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center" style="color: #fd4348" v-if="row.is_stuck">
              <span class="margin_r-8">{{ $t('stuckAnalysis.pp_stuck_alarm') }}</span>
              <Alarm />
            </div>
            <span v-else> {{ $t('stuckAnalysis.pp_non_stuck_alarm') }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <Page :page="dialogPage" :layout="'prev, pager, next'" @change="handleDialogPageChange" class="mini-page" />
      </div>

      <div v-if="dialogType == 'detail'" class="analysis-container">
        <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-20">
          <div class="flex-box flex_a_i-center">
            <span style="color: #595959">{{ $t('stuckAnalysis.pp_analyst') }}</span>
            <div class="flex-box flex_a_i-center margin_l-40" v-if="rowInfo.analysis_user">
              <el-avatar size="small" class="width-20 height-20" :src="rowInfo.analysis_user_avatar_url" />
              <span class="margin_l-8 dialog-content">{{ rowInfo.analysis_user }}</span>
            </div>
            <span class="margin_l-40 dialog-content" v-else>{{ $t('common.pp_no_data') }}</span>
            <Edit class="margin_l-8 cursor-pointer" @click="handleClickUser(rowInfo)" v-if="hasPermission('function:dashboard-overview:change-analyst')" />
          </div>
          <div class="flex-box flex_a_i-center">
            <span style="color: #595959">{{ $t('stuckAnalysis.pp_is_aware') }}</span>
            <span class="margin_l-40 dialog-content">{{ rowInfo.aware ? $t('common.pp_yes') : $t('common.pp_no') }}</span>
          </div>
          <div class="flex-box flex_a_i-center">
            <span style="color: #595959">{{ $t('stuckAnalysis.pp_associated_bug') }}</span>
            <span class="margin_l-40 operation-button" @click="handleJump('https://jira.nioint.com/browse/' + rowInfo.bug_url)" v-if="rowInfo.bug_url">{{ $t('common.pp_jump') }}</span>
            <span class="margin_l-40 dialog-content" v-else>{{ $t('common.pp_no_data') }}</span>
          </div>
          <div class="flex-box flex_a_i-center">
            <span style="color: #595959">{{ $t('stuckAnalysis.pp_associated_story') }}</span>
            <span class="margin_l-40 operation-button" @click="handleJump('https://jira.nioint.com/browse/' + rowInfo.jira_url)" v-if="rowInfo.jira_url">{{ $t('common.pp_jump') }}</span>
            <span class="margin_l-40 dialog-content" v-else>{{ $t('common.pp_no_data') }}</span>
          </div>
        </div>
        <el-input v-model="rowInfo.result" disabled autosize type="textarea" class="width-full" :placeholder="$t('common.pp_no_data')" />
        <div class="flex-box flex_a_i-center flex_j_c-flex-end margin_t-16">
          <el-button class="close-button" @click="operationVisible = false">{{ $t('common.pp_close') }}</el-button>
        </div>
      </div>

      <div v-else class="analysis-container">
        <el-form ref="analysisFormRef" :rules="rules" inline :model="analysisForm" require-asterisk-position="right" class="flex-box flex_w-wrap">
          <div class="analysis-form-container">
            <el-form-item prop="bug_url" :label="$t('stuckAnalysis.pp_associated_bug')" class="normal-form">
              <el-select v-model="analysisForm.bug_url" class="width-200" clearable filterable remote :placeholder="$t('stuckAnalysis.pp_jira_placeholder')" :remote-method="remoteBugMethod" :loading="bugRemoteLoading">
                <el-option v-for="item in bugOptions" :key="item.jira_id" :value="item.jira_id" :label="item.jira_id">
                  <span style="float: left">{{ item.jira_id }}</span>
                  <span class="width-240 ellipse margin_l-100" style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.jira_name }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="jira_url" :label="$t('stuckAnalysis.pp_associated_story')" class="normal-form">
              <el-select v-model="analysisForm.jira_url" class="width-200" clearable filterable remote :placeholder="$t('stuckAnalysis.pp_jira_placeholder')" :remote-method="remoteStoryMethod" :loading="storyRemoteLoading">
                <el-option v-for="item in storyOptions" :key="item.jira_id" :value="item.jira_id" :label="item.jira_id">
                  <span style="float: left">{{ item.jira_id }}</span>
                  <span class="width-240 ellipse margin_l-100" style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.jira_name }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="aware" :label="$t('stuckAnalysis.pp_is_aware')" class="normal-form">
              <el-radio-group v-model="analysisForm.aware">
                <el-radio :label="true">{{ $t('common.pp_yes') }}</el-radio>
                <el-radio :label="false">{{ $t('common.pp_no') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="result" class="width-full normal-form" style="grid-column: 1 / -1">
              <el-input v-model="analysisForm.result" :autosize="{ minRows: 4 }" type="textarea" class="width-full" :placeholder="$t('stuckAnalysis.pp_input_result')" />
            </el-form-item>
          </div>
        </el-form>
        <div class="flex-box flex_a_i-center flex_j_c-flex-end">
          <el-button class="text-button" @click="handleBeforeClose">{{ $t('common.pp_cancel') }}</el-button>
          <el-button type="primary" style="margin-left: 4px" :loading="analysisLoading" @click="handleConfirmAnalysis(analysisFormRef)">{{ $t('common.pp_confirm') }}</el-button>
        </div>
      </div>
    </el-dialog>

    <AlarmDialog v-model:alarmVisible="alarmVisible" :alarmId="alarmId" :project="rowInfo.project" v-if="alarmVisible" />

    <!-- 批量导入设备 -->
    <UploadDialog v-model:batchImportVisible="batchImportVisible" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, PropType, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { page } from '~/constvars/page';
import { hasPermission } from '~/auth';
import { useStore } from 'vuex';
import { formatTime, removeNullKeys, getDuration, clearJson, flexColumnWidth, getUserId } from '~/utils';
import { apiGetJiraList, apiGetAllAlarmList, apiGetOrderList, apiPostAnalysis, apiGetUserList } from '~/apis/stuck-analysis';
import type { FormInstance } from 'element-plus';
import { debounce } from 'lodash-es';
import { ElMessage, ElMessageBox } from 'element-plus';
import { apiGetDevices } from '~/apis/home';
import { initTodayStartTime, initTodayEndTime, getTodayShortcuts, getDisabledTodayDate, getFinalEndTime, projectOptions, dutyStatusOptions, analysisStatusOptions, projectMap, dutyStatusMap, analysisStatusMap, alarmTypeMap, alarmLevelMap, stateMap } from './constant';
import AlarmDialog from './alarm-dialog.vue';
import Unknown from '~/assets/svg/unknown.vue';
import Somebody from '~/assets/svg/somebody.vue';
import Nobody from '~/assets/svg/nobody.vue';
import Unanalyzed from '~/assets/svg/unanalyzed.vue';
import Analyzed from '~/assets/svg/analyzed.vue';
import Edit from '~/assets/svg/edit.vue';
import Remind from '~/assets/svg/remind.vue';
import Complete from '~/assets/svg/complete.vue';
import Time from '~/assets/svg/time.vue';
import Alarm from '~/assets/svg/alarm.vue';
import UploadIcon from '~/assets/svg/upload.vue';
import UploadDialog from './upload-dialog.vue';
import _ from 'lodash';

interface DeviceObject {
  device_id: string;
  description: string;
}

const props = defineProps({
  allDeviceList: {
    type: Array as PropType<DeviceObject[]>,
    required: true,
  },
});

const { t } = useI18n();
const store = useStore();
const ruleFormRef = ref();
const analysisFormRef = ref();
const route = useRoute();
const router = useRouter();
const loading = ref(false);
const dialogLoading = ref(false);
const remoteLoading = ref(false);
const bugRemoteLoading = ref(false);
const storyRemoteLoading = ref(false);
const analysisLoading = ref(false);
const editAnalystLoading = ref(false);
const operationVisible = ref(false);
const alarmVisible = ref(false);
const editAnalystVisible = ref(false);
const batchImportVisible = ref(false);
const dutyShow = ref(false);
const statusShow = ref(false);
const deviceShow = ref(false);
const remoteDeviceLoading = ref(false);
const goalAnalyst = ref('');
const dialogType = ref('');
const alarmId = ref('' as any);
const list = ref([] as any);
const brand = ref([] as any);
const alarmList = ref([] as any);
const userList = ref([] as any);
const userOptions = ref([] as any);
const dialogDatePicker = ref([] as any);
const bugOptions = ref([] as any);
const storyOptions = ref([] as any);
const deviceOptions = ref([] as any);
const datePicker = ref([initTodayStartTime, initTodayEndTime] as any);
const rowInfo = ref({} as any);
const cascaderProps = ref({ multiple: true });
const form = ref({
  start_time: initTodayStartTime,
  end_time: initTodayEndTime,
  device_id: [] as any,
  project: '',
  analysis_status: '' as string | number,
  analysis_user: '',
  vehicle_id: '',
  ev_brand: '',
  ev_type: '',
});
const detailForm = ref({
  start_time: initTodayStartTime,
  end_time: initTodayEndTime,
});
const analysisForm = ref({
  bug_url: '',
  jira_url: '',
  aware: '' as string | boolean,
  result: '',
});
const searchForm = ref({} as any);
const pages = ref(_.cloneDeep(page));
const dialogPage = ref(_.cloneDeep(page));
const initAnalysisForm = _.cloneDeep(analysisForm.value);
const user = getUserId();
const minDate = ref();
const rules = ref({
  aware: [{ required: true, message: t('stuckAnalysis.pp_select_aware'), trigger: 'change' }],
  result: [{ required: true, message: t('stuckAnalysis.pp_input_result'), trigger: 'blur' }],
});
const pathMap = ref(computed(() => store.state.vehicle.pathMap));
const brandOptions = ref(computed(() => store.state.vehicle.brandList));
const getShortcuts = () => {
  return [
    {
      text: t('common.pp_today'),
      value: [new Date(new Date().toLocaleDateString()).getTime(), new Date().getTime()],
    },
    {
      text: t('common.pp_last_week'),
      value: [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 6, new Date().getTime()],
    },
  ];
};

const disabledDate = (time: any) => {
  return new Date(time).getTime() > Date.now() || new Date(time).getTime() < minDate.value - 3600 * 1000 * 24 * 6 || new Date(time).getTime() > minDate.value + 3600 * 1000 * 24 * 6;
};

/**
 * @description: 选择车辆品牌
 * @return {*}
 */
const handleBrandChange = () => {
  const result = brand.value.reduce(
    (acc: any, curr: any) => {
      const [brand, type] = curr;
      if (!acc.ev_brand.includes(brand)) acc.ev_brand.push(brand);
      if (!acc.ev_type.includes(type)) acc.ev_type.push(type);
      return acc;
    },
    { ev_brand: [], ev_type: [] }
  );
  form.value.ev_brand = result.ev_brand.join(',');
  form.value.ev_type = result.ev_type.join(',');
};

const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return;
  searchDeviceList(query);
};
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true;
    const params = { project: 'PowerSwap2,PUS3,PUS4', name: val, device_ids: deviceIds, limit: 30 };
    const res = await apiGetDevices(params);
    remoteDeviceLoading.value = false;
    deviceOptions.value = res.data;
  }
}, 500);

/**
 * @description: 确认批量导入设备
 * @param {*} list
 * @return {*}
 */
const handleConfirmBatchImportDevice = (list: any) => {
  const deviceIds = props.allDeviceList.map((item: any) => item.device_id);
  const importDevice = list.filter((item: any) => deviceIds.includes(item));
  if (importDevice.length > 100) {
    ElMessage.error(t('stuckAnalysis.pp_exceed_100'));
  } else if (importDevice.length == 0) {
    ElMessage.error(t('stuckAnalysis.pp_number_0'));
  } else {
    form.value.device_id = [...new Set([...form.value.device_id, ...importDevice])];
    ElMessage.success(`${t('stuckAnalysis.pp_import_success')} ${importDevice.length} ${t('stuckAnalysis.pp_device_number')}`);
    batchImportVisible.value = false;
    searchDeviceList('NIO', form.value.device_id.join(','));
  }
};

/**
 * @description: 远程搜索用户
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchUserList(query);
};
const searchUserList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true;
    const params = { fuzzy_name: val };
    const res = await apiGetUserList(params);
    userOptions.value = res.data.people_list;
    remoteLoading.value = false;
  }
}, 500);

/**
 * @description: 跳转到高速录播
 * @param {*} row
 * @return {*}
 */
const handleJumpServiceDetail = (row: any) => {
  window.open(`//${location.host}/${pathMap.value[row.project]}/service-list/service-detail/${row.device_id}/${row.service_id}?start_time=${row.service_start_time}&end_time=${row.service_end_time}`);
};

/**
 * @description: 提交编辑分析人
 * @return {*}
 */
const handleEditAnalyst = async () => {
  const params = { ...rowInfo.value, analysis_user: goalAnalyst.value };
  editAnalystLoading.value = true;
  try {
    const res = await apiPostAnalysis(params);
    editAnalystLoading.value = false;
    if (res.err_code) {
      ElMessage.error(res.message);
    } else {
      ElMessage.success(t('common.pp_edit_success'));
      editAnalystVisible.value = false;
      operationVisible.value = false;
      getList(false);
    }
  } catch (error) {
    editAnalystLoading.value = false;
  }
};

/**
 * @description: 远程搜索Bug
 * @param {*} query
 * @return {*}
 */
const remoteBugMethod = (query: any) => {
  if (bugRemoteLoading.value) return;
  searchBugList(query);
};
const searchBugList = debounce(async (val: any) => {
  if (val) {
    bugRemoteLoading.value = true;
    const params = { key: val, type: '故障' };
    const res = await apiGetJiraList(params);
    bugOptions.value = res.data || [];
    bugRemoteLoading.value = false;
  }
}, 500);

/**
 * @description: 远程搜索Story
 * @param {*} query
 * @return {*}
 */
const remoteStoryMethod = (query: any) => {
  if (storyRemoteLoading.value) return;
  searchStoryList(query);
};
const searchStoryList = debounce(async (val: any) => {
  if (val) {
    storyRemoteLoading.value = true;
    const params = { key: val, type: '故事' };
    const res = await apiGetJiraList(params);
    storyOptions.value = res.data || [];
    storyRemoteLoading.value = false;
  }
}, 500);

const handleDutyVisibleChange = (visible: boolean) => {
  dutyShow.value = visible;
};

const handleStatusVisibleChange = (visible: boolean) => {
  statusShow.value = visible;
};

const handleDeviceVisibleChange = (visible: boolean) => {
  deviceShow.value = visible;
};

/**
 * @description: 查看单一告警
 * @param {*} id
 * @return {*}
 */
const handleViewAlarm = (id: any) => {
  alarmId.value = _.cloneDeep(id);
  alarmVisible.value = true;
};

/**
 * @description: 分析弹窗关闭前二次确认
 * @return {*}
 */
const handleBeforeClose = () => {
  if (dialogType.value == 'analysis') {
    ElMessageBox.confirm(t('stuckAnalysis.pp_close_tip'), t('stuckAnalysis.pp_prompt'), {
      confirmButtonText: t('common.pp_confirm'),
      cancelButtonText: t('common.pp_cancel'),
      type: 'warning',
      customClass: 'stuck-analysis-messagebox',
    })
      .then(() => {
        operationVisible.value = false;
      })
      .catch(() => {});
  } else {
    operationVisible.value = false;
  }
};

/**
 * @description: 提交分析结果
 * @param {*} formEl
 * @return {*}
 */
const handleConfirmAnalysis = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async (valid, fields) => {
    if (valid) {
      analysisLoading.value = true;
      const params = {
        ...analysisForm.value,
        analysis_status: 1,
        analysis_user: user,
      };
      try {
        const res = await apiPostAnalysis(params);
        analysisLoading.value = false;
        if (res.err_code) {
          ElMessage.error(res.message);
        } else {
          ElMessage.success(t('common.pp_analysis_success'));
          operationVisible.value = false;
          getList(false);
        }
      } catch (error) {
        analysisLoading.value = false;
      }
    }
  });
};

/**
 * @description: 获取弹窗中表格数据
 * @return {*}
 */
const getDialogList = async () => {
  dialogLoading.value = true;
  const formData = _.cloneDeep(detailForm.value) as any;
  formData.page = dialogPage.value.current;
  formData.size = dialogPage.value.size;
  formData.descending = true;
  try {
    const res = await apiGetAllAlarmList(formData, rowInfo.value.project, rowInfo.value.device_id);
    dialogLoading.value = false;
    if (res.err_code) {
      ElMessage.error(res.message);
    } else {
      alarmList.value = res.data;
      dialogPage.value.total = res.total;
    }
  } catch (error) {
    dialogLoading.value = false;
  }
};

/**
 * @description: 点击分析
 * @param {*} row
 * @return {*}
 */
const handleClickAnalysis = (row: any) => {
  rowInfo.value = row;
  dialogType.value = 'analysis';
  detailForm.value.start_time = _.cloneDeep(row.service_start_time);
  detailForm.value.end_time = _.cloneDeep(row.service_end_time);
  detailForm.value.end_time = detailForm.value.end_time || detailForm.value.start_time + 1000 * 60 * 5;
  dialogDatePicker.value = [detailForm.value.start_time, detailForm.value.end_time];
  dialogPage.value.current = 1;
  analysisForm.value = _.cloneDeep(row);
  operationVisible.value = true;
  getDialogList();
};

/**
 * @description: 点击查看详情
 * @param {*} row
 * @return {*}
 */
const handleClickDetail = (row: any) => {
  rowInfo.value = row;
  dialogType.value = 'detail';
  detailForm.value.start_time = _.cloneDeep(row.service_start_time);
  detailForm.value.end_time = _.cloneDeep(row.service_end_time);
  detailForm.value.end_time = detailForm.value.end_time || detailForm.value.start_time + 1000 * 60 * 5;
  dialogDatePicker.value = [detailForm.value.start_time, detailForm.value.end_time];
  dialogPage.value.current = 1;
  operationVisible.value = true;
  getDialogList();
};

/**
 * @description: 弹窗中时间变更
 * @param {*} val
 * @return {*}
 */
const handleDialogDateChange = (val: any) => {
  detailForm.value.start_time = new Date(val[0]).getTime();
  detailForm.value.end_time = new Date(val[1]).getTime();
  dialogPage.value.current = 1;
  getDialogList();
};

/**
 * @description: 当前告警
 * @return {*}
 */
const handleResetTime = () => {
  detailForm.value.start_time = _.cloneDeep(rowInfo.value.service_start_time);
  detailForm.value.end_time = _.cloneDeep(rowInfo.value.service_end_time);
  detailForm.value.end_time = detailForm.value.end_time || detailForm.value.start_time + 1000 * 60 * 5;
  dialogDatePicker.value = [detailForm.value.start_time, detailForm.value.end_time];
  dialogPage.value.current = 1;
  getDialogList();
};

/**
 * @description: 弹窗中的分页
 * @param {*} argPage
 * @return {*}
 */
const handleDialogPageChange = (argPage: any) => {
  dialogPage.value.current = argPage.current;
  dialogPage.value.size = argPage.size;
  getDialogList();
};

/**
 * @description: 时间选择范围限制一周
 * @param {*} val
 * @return {*}
 */
const handleCalendarChange = (val: any) => {
  minDate.value = new Date(val[0]).getTime();
  if (val[1]) minDate.value = undefined;
};

/**
 * @description: 链接跳转
 * @param {*} url
 * @return {*}
 */
const handleJump = (url: string) => {
  window.open(url);
};

/**
 * @description: 关闭分析弹窗
 * @return {*}
 */
const handleCloseOperation = () => {
  if (dialogType.value == 'analysis') {
    analysisForm.value = _.cloneDeep(initAnalysisForm);
    if (analysisFormRef.value) analysisFormRef.value.resetFields();
  }
  operationVisible.value = false;
};

/**
 * @description: 点击编辑分析人
 * @param {*} row
 * @return {*}
 */
const handleClickUser = (row: any) => {
  rowInfo.value = row;
  goalAnalyst.value = _.cloneDeep(row.analysis_user);
  editAnalystVisible.value = true;
};

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList();
};

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime();
  form.value.end_time = new Date(val[1]).getTime();
};

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  brand.value = [];
  clearJson(form.value);
  datePicker.value = [initTodayStartTime, initTodayEndTime];
  form.value.start_time = datePicker.value[0];
  form.value.end_time = datePicker.value[1];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  searchForm.value = _.cloneDeep(form.value);
  getList();
};

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = _.cloneDeep(searchForm.value) as any;
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.end_time = getFinalEndTime(formData.end_time);
  formData.device_id = formData.device_id.join(',');
  removeNullKeys(formData);
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: route.query.tab, ...formData },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetOrderList(formData);
    loading.value = false;
    if (res.err_code) {
      ElMessage.error(res.message);
    } else {
      list.value = res.data;
      pages.value.total = res.total;
      list.value.map((item: any) => {
        item.service_duration = getDuration(item.service_start_time, item.service_end_time);
      });
      userList.value = [...new Set(list.value.map((item: any) => item.analysis_user))];
    }
  } catch (error) {
    loading.value = false;
  }
};

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query;
  if (initParams.tab == 'order-dimension') {
    if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
      form.value.start_time = Number(initParams.start_time);
      form.value.end_time = Number(initParams.end_time);
      datePicker.value = [form.value.start_time, form.value.end_time];
    }
    brand.value = !!initParams.ev_type ? initParams.ev_type.split(',') : [];
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
    form.value.device_id = !!initParams.device_id ? initParams.device_id.split(',') : [];
    form.value.project = !!initParams.project ? initParams.project : '';
    form.value.analysis_status = !!initParams.analysis_status ? initParams.analysis_status : '';
    form.value.analysis_user = !!initParams.analysis_user ? initParams.analysis_user : '';
    form.value.vehicle_id = !!initParams.vehicle_id ? initParams.vehicle_id : '';
    form.value.ev_brand = !!initParams.ev_brand ? initParams.ev_brand : '';
    form.value.ev_type = !!initParams.ev_type ? initParams.ev_type : '';
  }
  searchForm.value = _.cloneDeep(form.value);
  getList(false);
  searchDeviceList('NIO', form.value.device_id.join(','));
};

onBeforeMount(() => {
  dialogPage.value.size = 5;
  initWeb();
});
</script>

<style lang="scss" scoped>
.order-dimension-container {
  :deep(.remove-border) {
    .el-input__wrapper {
      box-shadow: none !important;
    }
    .el-input.is-focus .el-input__wrapper {
      box-shadow: none !important;
    }
  }
  :deep(.short-select) {
    .el-input__wrapper {
      width: 16px;
    }
  }
  :deep(.mid-select) {
    .el-input__wrapper {
      width: 90px;
      .el-input__inner {
        color: #262626;
      }
    }
  }
  :deep(.long-select) {
    .el-input__wrapper {
      width: 100px;
      .el-input__inner {
        color: #262626;
      }
    }
  }
  :deep(.brand-cascader) {
    width: 100%;
    height: 32px;
    .el-input {
      height: 32px;
    }
  }
  :deep(.edit-dialog) {
    .el-dialog__body {
      padding-top: 8px;
      padding-bottom: 20px;
    }
  }
  :deep(.center-col) {
    display: flex;
    align-items: center;
  }
  :deep(.el-divider) {
    margin: 20px 0;
  }
  :deep(.analysis-dialog) {
    .el-dialog__header {
      padding-left: 24px;
      padding-right: 24px;
    }
    .el-dialog__body {
      padding-left: 24px;
      padding-right: 24px;
    }
  }
  .dialog-title {
    color: #8c8c8c;
  }
  .dialog-content {
    color: #262626;
  }
  .analysis-container {
    margin-top: -10px;
  }
  .analysis-form-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, minmax(0px, 1fr));
  }
}
:deep(.el-message-box__btns) {
  &:first-child {
    border: none;
  }
}
</style>
