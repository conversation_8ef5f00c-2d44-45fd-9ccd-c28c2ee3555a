<template>
  <div class="alarm-dialog-container">
    <el-dialog :title="$t('stuckAnalysis.pp_single_alarm_distribution')" v-model="alarmVisible" :width="'640px'" align-center :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
      <el-form :model="form" inline>
        <el-form-item :label="$t('stuckAnalysis.pp_time')">
          <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getTodayShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledTodayDate" />
        </el-form-item>
      </el-form>
      <el-divider />

      <el-skeleton :rows="10" animated v-if="loading" />

      <div v-else>
        <div class="grid-container margin_b-16">
          <span class="grid-title">{{ $t('stuckAnalysis.pp_alarm_id') }}</span>
          <span class="grid-content">{{ chartList.alarm_id }}</span>
          <span class="grid-title">{{ $t('stuckAnalysis.pp_alarm_name') }}</span>
          <span class="grid-content">{{ chartList.alarm_id_description }}</span>
        </div>
        <div class="grid-container margin_b-16">
          <span class="grid-title">{{ $t('stuckAnalysis.pp_alarm_times') }}</span>
          <span class="grid-content">{{ chartList.total_times }}</span>
          <span class="grid-title">{{ $t('stuckAnalysis.pp_related_fault_orders') }}</span>
          <span class="grid-content">{{ chartList.stuck_times }}</span>
        </div>
        <div id="alarmChart" class="width-full height-280" v-show="hasChartData"></div>
        <el-empty :description="$t('common.pp_empty')" class="width-full height-280" v-if="!hasChartData"></el-empty>

        <div v-if="hasChartData">
          <div style="color: #434343" class="margin_t-20 margin_b-15 font-weight-bold">{{ $t('stuckAnalysis.pp_alarm_occurrence_proportion') }}：{{ xAxisName }}</div>
          <el-table :data="tableList.slice((pages.current - 1) * pages.size, pages.current * pages.size)" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }">
            <el-table-column prop="description" :label="$t('parkingOccupancy.pp_device_name')" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="device_id" :label="$t('parkingOccupancy.pp_device_id')" min-width="200">
              <template #default="{ row }">
                <WelkinCopyBoard :text="row.device_id" direction="rtl" />
              </template>
            </el-table-column>
            <el-table-column prop="stuck_times" :label="$t('stuckAnalysis.pp_alarm_orders')" min-width="90" show-overflow-tooltip />

            <el-table-column prop="proportion" :label="$t('stuckAnalysis.pp_proportion')" min-width="90" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ (row.proportion * 100).toFixed(2) + '%' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="margin_t-20 flex-box flex_j_c-flex-end">
            <Page :page="pages" :pagerCount="5" :layout="'prev, pager, next'" @change="handlePageChange" class="mini-page" />
          </div>
        </div>
      </div>
      <div class="flex-box flex_a_i-center flex_j_c-flex-end margin_t-16">
        <el-button class="close-button" @click="handleCloseDialog">{{ $t('common.pp_close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { ElMessage } from 'element-plus'
import { apiGetSingleAlarmList } from '~/apis/stuck-analysis'
import { initTodayStartTime, initTodayEndTime, getTodayShortcuts, getDisabledTodayDate, getFinalEndTime, barOption } from './constant'
import * as echarts from 'echarts'
import _ from 'lodash'

const props = defineProps({
  alarmVisible: Boolean,
  alarmId: String,
  project: String
})

const emits = defineEmits(['update:alarmVisible'])

const loading = ref(false)
const hasChartData = ref(false)
const xAxisName = ref('' as any)
const tableList = ref([] as any)
const datePicker = ref([initTodayStartTime, initTodayEndTime] as any)
const chartList = ref({} as any)
const form = ref({
  start_time: initTodayStartTime,
  end_time: initTodayEndTime
})
const alarmOption = _.cloneDeep(barOption)
const pages = ref(_.cloneDeep(page))

let myChart: any
const echartRender = (chartId: string, option: any) => {
  myChart && myChart.dispose()
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
  myChart.off('click')
  myChart.on('click', (params: any) => {
    pages.value.current = 1
    xAxisName.value = params.name
    tableList.value = chartList.value.data.find((item: any) => item.rate === params.name).devices || []
    pages.value.total = tableList.value.length
  })
}

const setCharts = () => {
  echartRender('alarmChart', alarmOption)
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
}

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleCloseDialog = () => {
  emits('update:alarmVisible', false)
}

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
  getList()
}

/**
 * @description: 获取全量数据
 * @return {*}
 */
const getList = async () => {
  let formData = _.cloneDeep(form.value) as any
  formData.end_time = getFinalEndTime(formData.end_time)
  loading.value = true
  pages.value.current = 1
  try {
    const res = await apiGetSingleAlarmList(formData, props.project, props.alarmId)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      chartList.value = res
    }
    if (chartList.value.data && chartList.value.data.length > 0) {
      hasChartData.value = true
      alarmOption.xAxis.data = chartList.value.data.map((item: any) => item.rate)
      alarmOption.series[0].data = chartList.value.data.map((item: any) => item.device_count)
      nextTick(() => setCharts())
      tableList.value = chartList.value.data.find((item: any) => item.rate === xAxisName.value).devices || []
      pages.value.total = tableList.value.length
    } else {
      hasChartData.value = false
    }
  } catch (error) {
    loading.value = false
  }
}

onBeforeMount(() => {
  pages.value.size = 5
  xAxisName.value = '20%+'
  getList()
})
</script>

<style lang="scss" scoped>
.alarm-dialog-container {
  :deep(.el-dialog__header) {
    display: flex;
    justify-content: center;
    color: #1f1f1f;
    font-size: 18px;
  }
  :deep(.el-form--inline .el-form-item) {
    margin-bottom: 0;
  }
  :deep(.el-divider) {
    margin: 20px 0;
  }
  .grid-container {
    display: grid;
    grid-template-columns: 5fr 5fr 5fr 9fr;
    align-items: center;
    .grid-title {
      color: #595959;
    }
    .grid-content {
      color: #262626;
    }
  }
}
</style>
