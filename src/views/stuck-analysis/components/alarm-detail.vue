<template>
  <div class="alarm-detail-container">
    <el-form :model="form" ref="ruleFormRef" inline class="margin_b-2">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_time')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-date-picker v-model="datePicker" type="datetimerange" class="width-full" @change="handleDateChange" unlink-panels :shortcuts="getYesterdayShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledYesterdayDate" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_device_search')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-select v-model="form.device_id" filterable remote clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false" :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_alarm_search')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-select v-model="form.alarm_id" clearable filterable remote class="width-full" :placeholder="$t('stuckAnalysis.pp_alarm_tip')" :remote-method="remoteMethod" :loading="remoteLoading">
              <el-option v-for="item in pointOptions" :key="item.value" :value="item.value" :label="item.value + '-' + item.label">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_order_search')" class="width-full normal-form">
            <el-input v-model="form.service_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <div class="flex-box flex_j_c-space-between">
            <div>
              <el-button type="primary" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
              <el-button class="reset-button" style="margin-left: 8px" @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
            </div>
            <el-button @click="batchImportVisible = true" class="reset-button width-108 height-32" style="padding: 5px 16px">
              <UploadIcon />
              <span class="margin_l-4">{{ $t('common.pp_import') }}</span>
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="list" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="loading">
      <el-table-column prop="alarm_id_description" :label="$t('stuckAnalysis.pp_alarm_name')" min-width="240" fixed show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.alarm_id_description ? row.alarm_id_description : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="alarm_id" :label="$t('stuckAnalysis.pp_alarm_id')" min-width="90" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="operation-button" @click="handleViewAlarm(row.alarm_id)">{{ row.alarm_id }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_id" :label="$t('parkingOccupancy.pp_device_id')" min-width="250">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.device_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="description" :label="$t('parkingOccupancy.pp_device_name')" min-width="250" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="alarm_type" :label="$t('stuckAnalysis.pp_alarm_type')" min-width="90" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ $t(alarmTypeMap[row.alarm_type]) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="alarm_level" :label="$t('stuckAnalysis.pp_alarm_level')" min-width="110" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="alarm-level-container" v-if="alarmLevelMap[row.alarm_level]" :style="{ background: alarmLevelMap[row.alarm_level].background, color: alarmLevelMap[row.alarm_level].color }">
            <span>{{ $t(alarmLevelMap[row.alarm_level].name) }}</span>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="state" :label="$t('stuckAnalysis.pp_alarm_status')" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="duty-status width-94" :style="{ background: stateMap[row.state].background, color: '#fff' }">
            <Complete v-if="row.state == 1" />
            <Remind v-else-if="row.state == 2" />
            <Time v-else />
            <span class="margin_l-4">{{ $t(stateMap[row.state].name) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="create_ts" :label="$t('stuckAnalysis.pp_create_time')" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ formatTime(row.create_ts) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="clear_ts" :label="$t('stuckAnalysis.pp_clear_time')" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ formatTime(row.clear_ts) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="service_id" :label="$t('stuckAnalysis.pp_service_id')" min-width="250">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.service_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="service_start_time" :label="$t('stuckAnalysis.pp_service_start_time')" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ formatTime(row.service_start_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="service_end_time" :label="$t('stuckAnalysis.pp_service_end_time')" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ formatTime(row.service_end_time) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="margin_t-20 flex-box flex_j_c-flex-end">
      <Page :page="pages" @change="handlePageChange" />
    </div>

    <AlarmDialog v-model:alarmVisible="alarmVisible" :alarmId="alarmId" :project="project" v-if="alarmVisible" />

    <!-- 批量导入设备 -->
    <UploadDialog v-model:batchImportVisible="batchImportVisible" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, PropType } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { formatTime, removeNullKeys, clearJson } from '~/utils';
import { page } from '~/constvars/page';
import { debounce } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { apiGetDevices } from '~/apis/home';
import { apiGetAlarmIdList, apiGetAlarmDetailList } from '~/apis/stuck-analysis';
import { initYesterdayStartTime, initYesterdayEndTime, getYesterdayShortcuts, getDisabledYesterdayDate, alarmTypeMap, alarmLevelMap, stateMap } from './constant';
import AlarmDialog from './alarm-dialog.vue';
import Remind from '~/assets/svg/remind.vue';
import Complete from '~/assets/svg/complete.vue';
import Time from '~/assets/svg/time.vue';
import UploadIcon from '~/assets/svg/upload.vue';
import UploadDialog from './upload-dialog.vue';
import _ from 'lodash';

interface DeviceObject {
  device_id: string;
  description: string;
}

const props = defineProps({
  project: {
    type: String,
    default: 'PUS4',
  },
  deviceList: {
    type: Array as PropType<DeviceObject[]>,
    required: true,
  },
});

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const ruleFormRef = ref();
const loading = ref(false);
const remoteLoading = ref(false);
const alarmVisible = ref(false);
const batchImportVisible = ref(false);
const remoteDeviceLoading = ref(false);
const alarmId = ref('' as any);
const pointOptions = ref([] as any);
const deviceOptions = ref([] as any);
const datePicker = ref([initYesterdayStartTime, initYesterdayEndTime] as any);
const list = ref([] as any);
const form = ref({
  start_time: initYesterdayStartTime,
  end_time: initYesterdayEndTime,
  device_id: [] as any,
  alarm_id: '',
  service_id: '',
});
const searchForm = ref({} as any);
const pages = ref(_.cloneDeep(page));

const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return;
  searchDeviceList(query);
};
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true;
    const params = { project: props.project, name: val, device_ids: deviceIds, limit: 30 };
    const res = await apiGetDevices(params);
    remoteDeviceLoading.value = false;
    deviceOptions.value = res.data;
  }
}, 500);

/**
 * @description: 确认批量导入设备
 * @param {*} list
 * @return {*}
 */
const handleConfirmBatchImportDevice = (list: any) => {
  const deviceIds = props.deviceList.map((item: any) => item.device_id);
  const importDevice = list.filter((item: any) => deviceIds.includes(item));
  if (importDevice.length > 100) {
    ElMessage.error(t('stuckAnalysis.pp_exceed_100'));
  } else if (importDevice.length == 0) {
    ElMessage.error(t('stuckAnalysis.pp_number_0'));
  } else {
    form.value.device_id = [...new Set([...form.value.device_id, ...importDevice])];
    ElMessage.success(`${t('stuckAnalysis.pp_import_success')} ${importDevice.length} ${t('stuckAnalysis.pp_device_number')}`);
    batchImportVisible.value = false;
    searchDeviceList('NIO', form.value.device_id.join(','));
  }
};

/**
 * @description: 查看单一告警
 * @param {*} id
 * @return {*}
 */
const handleViewAlarm = (id: any) => {
  alarmId.value = _.cloneDeep(id);
  alarmVisible.value = true;
};

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList();
};

/**
 * @description: 远程搜索告警描述点
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchPointList(query);
};
const searchPointList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true;
    const params = { description: val };
    const res = await apiGetAlarmIdList(params, props.project);
    pointOptions.value = [];
    Object.keys(res.data).forEach((key: any) => {
      pointOptions.value.push({
        label: res.data[key],
        value: key,
      });
    });
    remoteLoading.value = false;
  }
}, 500);

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime();
  form.value.end_time = new Date(val[1]).getTime();
};

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  sessionStorage.setItem('stuck-form', JSON.stringify({ device_id: searchForm.value.device_id }));
  let formData = _.cloneDeep(searchForm.value) as any;
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.device_id = formData.device_id.join(',');
  removeNullKeys(formData);
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: route.query.tab, project: props.project, ...formData },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetAlarmDetailList(formData, props.project);
    loading.value = false;
    if (res.err_code) {
      ElMessage.error(res.message);
    } else {
      list.value = res.data;
      pages.value.total = res.total;
    }
  } catch (error) {
    loading.value = false;
  }
};

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value);
  datePicker.value = [initYesterdayStartTime, initYesterdayEndTime];
  form.value.start_time = datePicker.value[0];
  form.value.end_time = datePicker.value[1];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  searchForm.value = _.cloneDeep(form.value);
  getList();
};

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query;
  const deviceCache = JSON.parse(sessionStorage.getItem('stuck-form') as any);
  if (initParams.tab == 'order-dimension') {
    form.value.device_id = deviceCache ? deviceCache.device_id : [];
  } else {
    form.value.device_id = !!initParams.device_id ? initParams.device_id.split(',') : deviceCache ? deviceCache.device_id : [];
  }
  if (initParams.tab == 'alarm-detail') {
    if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
      form.value.start_time = Number(initParams.start_time);
      form.value.end_time = Number(initParams.end_time);
      datePicker.value = [form.value.start_time, form.value.end_time];
    }
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
    form.value.alarm_id = !!initParams.alarm_id ? initParams.alarm_id : '';
    form.value.service_id = !!initParams.service_id ? initParams.service_id : '';
  }
  searchForm.value = _.cloneDeep(form.value);
  getList(false);
  searchDeviceList('NIO', form.value.device_id.join(','));
};

watch(
  () => props.project,
  (newVal, oldVal) => {
    pages.value.current = 1;
    form.value.device_id = [];
    form.value.alarm_id = '';
    searchForm.value = _.cloneDeep(form.value);
    getList();
    searchDeviceList('NIO');
  }
);

onBeforeMount(() => {
  initWeb();
});
</script>

<style lang="scss" scoped>
.alarm-detail-container {
  :deep(.el-form-item__content) {
    flex-wrap: nowrap;
  }
}
</style>
