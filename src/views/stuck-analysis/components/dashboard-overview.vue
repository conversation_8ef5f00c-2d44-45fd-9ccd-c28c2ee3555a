<template>
  <div class="dashboard-overview-container">
    <el-form :model="form" inline class="margin_b-2">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_time')" class="width-full">
            <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_device_search')" class="width-full">
            <el-select v-model="form.device_id" filterable remote clearable :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <div class="flex-box flex_j_c-space-between">
            <el-button type="primary" :loading="tableLoading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="batchImportVisible = true" class="reset-button width-108 height-32" style="padding: 5px 16px">
              <UploadIcon />
              <span class="margin_l-4">{{ $t('common.pp_import') }}</span>
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="20">
      <el-col :span="17">
        <el-card>
          <el-skeleton :rows="8" animated v-show="chartLoading" />
          <div class="flex-box flex_j_c-space-between" v-show="!chartLoading">
            <div class="flex-box flex_d-column">
              <span class="margin_b-6 section-title">{{ $t('stuckAnalysis.pp_average_stuck_rate') }}</span>
              <span class="flex-box flex_a_i-center">
                <BookIcon />
                <span class="margin_l-8 font-size-16">{{ getFourDecimalPlaces(chartList.average_stuck_rate) + '‱' }}</span>
              </span>
            </div>
            <div class="flex-box flex_d-column">
              <span class="margin_b-6 section-title">{{ $t('stuckAnalysis.pp_stuck_order_quantity') }}</span>
              <span class="flex-box flex_a_i-center">
                <FlagIcon />
                <span class="margin_l-8 font-size-16">{{ chartList.stuck_service_count + ' ' + $t('stuckAnalysis.pp_orders') }}</span>
              </span>
            </div>
            <div class="flex-box flex_d-column">
              <span class="margin_b-6 section-title">{{ $t('stuckAnalysis.pp_total_order_quantity') }}</span>
              <span class="flex-box flex_a_i-center">
                <PeopleIcon />
                <span class="margin_l-8 font-size-16">{{ chartList.total_servie_count + ' ' + $t('stuckAnalysis.pp_orders') }}</span>
              </span>
            </div>
            <div class="flex-box flex_d-column">
              <span class="margin_b-6 section-title">{{ $t('stuckAnalysis.pp_selected_days') }}</span>
              <span class="flex-box flex_a_i-center">
                <CalendarIcon />
                <span class="margin_l-8 font-size-16">{{ chartList.stat_day_nums + ' ' + $t('stuckAnalysis.pp_days') }}</span>
              </span>
            </div>
          </div>
          <div id="lineChart" class="width-full height-240" v-show="!chartLoading && hasChartData"></div>
          <el-empty class="width-full height-240 margin_t-10" :description="$t('stuckAnalysis.pp_line_empty')" v-if="!chartLoading && !hasChartData"></el-empty>

          <div class="font-size-16 font-weight-500 margin_t-24 margin_b-8" style="color: #262626">{{ $t('stuckAnalysis.pp_stuck_alarm_ranking') }}</div>
          <el-table :data="tableList" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="tableLoading">
            <el-table-column prop="no" label="No." width="50" show-overflow-tooltip />
            <el-table-column prop="alarm_id_description" :label="$t('stuckAnalysis.pp_alarm_name')" min-width="230" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ row.alarm_id_description ? row.alarm_id_description : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="alarm_id" :label="$t('stuckAnalysis.pp_alarm_id')" min-width="100" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="operation-button" @click="handleViewAlarm(row.alarm_id)">{{ row.alarm_id }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="alarm_type" :label="$t('stuckAnalysis.pp_alarm_type')" min-width="115" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ $t(alarmTypeMap[row.alarm_type]) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="stuck_times" :label="$t('stuckAnalysis.pp_stuck_times')" min-width="110" show-overflow-tooltip />
            <el-table-column prop="total_times" :label="$t('stuckAnalysis.pp_total_times')" min-width="105" show-overflow-tooltip />
            <el-table-column prop="alarm_rate" :label="$t('stuckAnalysis.pp_alarm_stuck')" min-width="115" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ getFourDecimalPlaces(row.alarm_rate) + '‱' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="margin_t-20 flex-box flex_j_c-flex-end">
            <Page :page="pages" @change="handlePageChange" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="7" style="padding-left: 6px">
        <el-skeleton :rows="15" animated v-show="rightLoading" />
        <div v-show="!rightLoading" style="padding: 16px; border-radius: 4px; background: linear-gradient(180deg, #c6efef, #e5f9f9)">
          <div class="flex-box flex_j_c-space-between margin_b-8">
            <span class="font-size-16 font-weight-500" style="color: #262626">{{ $t('stuckAnalysis.pp_device_stuck_ranking') }}</span>
            <span class="cursor-pointer" style="color: #00bebe" @click="handleViewMore">{{ $t('stuckAnalysis.pp_more') }}</span>
          </div>
          <div class="flex-box flex_d-column gap_8" v-if="rightList.length > 0">
            <el-popover placement="bottom-end" :width="150" :offset="-14" trigger="hover" :show-arrow="false" v-for="item in chartList.daily_details && chartList.daily_details.length > 0 ? rightList.slice(0, 10) : rightList.slice(0, 6)" :popper-style="{ background: '#00bebe' }">
              <template #reference>
                <el-card class="list-card">
                  <div class="flex-box flex_a_i-center flex_j_c-space-between" style="color: #434343">
                    <div class="flex-box flex_a_i-center">
                      <span class="font-weight-480">{{ item.no }}</span>
                      <VerticalLine class="margin-n-8" />
                      <div>
                        <div class="margin_b-6 font-weight-450">{{ item.description }}</div>
                        <div class="font-size-13" style="color: #8c8c8c">{{ item.device_id }}</div>
                      </div>
                    </div>
                    <span class="font-weight-480">{{ getFourDecimalPlaces(item.stuck_rate) + '‱' }}</span>
                  </div>
                </el-card>
              </template>
              <div class="font-weight-480" style="color: #fff">
                <div class="margin_b-6">{{ $t('stuckAnalysis.pp_stuck_orders') }}：{{ item.stuck_service_count }}</div>
                <div>{{ $t('stuckAnalysis.pp_total_orders') }}：{{ item.service_count }}</div>
              </div>
            </el-popover>
          </div>
          <el-empty class="width-full height-240" :description="$t('common.pp_empty')" v-else />
        </div>
      </el-col>
    </el-row>

    <el-dialog :title="$t('stuckAnalysis.pp_device_stuck_ranking')" v-model="dialogVisible" :width="'1100px'" align-center @close="dialogVisible = false">
      <el-form :model="dialogForm" inline class="margin_b-6">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('stuckAnalysis.pp_time')" class="width-full">
              <el-date-picker v-model="dialogDatePicker" type="daterange" style="width: 100%" @change="handleDialogDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('stuckAnalysis.pp_device_name')" class="width-full">
              <RemoteDeviceSelect ref="remoteDeviceSelectRef" v-model="dialogForm.device_id" :project="dialogForm.project" @handleDeviceChange="handleDeviceChange" class="width-full" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('stuckAnalysis.pp_device_type')" class="width-full">
              <el-select v-model="dialogForm.project" filterable style="width: 100%" :placeholder="$t('common.pp_please_select')" @change="handleChangeProject">
                <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table :data="dialogList" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="dialogLoading">
        <el-table-column prop="no" label="No." width="50" show-overflow-tooltip />
        <el-table-column prop="device_id" :label="$t('stuckAnalysis.pp_device_id')" min-width="250">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('parkingOccupancy.pp_device_name')" min-width="250">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.description || $t('common.pp_unnamed_device')" textColor="#01A0AC" @clickCopyText="clickCopyText(row.device_id)" />
          </template>
        </el-table-column>
        <el-table-column prop="stuck_rate" :label="$t('stuckAnalysis.pp_stuck_rate')" min-width="115" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ getFourDecimalPlaces(row.stuck_rate) + '‱' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stuck_service_count" :label="$t('stuckAnalysis.pp_stuck_order')" min-width="110" show-overflow-tooltip />
        <el-table-column prop="service_count" :label="$t('stuckAnalysis.pp_total_orders')" min-width="105" show-overflow-tooltip />
        <el-table-column prop="owner" :label="$t('stuckAnalysis.pp_om_owner')" :width="ownerList.length > 0 ? flexColumnWidth(ownerList, 74, 8) : 100" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <span v-if="row.owner == '/' || !row.owner">{{ row.owner }}</span>
              <div class="flex-box flex_a_i-center avatar-container" v-else>
                <el-avatar size="small" :src="row.owner_avatar_url" />
                <span class="margin_l-8">{{ row.owner }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="margin_t-20 flex-box flex_j_c-flex-end">
        <Page :page="dialogPage" :layout="'prev, pager, next, sizes'" @change="handleDialogPageChange" />
      </div>
    </el-dialog>

    <AlarmDialog v-model:alarmVisible="alarmVisible" :alarmId="alarmId" :project="project" v-if="alarmVisible" />

    <!-- 批量导入设备 -->
    <UploadDialog v-model:batchImportVisible="batchImportVisible" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, watch, nextTick, PropType } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { page } from '~/constvars/page'
import { ElMessage } from 'element-plus'
import { getFourDecimalPlaces, flexColumnWidth, removeNullKeys } from '~/utils'
import { getFinalEndTime, lineOption, projectOptions, alarmTypeMap } from './constant'
import { apiGetDevices } from '~/apis/home'
import { apiGetDashboardList, apiGetAlarmList, apiGetDeviceRankList } from '~/apis/stuck-analysis'
import { debounce } from 'lodash-es'
import BookIcon from '~/assets/svg/book.vue'
import FlagIcon from '~/assets/svg/flag.vue'
import PeopleIcon from '~/assets/svg/people.vue'
import CalendarIcon from '~/assets/svg/calendar.vue'
import VerticalLine from '~/assets/svg/vertical-line.vue'
import UploadIcon from '~/assets/svg/upload.vue'
import AlarmDialog from './alarm-dialog.vue'
import UploadDialog from './upload-dialog.vue'
import * as echarts from 'echarts'
import _ from 'lodash'

interface DeviceObject {
  device_id: string
  description: string
}

const props = defineProps({
  project: {
    type: String,
    default: 'PUS4'
  },
  deviceList: {
    type: Array as PropType<DeviceObject[]>,
    required: true
  }
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const now = new Date()
const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
today.setHours(0, 0, 0, 0)
const initStartTime = today.getTime() - 3600 * 1000 * 24 * 7
const initEndTime = today.getTime() - 1
const chartLoading = ref(false)
const hasChartData = ref(false)
const tableLoading = ref(false)
const rightLoading = ref(false)
const dialogLoading = ref(false)
const dialogVisible = ref(false)
const alarmVisible = ref(false)
const batchImportVisible = ref(false)
const remoteDeviceLoading = ref(false)
const alarmId = ref('' as any)
const ownerList = ref([] as any)
const echartsArr = ref([] as any)
const chartList = ref({} as any)
const tableList = ref([] as any)
const rightList = ref([] as any)
const dialogList = ref([] as any)
const deviceOptions = ref([] as any)
const datePicker = ref([initStartTime, initEndTime] as any)
const dialogDatePicker = ref([] as any)
const form = ref({
  start_time: initStartTime,
  end_time: initEndTime,
  device_id: [] as any
})
const dialogForm = ref({
  start_time: '' as number | string,
  end_time: '' as number | string,
  device_id: '',
  project: ''
})
const pages = ref(_.cloneDeep(page))
const dialogPage = ref(_.cloneDeep(page))
const lineOption1 = _.cloneDeep(lineOption)
const isCollapse = computed(() => store.state.menus.collapse)
const getShortcuts = () => {
  return [
    {
      text: t('common.pp_last_week'),
      value: [initStartTime, initEndTime]
    },
    {
      text: t('common.pp_last_month'),
      value: [initStartTime - 3600 * 1000 * 24 * 23, initEndTime]
    }
  ]
}
const getDisabledDate = (time: Record<string, any>) => time.getTime() > initEndTime
const remoteDeviceSelectRef = ref()

const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true
    const params = { project: props.project, name: val, device_ids: deviceIds, limit: 30 }
    const res = await apiGetDevices(params)
    remoteDeviceLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 确认批量导入
 * @param {*} list
 * @return {*}
 */
const handleConfirmBatchImportDevice = (list: any) => {
  const deviceIds = props.deviceList.map((item: any) => item.device_id)
  const importDevice = list.filter((item: any) => deviceIds.includes(item))
  if (importDevice.length > 100) {
    ElMessage.error(t('stuckAnalysis.pp_exceed_100'))
  } else if (importDevice.length == 0) {
    ElMessage.error(t('stuckAnalysis.pp_number_0'))
  } else {
    form.value.device_id = [...new Set([...form.value.device_id, ...importDevice])]
    ElMessage.success(`${t('stuckAnalysis.pp_import_success')} ${importDevice.length} ${t('stuckAnalysis.pp_device_number')}`)
    batchImportVisible.value = false
    searchDeviceList('NIO', form.value.device_id.join(','))
  }
}

const handleViewAlarm = (id: any) => {
  alarmId.value = _.cloneDeep(id)
  alarmVisible.value = true
}

let myChart: any
const echartRender = (chartId: string, option: any) => {
  myChart && myChart.dispose()
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  echartsArr.value = [myChart]
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  echartRender('lineChart', lineOption1)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 跳转到订单维度
 * @param {*} deviceId
 * @return {*}
 */
const clickCopyText = (deviceId: any) => {
  const tab = 'order-dimension'
  window.open(`//${location.host}/fault-diagnosis/stuck-analysis?tab=${tab}&start_time=${dialogForm.value.start_time}&end_time=${getFinalEndTime(Number(dialogForm.value.end_time))}&device_id=${deviceId}&project=${dialogForm.value.project}`)
}

/**
 * @description: 切换弹窗中的设备类型
 * @return {*}
 */
const handleChangeProject = () => {
  dialogForm.value.device_id = ''
  dialogPage.value.current = 1
  if (remoteDeviceSelectRef.value) remoteDeviceSelectRef.value.searchDeviceList('NIO')
  getDialogList()
}

/**
 * @description: 切换弹窗中的设备名称
 * @return {*}
 */
const handleDeviceChange = () => {
  dialogPage.value.current = 1
  getDialogList()
}

/**
 * @description: 查看更多
 * @return {*}
 */
const handleViewMore = () => {
  dialogForm.value.start_time = _.cloneDeep(form.value.start_time)
  dialogForm.value.end_time = _.cloneDeep(form.value.end_time)
  dialogDatePicker.value = [dialogForm.value.start_time, dialogForm.value.end_time]
  dialogForm.value.device_id = ''
  dialogForm.value.project = props.project
  dialogPage.value.current = 1
  dialogVisible.value = true
  if (remoteDeviceSelectRef.value) remoteDeviceSelectRef.value.searchDeviceList('NIO')
  getDialogList()
}

const getChartList = async () => {
  const params = {
    project: props.project,
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    device_id: form.value.device_id.join(',')
  }
  chartLoading.value = true
  try {
    const res = await apiGetDashboardList(params)
    chartLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      chartList.value = res.data
      if (res.data.daily_details && res.data.daily_details.length > 0) {
        hasChartData.value = true
        lineOption1.xAxis.data = chartList.value.daily_details.map((item: any) => item.day)
        lineOption1.series[0].data = chartList.value.daily_details.map((item: any) => getFourDecimalPlaces(item.stuck_rate))
        nextTick(() => setCharts())
      } else {
        hasChartData.value = false
      }
    }
  } catch (error) {
    chartLoading.value = false
  }
}

const getTableList = async (updateRoute = true) => {
  const params = {
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    page: pages.value.current,
    size: pages.value.size,
    device_id: form.value.device_id.join(',')
  }
  removeNullKeys(params)
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: 'dashboard-overview', project: props.project, ...params }
    })
  }
  tableLoading.value = true
  try {
    const res = await apiGetAlarmList(params, props.project)
    tableLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      tableList.value = res.data
      pages.value.total = res.total
    }
  } catch (error) {
    tableLoading.value = false
  }
}

const getRightList = async () => {
  const params = {
    project: props.project,
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    page: 1,
    size: 10,
    descending: true
  }
  rightLoading.value = true
  try {
    const res = await apiGetDeviceRankList(params)
    rightLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      rightList.value = res.data || []
    }
  } catch (error) {
    rightLoading.value = false
  }
}

const getList = (updateRoute = true) => {
  sessionStorage.setItem('stuck-form', JSON.stringify({ device_id: form.value.device_id }))
  const queryObj = {
    tab: 'dashboard-overview',
    project: props.project,
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    page: pages.value.current,
    size: pages.value.size,
    device_id: form.value.device_id.join(',')
  }
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys(queryObj) }
    })
  }
  getChartList()
  getTableList(false)
  getRightList()
}

/**
 * @description: 获取弹窗中表格数据
 * @return {*}
 */
const getDialogList = async () => {
  dialogLoading.value = true
  const formData = _.cloneDeep(dialogForm.value) as any
  formData.page = dialogPage.value.current
  formData.size = dialogPage.value.size
  formData.end_time = getFinalEndTime(formData.end_time)
  removeNullKeys(formData)
  try {
    const res = await apiGetDeviceRankList(formData)
    dialogLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      dialogList.value = res.data || []
      dialogPage.value.total = res.total
      ownerList.value = [...new Set(dialogList.value.filter((item: any) => item.owner).map((item: any) => item.owner))]
    }
  } catch (error) {
    dialogLoading.value = false
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getTableList()
}

/**
 * @description: 点击搜索
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  getList()
}

/**
 * @description: 弹窗内分页
 * @param {*} argPage
 * @return {*}
 */
const handleDialogPageChange = (argPage: any) => {
  dialogPage.value.current = argPage.current
  dialogPage.value.size = argPage.size
  getDialogList()
}

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 弹窗中时间变更
 * @param {*} val
 * @return {*}
 */
const handleDialogDateChange = (val: any) => {
  dialogForm.value.start_time = new Date(val[0]).getTime()
  dialogForm.value.end_time = new Date(val[1]).getTime()
  dialogPage.value.current = 1
  getDialogList()
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  const deviceCache = JSON.parse(sessionStorage.getItem('stuck-form') as any)
  if (initParams.tab == 'order-dimension') {
    form.value.device_id = deviceCache ? deviceCache.device_id : []
  } else {
    form.value.device_id = !!initParams.device_id ? initParams.device_id.split(',') : deviceCache ? deviceCache.device_id : []
  }
  if (!initParams.tab || initParams.tab == 'dashboard-overview') {
    if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
      form.value.start_time = Number(initParams.start_time)
      form.value.end_time = Number(initParams.end_time)
      datePicker.value = [form.value.start_time, form.value.end_time]
    }
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  }
  getList(false)
  searchDeviceList('NIO', form.value.device_id.join(','))
}

watch(
  () => props.project,
  (newVal, oldVal) => {
    pages.value.current = 1
    form.value.device_id = []
    getList()
    searchDeviceList('NIO')
  }
)

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.dashboard-overview-container {
  font-size: 14px;
  :deep(.el-card.is-always-shadow) {
    border-color: rgba(220, 242, 243, 1);
  }
  :deep(.list-card:hover) {
    border-color: #00bebe;
  }
  :deep(.list-card .el-card__body) {
    padding: 15px;
  }
  .section-title {
    color: rgba(89, 89, 89, 1);
  }
}
</style>
