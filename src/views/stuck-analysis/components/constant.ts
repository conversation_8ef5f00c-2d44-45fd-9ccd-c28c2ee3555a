import { i18n } from '~/i18n'

// 订单维度和挂车告警列表默认选择当天一整天
const now = new Date()
const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
today.setHours(0, 0, 0, 0)
export const initTodayStartTime = today.getTime()
export const initTodayEndTime = today.getTime() + 86399999
export const getTodayShortcuts = () => {
  return [
    {
      text: i18n.global.t('common.pp_today'),
      value: [initTodayStartTime, initTodayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: [initTodayStartTime - 3600 * 1000 * 24 * 6, initTodayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: [initTodayStartTime - 3600 * 1000 * 24 * 30, initTodayEndTime]
    }
  ]
}
export const getDisabledTodayDate = (time: Record<string, any>) => time.getTime() > Date.now()

// 告警详情默认选择昨天一整天
const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
yesterday.setHours(0, 0, 0, 0)
export const initYesterdayStartTime = yesterday.getTime()
export const initYesterdayEndTime = yesterday.getTime() + 86399999
export const getYesterdayShortcuts = () => {
  return [
    {
      text: i18n.global.t('common.pp_yesterday'),
      value: [initYesterdayStartTime, initYesterdayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: [initYesterdayStartTime - 3600 * 1000 * 24 * 6, initYesterdayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: [initYesterdayStartTime - 3600 * 1000 * 24 * 29, initYesterdayEndTime]
    }
  ]
}
export const getDisabledYesterdayDate = (time: Record<string, any>) => time.getTime() > initYesterdayEndTime

export const getFinalEndTime = (timestamp: number) => {
  const date = new Date(timestamp)
  date.setHours(23, 59, 59, 999)
  return date.getTime()
}

export const dutyStatusOptions = [
  {
    label: 'stuckAnalysis.pp_unknown',
    value: 0
  },
  {
    label: 'stuckAnalysis.pp_somebody',
    value: 1
  },
  {
    label: 'stuckAnalysis.pp_nobody',
    value: 2
  }
]

export const dutyStatusMap: any = {
  0: {
    name: 'stuckAnalysis.pp_unknown',
    background: '#F4F1FF',
    color: '#888AF7'
  },
  1: {
    name: 'stuckAnalysis.pp_somebody',
    background: '#F0F9EB',
    color: '#26BD4B'
  },
  2: {
    name: 'stuckAnalysis.pp_nobody',
    background: '#FFF8E8',
    color: '#FD8C08'
  }
}

export const analysisStatusOptions: any = [
  {
    label: 'stuckAnalysis.pp_analyzed',
    value: 1
  },
  {
    label: 'stuckAnalysis.pp_unanalyzed',
    value: 0
  }
]

export const analysisStatusMap: any = {
  1: {
    name: 'stuckAnalysis.pp_analyzed',
    background: '#67C23A',
    color: '#fff'
  },
  0: {
    name: 'stuckAnalysis.pp_unanalyzed',
    background: '#FFC031',
    color: '#fff'
  }
}

export const lineOption = {
  animation: false,
  color: ['#00bebe'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    left: '0%',
    right: '2%',
    bottom: 0,
    top: 32,
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(228, 228, 228, 1)'
      }
    },
    data: [] as any
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dotted',
        color: 'rgba(227, 227, 227, 1)'
      }
    }
  },
  series: [
    {
      data: [],
      type: 'line',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, // 渐变起始位置的横坐标，0 为最左侧，0.5 为中间，1 为最右侧
          y: 0, // 渐变起始位置的纵坐标，0 为最顶部，0.5 为中间，1 为最底部
          x2: 0, // 渐变结束位置的横坐标
          y2: 1, // 渐变结束位置的纵坐标，1 为最底部
          colorStops: [
            {
              offset: 0,
              color: 'rgba(0, 190, 190, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(0, 190, 190, 0)'
            }
          ],
          global: false
        }
      },
      label: {
        show: true,
        position: 'top',
        color: 'rgba(53, 121, 139, 1)'
      },
      labelLayout: {
        hideOverlap: true
      },
      tooltip: {
        valueFormatter: (value: any) => value + ' ‱'
      }
    }
  ]
}

export const barOption = {
  animation: false,
  color: ['#00bebe'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    left: '0%',
    right: '2%',
    bottom: '4%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: { show: false },
    axisLabel: {
      show: true,
      interval: 0,
      color: 'rgba(89, 89, 89, 1)'
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(228, 228, 228, 1)'
      }
    },
    data: [] as any
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dotted',
        color: 'rgba(227, 227, 227, 1)'
      }
    }
  },
  series: [
    {
      data: [] as any,
      type: 'bar',
      barMaxWidth: 30,
      label: {
        show: true,
        position: 'top',
        color: '#35798B'
      },
      labelLayout: {
        hideOverlap: true
      }
    }
  ]
}

export const projectOptions = [
  {
    label: 'stuckAnalysis.pp_swap_station2',
    value: 'PowerSwap2'
  },
  {
    label: 'stuckAnalysis.pp_swap_station3',
    value: 'PUS3'
  },
  {
    label: 'stuckAnalysis.pp_swap_station4',
    value: 'PUS4'
  }
]

export const projectMap: any = {
  PUS4: {
    name: 'menu.pp_swap_station4',
    color: '#21819D'
  },
  PUS3: {
    name: 'menu.pp_swap_station3',
    color: '#00BEBE'
  },
  PowerSwap2: {
    name: 'menu.pp_swap_station2',
    color: '#67C23A'
  },
  PowerSwap: {
    name: 'menu.pp_swap_station1',
    color: '#FFC031'
  }
}

export const alarmTypeMap: any = {
  1: 'alarmList.pp_basic_alarm',
  2: 'alarmList.pp_battery_alarm',
  3: 'alarmList.pp_unknown_alarm'
}

export const alarmLevelMap: any = {
  0: {
    name: 'alarmList.pp_unknown_alarm',
    color: '#9e9e9e',
    background: '#eee'
  },
  1: {
    name: 'alarmList.pp_first_level',
    color: 'rgba(253, 140, 8, 1)',
    background: 'rgba(255, 192, 49, 0.1)'
  },
  2: {
    name: 'alarmList.pp_second_level',
    color: 'rgba(255, 119, 46, 1)',
    background: 'rgba(255, 159, 115, 0.1)'
  },
  3: {
    name: 'alarmList.pp_third_level',
    color: 'rgba(232, 48, 48, 1)',
    background: 'rgba(255, 117, 117, 0.1)'
  }
}

export const stateMap: any = {
  1: {
    name: 'stuckAnalysis.pp_alarm_clear',
    background: '#67C23A'
  },
  2: {
    name: 'alarmList.pp_alarming',
    background: '#FF7575'
  },
  3: {
    name: 'alarmList.pp_unknown',
    background: '#888AF7'
  }
}

export const alarmLevelOptions = [
  {
    label: 'alarmList.pp_unknown_alarm',
    value: 0
  },
  {
    label: 'alarmList.pp_first_level',
    value: 1
  },
  {
    label: 'alarmList.pp_second_level',
    value: 2
  },
  {
    label: 'alarmList.pp_third_level',
    value: 3
  }
]
