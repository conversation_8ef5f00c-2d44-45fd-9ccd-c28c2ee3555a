<template>
  <div class="powerswap-step-container">
    <el-form :model="form" inline class="margin_b-2">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_time')" class="width-full">
            <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getTodayShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledTodayDate" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_device_search')" class="width-full">
            <el-select v-model="form.device_id" filterable remote clearable :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <div class="flex-box flex_j_c-space-between">
            <el-button type="primary" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="batchImportVisible = true" class="reset-button width-108 height-32" style="padding: 5px 16px">
              <UploadIcon />
              <span class="margin_l-4">{{ $t('common.pp_import') }}</span>
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-skeleton :rows="15" animated v-if="loading" />

    <el-card v-if="!loading && hasData">
      <div class="content-box">
        <div v-for="(item, index) in list" class="flex-box">
          <div class="flex-box flex_d-column flex_a_i-center gap_8 width-132">
            <div class="circle-index">{{ item.step_num }}</div>
            <WelkinCopyBoard :text="item.step_name" class="width-full flex-box flex_j_c-center" />
            <div style="color: #595959">{{ $t('stuckAnalysis.pp_stuck_rate') }}</div>
            <div :style="{ color: item.step_stuck_rate > 0 ? 'red' : '#303133' }" :class="['font-size-16', { 'cursor-pointer': item.step_stuck_rate > 0 }]" @click="handleClickRate(item)">{{ getFourDecimalPlaces(item.step_stuck_rate) + '‱' }}</div>
          </div>
          <div class="margin_t-6 width-107" v-if="index < list.length - 1"><StepLine /></div>
        </div>
      </div>
    </el-card>

    <el-empty class="width-full height-400 margin_t-10" :description="$t('common.pp_empty')" v-if="!loading && !hasData"></el-empty>

    <el-dialog :title="alarmName" v-model="dialogVisible" :width="'900px'" align-center @close="dialogVisible = false">
      <el-table :data="tableList" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="tableLoading">
        <el-table-column prop="alarm_id_description" :label="$t('stuckAnalysis.pp_alarm_name')" min-width="210" fixed show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.alarm_id_description ? row.alarm_id_description : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarm_rate" :label="$t('stuckAnalysis.pp_alarm_stuck')" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ getFourDecimalPlaces(row.alarm_rate) + '‱' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stuck_times" :label="$t('stuckAnalysis.pp_stuck_orders')" min-width="100" show-overflow-tooltip />
        <el-table-column prop="owner_software" :label="$t('stuckAnalysis.pp_software_owner')" :width="flexColumnWidth(softOwnerList, 74, 7)" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <span v-if="row.owner_software == '/' || !row.owner_software">{{ row.owner_software }}</span>
              <div class="flex-box flex_a_i-center avatar-container" v-else>
                <el-avatar size="small" :src="avatarList[row.owner_software]" />
                <span class="margin_l-8">{{ row.owner_software }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="owner_hardware" :label="$t('stuckAnalysis.pp_hardware_owner')" :width="flexColumnWidth(hardOwnerList, 74, 7)" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <span v-if="row.owner_hardware == '/' || !row.owner_hardware">{{ row.owner_hardware }}</span>
              <div class="flex-box flex_a_i-center avatar-container" v-else>
                <el-avatar size="small" :src="avatarList[row.owner_hardware]" />
                <span class="margin_l-8">{{ row.owner_hardware }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="margin_t-20 flex-box flex_j_c-flex-end">
        <Page :page="pages" :layout="'prev, pager, next'" @change="handlePageChange" class="mini-page" />
      </div>
    </el-dialog>

    <!-- 批量导入设备 -->
    <UploadDialog v-model:batchImportVisible="batchImportVisible" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, PropType } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { page } from '~/constvars/page'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash-es'
import { apiGetDevices } from '~/apis/home'
import { getFourDecimalPlaces, flexColumnWidth, getFinalEndTime, removeNullKeys } from '~/utils'
import { apiGetStepList, apiGetAlarmList } from '~/apis/stuck-analysis'
import { initTodayStartTime, initTodayEndTime, getTodayShortcuts, getDisabledTodayDate } from './constant'
import StepLine from '~/assets/svg/step-line.vue'
import UploadIcon from '~/assets/svg/upload.vue'
import UploadDialog from './upload-dialog.vue'
import _ from 'lodash'

interface DeviceObject {
  device_id: string
  description: string
}

const props = defineProps({
  project: {
    type: String,
    default: 'PUS4'
  },
  deviceList: {
    type: Array as PropType<DeviceObject[]>,
    required: true
  }
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const tableLoading = ref(false)
const dialogVisible = ref(false)
const batchImportVisible = ref(false)
const remoteDeviceLoading = ref(false)
const hasData = ref(false)
const alarmName = ref('' as any)
const alarmId = ref('' as any)
const softOwnerList = ref([] as any)
const hardOwnerList = ref([] as any)
const deviceOptions = ref([] as any)
const datePicker = ref([initTodayStartTime, initTodayEndTime] as any)
const list = ref([] as any)
const tableList = ref([] as any)
const form = ref({
  start_time: initTodayStartTime,
  end_time: initTodayEndTime,
  device_id: [] as any
})
const avatarList = ref({} as any)
const pages = ref(_.cloneDeep(page))

const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true
    const params = { project: props.project, name: val, device_ids: deviceIds, limit: 30 }
    const res = await apiGetDevices(params)
    remoteDeviceLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 确认批量导入设备
 * @param {*} list
 * @return {*}
 */
const handleConfirmBatchImportDevice = (list: any) => {
  const deviceIds = props.deviceList.map((item: any) => item.device_id)
  const importDevice = list.filter((item: any) => deviceIds.includes(item))
  if (importDevice.length > 100) {
    ElMessage.error(t('stuckAnalysis.pp_exceed_100'))
  } else if (importDevice.length == 0) {
    ElMessage.error(t('stuckAnalysis.pp_number_0'))
  } else {
    form.value.device_id = [...new Set([...form.value.device_id, ...importDevice])]
    ElMessage.success(`${t('stuckAnalysis.pp_import_success')} ${importDevice.length} ${t('stuckAnalysis.pp_device_number')}`)
    batchImportVisible.value = false
    searchDeviceList('NIO', form.value.device_id.join(','))
  }
}

const getDialogList = async () => {
  const params = {
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    alarm_id: alarmId.value,
    page: pages.value.current,
    size: pages.value.size,
    device_id: form.value.device_id.join(',')
  }
  tableLoading.value = true
  try {
    const res = await apiGetAlarmList(params, props.project)
    tableLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      tableList.value = res.data
      pages.value.total = res.total
      avatarList.value = res.avatar || {}
      softOwnerList.value = [...new Set(tableList.value.map((item: any) => item.owner_software))]
      hardOwnerList.value = [...new Set(tableList.value.map((item: any) => item.owner_hardware))]
    }
  } catch (error) {
    tableLoading.value = false
  }
}

const handleClickRate = async (item: any) => {
  if (item.step_stuck_rate === 0) return
  alarmName.value = item.step_name
  alarmId.value = item.step_alarm_ids.join(',')
  pages.value.current = 1
  getDialogList()
  dialogVisible.value = true
}

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 点击搜索
 * @return {*}
 */
const handleSearch = () => {
  getList()
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  sessionStorage.setItem('stuck-form', JSON.stringify({ device_id: form.value.device_id }))
  const params = {
    project: props.project,
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    device_id: form.value.device_id.join(',')
  }
  removeNullKeys(params)
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: 'powerswap-step', ...params }
    })
  }
  loading.value = true
  try {
    const res = await apiGetStepList(params)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      hasData.value = res.data && res.data.length > 0
    }
  } catch (error) {
    loading.value = false
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getDialogList()
}

watch(
  () => props.project,
  (newVal, oldVal) => {
    form.value.device_id = []
    getList()
    searchDeviceList('NIO')
  }
)

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  const deviceCache = JSON.parse(sessionStorage.getItem('stuck-form') as any)
  if (initParams.tab == 'order-dimension') {
    form.value.device_id = deviceCache ? deviceCache.device_id : []
  } else {
    form.value.device_id = !!initParams.device_id ? initParams.device_id.split(',') : deviceCache ? deviceCache.device_id : []
  }
  if (initParams.tab == 'powerswap-step') {
    if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
      form.value.start_time = Number(initParams.start_time)
      form.value.end_time = Number(initParams.end_time)
      datePicker.value = [form.value.start_time, form.value.end_time]
    }
  }
  getList(false)
  searchDeviceList('NIO', form.value.device_id.join(','))
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.powerswap-step-container {
  .content-box {
    // display: grid;
    // grid-template-columns: repeat(5, minmax(0px, 1fr));
    display: flex;
    flex-wrap: wrap;
    row-gap: 15px;
    .circle-index {
      width: 32px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      background: #00bebe;
      color: #fff;
      border-radius: 50%;
      margin-bottom: 8px;
    }
  }
  :deep(.el-dialog__body) {
    padding-top: 15px;
  }
}
</style>
