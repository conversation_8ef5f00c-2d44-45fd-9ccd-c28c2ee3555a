<template>
  <div class="alarm-list-container">
    <el-form :model="form" ref="ruleFormRef" inline class="margin_b-2">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_time')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-date-picker v-model="datePicker" type="datetimerange" class="width-full" @change="handleDateChange" unlink-panels :shortcuts="getTodayShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledTodayDate" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_device_search')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-select v-model="form.device_id" filterable remote clearable :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_alarm_search')" class="width-full normal-form" style="margin-bottom: 16px">
            <el-select v-model="form.alarm_id" clearable filterable remote class="width-full" :placeholder="$t('stuckAnalysis.pp_alarm_tip')" :remote-method="remoteMethod" :loading="remoteLoading">
              <el-option v-for="item in pointOptions" :key="item.value" :value="item.value" :label="item.value + '-' + item.label">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('stuckAnalysis.pp_alarm_level')" class="width-full normal-form">
            <el-select v-model="form.alarm_level" clearable filterable class="width-full" :placeholder="$t('common.pp_please_select')">
              <el-option v-for="item in alarmLevelOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <div class="flex-box flex_j_c-space-between">
            <div>
              <el-button type="primary" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
              <el-button class="reset-button" style="margin-left: 8px" @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
            </div>
            <el-button @click="batchImportVisible = true" class="reset-button width-108 height-32" style="padding: 5px 16px">
              <UploadIcon />
              <span class="margin_l-4">{{ $t('common.pp_import') }}</span>
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-table :data="list" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="loading">
      <el-table-column prop="alarm_id_description" :label="$t('stuckAnalysis.pp_alarm_name')" min-width="240" fixed show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.alarm_id_description ? row.alarm_id_description : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="alarm_id" :label="$t('stuckAnalysis.pp_alarm_id')" min-width="90" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="operation-button" @click="handleViewAlarm(row.alarm_id)">{{ row.alarm_id }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="alarm_type" :label="$t('stuckAnalysis.pp_alarm_type')" min-width="90" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ $t(alarmTypeMap[row.alarm_type]) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="alarm_level" :label="$t('stuckAnalysis.pp_alarm_level')" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="alarm-level-container" v-if="alarmLevelMap[row.alarm_level]" :style="{ background: alarmLevelMap[row.alarm_level].background, color: alarmLevelMap[row.alarm_level].color }">
            <span>{{ $t(alarmLevelMap[row.alarm_level].name) }}</span>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="stuck_times" :label="$t('stuckAnalysis.pp_stuck_times')" min-width="80" show-overflow-tooltip />
      <el-table-column prop="total_times" :label="$t('stuckAnalysis.pp_total_times')" min-width="80" show-overflow-tooltip />
      <el-table-column prop="alarm_rate" :label="$t('stuckAnalysis.pp_alarm_stuck')" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ getFourDecimalPlaces(row.alarm_rate) + '‱' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="owner_software" :label="$t('stuckAnalysis.pp_software_owner')" :width="softOwnerList.length > 0 ? flexColumnWidth(softOwnerList, 90, 7) : 100" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box flex_a_i-center">
            <span v-if="row.owner_software == '/' || !row.owner_software">{{ row.owner_software }}</span>
            <div class="flex-box flex_a_i-center avatar-container" v-else>
              <el-avatar size="small" :src="avatarList[row.owner_software]" />
              <span class="margin_l-8">{{ row.owner_software }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="owner_hardware" :label="$t('stuckAnalysis.pp_hardware_owner')" :width="hardOwnerList.length > 0 ? flexColumnWidth(hardOwnerList, 80, 7) : 100" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box flex_a_i-center">
            <span v-if="row.owner_hardware == '/' || !row.owner_hardware">{{ row.owner_hardware }}</span>
            <div class="flex-box flex_a_i-center avatar-container" v-else>
              <el-avatar size="small" :src="avatarList[row.owner_hardware]" />
              <span class="margin_l-8">{{ row.owner_hardware }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="margin_t-20 flex-box flex_j_c-flex-end">
      <Page :page="pages" @change="handlePageChange" />
    </div>

    <AlarmDialog v-model:alarmVisible="alarmVisible" :alarmId="alarmId" :project="project" v-if="alarmVisible" />

    <!-- 批量导入设备 -->
    <UploadDialog v-model:batchImportVisible="batchImportVisible" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, PropType } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { removeNullKeys, clearJson, getFourDecimalPlaces, flexColumnWidth } from '~/utils';
import { page } from '~/constvars/page';
import { ElMessage } from 'element-plus';
import { debounce } from 'lodash-es';
import { apiGetDevices } from '~/apis/home';
import { apiGetAlarmIdList, apiGetAlarmList } from '~/apis/stuck-analysis';
import { initTodayStartTime, initTodayEndTime, getTodayShortcuts, getDisabledTodayDate, alarmTypeMap, alarmLevelMap, alarmLevelOptions } from './constant';
import AlarmDialog from './alarm-dialog.vue';
import UploadDialog from './upload-dialog.vue';
import UploadIcon from '~/assets/svg/upload.vue';
import _ from 'lodash';

interface DeviceObject {
  device_id: string;
  description: string;
}

const props = defineProps({
  project: {
    type: String,
    default: 'PUS4',
  },
  deviceList: {
    type: Array as PropType<DeviceObject[]>,
    required: true,
  },
});

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const ruleFormRef = ref();
const loading = ref(false);
const remoteLoading = ref(false);
const alarmVisible = ref(false);
const batchImportVisible = ref(false);
const remoteDeviceLoading = ref(false);
const alarmId = ref('' as any);
const softOwnerList = ref([] as any);
const hardOwnerList = ref([] as any);
const pointOptions = ref([] as any);
const deviceOptions = ref([] as any);
const datePicker = ref([initTodayStartTime, initTodayEndTime] as any);
const list = ref([] as any);
const form = ref({
  start_time: initTodayStartTime,
  end_time: initTodayEndTime,
  device_id: [] as any,
  alarm_id: '',
  alarm_level: '' as number | string,
});
const searchForm = ref({} as any);
const avatarList = ref({} as any);
const pages = ref(_.cloneDeep(page));

const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return;
  searchDeviceList(query);
};
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true;
    const params = { project: props.project, name: val, device_ids: deviceIds, limit: 30 };
    const res = await apiGetDevices(params);
    remoteDeviceLoading.value = false;
    deviceOptions.value = res.data;
  }
}, 500);

/**
 * @description: 确认批量导入设备
 * @param {*} list
 * @return {*}
 */
const handleConfirmBatchImportDevice = (list: any) => {
  const deviceIds = props.deviceList.map((item: any) => item.device_id);
  const importDevice = list.filter((item: any) => deviceIds.includes(item));
  if (importDevice.length > 100) {
    ElMessage.error(t('stuckAnalysis.pp_exceed_100'));
  } else if (importDevice.length == 0) {
    ElMessage.error(t('stuckAnalysis.pp_number_0'));
  } else {
    form.value.device_id = [...new Set([...form.value.device_id, ...importDevice])];
    ElMessage.success(`${t('stuckAnalysis.pp_import_success')} ${importDevice.length} ${t('stuckAnalysis.pp_device_number')}`);
    batchImportVisible.value = false;
    searchDeviceList('NIO', form.value.device_id.join(','));
  }
};

/**
 * @description: 查看单一告警
 * @param {*} id
 * @return {*}
 */
const handleViewAlarm = (id: any) => {
  alarmId.value = _.cloneDeep(id);
  alarmVisible.value = true;
};

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList();
};

/**
 * @description: 远程搜索告警描述点
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchPointList(query);
};
const searchPointList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true;
    const params = { description: val };
    const res = await apiGetAlarmIdList(params, props.project);
    pointOptions.value = [];
    Object.keys(res.data).forEach((key: any) => {
      pointOptions.value.push({
        label: res.data[key],
        value: key,
      });
    });
    remoteLoading.value = false;
  }
}, 500);

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime();
  form.value.end_time = new Date(val[1]).getTime();
};

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  sessionStorage.setItem('stuck-form', JSON.stringify({ device_id: searchForm.value.device_id }));
  let formData = _.cloneDeep(searchForm.value) as any;
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.device_id = formData.device_id.join(',');
  removeNullKeys(formData);
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: route.query.tab, project: props.project, ...formData },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetAlarmList(formData, props.project);
    loading.value = false;
    if (res.err_code) {
      ElMessage.error(res.message);
    } else {
      list.value = res.data;
      avatarList.value = res.avatar || {};
      pages.value.total = res.total;
      softOwnerList.value = [...new Set(list.value.filter((item: any) => item.owner_software).map((item: any) => item.owner_software))];
      hardOwnerList.value = [...new Set(list.value.filter((item: any) => item.owner_hardware).map((item: any) => item.owner_hardware))];
    }
  } catch (error) {
    loading.value = false;
  }
};

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value);
  datePicker.value = [initTodayStartTime, initTodayEndTime];
  form.value.start_time = datePicker.value[0];
  form.value.end_time = datePicker.value[1];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  searchForm.value = _.cloneDeep(form.value);
  getList();
};

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query;
  const deviceCache = JSON.parse(sessionStorage.getItem('stuck-form') as any);
  if (initParams.tab == 'order-dimension') {
    form.value.device_id = deviceCache ? deviceCache.device_id : [];
  } else {
    form.value.device_id = !!initParams.device_id ? initParams.device_id.split(',') : deviceCache ? deviceCache.device_id : [];
  }
  if (initParams.tab == 'alarm-list') {
    if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
      form.value.start_time = Number(initParams.start_time);
      form.value.end_time = Number(initParams.end_time);
      datePicker.value = [form.value.start_time, form.value.end_time];
    }
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
    form.value.alarm_id = !!initParams.alarm_id ? initParams.alarm_id : '';
    form.value.alarm_level = !!initParams.alarm_level ? initParams.alarm_level : '';
  }
  searchForm.value = _.cloneDeep(form.value);
  getList(false);
  searchDeviceList('NIO', form.value.device_id.join(','));
};

watch(
  () => props.project,
  (newVal, oldVal) => {
    pages.value.current = 1;
    form.value.device_id = [];
    form.value.alarm_id = '';
    searchForm.value = _.cloneDeep(form.value);
    getList();
    searchDeviceList('NIO');
  }
);

onBeforeMount(() => {
  initWeb();
});
</script>

<style lang="scss" scoped>
.alarm-list-container {
  :deep(.el-form-item__content) {
    flex-wrap: nowrap;
  }
}
</style>
