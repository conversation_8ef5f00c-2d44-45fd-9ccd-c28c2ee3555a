<template>
  <div :class="['stuck-analysis-container', { 'gray-background': activeTab == 'powerswap-step' || activeTab == 'dashboard-overview' }]">
    <div class="header-section">
      <div class="font-size-20 font-weight-bold margin_b-12" style="color: #1f1f1f">{{ $t('menu.pp_stuck_analysis') }}</div>
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane :label="$t('stuckAnalysis.pp_dashboard_overview')" name="dashboard-overview"></el-tab-pane>
        <el-tab-pane :label="$t('stuckAnalysis.pp_order_dimension')" name="order-dimension"></el-tab-pane>
        <el-tab-pane :label="$t('stuckAnalysis.pp_alarm_detail')" name="alarm-detail"></el-tab-pane>
        <el-tab-pane :label="$t('stuckAnalysis.pp_alarm_list')" name="alarm-list"></el-tab-pane>
        <el-tab-pane :label="$t('stuckAnalysis.pp_powerswap_step')" name="powerswap-step"></el-tab-pane>
      </el-tabs>
      <el-radio-group v-model="project" @change="handleChangeProject" :class="['radio-group', { 'gray-radio-group': activeTab == 'powerswap-step' || activeTab == 'dashboard-overview' }]" v-if="activeTab != 'order-dimension'">
        <el-radio label="PowerSwap2">{{ $t('stuckAnalysis.pp_swap_station2') }}</el-radio>
        <el-radio label="PUS3">{{ $t('stuckAnalysis.pp_swap_station3') }}</el-radio>
        <el-radio label="PUS4">{{ $t('stuckAnalysis.pp_swap_station4') }}</el-radio>
      </el-radio-group>
    </div>

    <div class="content-section">
      <DashboardOverview :project="project" :deviceList="deviceList" v-if="activeTab == 'dashboard-overview'" />
      <OrderDimension :allDeviceList="allDeviceList" v-else-if="activeTab == 'order-dimension'" />
      <AlarmDetail :project="project" :deviceList="deviceList" v-else-if="activeTab == 'alarm-detail'" />
      <AlarmList :project="project" :deviceList="deviceList" v-else-if="activeTab == 'alarm-list'" />
      <PowerswapStep :project="project" :deviceList="deviceList" v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { apiGetDevices } from '~/apis/home'
import { ElMessage } from 'element-plus'
import DashboardOverview from './components/dashboard-overview.vue'
import OrderDimension from './components/order-dimension.vue'
import AlarmDetail from './components/alarm-detail.vue'
import AlarmList from './components/alarm-list.vue'
import PowerswapStep from './components/powerswap-step.vue'

const route = useRoute()
const router = useRouter()
const activeTab = ref('' as any)
const project = ref('PUS4' as any)
const deviceList = ref([] as any)
const allDeviceList = ref([] as any)

/**
 * @description: 切换Tab
 * @return {*}
 */
const handleTabChange = () => {
  const queryObj = activeTab.value == 'order-dimension' ? { tab: activeTab.value } : { tab: activeTab.value, project: project.value }
  router.push({
    path: location.pathname,
    query: queryObj
  })
}

/**
 * @description: 获取站点列表
 * @return {*}
 */
const getDevice = async () => {
  const params = { project: project.value }
  const res = await apiGetDevices(params)
  deviceList.value = res.data
}

/**
 * @description: 获取二、三、四代站的所有站点列表
 * @return {*}
 */
const getAllDevice = async () => {
  const params = { project: 'PowerSwap2,PUS3,PUS4' }
  const res = await apiGetDevices(params)
  allDeviceList.value = res.data
}

/**
 * @description: 切换二代站 / 三代站 / 四代站
 * @param {*} val
 * @return {*}
 */
const handleChangeProject = () => {
  getDevice()
}

onBeforeMount(() => {
  activeTab.value = route.query.tab || 'dashboard-overview'
  if (activeTab.value !== 'order-dimension' && route.query.project) project.value = route.query.project
  getDevice()
  getAllDevice()
})
</script>

<style lang="scss" scoped>
.stuck-analysis-container,
.gray-background {
  font-family: 'Blue Sky Standard';
  padding: 24px;
  background: linear-gradient(180deg, #e2f9f9 -6.51%, rgba(255, 255, 255, 0.5) 12.89%);
  .header-section {
    position: relative;
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 15px;
      font-weight: 400;
      color: #595959;
    }
    :deep(.radio-group) {
      position: absolute;
      right: 0;
      bottom: 3px;
    }
    :deep(.gray-radio-group) {
      right: 24px;
    }
  }
  .content-section {
    padding-top: 20px;
    :deep(.alarm-level-container) {
      width: 84px;
      font-size: 13px;
      display: flex;
      justify-content: center;
      border-radius: 2px;
      padding: 1px 4px;
    }
    :deep(.avatar-container) {
      border-radius: 16px;
      background: #e5f9f9;
      padding: 2px 12px 2px 4px;
    }
    :deep(.duty-status) {
      padding: 1px 6px;
      font-size: 13px;
      border-radius: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    :deep(.operation-button) {
      color: #01a0ac;
      cursor: pointer;
      &:hover {
        color: #00bebe;
      }
    }
    :deep(.el-table td.el-table__cell div) {
      color: #262626;
    }
    :deep(.el-dialog__title) {
      color: #1f1f1f;
    }
    :deep(.search-button) {
      color: #fff;
    }
    :deep(.reset-button) {
      color: #01a0ac;
      border: 1px solid #00bebe;
      &:hover {
        color: #01a0ac !important;
        background-color: #e5f9f9 !important;
        border: 1px solid #00bebe;
      }
      &:focus {
        color: #01a0ac !important;
        background-color: #9cebe7 !important;
        border: 1px solid #00bebe;
      }
    }
    :deep(.el-button--primary:not(:disabled):hover) {
      background: #21ccc6 !important;
      color: #fff !important;
      border-color: #21ccc6;
    }
    :deep(.el-button--primary:focus) {
      background: #009499 !important;
      color: #fff !important;
      border-color: #009499;
    }
    :deep(.cancel-button) {
      border: none;
    }
    :deep(.text-button) {
      color: #595959;
      border: none;
      &:hover {
        color: #595959 !important;
        background-color: #f5f5f5 !important;
      }
      &:focus {
        color: #595959 !important;
        background-color: #f0f0f0 !important;
      }
    }
    :deep(.close-button) {
      color: #262626;
      border: 1px solid #bfbfbf;
      &:hover {
        color: #262626 !important;
        background-color: #f5f5f5 !important;
        border: 1px solid #bfbfbf;
      }
      &:focus {
        color: #262626 !important;
        background-color: #f0f0f0 !important;
        border: 1px solid #bfbfbf;
      }
    }
    :deep(.el-button) {
      font-weight: 420;
    }
    :deep(.normal-form) {
      margin-right: 0;
    }
    :deep(.el-form-item__label) {
      color: #595959;
    }
    :deep(.el-dialog__headerbtn .el-dialog__close) {
      font-size: 20px;
    }
    :deep(.el-select .el-select__tags .el-tag--info) {
      color: #262626;
    }
    :deep(.el-tag.el-tag--info) {
      color: #262626;
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
  }
}
.gray-background {
  padding: 0;
  background: none;
  display: flex;
  flex-direction: column;
  .header-section {
    padding: 24px;
    padding-bottom: 0;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
  }
  .content-section {
    flex: 1;
    padding: 20px 24px 24px;
    background: #f8f8f8;
  }
}
</style>
