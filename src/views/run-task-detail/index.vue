<template>
  <div class="run-task-detail-container">
    <div class="header-container">
      <Title :simulation_id="simulation_id"></Title>
      <div class="task-tabs">
        <el-tabs v-model="taskActive" @tab-change="handleTabChange">
          <el-tab-pane label="仿真报告" name="1"></el-tab-pane>
          <el-tab-pane label="配置详情" name="2"></el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div class="content-container">
      <ReportChart v-if="taskActive == '1' && simulation_report" :reportDetail="simulation_report" />
      <Detail v-if="taskActive == '2' && simulation_config" :configDetail="simulation_config" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onBeforeMount, computed } from 'vue'
import { apiGetSimulation } from '~/apis/run-list'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { options, options1, options2 } from './chart-mock'
import { ElMessage } from 'element-plus'
import Detail from './component/task/config-detail.vue'
import ReportChart from './component/task/report.vue'
import Title from './component/task/title.vue'
import * as echarts from 'echarts'
import _ from 'lodash'

const route = useRoute()
const router = useRouter()
const store = useStore()
const taskActive = ref('1' as any)
const simulation_id = ref()
const echartsArr = ref([]) as any
const simulation_config = ref(null) as any
const simulation_report = ref(null) as any
const isCollapse = computed(() => store.state.menus.collapse)

const handleTabChange = (val: any) => {
  router.push({
    path: location.pathname,
    query: { ...route.query, tab: val }
  })
  if (val == '1') {
    setCharts()
  } else {
    echartsArr.value.map((item: any) => {
      item && item.dispose()
    })
    echartsArr.value = []
  }
}

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  echartsArr.value.push(myChart)
  window.addEventListener('resize', () => {
    echartsArr.value.map((item: any) => item.resize())
  })
}

const getInteger = (num: number) => {
  return Number.isInteger(num * 100) ? num : num.toFixed(2)
}

const setCharts = () => {
  const user_report = simulation_report.value.user_report
  const swap_station_battery_report = simulation_report.value.swap_station_battery_report
  const device_report = simulation_report.value.device_report

  // 用户平均值
  user_report.x = user_report.graph_data.map((item: any) => item.hour)
  user_report.y = user_report.graph_data.map((item: any) => getInteger(item.avg_queue_time))
  user_report.z = user_report.graph_data.map((item: any) => item.service_num)
  options2.xAxis.data = user_report.x
  options2.series[0].data = user_report.z
  options2.series[1].data = user_report.y

  //站内电池充电
  swap_station_battery_report.x = swap_station_battery_report.graph_data.map((item: any) => item.hour)
  swap_station_battery_report.y = swap_station_battery_report.graph_data.map((item: any) => getInteger(item.charge_cost))
  swap_station_battery_report.z = swap_station_battery_report.graph_data.map((item: any) => item.charge_num)
  options1.xAxis.data = swap_station_battery_report.x
  options1.series[0].data = swap_station_battery_report.z
  options1.series[1].data = swap_station_battery_report.y

  // 设备性能
  device_report.x = device_report.graph_data.map((item: any) => item.hour)
  device_report.y = device_report.graph_data.map((item: any) => getInteger(item.capacity_factor * 100))
  device_report.z = device_report.graph_data.map((item: any) => getInteger(item.module_rate * 100))
  options.xAxis.data = device_report.x
  options.series[0].data = device_report.y
  options.series[1].data = device_report.z
  nextTick(() => {
    if (device_report.graph_data.length > 0) echartRender('lineChart3', options) //设备性能
    if (swap_station_battery_report.graph_data.length > 0) echartRender('lineChart2', options1) //站内电池充电
    if (user_report.graph_data.length > 0) echartRender('lineChart', options2) //用户体验
  })
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 获取数据
 * @return {*}
 */
const getConfigDetail = async () => {
  const id = route.query.simulation_id
  simulation_id.value = route.query.simulation_id || ''
  taskActive.value = route.query.tab || '1'
  if (!id) return
  const res = await apiGetSimulation(id)
  const { err_code, data } = res
  if (!err_code) {
    simulation_config.value = data.simulation_config
    simulation_report.value = data.simulation_report
    if (taskActive.value == '1') setCharts()
  } else {
    ElMessage.error(res.message)
  }
}

onBeforeMount(() => {
  getConfigDetail()
})
</script>

<style lang="scss" scoped>
.run-task-detail-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    padding: 24px 24px 0;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
  }
  .content-container {
    position: relative;
    padding: 0 24px 24px;
    background: #f8f8f8;
  }
  .task-tabs {
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__header) {
      margin: 0 0 20px;
    }
    :deep(.el-tabs__item) {
      font-size: 15px;
      font-weight: 400;
      color: #595959;
    }
  }

  :deep(.el-card.is-always-shadow) {
    border-color: rgba(220, 242, 243, 1);
  }
  :deep(.list-card:hover) {
    border-color: #00bebe;
  }
  :deep(.list-card .el-card__body) {
    padding: 20px;
  }
}
</style>
