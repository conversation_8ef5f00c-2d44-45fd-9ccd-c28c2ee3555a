<template>
  <div
    @click="openUrl(url)"
    class="flex-box flex_a_i-center font-size-14 chart-upload-csv"
  >
    <DownloadChart />&nbsp;{{ text }}
  </div>
</template>
<script setup lang="ts">
import DownloadChart from '../icon/download-chart.vue';
defineProps({
  url: {
    type: String,
    default: '',
  },
  text: {
    type: String,
    default: '用户级.csv',
  },
});

const openUrl = (url: any) => {
  console.log('openUrl', openUrl);
  window.open(url, '_bank');
};
</script>
<style lang="scss" scoped>
.chart-upload-csv {
  color: #01a0ac;
  position: absolute;
  cursor: pointer;
  right: 20px;
  top: 20px;
  z-index: 100;
}
</style>
