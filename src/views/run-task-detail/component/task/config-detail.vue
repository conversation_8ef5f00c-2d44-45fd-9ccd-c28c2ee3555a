<template>
  <div class="config-detail-contianer">
    <div v-if="configDetail" class="list-card">
      <div class="title margin_b-4">换电站配置</div>
      <el-row v-if="configDetail.electricity_price_model == 'one_price'">
        <el-col :span="12">
          <div class="station-config-item">
            <div class="title-span">仿真时间范围</div>
            <div>
              {{ formatTime(configDetail.start_ts) }}
              <span class="arrow-icon">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.6422 9.31563L11.0797 6.06563C11.033 6.00629 10.9734 5.95832 10.9055 5.92531C10.8375 5.89229 10.763 5.87509 10.6875 5.875H9.675C9.57031 5.875 9.5125 5.99531 9.57656 6.07812L11.8313 8.9375H2.375C2.30625 8.9375 2.25 8.99375 2.25 9.0625V10C2.25 10.0687 2.30625 10.125 2.375 10.125H13.2484C13.6672 10.125 13.9 9.64375 13.6422 9.31563Z"
                    fill="#BFBFBF"
                  />
                </svg>
              </span>
              {{ formatTime(configDetail.end_ts) }}
            </div>
          </div>
          <div class="station-config-item">
            <div class="title-span">电价模式</div>
            <div style="padding-right: 24px; color: #595959">一口价</div>
            <div>{{ configDetail.electricity_detail_list[0].price }}元</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="station-config-item">
            <div class="title-span">设备整站容量</div>
            <div class="item-span">
              <span>{{ configDetail.station_capacity }}</span>
              <span style="padding-left: 16px; color: #595959">线1容量</span>
              <span>{{ configDetail.circuit_1_capacity }}</span>
              <span style="padding-left: 16px; color: #595959">线2容量</span>
              <span>{{ configDetail.circuit_2_capacity }}</span>
            </div>
          </div>
          <div class="station-config-item" v-if="configDetail.is_real_device">
            <div class="title-span">基于换电站</div>
            <div>{{ configDetail.description || $t('common.pp_unnamed_device') }}</div>
          </div>
          <div class="station-config-item" v-else>
            <div class="title-span">基于换电站</div>
            <div>-</div>
          </div>
        </el-col>
      </el-row>
      <el-row v-else>
        <el-col :span="12">
          <div class="station-config-item">
            <div class="title-span">仿真时间范围</div>
            <div>
              {{ formatTime(configDetail.start_ts) }}
              <span class="arrow-icon">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.6422 9.31563L11.0797 6.06563C11.033 6.00629 10.9734 5.95832 10.9055 5.92531C10.8375 5.89229 10.763 5.87509 10.6875 5.875H9.675C9.57031 5.875 9.5125 5.99531 9.57656 6.07812L11.8313 8.9375H2.375C2.30625 8.9375 2.25 8.99375 2.25 9.0625V10C2.25 10.0687 2.30625 10.125 2.375 10.125H13.2484C13.6672 10.125 13.9 9.64375 13.6422 9.31563Z"
                    fill="#BFBFBF"
                  />
                </svg>
              </span>
              {{ formatTime(configDetail.end_ts) }}
            </div>
          </div>

          <div class="station-config-item">
            <div class="title-span">设备整站容量</div>
            <div class="item-span">
              <span>{{ configDetail.station_capacity }}</span>
              <span style="color: #595959">线1容量</span>
              <span>{{ configDetail.circuit_1_capacity }}</span>
              <span style="color: #595959">线2容量</span>
              <span>{{ configDetail.circuit_2_capacity }}</span>
            </div>
          </div>

          <div class="station-config-item" v-if="configDetail.is_real_device">
            <div class="title-span">基于换电站</div>
            <div>{{ configDetail.description || $t('common.pp_unnamed_device') }}</div>
          </div>
          <div class="station-config-item" v-else>
            <div class="title-span">基于换电站</div>
            <div>-</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="station-config-item">
            <div class="title-span">电价模式</div>
            <div>分时电价</div>
          </div>
          <div class="station-config-item" style="align-items: flex-start">
            <div class="title-span">电价详情</div>
            <div class="price-list">
              <div
                class="price-detail"
                v-for="(item, index) in configDetail.electricity_detail_list"
              >
                <span>{{ item.price }} 元</span>
                <span style="color: #595959">{{ item.start }}</span>
                <span style="color: #595959">至</span>
                <span style="color: #595959">{{ item.end }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div v-if="configDetail" class="list-card">
      <div class="title margin_b-4">策略配置</div>
      <el-row v-if="configDetail.battery_rest_switch.switch_value === 0">
        <el-col :span="12">
          <div class="station-config-item">
            <div class="title-span">换电运营时段</div>
            <div>{{ '0' + configDetail.operation_start_hour + ':00' }} — {{ configDetail.operation_end_hour + ':00' }}</div>
          </div>
          <div class="station-config-item">
            <div class="title-span">非满电换电开关</div>
            <div class="margin_r-32">{{ configDetail.notfully_swap_switch.switch_value ? '开' : '关' }}</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value" class="margin_r-10" style="color: #595959">电池SOC下限</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value" class="margin_r-16">{{ configDetail.notfully_swap_switch.soc_lower_limit }}%</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value" class="margin_r-10" style="color: #595959">电池SOC上限</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value">{{ configDetail.notfully_swap_switch.soc_upper_limit }}%</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="station-config-item">
            <div class="title-span">EPS策略开关</div>
            <div class="flex-box flex_a_i-center">
              <div class="margin_r-10">{{ configDetail.cms_switch ? '开' : '关' }}</div>
              <el-tooltip placement="top" v-if="configDetail.eps_params">
                <template #content>
                  <div class="flex-box flex_d-column gap_12" style="color: #fff">
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">EPS运营模式</span>
                      <span>{{ getEpsModelText(configDetail.eps_params.eps_model_mode) }}</span>
                    </div>
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">高电价时段是否多充</span>
                      <span>{{ configDetail.eps_params.eps_high_price_peak_shift_enable ? '进行多充' : '不进行多充' }}</span>
                    </div>
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">多充功率限制生效时段</span>
                      <span>{{ configDetail.eps_params.eps_peak_shift_power_effective_time || '-' }}</span>
                    </div>
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">多充功率限制值</span>
                      <span>{{ configDetail.eps_params.eps_peak_shift_power_limit || '-' }}</span>
                    </div>
                  </div>
                </template>
                <HelpIcon />
              </el-tooltip>
            </div>
          </div>
          <div class="station-config-item" v-if="configDetail.project == 'PUS3'">
            <div class="title-span">电池倒仓开关</div>
            <div>{{ configDetail.battery_exchange_switch ? '开' : '关' }}</div>
          </div>
          <div class="station-config-item" v-else>
            <div class="title-span">电池静置开关</div>
            <div>关</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="station-config-item">
            <div class="title-span">静音模式开关</div>
            <div>{{ configDetail.silent_mode_switch ? '开' : '关' }}</div>
          </div>
          <div class="station-config-item" v-if="configDetail.project == 'PUS3'">
            <div class="title-span">电池静置开关</div>
            <div>关</div>
          </div>
        </el-col>
      </el-row>

      <el-row v-else>
        <el-col :span="12">
          <div class="station-config-item">
            <div class="title-span">换电运营时段</div>
            <div>{{ '0' + configDetail.operation_start_hour + ':00' }} — {{ configDetail.operation_end_hour + ':00' }}</div>
          </div>
          <div class="station-config-item">
            <div class="title-span">EPS策略开关</div>
            <div class="flex-box flex_a_i-center">
              <div class="margin_r-10">{{ configDetail.cms_switch ? '开' : '关' }}</div>
              <el-tooltip placement="top" v-if="configDetail.eps_params">
                <template #content>
                  <div class="flex-box flex_d-column gap_12" style="color: #fff">
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">EPS运营模式</span>
                      <span>{{ getEpsModelText(configDetail.eps_params.eps_model_mode) }}</span>
                    </div>
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">高电价时段是否多充</span>
                      <span>{{ configDetail.eps_params.eps_high_price_peak_shift_enable ? '进行多充' : '不进行多充' }}</span>
                    </div>
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">多充功率限制生效时段</span>
                      <span>{{ configDetail.eps_params.eps_peak_shift_power_effective_time || '-' }}</span>
                    </div>
                    <div>
                      <span class="margin_r-10" style="color: #bebebe">多充功率限制值</span>
                      <span>{{ configDetail.eps_params.eps_peak_shift_power_limit || '-' }}</span>
                    </div>
                  </div>
                </template>
                <HelpIcon />
              </el-tooltip>
            </div>
          </div>
          <div class="station-config-item" style="align-items: flex-start">
            <div class="title-span">电池静置开关</div>
            <div class="margin_r-24">开</div>
            <div class="flex-box flex_d-column gap_12">
              <div>
                <span class="color-59 margin_r-10">静置阶段电流</span>
                <span class="color-26 margin_r-16">{{ configDetail.battery_rest_switch.default_rest_current }}</span>
                <span class="color-59 margin_r-10">充电缓起时长</span>
                <span class="color-26">{{ configDetail.battery_rest_switch.default_hanging_duration }}</span>
              </div>
              <div>
                <span class="color-59 margin_r-10">充电缓起步长</span>
                <span class="color-26 margin_r-16">{{ configDetail.battery_rest_switch.default_hanging_step }}</span>
                <span class="color-59 margin_r-10">充电缓起最大电流</span>
                <span class="color-26">{{ configDetail.battery_rest_switch.default_hanging_current_max }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="station-config-item">
            <div class="title-span">非满电换电开关</div>
            <div class="margin_r-32">{{ configDetail.notfully_swap_switch.switch_value ? '开' : '关' }}</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value" class="margin_r-10" style="color: #595959">电池SOC下限</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value" class="margin_r-16">{{ configDetail.notfully_swap_switch.soc_lower_limit }}%</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value" class="margin_r-10" style="color: #595959">电池SOC上限</div>
            <div v-if="configDetail.notfully_swap_switch.switch_value">{{ configDetail.notfully_swap_switch.soc_upper_limit }}%</div>
          </div>
          <div class="station-config-item" v-if="configDetail.project == 'PUS3'">
            <div class="title-span">电池倒仓开关</div>
            <div>{{ configDetail.battery_exchange_switch ? '开' : '关' }}</div>
          </div>
          <div class="station-config-item">
            <div class="title-span">静音模式开关</div>
            <div>{{ configDetail.silent_mode_switch ? '开' : '关' }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div v-if="configDetail" class="list-card">
      <div class="title">电池配置</div>
      <el-row :gutter="16" class="margin_t-8">
        <el-col :span="12" v-for="item in 2">
          <el-table
            :data="item == 1 ? tableLeft : tableRight"
            :header-cell-style="{
              background: '#E5F9F9',
              fontSize: '14px',
              color: '#262626',
            }"
            :cell-style="{ color: '#262626' }"
          >
            <el-table-column
              prop="slot_id"
              label="仓位号"
              width="70"
              show-overflow-tooltip
              fixed
              align="center"
            />
            <el-table-column
              prop="battery_soc"
              label="SOC"
              min-width="60"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_soc) ? '-' : row.battery_soc + '%'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="pack_max_temperature"
              label="电池温度"
              min-width="80"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.pack_max_temperature)
                    ? '-'
                    : row.pack_max_temperature + '°C'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="battery_ownership"
              label="电池产权"
              min-width="80"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_ownership)
                    ? '-'
                    : row.battery_ownership
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="battery_rated_kwh"
              label="电池度数"
              min-width="80"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_rated_kwh)
                    ? '-'
                    : row.battery_rated_kwh + 'kWh'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="charging_stop_soc"
              label="截止SOC"
              min-width="90"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.charging_stop_soc)
                    ? '-'
                    : row.charging_stop_soc + '%'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="battery_id"
              label="定位标签"
              min-width="100"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_id) ? '-' : row.battery_id
                }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>

    <div v-if="configDetail" class="list-card">
      <div class="title">订单配置</div>
      <div class="battery-container">
        <div class="battery-item">
          <div class="line-height-18">50kWh <span>电池单量</span></div>
          <div class="battery-num">{{ order_50_kwh }} <span>单</span></div>
        </div>
        <div class="battery-item" v-if="configDetail.project !== 'PowerSwap2'">
          <div class="line-height-18">60kWh <span>电池单量</span></div>
          <div class="battery-num">{{ order_60_kwh }} <span>单</span></div>
        </div>
        <div class="battery-item">
          <div class="line-height-18">75kWh <span>电池单量</span></div>
          <div class="battery-num">{{ order_75_kwh }} <span>单</span></div>
        </div>
        <div class="battery-item" v-if="configDetail.project !== 'PowerSwap2'">
          <div class="line-height-18">85kWh <span>电池单量</span></div>
          <div class="battery-num">{{ order_85_kwh }} <span>单</span></div>
        </div>
        <div class="battery-item">
          <div class="line-height-18">100kWh <span>电池单量</span></div>
          <div class="battery-num">{{ order_100_kwh }} <span>单</span></div>
        </div>
        <div class="battery-item" v-if="configDetail.project !== 'PowerSwap2'">
          <div class="line-height-18">102kWh <span>电池单量</span></div>
          <div class="battery-num">{{ order_102_kwh }} <span>单</span></div>
        </div>
        <div class="battery-item">
          <div class="line-height-18">150kWh <span>电池单量</span></div>
          <div class="battery-num">{{ order_150_kwh }} <span>单</span></div>
        </div>
      </div>
      <el-row class="margin_t-12">
        <el-col :span="24">
          <el-table
            :data="
              psosTable.slice(
                (pages.current - 1) * pages.size,
                pages.current * pages.size
              )
            "
            :header-cell-style="{
              background: '#E5F9F9',
              fontSize: '14px',
              color: '#262626',
            }"
            :cell-style="{ color: '#262626' }"
          >
            <el-table-column
              prop="user_arrival_time"
              label="用户到达时间"
              min-width="150"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{ formatTime(row.user_arrival_time) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="battery_rest_label"
              label="是否静置"
              min-width="90"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_rest_label)
                    ? '-'
                    : row.battery_rest_label == 1 ? '否'
                    : '是'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="battery_rated_kwh"
              label="进站电池度数"
              min-width="100"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_rated_kwh)
                    ? '-'
                    : row.battery_rated_kwh + 'kWh'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="battery_soc"
              label="进站电池SOC"
              min-width="100"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_soc) ? '-' : row.battery_soc + '%'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="pack_max_temperature"
              label="进站电池温度"
              min-width="100"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.pack_max_temperature)
                    ? '-'
                    : row.pack_max_temperature + '°C'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="target_battery_rated_kwh"
              label="目标度数"
              min-width="90"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.target_battery_rated_kwh)
                    ? '-'
                    : row.target_battery_rated_kwh + 'kWh'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="user_ownership"
              label="车辆产权"
              min-width="90"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.user_ownership) ? '-' : row.user_ownership
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="battery_ownership"
              label="电池产权"
              min-width="90"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.battery_ownership) ? '-' : row.battery_ownership
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="service_id"
              label="订单ID（标签）"
              min-width="120"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{
                  isEmptyData(row.service_id) ? '-' : row.service_id
                }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box margin_t-16">
            <Page :page="pages" @change="handlePageChange" />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, computed, watch } from 'vue';
import HelpIcon from '~/assets/svg/help.vue';
import { page } from '~/constvars/page';
import _ from 'lodash';
import { formatTime } from '~/utils';
const pages = ref(_.cloneDeep(page));
const tableLeft = ref([]);
const tableRight = ref([]);
const psosTable = ref([]);
const order_50_kwh = computed(
  () =>
    psosTable.value.filter((item: any) => item.target_battery_rated_kwh == 50)
      .length
);
const order_60_kwh = computed(
  () =>
    psosTable.value.filter((item: any) => item.target_battery_rated_kwh == 60)
      .length
);
const order_75_kwh = computed(
  () =>
    psosTable.value.filter((item: any) => item.target_battery_rated_kwh == 75)
      .length
);
const order_85_kwh = computed(
  () =>
    psosTable.value.filter((item: any) => item.target_battery_rated_kwh == 85)
      .length
);
const order_100_kwh = computed(
  () =>
    psosTable.value.filter((item: any) => item.target_battery_rated_kwh == 100)
      .length
);
const order_102_kwh = computed(
  () =>
    psosTable.value.filter((item: any) => item.target_battery_rated_kwh == 102)
      .length
);
const order_150_kwh = computed(
  () =>
    psosTable.value.filter((item: any) => item.target_battery_rated_kwh == 150)
      .length
);
const props = defineProps({
  configDetail: {
    type: Object,
    default: null,
  },
});

const isEmptyData = (data: any) => {
  return data === '' || data === undefined || data === null || data === 'null';
};

const getEpsModelText = (mode: number) => {
  const modeMap: { [key: number]: string } = {
    1: '收益优先',
    2: '体验优先',
    3: '收益&体验兼顾',
  };
  return modeMap[mode] || '-';
};

const initializeData = () => {
  if (props.configDetail && props.configDetail.battery_info) {
    const batteryList = props.configDetail.battery_info;
    const curIndex = Math.floor(batteryList.length / 2);
    tableLeft.value = batteryList.slice(0, curIndex);
    tableRight.value = batteryList.slice(curIndex);
  }
  if (props.configDetail && props.configDetail.service_list) {
    psosTable.value = props.configDetail.service_list || [];
    pages.value.total = psosTable.value.length;
  }
};

// 监听 configDetail 的变化
watch(() => props.configDetail, (newVal) => {
  if (newVal) {
    initializeData();
  }
}, { immediate: true });

onBeforeMount(() => {
  initializeData();
});

//分页
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
};
</script>

<style lang="scss" scoped>
.config-detail-contianer {
  font-size: 14px;
  .battery-container {
    display: inline-flex;
    border-radius: 4px;
    border: 1px solid rgba(173, 232, 232, 1);
    font-size: 12px;
    margin-top: 8px;
    .battery-item {
      margin: 8px 0px;
      padding: 0px 16px;
      border-right: 1px solid rgba(173, 232, 232, 1);
    }
    .battery-item:last-child {
      border-right: 0px solid #fff;
    }
    .battery-num {
      height: 24px;
      color: rgba(1, 160, 172, 1);
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      span {
        font-size: 12px;
        font-weight: 400;
      }
    }
  }
  .list-card {
    background-color: #fff;
    margin-bottom: 16px;
    padding: 24px;
    border: 1px solid rgba(220, 242, 243, 1);
    border-radius: 4px;
    .title-span {
      width: 100px;
      font-weight: 600;
      margin-right: 24px;
      color: #595959;
    }
    .item-span {
      span {
        padding-right: 24px;
      }
    }
    &:hover {
      border-color: #00bebe;
    }
  }
  .station-config-item {
    display: flex;
    align-items: center;
    margin-top: 12px;
    color: #262626;
    .arrow-icon {
      display: inline-block;
      vertical-align: middle;
    }
    .gap_12 {
      gap: 12px;
    }
    .color-59 {
      color: #595959;
    }
    .price-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px 30px;
    }
    .price-detail {
      white-space: nowrap;
      span:first-child {
        padding-right: 24px;
      }
      span {
        padding-right: 16px;
      }
    }
  }
  .title {
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }
  .pagination-box {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
