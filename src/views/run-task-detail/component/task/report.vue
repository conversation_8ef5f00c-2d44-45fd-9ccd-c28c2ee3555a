<template>
  <div class="image-button">
    <el-button class="welkin-primary-button width-108 height-32" style="padding: 5px 16px" @click="handleCreateImage" :loading="imgLoading">
      <ImageIcon />
      <span class="margin_l-4">生成图片</span>
    </el-button>
  </div>
  <div class="report-container" ref="canvasImg">
    <div class="line-one-container">
      <div class="line-one-card">
        <div class="card-title">设备类型</div>
        <div class="card-content font-size-26">{{ projectMap[reportDetail.project] }}</div>
      </div>
      <div class="line-one-card">
        <div class="card-title">电价模式</div>
        <div class="card-content font-size-24">{{ reportDetail.electricity_price_model == 'one_price' ? '一口价' : '分时电价' }}</div>
      </div>
      <div class="line-one-card">
        <div class="card-title">EPS开关</div>
        <div class="card-content font-size-24">{{ reportDetail.eps_params ? '开' : '关' }}</div>
      </div>
      <div class="line-one-card">
        <div class="card-title">仿真时长</div>
        <div class="card-content flex-box flex_a_i-center">
          <span class="font-size-26">{{ msToTime(reportDetail.simulation_duration)[0] }}</span>
          <span class="font-size-24 margin_l-4">{{ msToTime(reportDetail.simulation_duration)[1] }}</span>
        </div>
      </div>
      <div class="line-one-card">
        <div class="card-title">运营时段</div>
        <div class="card-content font-size-26">{{ '0' + reportDetail.operation_start_hour + ':00' }}—{{ reportDetail.operation_end_hour + ':00' }}</div>
      </div>
    </div>

    <div class="line-two-container">
      <div class="line-two-card">
        <div class="flex-box gap_10">
          <ClockIcon />
          <div class="flex-box flex_d-column gap_6">
            <div class="card-title">换电平均等待时长</div>
            <div class="card-content">单位：分钟</div>
          </div>
        </div>
        <div class="card-value">{{ getInteger(reportDetail.avg_swapping_queue_time) }}</div>
      </div>
      <div class="line-two-card">
        <div class="flex-box gap_10">
          <CashIcon />
          <div class="flex-box flex_d-column gap_6">
            <div class="card-title">边际贡献总额</div>
            <div class="card-content">单位：元</div>
          </div>
        </div>
        <div class="card-value">{{ getInteger(reportDetail.contribution_margin) }}</div>
      </div>
      <div class="line-two-card">
        <div class="flex-box gap_10">
          <TrendIcon />
          <div class="flex-box flex_d-column gap_6">
            <div class="card-title">容量利用率</div>
            <div class="card-content">单位：%</div>
          </div>
        </div>
        <div class="card-value">{{ getInteger(reportDetail.avg_capacity_utilization * 100) }}</div>
      </div>
    </div>

    <div class="line-three-container">
      <div class="line-three-card">
        <UploadCsv :url="reportDetail.user_report.download_url" v-if="reportDetail.user_report.download_url"></UploadCsv>
        <div id="lineChart" class="width-full height-full" v-show="reportDetail.user_report.graph_data.length > 0"></div>
        <div class="absolute" style="left: 20px; top: 55px; color: #8c8c8c" v-show="reportDetail.user_report.graph_data.length > 0">
          <div class="flex-box">换电订单（单）</div>
        </div>
        <div class="absolute" style="right: 10px; top: 55px; color: #8c8c8c" v-show="reportDetail.user_report.graph_data.length > 0">
          <div class="flex-box">平均等待时间（Min）</div>
        </div>
        <div v-show="reportDetail.user_report.graph_data.length == 0" class="flex-box width-full">
          <div class="line-title">用户体验</div>
          <el-empty description="暂无图像数据" style="flex: 1; padding: 0" />
        </div>
      </div>
      <div class="line-three-card">
        <div class="swap-left">
          <div class="title">换电（Min）</div>
          <div class="flex-between font-size-14 line-height-22 margin_b-6" style="color: #01a0ac">
            <div class="flex-box flex_a_i-center gap_4">
              <span>换电服务单量</span>
              <el-tooltip content="已完成/总单量" placement="top">
                <HelpIcon />
              </el-tooltip>
            </div>
            <span class="font-weight-bold">{{ getInteger(reportDetail.user_report.power_swap.actual_service_count) }}/{{ getInteger(reportDetail.user_report.power_swap.estimate_service_count) }}单</span>
          </div>
          <div class="flex-between font-size-14 line-height-22 margin_b-8">
            <span style="color: #595959">用户等待时长（平均）</span>
            <span style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.user_report.power_swap.wait_time.avg.total_wait_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18 margin_b-6" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等电池时长</span>
            <span>{{ getInteger(reportDetail.user_report.power_swap.wait_time.avg.wait_battery_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18 margin_b-8" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等用户时长</span>
            <span>{{ getInteger(reportDetail.user_report.power_swap.wait_time.avg.wait_user_time) }}</span>
          </div>
          <div class="flex-between font-size-14 line-height-22 margin_b-8">
            <span style="color: #595959">用户等待时长（90分位）</span>
            <span style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.user_report.power_swap.wait_time.p_90.total_wait_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18 margin_b-6" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等电池时长</span>
            <span>{{ getInteger(reportDetail.user_report.power_swap.wait_time.p_90.wait_battery_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等用户时长</span>
            <span>{{ getInteger(reportDetail.user_report.power_swap.wait_time.p_90.wait_user_time) }}</span>
          </div>
        </div>
        <div class="divider"></div>
        <div class="charge-right">
          <div class="title">充电（Min）</div>
          <div class="flex-between font-size-14 line-height-22 margin_b-6" style="color: #01a0ac">
            <div class="flex-box flex_a_i-center gap_4">
              <span>充电服务单量</span>
              <el-tooltip content="已完成/总单量" placement="top">
                <HelpIcon />
              </el-tooltip>
            </div>
            <span class="font-weight-bold">{{ getInteger(reportDetail.user_report.charge.actual_service_count) }}/{{ getInteger(reportDetail.user_report.charge.estimate_service_count) }}单</span>
          </div>
          <div class="flex-between font-size-14 line-height-22 margin_b-8">
            <span style="color: #595959">用户等待时长（平均）</span>
            <span style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.user_report.charge.wait_time.avg.total_wait_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18 margin_b-6" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等电池时长</span>
            <span>{{ getInteger(reportDetail.user_report.charge.wait_time.avg.wait_battery_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18 margin_b-8" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等用户时长</span>
            <span>{{ getInteger(reportDetail.user_report.charge.wait_time.avg.wait_user_time) }}</span>
          </div>
          <div class="flex-between font-size-14 line-height-22 margin_b-8">
            <span style="color: #595959">用户等待时长（90分位）</span>
            <span style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.user_report.charge.wait_time.p_90.total_wait_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18 margin_b-6" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等电池时长</span>
            <span>{{ getInteger(reportDetail.user_report.charge.wait_time.p_90.wait_battery_time) }}</span>
          </div>
          <div class="flex-between font-size-12 line-height-18" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>等用户时长</span>
            <span>{{ getInteger(reportDetail.user_report.charge.wait_time.p_90.wait_user_time) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="line-four-container">
      <div class="flex-between">
        <div class="line-title">经营测算</div>
        <UploadCsv :url="reportDetail.swap_station_battery_report.download_url" text="电池级.csv" v-if="reportDetail.swap_station_battery_report.download_url"></UploadCsv>
      </div>
      <div class="horizontal-divider"></div>
      <div class="line-four-card">
        <div class="flex-box">
          <div class="cost-left">
            <div class="line-title margin_b-6">成本（元）</div>
            <div class="flex-between font-size-14 line-height-22" style="color: #01a0ac">
              <div>总成本</div>
              <div class="font-weight-bold">{{ getInteger(reportDetail.swap_station_battery_report.cost.total_cost) }}</div>
            </div>
            <div class="flex-between font-size-14 line-height-22" style="color: #595959">
              <div>站内电池充电成本</div>
              <div style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.swap_station_battery_report.cost.station_inner_battery_charge_cost) }}</div>
            </div>
            <div class="flex-between font-size-14 line-height-22" style="color: #595959">
              <div>站外桩充电成本</div>
              <div style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.swap_station_battery_report.cost.station_out_charging_pile_charge_cost) }}</div>
            </div>
            <div class="flex-between font-size-14 line-height-22" style="color: #595959">
              <div>配电回路能耗成本</div>
              <div style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.swap_station_battery_report.cost.power_consumption_loop_cost) }}</div>
            </div>
          </div>
          <div class="divider"></div>
          <div class="revenue-right">
            <div class="line-title margin_b-6">收入（元）</div>
            <div class="flex-between font-size-14 line-height-22" style="color: #01a0ac">
              <div>总收入</div>
              <div class="font-weight-bold">{{ getInteger(reportDetail.swap_station_battery_report.income.total_income) }}</div>
            </div>
            <div class="flex-between font-size-14 line-height-22" style="color: #595959">
              <div>换电服务收入</div>
              <div style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.swap_station_battery_report.income.power_swap_service_income) }}</div>
            </div>
            <div class="flex-between font-size-14 line-height-22" style="color: #595959">
              <div>充电服务收入</div>
              <div style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.swap_station_battery_report.income.charge_service_income) }}</div>
            </div>
            <div class="flex-between font-size-14 line-height-22" style="color: #595959">
              <div>电网互动收入</div>
              <div style="color: #262626; font-weight: bold">{{ getInteger(reportDetail.swap_station_battery_report.income.grid_income) }}</div>
            </div>
          </div>
        </div>
        <div class="relative">
          <div id="lineChart2" class="width-full height-full" v-show="reportDetail.swap_station_battery_report.graph_data.length > 0"></div>
          <div class="absolute" style="left: 0px; top: 0px; color: #8c8c8c; line-height: 18px" v-show="reportDetail.swap_station_battery_report.graph_data.length > 0">
            <div class="flex-box">充电事件（次）</div>
          </div>
          <div class="absolute" style="right: 0px; top: 0px; color: #8c8c8c; line-height: 18px" v-show="reportDetail.swap_station_battery_report.graph_data.length > 0">
            <div class="flex-box">电池充电成本（元）</div>
          </div>
          <div v-show="reportDetail.swap_station_battery_report.graph_data.length == 0" class="width-full">
            <el-empty description="暂无图像数据" style="padding: 0; height: 174px" />
          </div>
        </div>
      </div>
    </div>

    <div class="line-five-container">
      <div class="line-five-card">
        <div class="card-left relative">
          <UploadCsv :url="reportDetail.device_report.download_url" text="整站级.csv" v-if="reportDetail.device_report.download_url" style="top: -2px; left: 80px; right: auto; line-height: 22px"></UploadCsv>
          <div id="lineChart3" class="width-full height-full" v-show="reportDetail.device_report.graph_data.length > 0"></div>
          <div v-show="reportDetail.device_report.graph_data.length == 0" class="flex-box width-full">
            <div class="line-title">设备性能</div>
            <el-empty description="暂无图像数据" style="flex: 1; padding: 0" />
          </div>
        </div>
        <div class="divider"></div>
        <div class="card-right">
          <div class="line-title margin_b-10">能效（kWh）</div>
          <div class="flex-between font-size-14 line-height-22 margin_b-8" style="color: #01a0ac">
            <span>能效</span>
            <span class="font-weight-bold">{{ getInteger(reportDetail.device_report.energy_efficiency.efficiency * 100) }}<span v-if="reportDetail.device_report.energy_efficiency.efficiency !== null">%</span></span>
          </div>
          <div class="flex-between font-size-14 line-height-22 margin_b-6" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>设备总能耗</span>
            <span>{{ getInteger(reportDetail.device_report.energy_efficiency.total_device_efficiency) }}</span>
          </div>
          <div class="flex-between font-size-14 line-height-22 margin_b-6" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>配电回路能耗</span>
            <span>{{ getInteger(reportDetail.device_report.energy_efficiency.power_distribution_loop_efficiency) }}</span>
          </div>
          <div class="flex-between font-size-14 line-height-22 margin_b-6" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>站内电池充电能耗</span>
            <span>{{ getInteger(reportDetail.device_report.energy_efficiency.station_battery_charge_amount) }}</span>
          </div>
          <div class="flex-between font-size-14 line-height-22" style="color: #8c8c8c">
            <span><span class="margin-n-4 font-weight-bold">·</span>SCT桩充电电量</span>
            <span>{{ getInteger(reportDetail.device_report.energy_efficiency.sct_charging_pile_charge_amount) }}</span>
          </div>
        </div>
      </div>
      <div class="horizontal-divider"></div>
      <div class="bottom-card">
        <div class="capacity-left">
          <div class="line-title margin_b-8">容量（kWh）</div>
          <div class="font-size-14 line-height-22 margin_b-8" style="color: #01a0ac">
            <span>整站容量利用率：</span>
            <span class="font-weight-bold">{{ getInteger(reportDetail.device_report.capacity.capacity_factor * 100) }}<span v-if="reportDetail.device_report.capacity.capacity_factor !== null">%</span></span>
          </div>
          <div class="multiple-column margin_b-6">
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 整站额定容量</span>
              <span>{{ getInteger(reportDetail.device_report.capacity.rated_capacity) }}</span>
            </div>
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 实际最大容量</span>
              <span>{{ getInteger(reportDetail.device_report.capacity.max_capacity) }}</span>
            </div>
          </div>
          <div class="multiple-column">
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 整站负荷因数</span>
              <span>{{ getInteger(reportDetail.device_report.capacity.load_factor * 100) }}<span v-if="reportDetail.device_report.capacity.load_factor !== null">%</span></span>
            </div>
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 实际平均容量</span>
              <span>{{ getInteger(reportDetail.device_report.capacity.avg_capacity) }}</span>
            </div>
          </div>
        </div>
        <div class="divider"></div>
        <div class="module-right">
          <div class="line-title margin_b-8">模块</div>
          <div class="font-size-14 line-height-22 margin_b-8" style="color: #01a0ac">
            <span>模块利用率：</span>
            <span class="font-weight-bold">{{ getInteger(reportDetail.device_report.module.module_ratio * 100) }}<span v-if="reportDetail.device_report.module.module_ratio !== null">%</span></span>
          </div>
          <div class="multiple-column margin_b-6">
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 模块数量</span>
              <span>{{ getInteger(reportDetail.device_report.module.module_amount) }}<span v-if="reportDetail.device_report.module.module_amount !== null">块</span></span>
            </div>
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 单模块实际运行时长</span>
              <span>{{ getInteger(reportDetail.device_report.module.module_real_run_time / 3600) }}<span v-if="reportDetail.device_report.module.module_real_run_time !== null">h</span></span>
            </div>
          </div>
          <div class="multiple-column">
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 单模块功率</span>
              <span>{{ getInteger(reportDetail.device_report.module.module_power) }}<span v-if="reportDetail.device_report.module.module_power !== null">kW</span></span>
            </div>
            <div class="column-item">
              <span><span class="font-weight-bold">·</span> 单模块理论运行时长</span>
              <span>{{ getInteger(reportDetail.device_report.module.module_theoretical_run_time / 3600) }}<span v-if="reportDetail.device_report.module.module_theoretical_run_time !== null">h</span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { projectMap } from '../../chart-mock'
import { msToTime, getUserId } from '~/utils'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import html2canvas from 'html2canvas'
import UploadCsv from './upload-csv.vue'
import ClockIcon from '../icon/clock-icon.vue'
import CashIcon from '../icon/cash-icon.vue'
import TrendIcon from '../icon/trend-icon.vue'
import ImageIcon from '../icon/image-icon.vue'
import HelpIcon from '~/assets/svg/help.vue'

defineProps({
  showTime: {
    type: Boolean,
    default: true
  },
  reportDetail: {
    type: Object,
    default: {}
  }
})

const route = useRoute()
const canvasImg = ref()
const imgLoading = ref(false)

/**
 * @description: 生成图片、添加水印并复制到剪贴板
 * @return {*}
 */
const handleCreateImage = async () => {
  const simulationId = `仿真详情 ${route.query.simulation_id}`
  imgLoading.value = true
  const canvas = await html2canvas(canvasImg.value)
  const dataUrl = canvas.toDataURL('image/png')
  const img = new Image()
  img.src = dataUrl
  await new Promise((resolve) => {
    img.onload = resolve
  })
  const tempCanvas = document.createElement('canvas')
  tempCanvas.width = img.width + 80
  tempCanvas.height = img.height + 160
  const ctx = tempCanvas.getContext('2d')!
  ctx.fillStyle = 'white'
  ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height)
  ctx.font = '500 40px Blue Sky Standard'
  ctx.fillStyle = '#1f1f1f'
  ctx.fillText(simulationId, 40, 80)
  ctx.drawImage(img, 40, 120)

  // 水印
  ctx.font = '500 50px Blue Sky Standard'
  ctx.fillStyle = 'rgba(200, 200, 200, 0.3)'
  const watermarkText = getUserId() || 'unknown'
  const rows = 4 // 水印的行数
  const columns = 3 // 水印的列数
  const xSpacing = tempCanvas.width / columns
  const ySpacing = tempCanvas.height / rows
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < columns; col++) {
      const x = col * xSpacing + xSpacing / 2
      const y = row * ySpacing + ySpacing / 2
      ctx.save()
      ctx.translate(x, y)
      ctx.rotate(-0.3)
      ctx.fillText(watermarkText, 0, 0)
      ctx.restore()
    }
  }

  tempCanvas.toBlob(async (blob: any) => {
    const data = [new ClipboardItem({ 'image/png': blob })]
    await navigator.clipboard.write(data)
    imgLoading.value = false
    ElMessage.success('图片已复制到剪贴板')
  })
}

const getInteger = (num: any) => {
  return num === null ? '--' : Number.isInteger(num) ? num : num.toFixed(2)
}
</script>

<style lang="scss" scoped>
.image-button {
  position: absolute;
  right: 24px;
  top: -54px;
}
.report-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  .line-one-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    column-gap: 8px;
    .line-one-card {
      height: 78px;
      padding: 8px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      border: 1px solid #ade8e8;
      background: #fff;
      .card-title {
        color: #595959;
        font-size: 16px;
        line-height: 24px;
      }
      .card-content {
        color: #262626;
        line-height: 34px;
      }
    }
  }
  .line-two-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 8px;
    .line-two-card {
      height: 88px;
      padding: 16px 24px 16px 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px;
      background: linear-gradient(to right, #45d9cf, #00bebe);
      .card-title {
        font-size: 18px;
        line-height: 26px;
        color: #fff;
      }
      .card-content {
        font-size: 16px;
        line-height: 24px;
        color: #fff;
      }
      .card-value {
        font-size: 38px;
        line-height: 60.27px;
        color: #fff;
      }
    }
  }
  .line-three-container {
    display: grid;
    grid-template-columns: 1fr 500px;
    column-gap: 8px;
    .line-three-card {
      height: 258px;
      padding: 20px;
      position: relative;
      border-radius: 4px;
      border: 1px solid #dcf2f3;
      background: #fff;
      display: flex;
      .swap-left,
      .charge-right {
        flex: 1;
        .title {
          font-size: 16px;
          line-height: 24px;
          font-weight: bold;
          color: #262626;
          margin-bottom: 12px;
        }
      }
      .divider {
        width: 1px;
        background: #e6e7ec;
        margin: 0 20px;
      }
    }
  }
  .line-four-container {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    position: relative;
    .horizontal-divider {
      width: 100%;
      height: 1px;
      background: #e6e7ec;
      margin-top: 8px;
      margin-bottom: 12px;
    }
    .line-four-card {
      height: 174px;
      display: grid;
      grid-template-columns: 480px 1fr;
      column-gap: 20px;
      .cost-left,
      .revenue-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 6px;
      }
      .divider {
        width: 1px;
        background: #e6e7ec;
        margin: 0 20px;
      }
    }
  }
  .line-five-container {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    .line-five-card {
      height: 194px;
      display: flex;
      .card-left {
        flex: 9;
      }
      .card-right {
        flex: 2;
      }
    }
    .divider {
      width: 1px;
      background: #e6e7ec;
      margin: 0 20px;
    }
    .horizontal-divider {
      width: 100%;
      height: 1px;
      background: #e6e7ec;
      margin-top: 12px;
      margin-bottom: 12px;
    }
    .bottom-card {
      display: flex;
      .capacity-left {
        flex: 1;
      }
      .module-right {
        flex: 1;
      }
      .multiple-column {
        display: flex;
        gap: 14%;
        .column-item {
          flex: 1;
          font-size: 14px;
          line-height: 22px;
          color: #8c8c8c;
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
  }
  .line-title {
    font-size: 16px;
    line-height: 24px;
    font-weight: bold;
    color: #262626;
  }
  :deep(.el-empty__image) {
    width: 120px;
  }
}
</style>
