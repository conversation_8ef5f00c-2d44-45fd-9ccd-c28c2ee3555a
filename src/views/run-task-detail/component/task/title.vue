<template>
  <div class="flex-box padding_b-4 flex_a_i-center font-size-20">
    <span class="padding_r-8 padding_t-3 cursor-pointer" @click="goBack"
      ><BackArrow></BackArrow
    ></span>
    <span style="color: #8c8c8c; font-weight: 500">任务详情 /</span>
    <span style="color: #1f1f1f; font-weight: 500">&nbsp;仿真详情</span>
    <span style="color: #1f1f1f; font-weight: 500; padding-left: 3px">{{
      simulation_id
    }}</span>
  </div>
</template>
<script setup lang="ts">
import BackArrow from '../icon/back-arrow.vue';
import { useRoute, useRouter } from 'vue-router'
defineProps({
  simulation_id: {
    type: String,
    default: '',
  },
});

const route = useRoute()
const router = useRouter()

const goBack = () => {
  router.push({
    path: '/device-simulation/run-list/run-detail',
    query: {
      task_id: route.query.task_id,
      project: route.query.project
    }
  })
};
</script>
<style lang="scss" scoped></style>
