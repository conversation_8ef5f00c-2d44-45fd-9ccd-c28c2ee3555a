<template>
  <div>
    <div class="measure-container" v-if="showTime">
      <span class="tick"><span class="one"></span>250</span>
      <span class="tick"><span class="two"></span>200</span>
      <span class="tick"><span class="thrid"></span>150</span>
      <span class="tick"><span class="four"></span>100</span>
      <span class="tick"><span class="five"></span>50</span>
      <span class="tick"><span class="six"></span>0</span>
      <!-- 假设还有正值的刻度 -->
    </div>
    <div class="measure-container" v-else>
      <span class="tick"><span class="one"></span>200</span>
      <span class="tick"><span class="two"></span>160</span>
      <span class="tick"><span class="thrid"></span>120</span>
      <span class="tick"><span class="four"></span>80</span>
      <span class="tick"><span class="five"></span>40</span>
      <span class="tick"><span class="six"></span>0</span>
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  showTime: {
    type: Boolean,
    default: true,
  },
});
</script>
<style lang="scss" scoped>
.measure-container {
  position: absolute;
  top: 60px;
  right: 22px;
  .tick {
    display: flex;
    font-size: 12px;
    align-items: center;
    color: #8c8c8c;
    margin: 21px 0; /* 水平间距 */
    .one {
      display: inline-block;
      width: 6px;
      height: 1px;
      margin-right: 3px;
      background-color: #347379;
    }
    .two {
      display: inline-block;
      width: 6px;
      margin-right: 3px;
      height: 1px;
      background-color: #34999d;
    }
    .thrid {
      display: inline-block;
      width: 6px;
      margin-right: 3px;
      height: 1px;
      background-color: #347379;
    }
    .four {
      display: inline-block;
      width: 6px;
      margin-right: 3px;
      height: 1px;
      background-color: #34c0c1;
    }
    .five {
      display: inline-block;
      width: 6px;
      margin-right: 3px;
      height: 1px;
      background-color: #92eae6;
    }
    .six {
      display: inline-block;
      width: 6px;
      margin-right: 3px;
      height: 1px;
      background-color: #cafcf6;
    }
  }
}
</style>
