// 设备性能
const options = {
  color: ['#01A0AC', '#FFCD5A'],
  title: {
    text: '设备性能',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 16
    }
  },
  legend: {
    padding: 0,
    right: 0,
    top: 3,
    textStyle: {
      color: '#595959'
    },
    itemStyle: {
      opacity: 1
    },
    itemWidth: 14,
    itemHeight: 8,
    data: [
      { name: '容量利用率', icon: 'path://M11.9691 7.50008C11.7231 5.52689 10.0398 4 8 4C5.96016 4 4.27695 5.52689 4.03094 7.50008H0.5C0.223858 7.50008 0 7.72394 0 8.00008C0 8.27622 0.223858 8.50008 0.5 8.50008H4.03096C4.27704 10.4732 5.96022 12 8 12C10.0398 12 11.723 10.4732 11.969 8.50008H15.5C15.7761 8.50008 16 8.27622 16 8.00008C16 7.72394 15.7761 7.50008 15.5 7.50008H11.9691ZM11 8C11 9.65685 9.65685 11 8 11C6.34315 11 5 9.65685 5 8C5 6.34315 6.34315 5 8 5C9.65685 5 11 6.34315 11 8Z' },
      { name: '模块利用率', icon: 'path://M11.9691 7.50008C11.7231 5.52689 10.0398 4 8 4C5.96016 4 4.27695 5.52689 4.03094 7.50008H0.5C0.223858 7.50008 0 7.72394 0 8.00008C0 8.27622 0.223858 8.50008 0.5 8.50008H4.03096C4.27704 10.4732 5.96022 12 8 12C10.0398 12 11.723 10.4732 11.969 8.50008H15.5C15.7761 8.50008 16 8.27622 16 8.00008C16 7.72394 15.7761 7.50008 15.5 7.50008H11.9691ZM11 8C11 9.65685 9.65685 11 8 11C6.34315 11 5 9.65685 5 8C5 6.34315 6.34315 5 8 5C9.65685 5 11 6.34315 11 8Z' }
    ]
  },
  tooltip: {
    trigger: 'axis',
    padding: [14, 16], // 内边距
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // 背景颜色
    formatter: function (params: any) {
      // 多系列数据时，params 是一个数组
      var result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${params[0].name}:00 - ${parseInt(params[0].name) + 1}:00</div>`
      // 遍历所有系列的数据
      params.forEach((item: any, index: number) => {
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
         ${params[index].marker}<span style="padding-right:17px;">${params[index].seriesName}</span> ${params[index].value}%
        </div>`
      })
      return result
    }
  },
  grid: {
    top: '40',
    left: '0',
    right: '0',
    bottom: '0',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisLabel: {
      alignWithLabel: true,
      show: true,
      color: '#8C8C8C'
    },
    axisLine: {
      show: true, // 是否显示轴线
      lineStyle: {
        color: 'rgba(225, 225, 225, 1)' // 轴线颜色
      }
    },
    axisTick: {
      alignWithLabel: true,
      show: true, // 是否显示刻度线
      lineStyle: {
        color: 'rgba(225, 225, 225, 1)' // 刻度线颜色
      }
    },
    data: []
  },
  yAxis: [
    {
      type: 'value',
      alignTicks: true,
      name: '',
      position: 'left',
      nameTextStyle: {
        padding: [0, 0, 5, 60]
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}%'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      max: 100
    }
  ],
  series: [
    {
      name: '容量利用率',
      type: 'line',
      itemStyle: {
        opacity: 0
      },
      emphasis: {
        itemStyle: {
          opacity: 1
        }
      },
      data: []
    },
    {
      name: '模块利用率',
      type: 'line',
      itemStyle: {
        opacity: 0
      },
      emphasis: {
        itemStyle: {
          opacity: 1
        }
      },
      data: []
    }
  ]
}

//站内电池充电
const options1 = {
  color: ['#00BEBE', '#FFC031'],
  tooltip: {
    trigger: 'axis', // 数据项触发
    showContent: true, // 显示 tooltip 内容
    alwaysShowContent: false, // 鼠标经过时显示 tooltip
    axisPointer: {
      type: 'shadow' // 坐标轴指示器类型
    },
    padding: [14, 16], // 内边距
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // 背景颜色
    formatter: function (params: any) {
      // 多系列数据时，params 是一个数组
      var result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${params[0].name}:00 - ${parseInt(params[0].name) + 1}:00</div>`
      // 遍历所有系列的数据
      params.forEach((item: any, index: number) => {
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
         ${params[index].marker}<span style="padding-right:17px;">${params[index].seriesName}</span> ${params[index].value} 
        </div>`
      })
      return result
    }
  },
  grid: {
    top: '32',
    left: '0',
    right: '0',
    bottom: '0',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    axisLabel: {
      alignWithLabel: true,
      show: true,
      color: '#8C8C8C'
    },
    axisLine: {
      show: true, // 是否显示轴线
      lineStyle: {
        color: 'rgba(225, 225, 225, 1)' // 轴线颜色
      }
    },
    axisTick: {
      alignWithLabel: true,
      show: true, // 是否显示刻度线
      lineStyle: {
        color: 'rgba(225, 225, 225, 1)' // 刻度线颜色
      }
    },
    data: []
  },
  yAxis: [
    {
      type: 'value',
      alignTicks: true,
      name: '',
      nameGap: 20,
      position: 'left',
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      }
    },
    {
      type: 'value',
      name: '',
      alignTicks: true,
      position: 'right',
      nameGap: 20,
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      }
    }
  ],
  series: [
    {
      name: '充电事件',
      type: 'bar',
      barMaxWidth: 14,
      yAxisIndex: 0,
      data: []
    },
    {
      name: '充电成本',
      type: 'line',
      itemStyle: {
        opacity: 0
      },
      emphasis: {
        itemStyle: {
          opacity: 1
        }
      },
      yAxisIndex: 1,
      data: []
    }
  ]
}

//用户体验
const options2 = {
  color: ['#00BEBE', '#FFC031'],
  title: {
    text: '用户体验',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 16
    }
  },
  tooltip: {
    trigger: 'axis', // 数据项触发
    showContent: true, // 显示 tooltip 内容
    alwaysShowContent: false, // 鼠标经过时显示 tooltip
    axisPointer: {
      type: 'shadow' // 坐标轴指示器类型
    },
    padding: [14, 16], // 内边距
    backgroundColor: 'rgba(255, 255, 255, 0.7)', // 背景颜色
    formatter: function (params: any) {
      // 多系列数据时，params 是一个数组
      var result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${params[0].name}:00 - ${parseInt(params[0].name) + 1}:00</div>`
      // 遍历所有系列的数据
      params.forEach((item: any, index: number) => {
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
         ${params[index].marker}<span style="padding-right:17px;">${params[index].seriesName}</span> ${params[index].value} 
        </div>`
      })
      return result
    }
  },
  grid: {
    top: '65',
    left: '0',
    right: '0',
    bottom: '0',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    axisLabel: {
      alignWithLabel: true,
      show: true,
      color: '#8C8C8C'
    },
    axisLine: {
      show: true, // 是否显示轴线
      lineStyle: {
        color: 'rgba(225, 225, 225, 1)' // 轴线颜色
      }
    },
    axisTick: {
      alignWithLabel: true,
      show: true, // 是否显示刻度线
      lineStyle: {
        color: 'rgba(225, 225, 225, 1)' // 刻度线颜色
      }
    },
    data: []
  },
  yAxis: [
    {
      type: 'value',
      alignTicks: true,
      name: '',
      nameGap: 20,
      position: 'left',
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      }
    },
    {
      type: 'value',
      name: '',
      alignTicks: true,
      position: 'right',
      nameGap: 20,
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      }
    }
  ],
  series: [
    {
      name: '换电订单',
      type: 'bar',
      barMaxWidth: 14,
      yAxisIndex: 0,
      data: []
    },
    {
      name: '平均等待时间',
      type: 'line',
      barWidth: 12,
      itemStyle: {
        opacity: 0
      },
      emphasis: {
        itemStyle: {
          opacity: 1
        }
      },
      yAxisIndex: 1,
      data: []
    }
  ]
}

const projectMap = {
  PowerSwap2: '2.0',
  PUS3: '3.0',
  PUS4: '4.0'
} as any

export { options, options1, options2, projectMap }
