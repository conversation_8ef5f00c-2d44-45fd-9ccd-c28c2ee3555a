<template>
  <div class="charge-list-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_charge_list') }}</div>
    </div>

    <div class="content-container">
      <el-form :model="form" :label-width="locale == 'zh' ? '64px' : 'auto'" label-position="left">
        <div class="first-line">
          <el-form-item :label="$t('common.pp_time_frame')">
            <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getTodayShortcuts()" :clearable="false" :disabledDate="getDisabledTodayDate" :prefix-icon="customPrefix">
              <template #range-separator>
                <TimeRangeIcon style="margin: 0 5px" />
              </template>
            </el-date-picker>
          </el-form-item>
          <el-form-item :label="$t('common.pp_device_type')">
            <el-select v-model="form.project" filterable :placeholder="$t('common.pp_please_select')" class="width-full">
              <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('common.pp_order_id')">
            <el-input v-model="form.order_id" :placeholder="$t('common.pp_please_input')" clearable>
              <template #prefix><SearchIcon /></template>
            </el-input>
          </el-form-item>
        </div>
        <div class="second-line">
          <el-form-item :label="$t('common.pp_device_name')">
            <el-select v-model="form.device_id" clearable filterable remote :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
              <template #prefix><SearchIcon /></template>
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description">
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('common.pp_order_status')">
            <el-select v-model="form.order_status" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
              <el-option v-for="item in orderStatusOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('chargeList.pp_stop_reason')">
            <el-select v-model="form.service_stop_reasons" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
              <el-option v-for="item in stopOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
          <el-form-item class="search-form-item">
            <div class="width-full height-32 flex-box flex_j_c-flex-end flex_a_i-center">
              <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
              <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
            </div>
          </el-form-item>
        </div>
      </el-form>

      <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="order_id" :label="$t('common.pp_order_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.order_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="order_status" :label="$t('common.pp_order_status')" min-width="160">
          <template #default="{ row }">
            <span>{{ getLabel(row.order_status, orderStatusOptions) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_stop_reason" :label="$t('chargeList.pp_stop_reason')" min-width="160">
          <template #default="{ row }">
            <span>{{ getLabel(row.service_stop_reason, stopOptions) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_start_timestamp" :label="$t('common.pp_order_start_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.order_start_timestamp) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_end_timestamp" :label="$t('common.pp_order_end_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.order_end_timestamp) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_start_timestamp" :label="$t('station.pp_service_start_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.service_start_timestamp) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_end_timestamp" :label="$t('station.pp_service_end_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.service_end_timestamp) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_id" :label="$t('station.pp_service_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.service_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_id" :label="$t('common.pp_vehicle_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.vehicle_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_vin" :label="$t('common.pp_vin')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.vehicle_vin" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_brand" :label="$t('common.pp_vehicle_brand')" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.vehicle_type || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_platform" :label="$t('common.pp_vehicle_platform')" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.vehicle_platform || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="channel" :label="$t('common.pp_channel')" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.channel || '-' }}</span>
            <span v-if="row.client">（{{ row.client }}）</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('common.pp_device_name')" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="device_id" :label="$t('common.pp_device_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="resource_id" :label="$t('common.pp_resource_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.resource_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.pp_detail')" width="70" fixed="right" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <DetailIcon @click="handleClickDetail(row)" class="cursor-pointer" />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination">
        <Page :page="pages" @change="handlePageChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef, h, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { useRoute, useRouter } from 'vue-router'
import { formatTime, removeNullKeys, clearJson, initStartTime, initEndTime, getTodayShortcuts, getDisabledTodayDate, getOptions, getLabel } from '~/utils'
import { apiGetDevices } from '~/apis/home'
import { apiGetStatusOptions, apiGetChargeOrderList } from '~/apis/charge-list'
import { projectOptions } from './components/constant'
import { ElMessage } from 'element-plus'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import DetailIcon from '~/views/satisfaction-analysis/components/icon/detail-icon.vue'
import { debounce, cloneDeep } from 'lodash-es'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const remoteLoading = ref(false)
const list = ref([] as any)
const deviceOptions = ref([] as any)
const orderStatusOptions = ref([] as any)
const stopOptions = ref([] as any)
const datePicker = ref([initStartTime, initEndTime] as any)
const form = ref({
  start_time: initStartTime,
  end_time: initEndTime,
  project: '',
  order_id: '',
  device_id: '',
  order_status: [],
  service_stop_reasons: []
})
const searchForm = ref({} as any)
const pages = ref(cloneDeep(page))
const { locale } = useI18n({ useScope: 'global' })

const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})

/**
 * @description: 跳转至详情
 * @param {*} row
 * @return {*}
 */
const handleClickDetail = (row: any) => {
  sessionStorage.setItem('charge-list', JSON.stringify(route.query))
  router.push({
    path: `/fault-diagnosis/charge-list/charge-detail`,
    query: { project: row.project, order: row.order_id }
  })
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: 'PowerCharger3_1', name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 获取订单列表
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.order_status = formData.order_status.join(',')
  formData.service_stop_reasons = formData.service_stop_reasons.join(',')
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.descending = true
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys(formData) }
    })
  }
  loading.value = true
  try {
    const res = await apiGetChargeOrderList(formData, form.value.project)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  form.value.project = 'PowerCharger3_1'
  datePicker.value = [initStartTime, initEndTime]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = cloneDeep(form.value)
  getList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 获取订单状态、结束原因列表项
 * @return {*}
 */
const getStatusOptions = async () => {
  const res = await apiGetStatusOptions()
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    orderStatusOptions.value = getOptions(res.data.order_status)
    stopOptions.value = getOptions(res.data.service_stop_reason)
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  }
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.project = !!initParams.project ? initParams.project : 'PowerCharger3_1'
  form.value.order_id = !!initParams.order_id ? initParams.order_id : ''
  form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
  form.value.order_status = !!initParams.order_status ? initParams.order_status.split(',') : []
  form.value.service_stop_reasons = !!initParams.service_stop_reasons ? initParams.service_stop_reasons.split(',') : []
  searchForm.value = cloneDeep(form.value)
  searchDeviceList(form.value.device_id || 'NIO')
  getStatusOptions()
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.charge-list-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    background-color: #fff;
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      margin-bottom: 20px;
      .first-line {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px 24px;
        margin-bottom: 16px;
      }
      .second-line {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 140px;
        gap: 16px 24px;
        .search-form-item .el-form-item__content {
          margin-left: 0 !important;
        }
      }
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-form-item__label-wrap {
          margin: 0 !important;
        }
        .el-form-item__content {
          white-space: nowrap;
          align-items: flex-start;
          flex-wrap: nowrap;
        }
        .el-date-editor .el-range-input {
          color: #262626;
        }
        .el-date-editor .el-range__icon {
          margin-right: 10px;
        }
        .el-select .el-select__tags .el-tag--info {
          color: #262626;
        }
        .el-tag.el-tag--info {
          color: #262626;
        }
      }
    }
  }
}
</style>
