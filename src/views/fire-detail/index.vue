<template>
  <div class="fire-alarm-detail-container">
    <!-- 1. 基本信息 -->
    <div class="basic-info-container" v-if="basic_info.device_id">
      <div class="flex-box flex_j_c-space-between flex_a_i-center">
        <div class="flex-box flex_a_i-center gap_12">
          <div class="font-size-16 line-height-24 color-26 font-weight-bold">{{ $t('fireAlarm.pp_basic_info') }}</div>
        </div>
      </div>
      <el-row :gutter="20">
        <el-col :span="2"><span class="label-text">设备名称</span></el-col>
        <el-col :span="6">
          <span class="value-text" v-if="basic_info.device_name">{{ basic_info.device_name }}</span>
          <span class="value-text" v-else>-</span>
        </el-col>
        <el-col :span="2"><span class="label-text">所在城市</span></el-col>
        <el-col :span="6">
          <span class="value-text" v-if="basic_info.area">{{ basic_info.area }}</span>
          <span class="value-text" v-else>-</span>
        </el-col>
        <el-col :span="2"><span class="label-text">设备ID</span></el-col>
        <el-col :span="6"
          ><span class="value-text">{{ basic_info.device_id }}</span></el-col
        >
      </el-row>
      <el-row :gutter="20">
        <el-col :span="2"><span class="label-text">消防仓位</span></el-col>
        <el-col :span="6">
          <span class="value-text" v-if="basic_info.fire_space">{{ basic_info.fire_space }}</span>
          <span class="value-text" v-else>-</span>
        </el-col>
        <el-col :span="2"><span class="label-text">发生时间</span></el-col>
        <el-col :span="6"
          ><span class="value-text">{{ formatTime(basic_info.alarm_createts) }}</span></el-col
        >
        <el-col :span="2"><span class="label-text">环境温度</span></el-col>
        <el-col :span="6">
          <span class="value-text" v-if="basic_info.temperature">{{ basic_info.temperature }}℃</span>
          <span class="value-text" v-else>-</span>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="2"><span class="label-text">转运结果</span></el-col>
        <el-col :span="6">
          <span class="value-text" style="color: rgb(103, 194, 58)" v-if="basic_info.transfer_result == 'success'">成功</span>
          <span class="value-text" v-if="basic_info.transfer_result == 'unexecuted'">未执行</span>
          <span class="value-text" v-if="basic_info.transfer_result == 'executing'">执行中</span>
          <span class="value-text" style="color: red" v-if="basic_info.transfer_result == 'fail'">失败</span>
        </el-col>
        <el-col :span="2"><span class="label-text">落水结果</span></el-col>
        <el-col :span="6">
          <span class="value-text" v-if="basic_info.fall_in_water_result == 'success'">成功</span>
          <span class="value-text" v-if="basic_info.fall_in_water_result == 'unexecuted'">未执行</span>
          <span class="value-text" v-if="basic_info.fall_in_water_result == 'executing'">执行中</span>
          <span class="value-text" style="color: red" v-if="basic_info.fall_in_water_result == 'fail'">失败</span>
          <span class="value-text" v-if="basic_info.fall_in_water_result == null">-</span>
        </el-col>
        <el-col :span="3"
          ><span class="label-text">消防日志</span><span>（{{ basic_info.fire_alarm_data.parts.length }}</span
          ><span>/{{ basic_info.fire_alarm_data.total_parts }}）</span></el-col
        >
        <el-col :span="5"
          ><span class="value-text" v-if="basic_info.fire_alarm_data"><span style="color: #00bebe; cursor: pointer" @click="handleDownload(basic_info.fire_alarm_data)">点击下载</span></span></el-col
        >
      </el-row>
    </div>

    <!-- 2. 消防告警表格 -->
    <div class="alarm-table-container margin_t-16">
      <div class="font-size-16 font-weight-bold margin_b-12">{{ $t('fireAlarm.pp_alarm_list') }}</div>
      <el-table :data="alarmList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-box flex_a_i-center width-full">
              <span class="ellipse">{{ scope.row.data_id_description ? scope.row.data_id_description : '-' }}</span>
              <el-popover :width="520" placement="top" v-if="scope.row.servo_fault_list">
                <template #reference>
                  <AlarmIcon class="margin_l-6" />
                </template>
                <el-table :data="scope.row.servo_fault_list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }">
                  <el-table-column prop="id" :label="$t('alarmList.pp_fault_id')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="code" :label="$t('alarmList.pp_native_code')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="real_code" :label="$t('alarmList.pp_real_code')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="code_name" :label="$t('alarmList.pp_fault_name')" min-width="120" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span>{{ row.code_name || '-' }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="130" show-overflow-tooltip />
        <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="130" show-overflow-tooltip>
          <template #default="scope">
            <span class="alarm-level-container" v-if="alarmLevelMap[scope.row.alarm_level]" :style="{ color: alarmLevelMap[scope.row.alarm_level].color, background: alarmLevelMap[scope.row.alarm_level].background }">
              {{ $t(alarmLevelMap[scope.row.alarm_level].name) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" min-width="130" show-overflow-tooltip>
          <template #default="scope">
            <div class="alarm-status-container" :style="{ background: alarmStatusMap[scope.row.state].background }">
              <ClearIcon v-if="scope.row.state == 1" />
              <CreateIcon v-else-if="scope.row.state == 2" />
              <UnknownIcon v-else />
              <span>{{ $t(alarmStatusMap[scope.row.state].name) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatTime(scope.row.create_ts) }}
          </template>
        </el-table-column>
        <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatTime(scope.row.clear_ts) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 3. 非消防相关告警 -->
    <div class="alarm-table-container margin_t-16">
      <div class="font-size-16 font-weight-bold margin_b-12">{{ $t('fireAlarm.pp_no_fire_alarm') }}</div>
      <el-table :data="notFireList" :max-height="300" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-box flex_a_i-center width-full">
              <span class="ellipse">{{ scope.row.data_id_description ? scope.row.data_id_description : '-' }}</span>
              <el-popover :width="520" placement="top" v-if="scope.row.servo_fault_list">
                <template #reference>
                  <AlarmIcon class="margin_l-6" />
                </template>
                <el-table :data="scope.row.servo_fault_list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }">
                  <el-table-column prop="id" :label="$t('alarmList.pp_fault_id')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="code" :label="$t('alarmList.pp_native_code')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="real_code" :label="$t('alarmList.pp_real_code')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="code_name" :label="$t('alarmList.pp_fault_name')" min-width="120" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span>{{ row.code_name || '-' }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="130" show-overflow-tooltip />
        <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="130" show-overflow-tooltip>
          <template #default="scope">
            <span class="alarm-level-container" v-if="alarmLevelMap[scope.row.alarm_level]" :style="{ color: alarmLevelMap[scope.row.alarm_level].color, background: alarmLevelMap[scope.row.alarm_level].background }">
              {{ $t(alarmLevelMap[scope.row.alarm_level].name) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" min-width="130" show-overflow-tooltip>
          <template #default="scope">
            <div class="alarm-status-container" :style="{ background: alarmStatusMap[scope.row.state].background }">
              <ClearIcon v-if="scope.row.state == 1" />
              <CreateIcon v-else-if="scope.row.state == 2" />
              <UnknownIcon v-else />
              <span>{{ $t(alarmStatusMap[scope.row.state].name) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatTime(scope.row.create_ts) }}
          </template>
        </el-table-column>
        <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatTime(scope.row.clear_ts) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 3. 转运流程 -->
    <div class="transfer-step-container margin_t-16">
      <div class="font-size-16 font-weight-bold margin_b-12">{{ $t('fireAlarm.pp_transfer_process') }}</div>
      <div class="info-card">
        <div class="flex-box margin_t-16 padding_b-12 overflow-auto" v-if="eventLineInfo.length > 0">
          <div v-for="(item, index) in eventLineInfo" class="flex-box flex_d-column flex_a_i-center flex-item_f-1" :style="{ minWidth: '120px', cursor: item.executed ? 'pointer' : 'not-allowed' }">
            <div class="width-full flex-box flex_a_i-center gap_16">
              <div class="horizontal-line" :style="{ opacity: index > 0 ? 1 : 0 }"></div>
              <div :class="['normal-index', { 'third-alarm-index': item.status == 'fail', 'non-executed': item.status == 'unexecuted' }]">
                {{ index + 1 }}
              </div>
              <div class="horizontal-line" :style="{ opacity: index < eventLineInfo.length - 1 ? 1 : 0 }"></div>
            </div>
            <WelkinCopyBoard :text="item.event_name" :showIcon="false" class="event-name" :style="{ color: item.is_fail_event ? '#FD4348' : '#262626' }" />
            <div class="event-time" :style="{ color: item.is_fail_event ? '#FD4348' : '#262626' }" v-if="item.timestamp">{{ formatTime(item.timestamp) }}</div>
          </div>
        </div>

        <el-empty :description="$t('common.pp_empty')" v-else>
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>
    </div>

    <!-- 4. 电池仓信息 -->
    <div class="battery-cabin-info margin_t-16">
      <div class="flex-box flex_j_c-space-between">
        <div class="font-size-16 font-weight-bold margin_b-12">{{ $t('fireAlarm.pp_battery_cabin_info') }}</div>
        <div class="position-select margin_b-12">
          <el-select v-model="selectedPosition" placeholder="请选择仓位" @change="handlePositionChange">
            <el-option v-for="(values, key) in positionOptions[project]" :key="key" :label="key" :value="key"> </el-option>
          </el-select>
        </div>
      </div>

      <PlcChartGroup :data="batteryChartData" v-if="selectedPosition" />

      <el-empty :description="$t('common.pp_empty')" v-else>
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>
    <DocumentDialog v-model:dialogVisible="dialogVisible" :rowInfo="rowInfo" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { alarmLevelMap, alarmStatusMap, positionOptions, initChartData } from './constant';
import { formatTime, formatLocaleDate } from '~/utils';
import { apiGetDataList, apiGetDetailList } from '~/apis/fire-alarm';
import PlcChartGroup from '~/views/components/plc-chart-group/index.vue';
import ClearIcon from './icon/alarm-clear.vue';
import CreateIcon from './icon/alarm-create.vue';
import UnknownIcon from './icon/alarm-unknown.vue';
import AlarmIcon from '~/assets/svg/alarm-view.vue';
import DocumentDialog from '../fire-alarm/document-dialog.vue';
import _ from 'lodash';
const route = useRoute();
// 1. 基本信息
const basic_info = ref({} as any);
// 2. 告警表格数据
const alarmList = ref([]);
// 非消防告警相关
const notFireList = ref([]);
// 3. 告警详情弹窗
const dialogVisible = ref(false);
const rowInfo = ref({} as any);
const loading = ref(false);
// 3. 转运流程
const eventLineInfo = ref([] as any);
// 4. 电池仓信息图表（mock数据，结构与PlcChartGroup一致）
const batteryChartData = ref({} as any);
// 仓位选择
const selectedPosition = ref(null as any);
// 项目
const project = route.query.project as string;

// 处理仓位变化
const handlePositionChange = (position: keyof typeof positionOptions) => {
  if (position && positionOptions[project][position]) {
    // 这里可以根据选择的仓位更新图表数据或进行其他操作
    console.log('选择的仓位:', position);
    console.log('仓位对应的值:', positionOptions[project][position]);

    // 示例：可以在这里更新 batteryChartData
    getChartData();
  }
};

// 初始化数据
const initData = () => {
  // 如果 basic_info 中有 fire_space，则设置为默认选中的仓位
  if (basic_info.value && basic_info.value.fire_space) {
    const fireSpace = basic_info.value.fire_space as keyof typeof positionOptions;
    selectedPosition.value = fireSpace;
  } else {
    let selectedValue = '';
    if (project == 'PowerSwap2') {
      selectedValue = '#1';
    } else {
      selectedValue = 'C1';
    }
    selectedPosition.value = selectedValue;
  }
};

const handleDownload = (row: any) => {
  rowInfo.value = _.cloneDeep(row);
  rowInfo.value.parts.sort((a: any, b: any) => a.part_id - b.part_id);
  for (let i = 0; i < row.total_parts; i++) {
    const index = rowInfo.value.parts.findIndex((item: any) => item.part_id == i);
    if (index == -1) {
      rowInfo.value.parts.splice(i, 0, { part_id: i, file_url: '', upload_ts: 0 });
    } else {
      const splitUrl = rowInfo.value.parts[i].file_url.split('/');
      rowInfo.value.parts[i].file_name = splitUrl[splitUrl.length - 1];
    }
  }
  dialogVisible.value = true;
};

//请求详情页面
const getDetail = () => {
  apiGetDetailList({
    alarm_createts: route.query.alarm_ts as string,
    device_id: route.query.device_id as string,
    project: route.query.project as string,
  }).then((res) => {
    eventLineInfo.value = res.transfer_events;
    basic_info.value = res.basic_info;
    alarmList.value = res.alarm_infos;
    notFireList.value = res.not_fire_alarm_infos;
    initData();
    getChartData();
  });
};

//请求图表数据
const getChartData = () => {
  const key = selectedPosition.value;
  const dataId = positionOptions[project][key] || [];
  if (!dataId.length) {
    return;
  }
  apiGetDataList(
    {
      alarm_createts: route.query.alarm_ts as string,
      device_id: route.query.device_id as string,
      start_time: Number(route.query.alarm_ts) - 1000 * 60 * 60,
      end_time: route.query.alarm_ts,
      page: 1,
      size: 99999,
      data_id: dataId.join(',') || '',
    },
    String(route.query.project),
    String(route.query.device_id)
  ).then((res) => {
    const resData = res.data;
    const time = resData.map((item: any) => formatLocaleDate(item.timestamp));
    const A1 = [] as any; //电池充电状态
    const A2 = [] as any; //绝缘值
    const A3 = [] as any; //电芯温度
    const A4 = [] as any; //电池电压
    const A5 = [] as any; //电池类型
    const A6 = [] as any; //电池ID
    resData.map((item: any) =>
      Object.entries(item).forEach(([key, value]) => {
        switch (project) {
          case 'PUS4':
            if (/12$/g.test(key)) {
              if (value == -1) {
                value = null;
              }
              A5.push(value);
            }
            if (/30$/g.test(key)) {
              if (value == -9999.9) {
                value = null;
              }
              A2.push(value);
            }
            if (/08$/g.test(key)) A1.push(value);
            if (/37$/g.test(key)) A3.push(value);
            if (/21$/g.test(key)) A4.push(value);
            if (/01$/g.test(key)) A6.push(value);
            break;
          case 'PUS3':
            if (/12$/g.test(key)) {
              if (value == -1) {
                value = null;
              }
              A5.push(value);
            }
            if (/29$/g.test(key)) {
              if (value == -9999.9) {
                value = null;
              }
              A2.push(value);
            }
            if (/08$/g.test(key)) A1.push(value);
            if (/36$/g.test(key)) A3.push(value);
            if (/20$/g.test(key)) A4.push(value);
            if (/01$/g.test(key)) A6.push(value);
            break;
          default:
            if (/56$/g.test(key)) {
              if (value == -1) {
                value = null;
              }
              A5.push(value);
            }
            if (/15$/g.test(key)) {
              if (value == -9999.9) {
                value = null;
              }
              A2.push(value);
            }
            if (/44$/g.test(key)) A1.push(value);
            if (/22$/g.test(key)) A3.push(value);
            if (/06$/g.test(key)) A4.push(value);
            if (/00$/g.test(key)) A6.push(value);
            break;
        }
      })
    );
    batteryChartData.value = initChartData(time, A1, A2, A3, A4, A5, A6);
  });
};

onMounted(() => {
  getDetail();
  getChartData();
});
</script>

<style lang="scss" scoped>
.fire-alarm-detail-container {
  padding: 24px;
  background-color: #f8f8fa;
  .basic-info-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 24px;
    margin-bottom: 16px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #dcf2f3;
    .label-text {
      color: #8c8c8c;
      font-size: 14px;
      line-height: 22px;
      white-space: nowrap;
    }
    .value-text {
      color: #262626;
      display: flex;
      font-size: 14px;
      font-weight: 300;
      line-height: 22px;
    }
  }
  .alarm-status-container {
    height: 24px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 2px 10px;
    margin-top: 2px;
    border-radius: 14px;
    font-size: 13px;
    line-height: 20px;
    color: #fff;
  }
  .alarm-table-container {
    background: #fff;
    border-radius: 4px;
    padding: 24px;
    overflow: auto;
    border: 1px solid #dcf2f3;
  }
  .transfer-step-container {
    background: #fff;
    border-radius: 4px;
    padding: 24px;
    border: 1px solid #dcf2f3;
  }
  .battery-cabin-info {
    background: #fff;
    border-radius: 4px;
    padding: 24px;
    border: 1px solid #dcf2f3;
    .chart-card {
      width: 520px;
      background: #f9f9f9;
      border-radius: 4px;
      padding: 16px;
    }
  }

  .info-card {
    width: 100%;
    background-color: #fff;
    .head-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #262626;
    }
    .second-alarm-icon {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #ff772e;
      margin-right: 8px;
    }
    .third-alarm-icon {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #f83535;
      margin-right: 8px;
    }
    %circle-index {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .normal-index {
      @extend %circle-index;
      color: #fff;
      border: 1px solid rgb(103, 194, 58);
      background-color: rgb(103, 194, 58);
    }
    .second-alarm-index {
      @extend %circle-index;
      color: #ff772e;
      border: 1px solid #ff772e;
      background-color: rgba(255, 255, 255, 0.5);
    }
    .third-alarm-index {
      @extend %circle-index;
      color: #fff;
      border: 1px solid #f83535;
      background-color: #f83535;
    }
    .non-executed {
      @extend %circle-index;
      color: #262626;
      border: 1px solid #d9d9d9;
      background-color: rgba(255, 255, 255, 0.5);
    }
    .horizontal-line {
      flex: 1;
      height: 1px;
      background-color: #d9d9d9;
    }
    :deep(.event-name) {
      width: 100%;
      display: flex;
      justify-content: center;
      font-size: 16px;
      line-height: 24px;
      margin-top: 8px;
      margin-bottom: 4px;
    }
    .event-time {
      font-size: 12px;
      line-height: 18px;
      color: #595959;
    }
    ::-webkit-scrollbar {
      height: 4px;
    }
    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 10px;
    }
  }
}
</style>
