export const alarmLevelMap = {
  0: {
    name: 'alarmList.pp_unknown_alarm',
    color: '#595959',
    background: '#EBECEE',
  },
  1: {
    name: 'alarmList.pp_first_level',
    color: '#FD8C08',
    background: '#FFF8DD',
  },
  2: {
    name: 'alarmList.pp_second_level',
    color: '#FF772E',
    background: '#FFF4E8',
  },
  3: {
    name: 'alarmList.pp_third_level',
    color: '#E83030',
    background: '#FFF2F0',
  },
} as any;

export const alarmStatusMap = {
  1: {
    name: 'alarmList.pp_cleared',
    background: '#67C23A',
  },
  2: {
    name: 'alarmList.pp_alarming',
    background: '#FF7575',
  },
  3: {
    name: 'alarmList.pp_unknown',
    background: '#888AF7',
  },
} as any;

export const stateMachineMap1 = {
  PowerSwap2: 5001,
  PUS3: 205109,
  PUS4: 4009,
} as any;

export const stateMachineMap2 = {
  PowerSwap2: 282,
  PUS3: 10,
  PUS4: 4004,
} as any;

export const positionOptions = {
  PowerSwap2: {
    '#1': [501144, 501115, 501122, 501106, 501156, 501100],
    '#2': [502144, 502115, 502122, 502106, 502156, 502100],
    '#3': [503144, 503115, 503122, 503106, 503156, 503100],
    '#4': [504144, 504115, 504122, 504106, 504156, 504100],
    '#5': [505144, 505115, 505122, 505106, 505156, 505100],
    '#6': [506144, 506115, 506122, 506106, 506156, 506100],
    '#7': [507144, 507115, 507122, 507106, 507156, 507100],
    '#8': [508144, 508115, 508122, 508106, 508156, 508100],
    '#9': [509144, 509115, 509122, 509106, 509156, 509100],
    '#10': [510144, 510115, 510122, 510106, 510156, 510100],
    '#11': [511144, 511115, 511122, 511106, 511156, 511100],
    '#12': [512144, 512115, 512122, 512106, 512156, 512100],
    '#13': [513144, 513115, 513122, 513106, 513156, 513100],
  },
  PUS3: {
    C1: [1508, 1029, 1036, 1020, 1012, 1001],
    C2: [2508, 2029, 2036, 2020, 2012, 2001],
    C3: [3508, 3029, 3036, 3020, 3012, 3001],
    C4: [4508, 4029, 4036, 4020, 4012, 4001],
    C5: [5508, 5029, 5036, 5020, 5012, 5001],
    C6: [6508, 6029, 6036, 6020, 6012, 6001],
    C7: [7508, 7029, 7036, 7020, 7012, 7001],
    C8: [8508, 8029, 8036, 8020, 8012, 8001],
    C9: [9508, 9029, 9036, 9020, 9012, 9001],
    C10: [10508, 10029, 10036, 10020, 10012, 10001],
    A1: [11508, 11029, 11036, 11020, 11012, 11001],
    A2: [12508, 12029, 12036, 12020, 12012, 12001],
    A3: [13508, 13029, 13036, 13020, 13012, 13001],
    A4: [14508, 14029, 14036, 14020, 14012, 14001],
    A5: [15508, 15029, 15036, 15020, 15012, 15001],
    A6: [16508, 16029, 16036, 16020, 16012, 16001],
    A7: [17508, 17029, 17036, 17020, 17012, 17001],
    A8: [18508, 18029, 18036, 18020, 18012, 18001],
    A9: [19508, 19029, 19036, 19020, 19012, 19001],
    A10: [20508, 20029, 20036, 20020, 20012, 20001],
    A11: [21508, 21029, 21036, 21020, 21012, 21001],
  },
  PUS4: {
    C1: [372008, 374030, 374037, 374021, 374012, 374001],
    C2: [392008, 394030, 394037, 394021, 394012, 394001],
    C3: [412008, 414030, 414037, 414021, 414012, 414001],
    C4: [432008, 434030, 434037, 424021, 424012, 424001],
    C5: [452008, 454030, 454037, 454021, 454012, 454001],
    C6: [472008, 474030, 474037, 474021, 474012, 474001],
    C7: [492008, 494030, 494037, 494021, 494012, 494001],
    C8: [512008, 514030, 514037, 514021, 514012, 514001],
    C9: [532008, 534030, 534037, 534021, 534012, 534001],
    C10: [552008, 554030, 554037, 554021, 554012, 554001],
    C11: [572008, 574030, 574037, 574021, 574012, 574001],
    C12: [592008, 594030, 594037, 594021, 594012, 594001],
    A1: [612008, 614030, 614037, 614021, 614012, 614001],
    A2: [632008, 634030, 634037, 634021, 634012, 634001],
    A3: [652008, 654030, 654037, 654021, 654012, 654001],
    A4: [672008, 674030, 674037, 674021, 674012, 674001],
    A5: [692008, 694030, 694037, 694021, 694012, 694001],
    A6: [712008, 714030, 714037, 714021, 714012, 714001],
    A7: [722008, 734030, 734037, 734021, 734012, 734001],
    A8: [752008, 754030, 754037, 754021, 754012, 754001],
    A9: [772008, 774030, 774037, 774021, 774012, 774001],
    A10: [792008, 794030, 794037, 794021, 794012, 794001],
    A11: [812008, 814030, 814037, 814021, 814012, 814001],
  },
} as any;
// 电池类型
export const batteryType = {
  0: '70度',
  1: '70度',
  2: '84度',
  3: '70度',
  4: '70度',
  6: '100度',
  7: '70度',
  8: '75度',
  9: '84度',
  10: '150度',
  11: '75度',
  12: '75度',
  13: '100度',
  14: '75度',
  16: '50度',
  17: '50度',
  66: '102度',
  68: '75度',
  71: '85度',
  73: '60度',
  74: '60度',
  75: '60度',
  77: '85度',
  79: '60度',
  200: '42度',
} as any;

const chargeTextMap = {
  '1': '充电',
  '0': '空闲',
  '2': '充电完成',
  '3': '充电错误',
  '4': '防止异常的热积累机制',
  '5': '保留',
  '6': '保留',
  '7': '无效',
} as any;
// 图表数据
export const initChartData = (time: any, A1: any, A2: any, A3: any, A4: any, A5: any, A6: any) => {
  const chartData = {
    time: time,
    group: [
      {
        key: 'torque1',
        color: ['#6697FF', '#5ECFFF'],
        legend: {
          data: ['电池充电状态', '电池电压'],
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params: any) => {
            const param1 = params[0].value;
            let text = chargeTextMap[param1] || '';
            const str = `${params[0].name}<br>
            <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:#6697FF;margin-right: 5px"></span>
            ${params[0].seriesName}: ${text} 
            <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:#5ECFFF;margin-left: 5px"></span>
            ${params[1].seriesName}: ${params[1].value} `;
            return str;
          },
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              padding: [0, 0, 0, 50], // 调整内边距
            },
            name: '电池充电状态',
            position: 'left',
            axisLine: {
              lineStyle: {},
            },
            axisLabel: {
              fontSize: 10,
              formatter: (value: number) => {
                let text = chargeTextMap[value] || '';
                return text;
              }, // 自定义标签格式
            },
          },
          {
            type: 'value',
            name: '电池电压',
            position: 'right',
            axisLine: {
              lineStyle: {},
            },
          },
        ],
        data: [
          { type: 'line', name: '电池充电状态', symbol: 'none', yAxisIndex: 0, lineStyle: { width: 2 }, data: A1 },
          { type: 'line', name: '电池电压', symbol: 'none', yAxisIndex: 1, lineStyle: { width: 2 }, data: A4 },
        ],
      },
      {
        key: 'position1',
        color: ['#4ECDC4', '#55EFCE'],
        legend: {
          data: ['电芯温度', '绝缘值'],
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              padding: [0, 0, 0, 20], // 调整内边距
            },
            name: '电芯温度',
            position: 'left',
            axisLine: {
              lineStyle: {},
            },
          },
          {
            type: 'value',
            name: '绝缘值',
            nameTextStyle: {
              padding: [0, 35, 0, 0], // 调整内边距
            },
            position: 'right',
            axisLine: {
              lineStyle: {},
            },
          },
        ],
        data: [
          { type: 'line', name: '电芯温度', yAxisIndex: 0, symbol: 'none', lineStyle: { width: 2 }, data: A3 },
          { type: 'line', name: '绝缘值', yAxisIndex: 1, symbol: 'none', connectNulls: false, lineStyle: { width: 2 }, data: A2 },
        ],
      },
      {
        key: 'position3',
        color: ['#91cc75', '#5470c6'],
        tooltip: {
          trigger: 'axis',
          formatter: (params: any) => {
            const param1 = params[0].value;
            let text = '';
            if (param1 == -1 || !param1) {
              text = '-';
            } else {
              text = batteryType[param1];
            }
            const str = `${params[0].name}<br>
            ${params[0].seriesName}: ${text} 
            ${params[1].seriesName}: ${params[1].value ? params[1].value : '-'}`;
            return str;
          },
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              padding: [0, 0, 0, 80], // 调整内边距
            },
            name: '仓位电池信息',
            position: 'left',
            axisTick: { show: false }, // 隐藏刻度线
            axisLabel: { show: false }, // 隐藏刻度标签
            splitLine: { show: false }, // 隐藏网格线（可选）
          },
          {
            type: 'value',
            name: '',
            nameTextStyle: {
              padding: [0, 0, 0, 0], // 调整内边距
            },
            position: 'right',
            axisTick: { show: false }, // 隐藏刻度线
            axisLabel: { show: false }, // 隐藏刻度标签
            splitLine: { show: false }, // 隐藏网格线（可选）
          },
        ],
        data: [
          {
            type: 'line',
            name: '电池类型',
            yAxisIndex: 0,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1, // 垂直渐变（从上到下）
                colorStops: [
                  { offset: 0, color: '#91cc75' }, // 顶部颜色
                  { offset: 1, color: 'rgba(145, 204, 117,0.1)' }, // 底部透明色
                ],
              },
              opacity: 0.3, // 整体透明度
            },
            symbol: 'none',
            lineStyle: { width: 2 },
            data: A5,
          },
          { type: 'line', name: '电池ID', yAxisIndex: 1, symbol: 'none', connectNulls: false, lineStyle: { width: 2 }, data: A6 },
        ],
      },
    ],
  };
  return chartData;
};
