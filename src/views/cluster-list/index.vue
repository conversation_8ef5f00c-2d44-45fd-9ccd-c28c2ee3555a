<template>
  <div class="edge-service-container">
    <!-- header -->
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('edgeCloud.pp_cloud') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t('edgeCloud.pp_cluster_list') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">
          {{ $t('edgeCloud.pp_cluster_list') }}
        </div>
      </div>
      <div class="header-right">
        <el-radio-group v-model="searchForm.device_type" @change="handleChangeType">
          <el-radio :label="item.value" v-for="item in deviceTypeOptions">{{ $t(item.label) }}</el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- body -->
    <div class="swap-page-container">
      <div class="search-container">
        <span class="search-item-label">
          {{ $t('edgeCloud.pp_cluster') }}
        </span>
        <el-select v-model="searchForm.device_id" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 30%; margin-right: 20px">
          <el-option v-for="item in deviceNameOptions" :key="item.device_id" :label="item.device_id + ' - ' + item.description" :value="item.device_id" />
        </el-select>
        <div class="search-button">
          <el-button @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
          <el-button @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
        </div>
      </div>
      <div class="swap-table-container">
        <el-table
          :data="tableData"
          row-key="device_id"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
          @expand-change="handleExpandTable"
        >
          <el-table-column type="expand">
            <template #default="scope">
              <div class="inner-container">
                <!-- 内层表格 -->
                <el-table border :data="scope.row.nodeList" row-key="name">
                  <el-table-column prop="name" :label="`${$t('edgeCloud.pp_node_name')}`" fixed min-width="260" show-overflow-tooltip />
                  <!-- <el-table-column prop="id" :label="`${$t('edgeCloud.pp_node_id')}`" /> -->
                  <el-table-column prop="subsystem" :label="`${$t('edgeCloud.pp_sub_system')}`" min-width="100" show-overflow-tooltip />
                  <el-table-column prop="status" :label="`${$t('edgeCloud.pp_running_status')}`" min-width="140" show-overflow-tooltip />
                  <el-table-column :label="`${$t('edgeCloud.pp_resource_usage')}`" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                      <div>
                        <span>CPU：</span>
                        <span>{{ scope.row.cpu_usage + ' / ' + scope.row.cpu_total }}</span>
                      </div>
                      <div>
                        <span>{{$t('edgeCloud.pp_memory')}}：</span>
                        <span>{{ scope.row.memory_usage + ' / ' + scope.row.memory_total }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="`${$t('edgeCloud.pp_resource_allocation')}`" min-width="170" show-overflow-tooltip>
                    <template #default="scope">
                      <div>
                        <span>CPU：</span>
                        <span>{{ scope.row.cpu_allocated }}</span>
                      </div>
                      <div>
                        <span>{{$t('edgeCloud.pp_memory')}}：</span>
                        <span>{{ scope.row.memory_allocated }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="`${$t('common.pp_operation')}`" min-width="120" show-overflow-tooltip>
                    <template #default="scope">
                      <span @click="handleStopSchedule(scope.$index, scope.row)" class="edit-text">{{ $t('edgeCloud.pp_stop_schedule') }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <!-- <div class="pagination-container children-page">
                <Page v-if="(scope.row.nodeList && scope.row.nodeList.length > 0) || !scope.row.nodeList" :page="scope.row.page" @change="(val) => handleChildPageChange(val, scope.row)" />
              </div> -->
            </template>
          </el-table-column>
          <el-table-column :label="`${$t('edgeCloud.pp_cluster_name')}`">
            <template #default="scope">
              <div class="pointer-column">
                <span class="point-span" @click="handleJump(scope.$index, scope.row)">
                  {{ scope.row.description }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="device_id" :label="`${$t('edgeCloud.pp_cluster_id')}`">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>
          <el-table-column prop="nodes_count" :label="`${$t('edgeCloud.pp_node_number')}`" />
        </el-table>
        <div class="pagination-container">
          <Page v-if="(tableData && tableData.length > 0) || !tableData" :page="pages" @change="handlePageChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, reactive, onBeforeMount, watch} from 'vue'
import {page} from '../../constvars/page'
import {removeNullProp} from '../../utils'
import {useRoute, useRouter} from 'vue-router'
import {useI18n} from 'vue-i18n'
import {ElMessage} from 'element-plus'
import {apiGetDeviceNameMap} from '../../apis/device-management'
import {apiGetClusterList, apiGetNodeList, apiPostStopSchedule} from '../../apis/edge-cloud'
import _ from 'lodash'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const pages = ref(_.cloneDeep(page))
const tableData = ref([])
const deviceNameOptions = ref([] as any)
const deviceTypeOptions = ref([
  {
    label: 'menu.pp_swap_station2',
    value: 'PowerSwap2'
  },
  {
    label: 'menu.pp_swap_station3',
    value: 'PUS3'
  }
])
const searchForm = reactive({
  device_id: '',
  device_type: 'PUS3',
  page: 1,
  size: 10
})

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  searchForm.device_id = ''
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  pages.value.size = 10
  getDeviceList()
}

/**
 * @description: 搜索
 * @param {*} updateRoute
 * @return {*}
 */
const getDeviceList = async (updateRoute = true) => {
  searchForm.page = pages.value.current
  searchForm.size = pages.value.size
  if (updateRoute) {
    router.push({
      path: `/cloud/cluster-list`,
      query: {...removeNullProp(searchForm)}
    })
  }
  loading.value = true
  try {
    const res = await apiGetClusterList(searchForm, searchForm.device_type)
    pages.value.total = res.total
    tableData.value = res.data
  } catch (error) {}
  loading.value = false
}

/**
 * @description: 点击跳转至详情页
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleJump = (index: number, row: any) => {
  sessionStorage.setItem('cloud-list', JSON.stringify({...removeNullProp(searchForm)}))
  router.push({
    path: `/cloud/cluster-list/cluster-detail/${row.device_id}`,
    query: {device_type: searchForm.device_type}
  })
}

/**
 * @description: 停止调度
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleStopSchedule = async (index: number, row: any) => {
  const params = {schedulable: false}
  const res = await apiPostStopSchedule(searchForm.device_type, row.name, params)
  if (!res.err_code) {
    ElMessage.success(t('edgeCloud.pp_stop_success'))
  } else {
    ElMessage.error(t('edgeCloud.pp_stop_error'))
  }
}

/**
 * @description: 外层分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getDeviceList()
}

/**
 * @description: 内层分页
 * @param {*} argPage
 * @param {*} row
 * @return {*}
 */
// const handleChildPageChange = (argPage: any, row: any) => {
//   row.page.current = argPage.current
//   row.page.size = argPage.size
// }

/**
 * @description: 获取集群map
 * @return {*}
 */
const getDeviceNameMapping = async () => {
  const res = await apiGetDeviceNameMap(searchForm.device_type)
  deviceNameOptions.value = res.data
}

/**
 * @description: 切换换电站2/3
 * @return {*}
 */
const handleChangeType = () => {
  handleReset()
  getDeviceNameMapping()
}

/**
 * @description: 展开行
 * @param {*} row
 * @return {*}
 */
const handleExpandTable = async (row: any) => {
  const res = await apiGetNodeList(searchForm.device_type, row.device_id)
  row.nodeList = res.data
}

/**
 * @description: 初始化筛选条件
 * @return {*}
 */
const initWeb = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      searchForm.device_id = !!initParams.device_id ? initParams.device_id : ''
      searchForm.device_type = !!initParams.device_type ? initParams.device_type : 'PUS3'
      getDeviceNameMapping()
      getDeviceList(false)
    }
  },
  {immediate: true}
)

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.edge-service-container {
  .edit-text {
    color: #00bebe;
    &:hover {
      cursor: pointer;
      color: #66d2d2;
    }
  }
  // .children-page {
  //   width: auto;
  //   margin: 8px 50px 10px;
  //   ::v-deep(.el-pagination) {
  //     margin: 0;
  //   }
  // }
  .inner-container {
    padding: 20px 50px;
  }
}
</style>
