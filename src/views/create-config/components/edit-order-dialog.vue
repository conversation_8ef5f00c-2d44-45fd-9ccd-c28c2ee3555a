<template>
  <div class="edit-order-dialog">
    <el-dialog v-model="editOrderDialogVisible" :title="editOrderDialogTitle" @close="handleCloseEditOrderDialog" class="common-dialog" width="400px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="editOrderForm" ref="editOrderDialogFormRef" class="vertical-form" label-position="top" require-asterisk-position="right">
        <el-form-item label="用户到达时间" prop="user_arrival_time" :rules="{ required: true, validator: timeValidator, trigger: 'change' }">
          <el-date-picker v-model="editOrderForm.user_arrival_time" type="datetime" :clearable="false" placeholder="请选择" format="YYYY/MM/DD HH:mm:ss" value-format="x" :disabled-date="disabledDate" style="width: 100%" />
        </el-form-item>
        <el-form-item label="进站电池度数" prop="battery_rated_kwh" :rules="{ required: true, message: '请选择进站电池度数', trigger: 'change' }">
          <el-select v-model="editOrderForm.battery_rated_kwh" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in route.query.project == 'PowerSwap2' ? powerSwap2BatteryTypeOptions : batteryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <div class="flex-box gap_16">
          <el-form-item label="是否静置" prop="battery_rest_label" :rules="{ required: true, message: '请选择是否静置', trigger: 'change' }" style="width: 50%">
            <el-select v-model="editOrderForm.battery_rest_label" placeholder="请选择" style="width: 100%">
              <el-option label="是" :value="2" />
              <el-option label="否" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="进站电池SOC %" prop="battery_soc" :rules="{ validator: socValidator, trigger: 'blur' }" style="width: 50%">
            <el-input v-model="editOrderForm.battery_soc" placeholder="请输入" clearable oninput="value = value.replace(/[^0-9]/g,'')" style="width: 100%" />
          </el-form-item>
        </div>
        <div class="flex-box gap_16">
          <el-form-item label="进站电池温度 °C" prop="pack_max_temperature" :rules="{ validator: temValidator, trigger: 'blur' }" style="width: 50%">
            <el-input v-model="editOrderForm.pack_max_temperature" placeholder="请输入" clearable @input="handleInput" style="width: 100%" />
          </el-form-item>
          <el-form-item label="目标电池度数" prop="target_battery_rated_kwh">
            <el-select v-model="editOrderForm.target_battery_rated_kwh" placeholder="请选择" clearable style="width: 100%">
              <el-option v-for="item in route.query.project == 'PowerSwap2' ? powerSwap2BatteryTypeOptions : batteryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex-box gap_16">
          <el-form-item label="车辆产权" prop="user_ownership" :rules="{ required: true, message: '请选择车辆产权', trigger: 'change' }" style="width: 50%">
            <el-select v-model="editOrderForm.user_ownership" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in userOwnershipOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="电池产权" prop="battery_ownership" :rules="{ required: true, message: '请选择电池产权', trigger: 'change' }" style="width: 50%">
            <el-select v-model="editOrderForm.battery_ownership" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in batteryOwnershipOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex-box flex_j_c-flex-end flex_a_i-center">
          <el-button @click="handleClose" class="text-button">取消</el-button>
          <el-button type="primary" style="margin-left: 4px" @click="handleConfirmEdit">确认</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { batteryTypeOptions, powerSwap2BatteryTypeOptions } from './constant'

const props = defineProps({
  editOrderDialogVisible: Boolean,
  editOrderDialogTitle: String,
  socValidator: Function,
  temValidator: Function,
  batteryOwnershipOptions: Array,
  userOwnershipOptions: Array,
  checkTimeValid: {
    type: Function,
    required: true
  },
  timeRange: {
    type: Array as () => number[],
    required: true
  },
  editOrderForm: {
    type: Object,
    default: {}
  }
})

const route = useRoute()

const emits = defineEmits(['update:editOrderDialogVisible', 'handleConfirmEditOrder', 'handleCloseEditOrder'])

const editOrderDialogFormRef = ref()

const disabledDate = (time: any) => {
  let date1 = new Date(props.timeRange[0])
  date1.setHours(0, 0, 0, 0)
  let startOfDay = date1.getTime()
  let date2 = new Date(props.timeRange[1])
  date2.setHours(0, 0, 0, 0)
  date2.setDate(date2.getDate() + 1)
  let startOfNextDay = date2.getTime()
  return time < startOfDay || time >= startOfNextDay
}

const timeValidator = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error('请选择用户到达时间'))
  } else if (value < props.timeRange[0] || value >= props.timeRange[1]) {
    return callback(new Error('必须位于仿真时段内'))
  } else if (!props.checkTimeValid(value)) {
    return callback(new Error('必须位于换电运营时段内'))
  } else {
    callback()
  }
}

const handleInput = (value: any) => {
  const regex = /^-?\d*$/
  if (regex.test(value)) {
    props.editOrderForm.pack_max_temperature = value
  } else {
    nextTick(() => {
      props.editOrderForm.pack_max_temperature = props.editOrderForm.pack_max_temperature.replace(/[^\d-]|(?!^)-/g, '')
    })
  }
}

/**
 * @description: 关闭
 * @return {*}
 */
const handleCloseEditOrderDialog = () => {
  emits('handleCloseEditOrder', editOrderDialogFormRef.value)
}

/**
 * @description: 取消
 * @return {*}
 */
const handleClose = () => {
  emits('update:editOrderDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirmEdit = async () => {
  if (!editOrderDialogFormRef.value) return
  await editOrderDialogFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      emits('handleConfirmEditOrder', props.editOrderForm)
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>
