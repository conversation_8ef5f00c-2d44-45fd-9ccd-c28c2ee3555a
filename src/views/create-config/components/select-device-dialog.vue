<template>
  <div class="select-device-dialog">
    <el-dialog v-model="deviceDialogVisible" title="设备选择" @open="handleOpenDeviceDialog" @close="handleCloseDeviceDialog" class="common-dialog" width="568px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="flex-box margin_b-16">
        <span class="form-label">设备名称</span>
        <el-input v-model="device" clearable :placeholder="$t('common.pp_enter')" class="width-full" />
        <el-button type="primary" style="margin-left: 24px" @click="handleSearchDevice">搜索</el-button>
      </div>

      <div class="table-container">
        <el-table :data="list" v-loading="loading" @select="handleSelect" ref="multipleTableRef" :row-key="getRowKey" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column :reserve-selection="true" type="selection" width="40" />
          <el-table-column prop="description" label="设备名称" width="240" />
          <el-table-column prop="device_id" label="设备ID" width="240" />
        </el-table>
        <div class="pagination-box">
          <Page :page="pages" :pagerCount="5" :layout="'prev, pager, next'" @change="handlePageChange" class="mini-page" />
        </div>
      </div>

      <div class="flex-box flex_j_c-flex-end margin_t-14">
        <el-button @click="handleCancel" class="text-button">取消</el-button>
        <el-button type="primary" style="margin-left: 4px" @click="handleConfirmSelectDevice">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { page } from '~/constvars/page'
import { apiGetDeviceList } from '~/apis/run-list'
import { ElMessage } from 'element-plus'
import _ from 'lodash'

const props = defineProps({
  deviceDialogVisible: Boolean,
  createProject: String,
  selectDeviceList: {
    type: Array,
    default: []
  }
})

const emits = defineEmits(['update:deviceDialogVisible', 'handleConfirmSelectDevice'])

const multipleTableRef = ref()
const device = ref('')
const loading = ref(false)
const list = ref([] as any)
const multipleSelection = ref([] as any)
const pages = ref(_.cloneDeep(page))

const getRowKey = (row: any) => {
  return row.device_id
}

/**
 * @description: 单选，手动维护状态
 * @param {*} selection
 * @param {*} row
 * @return {*}
 */
const handleSelect = (selection: any, row: any) => {
  const index = multipleSelection.value.findIndex((item: any) => item.device_id == row.device_id)
  if (index > -1) {
    multipleSelection.value.splice(index, 1)
  } else {
    multipleSelection.value.push(row)
  }
}

/**
 * @description: 获取设备列表
 * @return {*}
 */
const getList = async () => {
  const params = {
    fuzzy_device_id_or_name: device.value,
    page_no: pages.value.current,
    page_size: pages.value.size
  }
  loading.value = true
  try {
    const res = await apiGetDeviceList(params, props.createProject)
    loading.value = false
    list.value = res.data || []
    pages.value.total = res.total
    props.selectDeviceList.map((row: any) => {
      if (list.value.find((item: any) => item.device_id == row.device_id)) multipleTableRef.value!.toggleRowSelection(row, undefined)
    })
  } catch (error: any) {
    loading.value = false
    ElMessage.error(error)
  }
}

/**
 * @description: 点击搜索
 * @return {*}
 */
const handleSearchDevice = () => {
  pages.value.current = 1
  getList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:deviceDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirmSelectDevice = () => {
  emits('handleConfirmSelectDevice', multipleSelection.value)
}

/**
 * @description: 打开弹窗
 * @return {*}
 */
const handleOpenDeviceDialog = () => {
  device.value = ''
  pages.value.size = 5
  multipleSelection.value = _.cloneDeep(props.selectDeviceList)
  handleSearchDevice()
}

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleCloseDeviceDialog = () => {
  if (multipleTableRef.value) multipleTableRef.value.clearSelection()
  emits('update:deviceDialogVisible', false)
}
</script>

<style lang="scss" scoped>
.select-device-dialog {
  :deep(.form-label) {
    color: #595959;
    margin-right: 8px;
    white-space: nowrap;
    display: flex;
    align-items: center;
  }
  :deep(.el-table-column--selection.is-leaf .el-checkbox) {
    display: none;
  }
  :deep(.pagination-box) {
    margin-top: 14px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
