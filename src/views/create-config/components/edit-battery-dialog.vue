<template>
  <div class="edit-battery-dialog">
    <el-dialog v-model="editBatteryDialogVisible" v-if="editBatteryDialogVisible" @close="handleCloseEditBatteryDialog" class="common-dialog" width="400px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <template #header>
        <div class="header-title">
          编辑仓位号 <span class="font-weight-bold">{{ editBatteryForm.slot_id }}</span>
        </div>
      </template>
      <el-form :model="editBatteryForm" ref="editBatteryDialogFormRef" class="vertical-form" label-position="top" require-asterisk-position="right">
        <el-form-item
          label="电池SOC %"
          prop="battery_soc"
          :rules="[
            { required: true, message: '请输入电池SOC', trigger: 'blur' },
            { validator: socValidator, trigger: 'blur' }
          ]"
        >
          <el-input v-model="editBatteryForm.battery_soc" placeholder="请输入" clearable oninput="value = value.replace(/[^0-9]/g,'')" style="width: 100%" />
        </el-form-item>
        <div class="flex-box gap_16">
          <el-form-item label="电池度数" prop="battery_rated_kwh" :rules="{ required: true, message: '请选择电池度数', trigger: 'change' }" style="width: 50%">
            <el-select v-model="editBatteryForm.battery_rated_kwh" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in route.query.project == 'PowerSwap2' ? powerSwap2BatteryTypeOptions : batteryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="电池温度 °C" prop="pack_max_temperature" :rules="{ validator: temValidator, trigger: 'blur' }" style="width: 50%">
            <el-input v-model="editBatteryForm.pack_max_temperature" placeholder="请输入" @input="handleInput" clearable style="width: 100%" />
          </el-form-item>
        </div>
        <div class="flex-box gap_16">
          <el-form-item label="电池产权" prop="battery_ownership" :rules="{ required: true, message: '请选择电池产权', trigger: 'change' }" style="width: 50%">
            <el-select v-model="editBatteryForm.battery_ownership" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in batteryOwnershipOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="电池充电截止SOC %" prop="charging_stop_soc" :rules="{ validator: socValidator, trigger: 'blur' }" style="width: 50%">
            <el-input v-model="editBatteryForm.charging_stop_soc" placeholder="请输入" clearable oninput="value = value.replace(/[^0-9]/g,'')" style="width: 100%" />
          </el-form-item>
        </div>
        <el-form-item label="电池定位标签" prop="battery_id" :rules="{ validator: batteryIdValidator, trigger: 'blur' }">
          <el-input v-model="editBatteryForm.battery_id" placeholder="请输入" clearable style="width: 100%" />
        </el-form-item>
        <div class="flex-box flex_j_c-flex-end flex_a_i-center">
          <el-button @click="handleClose" class="text-button">取消</el-button>
          <el-button type="primary" style="margin-left: 4px" @click="handleConfirmEdit">确认</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { batteryTypeOptions, powerSwap2BatteryTypeOptions } from './constant'

const props = defineProps({
  editBatteryDialogVisible: Boolean,
  socValidator: Function,
  batteryIdValidator: Function,
  temValidator: Function,
  batteryOwnershipOptions: Array,
  editBatteryForm: {
    type: Object,
    default: {}
  }
})

const route = useRoute()

const emits = defineEmits(['update:editBatteryDialogVisible', 'handleConfirmEditBattery'])

const editBatteryDialogFormRef = ref()

const handleInput = (value: any) => {
  const regex = /^-?\d*$/
  if (regex.test(value)) {
    props.editBatteryForm.pack_max_temperature = value
  } else {
    nextTick(() => {
      props.editBatteryForm.pack_max_temperature = props.editBatteryForm.pack_max_temperature.replace(/[^\d-]|(?!^)-/g, '')
    })
  }
}

/**
 * @description: 关闭
 * @return {*}
 */
const handleCloseEditBatteryDialog = () => {
  if (!editBatteryDialogFormRef.value) return
  editBatteryDialogFormRef.value.resetFields()
  emits('update:editBatteryDialogVisible', false)
}

/**
 * @description: 取消
 * @return {*}
 */
const handleClose = () => {
  emits('update:editBatteryDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirmEdit = async () => {
  if (!editBatteryDialogFormRef.value) return
  await editBatteryDialogFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      emits('handleConfirmEditBattery', props.editBatteryForm)
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style lang="scss" scoped>
.edit-battery-dialog {
  font-family: Blue Sky Standard;
  :deep(.header-title) {
    font-size: 18px;
    font-weight: 420;
    line-height: 26px;
    color: #1f1f1f;
  }
}
</style>
