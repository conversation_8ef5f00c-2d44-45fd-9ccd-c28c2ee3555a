<template>
  <div class="batch-import-device">
    <el-dialog v-model="batchImportVisible" title="批量导入" @close="handleCloseBatchDialog" class="common-dialog" :width="showTable ? '520px' : '400px'" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-8">
        <span class="form-label" v-if="!showTable">导入文件</span>
        <span v-else class="form-label">有效设备 <span class="bold-text">{{ tableList.length }}</span> 个</span>
        <el-button class="cancel-button" @click="handleDownloadTemplate" style="height: 24px">模板下载</el-button>
      </div>

      <el-upload v-model:file-list="file" v-if="!showTable" drag accept=".csv" ref="uploadRef" :limit="1" :on-exceed="handleExceed" class="width-full" action="#" :auto-upload="false">
        <div class="el-upload__text">
          <UploadIcon />
          <span class="margin_l-6">请上传 <em>CSV文件</em></span>
        </div>
      </el-upload>

      <div class="table-container" v-else>
        <el-table :data="tableList.slice((pages.current - 1) * pages.size, pages.current * pages.size)" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column prop="description" label="设备名称" width="236">
            <template #default="{ row }">
              <span>{{ row.description ? row.description : '未命名设备' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="device_id" label="设备ID" width="236" />
        </el-table>
        <div class="pagination-box" v-if="tableList.length > 5">
          <Page :page="pages" :pagerCount="5" :layout="'prev, pager, next'" @change="handlePageChange" class="mini-page" />
        </div>
      </div>

      <div class="flex-box flex_j_c-flex-end margin_t-16">
        <el-button @click="handleCancel" class="text-button">取消</el-button>
        <el-button type="primary" :loading="loading" style="margin-left: 4px" @click="handleConfirmBatchImportDevice">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { miniPage } from '~/constvars/page'
import { apiPostDeviceFile } from '~/apis/config-list'
import UploadIcon from '~/views/run-list/component/icon/upload-icon.vue'
import { genFileId, ElMessage } from 'element-plus'
import _ from 'lodash'

const props = defineProps({
  batchImportVisible: Boolean,
  createProject: String
})

const emits = defineEmits(['update:batchImportVisible', 'handleConfirmBatchImportDevice'])

const uploadRef = ref()
const loading = ref(false)
const showTable = ref(false)
const file = ref([] as any)
const tableList = ref([] as any)
const pages = ref(_.cloneDeep(miniPage))

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
}

/**
 * @description: 覆盖上一个文件
 * @param {*} files
 * @return {*}
 */
const handleExceed = (files: any) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
}

/**
 * @description: 下载模板
 * @return {*}
 */
const handleDownloadTemplate = () => {
  window.open('https://cdn-welkin-public.nio.com/welkin/2024/06/20/cc542230-7deb-4613-b6a2-e9a4f6beb5af.csv')
}

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:batchImportVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirmBatchImportDevice = async () => {
  if (showTable.value) {
    emits('handleConfirmBatchImportDevice', tableList.value)
  } else {
    if (file.value.length == 0) {
      ElMessage.warning('请上传csv文件')
    } else {
      const params = {
        file: file.value[0].raw,
        project: props.createProject
      }
      loading.value = true
      try {
        const res = await apiPostDeviceFile(params)
        loading.value = false
        if (res.err_code === 0) {
          ElMessage.success('上传成功')
          tableList.value = res.data || []
          showTable.value = true
        } else if (res.err_code === -1) {
          tableList.value = res.data || []
          showTable.value = true
          ElMessage.error(res.message)
        } else {
          ElMessage.error(res.message)
        }
      } catch (error: any) {
        loading.value = false
        ElMessage.error(error)
      }
    }
  }
}

/**
 * @description: 关闭
 * @return {*}
 */
const handleCloseBatchDialog = () => {
  showTable.value = false
  file.value = []
  emits('update:batchImportVisible', false)
}
</script>

<style lang="scss" scoped>
.batch-import-device {
  :deep(.form-label) {
    color: #595959;
    font-size: 14px;
    line-height: 22px;
    .bold-text {
      color: #262626;
      font-weight: bold;
    }
  }
  :deep(.el-upload-dragger) {
    border: 1px dashed #d9d9d9;
    width: 352px;
    height: 96px;
    border-radius: 4px;
    .el-upload__text {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #262626;
    }
    .el-upload__text em {
      color: #01a0ac;
    }
  }
  :deep(.el-upload-list__item-file-name) {
    color: #595959;
    font-weight: normal;
  }
  :deep(.pagination-box) {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
