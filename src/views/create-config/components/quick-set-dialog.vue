<template>
  <div class="quick-set-dialog">
    <el-dialog v-model="quickSettingDialogVisible" title="快速配置" @close="handleCloseQuickSettingDialog" class="common-dialog" :width="quickSetType == 'battery' ? '352px' : '460px'" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="form-title" v-if="quickSetType === 'battery'">
        * 请注意，电池数量总和不得超过 <span class="color-59 font-weight-bold">{{ batteryLimit[route.query.project as string] }}</span> 块！
      </div>
      <el-form :model="quickSettingDialogForm" ref="quickSettingDialogFormRef" class="quick-setting-form" label-width="64px" label-position="left" hide-required-asterisk>
        <div class="flex-box flex_a_i-center margin_b-16">
          <span class="margin_r-24" style="color: #595959" v-if="quickSetType !== 'battery'">填写订单数量</span>
          <el-form-item
            label="50kWh"
            prop="battery_50_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
            style="margin-right: 16px"
          >
            <el-input v-model="quickSettingDialogForm.battery_50_kwh" :disabled="battery_50_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
          <el-form-item
            v-if="route.query.project !== 'PowerSwap2'"
            label="60kWh"
            prop="battery_60_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
          >
            <el-input v-model="quickSettingDialogForm.battery_60_kwh" :disabled="battery_60_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
          <el-form-item
            v-else
            label="75kWh"
            prop="battery_75_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
          >
            <el-input v-model="quickSettingDialogForm.battery_75_kwh" :disabled="battery_75_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
        </div>
        <div :class="['flex-box', 'flex_a_i-center', 'margin_b-16', quickSetType == 'battery' ? 'margin_l-0' : 'margin_l-108']" v-if="route.query.project !== 'PowerSwap2'">
          <el-form-item
            label="75kWh"
            prop="battery_75_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
            style="margin-right: 16px"
          >
            <el-input v-model="quickSettingDialogForm.battery_75_kwh" :disabled="battery_75_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
          <el-form-item
            label="85kWh"
            prop="battery_85_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
          >
            <el-input v-model="quickSettingDialogForm.battery_85_kwh" :disabled="battery_85_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
        </div>
        <div :class="['flex-box', 'flex_a_i-center', 'margin_b-16', quickSetType == 'battery' ? 'margin_l-0' : 'margin_l-108']">
          <el-form-item
            label="100kWh"
            prop="battery_100_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
            style="margin-right: 16px"
          >
            <el-input v-model="quickSettingDialogForm.battery_100_kwh" :disabled="battery_100_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
          <el-form-item
            v-if="route.query.project !== 'PowerSwap2'"
            label="102kWh"
            prop="battery_102_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
            style="margin-right: 16px"
          >
            <el-input v-model="quickSettingDialogForm.battery_102_kwh" :disabled="battery_102_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
          <el-form-item
            label="150kWh"
            prop="battery_150_kwh"
            :rules="{
              validator: quickSetType == 'battery' ? batteryValidator : orderValidator,
              trigger: 'blur'
            }"
          >
            <el-input v-model="quickSettingDialogForm.battery_150_kwh" :disabled="battery_150_kwh == 0 && quickSetType != 'battery'" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 80px" />
          </el-form-item>
        </div>
        <div v-if="quickSetType == 'order'" class="margin_b-16">
          <span class="margin_r-24" style="color: #595959">跨级换电开关</span>
          <el-switch v-model="quickSettingDialogForm.skip_level_swap_switch" />
          <span style="margin-left: 8px; color: #040b29">{{ quickSettingDialogForm.skip_level_swap_switch ? '开' : '关' }}</span>
        </div>
        <div v-if="quickSetType == 'order'">
          <span class="margin_r-24" style="color: #595959">故障换电开关</span>
          <el-switch v-model="quickSettingDialogForm.swapping_failure_switch" />
          <span style="margin-left: 8px; color: #040b29">{{ quickSettingDialogForm.swapping_failure_switch ? '开' : '关' }}</span>
        </div>
        <div class="flex-box flex_j_c-flex-end flex_a_i-center">
          <el-button @click="handleClose" class="text-button">取消</el-button>
          <el-button type="primary" style="margin-left: 4px" :loading="quickLoading" @click="handleConfirmQuickSetting">确认</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { batteryLimit } from './constant'

const props = defineProps({
  quickSettingDialogVisible: Boolean,
  quickLoading: Boolean,
  quickSetType: String,
  batteryValidator: Function,
  battery_50_kwh: Number,
  battery_60_kwh: Number,
  battery_75_kwh: Number,
  battery_85_kwh: Number,
  battery_100_kwh: Number,
  battery_102_kwh: Number,
  battery_150_kwh: Number,
  quickSettingDialogForm: {
    type: Object,
    default: {}
  }
})

const route = useRoute()

const emits = defineEmits(['update:quickSettingDialogVisible', 'handleCloseQuickSettingDialog', 'handleConfirmQuickSetting'])

const quickSettingDialogFormRef = ref()

const orderValidator = (rule: any, value: any, callback: any) => {
  const { battery_50_kwh, battery_60_kwh, battery_75_kwh, battery_85_kwh, battery_100_kwh, battery_102_kwh, battery_150_kwh } = props.quickSettingDialogForm
  const batteryTotal = Number(battery_50_kwh) + Number(battery_60_kwh) + Number(battery_75_kwh) + Number(battery_85_kwh) + Number(battery_100_kwh) + Number(battery_102_kwh) + Number(battery_150_kwh)
  if (value === '') {
    callback(new Error('请输入数量'))
  } else if (value > 300) {
    callback(new Error('上限为300'))
  } else if (batteryTotal > 300) {
    callback(new Error('总数上限为300'))
  } else {
    callback()
  }
}

/**
 * @description: 关闭
 * @return {*}
 */
const handleCloseQuickSettingDialog = () => {
  emits('handleCloseQuickSettingDialog', quickSettingDialogFormRef.value)
}

/**
 * @description: 取消
 * @return {*}
 */
const handleClose = () => {
  emits('update:quickSettingDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirmQuickSetting = async () => {
  if (!quickSettingDialogFormRef.value) return
  await quickSettingDialogFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      emits('handleConfirmQuickSetting', props.quickSettingDialogForm)
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style lang="scss" scoped>
.quick-set-dialog {
  .form-title {
    color: #8c8c8c;
    line-height: 22px;
    margin-bottom: 16px;
  }
}
</style>
