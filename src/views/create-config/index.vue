<template>
  <div class="create-config-container">

    <div class="header-container">
      <BackIcon @click="handleClickBack" class="cursor-pointer margin_r-6" />
      <span class="back-title">{{ pageTitle }}</span>
      <span class="back-subtitle" v-if="createType == 'add'">{{ pageSubTitle }}</span>
    </div>

    <div class="config-container" v-if="createType != 'view'">
      <Steps :steps="createMethod == 'single' ? singleSteps : batchSteps" :width="'100%'" :descMaxWidth="160" :current="current" :withCompletedIcon="false" @change="onChange" />

      <!-- 步骤1：换电站配置 -->
      <div :class="[createMethod == 'single' ? 'single-step1-content' : 'batch-step1-content']" v-if="current == 1">
        <el-button v-if="createMethod == 'single'" type="primary" class="margin_b-18" @click="realDeviceDialogVisible = true">{{ $t('configList.pp_base_real') }}</el-button>
        <el-form ref="stepOneFormRef" :model="stepOneForm" :rules="settingRules" label-position="left" require-asterisk-position="right">
          <el-form-item label="设备选择" v-if="createMethod == 'single' && isRealDevice">
            <div class="device-tag">{{ createDeviceName }}</div>
          </el-form-item>

          <div v-if="createMethod != 'single'" class="color-26 font-size-16 line-height-24 font-weight-bold margin_b-16">换电站选择</div>

          <!-- 仿真时段 -->
          <div class="flex-box flex_a_i-center width-940">
            <el-form-item :label="$t('configList.pp_time')" prop="datePicker">
              <el-date-picker v-model="stepOneForm.datePicker" :disabled="isRealDevice" format="YYYY/MM/DD HH:mm" value-format="x" :prefix-icon="stepOneForm.datePicker && stepOneForm.datePicker.length > 0 ? customPrefix : emptyPrefix" :clearable="false" type="datetimerange" :start-placeholder="$t('common.pp_start_time')" :end-placeholder="$t('common.pp_end_time')" :disabledDate="disabledDateFun">
                <template #range-separator>
                  <TimeRangeIcon />
                </template>
              </el-date-picker>
              <WarningIcon class="margin_l-16" />
              <span class="margin_l-6" style="color: #8c8c8c">{{ $t('configList.pp_tip') }}</span>
            </el-form-item>
          </div>

          <!-- 批量生成配方：换电站选择 -->
          <el-form-item label="换电站选择" prop="devices" v-if="createMethod != 'single'">
            <el-button class="cancel-button" @click="deviceDialogVisible = true">设备选择</el-button>
            <el-button class="cancel-button" style="margin-left: 16px" @click="batchImportVisible = true">批量导入</el-button>
          </el-form-item>
          <div class="device-show-tag" v-if="createMethod != 'single' && selectDeviceList.length > 0">
            <div class="tag-content" v-for="(item, index) in selectDeviceList.slice(0, 10)">
              <div style="font-size: 14px; line-height: 22px">{{ item.description }}</div>
              <CloseIcon @click="handleDeleteDevice(index)" class="cursor-pointer" />
            </div>
            <div class="tag-content" v-if="selectDeviceList.length > 10">+{{ selectDeviceList.length - 10 }}</div>
          </div>

          <div v-if="createMethod != 'single'" class="color-26 font-size-16 line-height-24 font-weight-bold margin_b-16 margin_t-20">自定义配置</div>
          <el-form-item label="选择编辑内容" v-if="createMethod != 'single'">
            <div :class="['custom-button', { 'active-custom-button': customCapacity }]" @click="customCapacity = !customCapacity">
              <span>整站容量</span>
              <HookIcon v-if="customCapacity" class="hook-icon" />
            </div>
            <div :class="['custom-button', { 'active-custom-button': customPrice }, 'margin_l-16']" @click="customPrice = !customPrice">
              <span>电价模式</span>
              <HookIcon v-if="customPrice" class="hook-icon" />
            </div>
          </el-form-item>
          <div v-if="createMethod != 'single' && (customCapacity || customPrice)" class="color-26 font-size-16 line-height-24 font-weight-bold margin_b-16 margin_t-20">配置信息</div>

          <!-- 整站容量 -->
          <div class="three-capacity margin_b-18" v-if="createMethod == 'single' || (createMethod != 'single' && customCapacity)">
            <el-form-item :label="$t('configList.pp_station_capacity')" prop="station_capacity" style="margin-bottom: 0">
              <el-input v-model="stepOneForm.station_capacity" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 160px; margin-right: 80px" class="long-append">
                <template #append>KVA</template>
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('configList.pp_circuit1_capacity')" prop="circuit_1_capacity" class="line-form-item" style="margin-bottom: 0">
              <el-input v-model="stepOneForm.circuit_1_capacity" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 160px; margin-right: 32px" class="long-append">
                <template #append>KVA</template>
              </el-input>
            </el-form-item>
            <el-form-item :label="$t('configList.pp_circuit2_capacity')" prop="circuit_2_capacity" class="line-form-item" style="margin-bottom: 0">
              <el-input v-model="stepOneForm.circuit_2_capacity" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 160px; margin-right: 80px" class="long-append">
                <template #append>KVA</template>
              </el-input>
            </el-form-item>
          </div>

          <el-form-item :label="$t('configList.pp_price_mode')" prop="electricity_price_model" v-if="createMethod == 'single' || (createMethod != 'single' && customPrice)">
            <el-radio-group v-model="stepOneForm.electricity_price_model">
              <el-radio label="one_price">{{ $t('configList.pp_one_price') }}</el-radio>
              <el-radio label="different_price">{{ $t('configList.pp_time_of_use') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 一口价 -->
          <el-form-item :label="$t('configList.pp_price_detail')" prop="one_price_price" v-if="stepOneForm.electricity_price_model == 'one_price' && (createMethod == 'single' || (createMethod != 'single' && customPrice))">
            <el-input v-model="stepOneForm.one_price_price" type="number" style="width: 160px" class="short-append">
              <template #append>
                {{ $t('configList.pp_yuan') }}
              </template>
            </el-input>
          </el-form-item>

          <!-- 分时电价 -->
          <div v-else-if="stepOneForm.electricity_price_model != 'one_price' && (createMethod == 'single' || (createMethod != 'single' && customPrice))">
            <div v-for="(item, index) in stepOneForm.different_price" class="flex-box flex_a_i-center price-item" :style="{ marginLeft: index == 0 ? 0 : '144px' }">
              <el-form-item
                :label="index == 0 ? $t('configList.pp_price_detail') : ''"
                :prop="'different_price.' + index + '.price'"
                :rules="{
                  required: true,
                  message: t('configList.pp_enter_price'),
                  trigger: 'blur'
                }"
                class="margin_r-80"
              >
                <el-input v-model="item.price" type="number" style="width: 160px" class="short-append">
                  <template #append>
                    {{ $t('configList.pp_yuan') }}
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item
                label="起始时间"
                :prop="'different_price.' + index + '.start'"
                :rules="{
                  required: true,
                  message: t('configList.pp_enter_start'),
                  trigger: 'change'
                }"
                class="line-form-item margin_r-32 hide-required"
              >
                <el-time-select v-model="item.start" :clearable="false" placeholder="开始时间" :max-time="item.end" disabled start="00:00" end="23:30" step="00:30" style="width: 160px" />
              </el-form-item>
              <el-form-item
                label="结束时间"
                :prop="'different_price.' + index + '.end'"
                :rules="{
                  required: true,
                  message: t('configList.pp_enter_end'),
                  trigger: 'change'
                }"
                class="line-form-item margin_r-32 hide-required"
              >
                <el-time-select v-model="item.end" @change="handleChangeEndTime($event, index)" :clearable="false" placeholder="结束时间" :min-time="item.start" :disabled="index == stepOneForm.different_price.length - 1" start="00:00" end="23:30" step="00:30" style="width: 160px" />
              </el-form-item>
              <DeleteIcon @click="handleDeletePrice(index)" v-if="stepOneForm.different_price.length > 2 && index != 0 && index != stepOneForm.different_price.length - 1" class="cursor-pointer" />
            </div>
            <span @click="handleAddPrice" style="display: inline-flex; align-items: center; gap: 4px; cursor: pointer; margin-left: 144px; margin-top: 2px">
              <AddIcon />
              <span style="color: #01a0ac; font-size: 14px; line-height: 22px">新增时间段</span>
            </span>
          </div>
        </el-form>
        <div class="flex-box flex_j_c-space-between margin_t-56">
          <el-button class="cancel-button" @click="cancelDialogVisible = true">放弃配置</el-button>
          <el-button type="primary" @click="handleNext">
            <div class="margin_r-4">下一步</div>
            <NextIcon />
          </el-button>
        </div>
      </div>

      <!-- 步骤2：策略配置 -->
      <div class="step2-content-container" v-else-if="current == 2">
        <!-- 单配方 -->
        <el-form v-if="createMethod == 'single'" ref="stepTwoFormRef" :model="stepTwoForm" :rules="settingRules" label-position="left" require-asterisk-position="right" hide-required-asterisk>
          <div class="step-two-title">换电相关策略</div>

          <div class="flex-box flex_a_i-center">
            <el-form-item label="换电运营时段" prop="swap_time">
              <el-select v-model="stepTwoForm.swap_time" placeholder="请选择" style="width: 384px">
                <template #prefix>
                  <ClockIcon :color="stepTwoForm.swap_time ? '#01a0ac' : '#bfbfbf'" />
                </template>
                <el-option v-for="item in swapTimeoptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <WarningIcon class="margin_l-16" />
              <span class="margin_l-6" style="color: #8c8c8c">该参数表示设备可提供换电服务的时间区间，会影响到换电订单的生成</span>
            </el-form-item>
          </div>

          <div class="three-capacity">
            <el-form-item prop="notfully_swap_switch">
              <template #label>
                <div class="flex-box flex_a_i-center gap_4">
                  <span>非满电换电开关</span>
                  <el-tooltip content="该参数表示换电中站内无满电电池时，会自动选择SOC区间内的电池进行换电" placement="top">
                    <HelpIcon />
                  </el-tooltip>
                </div>
              </template>
              <el-switch v-model="stepTwoForm.notfully_swap_switch" />
              <span style="margin-left: 8px; margin-right: 80px; color: #040b29">{{ stepTwoForm.notfully_swap_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
            <el-form-item v-if="stepTwoForm.notfully_swap_switch" label="非满电电池SOC下限 %" prop="soc_lower_limit" class="line-form-item soc-form-item">
              <el-input v-model="stepTwoForm.soc_lower_limit" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px; margin-right: 40px" />
            </el-form-item>
            <el-form-item v-if="stepTwoForm.notfully_swap_switch" label="非满电电池SOC上限 %" prop="soc_upper_limit" class="line-form-item soc-form-item">
              <el-input v-model="stepTwoForm.soc_upper_limit" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px" />
            </el-form-item>
          </div>

          <div class="step-two-title margin_t-2">充电相关策略</div>
          <div class="three-capacity">
            <el-form-item label="EPS策略开关" prop="cms_switch">
              <el-switch v-model="stepTwoForm.cms_switch" />
              <span style="margin-left: 8px; margin-right: 80px; color: #040b29">{{ stepTwoForm.cms_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
            <el-form-item label="静音模式开关" prop="silent_mode_switch">
              <el-switch v-model="stepTwoForm.silent_mode_switch" />
              <span style="margin-left: 8px; color: #040b29">{{ stepTwoForm.silent_mode_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
          </div>
          <div class="three-capacity">
            <el-form-item v-if="createProject !== 'PowerSwap2'" label="电池倒仓开关" prop="battery_exchange_switch">
              <el-switch v-model="stepTwoForm.battery_exchange_switch" />
              <span style="margin-left: 8px; margin-right: 80px; color: #040b29">{{ stepTwoForm.battery_exchange_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
            <el-form-item label="电池静置开关" prop="battery_rest_switch">
              <el-switch v-model="stepTwoForm.battery_rest_switch" />
              <span style="margin-left: 8px; color: #040b29">{{ stepTwoForm.battery_rest_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
          </div>
          <div class="step-two-title margin_t-2" v-if="stepTwoForm.battery_rest_switch">电池静置相关参数</div>
          <div class="three-capacity" v-if="stepTwoForm.battery_rest_switch">
            <el-form-item label="静置阶段电流" prop="default_rest_current" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_rest_current" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px; margin-right: 51px" />
            </el-form-item>
            <el-form-item label="充电缓起时长" prop="default_hanging_duration" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_hanging_duration" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px" />
            </el-form-item>
          </div>
          <div class="three-capacity" v-if="stepTwoForm.battery_rest_switch">
            <el-form-item label="充电缓起步长" prop="default_hanging_step" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_hanging_step" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px; margin-right: 51px" />
            </el-form-item>
            <el-form-item label="充电缓起最大电流" prop="default_hanging_current_max" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_hanging_current_max" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px" />
            </el-form-item>
          </div>
          <div class="step-two-title margin_t-2" v-if="stepTwoForm.cms_switch">EPS策略相关参数</div>
          <div class="three-capacity" v-if="stepTwoForm.cms_switch">
            <el-form-item label="EPS运营模式" prop="eps_model_mode" class="rest-switch-form" label-width="180px">
              <el-select v-model="stepTwoForm.eps_model_mode" placeholder="请选择" style="width: 263.23px; margin-right: 51px">
                <el-option label="收益优先" :value="1" />
                <el-option label="体验优先" :value="2" />
                <el-option label="收益&体验兼顾" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="高电价时段是否多充" prop="eps_high_price_peak_shift_enable" class="rest-switch-form" label-width="180px">
              <el-select v-model="stepTwoForm.eps_high_price_peak_shift_enable" placeholder="请选择" style="width: 200px">
                <el-option label="不进行多充" :value="0" />
                <el-option label="进行多充" :value="1" />
              </el-select>
            </el-form-item>
          </div>
          <div class="three-capacity" v-if="stepTwoForm.cms_switch">
            <el-form-item label="多充功率限制生效时段" class="rest-switch-form" label-width="180px" prop="eps_peak_shift_start_time">
              <div class="flex-box flex_a_i-center">
                <el-time-select v-model="stepTwoForm.eps_peak_shift_start_time" placeholder="开始时间" start="00:00" end="23:30" step="00:30" style="width: 120px" />
                <span style="margin: 0 8px">~</span>
                <el-time-select v-model="stepTwoForm.eps_peak_shift_end_time" placeholder="结束时间" start="00:00" end="23:30" step="00:30" style="width: 120px; margin-right: 51px" />
              </div>
            </el-form-item>
            <el-form-item label="多充功率限制值" prop="eps_peak_shift_power_limit" class="rest-switch-form" label-width="180px">
              <el-input v-model="stepTwoForm.eps_peak_shift_power_limit" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 200px" />
            </el-form-item>
          </div>
        </el-form>

        <!-- 批量生成配方 -->
        <el-form v-else ref="stepTwoFormRef" :model="stepTwoForm" :rules="settingRules" label-position="left" require-asterisk-position="right" hide-required-asterisk>
          <div class="step-two-title">自定义配置</div>
          <el-form-item label="选择编辑内容">
            <div :class="['step2-custom-button', { 'active-custom-button': customOperationTime }]" @click="customOperationTime = !customOperationTime">
              <span>换电运营时段</span>
              <HookIcon v-if="customOperationTime" class="hook-icon" />
            </div>
            <div :class="['step2-custom-button', { 'active-custom-button': customNotFullySwapSwitch }, 'margin_l-16', 'width-120']" @click="customNotFullySwapSwitch = !customNotFullySwapSwitch">
              <span>非满电换电开关</span>
              <HookIcon v-if="customNotFullySwapSwitch" class="hook-icon" />
            </div>
            <div :class="['step2-custom-button', { 'active-custom-button': customEpsSwitch }, 'margin_l-16', 'width-120']" @click="customEpsSwitch = !customEpsSwitch">
              <span>EPS策略开关</span>
              <HookIcon v-if="customEpsSwitch" class="hook-icon" />
            </div>
            <div :class="['step2-custom-button', { 'active-custom-button': customSilentModeSwitch }, 'margin_l-16', 'width-120']" @click="customSilentModeSwitch = !customSilentModeSwitch">
              <span>静音模式开关</span>
              <HookIcon v-if="customSilentModeSwitch" class="hook-icon" />
            </div>
            <div v-if="createProject !== 'PowerSwap2'" :class="['step2-custom-button', { 'active-custom-button': customBatteryExchangeSwitch }, 'margin_l-16', 'width-120']" @click="customBatteryExchangeSwitch = !customBatteryExchangeSwitch">
              <span>电池倒仓开关</span>
              <HookIcon v-if="customBatteryExchangeSwitch" class="hook-icon" />
            </div>
            <div :class="['step2-custom-button', { 'active-custom-button': customBatteryRestSwitch }, 'margin_l-16', 'width-120']" @click="customBatteryRestSwitch = !customBatteryRestSwitch">
              <span>电池静置开关</span>
              <HookIcon v-if="customBatteryRestSwitch" class="hook-icon" />
            </div>
          </el-form-item>

          <div class="step-two-title margin_t-20" v-if="isCustomStep2">充换电相关策略</div>
          <div class="flex-box flex_a_i-center" v-if="customOperationTime">
            <el-form-item label="换电运营时段" prop="swap_time">
              <el-select v-model="stepTwoForm.swap_time" placeholder="请选择" style="width: 384px">
                <template #prefix>
                  <ClockIcon :color="stepTwoForm.swap_time ? '#01a0ac' : '#bfbfbf'" />
                </template>
                <el-option v-for="item in swapTimeoptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <WarningIcon class="margin_l-16" />
              <span class="margin_l-6" style="color: #8c8c8c">该参数表示设备可提供换电服务的时间区间，会影响到换电订单的生成</span>
            </el-form-item>
          </div>

          <div class="three-capacity" v-if="customNotFullySwapSwitch">
            <el-form-item prop="notfully_swap_switch">
              <template #label>
                <div class="flex-box flex_a_i-center gap_4">
                  <span>非满电换电开关</span>
                  <el-tooltip content="该参数表示换电中站内无满电电池时，会自动选择SOC区间内的电池进行换电" placement="top">
                    <HelpIcon />
                  </el-tooltip>
                </div>
              </template>
              <el-switch v-model="stepTwoForm.notfully_swap_switch" />
              <span style="margin-left: 8px; margin-right: 80px; color: #040b29">{{ stepTwoForm.notfully_swap_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
            <el-form-item v-if="stepTwoForm.notfully_swap_switch" label="非满电电池SOC下限 %" prop="soc_lower_limit" class="line-form-item soc-form-item">
              <el-input v-model="stepTwoForm.soc_lower_limit" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px; margin-right: 40px" />
            </el-form-item>
            <el-form-item v-if="stepTwoForm.notfully_swap_switch" label="非满电电池SOC上限 %" prop="soc_upper_limit" class="line-form-item soc-form-item">
              <el-input v-model="stepTwoForm.soc_upper_limit" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px" />
            </el-form-item>
          </div>

          <div class="three-capacity" v-if="customEpsSwitch || customSilentModeSwitch">
            <el-form-item label="EPS策略开关" prop="cms_switch" v-if="customEpsSwitch">
              <el-switch v-model="stepTwoForm.cms_switch" />
              <span style="margin-left: 8px; margin-right: 80px; color: #040b29">{{ stepTwoForm.cms_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
            <el-form-item label="静音模式开关" prop="silent_mode_switch" v-if="customSilentModeSwitch">
              <el-switch v-model="stepTwoForm.silent_mode_switch" />
              <span style="margin-left: 8px; color: #040b29">{{ stepTwoForm.silent_mode_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
          </div>

          <div class="three-capacity" v-if="customBatteryExchangeSwitch || customBatteryRestSwitch">
            <el-form-item v-if="createProject !== 'PowerSwap2' && customBatteryExchangeSwitch" label="电池倒仓开关" prop="battery_exchange_switch">
              <el-switch v-model="stepTwoForm.battery_exchange_switch" />
              <span style="margin-left: 8px; margin-right: 80px; color: #040b29">{{ stepTwoForm.battery_exchange_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
            <el-form-item label="电池静置开关" prop="battery_rest_switch" v-if="customBatteryRestSwitch">
              <el-switch v-model="stepTwoForm.battery_rest_switch" />
              <span style="margin-left: 8px; color: #040b29">{{ stepTwoForm.battery_rest_switch ? $t('configList.pp_open') : $t('configList.pp_close') }}</span>
            </el-form-item>
          </div>

          <div class="three-capacity" v-if="stepTwoForm.battery_rest_switch && customBatteryRestSwitch">
            <el-form-item label="静置阶段电流" prop="default_rest_current" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_rest_current" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px; margin-right: 51px" />
            </el-form-item>
            <el-form-item label="充电缓起时长" prop="default_hanging_duration" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_hanging_duration" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px" />
            </el-form-item>
          </div>
          <div class="three-capacity" v-if="stepTwoForm.battery_rest_switch && customBatteryRestSwitch">
            <el-form-item label="充电缓起步长" prop="default_hanging_step" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_hanging_step" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px; margin-right: 51px" />
            </el-form-item>
            <el-form-item label="充电缓起最大电流" prop="default_hanging_current_max" class="rest-switch-form">
              <el-input v-model="stepTwoForm.default_hanging_current_max" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 91px" />
            </el-form-item>
          </div>

          <div class="three-capacity" v-if="stepTwoForm.cms_switch && customEpsSwitch">
            <el-form-item label="EPS运营模式" prop="eps_model_mode" class="rest-switch-form" label-width="160px">
              <el-select v-model="stepTwoForm.eps_model_mode" placeholder="请选择" style="width: 263.23px; margin-right: 51px">
                <el-option label="收益优先" :value="1" />
                <el-option label="体验优先" :value="2" />
                <el-option label="收益&体验兼顾" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="高电价时段是否多充" prop="eps_high_price_peak_shift_enable" class="rest-switch-form" label-width="160px">
              <el-select v-model="stepTwoForm.eps_high_price_peak_shift_enable" placeholder="请选择" style="width: 200px">
                <el-option label="不进行多充" :value="0" />
                <el-option label="进行多充" :value="1" />
              </el-select>
            </el-form-item>
          </div>
          <div class="three-capacity" v-if="stepTwoForm.cms_switch && customEpsSwitch">
            <el-form-item label="多充功率限制生效时段" class="rest-switch-form" label-width="160px" prop="eps_peak_shift_start_time">
              <div class="flex-box flex_a_i-center">
                <el-time-select v-model="stepTwoForm.eps_peak_shift_start_time" placeholder="开始时间" start="00:00" end="23:30" step="00:30" style="width: 120px" />
                <span style="margin: 0 8px">~</span>
                <el-time-select v-model="stepTwoForm.eps_peak_shift_end_time" placeholder="结束时间" start="00:00" end="23:30" step="00:30" style="width: 120px; margin-right: 51px" />
              </div>
            </el-form-item>
            <el-form-item label="多充功率限制值" prop="eps_peak_shift_power_limit" class="rest-switch-form" label-width="160px">
              <el-input v-model="stepTwoForm.eps_peak_shift_power_limit" oninput="value = value.replace(/[^0-9]/g,'')" style="width: 200px" />
            </el-form-item>
          </div>
        </el-form>

        <div class="flex-box flex_j_c-space-between flex_a_i-center" :class="!isCustomStep2 && createMethod !== 'single' ? 'margin_t-56' : 'margin_t-38'">
          <div>
            <el-button class="cancel-button" @click="handlePre">
              <PreIcon />
              <div class="margin_l-4">上一步</div>
            </el-button>
            <el-button class="cancel-button" style="margin-left: 12px" @click="cancelDialogVisible = true">放弃配置</el-button>
          </div>
          <el-button type="primary" @click="handleNext">
            <div class="margin_r-4">下一步</div>
            <NextIcon />
          </el-button>
        </div>
      </div>

      <!-- 步骤3：电池配置 -->
      <div class="step2-content-container" v-else-if="current == 3 && createMethod == 'single'">
        <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-16">
          <el-button class="cancel-button" @click="batchEditDialogVisible = true" :disabled="!selectedSlot || selectedSlot.length == 0">批量编辑</el-button>
          <div>
            <el-button class="clear-button" @click="clearDialogVisible = true">清空所有</el-button>
            <el-button type="primary" @click="handleClickQuickSet('battery')">快速配置</el-button>
          </div>
        </div>
        <el-table :data="batteryTableList" ref="multipleTableRef" @selection-change="handleSelectionChange" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column type="selection" width="45" />
          <el-table-column prop="slot_id" label="仓位号" width="80" show-overflow-tooltip fixed />
          <el-table-column prop="battery_soc" label="电池SOC" min-width="145" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_soc) ? '-' : row.battery_soc + '%' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pack_max_temperature" label="电池温度" min-width="145" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.pack_max_temperature) ? '-' : row.pack_max_temperature + '°C' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_ownership" label="电池产权" min-width="145" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_ownership) ? '-' : row.battery_ownership }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_rated_kwh" label="电池度数" min-width="145" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_rated_kwh) ? '-' : row.battery_rated_kwh + 'kWh' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="charging_stop_soc" label="电池充电截止SOC" min-width="145" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.charging_stop_soc) ? '-' : row.charging_stop_soc + '%' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_id" label="电池定位标签" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_id) ? '-' : row.battery_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="70" fixed="right" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="light-column" @click="handleClickEditBattery(row)">编辑</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex-box flex_j_c-space-between flex_a_i-center margin_t-16">
          <div>
            <el-button class="cancel-button" @click="handlePre">
              <PreIcon />
              <div class="margin_l-4">上一步</div>
            </el-button>
            <el-button class="cancel-button" style="margin-left: 12px" @click="cancelDialogVisible = true">放弃配置</el-button>
          </div>
          <el-button type="primary" @click="handleNext">
            <div class="margin_r-4">下一步</div>
            <NextIcon />
          </el-button>
        </div>
      </div>

      <!-- 步骤4: 订单配置 -->
      <div class="step3-content-container" v-else-if="current == 4 && createMethod == 'single'">
        <!-- 二代站 -->
        <div class="flex-box flex_j_c-space-between margin_b-16" v-if="createProject == 'PowerSwap2'">
            <div class="flex-box gap_16">
            <div class="order-total">
              <div class="flex-box flex_d-column width-96">
                  <div class="battery-type">50kWh 电池单量</div>
                  <div class="battery-order">{{ order_50_kwh }} <span class="order-unit">单</span></div>
                </div>
                <VerticalLine class="margin-n-16" />
              <div class="flex-box flex_d-column width-96">
                  <div class="battery-type">75kWh 电池单量</div>
                  <div class="battery-order">{{ order_75_kwh }} <span class="order-unit">单</span></div>
                </div>
                <VerticalLine class="margin-n-16" />
              <div class="flex-box flex_d-column width-98">
                  <div class="battery-type">100kWh 电池单量</div>
                  <div class="battery-order">{{ order_100_kwh }} <span class="order-unit">单</span></div>
                </div>
                <VerticalLine class="margin-n-14" />
              <div class="flex-box flex_d-column width-98">
                  <div class="battery-type">150kWh 电池单量</div>
                  <div class="battery-order">{{ order_150_kwh }} <span class="order-unit">单</span></div>
                </div>
              </div>
            </div>
            <!-- 第二行：运营信息 -->
            <div class="flex-box gap_16">
              <div class="order-total flex_d-column" style="width: 200px">
                <div class="battery-type">运营时段</div>
                <div class="battery-order">{{ '0' + stepTwoForm.swap_time.split('-')[0] + ':00' }}—{{ stepTwoForm.swap_time.split('-')[1] + ':00' }}</div>
              </div>
              <div class="order-total flex_d-column" style="width: 200px" v-if="realSwapElecConsumption !== null && realSwapElecConsumption !== undefined">
                <div class="battery-type">用户实际交易总电量</div>
                <div class="battery-order">{{ realSwapElecConsumption }}</div>
              </div>
          </div>
          <div class="flex-box flex_a_i-flex-end" style="margin-top: 16px;">
            <el-button class="cancel-button" @click="handleClickQuickSet('order')">快速配置</el-button>
            <el-button class="cancel-button" style="margin-left: 12px" @click="handleClickAddOrder">增加订单</el-button>
            <el-button class="clear-button" style="margin-left: 12px" @click="clearDialogVisible = true">清空所有</el-button>
          </div>
        </div>

        <!-- 三、四代站 -->
        <div class="margin_b-16" v-else>
          <div class="flex-box flex_j_c-space-between flex gap_16 margin_b-16">
            <div class="order-total flex-item_f-1">
              <div class="flex-box flex_d-column flex-item_f-1">
                <div class="battery-type">50kWh 电池单量</div>
                <div class="battery-order">{{ order_50_kwh }} <span class="order-unit">单</span></div>
              </div>
              <VerticalLine class="margin_r-16" />
              <div class="flex-box flex_d-column flex-item_f-1">
                <div class="battery-type">60kWh 电池单量</div>
                <div class="battery-order">{{ order_60_kwh }} <span class="order-unit">单</span></div>
              </div>
              <VerticalLine class="margin_r-16" />
              <div class="flex-box flex_d-column flex-item_f-1">
                <div class="battery-type">75kWh 电池单量</div>
                <div class="battery-order">{{ order_75_kwh }} <span class="order-unit">单</span></div>
              </div>
              <VerticalLine class="margin_r-16" />
              <div class="flex-box flex_d-column flex-item_f-1">
                <div class="battery-type">85kWh 电池单量</div>
                <div class="battery-order">{{ order_85_kwh }} <span class="order-unit">单</span></div>
              </div>
              <VerticalLine class="margin_r-16" />
              <div class="flex-box flex_d-column flex-item_f-1">
                <div class="battery-type">100kWh 电池单量</div>
                <div class="battery-order">{{ order_100_kwh }} <span class="order-unit">单</span></div>
              </div>
              <VerticalLine class="margin_r-16" v-if="createProject === 'PUS3' || createProject === 'PUS4'" />
              <div class="flex-box flex_d-column flex-item_f-1" v-if="createProject === 'PUS3' || createProject === 'PUS4'">
                <div class="battery-type">102kWh 电池单量</div>
                <div class="battery-order">{{ order_102_kwh }} <span class="order-unit">单</span></div>
              </div>
              <VerticalLine class="margin_r-16" />
              <div class="flex-box flex_d-column flex-item_f-1">
                <div class="battery-type">150kWh 电池单量</div>
                <div class="battery-order">{{ order_150_kwh }} <span class="order-unit">单</span></div>
              </div>
            </div>
          </div>
          <!-- 第二行：运营信息 -->
          <div class="flex-box gap_16">
            <div class="order-total flex_d-column" style="width: 200px">
              <div class="battery-type">运营时段</div>
              <div class="battery-order">{{ '0' + stepTwoForm.swap_time.split('-')[0] + ':00' }}—{{ stepTwoForm.swap_time.split('-')[1] + ':00' }}</div>
            </div>
            <div class="order-total flex_d-column" style="width: 200px" v-if="realSwapElecConsumption !== null && realSwapElecConsumption !== undefined">
              <div class="battery-type">实际用户交易电量</div>
              <div class="battery-order">{{ realSwapElecConsumption + ' kWh' }}</div>
            </div>
          </div>

          <div class="flex-box flex_j_c-space-between flex_a_i-center" style="margin-top: 16px;">
            <div class="flex-box gap_12">
              <el-button class="cancel-button" @click="handleClickQuickSet('order')">快速配置</el-button>
              <el-button class="cancel-button" style="margin-left: 0" @click="handleClickAddOrder">增加订单</el-button>
            </div>
            <el-button class="clear-button" @click="clearDialogVisible = true">清空所有</el-button>
          </div>
        </div>

        <el-table :data="orderList.slice((pages.current - 1) * pages.size, pages.current * pages.size)" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column prop="user_arrival_time" label="用户到达时间" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ formatTime(row.user_arrival_time) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_rest_label" label="是否静置" min-width="90" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_rest_label) ? '-' : row.battery_rest_label == 1 ? '否' : '是' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_rated_kwh" label="进站度数" min-width="90" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_rated_kwh) ? '-' : row.battery_rated_kwh + 'kWh' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_soc" label="进站电池SOC" min-width="100" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_soc) ? '-' : row.battery_soc + '%' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pack_max_temperature" label="进站电池温度" min-width="110" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.pack_max_temperature) ? '-' : row.pack_max_temperature + '°C' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="target_battery_rated_kwh" label="目标度数" min-width="90" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.target_battery_rated_kwh) ? '-' : row.target_battery_rated_kwh + 'kWh' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="user_ownership" label="车辆产权" min-width="90" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.user_ownership) ? '-' : row.user_ownership }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_ownership" label="电池产权" min-width="90" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.battery_ownership) ? '-' : row.battery_ownership }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="service_id" label="订单ID（标签）" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ isEmptyData(row.service_id) ? '-' : row.service_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="95" fixed="right" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="flex-box flex_a_i-center gap_10">
                <EditIcon class="cursor-pointer" @click="handleClickEditOrder(row)" />
                <Delete class="cursor-pointer" @click="handleDeleteOrder(row)" />
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-box">
          <Page :page="pages" @change="handlePageChange" />
        </div>

        <div class="flex-box flex_j_c-space-between flex_a_i-center margin_t-16">
          <div>
            <el-button class="cancel-button" @click="handlePre">
              <PreIcon />
              <div class="margin_l-4">上一步</div>
            </el-button>
            <el-button class="cancel-button" style="margin-left: 12px" @click="cancelDialogVisible = true">放弃配置</el-button>
          </div>
          <el-button type="primary" @click="handleNext">
            <div class="margin_r-4">下一步</div>
            <NextIcon />
          </el-button>
        </div>
      </div>

      <!-- 步骤5: 配置总览 -->
      <div class="step4-content-container" v-else-if="current == 5 && createMethod == 'single'">
        <div class="step4-content-card list-card margin_b-16">
          <div class="card-title">基本信息</div>
          <el-form ref="stepFourFormRef" :model="stepFourForm" inline scroll-to-error label-position="left" require-asterisk-position="right">
            <el-form-item label="配置名称" prop="config_name" :rules="{ required: true, message: '请输入配置名称', trigger: 'blur' }">
              <el-input v-model="stepFourForm.config_name" placeholder="请输入" maxlength="50" show-word-limit style="width: 100%" />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="stepFourForm.remark" placeholder="请输入" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" style="width: 100%" />
            </el-form-item>
          </el-form>
        </div>
        <ConfigDetail :configDetail="createParams" />
        <div class="flex-box flex_j_c-space-between flex_a_i-center margin_t-20">
          <div>
            <el-button class="cancel-button" @click="handlePre">
              <PreIcon />
              <div class="margin_l-4">上一步</div>
            </el-button>
            <el-button class="cancel-button" @click="cancelDialogVisible = true">放弃配置</el-button>
          </div>
          <el-button type="primary" :loading="confirmLoading" @click="handleConfirmSingle">确认并保存</el-button>
        </div>
      </div>

      <!-- 批量生成配方最后一步 -->
      <div class="step4-content-container" v-else-if="current == 3 && createMethod != 'single'">
        <div class="step4-content-card">
          <el-form ref="stepFourFormRef" :model="stepFourForm" inline scroll-to-error label-position="left" require-asterisk-position="right">
            <el-form-item label="配置名称" prop="config_name" :rules="{ required: true, message: '请输入配置名称', trigger: 'blur' }">
              <el-input v-model="stepFourForm.config_name" placeholder="请输入" maxlength="50" show-word-limit style="width: 100%" />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="stepFourForm.remark" placeholder="请输入" :autosize="{ minRows: 4, maxRows: 6 }" type="textarea" style="width: 100%" />
            </el-form-item>
          </el-form>
          <div class="flex-box flex_j_c-space-between flex_a_i-center margin_t-56">
            <div>
              <el-button class="cancel-button" @click="handlePre">
                <PreIcon />
                <div class="margin_l-4">上一步</div>
              </el-button>
              <el-button class="cancel-button" @click="cancelDialogVisible = true">放弃配置</el-button>
            </div>
            <el-button type="primary" :loading="confirmLoading" @click="handleConfirmSingle">确认并保存</el-button>
          </div>
        </div>
      </div>

    <div v-else class="view-all-container">
      <div v-if="viewConfigLoading" class="loading-container" v-loading="true" element-loading-text="正在加载配置详情...">
        <div style="height: 200px;"></div>
      </div>
      <template v-else-if="createParams">
        <div class="basic-info-container">
          <div class="basic-title">基本信息</div>
          <div class="basic-content">
            <div class="flex-box">
              <div class="label-text">配置名称</div>
              <div class="value-text">{{ createParams.config_name }}</div>
            </div>
            <div class="flex-box">
              <div class="label-text">备注</div>
              <div class="value-text">{{ createParams.remark || '-' }}</div>
            </div>
          </div>
        </div>
        <ConfigDetail :configDetail="createParams" />
      </template>
      <div v-else class="empty-container">
        <div class="empty-text">配置详情加载失败</div>
        <div class="empty-text" style="margin-top: 10px; font-size: 12px; color: #999;">
          请检查网络连接或稍后重试
        </div>
      </div>
    </div>

    <!-- 放弃创建 -->
    <el-dialog v-model="cancelDialogVisible" :title="$t('deviceSimulation.pp_prompt')" @close="cancelDialogVisible = false" class="giveup-dialog" width="340px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="flex-box flex_a_i-center gap_6">
        <WarningIcon />
        <span class="dialog_content">{{ $t('deviceSimulation.pp_prompt_content') }}</span>
      </div>
      <div class="flex-box flex_j_c-flex-end margin_t-24">
        <el-button @click="cancelDialogVisible = false" class="text-button">{{ $t('deviceSimulation.pp_cancel_giveup') }}</el-button>
        <el-button type="primary" style="margin-left: 4px" @click="handleBack">{{ $t('deviceSimulation.pp_confirm_giveup') }}</el-button>
      </div>
    </el-dialog>

    <!-- 基于真实设备模拟 -->
    <el-dialog v-model="realDeviceDialogVisible" :title="$t('configList.pp_base_real')" @close="handleCloseRealDeviceDialog" class="common-dialog" width="424px" top="25vh" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="realDeviceDialogForm" ref="realDeviceDialogFormRef" label-position="top" :rules="settingRules" require-asterisk-position="right" class="vertical-form">
        <el-form-item :label="$t('configList.pp_device')" prop="device_id">
          <el-select v-model="realDeviceDialogForm.device_id" filterable remote :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('configList.pp_time')" prop="datePicker">
          <el-date-picker v-model="realDeviceDialogForm.datePicker" format="YYYY/MM/DD HH:mm" value-format="x" :prefix-icon="realDeviceDialogForm.datePicker && realDeviceDialogForm.datePicker.length > 0 ? customPrefix : emptyPrefix" :clearable="false" type="datetimerange" :start-placeholder="$t('common.pp_start_time')" :end-placeholder="$t('common.pp_end_time')" :disabledDate="disabledDateFun" :teleported="false">
            <template #range-separator>
              <TimeRangeIcon />
            </template>
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div class="flex-box flex_j_c-flex-end">
        <el-button @click="realDeviceDialogVisible = false" class="text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button type="primary" style="margin-left: 4px" :loading="realDeviceLoading" @click="handleImportRealDevice">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>

    <!-- 批量编辑 -->
    <el-dialog v-model="batchEditDialogVisible" title="批量编辑" @close="handleCloseBatchEditDialog" class="common-dialog" width="345px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="margin_b-12">
        <span style="color: #8c8c8c">已选仓位号 </span>
        <span style="color: #262626; font-weight: bold">{{ selectedSlot.join('、') }}</span>
      </div>
      <el-form :model="batchEditDialogForm" ref="batchEditDialogFormRef" class="batch-edit-form" hide-required-asterisk>
        <div class="flex-box flex_a_i-center gap_8 margin_b-8">
          <el-checkbox v-model="batchEditDialogForm.battery_soc_selected" size="large" />
          <el-form-item
            label="电池SOC %"
            prop="battery_soc"
            :rules="[
              { required: batchEditDialogForm.battery_soc_selected, message: '请输入电池SOC', trigger: 'blur' },
              {
                validator: batchEditDialogForm.battery_soc_selected ?  socValidator : (rule: any, value: any, callback: any) => callback(),
                trigger: 'blur'
              }
            ]"
          >
            <el-input v-model="batchEditDialogForm.battery_soc" :disabled="!batchEditDialogForm.battery_soc_selected" clearable oninput="value = value.replace(/[^0-9]/g,'')" style="width: 120px" />
          </el-form-item>
        </div>
        <div class="flex-box flex_a_i-center gap_8 margin_b-8">
          <el-checkbox v-model="batchEditDialogForm.pack_max_temperature_selected" size="large" />
          <el-form-item
            label="电池温度 °C"
            prop="pack_max_temperature"
            :rules="[
              { required: batchEditDialogForm.pack_max_temperature_selected, message: '请输入电池温度', trigger: 'blur' },
              {
                validator: batchEditDialogForm.pack_max_temperature_selected ?  temValidator : (rule: any, value: any, callback: any) => callback(),
                trigger: 'blur'
              }
            ]"
          >
            <el-input v-model="batchEditDialogForm.pack_max_temperature" :disabled="!batchEditDialogForm.pack_max_temperature_selected" clearable @input="handleInput" style="width: 120px" />
          </el-form-item>
        </div>
        <div class="flex-box flex_a_i-center gap_8 margin_b-8">
          <el-checkbox v-model="batchEditDialogForm.battery_ownership_selected" size="large" />
          <el-form-item label="电池产权" prop="battery_ownership">
            <el-select v-model="batchEditDialogForm.battery_ownership" :disabled="!batchEditDialogForm.battery_ownership_selected" placeholder="请选择" style="width: 120px">
              <el-option v-for="item in batteryOwnershipOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex-box flex_a_i-center gap_8 margin_b-8">
          <el-checkbox v-model="batchEditDialogForm.battery_rated_kwh_selected" size="large" />
          <el-form-item label="电池度数" prop="battery_rated_kwh">
            <el-select v-model="batchEditDialogForm.battery_rated_kwh" :disabled="!batchEditDialogForm.battery_rated_kwh_selected" placeholder="请选择" style="width: 120px">
              <el-option v-for="item in createProject == 'PowerSwap2' ? powerSwap2BatteryTypeOptions : batteryTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex-box flex_a_i-center gap_8 margin_b-12">
          <el-checkbox v-model="batchEditDialogForm.charging_stop_soc_selected" size="large" />
          <el-form-item
            label="电池充电截止SOC %"
            prop="charging_stop_soc"
            :rules="[
              { required: batchEditDialogForm.charging_stop_soc_selected, message: '请输入电池充电截止SOC', trigger: 'blur' },
              {
                validator: batchEditDialogForm.charging_stop_soc_selected ? socValidator : (rule: any, value: any, callback: any) => callback(),
                trigger: 'blur'
              }
            ]"
          >
            <el-input v-model="batchEditDialogForm.charging_stop_soc" :disabled="!batchEditDialogForm.charging_stop_soc_selected" clearable oninput="value = value.replace(/[^0-9]/g,'')" style="width: 120px" />
          </el-form-item>
        </div>
      </el-form>
      <div class="flex-box flex_j_c-flex-end">
        <el-button @click="batchEditDialogVisible = false" class="text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button type="primary" style="margin-left: 4px" @click="handleConfirmBatchEdit">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>

    <!-- 清空所有 -->
    <el-dialog v-model="clearDialogVisible" title="提示" @close="clearDialogVisible = false" class="giveup-dialog" width="294px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="flex-box flex_a_i-center gap_8 margin_b-24">
        <WarningIcon />
        <span style="color: #262626">确认后将清空当前表格内所有信息！</span>
      </div>
      <div class="flex-box flex_j_c-flex-end flex_a_i-center">
        <el-button @click="clearDialogVisible = false" class="text-button">取消清空</el-button>
        <el-button type="primary" style="margin-left: 4px" @click="handleConfirmClear">确认清空</el-button>
      </div>
    </el-dialog>

    <!-- 电池/订单快速配置弹窗 -->
    <QuickSetDialog v-model:quickSettingDialogVisible="quickSettingDialogVisible" @handleConfirmQuickSetting="handleConfirmQuickSetting" @handleCloseQuickSettingDialog="handleCloseQuickSettingDialog" :quickSettingDialogForm="quickSettingDialogForm" :batteryValidator="batteryValidator" :quickLoading="quickLoading" :quickSetType="quickSetType" :battery_50_kwh="battery_50_kwh" :battery_60_kwh="battery_60_kwh" :battery_75_kwh="battery_75_kwh" :battery_85_kwh="battery_85_kwh" :battery_100_kwh="battery_100_kwh" :battery_102_kwh="battery_102_kwh" :battery_150_kwh="battery_150_kwh" />

    <!-- 编辑电池配置弹窗 -->
    <EditBatteryDialog v-model:editBatteryDialogVisible="editBatteryDialogVisible" @handleConfirmEditBattery="handleConfirmEditBattery" :editBatteryForm="editBatteryForm" :socValidator="socValidator" :batteryIdValidator="batteryIdValidator" :temValidator="temValidator" :batteryOwnershipOptions="batteryOwnershipOptions" />

    <!-- 新增/编辑订单弹窗 -->
    <EditOrderDialog v-model:editOrderDialogVisible="editOrderDialogVisible" @handleConfirmEditOrder="handleConfirmEditOrder" @handleCloseEditOrder="handleCloseEditOrder" :checkTimeValid="checkTimeValid" :editOrderDialogTitle="editOrderDialogTitle" :editOrderForm="editOrderForm" :timeRange="stepOneForm.datePicker" :socValidator="socValidator" :temValidator="temValidator" :batteryOwnershipOptions="batteryOwnershipOptions" :userOwnershipOptions="userOwnershipOptions" />

    <!-- 选择设备弹窗 -->
    <SelectDeviceDialog v-model:deviceDialogVisible="deviceDialogVisible" @handleConfirmSelectDevice="handleConfirmSelectDevice" :createProject="createProject" :selectDeviceList="selectDeviceList" />

    <!-- 批量导入设备 -->
    <BatchImportDevice v-model:batchImportVisible="batchImportVisible" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" :createProject="createProject" />
    
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, shallowRef, h, onBeforeMount, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { batteryTypeOptions, powerSwap2BatteryTypeOptions, initBatteryList, batteryLimit } from './components/constant'
import { page } from '~/constvars/page'
import { apiGetDevices } from '~/apis/home'
import { apiGetRealDeviceConfig, apiGetQuickBattery, apiGetQuickOrder, apiPostSingleConfig, apiPostBatchConfig, apiGetConfigDetail } from '~/apis/config-list'
import { generateUUID, formatTime } from '~/utils'
import { ElMessage } from 'element-plus'
import HookIcon from './components/icon/hook-icon.vue'
import BackIcon from './components/icon/back-icon.vue'
import WarningIcon from './components/icon/warning-icon.vue'
import AddIcon from './components/icon/add-icon.vue'
import DeleteIcon from './components/icon/delete-icon.vue'
import VerticalLine from './components/icon/vertical-line.vue'
import CloseIcon from './components/icon/close-icon.vue'
import NextIcon from '../run-list/component/icon/next-icon.vue'
import PreIcon from '../run-list/component/icon/pre-icon.vue'
import EditIcon from '../config-list/components/icon/edit.vue'
import Delete from '~/assets/svg/delete.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import HelpIcon from '~/assets/svg/help.vue'
import ClockIcon from '~/assets/svg/clock.vue'
import Steps from '../run-list/component/steps.vue'
import QuickSetDialog from './components/quick-set-dialog.vue'
import EditBatteryDialog from './components/edit-battery-dialog.vue'
import EditOrderDialog from './components/edit-order-dialog.vue'
import SelectDeviceDialog from './components/select-device-dialog.vue'
import BatchImportDevice from './components/batch-import-device.vue'
import ConfigDetail from '../run-task-detail/component/task/config-detail.vue'
import { debounce } from 'lodash-es'
import _ from 'lodash'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const stepOneFormRef = ref()
const stepTwoFormRef = ref()
const stepFourFormRef = ref()
const multipleTableRef = ref()
const realDeviceDialogFormRef = ref()
const batchEditDialogFormRef = ref()
const pages = ref(_.cloneDeep(page))
const customCapacity = ref(false)
const customPrice = ref(false)
const customOperationTime = ref(false)
const customNotFullySwapSwitch = ref(false)
const customEpsSwitch = ref(false)
const customSilentModeSwitch = ref(false)
const customBatteryExchangeSwitch = ref(false)
const customBatteryRestSwitch = ref(false)
const isRealDevice = ref(false)
const realSwapElecConsumption = ref(null)
const realDeviceLoading = ref(false)
const remoteLoading = ref(false)
const quickLoading = ref(false)
const confirmLoading = ref(false)
const viewConfigLoading = ref(false)
const cancelDialogVisible = ref(false)
const realDeviceDialogVisible = ref(false)
const batchEditDialogVisible = ref(false)
const clearDialogVisible = ref(false)
const quickSettingDialogVisible = ref(false)
const editBatteryDialogVisible = ref(false)
const editOrderDialogVisible = ref(false)
const deviceDialogVisible = ref(false)
const batchImportVisible = ref(false)
const skipLevelSwapSwitch = ref(false)
const swappingFailureSwitch = ref(false)
const current = ref(1)
const pageTitle = ref('')
const pageSubTitle = ref('')
const createType = ref('')
const createDevice = ref('')
const createDeviceName = ref('')
const createMethod = ref('')
const createProject = ref('')
const editOrderDialogTitle = ref('')
const quickSetType = ref('')
const battery_50_kwh = computed(() => batteryTableList.value.filter((item: any) => item.battery_rated_kwh == 50).length)
const battery_60_kwh = computed(() => batteryTableList.value.filter((item: any) => item.battery_rated_kwh == 60).length)
const battery_75_kwh = computed(() => batteryTableList.value.filter((item: any) => item.battery_rated_kwh == 75).length)
const battery_85_kwh = computed(() => batteryTableList.value.filter((item: any) => item.battery_rated_kwh == 85).length)
const battery_100_kwh = computed(() => batteryTableList.value.filter((item: any) => item.battery_rated_kwh == 100).length)
const battery_102_kwh = computed(() => batteryTableList.value.filter((item: any) => item.battery_rated_kwh == 102).length)
const battery_150_kwh = computed(() => batteryTableList.value.filter((item: any) => item.battery_rated_kwh == 150).length)
const order_50_kwh = computed(() => orderList.value.filter((item: any) => item.target_battery_rated_kwh == 50).length)
const order_60_kwh = computed(() => orderList.value.filter((item: any) => item.target_battery_rated_kwh == 60).length)
const order_75_kwh = computed(() => orderList.value.filter((item: any) => item.target_battery_rated_kwh == 75).length)
const order_85_kwh = computed(() => orderList.value.filter((item: any) => item.target_battery_rated_kwh == 85).length)
const order_100_kwh = computed(() => orderList.value.filter((item: any) => item.target_battery_rated_kwh == 100).length)
const order_102_kwh = computed(() => orderList.value.filter((item: any) => item.target_battery_rated_kwh == 102).length)
const order_150_kwh = computed(() => orderList.value.filter((item: any) => item.target_battery_rated_kwh == 150).length)
const isCustomStep2 = computed(() => customOperationTime.value || customNotFullySwapSwitch.value || customEpsSwitch.value || customSilentModeSwitch.value || customBatteryExchangeSwitch.value || customBatteryRestSwitch.value)
const deviceOptions = ref([] as any)
const multipleSelection = ref([] as any)
const selectedSlot = ref([] as any)
const orderList = ref([] as any)
const selectDeviceList = ref([] as any)
const batteryTableList = ref([] as any)
const batteryOwnershipOptions = ref(['NIO', 'Bac', 'Car_owner'])
const userOwnershipOptions = ref(['BaaS', 'BuyOut'])
const singleSteps = ref([
  {
    title: '换电站配置',
    description: '配置设备基本信息，容量、电价等'
  },
  {
    title: '策略配置',
    description: '配置充换电运营策略，个性化仿真设备'
  },
  {
    title: '电池配置',
    description: '配置电池仓内初始每块电池的信息'
  },
  {
    title: '订单配置',
    description: '自定义配置换电用户行为'
  },
  {
    title: '配置总览',
    description: '查看当前配置的所有信息'
  }
])
const batchSteps = ref([
  {
    title: '换电站配置',
    description: '配置设备基本信息，容量、电价等'
  },
  {
    title: '策略配置',
    description: '配置充换电运营策略，个性化仿真设备'
  },
  {
    title: '完成创建',
    description: '完成批量生成配方'
  }
])
const swapTimeoptions = ref([
  {
    label: '00:00 - 24:00',
    value: '0-24'
  },
  {
    label: '07:00 - 22:00',
    value: '7-22'
  },
  {
    label: '08:00 - 20:00',
    value: '8-20'
  },
  {
    label: '08:00 - 22:00',
    value: '8-22'
  },
  {
    label: '09:00 - 21:00',
    value: '9-21'
  }
])
const stepOneForm = ref({
  datePicker: [],
  station_capacity: 500,
  circuit_1_capacity: 250,
  circuit_2_capacity: 250,
  electricity_price_model: 'one_price',
  one_price_price: 0.8,
  different_price: [
    { start: '00:00', end: '', price: '' },
    { start: '', end: '24:00', price: '' }
  ],
  devices: []
} as any)
const stepTwoForm = ref({
  swap_time: '0-24',
  notfully_swap_switch: false,
  soc_lower_limit: 78,
  soc_upper_limit: 87,
  cms_switch: false,
  eps_model_mode: 3,
  eps_high_price_peak_shift_enable: 1,
  eps_peak_shift_start_time: '23:00',
  eps_peak_shift_end_time: '07:00',
  eps_peak_shift_power_limit: 20,
  silent_mode_switch: false,
  battery_exchange_switch: true,
  battery_rest_switch: false,
  default_rest_current: 0,
  default_hanging_duration: 0,
  default_hanging_step: 10,
  default_hanging_current_max: 50
} as any)
const stepFourForm = ref({
  config_name: '',
  remark: ''
})
const realDeviceDialogForm = ref({
  device_id: '',
  datePicker: []
})
const batchEditDialogForm = ref({
  battery_soc_selected: false,
  battery_soc: 30,
  battery_rated_kwh_selected: false,
  battery_rated_kwh: 75,
  pack_max_temperature_selected: false,
  pack_max_temperature: 26,
  charging_stop_soc_selected: false,
  charging_stop_soc: 93,
  battery_ownership_selected: false,
  battery_ownership: 'NIO'
} as any)
const batchEditDialogFormClone = _.cloneDeep(batchEditDialogForm.value)
const quickSettingDialogForm = ref({
  battery_50_kwh: 0,
  battery_60_kwh: 0,
  battery_75_kwh: 0,
  battery_85_kwh: 0,
  battery_100_kwh: 0,
  battery_102_kwh: 0,
  battery_150_kwh: 0,
  skip_level_swap_switch: false,
  swapping_failure_switch: false
} as any)
const quickSettingDialogFormClone = _.cloneDeep(quickSettingDialogForm.value)
const editBatteryForm = ref({} as any)
const cloneEditBatteryForm = ref({} as any)
const createParams = ref(null as any)
const editOrderForm = ref({
  service_id: '',
  vehicle_id: '',
  user_arrival_time: '',
  target_battery_rated_kwh: 75,
  real_target_battery_rated_kwh: 75,
  battery_id: '',
  battery_soc: 10,
  battery_rated_kwh: 75,
  real_battery_rated_kwh: 75,
  pack_max_temperature: 26,
  battery_ownership: 'NIO',
  user_ownership: 'BaaS',
  battery_rest_label: 1
} as any)
const cloneEditOrderForm = _.cloneDeep(editOrderForm.value)
const backupEditOrderForm = ref({} as any)
const dateValidator = (rule: any, value: any, callback: any) => {
  let daterange = value[1] - value[0]
  if (daterange > 86400000 || daterange < 300000) {
    return callback(new Error())
  } else {
    callback()
  }
}
const dateFutureValidator = (rule: any, value: any, callback: any) => {
  if (value[0] > Date.now() || value[1] > Date.now()) {
    return callback(new Error())
  } else {
    callback()
  }
}
const socValidator = (rule: any, value: any, callback: any) => {
  if (value < 0 || value > 100) {
    return callback(new Error('SOC必须在0和100之间'))
  } else {
    callback()
  }
}
const batteryIdValidator = (rule: any, value: any, callback: any) => {
  if (!value) callback()
  const sameBatteryArr = batteryTableList.value.filter((item: any) => item.slot_id !== editBatteryForm.value.slot_id && item.battery_id === value)
  if (sameBatteryArr.length > 0) {
    return callback(new Error('同一配方中，电池定位标签不能重复'))
  } else {
    callback()
  }
}
const batteryValidator = (rule: any, value: any, callback: any) => {
  const { battery_50_kwh, battery_60_kwh, battery_75_kwh, battery_85_kwh, battery_100_kwh, battery_102_kwh, battery_150_kwh } = quickSettingDialogForm.value
  const batteryTotal = Number(battery_50_kwh) + Number(battery_60_kwh) + Number(battery_75_kwh) + Number(battery_85_kwh) + Number(battery_100_kwh) + Number(battery_102_kwh) + Number(battery_150_kwh)
  if (value === '') {
    callback(new Error('请输入数量'))
  } else if ((createProject.value == 'PUS3' && value > 20) || (createProject.value == 'PowerSwap2' && value > 13) || (createProject.value == 'PUS4' && value > 23)) {
    callback(new Error(`上限为${batteryLimit[createProject.value]}块`))
  } else if ((createProject.value == 'PUS3' && batteryTotal > 20) || (createProject.value == 'PowerSwap2' && batteryTotal > 13) || (createProject.value == 'PUS4' && batteryTotal > 23)) {
    callback(new Error(`总数上限为${batteryLimit[createProject.value]}块`))
  } else {
    callback()
  }
}
const temValidator = (rule: any, value: any, callback: any) => {
  if (value < -20 || value > 50) {
    return callback(new Error('温度必须在-20和50之间'))
  } else {
    callback()
  }
}
const deviceValidator = (rule: any, value: any, callback: any) => {
  if (value.length < 2 || value.length > 50) {
    return callback(new Error('换电站至少选择2个，至多选择50个'))
  } else {
    callback()
  }
}
const socLowerLimitValidator = (rule: any, value: any, callback: any) => {
  if (isEmptyData(value)) {
    return callback(new Error('必填'))
  } else if (Number(value) > 100) {
    return callback(new Error('需在0-100之间'))
  } else if (Number(value) > Number(stepTwoForm.value.soc_upper_limit)) {
    return callback(new Error('需小于/等于上限'))
  } else {
    callback()
  }
}
const socUpperLimitValidator = (rule: any, value: any, callback: any) => {
  if (isEmptyData(value)) {
    return callback(new Error('必填'))
  } else if (Number(value) > 100) {
    return callback(new Error('需在0-100之间'))
  } else if (Number(value) < Number(stepTwoForm.value.soc_lower_limit)) {
    return callback(new Error('需大于/等于下限'))
  } else {
    callback()
  }
}
const settingRules = reactive({
  datePicker: [
    { required: true, message: t('configList.pp_select_time'), trigger: 'change' },
    {
      validator: (rule: any, value: any, cb: any) => dateValidator(rule, value, cb),
      message: t('configList.pp_date_rangeprompt'),
      trigger: 'change'
    },
    {
      validator: (rule: any, value: any, cb: any) => dateFutureValidator(rule, value, cb),
      message: '不能选择未来时间',
      trigger: 'change'
    }
  ],
  station_capacity: [{ required: true, message: t('configList.pp_enter_total'), trigger: 'blur' }],
  circuit_1_capacity: [{ required: true, message: t('configList.pp_enter_line1'), trigger: 'blur' }],
  circuit_2_capacity: [{ required: true, message: t('configList.pp_enter_line2'), trigger: 'blur' }],
  electricity_price_model: [{ required: true, message: t('configList.pp_select_mode'), trigger: 'change' }],
  one_price_price: [{ required: true, message: t('configList.pp_enter_price'), trigger: 'blur' }],
  device_id: [{ required: true, message: t('configList.pp_select_device'), trigger: 'change' }],
  devices: [{ required: true, validator: deviceValidator, trigger: 'change' }],
  soc_lower_limit: [{ required: true, validator: socLowerLimitValidator, trigger: 'blur' }],
  soc_upper_limit: [{ required: true, validator: socUpperLimitValidator, trigger: 'blur' }],
  default_rest_current: [{ required: true, message: '必填', trigger: 'blur' }],
  default_hanging_duration: [{ required: true, message: '必填', trigger: 'blur' }],
  default_hanging_step: [{ required: true, message: '必填', trigger: 'blur' }],
  eps_peak_shift_start_time: [{ required: true, message: '必填', trigger: 'blur' }],
  eps_peak_shift_end_time: [{ required: true, message: '必填', trigger: 'blur' }],
  default_hanging_current_max: [{ required: true, message: '必填', trigger: 'blur' }]
})

const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})

const emptyPrefix = shallowRef({
  render() {
    return h(DatePrefix, { color: '#BFBFBF' })
  }
})

const disabledDateFun = (time: any) => {
  return time.getTime() > Date.now()
}

const onChange = (index: number) => {
  console.log('current:', index)
}

const isEmptyData = (data: any) => {
  return data === '' || data === undefined || data === null || data === 'null'
}

/**
 * @description: 温度输入限制
 * @param {*} value
 * @return {*}
 */
const handleInput = (value: any) => {
  const regex = /^-?\d*$/
  if (regex.test(value)) {
    batchEditDialogForm.value.pack_max_temperature = value
  } else {
    nextTick(() => {
      batchEditDialogForm.value.pack_max_temperature = batchEditDialogForm.value.pack_max_temperature.replace(/[^\d-]|(?!^)-/g, '')
    })
  }
}

/**
 * @description: 处理提交数据格式
 * @return {*}
 */
const formatParams = () => {
  if (stepOneForm.value.electricity_price_model != 'one_price') {
    stepOneForm.value.different_price.forEach((item: any) => {
      item.price = Number(item.price)
    })
  }
  const basicParams = {
    config_name: stepFourForm.value.config_name,
    remark: stepFourForm.value.remark,
    project: createProject.value,
    start_ts: stepOneForm.value.datePicker[0],
    end_ts: stepOneForm.value.datePicker[1],
    station_capacity: Number(stepOneForm.value.station_capacity),
    circuit_1_capacity: Number(stepOneForm.value.circuit_1_capacity),
    circuit_2_capacity: Number(stepOneForm.value.circuit_2_capacity),
    electricity_price_model: stepOneForm.value.electricity_price_model,
    electricity_detail_list: stepOneForm.value.electricity_price_model == 'one_price' ? [{ start: '00:00', end: '24:00', price: Number(stepOneForm.value.one_price_price) }] : stepOneForm.value.different_price,
    operation_start_hour: Number(stepTwoForm.value.swap_time.split('-')[0]),
    operation_end_hour: Number(stepTwoForm.value.swap_time.split('-')[1]),
    notfully_swap_switch: {
      switch_value: stepTwoForm.value.notfully_swap_switch ? 1 : 0,
      soc_lower_limit: Number(stepTwoForm.value.soc_lower_limit),
      soc_upper_limit: Number(stepTwoForm.value.soc_upper_limit)
    },
    silent_mode_switch: stepTwoForm.value.silent_mode_switch ? 1 : 0,
    cms_switch: stepTwoForm.value.cms_switch ? 1 : 0,
    battery_exchange_switch: stepTwoForm.value.battery_exchange_switch ? 1 : 0,
    battery_rest_switch: {
      switch_value: stepTwoForm.value.battery_rest_switch ? 1 : 0,
      default_rest_current: Number(stepTwoForm.value.default_rest_current),
      default_hanging_duration: Number(stepTwoForm.value.default_hanging_duration),
      default_hanging_step: Number(stepTwoForm.value.default_hanging_step),
      default_hanging_current_max: Number(stepTwoForm.value.default_hanging_current_max)
    },
    is_real_device: isRealDevice.value,
    description: createDeviceName.value
  } as any
  if (stepTwoForm.value.cms_switch) {
    basicParams.eps_params = {
      eps_model_mode: stepTwoForm.value.eps_model_mode,
      eps_high_price_peak_shift_enable: stepTwoForm.value.eps_high_price_peak_shift_enable,
      eps_peak_shift_power_effective_time: `${stepTwoForm.value.eps_peak_shift_start_time}-${stepTwoForm.value.eps_peak_shift_end_time}`,
      eps_peak_shift_power_limit: Number(stepTwoForm.value.eps_peak_shift_power_limit)
    }
  }
  let params: any =
    createMethod.value == 'single'
      ? {
          ...basicParams,
          device_id: createDevice.value ? createDevice.value : 'full',
          battery_info: batteryTableList.value.map((item: any) => {
            return {
              slot_id: item.slot_id,
              battery_soc: isEmptyData(item.battery_soc) ? null : Number(item.battery_soc),
              battery_rated_kwh: isEmptyData(item.battery_rated_kwh) ? null : Number(item.battery_rated_kwh),
              pack_max_temperature: isEmptyData(item.pack_max_temperature) ? null : Number(item.pack_max_temperature),
              charging_stop_soc: isEmptyData(item.charging_stop_soc) ? null : Number(item.charging_stop_soc),
              battery_id: isEmptyData(item.battery_id) ? null : item.battery_id,
              real_battery_rated_kwh: isEmptyData(item.real_battery_rated_kwh) ? null : Number(item.real_battery_rated_kwh),
              battery_ownership: isEmptyData(item.battery_ownership) ? null : item.battery_ownership
            }
          }),
          service_list: orderList.value.map((item: any) => {
            return {
              battery_id: isEmptyData(item.battery_id) ? null : item.battery_id,
              service_id: isEmptyData(item.service_id) ? null : item.service_id,
              vehicle_id: isEmptyData(item.vehicle_id) ? null : item.vehicle_id,
              user_arrival_time: item.user_arrival_time,
              swapping_time: isEmptyData(item.swapping_time) ? null : item.swapping_time,
              target_battery_rated_kwh: isEmptyData(item.target_battery_rated_kwh) ? null : Number(item.target_battery_rated_kwh),
              battery_soc: isEmptyData(item.battery_soc) ? null : Number(item.battery_soc),
              pack_max_temperature: isEmptyData(item.pack_max_temperature) ? null : Number(item.pack_max_temperature),
              battery_rated_kwh: isEmptyData(item.battery_rated_kwh) ? null : Number(item.battery_rated_kwh),
              real_battery_rated_kwh: isEmptyData(item.real_battery_rated_kwh) ? null : Number(item.real_battery_rated_kwh),
              real_target_battery_rated_kwh: isEmptyData(item.real_target_battery_rated_kwh) ? null : Number(item.real_target_battery_rated_kwh),
              battery_ownership: isEmptyData(item.battery_ownership) ? null : item.battery_ownership,
              user_ownership: isEmptyData(item.user_ownership) ? null : item.user_ownership,
              battery_rest_label: isEmptyData(item.battery_rest_label) ? null : Number(item.battery_rest_label),
            }
          }),
          skip_level_swap_switch: skipLevelSwapSwitch.value ? 1 : 0,
          swapping_failure_switch: swappingFailureSwitch.value ? 1 : 0
        }
      : {
          ...basicParams,
          devices: selectDeviceList.value.map((item: any) => item.device_id)
        }
  if (createMethod.value != 'single') {
    if (!customCapacity.value) {
      delete params.station_capacity
      delete params.circuit_1_capacity
      delete params.circuit_2_capacity
    }
    if (!customPrice.value) {
      delete params.electricity_price_model
      delete params.electricity_detail_list
    }
    if (!customOperationTime.value) {
      delete params.operation_start_hour
      delete params.operation_end_hour
    }
    if (!customNotFullySwapSwitch.value) {
      delete params.notfully_swap_switch
    }
    if (!customEpsSwitch.value) {
      delete params.eps_params
    }
    if (!customSilentModeSwitch.value) {
      delete params.silent_mode_switch
    }
    if (!customBatteryExchangeSwitch.value) {
      delete params.battery_exchange_switch
    }
    if (!customBatteryRestSwitch.value) {
      delete params.battery_rest_switch
    }
  }
  if (createType.value == 'edit') params.config_id = route.query.configID
  return params
}

/**
 * @description: 确认创建配方
 * @return {*}
 */
const handleConfirmSingle = async () => {
  if (!stepFourFormRef.value) return
  await stepFourFormRef.value.validate(async (valid: any, fields: any) => {
    if (valid) {
      createParams.value.config_name = stepFourForm.value.config_name
      createParams.value.remark = stepFourForm.value.remark
      confirmLoading.value = true
      try {
        const res = createMethod.value == 'single' || createType.value == 'clone' ? await apiPostSingleConfig(createParams.value) : await apiPostBatchConfig(createParams.value)
        confirmLoading.value = false
        if (res.err_code) {
          ElMessage.error(res.message)
        } else {
          ElMessage.success('保存成功')
          handleBack()
        }
      } catch (error) {
        confirmLoading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 确认批量导入设备
 * @param {*} devices
 * @return {*}
 */
const handleConfirmBatchImportDevice = (devices: any) => {
  selectDeviceList.value = devices
  batchImportVisible.value = false
}

/**
 * @description: 确认选择设备
 * @param {*} selection
 * @return {*}
 */
const handleConfirmSelectDevice = (selection: any) => {
  selectDeviceList.value = selection
  deviceDialogVisible.value = false
}

/**
 * @description: 删除设备
 * @param {*} index
 * @return {*}
 */
const handleDeleteDevice = (index: any) => {
  selectDeviceList.value.splice(index, 1)
}

/**
 * @description: 删除订单
 * @param {*} row
 * @return {*}
 */
const handleDeleteOrder = (row: any) => {
  orderList.value = orderList.value.filter((item: any) => item.id != row.id)
  pages.value.total = orderList.value.length
  ElMessage.success('删除成功')
}

/**
 * @description: 确认增加/编辑订单
 * @param {*} form
 * @return {*}
 */
const handleConfirmEditOrder = (form: any) => {
  if (editOrderDialogTitle.value == '增加订单') {
    const newOrder = { ...form, id: generateUUID(), real_battery_rated_kwh: form.battery_rated_kwh, real_target_battery_rated_kwh: form.target_battery_rated_kwh }
    orderList.value.unshift(newOrder)
    pages.value.current = 1
    pages.value.total = orderList.value.length
    ElMessage.success('新增成功')
  } else {
    const newOrder = { ...form, real_battery_rated_kwh: form.battery_rated_kwh == backupEditOrderForm.value.battery_rated_kwh ? form.real_battery_rated_kwh : form.battery_rated_kwh, real_target_battery_rated_kwh: form.target_battery_rated_kwh == backupEditOrderForm.value.target_battery_rated_kwh ? form.real_target_battery_rated_kwh : form.target_battery_rated_kwh }
    orderList.value = orderList.value.map((item: any) => {
      if (item.id == form.id) {
        return newOrder
      } else {
        return item
      }
    })
    ElMessage.success('编辑成功')
  }
  editOrderDialogVisible.value = false
}

/**
 * @description: 关闭增加/编辑订单
 * @param {*} editOrderDialogFormRef
 * @return {*}
 */
const handleCloseEditOrder = (editOrderDialogFormRef: any) => {
  editOrderForm.value = _.cloneDeep(cloneEditOrderForm)
  if (editOrderDialogFormRef) editOrderDialogFormRef.resetFields()
  editOrderDialogVisible.value = false
}

/**
 * @description: 点击增加订单
 * @return {*}
 */
const handleClickAddOrder = () => {
  editOrderDialogTitle.value = '增加订单'
  editOrderDialogVisible.value = true
}

/**
 * @description: 点击编辑订单
 * @param {*} row
 * @return {*}
 */
const handleClickEditOrder = (row: any) => {
  editOrderForm.value = _.cloneDeep(row)
  backupEditOrderForm.value = _.cloneDeep(row)
  editOrderDialogTitle.value = '编辑订单'
  editOrderDialogVisible.value = true
}

/**
 * @description: 配置订单表格分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
}

/**
 * @description: 点击编辑电池
 * @param {*} row
 * @return {*}
 */
const handleClickEditBattery = (row: any) => {
  editBatteryForm.value = _.cloneDeep(row)
  cloneEditBatteryForm.value = _.cloneDeep(row)
  editBatteryDialogVisible.value = true
}

/**
 * @description: 确认编辑电池
 * @param {*} form
 * @return {*}
 */
const handleConfirmEditBattery = (form: any) => {
  batteryTableList.value = batteryTableList.value.map((item: any) => {
    if (item.slot_id === form.slot_id) {
      return { ...form, real_battery_rated_kwh: form.battery_rated_kwh == cloneEditBatteryForm.value.battery_rated_kwh ? form.real_battery_rated_kwh : form.battery_rated_kwh }
    } else {
      return item
    }
  })
  ElMessage.success('编辑成功')
  editBatteryDialogVisible.value = false
}

/**
 * @description: 点击快速配置
 * @param {*} type
 * @return {*}
 */
const handleClickQuickSet = (type: string) => {
  quickSetType.value = type
  quickSettingDialogVisible.value = true
}

/**
 * @description: 确认电池/订单快速配置
 * @param {*} form
 * @return {*}
 */
const handleConfirmQuickSetting = async (form: any) => {
  quickLoading.value = true
  const batteryConfig = createProject.value == 'PowerSwap2'
    ? `${form.battery_50_kwh},${form.battery_75_kwh},${form.battery_100_kwh},${form.battery_150_kwh}`
    : `${form.battery_50_kwh},${form.battery_60_kwh},${form.battery_75_kwh},${form.battery_85_kwh},${form.battery_100_kwh},${form.battery_102_kwh || 0},${form.battery_150_kwh}`
  const batteryParams = {
    battery_config: batteryConfig,
    project: createProject.value,
    device_id: createDevice.value ? createDevice.value : 'full',
    start_ts: stepOneForm.value.datePicker[0]
  }
  const orderParams = {
    service_count: batteryConfig,
    battery_config: createProject.value == 'PowerSwap2'
      ? `${battery_50_kwh.value},${battery_75_kwh.value},${battery_100_kwh.value},${battery_150_kwh.value}`
      : `${battery_50_kwh.value},${battery_60_kwh.value},${battery_75_kwh.value},${battery_85_kwh.value},${battery_100_kwh.value},${battery_102_kwh.value},${battery_150_kwh.value}`,
    skip_level_swap_switch: form.skip_level_swap_switch ? 1 : 0,
    swapping_failure_switch: form.swapping_failure_switch ? 1 : 0,
    project: createProject.value,
    device_id: createDevice.value ? createDevice.value : 'full',
    start_ts: stepOneForm.value.datePicker[0],
    end_ts: stepOneForm.value.datePicker[1],
    operation_start_hour: Number(stepTwoForm.value.swap_time.split('-')[0]),
    operation_end_hour: Number(stepTwoForm.value.swap_time.split('-')[1])
  }
  try {
    const res = quickSetType.value == 'battery' ? await apiGetQuickBattery(batteryParams) : await apiGetQuickOrder(orderParams)
    quickLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      if (quickSetType.value == 'battery') {
        batteryTableList.value = res.data || []
      } else {
        skipLevelSwapSwitch.value = _.cloneDeep(form.skip_level_swap_switch)
        swappingFailureSwitch.value = _.cloneDeep(form.swapping_failure_switch)
        orderList.value = res.data || []
        if (orderList.value && orderList.value.length > 0) {
          orderList.value.forEach((item: any) => {
            item.id = generateUUID()
          })
        }
        pages.value.total = res.total
      }
      ElMessage.success('快速配置成功')
      quickSettingDialogVisible.value = false
    }
  } catch (error: any) {
    quickLoading.value = false
  }
}

/**
 * @description: 关闭电池/订单快速配置弹窗
 * @param {*} formRef
 * @return {*}
 */
const handleCloseQuickSettingDialog = (formRef: any) => {
  if (!formRef) return
  quickSettingDialogForm.value = _.cloneDeep(quickSettingDialogFormClone)
  formRef.resetFields()
  quickSettingDialogVisible.value = false
}

/**
 * @description: 确认清空所有
 * @return {*}
 */
const handleConfirmClear = () => {
  if (current.value == 3) {
    batteryTableList.value.forEach((item: any) => {
      item.battery_soc = null
      item.pack_max_temperature = null
      item.battery_rated_kwh = null
      item.charging_stop_soc = null
      item.battery_id = null
      item.real_battery_rated_kwh = null
      item.battery_ownership = null
    })
  } else {
    orderList.value = []
    pages.value.total = 0
  }
  clearDialogVisible.value = false
}

/**
 * @description: 勾选（批量编辑）
 * @param {*} val
 * @return {*}
 */
const handleSelectionChange = (val: any) => {
  multipleSelection.value = val.sort((a: any, b: any) => a.slot_id - b.slot_id)
  selectedSlot.value = multipleSelection.value.map((item: any) => item.slot_id)
}

/**
 * @description: 关闭批量编辑弹窗
 * @return {*}
 */
const handleCloseBatchEditDialog = () => {
  if (!batchEditDialogFormRef.value) return
  batchEditDialogForm.value = _.cloneDeep(batchEditDialogFormClone)
  batchEditDialogFormRef.value.resetFields()
  batchEditDialogVisible.value = false
}

/**
 * @description: 确认批量编辑
 * @return {*}
 */
const handleConfirmBatchEdit = async () => {
  if (!batchEditDialogFormRef.value) return
  const { battery_soc_selected, battery_rated_kwh_selected, pack_max_temperature_selected, charging_stop_soc_selected, battery_ownership_selected } = batchEditDialogForm.value
  const selectedParams = battery_soc_selected || battery_rated_kwh_selected || pack_max_temperature_selected || charging_stop_soc_selected || battery_ownership_selected
  await batchEditDialogFormRef.value.validate((valid: any, fields: any) => {
    if (valid && selectedParams) {
      batteryTableList.value.forEach((item: any) => {
        if (selectedSlot.value.includes(item.slot_id)) {
          if (battery_soc_selected) item.battery_soc = Number(batchEditDialogForm.value.battery_soc)
          if (pack_max_temperature_selected) item.pack_max_temperature = Number(batchEditDialogForm.value.pack_max_temperature)
          if (charging_stop_soc_selected) item.charging_stop_soc = Number(batchEditDialogForm.value.charging_stop_soc)
          if (battery_rated_kwh_selected) {
            item.battery_rated_kwh = Number(batchEditDialogForm.value.battery_rated_kwh)
            item.real_battery_rated_kwh = Number(batchEditDialogForm.value.battery_rated_kwh)
          }
          if (battery_ownership_selected) {
            item.battery_ownership = batchEditDialogForm.value.battery_ownership
          }
        }
      })
      ElMessage.success('批量编辑成功')
      multipleTableRef.value!.clearSelection()
      batchEditDialogVisible.value = false
    } else if (!selectedParams) {
      ElMessage.warning('至少选择一个参数')
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: route.query.project, name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 获取初始设备列表
 * @return {*}
 */
const getInitDeviceList = async () => {
  const params = { project: route.query.project, name: 'NIO', limit: 30 }
  const res = await apiGetDevices(params)
  deviceOptions.value = res.data
}

/**
 * @description: 关闭导入真实设备弹窗
 * @return {*}
 */
const handleCloseRealDeviceDialog = () => {
  realDeviceDialogVisible.value = false
  realDeviceDialogFormRef.value && realDeviceDialogFormRef.value.resetFields()
}

/**
 * @description: 确认导入真实设备
 * @return {*}
 */
const handleImportRealDevice = async () => {
  if (!realDeviceDialogFormRef.value) return
  await realDeviceDialogFormRef.value.validate(async (valid: any, fields: any) => {
    if (valid) {
      const params = {
        start_ts: realDeviceDialogForm.value.datePicker[0],
        end_ts: realDeviceDialogForm.value.datePicker[1]
      }
      realDeviceLoading.value = true
      try {
        const res = await apiGetRealDeviceConfig(createProject.value, realDeviceDialogForm.value.device_id, params)
        realDeviceLoading.value = false
        if (res.err_code) {
          ElMessage.error(res.message)
        } else {
          const { station_capacity, circuit_1_capacity, circuit_2_capacity, eps_params, electricity_price_model, electricity_detail_list, battery_info, service_list, operation_start_hour, operation_end_hour, notfully_swap_switch, silent_mode_switch, battery_exchange_switch, battery_rest_switch, real_swap_elec_consumption } = res.data
          stepOneForm.value.datePicker = _.cloneDeep(realDeviceDialogForm.value.datePicker)
          stepOneForm.value.station_capacity = station_capacity
          stepOneForm.value.circuit_1_capacity = circuit_1_capacity
          stepOneForm.value.circuit_2_capacity = circuit_2_capacity
          stepOneForm.value.electricity_price_model = electricity_price_model
          if (electricity_price_model == 'one_price') {
            stepOneForm.value.one_price_price = electricity_detail_list[0].price
          } else {
            stepOneForm.value.different_price = electricity_detail_list
          }
          stepTwoForm.value.swap_time = operation_start_hour + '-' + operation_end_hour
          stepTwoForm.value.notfully_swap_switch = Boolean(notfully_swap_switch.switch_value)
          stepTwoForm.value.soc_lower_limit = notfully_swap_switch.soc_lower_limit
          stepTwoForm.value.soc_upper_limit = notfully_swap_switch.soc_upper_limit
          stepTwoForm.value.cms_switch = Boolean(eps_params)
          if (eps_params) {
            stepTwoForm.value.eps_model_mode = eps_params.eps_model_mode || 3
            stepTwoForm.value.eps_high_price_peak_shift_enable = eps_params.eps_high_price_peak_shift_enable !== undefined ? eps_params.eps_high_price_peak_shift_enable : 1
            stepTwoForm.value.eps_peak_shift_start_time = (eps_params.eps_peak_shift_power_effective_time || '23:00-07:00').split('-')[0]
            stepTwoForm.value.eps_peak_shift_end_time = (eps_params.eps_peak_shift_power_effective_time || '23:00-07:00').split('-')[1]
            stepTwoForm.value.eps_peak_shift_power_limit = eps_params.eps_peak_shift_power_limit || 20
          }
          stepTwoForm.value.silent_mode_switch = Boolean(silent_mode_switch)
          realSwapElecConsumption.value = real_swap_elec_consumption
          stepTwoForm.value.battery_exchange_switch = Boolean(battery_exchange_switch)
          stepTwoForm.value.battery_rest_switch = Boolean(battery_rest_switch.switch_value)
          stepTwoForm.value.default_rest_current = battery_rest_switch.default_rest_current
          stepTwoForm.value.default_hanging_duration = battery_rest_switch.default_hanging_duration
          stepTwoForm.value.default_hanging_step = battery_rest_switch.default_hanging_step
          stepTwoForm.value.default_hanging_current_max = battery_rest_switch.default_hanging_current_max
          batteryTableList.value = battery_info || []
          orderList.value = service_list || []
          if (orderList.value && orderList.value.length > 0) {
            orderList.value.forEach((item: any) => {
              item.id = generateUUID()
            })
          }
          pages.value.total = orderList.value.length
          ElMessage.success('模拟成功')
          isRealDevice.value = true
          createDevice.value = _.cloneDeep(realDeviceDialogForm.value.device_id)
          createDeviceName.value = deviceOptions.value.find((item: any) => item.device_id === createDevice.value).description
          realDeviceDialogVisible.value = false
        }
      } catch (error) {
        realDeviceLoading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 判断电池SOC和电池度数和电池产权是否同时存在或者同时不存在
 * @param {*} batteryData
 * @return {*}
 */
const checkBatteryData = (batteryData: any) => {
  return batteryData.every((item: any) => {
    if ((isEmptyData(item.battery_rated_kwh) && isEmptyData(item.battery_soc) && isEmptyData(item.battery_ownership)) || (!isEmptyData(item.battery_rated_kwh) && !isEmptyData(item.battery_soc) && !isEmptyData(item.battery_ownership))) {
      return true
    } else {
      return false
    }
  })
}

/**
 * @description: 判断用户到达时间是否在换电运营时段内
 * @param {*} time
 * @return {*}
 */
const checkTimeValid = (time: any) => {
  const date = new Date(time).getHours()
  const start = Number(stepTwoForm.value.swap_time.split('-')[0])
  const end = Number(stepTwoForm.value.swap_time.split('-')[1])
  const timeValid = date >= start && date < end
  return timeValid
}

/**
 * @description: 点击上一步
 * @return {*}
 */
const handlePre = () => {
  if (current.value > 1) current.value--
}

/**
 * @description: 点击下一步
 * @return {*}
 */
const handleNext = async () => {
  if (current.value == 1) {
    if (!stepOneFormRef.value) return
    stepOneForm.value.devices = selectDeviceList.value
    await stepOneFormRef.value.validate((valid: any, fields: any) => {
      if (valid) {
        current.value++
      } else {
        console.log('error submit!', fields)
      }
    })
  } else if (current.value == 2) {
    if (!stepTwoFormRef.value) return
    await stepTwoFormRef.value.validate((valid: any, fields: any) => {
      if (valid) {
        current.value++
        if (createMethod.value != 'single') createParams.value = formatParams()
      } else {
        console.log('error submit!', fields)
      }
    })
  } else if (current.value == 3) {
    if (checkBatteryData(batteryTableList.value)) {
      current.value++
    } else {
      ElMessage.warning('电池SOC、电池产权和电池度数必须同时存在或者同时不存在')
    }
  } else if (current.value == 4) {
    const differentBattery = orderList.value.filter((item2: any) => item2.target_battery_rated_kwh !== '' && !batteryTableList.value.find((item1: any) => item1.battery_rated_kwh === item2.target_battery_rated_kwh)).map((item: any) => item.target_battery_rated_kwh)
    if (differentBattery.length > 0) {
      const filterBattery = [...new Set(differentBattery)].sort((a: any, b: any) => a - b).map((item: any) => item + 'kWh')
      ElMessage.warning(`当前站内没有 ${filterBattery.join('、')} 电池`)
    } else if (orderList.value.find((item: any) => item.user_arrival_time < stepOneForm.value.datePicker[0] || item.user_arrival_time >= stepOneForm.value.datePicker[1])) {
      ElMessage.warning('用户到达时间必须位于仿真时段内')
    } else if (orderList.value.find((item: any) => !checkTimeValid(item.user_arrival_time))) {
      ElMessage.warning('用户到达时间必须位于换电运营时段内')
    } else {
      current.value++
      createParams.value = formatParams()
    }
  }
}

/**
 * @description: 添加分时电价
 * @return {*}
 */
const handleAddPrice = () => {
  const lastItem = stepOneForm.value.different_price[stepOneForm.value.different_price.length - 2]
  if (!lastItem.end) {
    ElMessage.warning('请选择结束时间')
    return
  }
  stepOneForm.value.different_price.splice(stepOneForm.value.different_price.length - 1, 0, { start: lastItem.end, end: '', price: '' })
}
const handleDeletePrice = (index: any) => {
  stepOneForm.value.different_price.splice(index, 1)
  stepOneForm.value.different_price[index].start = stepOneForm.value.different_price[index - 1].end
}
const handleChangeEndTime = (time: any, index: any) => {
  stepOneForm.value.different_price[index + 1].start = time
  if (stepOneForm.value.different_price[index + 1].end < time) {
    ElMessage.warning('超出其余所选时间')
    stepOneForm.value.different_price.splice(index + 1)
    stepOneForm.value.different_price.push({ start: time, end: '24:00', price: '' })
  }
}

/**
 * @description: 返回配置管理
 * @return {*}
 */
const handleBack = () => {
  router.push({
    path: `/device-simulation/config-list`
  })
}

/**
 * @description: 点击返回ICON
 * @return {*}
 */
const handleClickBack = () => {
  if (createType.value == 'view') {
    handleBack()
  } else {
    cancelDialogVisible.value = true
  }
}

/**
 * @description: 编辑时获取详情
 * @param {*} configId
 * @return {*}
 */
const getEditInitConfig = async (configId: any) => {
  try {
    const res = await apiGetConfigDetail(configId)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      const { start_ts, end_ts, cms_switch, station_capacity, circuit_1_capacity, circuit_2_capacity, eps_params, electricity_price_model, electricity_detail_list, device_id, battery_info, service_list, description, remark, config_name_origin, is_real_device, operation_start_hour, operation_end_hour, notfully_swap_switch, silent_mode_switch, battery_exchange_switch, battery_rest_switch, skip_level_swap_switch, swapping_failure_switch, real_swap_elec_consumption } = res.data
      stepOneForm.value.datePicker = [start_ts, end_ts]
      stepOneForm.value.station_capacity = station_capacity
      stepOneForm.value.circuit_1_capacity = circuit_1_capacity
      stepOneForm.value.circuit_2_capacity = circuit_2_capacity
      stepOneForm.value.electricity_price_model = electricity_price_model
      if (electricity_price_model == 'one_price') {
        stepOneForm.value.one_price_price = electricity_detail_list[0].price
      } else {
        stepOneForm.value.different_price = electricity_detail_list
      }
      isRealDevice.value = is_real_device
      createDevice.value = device_id
      createDeviceName.value = description || '未命名设备'
      stepTwoForm.value.swap_time = operation_start_hour + '-' + operation_end_hour
      stepTwoForm.value.notfully_swap_switch = Boolean(notfully_swap_switch.switch_value)
      stepTwoForm.value.soc_lower_limit = notfully_swap_switch.soc_lower_limit
      stepTwoForm.value.soc_upper_limit = notfully_swap_switch.soc_upper_limit
      stepTwoForm.value.cms_switch = Boolean(cms_switch)
      if (eps_params) {
        stepTwoForm.value.eps_model_mode = eps_params.eps_model_mode || 3
        stepTwoForm.value.eps_high_price_peak_shift_enable = eps_params.eps_high_price_peak_shift_enable !== undefined ? eps_params.eps_high_price_peak_shift_enable : 1
        stepTwoForm.value.eps_peak_shift_start_time = (eps_params.eps_peak_shift_power_effective_time || '23:00-07:00').split('-')[0]
        stepTwoForm.value.eps_peak_shift_end_time = (eps_params.eps_peak_shift_power_effective_time || '23:00-07:00').split('-')[1]
        stepTwoForm.value.eps_peak_shift_power_limit = eps_params.eps_peak_shift_power_limit || 20
      }
      stepTwoForm.value.silent_mode_switch = Boolean(silent_mode_switch)
      realSwapElecConsumption.value = real_swap_elec_consumption
      stepTwoForm.value.battery_exchange_switch = Boolean(battery_exchange_switch)
      stepTwoForm.value.battery_rest_switch = Boolean(battery_rest_switch.switch_value)
      stepTwoForm.value.default_rest_current = battery_rest_switch.default_rest_current
      stepTwoForm.value.default_hanging_duration = battery_rest_switch.default_hanging_duration
      stepTwoForm.value.default_hanging_step = battery_rest_switch.default_hanging_step
      stepTwoForm.value.default_hanging_current_max = battery_rest_switch.default_hanging_current_max
      batteryTableList.value = battery_info || []
      orderList.value = service_list || []
      if (orderList.value && orderList.value.length > 0) {
        orderList.value.forEach((item: any) => {
          item.id = generateUUID()
        })
      }
      skipLevelSwapSwitch.value = Boolean(skip_level_swap_switch)
      swappingFailureSwitch.value = Boolean(swapping_failure_switch)
      quickSettingDialogForm.value.skip_level_swap_switch = Boolean(skip_level_swap_switch)
      quickSettingDialogForm.value.swapping_failure_switch = Boolean(swapping_failure_switch)
      pages.value.total = orderList.value.length
      stepFourForm.value.config_name = config_name_origin
      stepFourForm.value.remark = remark
    }
  } catch (error) {}
}

/**
 * @description: 获取配置总览详情
 * @param {*} configId
 * @return {*}
 */
const getViewConfig = async (configId: any) => {
  console.log('开始获取配置详情，configId:', configId)
  viewConfigLoading.value = true
  try {
    const res = await apiGetConfigDetail(configId)
    console.log('API 响应:', res)
    if (res && res.err_code) {
      console.error('API 返回错误:', res.message)
      ElMessage.error(res.message || '获取配置详情失败')
    } else if (res && res.data) {
      createParams.value = res.data
      console.log('配置详情加载成功')
    } else {
      console.error('API 响应格式异常:', res)
      ElMessage.error('配置详情数据格式异常')
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    ElMessage.error('网络请求失败，请检查网络连接')
  } finally {
    viewConfigLoading.value = false
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  console.log('初始化页面参数:', initParams)
  createMethod.value = initParams.method
  createProject.value = initParams.project
  createType.value = initParams.type

  if (createType.value == 'add') {
    pageTitle.value = '新建配置'
    pageSubTitle.value = createMethod.value == 'single' ? '单配方' : '批量生成配方'
    batteryTableList.value = initBatteryList[createProject.value]
  } else if (createType.value == 'edit') {
    pageTitle.value = initParams.configName
    getEditInitConfig(initParams.configID)
  } else if (createType.value == 'clone') {
    pageTitle.value = '克隆配方 ' + initParams.configName
    getEditInitConfig(initParams.configID)
  } else if (createType.value == 'view') {
    pageTitle.value = '配置总览'
    if (initParams.configID) {
      getViewConfig(initParams.configID)
    } else {
      console.error('缺少 configID 参数')
      ElMessage.error('缺少配置ID参数')
    }
  }

  if (createType.value != 'view' && createMethod.value == 'single') {
    getInitDeviceList()
  }
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.create-config-container {
  font-family: 'Blue Sky Standard';
  height: 100%;
  display: flex;
  flex-direction: column;
  .header-container {
    display: flex;
    align-items: center;
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
    .back-title {
      color: #1f1f1f;
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
    }
    .back-subtitle {
      color: #8c8c8c;
      font-size: 16px;
      line-height: 24px;
      margin-left: 8px;
    }
  }
  .config-container {
    flex: 1;
    padding: 0 24px 24px;
    background: #f8f8f8;
    :deep(.three-capacity) {
      display: flex;
      .el-form-item__content {
        flex-wrap: nowrap;
      }
      .rest-switch-form {
        .el-form-item__label {
          color: #595959;
        }
        .el-form-item__label:after {
          content: '*';
          color: var(--el-color-danger);
          margin-left: 4px;
        }
      }
    }
    :deep(.custom-button),
    :deep(.step2-custom-button) {
      width: 88px;
      height: 32px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #262626;
      font-size: 14px;
      cursor: pointer;
      position: relative;
    }
    :deep(.step2-custom-button) {
      width: 120px;
    }
    :deep(.hook-icon) {
      position: absolute;
      right: -1px;
      top: 0;
    }
    :deep(.active-custom-button) {
      border: 1px solid #00bebe;
    }
    :deep(.el-form) {
      .el-form-item__label {
        color: #262626;
        width: 144px;
      }
      .line-form-item .el-form-item__label {
        width: auto;
        color: #595959;
        padding-right: 8px;
      }
      .soc-form-item .el-form-item__label {
        color: #262626;
      }
      .el-radio {
        margin-right: 40px;
        .el-radio__label {
          color: #262626;
          font-weight: normal;
        }
      }
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }
      input[type='number'] {
        -moz-appearance: textfield;
        appearance: textfield;
      }
    }
    :deep(.price-item) {
      margin-bottom: 16px;
      .el-form-item {
        margin-bottom: 0px;
      }
    }
    :deep(.long-append .el-input-group__append) {
      width: 50px;
      color: #262626;
    }
    :deep(.short-append .el-input-group__append) {
      width: 37px;
      color: #262626;
    }
    .single-step1-content {
      background: #fff;
      padding: 24px;
      margin-top: 20px;
      border: 1px solid #dcf2f3;
      border-radius: 4px;
      .device-tag {
        height: 32px;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 14px;
        line-height: 22px;
        color: #262626;
        background: #e5f9f9;
        display: flex;
        align-items: center;
      }
    }
    .batch-step1-content {
      background: #fff;
      padding: 24px;
      margin-top: 20px;
      border: 1px solid #dcf2f3;
      border-radius: 4px;
      .device-show-tag {
        margin-left: 144px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px 8px;
        .tag-content {
          height: 32px;
          padding: 3px 8px;
          border-radius: 4px;
          font-size: 14px;
          color: #262626;
          background: #e5f9f9;
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
    .step2-content-container,
    .step3-content-container {
      background: #fff;
      padding: 24px;
      margin-top: 20px;
      border: 1px solid #dcf2f3;
      border-radius: 4px;
      .step-two-title {
        font-size: 16px;
        line-height: 24px;
        color: #262626;
        font-weight: bold;
        margin-bottom: 16px;
      }
      .el-button + .el-button {
        margin-left: 16px;
      }
      .order-total {
        display: flex;
        padding: 8px 16px 4px;
        border: 1px solid #ade8e8;
        border-radius: 4px;
        .battery-type {
          color: #262626;
          font-size: 12px;
          line-height: 18px;
        }
        .battery-order {
          color: #01a0ac;
          font-size: 16px;
          line-height: 24px;
          font-weight: bold;
          .order-unit {
            font-size: 12px;
            line-height: 24px;
            font-weight: normal;
          }
        }
      }
      :deep(.pagination-box) {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        .el-pagination__total {
          color: #595959;
        }
        .el-input__inner {
          color: #595959;
        }
        .el-pager li {
          background: #f0f0f0;
          color: #595959;
        }
        .el-pager li.is-active {
          background: #00bebe;
          color: #fff;
        }
        .el-pagination__jump {
          color: #595959;
        }
      }
    }
    .step4-content-container {
      margin-top: 20px;
      .step4-content-card {
        background: #fff;
        padding: 24px;
        border: 1px solid #dcf2f3;
        border-radius: 4px;
        .card-title {
          color: #262626;
          font-size: 16px;
          line-height: 24px;
          font-weight: bold;
          margin-bottom: 16px;
        }
        :deep(.el-form) {
          display: grid;
          align-items: flex-start;
          grid-template-columns: 2fr 3fr;
          column-gap: 80px;
          .el-form-item {
            margin: 0;
            .el-form-item__label {
              color: #595959;
              width: auto;
              padding-right: 10px;
            }
          }
        }
      }
      .list-card:hover {
        border-color: #00bebe;
      }
    }
  }
  .view-all-container {
    background: #f8f8f8;
    padding: 0 24px 14px;
    .basic-info-container {
      padding: 24px;
      background: #fff;
      border: 1px solid #dcf2f3;
      border-radius: 4px;
      margin-bottom: 16px;
      .basic-title {
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 16px;
        color: #262626;
        font-weight: 600;
      }
      .basic-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        .label-text {
          width: 100px;
          font-size: 14px;
          line-height: 22px;
          color: #595959;
          margin-right: 24px;
          font-weight: 600;
        }
        .value-text {
          font-size: 14px;
          line-height: 22px;
          color: #262626;
        }
      }
      &:hover {
        border-color: #00bebe;
      }
    }
  }
  :deep(.giveup-dialog) {
    .el-dialog__header {
      padding: 24px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
    .dialog_content {
      font-family: 'Blue Sky Standard';
      font-size: 14px;
      line-height: 22px;
      color: #262626;
    }
  }
  :deep(.common-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
    .vertical-form {
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          color: #595959;
          margin-bottom: 4px;
        }
      }
    }
    .batch-edit-form {
      .el-form-item {
        flex: 1;
        margin-bottom: 0;
        .el-form-item__label {
          color: #595959;
        }
        .el-form-item__content {
          position: relative;
          justify-content: flex-end;
          .el-form-item__error {
            display: flex;
            justify-content: flex-end;
            white-space: nowrap;
            right: 0;
          }
        }
      }
    }
    .quick-setting-form {
      .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          padding-right: 10px;
          white-space: nowrap;
          color: #595959;
        }
        .el-form-item__error {
          display: flex;
          justify-content: flex-end;
          white-space: nowrap;
        }
      }
    }
  }
  :deep(.el-dialog__title) {
    font-family: Blue Sky Standard;
    font-size: 18px;
    font-weight: 420;
    line-height: 26px;
    color: #1f1f1f;
  }
  :deep(.el-dialog__headerbtn .el-dialog__close) {
    font-size: 20px;
    color: #262626;
  }
  :deep(.hide-required) {
    .el-form-item__label:after {
      content: '' !important;
    }
  }
  :deep(.el-range-editor.el-input__wrapper) {
    padding: 0;
  }
  :deep(.text-button) {
    color: #595959;
    border: none;
    &:hover {
      color: #595959 !important;
      background-color: #f5f5f5 !important;
    }
    &:focus {
      color: #595959 !important;
      background-color: #f0f0f0 !important;
    }
  }
  :deep(.cancel-button:not(.is-disabled)) {
    color: #01a0ac;
    border: 1px solid #00bebe;
    &:hover {
      color: #01a0ac !important;
      background-color: #e5f9f9 !important;
      border: 1px solid #00bebe;
    }
    &:focus {
      color: #01a0ac !important;
      background-color: #9cebe7 !important;
      border: 1px solid #00bebe;
    }
  }
  :deep(.cancel-button.is-disabled) {
    color: rgba(1, 160, 172, 0.5);
    border: 1px solid rgba(0, 190, 190, 0.5);
    background-color: #fff !important;
  }
  :deep(.clear-button) {
    color: #f83535;
    border: 1px solid #f83535;
    &:hover {
      color: #f83535 !important;
      background-color: #fff2f0 !important;
      border: 1px solid #f83535;
    }
    &:focus {
      color: #f83535 !important;
      background-color: #ffb8b0 !important;
      border: 1px solid #f83535;
    }
  }
  :deep(.el-button--primary:not(:disabled):hover) {
    background: #21ccc6 !important;
    color: #fff !important;
    border-color: #21ccc6;
  }
  :deep(.el-button--primary:focus) {
    background: #009499 !important;
    color: #fff !important;
    border-color: #009499;
  }
  :deep(.el-button) {
    font-weight: 420;
    font-family: 'Blue Sky Standard';
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  :deep(.el-textarea__inner) {
    color: #262626;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    .empty-text {
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}
</style>
