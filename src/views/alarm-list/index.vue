<template>
  <div class="alarm-list-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_alarm_list') }}</div>
      <el-button class="welkin-primary-button" @click="handleJumpAnalysis" v-if="hasAnalysisPermission">
        <AnalysisIcon />
        <span class="margin_l-4">{{ $t('alarmList.pp_alarm_analysis') }}</span>
      </el-button>
    </div>

    <div class="content-container">
      <el-form :model="form" :class="showMore ? 'unfold-container' : 'fold-container'">
        <el-form-item :label="$t('alarmList.pp_alarm_time')">
          <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_alarm_description')">
          <el-select v-model="form.data_id" multiple collapse-tags collapse-tags-tooltip clearable filterable remote :placeholder="$t('alarmList.pp_keywords')" :remote-method="remoteMethod" :loading="remoteLoading" class="width-full">
            <el-option v-for="item in pointOptions" :key="item.value" :value="item.value" :label="item.label + '-' + item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.value }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_alarm_level')" v-if="showMore">
          <el-select v-model="form.alarm_level" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
            <el-option v-for="item in alarmLevelOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_alarm_status')" v-if="showMore">
          <el-select v-model="form.state" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
            <el-option v-for="item in stateOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_alarm_type')" v-if="showMore">
          <el-select v-model="form.alarm_type" :placeholder="$t('common.pp_please_select')" clearable filterable @change="handleChangeAlarmType" class="width-full">
            <el-option v-for="item in alarmTypeOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_device')" v-if="showMore">
          <el-select v-model="form.device_id" filterable remote clearable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_battery_id')" v-if="form.alarm_type == 2 && showMore">
          <el-input v-model="form.battery_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
        </el-form-item>
        <el-form-item>
          <div class="flex-box flex_a_i-center">
            <div @click="showMore = !showMore" class="margin_r-14 font-size-14">
              <div class="cursor-pointer flex-box flex_a_i-center gap_4" style="color: #595959" v-if="showMore">
                <UpIcon />
                <span>{{ $t('station.pp_collapse') }}</span>
              </div>
              <div class="cursor-pointer flex-box flex_a_i-center gap_4" style="color: #595959" v-if="!showMore">
                <DownIcon />
                <span>{{ $t('station.pp_expand') }}</span>
              </div>
            </div>
            <el-button class="welkin-primary-button" @click="handleSearch" :loading="loading">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="form.alarm_type != 2 && showMore"> </el-form-item>
        <el-form-item v-if="showMore">
          <div class="flex-box flex_j_c-flex-end width-full">
            <el-button @click="batchImportVisible = true" class="welkin-secondary-button width-108 height-32" style="padding: 5px 16px">
              <UploadIcon />
              <span class="margin_l-4">{{ $t('common.pp_import') }}</span>
            </el-button>
            <el-button @click="handleDownload" class="welkin-secondary-button width-108 height-32" style="padding: 5px 16px">
              <DownloadIcon />
              <span class="margin_l-4">{{ $t('alarmList.pp_alarm_download') }}</span>
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="220" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-box flex_a_i-center width-full">
              <span class="ellipse">{{ scope.row.data_id_description ? scope.row.data_id_description : '-' }}</span>
              <el-popover :width="520" placement="top" v-if="scope.row.servo_fault_list">
                <template #reference>
                  <AlarmIcon class="margin_l-6" />
                </template>
                <el-table :data="scope.row.servo_fault_list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }">
                  <el-table-column prop="id" :label="$t('alarmList.pp_fault_id')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="code" :label="$t('alarmList.pp_native_code')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="real_code" :label="$t('alarmList.pp_real_code')" min-width="120" show-overflow-tooltip />
                  <el-table-column prop="code_name" :label="$t('alarmList.pp_fault_name')" min-width="120" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span>{{ row.code_name || '-' }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="100" show-overflow-tooltip />
        <el-table-column prop="alarm_type" :label="$t('alarmList.pp_alarm_type')" min-width="130" show-overflow-tooltip>
          <template #default="scope">
            {{ $t(alarmTypeMap[scope.row.alarm_type]) }}
          </template>
        </el-table-column>
        <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="140" show-overflow-tooltip>
          <template #default="scope">
            <span class="alarm-level-container" v-if="alarmLevelMap[scope.row.alarm_level]" :style="{ color: alarmLevelMap[scope.row.alarm_level].color, background: alarmLevelMap[scope.row.alarm_level].background }">
              {{ $t(alarmLevelMap[scope.row.alarm_level].name) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" min-width="140" show-overflow-tooltip>
          <template #default="scope">
            <div class="alarm-status-container" :style="{ background: alarmStatusMap[scope.row.state].background }">
              <ClearIcon v-if="scope.row.state == 1" />
              <CreateIcon v-else-if="scope.row.state == 2" />
              <UnknownIcon v-else />
              <span>{{ $t(alarmStatusMap[scope.row.state].name) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatTime(scope.row.create_ts) }}
          </template>
        </el-table-column>
        <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="180" show-overflow-tooltip v-if="searchForm.alarm_type != 3">
          <template #default="scope">
            {{ formatTime(scope.row.clear_ts) }}
          </template>
        </el-table-column>
        <el-table-column prop="device_name" :label="`${$t('alarmList.pp_device_name')}`" min-width="260" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-box flex_a_i-center">
              <div class="ellipse">
                {{ scope.row.device_name ? scope.row.device_name : $t('common.pp_unnamed_device') }}
              </div>
              <JumpIcon @click="handleJumpToSingleStation(scope.row)" class="cursor-pointer margin_l-8" style="min-width: 16px" v-if="project.project !== 'PSC4'" />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="device_id" :label="$t('alarmList.pp_device_id')" min-width="260">
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="battery_id" :label="$t('alarmList.pp_battery_id')" min-width="260" v-if="searchForm.alarm_type == 2">
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.battery_id" direction="rtl" />
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination">
        <Page :page="pages" @change="handlePageChange" />
      </div>
    </div>

    <BatchUploadDevice v-model:batchImportVisible="batchImportVisible" :deviceList="allDevices" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { page } from '~/constvars/page';
import { hasPermission } from '~/auth';
import { alarmLevelMap, alarmStatusMap } from '~/views/single-station/constant';
import { getDisabledDate, formatTime, clearJson, removeNullProp, getShortcuts } from '~/utils';
import { apiGetDevices } from '~/apis/home';
import { apiGetDeviceNameMap } from '~/apis/device-management';
import { apiGetAllAlarmList, apiGetPointList, apiDownloadAlarm } from '~/apis/alarm-list';
import { debounce } from 'lodash-es';
import AnalysisIcon from './icon/analysis-icon.vue';
import DownloadIcon from './icon/download-icon.vue';
import UploadIcon from '~/assets/svg/upload.vue';
import AlarmIcon from '~/assets/svg/alarm-view.vue';
import JumpIcon from '~/assets/svg/jump.vue';
import UpIcon from '~/views/service-list/icon/up-icon.vue';
import DownIcon from '~/views/service-list/icon/down-icon.vue';
import ClearIcon from '~/views/single-station/icon/alarm-clear.vue';
import CreateIcon from '~/views/single-station/icon/alarm-create.vue';
import UnknownIcon from '~/views/single-station/icon/alarm-unknown.vue';
import _ from 'lodash';

const { t } = useI18n();
const store = useStore();
const route = useRoute();
const router = useRouter();
const project = ref(computed(() => store.state.project));
const { locale } = useI18n({ useScope: 'global' });

const pages = ref(_.cloneDeep(page));
const loading = ref(false);
const remoteLoading = ref(false);
const remoteDeviceLoading = ref(false);
const showMore = ref(false);
const batchImportVisible = ref(false);
const datePicker = ref([new Date().getTime() - 3600000, new Date().getTime()] as any);
const deviceOptions = ref([] as any);
const allDevices = ref([] as any);
const list = ref([] as any);
const alarmTypeOptions = ref([
  {
    label: 'alarmList.pp_basic_alarm',
    value: 1,
  },
  {
    label: 'alarmList.pp_battery_alarm',
    value: 2,
  },
  {
    label: 'alarmList.pp_unknown_alarm',
    value: 3,
  },
]);
const alarmLevelOptions = ref([
  {
    label: 'alarmList.pp_first_level',
    value: 1,
  },
  {
    label: 'alarmList.pp_second_level',
    value: 2,
  },
  {
    label: 'alarmList.pp_third_level',
    value: 3,
  },
]);
const stateOptions = ref([
  {
    label: 'alarmList.pp_cleared',
    value: 1,
  },
  {
    label: 'alarmList.pp_alarming',
    value: 2,
  },
  {
    label: 'alarmList.pp_unknown',
    value: 3,
  },
]);
const pointOptions = ref([] as any);
const alarmTypeMap = ref({
  1: 'alarmList.pp_basic_alarm',
  2: 'alarmList.pp_battery_alarm',
  3: 'alarmList.pp_unknown_alarm',
} as any);
const form = ref({
  start_time: new Date().getTime() - 3600000,
  end_time: new Date().getTime(),
  alarm_type: '' as number | string,
  alarm_level: '' as number | string,
  state: '' as number | string,
  data_id: [] as any,
  device_id: [] as any,
  battery_id: '',
});
const searchForm = ref({
  start_time: new Date().getTime() - 3600000,
  end_time: new Date().getTime(),
  alarm_type: '' as number | string,
  alarm_level: '' as number | string,
  state: '' as number | string,
  data_id: [] as any,
  device_id: [] as any,
  battery_id: '',
});
const hasAnalysisPermission = computed(() => {
  if (project.value.project === 'PSC4') {
    return false;
  } else if (project.value.route === 'firefly1') {
    return hasPermission(`firefly1:alarm-list:alarm-analysis`);
  } else {
    return hasPermission(`powerSwap${project.value.version}:alarm-list:alarm-analysis`);
  }
});

const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return;
  searchDeviceList(query);
};
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true;
    const params = { project: project.value.project, name: val, device_ids: deviceIds, limit: 30 };
    const res = await apiGetDevices(params);
    remoteDeviceLoading.value = false;
    deviceOptions.value = res.data;
  }
}, 500);

/**
 * @description: 获取全量设备列表，用于批量导入设备时校验设备的有效性
 * @return {*}
 */
const getDeviceNameMap = async () => {
  const res = await apiGetDeviceNameMap(project.value.project);
  allDevices.value = res.data;
};

const handleJumpAnalysis = () => {
  router.push({
    path: `/${project.value.route}/alarm-list/alarm-analysis`,
  });
};

/**
 * @description: 确认批量导入设备
 * @param {*} list
 * @return {*}
 */
const handleConfirmBatchImportDevice = (list: any) => {
  if (!list) return;
  form.value.device_id = [...new Set([...form.value.device_id, ...list])];
  batchImportVisible.value = false;
  searchDeviceList('NIO', form.value.device_id.join(','));
};

/**
 * @description: 时间
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  form.value.start_time = new Date(value[0]).getTime();
  form.value.end_time = new Date(value[1]).getTime();
};

/**
 * @description: 清空电池ID
 * @return {*}
 */
const handleChangeAlarmType = () => {
  form.value.battery_id = '';
};

/**
 * @description: 远程搜索告警描述点
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchPointList(query);
};
const searchPointList = debounce(async (val: any) => {
  if (val && val.length > 0) {
    remoteLoading.value = true;
    const params = typeof val == 'string' ? { description: val } : { data_ids: val.join(',') };
    const res = await apiGetPointList(params, project.value.project);
    pointOptions.value = [];
    Object.keys(res.data).forEach((key: any) => {
      pointOptions.value.push({
        label: res.data[key],
        value: key,
      });
    });
    remoteLoading.value = false;
  }
}, 500);

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  pointOptions.value = [];
  clearJson(form.value);
  form.value.start_time = new Date().getTime() - 3600000;
  form.value.end_time = new Date().getTime();
  datePicker.value = [form.value.start_time, form.value.end_time];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  pages.value.size = 10;
  searchForm.value = { ...form.value };
  getList(true);
};

/**
 * @description: 跳转到具体设备
 * @param {*} row
 * @return {*}
 */
const handleJumpToSingleStation = (row: any) => {
  window.open(`/${project.value.route}/device-management/single-station/${row.device_id}?tab=alarm`);
};

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  if (loading.value) return;
  let formData = {} as any;
  formData = { ...searchForm.value };
  formData.device_id = formData.device_id.join(',');
  formData.data_id = formData.data_id.join(',');
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.descending = true;
  if (updateRoute) {
    router.push({
      path: `/${project.value.route}/alarm-list`,
      query: { ...removeNullProp(formData) },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetAllAlarmList(formData, project.value.project);
    list.value = res.data;
    pages.value.total = res.total;
  } catch (error) {}
  loading.value = false;
};

/**
 * @description: 下载告警数据
 * @return {*}
 */
const handleDownload = async () => {
  let formData = {} as any;
  formData = { ...searchForm.value };
  formData.device_id = formData.device_id.join(',');
  formData.data_id = formData.data_id.join(',');
  formData.descending = true;
  formData.download = true;
  removeNullProp(formData);
  await apiDownloadAlarm(formData, project.value.project);
};

watch(
  () => locale.value,
  (newValue, oldValue) => {
    getList(false);
    searchPointList(form.value.data_id);
  }
);

const initWeb = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query;
      if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
        form.value.start_time = Number(initParams.start_time);
        form.value.end_time = Number(initParams.end_time);
        datePicker.value[0] = form.value.start_time;
        datePicker.value[1] = form.value.end_time;
      }
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
      form.value.alarm_type = !!initParams.alarm_type ? Number(initParams.alarm_type) : '';
      form.value.alarm_level = !!initParams.alarm_level ? Number(initParams.alarm_level) : '';
      form.value.state = !!initParams.state ? Number(initParams.state) : '';
      form.value.device_id = !!initParams.device_id ? initParams.device_id.split(',') : [];
      form.value.data_id = !!initParams.data_id ? initParams.data_id.split(',') : [];
      form.value.battery_id = !!initParams.battery_id ? initParams.battery_id : '';
      searchForm.value = { ...form.value };
      if (form.value.data_id.length > 0) searchPointList(form.value.data_id);
      getList(false);
    } else if (newPath.split('/').length == 3 && newPath.split('/')[2] == oldPath.split('/')[2]) {
      handleReset();
    }
    getDeviceNameMap();
    searchDeviceList('NIO', route.query.device_id);
  },
  { immediate: true }
);

onBeforeUnmount(() => {
  initWeb();
});

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList(true);
};
</script>

<style lang="scss" scoped>
.alarm-list-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    background-color: #fff;
    :deep(.fold-container) {
      display: grid;
      grid-template-columns: 1fr 1fr 220px;
      gap: 16px 24px;
    }
    :deep(.unfold-container) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px 24px;
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-form-item__content {
          align-items: normal;
        }
        .el-select .el-select__tags .el-tag--info {
          color: #262626;
        }
      }
    }
    :deep(.operation-column) {
      .cell {
        display: flex;
        gap: 14px;
        div,
        a {
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    .alarm-level-container {
      display: inline-block;
      height: 26px;
      padding: 2px 8px;
      border-radius: 2px;
    }
    .alarm-status-container {
      height: 24px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
      padding: 2px 10px;
      margin-top: 2px;
      border-radius: 14px;
      font-size: 13px;
      line-height: 20px;
      color: #fff;
    }
  }
}
</style>
