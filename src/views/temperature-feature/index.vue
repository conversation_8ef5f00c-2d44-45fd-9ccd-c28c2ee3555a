<template>
  <content-view class="temperature-container" flex="true">
    <page-header
      title="充电桩枪温特征数据看板"
      firTitle="故障诊断"
      secTitle="充电桩枪温特征数据看板"
    ></page-header>
    <div class="main-container">
      <div class="select-container">
        <el-row>
          <el-col :span="8">
            <span class="span-title">Connector ID</span>
            <el-tooltip
              effect="light"
              :content="state.searchQuery.connector_id.join(',')"
              :disabled="tooltipDisabled"
              popper-class="atooltip"
            >
              <el-select
                v-model="state.searchQuery.connector_id"
                ref="selectRef"
                multiple
                clearable
                filterable
                collapse-tags
                size="default"
                placeholder="请选择枪号"
                @visible-change="changeConnectorId($event)"
                @change="bindChange"
                @clear="clearBrand"
              >
                <el-option
                  v-for="item in state.connectorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-tooltip>
          </el-col>
          <el-col :span="8">
            <span class="span-title">日期范围</span>
            <el-date-picker
              style="width: 260px"
              v-model="state.dateValue"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :clearable="false"
              @change="handleDateChange"
              :picker-options="state.expireTimeOption"
            >
            </el-date-picker>
          </el-col>
          <el-col :span="8">
            <span class="span-title">车品牌</span>
            <el-select
              v-model="state.searchQuery.vehicle_brand"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              filterable
              placeholder="ALL"
              @visible-change="changeBrand($event)"
              @clear="clearBrand"
            >
              <el-option
                v-for="item in state.vehicleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row class="health-row" v-if="state.hasHealthPermission">
          <el-col :span="24" style="display: flex; align-items: center">
            <span class="span-title health-title">枪线健康等级 : </span>
            <span class="span-title health-title" v-if="!state.loading">{{
              state.healthLevel
            }}</span>
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              v-if="!state.loading"
            >
              <div>
                <span style="color: #2c73ff">枪线健康等级图</span>
                <span
                  >根据所选枪号、时间段、车型等自动得出,level越高表征健康度越差</span
                >
              </div>
              <template #reference>
                <div class="suspend-tips">i</div>
              </template>
            </el-popover>
            <el-progress
              class="level1-progress"
              :text-inside="true"
              :stroke-width="14"
              :percentage="25"
              color="#00b42a"
              :format="(format as any)"
              v-if="state.healthLevel === 1 && !state.loading"
            ></el-progress>
            <el-progress
              class="level2-progress"
              :text-inside="true"
              :stroke-width="14"
              :percentage="50"
              color="#3491fa"
              :format="(format as any)"
              v-if="state.healthLevel === 2 && !state.loading"
            ></el-progress>
            <el-progress
              class="level3-progress"
              :text-inside="true"
              :stroke-width="14"
              :percentage="75"
              color="#f7ba1e"
              :format="(format as any)"
              v-if="state.healthLevel === 3 && !state.loading"
            ></el-progress>
            <el-progress
              class="level4-progress"
              :text-inside="true"
              :stroke-width="14"
              :percentage="100"
              color="#f53f3f"
              :format="(format as any)"
              v-if="state.healthLevel === 4 && !state.loading"
            ></el-progress>
          </el-col>
        </el-row>
      </div>
      <div class="linechart-card">
        <el-card
          class="box-card"
          style="position: relative"
          v-loading="state.loading"
          v-show="state.hasFirstPermission"
        >
          <div class="hover-tip1">
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              v-if="!state.firstEmpty && !state.loading"
            >
              <div>
                <span
                  >特征值K表征的是枪线的发热属性,K值越大代表发热越多,散热条件差。查看所选时间段内</span
                >
                <span style="color: #2c73ff">特征值K趋势图</span>
                <span
                  >,可以看到每日的平均K在level线的什么区间内,点击线上的点可得到该枪的特征平均值</span
                >
              </div>
              <template #reference>
                <div class="suspend-tips">i</div>
              </template>
            </el-popover>
          </div>
          <el-empty description="暂无数据" v-if="state.firstEmpty"></el-empty>
          <div
            id="firstChart"
            style="height: 300px"
            v-show="!state.firstEmpty"
          ></div>
        </el-card>
        <el-card
          class="box-card"
          style="position: relative"
          v-show="!state.hasFirstPermission"
        >
          <div class="empty-text">特征值K趋势图</div>
          <div class="hover-tip1">
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              v-if="!state.hasFirstPermission"
            >
              <div>
                <span
                  >特征值K表征的是枪线的发热属性,K值越大代表发热越多,散热条件差。查看所选时间段内</span
                >
                <span style="color: #2c73ff">特征值K趋势图</span>
                <span
                  >,可以看到每日的平均K在level线的什么区间内,点击线上的点可得到该枪的特征平均值</span
                >
              </div>
              <template #reference>
                <div class="suspend-tips">i</div>
              </template>
            </el-popover>
          </div>
          <el-empty
            description="暂无权限,请联系管理员"
            v-if="!state.hasFirstPermission"
          ></el-empty>
        </el-card>
        <el-card
          class="box-card"
          style="position: relative"
          v-loading="state.loading"
          v-show="state.hasSecondPermission"
        >
          <div class="hover-tip2">
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              v-if="!state.secondEmpty && !state.loading"
            >
              <div>
                <span style="color: #2c73ff">枪故障告警数趋势</span>
                <span>同时展示相应的告警/故障数以及每日的服务单数</span>
              </div>
              <template #reference>
                <div class="suspend-tips">i</div>
              </template>
            </el-popover>
          </div>
          <el-empty description="暂无数据" v-if="state.secondEmpty"></el-empty>
          <div
            id="secondChart"
            style="height: 300px"
            v-show="!state.secondEmpty"
          ></div>
        </el-card>
        <el-card
          class="box-card"
          style="position: relative"
          v-show="!state.hasSecondPermission"
        >
          <div class="empty-text">枪故障告警数趋势图</div>
          <div class="hover-tip2">
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              v-if="!state.hasSecondPermission"
            >
              <div>
                <span style="color: #2c73ff">枪故障告警数趋势</span>
                <span>同时展示相应的告警/故障数以及每日的服务单数</span>
              </div>
              <template #reference>
                <div class="suspend-tips">i</div>
              </template>
            </el-popover>
          </div>
          <el-empty
            description="暂无权限,请联系管理员"
            v-if="!state.hasSecondPermission"
          ></el-empty>
        </el-card>
      </div>
      <div class="linechart-card">
        <el-card
          class="box-card"
          style="position: relative"
          v-loading="state.loading"
          v-show="state.hasThirdPermission"
        >
          <div class="hover-tip3">
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              v-if="!state.thirdEmpty && !state.loading"
            >
              <div>
                <span>为了明确是车型原因还是枪线差的原因,需要查看</span>
                <span style="color: #2c73ff">不同车品牌特征值K</span>
                <span>以及</span>
                <span style="color: #2c73ff">不同车品牌服务单数</span>
                <span
                  >。若某品牌单量大且高K,而蔚来等高端品牌低K,则可以判定是车型主导</span
                >
              </div>
              <template #reference>
                <div class="suspend-tips">i</div>
              </template>
            </el-popover>
          </div>
          <el-empty description="暂无数据" v-if="state.thirdEmpty"></el-empty>
          <div
            id="thirdChart"
            style="height: 300px"
            v-show="!state.thirdEmpty"
          ></div>
        </el-card>
        <el-card
          class="box-card"
          style="position: relative"
          v-show="!state.hasThirdPermission"
        >
          <div class="empty-text">不同车品牌特征值K - Top15</div>
          <div class="hover-tip3">
            <el-popover
              placement="top-start"
              width="200"
              trigger="hover"
              v-if="!state.hasThirdPermission"
            >
              <div>
                <span>为了明确是车型原因还是枪线差的原因,需要查看</span>
                <span style="color: #2c73ff">不同车品牌特征值K</span>
                <span>以及</span>
                <span style="color: #2c73ff">不同车品牌服务单数</span>
                <span
                  >。若某品牌单量大且高K,而蔚来等高端品牌低K,则可以判定是车型主导</span
                >
              </div>
              <template #reference>
                <div class="suspend-tips">i</div>
              </template>
            </el-popover>
          </div>
          <el-empty
            description="暂无权限,请联系管理员"
            v-if="!state.hasThirdPermission"
          ></el-empty>
        </el-card>
        <el-card
          class="box-card"
          style="position: relative"
          v-loading="state.loading"
          v-show="state.hasFourthPermission"
        >
          <el-empty description="暂无数据" v-if="state.fourthEmpty"></el-empty>
          <div
            id="fourthChart"
            style="height: 300px"
            v-show="!state.fourthEmpty"
          ></div>
        </el-card>
        <el-card
          class="box-card"
          style="position: relative"
          v-show="!state.hasFourthPermission"
        >
          <div class="empty-text">不同车品牌服务单数 - Top15</div>
          <el-empty
            description="暂无权限,请联系管理员"
            v-if="!state.hasFourthPermission"
          ></el-empty>
        </el-card>
      </div>
      <div class="map-card">
        <el-card v-loading="state.loading" v-show="state.hasMapPermission">
          <div class="map-title" v-if="!state.loading">
            <div class="first-line">
              <span class="first-span">特征值K地区平均</span>
              <div class="hover-tips">
                <el-popover placement="top-start" width="200" trigger="hover">
                  <div>
                    <span>对于不同地域可以对比相应</span>
                    <span style="color: #2c73ff">特征值K地区平均</span>
                    <span>,目前不代表某区域优劣,仅供参考</span>
                  </div>
                  <template #reference>
                    <div class="suspend-tips">i</div>
                  </template>
                </el-popover>
              </div>
            </div>
            <div class="second-line">中国</div>
            <div class="third-line">
              <span class="third-span">动态指标</span>
              <el-select
                v-model="state.dynamicIndicators"
                size="small"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in state.dynamicOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="bottom-title" v-if="!state.loading">k_avg(10⁻³)</div>
          <div id="mapChart" style="height: 540px"></div>
        </el-card>
        <el-card style="position: relative" v-show="!state.hasMapPermission">
          <div class="first-line">
            <span class="first-span">特征值K地区平均</span>
            <div class="hover-tips">
              <el-popover placement="top-start" width="200" trigger="hover">
                <div>
                  <span>对于不同地域可以对比相应</span>
                  <span style="color: #2c73ff">特征值K地区平均</span>
                  <span>,目前不代表某区域优劣,仅供参考</span>
                </div>
                <template #reference>
                  <div class="suspend-tips">i</div>
                </template>
              </el-popover>
            </div>
          </div>
          <div class="empty-card">
            <el-empty
              description="暂无权限,请联系管理员"
              v-if="!state.hasMapPermission"
            ></el-empty>
          </div>
          <!-- <el-empty description="暂无权限,请联系管理员" v-if="!state.hasMapPermission"></el-empty> -->
        </el-card>
      </div>
    </div>
  </content-view>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount } from 'vue';
import { PageHeader } from '~/views/components';
import { ElMessage } from 'element-plus';
//全局组件
import ContentView from '~/components/owl/content-view';
import {
  apiGetGunInfo,
  apiGetTemperatureInfo,
  apiGetBrandInfo,
} from '~/apis/owl';
import { hasPermission } from '~/auth';
import * as echarts from 'echarts';
let mapJson:any = null;

const state = reactive({
  loading: true,
  healthLevel: '' as number | string,
  searchQuery: {
    connector_id: [
      'P0091202AA19130003WB',
      'P0091202AA19130004WB',
      'P0091202AA19130008WB',
    ],
    start_ts: new Date().getTime() - 90 * 24 * 3600 * 1000,
    end_ts: new Date().getTime(),
    vehicle_brand: [],
  } as any,
  expireTimeOption: {
    disabledDate(date: any) {
      return date.getTime() >= Date.now();
    },
  },
  kavgAverage: '' as number | string,
  seriesList: [] as any,
  seriesData: [] as any,
  firstXData: [] as any,
  secondXData: [] as any,
  thirdXData: [] as any,
  thirdYData: [] as any,
  fourthXData: [] as any,
  fourthYData: [] as any,
  service_order: [] as any,
  temperature_warn: [] as any,
  temperature_fault: [] as any,
  map_data: [] as any,
  kavgAverageArr: [] as any,
  minValue: '' as number | string,
  maxValue: '' as number | string,
  firstEmpty: false,
  secondEmpty: false,
  thirdEmpty: false,
  fourthEmpty: false,
  hasHealthPermission: false,
  hasFirstPermission: false,
  hasSecondPermission: false,
  hasThirdPermission: false,
  hasFourthPermission: false,
  hasMapPermission: false,
  dateValue: [
    new Date(new Date().getTime() - 90 * 24 * 3600 * 1000),
    new Date(),
  ] as any,
  dynamicIndicators: 'k_avg(10⁻³)',
  dynamicOptions: [
    {
      label: 'k_avg(10⁻³)',
      value: 'k_avg(10⁻³)',
    },
  ],
  connectorValue: [
    'P0091202AA19130003WB',
    'P0091202AA19130004WB',
    'P0091202AA19130008WB',
  ],
  connectorOptions: [] as any,
  vehicleOptions: [] as any,
});

const getJson = async () => {
  const url = new URL('/map.json', import.meta.url).href;
  const response = await fetch(url);
  const res = await response.json();
  mapJson = res
};

const tooltipDisabled = ref(false);
const selectRef = ref(null);

onBeforeMount(async () => {
  getJson()
  state.hasHealthPermission = hasPermission('temperature:health-level');
  state.hasFirstPermission = hasPermission('temperature:k-value');
  state.hasSecondPermission = hasPermission('temperature:warn');
  state.hasThirdPermission = hasPermission('temperature:brand-kvalue');
  state.hasFourthPermission = hasPermission('temperature:service-order');
  state.hasMapPermission = hasPermission('temperature:map');
  await getTemperatureInfo();
  await setFirstChart();
  await setSecondChart();
  await setThirdChart();
  await setFourthChart();
  await setMapChart();
  await getGunInfo();
  await getBrandInfo();
});

const getGunInfo = async () => {
  return apiGetGunInfo().then((res: any) => {
    res.data.map((item: any) => {
      state.connectorOptions.push({
        label: item,
        value: item,
      });
    });
  });
};

const getBrandInfo = async () => {
  return apiGetBrandInfo().then((res: any) => {
    res.data.map((item: any) => {
      state.vehicleOptions.push({
        label: item,
        value: item,
      });
    });
  });
};

const getTemperatureInfo = async () => {
  const tempQuery = JSON.parse(JSON.stringify(state.searchQuery));
  if (tempQuery.vehicle_brand.length === 0) {
    delete tempQuery.vehicle_brand;
  } else {
    tempQuery.vehicle_brand = tempQuery.vehicle_brand.join(',');
  }
  tempQuery.connector_id = tempQuery.connector_id.join(',');
  return apiGetTemperatureInfo(tempQuery).then((res: any) => {
    state.seriesList = [];
    state.seriesData = [];
    state.kavgAverageArr = [];
    state.minValue = res.minValue;
    state.maxValue = res.maxValue;
    state.healthLevel = res.health_level === 0 ? '无' : res.health_level;
    console.log(state.healthLevel);
    if (res.k_value === null) {
      state.firstEmpty = true;
    } else {
      state.firstEmpty = false;
      state.firstXData = [];
      for (let i = 0; i < res.k_value.length; i++) {
        state.seriesList.push([]);
        res.k_value[i].k_value_response.map((item: any) => {
          state.seriesList[i].push([
            item.date,
            Math.round(item.k_avg * 1000) / 1000,
          ]);
        });
        state.kavgAverageArr.push(res.k_value[i].avg);
        state.seriesData.push({
          data: state.seriesList[i],
          type: 'line',
          name: res.k_value[i].connector_id,
          smooth: true,
          // lineStyle: {
          //   color: '#2c73ff'
          // },
          markLine: {
            silent: true,
            symbol: ['circle', 'none'],
            label: {
              position: 'end',
              silent: true,
            },
            data: [
              {
                yAxis: 1.0,
                lineStyle: {
                  type: 'dashed',
                  color: '#3491fa',
                  width: 2,
                },
                label: {
                  formatter: 'Level 2',
                  color: '#3491fa',
                },
              },
              {
                yAxis: 1.5,
                lineStyle: {
                  type: 'dashed',
                  color: '#f7ba1e',
                  width: 2,
                },
                label: {
                  formatter: 'Level 3',
                  color: '#f7ba1e',
                },
              },
              {
                yAxis: 2.0,
                lineStyle: {
                  type: 'dashed',
                  color: '#f53f3f',
                  width: 2,
                },
                label: {
                  formatter: 'Level 4',
                  color: '#f53f3f',
                },
              },
              {
                type: 'average',
                lineStyle: {
                  width: 2,
                  opacity: 0,
                },
                label: {
                  // fontSize: 8,
                  color: '#2c73ff',
                  formatter: 'AVG {c}',
                },
              },
            ],
          },
        });
      }
      console.log(state.seriesData)
      res.k_value[0].k_value_response.map((item: any) => {
        state.firstXData.push(item.date);
      });
    }
    if (res.warning === null) {
      state.secondEmpty = true;
    } else {
      state.secondEmpty = false;
      state.secondXData = [];
      state.service_order = [];
      state.temperature_warn = [];
      state.temperature_fault = [];
      res.warning.map((item: any) => {
        state.secondXData.push(item.date);
        state.service_order.push(item.service_order);
        state.temperature_warn.push(item.temperature_warn);
        state.temperature_fault.push(item.temperature_fault);
      });
    }
    if (res.K_value_top15 === null) {
      state.thirdEmpty = true;
    } else {
      state.thirdEmpty = false;
      state.thirdXData = [];
      state.thirdYData = [];
      res.K_value_top15.map((item: any) => {
        state.thirdXData.push(item.vehicle_brand);
        state.thirdYData.push(Math.round(item.k_avg * 1000) / 1000);
      });
    }
    if (res.service_order_top15 === null) {
      state.fourthEmpty = true;
    } else {
      state.fourthEmpty = false;
      state.fourthXData = [];
      state.fourthYData = [];
      res.service_order_top15.map((item: any) => {
        state.fourthXData.push(item.vehicle_brand);
        state.fourthYData.push(item.service_order);
      });
    }
    if (state.searchQuery.connector_id.length === 0) {
      state.firstEmpty = true;
      state.secondEmpty = true;
      state.thirdEmpty = true;
      state.fourthEmpty = true;
    }
    res.K_value_province.map((item: any) => {
      item.value = Math.round(item.value * 1000) / 1000;
    });
    state.map_data = res.K_value_province;
    state.loading = false;
  });
};

const changeConnectorId = async (callback: any) => {
  if (!callback) {
    state.loading = true;
    await getTemperatureInfo();
    await setFirstChart();
    await setSecondChart();
    await setThirdChart();
    await setFourthChart();
    await setMapChart();
  }
};

const handleDateChange = async (value: any) => {
  state.searchQuery.start_ts = new Date(value[0]).getTime();
  state.searchQuery.end_ts = new Date(value[1]).getTime();
  state.loading = true;
  await getTemperatureInfo();
  await setFirstChart();
  await setSecondChart();
  await setThirdChart();
  await setFourthChart();
  await setMapChart();
};

const changeBrand = async (callback: any) => {
  if (!callback) {
    state.loading = true;
    await getTemperatureInfo();
    await setFirstChart();
    await setSecondChart();
    await setThirdChart();
    await setFourthChart();
    await setMapChart();
  }
};

const clearBrand = async () => {
  state.loading = true;
  await getTemperatureInfo();
  await setFirstChart();
  await setSecondChart();
  await setThirdChart();
  await setFourthChart();
  await setMapChart();
};

const format = (percentage: any) => {
  if (percentage === 25) {
    return 'Level 1';
  } else if (percentage === 50) {
    return 'Level 2';
  } else if (percentage === 75) {
    return 'Level 3';
  } else {
    return 'Level 4';
  }
};

const bindChange = (e: any) => {
  if (e.length > 8) {
    ElMessage.warning('最多只能选择8个Connector ID');
    state.searchQuery.connector_id.splice(-1);
  }
  if (e.length < 2) {
    tooltipDisabled.value = true;
  } else {
    tooltipDisabled.value = false;
  }
};

// 特征值K趋势图
const setFirstChart = () => {
  type EChartsOption = echarts.EChartsOption;
  var chartDom = document.getElementById('firstChart')!;
  var myChart = echarts.init(chartDom);
  var option: EChartsOption;
  const colors = [
    '#2c73ff',
    '#ff7d00',
    '#f7ba1e',
    '#9fdb1d',
    '#00b42a',
    '#14c9c9',
    '#f53f3f',
    '#722ed1',
  ];
  option = {
    color: colors,
    animation: false,
    title: {
      show: true,
      text: '特征值K趋势图',
      textStyle: {
        fontSize: 16,
        fontFamily: 'Noto Sans CJK SC',
        color: '#292c33',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      backgroundColor: 'rgba(255,255,255,0.8)',
      // formatter: function (params: any) {
      //   return '日期: ' + params.name + '<br>特征值K: ' + params.data
      // }
    },
    grid: {
      top: 50,
      right: '11%',
    },
    xAxis: {
      type: 'time',
      axisTick: { show: false },
      axisLabel: {
        formatter: '{yyyy}/{MM}/{dd}',
        hideOverlap: true,
      },
    },
    yAxis: {
      type: 'value',
      name: 'k_avg(10⁻³)',
      nameGap: 35,
      nameLocation: 'middle',
      nameTextStyle: {
        color: '#292C33',
        fontSize: 12,
      },
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        start: 0,
        end: 100,
      },
    ],
    series: state.seriesData,
  };
  myChart.clear();
  option && myChart.setOption(option);
  myChart.on('click', (params: any) => {
    const index = params.componentIndex;
    state.kavgAverage = Math.round(state.kavgAverageArr[index] * 1000) / 1000;
    for (let item of state.seriesData) {
      item.markLine.data[3].lineStyle.opacity = 0;
    }
    state.seriesData[index].markLine.data[3].lineStyle.opacity = 1;
    myChart.clear();
    option && myChart.setOption(option);
  });
};

// 枪故障告警数趋势
const setSecondChart = async () => {
  const orderArr = JSON.parse(JSON.stringify(state.service_order));
  const warnArr = JSON.parse(JSON.stringify(state.temperature_warn));
  const max1 = state.service_order.sort((a: any, b: any) => b - a)[0];
  const max2 = state.temperature_warn.sort((a: any, b: any) => b - a)[0];
  type EChartsOption = echarts.EChartsOption;
  var warnChartDom = document.getElementById('secondChart')!;
  var myWarnChart = echarts.init(warnChartDom);
  var warnOption: EChartsOption;
  const colors = ['#2c73ff', '#f53f3f', '#f7ba1e'];
  warnOption = {
    color: colors,
    animation: false,
    title: {
      show: true,
      text: '枪故障告警数趋势图',
      textStyle: {
        fontSize: 16,
        fontFamily: 'Noto Sans CJK SC',
        color: '#292c33',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      backgroundColor: 'rgba(255,255,255,0.8)',
    },
    grid: {
      top: 50,
    },
    legend: {
      data: ['服务单数', '过温故障数', '过温告警数'],
      right: '0',
    },
    xAxis: [
      {
        type: 'category',
        axisTick: {
          show: false,
        },
        data: state.secondXData,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '服务单数',
        position: 'left',
        nameGap: 35,
        nameLocation: 'middle',
        nameTextStyle: {
          color: '#292C33',
          fontSize: 12,
        },
        splitNumber: 5,
        interval: (max1 - 0) / 5,
        min: 0,
        max: max1,
      },
      {
        type: 'value',
      },
      {
        type: 'value',
        name: '故障报警数',
        position: 'right',
        nameGap: 35,
        nameLocation: 'middle',
        nameTextStyle: {
          color: '#292C33',
          fontSize: 12,
        },
        splitNumber: 5,
        interval: Math.floor((max2 - 0) / 5),
        min: 0,
        max: max2,
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        start: 0,
        end: 100,
      },
    ],
    series: [
      {
        name: '服务单数',
        type: 'bar',
        data: orderArr,
      },
      {
        name: '过温故障数',
        type: 'line',
        data: state.temperature_fault,
        smooth: true,
      },
      {
        name: '过温告警数',
        type: 'line',
        yAxisIndex: 2,
        data: warnArr,
        smooth: true,
      },
    ],
  };
  myWarnChart.clear();
  document
    .getElementById('secondChart')!
    .setAttribute('_echarts_instance_', '');
  warnOption && myWarnChart.setOption(warnOption, true);
};

// 不同车品牌特征值K - Top15
const setThirdChart = async () => {
  type EChartsOption = echarts.EChartsOption;
  let chartDom = document.getElementById('thirdChart')!;
  let myChart = echarts.init(chartDom);
  let option: EChartsOption;
  option = {
    animation: false,
    color: '#2C73FF',
    title: {
      show: true,
      text: '不同车品牌特征值K - Top15',
      textStyle: {
        fontSize: 16,
        fontFamily: 'Noto Sans CJK SC',
        color: '#292c33',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(255,255,255,0.8)',
    },
    grid: {
      top: 50,
      left: '25%',
      right: '6%',
      bottom: '15%',
    },
    xAxis: {
      type: 'value',
      name: 'k_avg(10⁻³)',
      nameGap: 30,
      nameLocation: 'middle',
      nameTextStyle: {
        color: '#292C33',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'category',
      // name: 'Vehicle Brands',
      // nameGap: 65,
      // nameLocation: 'middle',
      nameTextStyle: {
        color: '#292C33',
        fontSize: 12,
      },
      data: state.thirdXData.reverse(),
      axisLabel: {
        fontSize: 10,
      },
      axisTick: {
        show: false,
      },
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        yAxisIndex: 0,
        filterMode: 'filter',
        left: '10',
      },
    ],
    series: [
      {
        type: 'bar',
        data: state.thirdYData.reverse(),
        label: {
          show: true,
          position: 'right',
          color: '#91a2bc',
        },
        markLine: {
          silent: true,
          symbol: ['none', 'none'],
          label: {
            position: 'insideEndBottom',
            silent: true,
          },
          data: [
            {
              xAxis: 2.0,
              lineStyle: {
                type: 'dashed',
                color: '#f53f3f',
                width: 2,
              },
              label: {
                formatter: 'Level 4',
              },
            },
          ],
        },
      },
    ],
  };
  option && myChart.setOption(option);
};

// 不同车品牌服务单数 - Top15
const setFourthChart = async () => {
  type EChartsOption = echarts.EChartsOption;
  let chartDom = document.getElementById('fourthChart')!;
  let myChart = echarts.init(chartDom);
  let option: EChartsOption;
  option = {
    animation: false,
    color: '#2C73FF',
    title: {
      show: true,
      text: '不同车品牌服务单数 - Top15',
      textStyle: {
        fontSize: 16,
        fontFamily: 'Noto Sans CJK SC',
        color: '#292c33',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(255,255,255,0.8)',
    },
    grid: {
      top: 50,
      left: '25%',
      right: '5%',
      bottom: '15%',
    },
    xAxis: {
      type: 'value',
      name: '服务单数',
      nameGap: 30,
      nameLocation: 'middle',
      nameTextStyle: {
        color: '#292C33',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'category',
      // name: 'Vehicle Brands',
      // nameGap: 65,
      // nameLocation: 'middle',
      nameTextStyle: {
        color: '#292C33',
        fontSize: 12,
      },
      data: state.fourthXData.reverse(),
      axisLabel: {
        fontSize: 10,
      },
      axisTick: {
        show: false,
      },
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        yAxisIndex: 0,
        filterMode: 'filter',
        left: '10',
      },
    ],
    series: [
      {
        type: 'bar',
        data: state.fourthYData.reverse(),
        label: {
          show: true,
          position: 'right',
          color: '#91a2bc',
        },
      },
    ],
  };
  option && myChart.setOption(option);
};

// 地图
const setMapChart = () => {
  type EChartsOption = echarts.EChartsOption;
  let chartDom = document.getElementById('mapChart')!;
  let myChart = echarts.init(chartDom);
  echarts.registerMap('china', mapJson);
  const option = {
    // 数据映射
    visualMap: {
      type: 'continuous', //连续标签
      min: state.minValue, //最小值
      max: state.maxValue, //最大值，不设置为无限大
      orient: 'horizontal', //组件水平放置
      align: 'left',
      bottom: '10%',
      realtime: false,
      itemHeight: 200,
      itemWidth: 12,
      color: ['#0E37A6', '#2C73FF', '#77AEFF', '#9DC7FF', '#E8F4FF'],
      precision: 3,
      calculable: true,
      seriesIndex: 0,
      textStyle: {
        color: '#292C33',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>{c}',
    },
    toolbox: {
      show: true,
      feature: {
        restore: {},
      },
    },
    geo: {
      zoom: 1.6, //缩放比例
      top: '30%',
      map: 'china',
      show: true,
      roam: true,
      itemStyle: {
        borderColor: '#99CBF9', //区域描边颜色
        borderWidth: 0.6, //区域描边线宽
      },
      emphasis: {
        label: {
          color: '#292C33',
        },
        itemStyle: {
          areaColor: '#FADC19',
        },
      },
      select: {
        label: {
          color: '#292C33',
        },
        itemStyle: {
          areaColor: '#FADC19',
        },
      },
    },
    series: [
      {
        type: 'map',
        geoIndex: 0,
        data: state.map_data,
      },
    ],
  };
  myChart.setOption(option);
};
</script>
<style lang="scss">
.temperature-container {
  font-family: 'Blue Sky Standard';
  .main-container {
    border-color: #e8eef2;
    background-color: #fff;
    width: auto;
    // margin: 120px 20px 50px 20px;
    margin: 20px;
    // overflow-y: scroll;
    // height: 100%;
    padding: 20px;
    .select-container {
      margin-top: 20px;
      .span-title {
        margin-right: 10px;
        font-size: 14px;
        color: #292c33;
      }
      .health-title {
        font-size: 16px;
        margin-right: 12px;
      }
      .health-row {
        margin-top: 20px;
      }
    }
    .linechart-card {
      display: flex;
      margin-top: 20px;
      .el-card {
        width: 580px;
        height: 340px;
        margin-right: 20px;
        .empty-text {
          font-size: 16px;
          font-family: 'Noto Sans CJK SC';
          color: #292c33;
        }
      }
      .hover-tip1 {
        position: absolute;
        left: 145px;
        top: 26px;
        z-index: 2;
      }
      .hover-tip2 {
        position: absolute;
        left: 180px;
        top: 26px;
        z-index: 2;
      }
      .hover-tip3 {
        position: absolute;
        left: 240px;
        top: 26px;
        z-index: 2;
      }
    }
    .map-card {
      width: calc(100% - 20px);
      height: 560px;
      margin-top: 20px;
      position: relative;
      .el-card {
        width: 100%;
        height: 100%;
        .el-card__body {
          height: 100%;
        }
        .first-line {
          display: flex;
        }
        .first-span {
          color: #292c33;
          font-size: 16px;
          font-weight: 500;
          font-family: 'Noto Sans CJK SC';
        }
        .hover-tips {
          margin-left: 16px;
          margin-top: 6px;
        }
        .empty-card {
          height: 100%;
          .el-empty {
            height: 100%;
          }
        }
      }
      .map-title {
        position: absolute;
        z-index: 2;
        .first-line {
          display: flex;
          color: #292c33;
          font-weight: 500;
          font-family: 'Noto Sans CJK SC';
          .first-span {
            font-size: 16px;
          }
          .hover-tips {
            margin-left: 16px;
            margin-top: 6px;
          }
        }
        .second-line {
          font-size: 14px;
          color: #2c73ff;
          margin-top: 14px;
        }
        .third-line {
          font-size: 14px;
          color: #292c33;
          margin-top: 14px;
          .third-span {
            margin-right: 14px;
          }
        }
      }
      .bottom-title {
        position: absolute;
        bottom: 90px;
        font-size: 10px;
        z-index: 2;
      }
    }
    .suspend-tips {
      border: 2px solid #91a2bc;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 12px;
      cursor: pointer;
      color: #91a2bc;
      font-weight: 700;
    }
  }
  .el-select {
    width: 260px;
    .el-select__tags {
      max-width: 240px;
      height: 32px;
    }
    .el-input {
      width: 260px;
    }
  }
  .el-input {
    width: 260px;
  }
  .el-progress {
    width: 320px;
    display: flex;
    align-items: center;
    .el-progress-bar__innerText {
      display: flex;
      justify-content: center;
    }
  }
  .level1-progress {
    .el-progress-bar__outer {
      background-color: #e8ffea;
    }
  }
  .level2-progress {
    .el-progress-bar__outer {
      background-color: #e8f7ff;
    }
  }
  .level3-progress {
    .el-progress-bar__outer {
      background-color: #fffce8;
    }
  }
  .level4-progress {
    .el-progress-bar__outer {
      background-color: #e8ffea;
    }
  }
}
.atooltip {
  border-color: #e9e9eb !important;
  background-color: #f4f4f5;
  color: #909399;
}
</style>
