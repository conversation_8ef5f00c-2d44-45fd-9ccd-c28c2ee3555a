<template>
  <div class="alarm-analysis-container">
    <div class="header-container">
      <div class="flex-box flex_a_i-center font-size-20 line-height-28">
        <BackIcon @click="changeToPre" class="cursor-pointer" />
        <span style="color: #8c8c8c; font-weight: 480; margin-left: 4px">{{ $t('menu.pp_alarm_list') }} /</span>
        <span style="color: #1f1f1f; font-weight: 420">&nbsp;{{ $t('menu.pp_alarm_analysis') }}</span>
      </div>
      <el-button v-if="!isFirstEnter" class="welkin-primary-button" @click="createDialogVisible = true">{{ $t('alarmList.pp_condition_selection') }}</el-button>
    </div>

    <div class="content-container">
      <div v-if="isFirstEnter" class="first-enter-container">
        <EmptyIcon />
        <div class="empty-description">{{ $t('alarmList.pp_empty_text') }}</div>
        <el-button class="welkin-primary-button" @click="createDialogVisible = true">{{ $t('alarmList.pp_condition_selection') }}</el-button>
      </div>

      <div v-else class="non-first-enter-container">
        <div class="params-container">
          <span class="font-size-14 font-weight-bold line-height-22" style="color: #595959">{{ $t('alarmList.pp_device_selected') }}</span>
          <div>
            <span class="device-tag margin_b-16">{{ cloneCreateForm.device_ids[0] + ' ' + getDeviceName(cloneCreateForm.device_ids[0]) }}</span>
            <div class="flex-box gap_8" v-if="cloneCreateForm.device_ids.length > 1">
              <span class="device-tag">{{ cloneCreateForm.device_ids[1] + ' ' + getDeviceName(cloneCreateForm.device_ids[1]) }}</span>
              <span class="device-tag" v-if="cloneCreateForm.device_ids.length > 2">+{{ cloneCreateForm.device_ids.length - 2 }}</span>
            </div>
          </div>
          <div>
            <div class="flex-box flex_a_i-center">
              <span class="margin_r-4 font-size-14 font-weight-bold line-height-22" style="color: #595959">{{ $t('alarmList.pp_time_frame') }}</span>
              <el-tooltip :content="$t('alarmList.pp_time_tip')" effect="light" placement="top">
                <HelpIcon />
              </el-tooltip>
            </div>
          </div>
          <div class="flex-box flex_d-column gap_16">
            <div class="font-size-14 line-height-22">
              <span class="margin_r-24" style="color: #595959">{{ $t('alarmList.pp_interval_one') }}</span>
              <span style="color: #262626">{{ formatTimeToDay(cloneCreateForm.section_1_date[0]) }}</span>
              <span class="margin-n-16" style="color: #595959">{{ $t('common.pp_to') }}</span>
              <span style="color: #262626">{{ formatTimeToDay(cloneCreateForm.section_1_date[1]) }}</span>
            </div>
            <div class="font-size-14 line-height-22">
              <span class="margin_r-24" style="color: #595959">{{ $t('alarmList.pp_interval_two') }}</span>
              <span style="color: #262626">{{ formatTimeToDay(cloneCreateForm.section_2_date[0]) }}</span>
              <span class="margin-n-16" style="color: #595959">{{ $t('common.pp_to') }}</span>
              <span style="color: #262626">{{ formatTimeToDay(cloneCreateForm.section_2_date[1]) }}</span>
            </div>
          </div>
        </div>

        <div class="list-container">
          <el-form :model="form">
            <el-form-item :label="$t('alarmList.pp_alarm_id_or_name')">
              <el-select v-model="form.data_ids" multiple collapse-tags collapse-tags-tooltip clearable filterable remote :placeholder="$t('common.pp_please_input')" :remote-method="remoteMethod" :loading="remoteLoading" class="width-full">
                <el-option v-for="item in pointOptions" :key="item.value" :value="item.value" :label="item.label + '-' + item.value">
                  <span style="float: left">{{ item.label }}</span>
                  <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.value }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <template #label>
                <div class="flex-box flex_a_i-center gap_4">
                  <span>{{ $t('alarmList.pp_threshold') }}</span>
                  <el-tooltip placement="top" effect="light">
                    <template #content>
                      <div class="flex-box flex_a_i-center gap_4">
                        <InfoIcon />
                        <span>{{ $t('alarmList.pp_threshold_tip') }}</span>
                      </div>
                    </template>
                    <HelpIcon />
                  </el-tooltip>
                </div>
              </template>
              <el-input v-model="form.happen_times" oninput="value = value.replace(/[^0-9]/g,'')" :placeholder="$t('common.pp_please_input')" class="width-full height-32" clearable />
            </el-form-item>
            <el-form-item :label="$t('alarmList.pp_alarm_level')">
              <el-select v-model="form.alarm_levels" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
                <el-option v-for="item in alarmLevelOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('alarmList.pp_in_service')">
              <el-select v-model="form.in_service" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
                <el-option :value="true" :label="$t('common.pp_yes')" />
                <el-option :value="false" :label="$t('common.pp_no')" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button class="welkin-primary-button" @click="handleSearch">{{ $t('common.pp_filter') }}</el-button>
              <el-button class="welkin-secondary-button" @click="handleReset" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
            </el-form-item>
          </el-form>

          <el-table :data="list" ref="tableRef" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626', cursor: 'auto' }" :cell-style="{ color: '#262626' }">
            <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="240" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="flex-box flex_a_i-center width-full">
                  <span class="ellipse">{{ row.data_id_description ? row.data_id_description : '-' }}</span>
                  <el-popover :width="520" placement="top" v-if="row.servo_fault_list">
                    <template #reference>
                      <AlarmIcon class="margin_l-6" />
                    </template>
                    <el-table :data="row.servo_fault_list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }">
                      <el-table-column prop="id" :label="$t('alarmList.pp_fault_id')" min-width="120" show-overflow-tooltip />
                      <el-table-column prop="code" :label="$t('alarmList.pp_native_code')" min-width="120" show-overflow-tooltip />
                      <el-table-column prop="real_code" :label="$t('alarmList.pp_real_code')" min-width="120" show-overflow-tooltip />
                      <el-table-column prop="code_name" :label="$t('alarmList.pp_fault_name')" min-width="120" show-overflow-tooltip>
                        <template #default="{ row }">
                          <span>{{ row.code_name || '-' }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-popover>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="100" show-overflow-tooltip />
            <el-table-column prop="alarm_type" :label="$t('alarmList.pp_alarm_type')" min-width="120" show-overflow-tooltip>
              <template #default="{ row }">
                {{ $t(alarmTypeMap[row.alarm_type]) }}
              </template>
            </el-table-column>
            <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="130" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="alarm-level-container" v-if="alarmLevelMap[row.alarm_level]" :style="{ color: alarmLevelMap[row.alarm_level].color, background: alarmLevelMap[row.alarm_level].background }">
                  {{ $t(alarmLevelMap[row.alarm_level].name) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="section_1_count" :label="$t('alarmList.pp_interval_1_count')" sortable="custom" min-width="140" show-overflow-tooltip>
              <template #header="{ column }">
                <div class="flex-box flex_a_i-center gap_8" @click.stop>
                  <span>{{ column.label }}</span>
                  <div class="flex-box flex_d-column">
                    <SortUp @click.stop="handleSort('ascending', column)" :color="column.order === 'ascending' ? '#00bebe' : '#262626'" class="cursor-pointer" />
                    <SortDown @click.stop="handleSort('descending', column)" :color="column.order === 'descending' ? '#00bebe' : '#262626'" class="cursor-pointer" />
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span class="light-column" @click="handleClickCount('count1', row)">{{ row.section_1_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="section_2_count" :label="$t('alarmList.pp_interval_2_count')" sortable="custom" min-width="140" show-overflow-tooltip>
              <template #header="{ column }">
                <div class="flex-box flex_a_i-center gap_8" @click.stop>
                  <span>{{ column.label }}</span>
                  <div class="flex-box flex_d-column">
                    <SortUp @click.stop="handleSort('ascending', column)" :color="column.order === 'ascending' ? '#00bebe' : '#262626'" class="cursor-pointer" />
                    <SortDown @click.stop="handleSort('descending', column)" :color="column.order === 'descending' ? '#00bebe' : '#262626'" class="cursor-pointer" />
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span class="light-column" @click="handleClickCount('count2', row)">{{ row.section_2_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="section_2_dive_section_1" :label="$t('alarmList.pp_interval_divide')" sortable="custom" min-width="140" show-overflow-tooltip>
              <template #header="{ column }">
                <div class="flex-box flex_a_i-center gap_8" @click.stop>
                  <span>{{ column.label }}</span>
                  <div class="flex-box flex_d-column">
                    <SortUp @click.stop="handleSort('ascending', column)" :color="column.order === 'ascending' ? '#00bebe' : '#262626'" class="cursor-pointer" />
                    <SortDown @click.stop="handleSort('descending', column)" :color="column.order === 'descending' ? '#00bebe' : '#262626'" class="cursor-pointer" />
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ Number.isInteger(row.section_2_dive_section_1) ? row.section_2_dive_section_1 : row.section_2_dive_section_1.toFixed(3) }}</span>
              </template>
            </el-table-column>
          </el-table>

          <div class="common-pagination">
            <Page :page="pages" @change="handlePageChange" />
          </div>
        </div>
      </div>
    </div>

    <CreateDialog v-model:createDialogVisible="createDialogVisible" :createLoading="createLoading" :createForm="createForm" :deviceOptions="deviceOptions" @handleConfirmCreate="handleConfirmCreate" @handleCloseDialog="handleCloseDialog" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, watch } from 'vue';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import { page } from '~/constvars/page';
import { alarmTypeMap, alarmLevelOptions } from './components/constant';
import { alarmLevelMap } from '~/views/single-station/constant';
import { formatTimeToDay, removeNullKeys, getFinalEndTime } from '~/utils';
import { apiGetAnalysis } from '~/apis/alarm-list';
import { apiGetPointList } from '~/apis/alarm-list';
import { useRoute, useRouter } from 'vue-router';
import { apiGetDevices } from '~/apis/home';
import { ElMessage } from 'element-plus';
import CreateDialog from './components/create-dialog.vue';
import BackIcon from '~/assets/svg/page-back.vue';
import EmptyIcon from '~/assets/svg/empty.vue';
import HelpIcon from '~/assets/svg/help.vue';
import AlarmIcon from '~/assets/svg/alarm-view.vue';
import SortUp from '~/assets/svg/sort-up.vue';
import SortDown from '~/assets/svg/sort-down.vue';
import InfoIcon from './components/icon/info-icon.vue';
import { debounce } from 'lodash-es';
import _ from 'lodash';

const route = useRoute();
const router = useRouter();
const store = useStore();
const tableRef = ref();
const isFirstEnter = ref(true);
const loading = ref(false);
const remoteLoading = ref(false);
const createLoading = ref(false);
const createDialogVisible = ref(false);
const pointOptions = ref([] as any);
const deviceOptions = ref([] as any);
const list = ref([] as any);
const createForm = ref({
  device_method: 1,
  device_ids: [],
  section_type: 1,
  section_1_date: [],
  section_2_date: [],
});
const form = ref({
  data_ids: [],
  happen_times: '' as number | string,
  alarm_levels: '' as number | string,
  in_service: '' as boolean | string,
});
const sortForm = ref({
  sort: '',
  descending: true,
});
const initCreateForm = _.cloneDeep(createForm.value);
const cloneCreateForm = ref({} as any);
const pages = ref(_.cloneDeep(page));
const project = ref(computed(() => store.state.project));
const { locale } = useI18n({ useScope: 'global' });

/**
 * @description: 手动排序
 * @param {*} order
 * @param {*} column
 * @return {*}
 */
const handleSort = (order: any, column: any) => {
  if (column.order === order) {
    tableRef.value.clearSort();
    sortForm.value.sort = '';
  } else {
    tableRef.value.sort(column.property, order);
    sortForm.value.sort = column.property;
    sortForm.value.descending = order == 'descending';
  }
  getList(false);
};

/**
 * @description: 点击区间发生次数
 * @param {*} type
 * @param {*} row
 * @return {*}
 */
const handleClickCount = (type: string, row: any) => {
  const path = `/${project.value.route}/alarm-list`;
  const startTime = type == 'count1' ? cloneCreateForm.value.section_1_date[0] : cloneCreateForm.value.section_2_date[0];
  const endTime = type == 'count1' ? getFinalEndTime(cloneCreateForm.value.section_1_date[1]) : getFinalEndTime(cloneCreateForm.value.section_2_date[1]);
  const dataId = row.data_id;
  const deviceIds = cloneCreateForm.value.device_ids.join(',');
  window.open(`${path}?start_time=${startTime}&end_time=${endTime}&data_id=${dataId}&device_id=${deviceIds}`);
};

/**
 * @description: 远程搜索告警描述点
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchPointList(query);
};
const searchPointList = debounce(async (val: any) => {
  if (val && val.length > 0) {
    remoteLoading.value = true;
    const params = typeof val == 'string' ? { description: val } : { data_ids: val.join(',') };
    const res = await apiGetPointList(params, project.value.project);
    pointOptions.value = [];
    Object.keys(res.data).forEach((key: any) => {
      pointOptions.value.push({
        label: res.data[key],
        value: key,
      });
    });
    remoteLoading.value = false;
  }
}, 500);

watch(
  () => locale.value,
  (newValue, oldValue) => {
    getList(false);
    searchPointList(form.value.data_ids);
  }
);

const getList = async (updateRoute = true) => {
  loading.value = true;
  const params = {
    page: pages.value.current,
    size: pages.value.size,
    descending: sortForm.value.sort ? sortForm.value.descending : '',
    sort: sortForm.value.sort,
    device_ids: cloneCreateForm.value.device_ids.join(','),
    section_1_start_timestamp: cloneCreateForm.value.section_1_date[0],
    section_1_end_timestamp: getFinalEndTime(cloneCreateForm.value.section_1_date[1]),
    section_2_start_timestamp: cloneCreateForm.value.section_2_date[0],
    section_2_end_timestamp: getFinalEndTime(cloneCreateForm.value.section_2_date[1]),
    data_ids: form.value.data_ids.join(','),
    happen_times: form.value.happen_times !== '' ? Number(form.value.happen_times) : '',
    alarm_levels: form.value.alarm_levels,
    in_service: form.value.in_service,
  };
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys({ ...params, descending: '', sort: '' }) },
    });
  }
  try {
    const res = await apiGetAnalysis(removeNullKeys(params), project.value.project);
    loading.value = false;
    createLoading.value = false;
    if (res.err_code) {
      ElMessage.error(res.message);
    } else {
      list.value = res.data;
      pages.value.total = res.total;
      isFirstEnter.value = false;
      createDialogVisible.value = false;
    }
  } catch (error) {
    loading.value = false;
    createLoading.value = false;
  }
};

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  getList();
};

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  form.value.data_ids = [];
  form.value.happen_times = '';
  form.value.alarm_levels = '';
  form.value.in_service = '';
  pages.value.current = 1;
  getList();
};

/**
 * @description: 确认创建分析
 * @param {*} list
 * @return {*}
 */
const handleConfirmCreate = (list: any) => {
  cloneCreateForm.value = _.cloneDeep(createForm.value);
  if (createForm.value.device_method == 2) cloneCreateForm.value.device_ids = list;
  createLoading.value = true;
  pages.value.current = 1;
  getList();
};

/**
 * @description: 关闭弹窗
 * @param {*} formRef
 * @return {*}
 */
const handleCloseDialog = (formRef: any) => {
  createForm.value = _.cloneDeep(initCreateForm);
  formRef.resetFields();
  createDialogVisible.value = false;
};

/**
 * @description: 回到告警管理
 * @return {*}
 */
const changeToPre = () => {
  router.push({
    path: `/${project.value.route}/alarm-list`,
  });
};

const getDeviceName = (deviceId: string) => {
  const filterObj = deviceOptions.value.find((item: any) => item.device_id == deviceId);
  if (filterObj) return filterObj.description;
};

/**
 * @description: 获取设备列表
 * @return {*}
 */
const getDeviceOptions = async () => {
  const params = { project: project.value.project };
  const res = await apiGetDevices(params);
  deviceOptions.value = res.data;
};

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query;
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
  form.value.data_ids = !!initParams.data_ids ? initParams.data_ids.split(',') : [];
  form.value.happen_times = !!initParams.happen_times ? Number(initParams.happen_times) : '';
  form.value.alarm_levels = !!initParams.alarm_levels ? Number(initParams.alarm_levels) : '';
  form.value.in_service = !!initParams.in_service ? JSON.parse(initParams.in_service) : '';
  cloneCreateForm.value = {
    device_ids: !!initParams.device_ids ? initParams.device_ids.split(',') : [],
    section_1_date: [Number(initParams.section_1_start_timestamp), Number(initParams.section_1_end_timestamp)],
    section_2_date: [Number(initParams.section_2_start_timestamp), Number(initParams.section_2_end_timestamp)],
  };
  if (form.value.data_ids.length > 0) searchPointList(form.value.data_ids);
  getList(false);
};

onBeforeMount(() => {
  getDeviceOptions();
  if (route.query.device_ids) {
    isFirstEnter.value = false;
    initWeb();
  }
});
</script>

<style lang="scss" scoped>
.alarm-analysis-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
  }
  .content-container {
    flex: 1;
    padding: 0 24px 24px;
    background: #f8f8f8;
    .first-enter-container {
      width: 100%;
      height: 60%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #fff;
      border: 1px solid #dcf2f3;
      border-radius: 4px;
      .empty-description {
        font-size: 14px;
        line-height: 22px;
        color: #8c8c8c;
        margin-bottom: 16px;
      }
    }
    .non-first-enter-container {
      .params-container {
        padding: 24px;
        display: grid;
        grid-template-columns: 56px 3fr 100px 2fr;
        column-gap: 24px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #dcf2f3;
        margin-bottom: 16px;
        .device-tag {
          display: inline-flex;
          align-items: center;
          height: 32px;
          font-size: 14px;
          line-height: 22px;
          color: #262626;
          background: #e5f9f9;
          padding: 3px 8px;
          border-radius: 4px;
        }
      }
      .list-container {
        padding: 24px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #dcf2f3;
        :deep(.el-form) {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          column-gap: 24px;
          .el-form-item__label {
            font-size: 14px;
            color: #595959;
            padding-right: 8px;
          }
          .el-form-item__content {
            flex-wrap: nowrap;
            align-items: normal;
          }
        }
        :deep(.caret-wrapper) {
          display: none;
        }
        .alarm-level-container {
          display: inline-block;
          height: 26px;
          padding: 2px 8px;
          border-radius: 2px;
        }
      }
    }
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  :deep(.el-select .el-select__tags .el-tag--info) {
    color: #262626;
  }
}
</style>
