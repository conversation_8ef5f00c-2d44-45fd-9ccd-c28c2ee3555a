<template>
  <div class="create-dialog-container">
    <el-dialog v-model="createDialogVisible" :title="$t('alarmList.pp_dialog_title')" width="592px" @close="handleCloseDialog" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="createForm" ref="createFormRef" label-position="top" :rules="rules" require-asterisk-position="right">
        <el-form-item :label="$t('alarmList.pp_device_method')" prop="device_method" style="margin-bottom: 8px">
          <el-radio-group v-model="createForm.device_method">
            <el-radio :label="1">{{ $t('alarmList.pp_direct_selection') }}</el-radio>
            <el-radio :label="2">{{ $t('alarmList.pp_csv_upload') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_device_select')" prop="device_ids" v-if="createForm.device_method == 1" :rules="[{ required: true, message: $t('configList.pp_select_device'), trigger: 'change' }]">
          <el-select v-model="createForm.device_ids" multiple collapse-tags collapse-tags-tooltip clearable filterable :placeholder="$t('common.pp_enter')" class="width-full">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="device_ids" v-else style="margin-bottom: 6px">
          <div class="width-full flex-box flex_j_c-space-between flex_a_i-center margin_b-8">
            <span class="form-label">{{ $t('stuckAnalysis.pp_import_file') }}</span>
            <el-button class="welkin-secondary-button" @click="handleDownloadTemplate" style="height: 24px">{{ $t('stuckAnalysis.pp_download_template') }}</el-button>
          </div>
          <el-upload v-model:file-list="file" :on-change="handleChangeFile" drag accept=".csv" ref="uploadRef" :limit="1" :on-exceed="handleExceed" class="width-full" action="#" :auto-upload="false">
            <div class="el-upload__text">
              <UploadIcon />
              <span class="margin_l-6"
                >{{ $t('stuckAnalysis.pp_please_upload') }} <em>{{ $t('stuckAnalysis.pp_csv') }}</em></span
              >
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('alarmList.pp_analysis_method')" prop="section_type" style="margin-bottom: 8px">
          <el-radio-group v-model="createForm.section_type">
            <el-radio :label="1">{{ $t('alarmList.pp_based_on_time') }}</el-radio>
            <el-radio :label="2" disabled>{{ $t('alarmList.pp_based_on_version') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="time-form-item">
          <el-form-item :label="$t('alarmList.pp_interval_one')" prop="section_1_date">
            <el-date-picker v-model="createForm.section_1_date" value-format="x" :prefix-icon="createForm.section_1_date && createForm.section_1_date.length > 0 ? customPrefix : emptyPrefix" :clearable="false" type="daterange" :start-placeholder="$t('common.pp_start_date')" :end-placeholder="$t('common.pp_end_date')" :disabledDate="disabledDateFun">
              <template #range-separator>
                <TimeRangeIcon />
              </template>
            </el-date-picker>
          </el-form-item>
          <el-form-item :label="$t('alarmList.pp_interval_two')" prop="section_2_date">
            <el-date-picker v-model="createForm.section_2_date" value-format="x" :prefix-icon="createForm.section_2_date && createForm.section_2_date.length > 0 ? customPrefix : emptyPrefix" :clearable="false" type="daterange" :start-placeholder="$t('common.pp_start_date')" :end-placeholder="$t('common.pp_end_date')" :disabledDate="disabledDateFun">
              <template #range-separator>
                <TimeRangeIcon />
              </template>
            </el-date-picker>
          </el-form-item>
        </div>
        <el-form-item>
          <div class="flex-box flex_a_i-center flex_w-wrap font-size-14 line-height-22">
            <HelpIcon />
            <span class="margin_l-8 white-space-nowrap" style="color: #595959">{{ $t('alarmList.pp_time') }}</span>
            <span style="color: #8c8c8c">{{ $t('alarmList.pp_time_tip') }}</span>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-box flex_j_c-flex-end">
        <el-button @click="handleCancel" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button @click="handleConfirm" class="welkin-primary-button" style="margin-left: 4px" :loading="createLoading">{{ $t('alarmList.pp_create_analysis') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, PropType, shallowRef, h } from 'vue'
import { useI18n } from 'vue-i18n'
import { confirmBatchUploadDevice } from '~/utils'
import { genFileId, ElMessage } from 'element-plus'
import HelpIcon from '~/assets/svg/help.vue'
import UploadIcon from '~/views/run-list/component/icon/upload-icon.vue'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'

interface DeviceObject {
  device_id: string
  description: string
}

const props = defineProps({
  createDialogVisible: Boolean,
  createLoading: Boolean,
  deviceOptions: {
    type: Array as PropType<DeviceObject[]>,
    required: true
  },
  createForm: {
    type: Object,
    default: {}
  }
})

const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const emptyPrefix = shallowRef({
  render() {
    return h(DatePrefix, { color: '#BFBFBF' })
  }
})
const disabledDateFun = (time: any) => {
  return time.getTime() > Date.now()
}

const { t } = useI18n()
const rules = ref({
  section_1_date: [{ required: true, message: t('common.pp_select_time'), trigger: 'change' }],
  section_2_date: [{ required: true, message: t('common.pp_select_time'), trigger: 'change' }]
})

const emits = defineEmits(['update:createDialogVisible', 'handleConfirmCreate', 'handleCloseDialog'])

const createFormRef = ref()
const uploadRef = ref()
const file = ref([] as any)
const csvList = ref([] as any)

/**
 * @description: 上传文件
 * @param {*} uploadFile
 * @return {*}
 */
const handleChangeFile = async (uploadFile: any) => {
  console.log(uploadFile)
  const params = { file: uploadFile.raw }
  csvList.value = await confirmBatchUploadDevice(params, props.deviceOptions)
}

/**
 * @description: 覆盖上一个文件
 * @param {*} files
 * @return {*}
 */
const handleExceed = (files: any) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
}

/**
 * @description: 下载模板
 * @return {*}
 */
const handleDownloadTemplate = () => {
  window.open('https://cdn-welkin-public.nio.com/welkin/2024/07/30/template_device_id_csv.csv')
}

/**
 * @description: 关闭
 * @return {*}
 */
const handleCloseDialog = () => {
  if (!createFormRef.value) return
  file.value = []
  emits('handleCloseDialog', createFormRef.value)
}

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:createDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirm = async () => {
  if (!createFormRef.value) return
  await createFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      if (props.createForm.device_method == 2 && file.value.length == 0) {
        ElMessage.warning(t('stuckAnalysis.pp_please_upload') + ' ' + t('stuckAnalysis.pp_csv'))
      } else {
        emits('handleConfirmCreate', csvList.value)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style lang="scss" scoped>
.create-dialog-container {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
      .el-dialog__title {
        font-size: 18px;
        font-weight: 420;
        line-height: 26px;
        color: #1f1f1f;
      }
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          color: #595959;
          margin-bottom: 4px;
        }
        .el-radio {
          margin-right: 40px;
          .el-radio__label {
            color: #262626;
            font-weight: normal;
          }
          .el-radio__input.is-disabled + span.el-radio__label {
            color: #bfbfbf;
          }
        }
        .form-label {
          color: #595959;
          font-size: 14px;
          line-height: 22px;
        }
        .el-upload-dragger {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px dashed #d9d9d9;
          height: 96px;
          border-radius: 4px;
          .el-upload__text {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #262626;
          }
          .el-upload__text em {
            color: #01a0ac;
          }
        }
        .el-upload-list__item-file-name {
          color: #595959;
          font-weight: normal;
        }
      }
      .time-form-item {
        display: grid;
        grid-template-columns: 1fr 1fr;
        column-gap: 24px;
        .el-range-editor.el-input__wrapper {
          width: 260px;
        }
      }
    }
  }
}
</style>
