<template>
  <div class="battery-trace-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_info_trace') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_battery_swap') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_battery_swap') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" inline style="width: 100%">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('station.pp_battery_id')" class="upper-form-item width-full">
                <el-input v-model="form.battery_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item :label="$t('deviceManagement.pp_device')" class="upper-form-item width-full">
                <RemoteDeviceSelect v-model="form.device_id" :initDevice="route.query.device_id" project="PowerSwap2,PUS3" class="width-full" />
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item :label="$t('common.pp_time')" class="upper-form-item width-full">
                <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('batterySwap.pp_slot')" class="upper-form-item width-full">
                <el-select v-model="form.slot_id" clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in slotOptions" :key="item.value" :value="item.value" :label="item.value == 'A12' ? $t(item.label) + ' ' + $t('batterySwap.pp_firefighting_slot') : $t(item.label) + ' ' + item.value">
                    <span style="float: left">{{ item.value == 'A12' ? $t('batterySwap.pp_firefighting_slot') : item.value }}</span>
                    <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ $t(item.label) }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item :label="$t('batterySwap.pp_reason')" class="upper-form-item width-full">
                <el-select v-model="form.event_id" multiple collapse-tags collapse-tags-tooltip clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in eventOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item class="upper-form-item width-full">
                <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
                <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="swap-table-container">
        <div class="flex-box flex_j_c-space-between flex_a_i-center padding-10 font-weight-bold">
          <span class="tip-text">{{ $t('batterySwap.pp_tip') }}</span>
          <div class="flex-box flex_a_i-center">
            <span class="font-size-14 margin_r-10">{{ $t('batterySwap.pp_visualization') }}：</span>
            <el-switch v-model="switchVal" />
          </div>
        </div>
        <el-table :data="list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }" v-loading="loading" v-if="list && list.length > 0 && !switchVal">
          <el-table-column prop="timestamp" :label="$t('batterySwap.pp_swap_time')" min-width="170" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.timestamp">{{ formatTime(scope.row.timestamp) }}</span>
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="battery_id" :label="$t('batterySwap.pp_battery_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.battery_id" v-if="scope.row.battery_id" />
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="description" :label="$t('alarmList.pp_device')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.description ? scope.row.description : $t('common.pp_unnamed_device') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="device_id" :label="$t('batterySwap.pp_device_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" v-if="scope.row.device_id" />
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="type" :label="$t('batterySwap.pp_swap_method')" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <span :class="scope.row.type == 1 ? 'battery-in' : 'battery-out'" v-if="scope.row.type && typeMap.hasOwnProperty(scope.row.type)">{{ $t(typeMap[scope.row.type]) }}</span>
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="slot_id" :label="$t('batterySwap.pp_slot')" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.slot_id">{{ scope.row.slot_id == 'A12' ? $t('batterySwap.pp_firefighting_slot') : scope.row.slot_id }}</span>
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="event_id" :label="$t('batterySwap.pp_reason')" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.event_id && eventMap.hasOwnProperty(scope.row.event_id)">{{ $t(eventMap[scope.row.event_id]) }}</span>
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="related" :label="$t('batterySwap.pp_related_id')" min-width="320">
            <template #default="scope">
              <span v-if="[1, 6, 7].includes(scope.row.event_id)">-</span>
              <div class="flex-box" v-else-if="[2, 3].includes(scope.row.event_id)">
                <span>{{ $t('batterySwap.pp_service_id') }}：</span>
                <WelkinCopyBoard :text="scope.row.related.service_id" v-if="scope.row.related.service_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
              <div class="flex-box" v-else-if="[4, 5].includes(scope.row.event_id)">
                <span>{{ $t('batterySwap.pp_turnover_id') }}：</span>
                <WelkinCopyBoard :text="scope.row.related.trans_id" v-if="scope.row.related.trans_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
              <div class="flex-box" v-if="scope.row.event_id == 2">
                <span>{{ $t('batterySwap.pp_car_id') }}：</span>
                <WelkinCopyBoard :text="maskString(scope.row.related.ev_id)" v-if="scope.row.related.ev_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div v-else-if="list && list.length > 0 && switchVal && !loading" class="svg-container">
          <div v-for="(item, index) in list" class="svg-item relative">
            <div class="svg-title">
              <span class="margin_r-10">{{ $t(eventMap[item.event_id]) }}</span>
              <span :class="item.type == 1 ? 'battery-in' : 'battery-out'">{{ $t(typeMap[item.type]) }}</span>
            </div>

            <div class="flex-box flex_a_i-center relative">
              <Edit v-if="item.event_id == 1" class="svg-icon" />
              <Station v-if="[2, 3].includes(item.event_id) && item.type == 1" class="svg-icon" />
              <Car v-if="[2, 3].includes(item.event_id) && item.type == 2" class="svg-icon" />
              <SlotIn v-if="[4, 5].includes(item.event_id) && item.type == 1" class="svg-icon" />
              <SlotOut v-if="[4, 5].includes(item.event_id) && item.type == 2" class="svg-icon" />
              <Transfer v-if="item.event_id == 6" class="svg-icon" />
              <Water v-if="item.event_id == 7" class="svg-icon" />
              <Arrow style="position: absolute; left: 160px; top: 40px; transform: rotate(180deg)" />
            </div>

            <div class="detail-text margin_b-10">{{ formatTime(item.timestamp) }}</div>
            <div class="detail-text margin_b-10">
              <span class="white-space-nowrap">{{ $t('batterySwap.pp_slot') }}：</span>
              <span v-if="item.slot_id">{{ item.slot_id == 'A12' ? $t('batterySwap.pp_firefighting_slot') : item.slot_id }}</span>
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </div>
            <div class="flex-box flex_d-column">
              <div class="detail-text margin_b-8">
                <span class="white-space-nowrap">{{ $t('batterySwap.pp_battery_id') }}：</span>
                <WelkinCopyBoard :text="item.battery_id" v-if="item.battery_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
              <div class="detail-text margin_b-8">
                <span class="white-space-nowrap">{{ $t('batterySwap.pp_device_id') }}：</span>
                <WelkinCopyBoard :text="item.device_id" :width="'100%'" v-if="item.device_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
              <div class="detail-text margin_b-8">
                <span class="white-space-nowrap">{{ $t('alarmList.pp_device') }}：</span>
                <WelkinCopyBoard :text="item.description" v-if="item.description" class="width-240" />
                <span v-else>{{ $t('common.pp_unnamed_device') }}</span>
              </div>
              <div class="detail-text margin_b-8" v-if="[2, 3].includes(item.event_id)">
                <span class="white-space-nowrap">{{ $t('batterySwap.pp_service_id') }}：</span>
                <WelkinCopyBoard :text="item.related.service_id" v-if="item.related.service_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
              <div class="detail-text margin_b-8" v-if="[4, 5].includes(item.event_id)">
                <span class="white-space-nowrap">{{ $t('batterySwap.pp_turnover_id') }}：</span>
                <WelkinCopyBoard :text="item.related.trans_id" v-if="item.related.trans_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
              <div class="detail-text margin_b-10" v-if="item.event_id == 2">
                <span class="white-space-nowrap">{{ $t('batterySwap.pp_car_id') }}：</span>
                <WelkinCopyBoard :text="maskString(item.related.ev_id)" v-if="item.related.ev_id" class="width-240" />
                <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
              </div>
            </div>
          </div>
        </div>

        <el-skeleton :rows="10" animated v-else-if="loading && switchVal" />

        <el-empty :description="$t('common.pp_empty')" v-else></el-empty>
        <div class="pagination-container" v-if="list && list.length > 0">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { useRoute, useRouter } from 'vue-router'
import { getDisabledDate, getShortcuts, formatTime, clearJson, removeNullKeys, maskString } from '~/utils'
import { eventOptions, slotOptions, eventMap, typeMap } from './constant'
import { apiGetSwapList } from '~/apis/battery-swap'
import { ElMessage } from 'element-plus'
import Water from './svg/water.vue'
import Edit from './svg/edit.vue'
import SlotIn from './svg/slot-in.vue'
import SlotOut from './svg/slot-out.vue'
import Car from './svg/car.vue'
import Station from './svg/station.vue'
import Arrow from './svg/arrow.vue'
import Transfer from './svg/transfer.vue'
import _ from 'lodash'

const { t } = useI18n()

const ruleFormRef = ref()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const switchVal = ref(true)
const datePicker = ref([new Date().getTime() - 3600 * 1000 * 24 * 7, new Date().getTime()] as any)
const list = ref([] as any)
const pages = ref(_.cloneDeep(page))
const form = ref({
  start_time: new Date().getTime() - 3600 * 1000 * 24 * 7,
  end_time: new Date().getTime(),
  event_id: [] as any,
  battery_id: '',
  device_id: '',
  slot_id: ''
})
const searchForm = ref(_.cloneDeep(form.value))

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList(true, 2)
}

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @param {*} status // 是否实时带筛选项
 * @return {*}
 */
const getList = async (updateRoute = true, status: number) => {
  if (loading.value) return
  let formData = {} as any
  if (status === 2) {
    formData = _.cloneDeep(searchForm.value)
  } else {
    formData = _.cloneDeep(form.value)
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.descending = true
  removeNullKeys(formData)
  if (formData.event_id) formData.event_id = formData.event_id.join(',')
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: formData
    })
  }
  loading.value = true
  try {
    const res = await apiGetSwapList(formData)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
  } catch (error) {}
  loading.value = false
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  datePicker.value = [new Date().getTime() - 3600 * 1000 * 24 * 7, new Date().getTime()]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = { ...form.value }
  getList(true, 2)
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value[0] = form.value.start_time
    datePicker.value[1] = form.value.end_time
  }
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.event_id = !!initParams.event_id ? initParams.event_id.split(',').map((item: any) => Number(item)) : []
  form.value.battery_id = !!initParams.battery_id ? initParams.battery_id : ''
  form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
  form.value.slot_id = !!initParams.slot_id ? initParams.slot_id : ''
  searchForm.value = { ...form.value }
  getList(false, 2)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.battery-trace-container {
  font-family: 'Blue Sky Standard';
  .header-title {
    font-weight: bold !important;
  }
  .search-form-container {
    padding-bottom: 5px;
    :deep(.upper-form-item) {
      margin-bottom: 15px;
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-select .el-select__tags .el-tag--info) {
      color: #262626;
    }
  }
  .swap-table-container {
    .unknown-text {
      color: red;
      font-weight: bold;
    }
    .battery-in {
      color: green;
      font-weight: bold;
    }
    .battery-out {
      color: var(--el-color-primary);
      font-weight: bold;
    }
    .tip-text {
      color: var(--el-color-primary);
      font-size: 14px;
    }
    .svg-container {
      width: 100%;
      display: flex;
      overflow-x: auto;
      font-family: 'Noto Sans';
      .svg-item {
        margin: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #292c33;
        .svg-title {
          display: flex;
          justify-content: center;
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          position: absolute;
          top: 30px;
          right: -60px;
        }
        .svg-icon {
          margin-top: 20px;
          margin-bottom: 20px;
        }
        .detail-text {
          display: flex;
          font-size: 14px;
        }
      }
    }
    :deep(.pagination-container .el-pagination) {
      margin-bottom: 20px;
    }
  }
}
</style>
