export const eventOptions = [
  {
    label: 'batterySwap.pp_manual',
    value: 1
  },
  {
    label: 'batterySwap.pp_authentication_swap',
    value: 2
  },
  {
    label: 'batterySwap.pp_non_authenticated',
    value: 3
  },
  {
    label: 'batterySwap.pp_slot_turnover',
    value: 4
  },
  {
    label: 'batterySwap.pp_slot_outside',
    value: 5
  },
  {
    label: 'batterySwap.pp_transfer',
    value: 6
  },
  {
    label: 'batterySwap.pp_fall_water',
    value: 7
  }
]

export const eventMap = {
  1: 'batterySwap.pp_manual',
  2: 'batterySwap.pp_authentication_swap',
  3: 'batterySwap.pp_non_authenticated',
  4: 'batterySwap.pp_slot_turnover',
  5: 'batterySwap.pp_slot_outside',
  6: 'batterySwap.pp_transfer',
  7: 'batterySwap.pp_fall_water',
} as any

export const typeMap = {
  1: 'batterySwap.pp_enter_slot',
  2: 'batterySwap.pp_exit_slot'
} as any

export const slotOptions = [
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A1'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A2'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A3'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A4'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A5'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A6'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A7'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A8'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A9'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A10'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A11'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'A12'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C1'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C2'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C3'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C4'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C5'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C6'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C7'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C8'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C9'
  },
  {
    label: 'batterySwap.pp_swap_station3',
    value: 'C10'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '1'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '2'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '3'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '4'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '5'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '6'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '7'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '8'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '9'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '10'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '11'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '12'
  },
  {
    label: 'batterySwap.pp_swap_station2',
    value: '13'
  }
]
