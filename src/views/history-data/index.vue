<template>
  <div class="history-data-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <WelkinStationBreadcrumb :version="project.version" />

          <el-breadcrumb-item>
            {{ $t('historyData.pp_history_data') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_history_data') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <el-form :model="searchForm" :rules="rules" ref="ruleFormRef" label-width="100px" style="width: 100%">
        <div class="collapse-search-container padding_r-30">
          <el-form-item :label="`${$t('common.pp_time')}`" class="required-form-item">
            <el-date-picker style="width: 300px" v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
          </el-form-item>
          <el-form-item :label="`${$t('deviceManagement.pp_device')}`" prop="device_id" ref="deviceRef">
            <CommonDeviceSelect v-model="searchForm.device_id" />
          </el-form-item>
          <el-form-item :label="`${$t('historyData.pp_parameters')}`" prop="data_id" class="required-form-item" ref="dataIdRef">
            <el-cascader v-model="searchForm.data_id" :options="parameterOptions" :props="cascaderProps" separator="——" collapse-tags clearable @change="handleParameterChange" class="history-carousel" :placeholder="`${t('historyData.pp_parameters_select')}`" />
          </el-form-item>
          <el-form-item>
            <div class="width-full flex-box flex_j_c-space-between">
              <div>
                <el-button class="welkin-primary-button" :loading="searchLoading" @click="filterEvent(ruleFormRef)">{{ $t('common.pp_search') }}</el-button>
                <el-button @click="resetSelect" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
              </div>
              <el-button v-if="tableData.length > 0 || echartsData.length > 0" class="welkin-secondary-button" :loading="downLoading" @click="downloadEvent">{{ $t('common.pp_download') }}</el-button>
            </div>
          </el-form-item>
        </div>
      </el-form>

      <!-- 已选参数 -->
      <el-row :class="oddParameterArr.length > 5 ? 'parameter-oversize-container' : 'parameter-normal-container'" v-if="searchForm.data_id.length > 0">
        <el-col :span="2"
          ><span class="parameter-title">{{ $t('historyData.pp_selected_parameters') }}</span></el-col
        >
        <el-col :span="10" class="flex-box flex_d-column">
          <span v-for="item in oddParameterArr" class="parameter-info margin_l-9 margin_b-10">{{ item }}</span>
        </el-col>
        <el-col :span="12" class="flex-box flex_d-column">
          <span v-for="item in evenParameterArr" class="parameter-info margin_b-10">{{ item }}</span>
        </el-col>
      </el-row>

      <!-- 图形 -->
      <span class="tip-text" v-if="project.project == 'PowerSwap2' && !isTable && !isEmpty && isSearch">{{ $t('historyData.pp_tip_text') }}</span>
      <el-row class="data-container width-full flex-item_f-1" v-if="!isTable && !isEmpty && isSearch">
        <div id="lineChart" class="width-full"></div>
      </el-row>

      <!-- 表格 -->
      <el-row class="swap-table-container width-full height-440" v-if="isTable && !isEmpty && isSearch">
        <el-auto-resizer>
          <template #default="{height, width}">
            <el-table-v2 :columns="columns" :data="tableData" :width="width" :height="height" :header-height="40" :row-height="40" fixed @scroll="handleEndReached" />
          </template>
        </el-auto-resizer>
      </el-row>

      <!-- 暂无数据 -->
      <el-row class="data-container width-full flex-item_f-1" v-if="isEmpty && isSearch">
        <el-empty :description="`${$t('common.pp_empty')}`" class="width-full" />
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, computed, onBeforeUnmount, nextTick, h, watch} from 'vue'
import {useI18n} from 'vue-i18n'
import {useStore} from 'vuex'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, FormInstance, ElAutoResizer} from 'element-plus'
import {getDisabledDate, formatHistoryData, formatTime} from '../../utils'
import {apiGetDataIdList, apiGetDataList, apiDownloadData} from '../../apis/history-data'
import * as echarts from 'echarts'
import _ from 'lodash'

const {t} = useI18n()
const $store = useStore()
const route = useRoute()
const router = useRouter()
const {locale} = useI18n({useScope: 'global'})
const project = ref(computed(() => $store.state.project))
const searchLoading = ref(false)
const downLoading = ref(false)
const isTable = ref(false)
const isEmpty = ref(false)
const isSearch = ref(false)
const ruleFormRef = ref<FormInstance>()
const deviceRef = ref()
const dataIdRef = ref()
const datePicker = ref([new Date().getTime() - 3600000, new Date().getTime()] as any)
const oddParameterArr = ref([] as any)
const evenParameterArr = ref([] as any)
const tableData = ref([] as any)
const columns = ref([] as any)
const echartsData = ref([] as any)
const parameterMap = ref([] as any)
let parameterOptions = ref([] as any)
const validateDataId = (rule: any, value: any, callback: any) => {
  if (value.length === 0) {
    callback(new Error(t('historyData.pp_parameters_select')))
  } else {
    callback()
  }
}
const cascaderProps = reactive({multiple: true})
const rules = computed(() => {
  return {
    device_id: [
      {
        message: t('deviceManagement.pp_select_device_id'),
        required: true,
        trigger: 'change'
      }
    ],
    data_id: [
      {
        validator: validateDataId,
        trigger: 'change'
      }
    ]
  }
})
const searchForm = reactive({
  device_id: '',
  start_time: new Date().getTime() - 3600000,
  end_time: new Date().getTime(),
  data_id: [] as any,
  page: 1,
  size: 2000,
  descending: false,
  download: false
})

let queryForm = reactive({} as any)

/**
 * @description: 参数点数据整合
 * @param {*} numArr
 * @return {*}
 */
const numArrToObjArr = (numArr: any) => {
  let objArr = [] as any
  numArr.map((item: any) => {
    objArr.push({
      value: item,
      label: parameterMap.value[Number(item)]
    })
  })
  return objArr
}
const formatParameterData = (obj: any) => {
  let options = [] as any
  for (let key in obj) {
    options.push({
      value: key,
      label: key,
      children: Array.isArray(obj[key]) ? numArrToObjArr(obj[key]) : formatParameterData(obj[key])
    })
  }
  options.forEach((item: any) => {
    if (item.value === '充放电系统' || item.value == 'Charging & Discharging System') {
      item.children.sort((a: any, b: any) => a.value.localeCompare(b.value, 'zh-CN', {numeric: true}))
    }
  })
  return options
}

/**
 * @description: 不多于5个参数点或时间小于24小时画图
 * @return {*}
 */
const setLineEcharts = () => {
  const lineChartDom = document.getElementById('lineChart')!
  const lineEchart = echarts.init(lineChartDom)
  let lineOption = {
    animation: false,
    color: ['#00BEBE', '#00B42A', '#9FDB1D', '#FADC19', '#F7BA1E', '#FF7D00'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255,255,255,0.5)'
    },
    legend: {
      icon: 'circle',
      top: '5%',
      left: '3%'
    },
    xAxis: {
      data: echartsData.value[0].xAxis,
      boundaryGap: false,
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value'
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomOnMouseWheel: false
      },
      {
        height: 20,
        bottom: '5%',
        start: 0,
        end: 100,
        zoomOnMouseWheel: false,
        fillerColor: 'rgba(0, 190, 190, 0.1)',
        borderColor: '#DCE2E5',
        backgroundColor: '#fff',
        selectedDataBackground: {
          areaStyle: {
            color: 'rgba(0, 190, 190, 0.4)'
          },
          lineStyle: {
            color: 'rgba(0, 190, 190, 0.4)'
          }
        }
      }
    ],
    grid: {
      top: '15%',
      left: '3%',
      right: '3%',
      bottom: '12%',
      containLabel: true
    },
    series: echartsData.value[0].series
  }
  lineEchart.clear()
  lineChartDom.setAttribute('_echarts_instance_', '')
  lineOption && lineEchart.setOption(lineOption, true)
  window.onresize = function () {
    lineEchart.resize()
  }
}

/**
 * @description: 时间配置
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  searchForm.start_time = new Date(value[0]).getTime()
  searchForm.end_time = new Date(value[1]).getTime()
}

/**
 * @description: 下方参数点展示
 * @return {*}
 */
const handleParameterChange = () => {
  const dataId = _.cloneDeep(searchForm.data_id)
  const stringArr = dataId.map((item: any) => {
    item[2] = parameterMap.value[item[2]]
    return item.join('——')
  })
  oddParameterArr.value = stringArr.filter((item: any, index: number) => index % 2 === 0)
  evenParameterArr.value = stringArr.filter((item: any, index: number) => index % 2 !== 0)
}

/**
 * @description: 重置
 * @return {*}
 */
const resetSelect = () => {
  datePicker.value = [new Date().getTime() - 3600000, new Date().getTime()]
  searchForm.data_id = []
  searchForm.device_id = ''
  const timeOut = setTimeout(() => {
    dataIdRef.value.clearValidate()
    deviceRef.value.clearValidate()
    clearTimeout(timeOut)
  }, 30)
}

/**
 * @description: 处理表格的列数据
 * @param {*} data
 * @return {*}
 */
const formatTableData = (data: any) => {
  columns.value = []
  for (let key in data) {
    if (key === 'timestamp') {
      columns.value.unshift({
        key: 'timestamp',
        dataKey: 'timestamp',
        title: t('common.pp_time'),
        width: 200,
        fixed: true,
        style: {fontFamily: 'Noto Sans', color: '#292c33', fontWeight: 'normal'},
        cellRenderer: ({cellData}: any) => h('span', {type: 'success'}, {default: () => formatTime(cellData)})
      })
    } else {
      columns.value.push({
        key: key,
        dataKey: key,
        title: parameterMap.value[key],
        minWidth: 210,
        width: 280,
        style: {fontFamily: 'Noto Sans', color: '#292c33', fontWeight: 'normal'}
      })
    }
  }
}

/**
 * @description: 搜索
 * @param {*} formEl
 * @return {*}
 */
const filterEvent = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid, fields) => {
    if (valid && searchForm.data_id.length <= 100) {
      searchLoading.value = true
      isSearch.value = true
      queryForm = _.cloneDeep(searchForm)
      queryForm.data_id = queryForm.data_id.map((item: any) => Number(item[2])).join(',')
      queryForm.size = queryForm.data_id.split(',').length > 5 || queryForm.end_time - queryForm.start_time > 86400000 ? 2000 : 999999999
      router.push({
        path: location.pathname,
        query: queryForm
      })
      try {
        const res = await apiGetDataList(queryForm, project.value.project, queryForm.device_id)
        if (!res.data || res.data.length === 0) {
          isEmpty.value = true
        } else {
          isEmpty.value = false
          if (queryForm.data_id.split(',').length > 5) {
            ElMessage.warning(t('historyData.pp_over_parameters'))
            isTable.value = true
            tableData.value = res.data
            formatTableData(tableData.value[0])
          } else if (queryForm.end_time - queryForm.start_time > 86400000) {
            ElMessage.warning(t('historyData.pp_over_time'))
            isTable.value = true
            tableData.value = res.data
            formatTableData(tableData.value[0])
          } else {
            isTable.value = false
            echartsData.value = formatHistoryData(res.data, parameterMap.value)
            nextTick(() => {
              setLineEcharts()
            })
          }
        }
      } catch (error) {}
      searchLoading.value = false
    } else if (valid && searchForm.data_id.length > 100) {
      ElMessage.warning(t('historyData.pp_over_hundred_parameters'))
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 下载
 * @return {*}
 */
const downloadEvent = async () => {
  downLoading.value = true
  const downloadQuery = _.cloneDeep(queryForm)
  downloadQuery.download = true
  downloadQuery.page = 1
  downloadQuery.size = 999999999
  try {
    await apiDownloadData(downloadQuery, project.value.project, downloadQuery.device_id)
    downLoading.value = false
  } catch (e: any) {
    downLoading.value = false
  }
}

/**
 * @description: 拉取参数点
 * @return {*}
 */
const getDataIdList = async (flag: number) => {
  try {
    const {data, data_id_description} = await apiGetDataIdList(project.value.project)
    parameterMap.value = data_id_description
    parameterOptions.value = formatParameterData(data)
    if (!flag && searchForm.data_id.length > 0) {
      searchForm.data_id = searchForm.data_id.map((id: string) => {
        return getParentsById(parameterOptions.value, id)
      })
      handleParameterChange()
    } else if(flag) {
      searchForm.data_id = searchForm.data_id.map((item: any) => {
        return getParentsById(parameterOptions.value, item[2])
      })
      handleParameterChange()
    }
  } catch (error: any) {
    parameterOptions.value = []
    ElMessage.error(error)
  }
}

// 回显树形结构的父节点
const getParentsById = (list: any, id: string) => {
  for (let i in list) {
    if (list[i].value == id) {
      return [list[i].value]
    }
    if (list[i].children) {
      let node = getParentsById(list[i].children, id) as any
      if (node !== undefined) {
        node.unshift(list[i].value)
        return node
      }
    }
  }
}

/**
 * @description: 滚动分页
 * @param {*} params
 * @return {*}
 */
const handleEndReached = async (params: any) => {
  const isRepeatScroll = tableData.value.length < params.scrollTop / 40 + 2010
  if ((params.scrollTop / 40 - 1990) % 2000 === 0 && params.yAxisScrollDir === 'forward' && isRepeatScroll) {
    queryForm.page = queryForm.page + 1
    const res = await apiGetDataList(queryForm, project.value.project, queryForm.device_id)
    tableData.value.push(...res.data)
  }
}

/**
 * @description: 切换语言
 * @return {*}
 */
watch(
  () => locale.value,
  (newValue, oldValue) => {
    getDataIdList(1)
    filterEvent(ruleFormRef.value)
  }
)

const stopWatch = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
        searchForm.start_time = Number(initParams.start_time)
        searchForm.end_time = Number(initParams.end_time)
        datePicker.value[0] = searchForm.start_time
        datePicker.value[1] = searchForm.end_time
      }
      searchForm.data_id = !!initParams.data_id ? initParams.data_id.split(',') : []
      searchForm.device_id = !!initParams.device_id ? initParams.device_id : ''
      getDataIdList(0)
    } else if (newPath.split('/').length == 3 && newPath.split('/')[2] == oldPath.split('/')[2]) {
      resetSelect()
      isSearch.value = false
      tableData.value = []
      echartsData.value = []
      getDataIdList(0)
    }
  },
  {immediate: true}
)

/**
 * @description: 组件销毁时取消监听路由
 * @return {*}
 */
onBeforeUnmount(() => {
  stopWatch()
})
</script>

<style lang="scss">
.history-data-container {
  .history-carousel {
    width: 100%;
    height: 32px;
    .el-input {
      height: 32px;
    }
  }
  .parameter-oversize-container,
  .parameter-normal-container {
    background: #fff;
    width: 100%;
    padding: 20px;
    padding-bottom: 10px;
    margin-bottom: 20px;
    .parameter-title {
      font-family: 'Noto Sans';
      color: #292c33;
      font-size: 14px;
      margin-left: 10px;
      display: inline-block;
      width: 100px;
    }
    .parameter-info {
      font-family: 'Noto Sans';
      color: #292c33;
      font-size: 14px;
    }
  }
  .parameter-oversize-container {
    height: 172px;
    overflow: auto;
  }

  .tip-text {
    color: #FF772E;
    margin-bottom: 10px;
    margin-top: -10px;
  }
  .data-container {
    font-family: 'Noto Sans';
    background-color: #fff;
  }
}
</style>
