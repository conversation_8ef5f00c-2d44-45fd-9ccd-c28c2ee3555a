<template>
  <div class="service-list-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_service_list') }}</div>
    </div>

    <div class="content-container">
      <el-form :model="searchForm" :label-width="isFold ? '' : locale == 'en' ? '90px' : '64px'" label-position="left" :class="isFold ? 'fold-container' : 'unfold-container'">
        <el-form-item :label="$t('common.pp_time_frame')">
          <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :shortcuts="shortcuts" :clearable="false" :disabledDate="getDisabledDate" />
        </el-form-item>
        <el-form-item :label="$t('deviceManagement.pp_device_select')">
          <CommonDeviceSelect v-model="searchForm.device_ids" class="width-full" :isMultiple="true" :multipleLimit="20" />
        </el-form-item>
        <el-form-item :label="$t('station.pp_car_id')" v-if="!isFold">
          <el-input v-model.trim="searchForm.ev_id" :placeholder="$t('common.pp_please_input')" clearable />
        </el-form-item>
        <el-form-item :label="$t('station.pp_battery_id')" v-if="!isFold">
          <el-input v-model.trim="searchForm.battery_id" :placeholder="$t('common.pp_please_input')" clearable />
        </el-form-item>
        <el-form-item :label="$t('station.pp_service_id')" v-if="!isFold">
          <el-input v-model.trim="searchForm.service_id" :placeholder="$t('common.pp_please_input')" clearable />
        </el-form-item>
        <el-form-item :label="$t('common.pp_vehicle_brand')" v-if="!isFold">
          <el-cascader v-model="brand" :options="brandOptions" :props="cascaderProps" separator="-" collapse-tags clearable @change="handleBrandChange" class="brand-cascader" :placeholder="t('common.pp_please_select')" />
        </el-form-item>
        <el-form-item :label="$t('station.pp_status_select')" v-if="!isFold">
          <el-select v-model="searchForm.finish_result" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('station.pp_stuck')" v-if="!isFold">
          <el-select v-model="searchForm.is_stuck" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option v-for="item in isStuckOptions" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item label-width="0">
          <div class="width-full flex-box flex_a_i-center" :class="{ 'flex_j_c-flex-end': !isFold }">
            <div @click="changeFold" class="margin_r-14 font-size-16 line-height-24">
              <div class="cursor-pointer flex-box flex_a_i-center gap_4" style="color: #595959" v-if="!isFold">
                <UpIcon />
                <span>{{ $t('station.pp_collapse') }}</span>
              </div>
              <div class="cursor-pointer flex-box flex_a_i-center gap_4" style="color: #595959" v-if="isFold">
                <DownIcon />
                <span>{{ $t('station.pp_expand') }}</span>
              </div>
            </div>
            <el-button class="welkin-primary-button" @click="filterEvent" :loading="loading">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
          </div>
        </el-form-item>
      </el-form>

      <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="finish_result" :label="$t('common.pp_status')" width="56" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip :content="[1, -1, 0].includes(scope.row.finish_result) ? $t(statusNameMap[scope.row.finish_result]) : $t('station.pp_unknown')" placement="top">
              <div class="flex-box flex_j_c-center flex_a_i-center">
                <SuccessIcon v-if="scope.row.finish_result == 1" />
                <FailIcon v-else-if="scope.row.finish_result == 0" />
                <UnfinishedIcon v-else-if="scope.row.finish_result == -1" />
                <UnknownIcon v-else />
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('deviceManagement.pp_device_name')" width="300" show-overflow-tooltip>
          <template #default="scope">
            <span class="light-column" @click="changeToSingleStation(scope.$index, scope.row)">
              {{ scope.row.description }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="device_id" :label="$t('deviceManagement.pp_device_id')" width="240">
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="service_start_time" :label="$t('common.pp_start_time')" width="170" show-overflow-tooltip>
          <template #default="scope">
            {{ formatLocaleDate(scope.row.service_start_time, false) }}
          </template>
        </el-table-column>
        <el-table-column prop="service_end_time" :label="$t('common.pp_end_time')" width="170" show-overflow-tooltip>
          <template #default="scope">
            {{ formatLocaleDate(scope.row.service_end_time, false) }}
          </template>
        </el-table-column>
        <el-table-column prop="service_duration" :label="$t('station.pp_service_duration')" width="100" show-overflow-tooltip />
        <el-table-column prop="ev_brand" :label="$t('common.pp_vehicle_brand')" width="100" show-overflow-tooltip>
          <template #default="scope"> {{ scope.row.ev_brand ? scope.row.ev_brand : '' }}-{{ scope.row.ev_type ? scope.row.ev_type : '' }} </template>
        </el-table-column>
        <el-table-column prop="service_battery_id" :label="$t('station.pp_service_battery_id')" width="240">
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.service_battery_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="ev_battery_id" :label="$t('station.pp_car_battery_id')" width="240">
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.ev_battery_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="ev_id" :label="$t('station.pp_car_id')" width="200">
          <template #default="scope">
            <WelkinCopyBoard :text="maskString(scope.row.ev_id)" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="service_id" :label="$t('station.pp_service_id')" width="200">
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.service_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.pp_operation')" width="180" class-name="operation-column">
          <template #default="scope">
            <div @click.prevent="serviceDetail(scope.$index, scope.row)">
              <a :href="`https://${host}/${project.route}/service-list/service-detail/${scope.row.device_id}/${scope.row.service_id}?start_time=${scope.row.service_start_time}&end_time=${scope.row.service_end_time}`">
                <el-tooltip effect="dark" :content="$t('menu.pp_service_detail')" placement="bottom">
                  <ServiceIcon />
                </el-tooltip>
              </a>
            </div>
            <el-tooltip :content="$t('station.pp_view_normal_image')" placement="bottom">
              <NormalImage v-if="scope.row.has_normal_image" @click="viewNormalImage(scope.$index, scope.row)" class="cursor-pointer" />
              <NonNormalImage v-else @click="viewNormalImage(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
            <el-tooltip :content="$t('station.pp_view_abnormal_picture')" placement="bottom">
              <AbnormalImage v-if="scope.row.has_abnormal_image" @click="viewAbnormalImage(scope.$index, scope.row)" class="cursor-pointer" />
              <NonAbnormalImage v-else @click="viewAbnormalImage(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
            <el-tooltip :content="$t('station.pp_view_flash')" placement="bottom">
              <FlashIcon @click="goToFlashPage(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination">
        <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
      <div v-if="showImageViewer">
        <WelkinImageViewer :data="imageList" @onClose="closeImageView" />
      </div>
      <el-dialog v-model="showFlashLogDialog" :title="$t('menu.pp_flash_log')" width="1200px" align-center @close="showFlashLogDialog = false">
        <div class="station-flash-log">
          <el-table :data="flashLogList" v-loading="flashLogLoading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
            <el-table-column prop="refresh_result" :label="$t('station.pp_status')" :width="locale == 'zh' ? 56 : 70" show-overflow-tooltip>
              <template #default="{ row }">
                <el-tooltip :content="flashLogGetResultMap(row.refresh_result)" placement="top">
                  <div class="flex-box flex_j_c-center flex_a_i-center">
                    <SuccessIcon v-if="row.refresh_result == '1'" />
                    <FailIcon v-else-if="row.refresh_result == '2'" />
                    <OvertimeIcon v-else-if="row.refresh_result == '3'" />
                    <UnknownIcon v-else />
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="start_time" :label="$t('station.pp_flash_start_time')" min-width="160" show-overflow-tooltip>
              <template #default="{ row }">
                {{ formatLocaleDate(row.start_time, false) }}
              </template>
            </el-table-column>
            <el-table-column prop="end_time" :label="$t('station.pp_flash_end_time')" min-width="160" show-overflow-tooltip>
              <template #default="{ row }">
                {{ formatLocaleDate(row.end_time, false) }}
              </template>
            </el-table-column>
            <el-table-column prop="battery_id" :label="$t('station.pp_flash_battery_id')" min-width="240">
              <template #default="{ row }">
                <WelkinCopyBoard :text="row.battery_id" direction="rtl" />
              </template>
            </el-table-column>
            <el-table-column prop="refresh_type" :label="$t('station.pp_trigger_mode')" :min-width="locale == 'zh' ? 120 : 150" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="refresh-type-tag" :style="{ color: refreshTypeMap[row.refresh_type].color, background: refreshTypeMap[row.refresh_type].background, width: locale == 'zh' ? '64px' : '120px' }">
                  {{ $t(refreshTypeMap[row.refresh_type].name) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="battery_capacity" :label="$t('station.pp_flash_battery_capacity')" :min-width="locale == 'zh' ? 140 : 150" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.battery_capacity">{{ row.battery_capacity + 'kW' }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="refresh_target_version" :label="$t('station.pp_flash_target_version')" min-width="140" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ row.refresh_target_version || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="battery_road" :label="$t('station.pp_battery_branch_ownership')" :min-width="locale == 'zh' ? 130 : 140" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ row.battery_road || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('station.pp_detail')" width="100" fixed="right" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="operate-column" @click="flashLogHandleViewDetail(row)">
                  <EyeIcon />
                  <span class="edit-text">{{ $t('common.pp_view') }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="common-pagination">
            <Page :page="flashLogPages" @change="flashLogHandlePageChange" />
          </div>
          <el-dialog v-model="flashLogDetailVisible" :title="$t('station.pp_detail')" width="1141px" align-center @close="flashLogDetailVisible = false">
            <div class="text-container">
              <div class="text-item">
                <div class="text-label">{{ $t('station.pp_flash_result') }}</div>
                <div class="text-value flex-box flex_a_i-center gap_6">
                  <SuccessIcon v-if="flashLogRowInfo.refresh_result == '1'" />
                  <FailIcon v-else-if="flashLogRowInfo.refresh_result == '2'" />
                  <OvertimeIcon v-else-if="flashLogRowInfo.refresh_result == '3'" />
                  <UnknownIcon v-else />
                  <span :style="{ color: refreshResultMap[flashLogRowInfo.refresh_result]?.color }">{{ flashLogGetResultMap(flashLogRowInfo.refresh_result) }}</span>
                </div>
              </div>
              <div class="text-item">
                <div class="text-label">{{ $t('common.pp_start_time') }}</div>
                <div class="text-value">{{ formatLocaleDate(flashLogRowInfo.start_time, false) }}</div>
              </div>
              <div class="text-item">
                <div class="text-label">{{ $t('common.pp_end_time') }}</div>
                <div class="text-value">{{ formatLocaleDate(flashLogRowInfo.end_time, false) }}</div>
              </div>
              <div class="text-item">
                <div class="text-label">{{ $t('station.pp_trigger_mode') }}</div>
                <div class="text-value">{{ $t(refreshTypeMap[flashLogRowInfo.refresh_type]?.name) }}</div>
              </div>
              <div class="text-item">
                <div class="text-label">{{ $t('station.pp_flash_battery_id') }}</div>
                <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.battery_id" direction="rtl" /></div>
              </div>
              <div class="text-item">
                <div class="text-label">{{ $t('station.pp_flash_battery_capacity') }}</div>
                <div class="text-value">{{ flashLogRowInfo.battery_capacity ? flashLogRowInfo.battery_capacity + 'kW' : '-' }}</div>
              </div>
              <div class="text-item">
                <div class="text-label">{{ $t('station.pp_flash_target_version') }}</div>
                <div class="text-value">{{ flashLogRowInfo.refresh_target_version || '-' }}</div>
              </div>
              <div class="text-item">
                <div class="text-label">{{ $t('station.pp_battery_branch_ownership') }}</div>
                <div class="text-value">{{ flashLogRowInfo.battery_road || '-' }}</div>
              </div>
              <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1'">
                <div class="text-label">{{ $t('station.pp_authentication_start') }}</div>
                <div class="text-value">{{ formatLocaleDate(flashLogRowInfo.authenticate_timestamp, false) }}</div>
              </div>
              <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1' || flashLogRowInfo.refresh_type == '2'">
                <div class="text-label">{{ $t('station.pp_cloud_specified_battery_id') }}</div>
                <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.oss_command_battery_id" direction="rtl" /></div>
              </div>
              <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1' || flashLogRowInfo.refresh_type == '2'">
                <div class="text-label">{{ $t('station.pp_cloud_specified_battery_capacity') }}</div>
                <div class="text-value">{{ flashLogRowInfo.oss_command_battery_capacity ? flashLogRowInfo.oss_command_battery_capacity + 'kW' : '-' }}</div>
              </div>
              <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1' || flashLogRowInfo.refresh_type == '2'">
                <div class="text-label">{{ $t('station.pp_order_id') }}</div>
                <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.order_id" direction="rtl" /></div>
              </div>
              <div class="text-item" v-if="flashLogRowInfo.refresh_type == '3'">
                <div class="text-label">{{ $t('station.pp_request_id') }}</div>
                <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.oss_request_id" direction="rtl" /></div>
              </div>
            </div>
          </el-dialog>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onBeforeUnmount } from 'vue'
import { formatLocaleDate, getDuration, getShortcuts, getDisabledDate, removeNullProp, maskString, isEmptyData } from '~/utils'
import { statusOptions, isStuckOptions, statusNameMap } from '~/views/single-station/constant'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { apiGetServiceList } from '~/apis/service-list'
import { apiGetImageList } from '~/apis/image-list'
import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'
import { pagination } from '~/constvars'
import ServiceIcon from '~/assets/svg/service-icon.vue'
import NormalImage from '~/assets/svg/normal-image.vue'
import NonNormalImage from '~/assets/svg/non-normal-image.vue'
import AbnormalImage from '~/assets/svg/abnormal-image.vue'
import NonAbnormalImage from '~/assets/svg/non-abnormal-image.vue'
import UpIcon from './icon/up-icon.vue'
import DownIcon from './icon/down-icon.vue'
import SuccessIcon from '~/views/single-station/icon/success-icon.vue'
import FailIcon from '~/views/single-station/icon/fail-icon.vue'
import UnknownIcon from '~/views/single-station/icon/unknown-icon.vue'
import UnfinishedIcon from '~/views/single-station/icon/unfinished-icon.vue'
import FlashIcon from '~/assets/svg/flash-icon.vue'
import { apiGetFlashLog } from '~/apis/plc-record'
import { refreshResultOptions, refreshTypeOptions, refreshTypeMap, refreshResultMap } from '~/views/single-station/constant'
import Page from '~/components/global/page/index.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import EyeIcon from '~/assets/svg/eye-icon.vue'
import OvertimeIcon from '~/views/single-station/icon/overtime-icon.vue'
import _ from 'lodash'

import 'swiper/css'

const { t } = useI18n()
const $store = useStore()
const $route = useRoute()
const $router = useRouter()
const project = ref(computed(() => $store.state.project))
const brandOptions = ref(computed(() => $store.state.vehicle.brandList))
const isFold = ref(computed(() => $store.state.fold.serviceFold))
const loading = ref(false)
const showImageViewer = ref(false)
const imageList = ref([] as any)
const host = ref(window.location.host)
const cascaderProps = reactive({ multiple: true })
const tableList = ref([] as any)
const brand = ref([] as any)
const totalNumber = ref(0)
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date()] as any)
const searchForm = reactive({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  device_ids: [] as any,
  ev_id: '',
  service_id: '',
  battery_id: '',
  isWarning: '',
  abnormalPictures: '',
  ev_brand: '',
  ev_type: '',
  finish_result: '' as number | string,
  is_stuck: '' as boolean | string,
  page: 1,
  size: 10,
  descending: true
})
const shortcuts = ref(getShortcuts())
const { locale } = useI18n({ useScope: 'global' })
watch(
  () => locale.value,
  (newValue, oldValue) => {
    if (newValue === 'en') {
      searchForm.abnormalPictures = 'All'
      searchForm.isWarning = 'All'
    }
  },
  { immediate: true }
)

/**
 * @description: 选择车辆品牌
 * @return {*}
 */
const handleBrandChange = () => {
  const result = brand.value.reduce(
    (acc: any, curr: any) => {
      const [brand, type] = curr
      if (!acc.ev_brand.includes(brand)) acc.ev_brand.push(brand)
      if (!acc.ev_type.includes(type)) acc.ev_type.push(type)
      return acc
    },
    { ev_brand: [], ev_type: [] }
  )
  searchForm.ev_brand = result.ev_brand.join(',')
  searchForm.ev_type = result.ev_type.join(',')
}

// 重置
const resetSelect = () => {
  brand.value = []
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
  searchForm.end_time = new Date().getTime()
  datePicker.value = [searchForm.start_time, searchForm.end_time]

  searchForm.device_ids = []
  searchForm.battery_id = ''
  searchForm.ev_id = ''
  searchForm.service_id = ''
  searchForm.finish_result = ''
  searchForm.is_stuck = ''
  searchForm.ev_brand = ''
  searchForm.ev_type = ''
  publicSearch()
}

const changeFold = () => {
  $store.commit('fold/SET_SERVICE_FOLD')
}

// 查看服务详情
const serviceDetail = (index: any, row: any) => {
  let formData = {} as any
  formData = { ...searchForm }
  formData.device_ids = formData.device_ids.join(',')
  sessionStorage.setItem('service-list', JSON.stringify({ ...removeNullProp(formData) }))
  $router.push({
    path: `/${project.value.route}/service-list/service-detail/${row.device_id}/${row.service_id}`,
    query: {
      start_time: row.service_start_time,
      end_time: row.service_end_time
    }
  })
}

// 进入单站设备详情
const changeToSingleStation = (index: any, row: any) => {
  $router.push({
    path: `/${project.value.route}/device-management/single-station/${row.device_id}`
  })
}

const closeImageView = () => {
  showImageViewer.value = false
  imageList.value = []
}

const viewServiceImage = (query: any, device_id: any) => {
  query.end_time = Number(query.end_time) + 5 * 60 * 1000
  return apiGetImageList(query, device_id, project.value.project).then((res) => {
    if (res.data.length === 0) {
      showImageViewer.value = false
      ElMessage.warning(t('common.pp_lack_image'))
    } else {
      imageList.value = res.data
      showImageViewer.value = true
    }
  })
}
// 查看图像
const viewNormalImage = (index: any, row: any) => {
  const start_time = row.service_start_time ? row.service_start_time : Number(searchForm.start_time)
  const end_time = row.service_end_time ? row.service_end_time : Number(searchForm.end_time)

  const query = {
    abnormal: 0,
    service_id: row.service_id,
    start_time: start_time,
    end_time: end_time
  }
  return viewServiceImage(query, row.device_id)
}

// 查看异常图像
const viewAbnormalImage = (index: any, row: any) => {
  const start_time = row.service_start_time ? row.service_start_time : Number(searchForm.start_time)
  const end_time = row.service_end_time ? row.service_end_time : Number(searchForm.end_time)

  const query = {
    abnormal: 1,
    service_id: row.service_id,
    start_time: start_time,
    end_time: end_time
  }
  return viewServiceImage(query, row.device_id)
}

// 跳转到电池刷写页面
const goToFlashPage = (index: any, row: any) => {
  showFlashLogDialog.value = true
  flashLogDeviceId.value = row.device_id
  const serviceEndTime = row.service_end_time === 0 ? row.service_start_time : row.service_end_time
  const start = serviceEndTime - 2 * 60 * 60 * 1000
  const end = serviceEndTime
  flashLogForm.value = {
    start_time: start,
    end_time: end,
    battery_id: row.service_battery_id || '',
    refresh_result: '',
    battery_capacity: '',
    refresh_type: '',
    refresh_target_version: ''
  }
  flashLogDatePicker.value = [start, end]
  flashLogSearchForm.value = { ...flashLogForm.value }
  flashLogPages.value.current = 1
  getFlashLogList()
}

// 公用搜索事件
const publicSearch = (updateRoute = true) => {
  let formData = {} as any
  formData = { ...searchForm }
  formData.device_ids = formData.device_ids.join(',')
  loading.value = true
  updateRoute &&
    $router.push({
      path: $router.currentRoute.value.path,
      query: { ...removeNullProp(formData) }
    })

  return apiGetServiceList(formData, project.value.project)
    .then((res) => {
      tableList.value = res.data || []
      totalNumber.value = res.total
      tableList.value.map((item: any) => {
        item.service_duration = getDuration(item.service_start_time, item.service_end_time)
      })
    })
    .finally(() => {
      loading.value = false
    })
}

const handleDateChange = (value: any) => {
  if (value) {
    searchForm.start_time = value[0].valueOf()
    searchForm.end_time = value[1].valueOf()
  } else {
    searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
    searchForm.end_time = new Date().getTime()
  }
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  searchForm.size = val
  searchForm.page = 1
  publicSearch()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  searchForm.page = val
  publicSearch()
}

// 点击筛选按钮
const filterEvent = () => {
  searchForm.page = 1
  publicSearch()
}

const initPage = () => {
  let init_params: any = $route.query
  if (!isNaN(init_params.start_time) && !isNaN(init_params.end_time)) {
    searchForm.start_time = init_params.start_time
    searchForm.end_time = init_params.end_time
    datePicker.value[0] = Number(searchForm.start_time)
    datePicker.value[1] = Number(searchForm.end_time)
  }
  if (!!init_params.page && !!init_params.size) {
    searchForm.page = Number(init_params.page)
    searchForm.size = Number(init_params.size)
  }

  brand.value = init_params.ev_type ? init_params.ev_type.split(',') : []
  searchForm.device_ids = init_params.device_ids ? init_params.device_ids.split(',') : []
  searchForm.ev_id = init_params.ev_id
  searchForm.service_id = init_params.service_id
  searchForm.battery_id = init_params.battery_id
  searchForm.ev_brand = init_params.ev_brand
  searchForm.ev_type = init_params.ev_type
  searchForm.finish_result = !!init_params.finish_result ? Number(init_params.finish_result) : ''
  searchForm.is_stuck = !!init_params.is_stuck ? JSON.parse(init_params.is_stuck) : ''
  publicSearch(false)
}

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    console.log('服务列表 watch', newPath, oldPath)
    if (!oldPath) {
      console.log('url进入')
      initPage()
    } else if (newPath.split('/').length == 3 && newPath.split('/')[2] == oldPath.split('/')[2]) {
      console.log('切换版本')
      resetSelect()
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  stopWatch()
})

const showFlashLogDialog = ref(false)
const flashLogLoading = ref(false)
const flashLogPages = ref(_.cloneDeep({ current: 1, size: 10, total: 0, sizes: [5, 10, 20, 50, 100] }))
const flashLogList = ref([] as any)
const flashLogForm = ref({
  start_time: 0,
  end_time: 0,
  battery_id: '',
  refresh_result: '',
  battery_capacity: '',
  refresh_type: '',
  refresh_target_version: ''
})
const flashLogDatePicker = ref([0, 0] as any)
const flashLogSearchForm = ref({} as any)
const flashLogRowInfo = ref({} as any)
const flashLogDetailVisible = ref(false)
const flashLogDeviceId = ref('')

function flashLogGetResultMap(result: string) {
  const findObj = refreshResultOptions.find((obj: any) => obj.value == result)
  return findObj ? t(findObj.label) : t('station.pp_unknown')
}

function flashLogHandleViewDetail(row: any) {
  flashLogRowInfo.value = row
  flashLogDetailVisible.value = true
}

function flashLogHandlePageChange(argPage: any) {
  flashLogPages.value.current = argPage.current
  flashLogPages.value.size = argPage.size
  getFlashLogList()
}

async function getFlashLogList() {
  if (flashLogLoading.value) return
  let formData = { ...flashLogSearchForm.value }
  formData.page = flashLogPages.value.current
  formData.size = flashLogPages.value.size
  formData.descending = true
  flashLogLoading.value = true
  try {
    const res = await apiGetFlashLog(formData, project.value.project, flashLogDeviceId.value)
    flashLogList.value = res.data
    flashLogPages.value.total = res.total
  } catch (error) {}
  flashLogLoading.value = false
}
</script>

<style lang="scss" scoped>
.service-list-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    background-color: #fff;
    :deep(.fold-container) {
      display: grid;
      grid-template-columns: 1fr 1fr 220px;
      gap: 16px 24px;
    }
    :deep(.unfold-container) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px 24px;
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-form-item__content {
          flex-wrap: nowrap;
          align-items: flex-start;
        }
        .brand-cascader {
          width: 100%;
          height: 32px;
          .el-input {
            height: 32px;
          }
        }
        .el-tag.el-tag--info {
          color: #262626;
        }
      }
    }
    :deep(.operation-column) {
      .cell {
        display: flex;
        gap: 14px;
        div,
        a {
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    :deep(.operate-column) {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      margin-top: 2px;
      .edit-text {
        color: #01a0ac;
      }
    }
    :deep(.el-dialog) {
      .el-dialog__header {
        padding: 24px 28px 16px;
        .el-dialog__title {
          color: #1f1f1f;
        }
      }
      .el-dialog__body {
        padding: 0 28px 32px;
        .text-container {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 12px 88px;
          .text-item {
            display: flex;
            align-items: center;
            .text-label {
              width: 120px;
              font-size: 14px;
              line-height: 22px;
              color: #8c8c8c;
            }
            .text-value {
              width: 183px;
              font-size: 14px;
              line-height: 22px;
              color: #262626;
            }
          }
        }
      }
    }
    .refresh-type-tag {
      display: flex;
      height: 24px;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      font-size: 12px;
      line-height: 18px;
    }
  }
}
</style>
