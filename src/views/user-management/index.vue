<template>
  <div>
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <WelkinStationBreadcrumb :version="project.version" />
          <el-breadcrumb-item>
            {{ $t('menu.pp_user_management') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">
          {{ $t('menu.pp_user_management') }}
        </div>
      </div>
      <div class="header-right">
        <el-button class="welkin-primary-button" @click="showAddDialog"> + {{ $t('userManagement.pp_add_user') }} </el-button>
      </div>
    </div>

    <div class="swap-page-container">
      <div class="search-container">
        <span class="search-item-label">
          {{ $t('userManagement.pp_user_name') }}
        </span>
        <el-input v-model="searchForm.username" :placeholder="`${$t('userManagement.pp_enter_user_name')}`" class="width-400" clearable />
        <span class="search-item-label">
          {{ $t('userManagement.pp_domain_account') }}
        </span>
        <el-input v-model.trim="searchForm.user_id" :placeholder="`${$t('userManagement.pp_enter_domain_account')}`" class="width-400" clearable />
        <span class="search-item-label">
          {{ $t('userManagement.pp_role') }}
        </span>
        <el-select v-model="searchForm.role" :placeholder="`${$t('userManagement.pp_select_role')}`" class="width-400" clearable filterable>
          <el-option v-for="item in roleOptions" :key="item.value" :label="$t(`userManagement.${item.label}`)" :value="item.value" />
        </el-select>
        <div class="margin_l-20">
          <div class="search-button">
            <el-button class="welkin-primary-button" :loading="loading" @click="filterEvent">
              {{ $t('common.pp_search') }}
            </el-button>
            <el-button @click="reset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
          </div>
        </div>
      </div>
      <div class="swap-table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="{
            // 'text-align': 'center',
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
        >
          <el-table-column prop="username" :label="`${$t('userManagement.pp_user_name')}`" min-width="20%" />
          <el-table-column prop="user_id" :label="`${$t('userManagement.pp_domain_account')}`" min-width="20%" />
          <el-table-column prop="role" :label="`${$t('userManagement.pp_role')}`" min-width="20%">
            <template #default="scope">
              <span v-for="(item, index) in scope.row.role" :key="index" class="user-role-tag">
                <el-tag :color="roleMap[item]?.color" effect="dark" disable-transitions round>
                  {{ $t(`userManagement.${roleMap[item]?.label}`) }}
                </el-tag>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_time" :label="`${$t('userManagement.pp_create_time')}`" min-width="30%">
            <template #default="scope">
              {{ formatLocaleDate(scope.row.created_time, false) }}
            </template>
          </el-table-column>
          <el-table-column :label="`${$t('common.pp_operation')}`" min-width="10%" class-name="operation-column">
            <template #default="scope">
              <div @click="editTable(scope.$index, scope.row)" class="edit-icon">
                <el-popover width="auto" placement="bottom" trigger="hover" effect="dark" :content="`${$t('common.pp_edit')}`" popper-class="message-popover">
                  <template #reference>
                    <el-icon class="operation-icon">
                      <Icon :icon="iconMap['user-management-edit']" />
                    </el-icon>
                  </template>
                </el-popover>
              </div>
              <div @click="deleteTable(scope.$index, scope.row)" class="delete-icon">
                <el-popover width="auto" placement="bottom" trigger="hover" effect="dark" :content="`${$t('common.pp_delete')}`" popper-class="message-popover">
                  <template #reference>
                    <el-icon class="operation-icon">
                      <Icon :icon="iconMap['user-management-delete']" />
                    </el-icon>
                  </template>
                </el-popover>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>

    <!-- 顶部 添加用户 对话框 -->
    <el-dialog v-model="addUserDialog" :title="`${$t('userManagement.pp_add_user')}`" top="20vh" :close-on-click-modal="false" @close="closeAddUserDialog" class="user-delete-dialog">
      <el-form ref="addRuleFormRef" :model="addUserForm" :rules="rules">
        <el-form-item :label="`${$t('userManagement.pp_user_name')}`" prop="username" :label-width="formLabelWidth">
          <el-input v-model="addUserForm.username" :placeholder="`${$t('userManagement.pp_enter_user_name')}`" clearable />
        </el-form-item>
        <el-form-item :label="`${$t('userManagement.pp_domain_account')}`" prop="user_id" :label-width="formLabelWidth">
          <el-input v-model.trim="addUserForm.user_id" :placeholder="`${$t('userManagement.pp_enter_domain_account')}`" clearable />
        </el-form-item>
        <el-form-item :label="`${$t('userManagement.pp_role')}`" prop="role" :label-width="formLabelWidth">
          <el-select v-model="addUserForm.role" :placeholder="`${$t('userManagement.pp_select_role')}`" clearable filterable>
            <el-option v-for="item in roleEditOptions" :key="item.value" :label="$t(`userManagement.${item.label}`)" :value="item.value" :disabled="item.disabled" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelAddUser(addRuleFormRef)" class="welkin-text-button">
            {{ $t('common.pp_cancel') }}
          </el-button>
          <el-button @click="addUser(addRuleFormRef)" class="welkin-primary-button" style="margin-left: 4px">
            {{ $t('userManagement.pp_add_user') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量添加 对话框（已被注释，暂无） -->
    <el-dialog v-model="batchAddDialog" :title="`${$t('userManagement.pp_batch_add')}`" top="20vh" :close-on-click-modal="false" @close="closeBatchAddDialog" class="user-delete-dialog">
      <el-upload ref="uploadRef" class="upload-demo" drag action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" multiple>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          <span class="normal-text">
            {{ $t('userManagement.pp_upload_normal_text') }}
          </span>
          <span class="light-text">
            {{ $t('userManagement.pp_upload_light_text') }}
          </span>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            <span class="normal-tips">
              {{ $t('userManagement.pp_upload_normal_tip1') }}
            </span>
            <span class="light-tips">
              {{ $t('userManagement.pp_download_batch_template') }}
            </span>
            <span class="normal-tips">
              {{ $t('userManagement.pp_upload_normal_tip2') }}
            </span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelBatch">
            {{ $t('userManagement.pp_download_batch_template') }}
          </el-button>
          <el-button @click="cancelBatch">
            {{ $t('common.pp_cancel') }}
          </el-button>
          <el-button @click="cancelBatch" class="add-user-button">
            {{ $t('userManagement.pp_batch_add_user') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 操作栏 编辑对话框 -->
    <el-dialog v-model="editUserDialog" :title="`${$t('userManagement.pp_edit_user')}`" top="20vh" :close-on-click-modal="false" @close="closeEditUserDialog" class="user-delete-dialog">
      <el-form ref="editRuleFormRef" :model="editUserForm" :rules="rules">
        <el-form-item :label="`${$t('userManagement.pp_role')}`" prop="role" :label-width="formLabelWidth">
          <el-select v-model="editUserForm.role" :placeholder="`${$t('userManagement.pp_select_role')}`" clearable filterable>
            <el-option v-for="item in roleEditOptions" :key="item.value" :label="$t(`userManagement.${item.label}`)" :value="item.value" :disabled="item.disabled" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelEditUser(editRuleFormRef)" class="welkin-text-button">
            {{ $t('common.pp_cancel') }}
          </el-button>
          <el-button @click="editUser(editRuleFormRef)" class="welkin-primary-button" style="margin-left: 4px">
            {{ $t('common.pp_confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 操作栏删除对话框 -->
    <el-dialog v-model="deleteUserDialog" :title="`${$t('userManagement.pp_delete_user')}`" top="20vh" :close-on-click-modal="false" class="user-delete-dialog">
      <div class="delete-user">
        <span>{{ $t('userManagement.pp_delete_info') }}</span>
        <span class="delete-username">{{ deleteUsername }}</span>
        <span>?</span>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteUserDialog = false" class="welkin-text-button">
            {{ $t('common.pp_cancel') }}
          </el-button>
          <el-button @click="deleteUser" class="welkin-primary-button" style="margin-left: 4px">
            {{ $t('common.pp_confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, watch, onBeforeUnmount, onBeforeMount, computed} from 'vue'
import {iconMap} from '~/auth'
import {Icon} from '@iconify/vue/dist/iconify'
import {formatLocaleDate} from '~/utils'
import type {FormInstance, FormRules, UploadInstance} from 'element-plus'
import {ElMessage} from 'element-plus'
import {UploadFilled} from '@element-plus/icons-vue'
import {useI18n} from 'vue-i18n'
import {useRoute} from 'vue-router'
import {useStore} from 'vuex'
import {apiGetUserInfo, apiPostUserInfo, apiPutUserInfo, apiDeleteUserInfo} from '~/apis/user-management'
import {roleMap, roleOptions, roleEditOptions} from '~/constvars/user-management'
import {pagination} from '~/constvars'
// 监听路由
const $route = useRoute()
const $store = useStore()
const {t} = useI18n()

const project = ref(computed(() => $store.state.project))

// const activeTab = ref('登陆权限');
const loading = ref(false)
const addUserDialog = ref(false)
const batchAddDialog = ref(false)
const editUserDialog = ref(false)
const deleteUserDialog = ref(false)
const addUserForm = reactive({
  username: '',
  user_id: '',
  role: ''
})
const editUserForm = reactive({
  role: ''
})
const searchForm = reactive({
  user_id: '',
  username: '',
  role: '',
  page: 1,
  size: 10,
  descending: true
})
const formLabelWidth = '100px'
const totalNumber = ref(0)
const addRuleFormRef = ref<FormInstance>()
const editRuleFormRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const usernameRules = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error(t('userManagement.pp_enter_user_name')))
  }
  // if (value.indexOf(' ') !== -1) {
  //   return callback(new Error(t('userManagement.pp_not_space')));
  // }
  else {
    return callback()
  }
}
const areaRules = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error(t('userManagement.pp_enter_domain_account')))
  }
  if (value.indexOf(' ') !== -1) {
    return callback(new Error(t('userManagement.pp_not_space')))
  } else {
    return callback()
  }
}
const roleRules = (rule: any, value: any, callback: any) => {
  if (value.length === 0) {
    return callback(new Error(t('userManagement.pp_select_role')))
  } else {
    return callback()
  }
}
const rules = reactive<FormRules>({
  username: [
    {
      validator: usernameRules,
      required: true,
      trigger: 'blur'
    }
  ],
  user_id: [
    {
      validator: areaRules,
      required: true,
      trigger: 'blur'
    }
  ],
  role: [
    {
      validator: roleRules,
      required: true,
      trigger: 'change'
    }
  ]
})
const tableData = ref([] as any)
const editUserId = ref('')
const deleteUsername = ref('')
const deleteUserId = ref('')

// 点击编辑icon
const editTable = (index: any, row: any) => {
  editUserDialog.value = true
  editUserForm.role = row.role[0]
  editUserId.value = row.user_id
}

// 点击删除icon
const deleteTable = (index: any, row: any) => {
  console.log('deleteTable', row)
  deleteUserDialog.value = true
  deleteUsername.value = row.username
  deleteUserId.value = row.user_id
}

// 点击添加用户按钮
const showAddDialog = () => {
  addUserDialog.value = true
}

// 取消添加用户
const cancelAddUser = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  addUserDialog.value = false
  formEl.resetFields()
}

// 取消编辑用户
const cancelEditUser = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  editUserDialog.value = false
  formEl.resetFields()
}

// 关闭添加用户对话框
const closeAddUserDialog = () => {
  addRuleFormRef.value?.resetFields()
}

// 关闭编辑用户对话框
const closeEditUserDialog = () => {
  editRuleFormRef.value?.resetFields()
}

// 点击批量添加按钮
const showBatchDialog = () => {
  batchAddDialog.value = true
}

// 取消批量添加
const cancelBatch = () => {
  batchAddDialog.value = false
}

// 关闭批量添加对话框
const closeBatchAddDialog = () => {
  uploadRef.value?.clearFiles()
}

const publicSearch = () => {
  return apiGetUserInfo(searchForm, project.value.project)
    .then((res: any) => {
      tableData.value = res.data
      totalNumber.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
// 筛选
const filterEvent = () => {
  loading.value = true
  searchForm.page = 1
  searchForm.size = 10
  publicSearch()
}

// 重置
const reset = () => {
  loading.value = true
  searchForm.username = ''
  searchForm.user_id = ''
  searchForm.role = ''
  searchForm.page = 1
  searchForm.size = 10
  publicSearch()
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true
  searchForm.page = 1
  searchForm.size = val
  publicSearch()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true
  searchForm.page = val
  publicSearch()
}

// 添加用户
const addUser = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      const message = addUserForm.user_id
      addUserDialog.value = false
      const postQuery = reactive({
        username: '',
        role: [] as any
      })
      postQuery.username = addUserForm.username
      postQuery.role.push(addUserForm.role)
      return apiPostUserInfo(addUserForm.user_id, postQuery, project.value.project).then((res) => {
        if (res.data.err_code === 3) {
          ElMessage.error(`添加用户失败,已存在域账号${message}`)
        }
        reset()
      })
    }
  })
}

// 编辑用户
const editUser = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      editUserDialog.value = false
      const editQuery = reactive({
        role: [] as any
      })
      editQuery.role.push(editUserForm.role)
      return apiPutUserInfo(editUserId.value, editQuery, project.value.project).then((res) => {
        reset()
      })
    }
  })
}

// 删除用户
const deleteUser = () => {
  return apiDeleteUserInfo(deleteUserId.value, project.value.project).then((res) => {
    deleteUserDialog.value = false
    reset()
    ElMessage.success(`成功删除用户${deleteUsername.value}`)
  })
}

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    console.log('user-management watch', newPath, oldPath)
    if (!oldPath || newPath.split('/')[2] == oldPath.split('/')[2]) {
      console.log('user-management api')
      loading.value = true
      publicSearch()
    }
  },
  {immediate: true}
)

onBeforeUnmount(() => {
  stopWatch()
})
</script>

<style lang="scss">
@import '~/styles/swap-page.scss';
.user-role-tag {
  .el-tag.is-round {
    border: 0px;
    margin-right: 2px;
  }
}
.message-popover {
  min-width: 40px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-delete-dialog {
  width: 500px !important;
  border-radius: 4px;
  .el-dialog__header {
    .el-dialog__headerbtn:hover .el-dialog__close {
      color: var(--el-color-primary);
    }
    .el-dialog__headerbtn .el-dialog__close:hover {
      color: var(--el-color-primary);
    }
  }
  .el-dialog__body {
    padding-bottom: 10px;
    .el-form-item__label {
      padding-right: 18px;
    }
    .el-input {
      width: 300px;
    }
    .normal-text {
      color: #666f7f;
    }
    .light-text {
      color: var(--el-color-primary);
    }
    .el-upload__tip {
      .normal-tips,
      .light-tips {
        font-size: 12px;
        color: #666f7f;
        font-weight: 400;
      }
      .light-tips {
        color: var(--el-color-primary);
      }
    }
    .el-upload.is-drag:hover .el-upload-dragger {
      border-color: var(--el-color-primary);
    }
    .delete-user {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      .delete-username {
        color: var(--el-color-primary);
      }
    }
  }
  .el-dialog__footer {
    .add-user-button {
      background-color: var(--el-color-primary);
      color: #fff;
    }
  }
  .el-dialog__body {
    padding-top: 10px;
  }
}
</style>
