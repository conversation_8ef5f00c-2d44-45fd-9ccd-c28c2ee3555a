/*
 * @Author: zhenxing.chen
 * @Date: 2022-12-16 19:28:53
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-12 15:33:43
 * @Email: <EMAIL>
 * @Description: 迁移owl到天宫
 */
import HighSpeedRecording from './high-speed-recording.vue';
import SensorRecording from './sensor-recording.vue';
import ConverterRecording from './converter-recording.vue';
import OperationRecording from './operation-log.vue';
import DiRecording from './di-recording.vue';
import StateMachine from './state-machine.vue'
import AlarmList from './alarm-list.vue'
export {
  HighSpeedRecording,
  SensorRecording,
  ConverterRecording,
  OperationRecording,
  DiRecording,
  StateMachine,
  AlarmList
};
