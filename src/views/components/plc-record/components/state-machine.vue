<template>
  <div class="state-machine-container">
    <div class="machine-header">
      <div class="tabs-box">
        <div class="tab-item" :class="{ ac: serviceType == 1 }" @click="handleChangeType(1)">{{ $t('serviceDetail.pp_state_machine') }}</div>
        <div class="tab-item" :class="{ ac: serviceType == 2 }" @click="handleChangeType(2)">{{ $t('serviceDetail.pp_standby_mode') }}</div>
        <div :style="{ width: locale == 'en' ? '122px' : serviceType == 1 ? '66px' : '80px' }" class="bg" :class="{ left1: serviceType == 1, left2: serviceType == 2 && locale == 'zh', left3: serviceType == 2 && locale == 'en' }"></div>
      </div>
      <el-form :model="form" ref="formRef" inline label-position="left">
        <el-form-item>
          <template #label>
            <span class="margin_r-4">{{ $t('common.pp_time') }}</span>
            <el-tooltip :content="$t('serviceDetail.pp_limit_warn')" placement="top">
              <HelpIcon />
            </el-tooltip>
          </template>
          <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
            <template #range-separator>
              <TimeRangeIcon style="margin: 0 5px" />
            </template>
          </el-date-picker>
        </el-form-item>

        <el-form-item style="margin-left: 24px">
          <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
          <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="machine-content">
      <el-skeleton :rows="10" animated v-show="loading" />
      <div id="stateCharts" class="width-full height-400" v-show="!loading && !isEmpty"></div>
      <el-empty class="width-full height-400" :description="$t('common.pp_empty')" v-if="!loading && isEmpty"></el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, computed, watch, nextTick, shallowRef, h } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { stateMachineMap1, stateMachineMap2 } from './constant'
import { useRoute, useRouter } from 'vue-router'
import { apiGetStateMachineList } from '~/apis/plc-record'
import { stepOption } from '~/constvars/plc-record/chart'
import { formatTime, getDisabledDate } from '~/utils'
import { ElMessage } from 'element-plus'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import HelpIcon from '~/assets/svg/help.vue'
import * as echarts from 'echarts'
import _ from 'lodash'

const props = defineProps({
  deviceId: {
    type: String,
    default: ''
  }
})

const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})

const { locale } = useI18n({ useScope: 'global' })
const { t } = useI18n()
const store = useStore()
const route = useRoute()
const router = useRouter()
const formRef = ref()
const loading = ref(false)
const isEmpty = ref(true)
const form = ref({
  start_time: '' as number | string,
  end_time: '' as number | string,
  data_id: 205109,
  page: 1,
  size: 9000,
  descending: false,
  download: false
})
const serviceType = ref(1)
const echartsArr = ref([] as any)
const datePicker = ref([] as any)
const currentProject = computed(() => store.state.project)
const isCollapse = computed(() => store.state.menus.collapse)
const statusMap = reactive({
  1: 'serviceDetail.pp_state_automatic',
  2: 'serviceDetail.pp_state_manual',
  3: 'serviceDetail.pp_state_fault',
  4: 'serviceDetail.pp_state_shutdown'
}) as any
const standByMap = reactive({
  0: 'serviceDetail.pp_unknown',
  1: 'serviceDetail.pp_attended',
  2: 'serviceDetail.pp_unattended'
}) as any
const stateOption = _.cloneDeep(stepOption)

let myChart: any
const echartRender = (chartId: string, option: any) => {
  myChart && myChart.dispose()
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  echartsArr.value = [myChart]
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  echartRender('stateCharts', stateOption)
}

const handleChangeType = (val: number) => {
  serviceType.value = val
  handleSearch()
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

watch(
  () => locale.value,
  (newValue, oldValue) => {
    handleSearch(false)
  }
)

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = async (update = true) => {
  if (Number(form.value.end_time) - Number(form.value.start_time) > 1000 * 60 * 60 * 24) {
    ElMessage.warning(t('serviceDetail.pp_limit_warn'))
  } else {
    if (serviceType.value == 1) {
      form.value.data_id = stateMachineMap1[currentProject.value.project]
    } else {
      form.value.data_id = stateMachineMap2[currentProject.value.project]
    }
    if (update) {
      const queryForm = {} as any
      queryForm.search_start_time = form.value.start_time
      queryForm.search_end_time = form.value.end_time
      queryForm.serviceType = serviceType.value
      router.push({
        path: location.pathname,
        query: { ...route.query, ...queryForm }
      })
    }
    loading.value = true
    try {
      const res = await apiGetStateMachineList(form.value, currentProject.value.project, props.deviceId)
      loading.value = false
      if (res.err_code) {
        ElMessage.error(res.message)
      } else {
        if (res.data && res.data.length > 0) {
          isEmpty.value = false
          stateOption.xAxis.data = res.data.map((item: any) => formatTime(item.timestamp))
          if (serviceType.value == 1) {
            const dataID = stateMachineMap1[currentProject.value.project]
            stateOption.series[0].data = res.data.map((item: any) => {
              if (![1, 2, 3, 4].includes(item[dataID])) {
                return t('serviceDetail.pp_unknown')
              } else {
                return t(statusMap[item[dataID]])
              }
            })
          } else {
            const dataID = stateMachineMap2[currentProject.value.project]
            stateOption.series[0].data = res.data.map((item: any) => {
              if (![0, 1, 2].includes(item[dataID])) {
                return t('serviceDetail.pp_unknown')
              } else {
                return t(standByMap[item[dataID]])
              }
            })
          }
          nextTick(() => setCharts())
        } else {
          isEmpty.value = true
        }
      }
    } catch (error) {
      loading.value = false
    }
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  let initParams: any = route.query
  form.value.start_time = Number(initParams.start_time)
  form.value.end_time = initParams.end_time == 0 ? Number(initParams.start_time) + 1000 * 60 * 5 : Number(initParams.end_time)
  datePicker.value = [form.value.start_time, form.value.end_time]
  handleSearch()
}

onBeforeMount(() => {
  let initParams: any = route.query
  if (initParams.search_start_time && initParams.search_end_time) {
    form.value.start_time = Number(initParams.search_start_time)
    form.value.end_time = Number(initParams.search_end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  } else {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = initParams.end_time == 0 ? Number(initParams.start_time) + 1000 * 60 * 5 : Number(initParams.end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  }
  serviceType.value = initParams.serviceType ? Number(initParams.serviceType) : 1
  handleSearch(false)
})
</script>

<style lang="scss" scoped>
.state-machine-container {
  .machine-header {
    width: 100%;
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .tabs-box {
      .left1 {
        left: 4px;
      }
      .left2 {
        left: 70px;
      }
      .left3 {
        left: 126px;
      }
    }
  }
  :deep(.el-form) {
    .el-form-item {
      margin: 0;
      display: inline-flex;
      align-items: center;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
        align-items: center;
      }
    }
  }
}
</style>
