<template>
  <div class="di-recording-container">
    <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-16">
      <div class="tabs-box">
        <div class="tab-item" :class="{ ac: searchTab == 'platform' }" @click="changeRadio('platform')">{{ $t('serviceDetail.pp_platform_side') }}</div>
        <div class="tab-item" :class="{ ac: searchTab == 'battery' }" @click="changeRadio('battery')">{{ $t('serviceDetail.pp_battery_side') }}</div>
        <div :style="{ width: locale == 'zh' ? '66px' : searchTab == 'platform' ? '116px' : '106px' }" class="bg" :class="{ left1: searchTab == 'platform', left2: searchTab == 'battery' && locale == 'zh', left3: searchTab == 'battery' && locale == 'en' }"></div>
      </div>
      <el-button @click="downloadCsv(ruleFormRef)" class="welkin-secondary-button">{{ $t('serviceDetail.pp_full_csv_download') }}</el-button>
    </div>

    <el-form ref="ruleFormRef" :model="searchForm" :rules="rules" inline label-position="left" hide-required-asterisk :class="formClass">
      <el-form-item prop="pl_step_num" :label="$t('serviceDetail.pp_platform_step')" v-if="searchTab == 'platform'">
        <el-select v-model="searchForm.pl_step_num" clearable filterable class="width-full" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in step_axis.platform_step_options" :key="item.value" :label="$t(`plcRecord.platformStep.${project.version}.${item.value}`)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="bc_step_num" :label="$t('serviceDetail.pp_battery_step')" v-if="searchTab != 'platform'">
        <el-select v-model="searchForm.bc_step_num" clearable filterable class="width-full" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in step_axis.battery_step_options" :key="item.value" :label="$t(`plcRecord.batteryStep.${project.version}.${item.value}`)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="varname" :label="$t('serviceDetail.pp_data_point_name')">
        <el-select v-model="searchForm.varname" multiple clearable filterable collapse-tags collapse-tags-tooltip class="width-full" @change="onChangeAxis" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in step_axis.axis_options" :key="item.value" :label="$t(`plcRecord.${enNameMap[project.version]}.${item.value}`)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="speedSearch(ruleFormRef)" class="welkin-primary-button" :loading="loading">
          {{ $t('common.pp_search') }}
        </el-button>
        <el-button @click="resetSearch(ruleFormRef)" class="welkin-secondary-button" style="margin-left: 8px">
          {{ $t('common.pp_reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <div v-show="!showImage && !dataEmpty">
      <el-empty :description="loading ? $t('common.pp_loading') : $t('serviceDetail.pp_di_empty')" />
    </div>

    <div v-show="!showImage && dataEmpty">
      <el-empty :description="$t('common.pp_empty')" />
    </div>

    <div v-show="showImage">
      <PlcChartGroup :data="chartData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeMount, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { apiGetDi } from '~/apis/plc-record'
import { formatLocaleDate, getChartGroupData, platformStepRules, batteryStepRules, dataPointRules } from '~/utils'
import { useStore } from 'vuex'
import { di_varname, di_varname_pus4, di_varname_firefly1 } from '~/constvars/plc-record/di'
import { battery_step, platform_step } from '~/constvars/plc-record/step'
import PlcChartGroup from '../../plc-chart-group/index.vue'
import * as XLSX from 'xlsx'

// 国际化切换时改变默认筛选项
const { locale } = useI18n({ useScope: 'global' })
// const { t } = useI18n();
const props = defineProps(['deviceId', 'serviceId'])

// 监听路由
const $route = useRoute()
// 点击搜索按钮，展示echarts图像
const $router = useRouter()
const $store = useStore()

const project = ref(computed(() => $store.state.project))
const service_detail = ref(computed(() => $store.state.service_detail))
const formClass = computed(() => {
  if (locale.value == 'zh') {
    if (loading.value) {
      return 'zh-loading-form'
    } else {
      return 'zh-unloading-form'
    }
  } else {
    if (loading.value) {
      return 'en-loading-form'
    } else {
      return 'en-unloading-form'
    }
  }
})

const chartData = ref([] as any)
const varnameMap = {
  1: di_varname,
  2: di_varname,
  3: di_varname,
  4: di_varname_pus4,
  7: di_varname_firefly1
} as any
const enNameMap = {
  1: 'diVarname',
  2: 'diVarname',
  3: 'diVarname',
  4: 'diVarnamePus4',
  7: 'diVarnameFirefly1'
} as any

interface query {
  step_num?: number | string
  pl_step_num: number | string
  bc_step_num: number | string
  varname: string[]
  start_time?: number | string
  end_time?: number | string
  service_id: string
}

const loading = ref(false)
const step_axis = ref<any>({
  platform_step_options: [] as any[],
  battery_step_options: [] as any[],
  axis_options: [] as any[],
  axis: {}
})
const searchTab = ref<any>('platform')
const searchForm: query = reactive({
  pl_step_num: '' as number | string,
  bc_step_num: '' as number | string,
  varname: [],
  start_time: '' as number | string,
  end_time: '' as number | string,
  service_id: ''
})

const rules = reactive<FormRules>({
  pl_step_num: [
    {
      validator: platformStepRules,
      required: true,
      trigger: 'change'
    }
  ],
  bc_step_num: [
    {
      validator: batteryStepRules,
      required: true,
      trigger: 'change'
    }
  ],
  varname: [
    {
      validator: dataPointRules,
      required: true,
      trigger: 'change'
    }
  ]
})
const ruleFormRef = ref<FormInstance>()
const showImage = ref(false)
const dataEmpty = ref(false)
const groupOption = ref(false)

// “所有点”和其他不能同时选择
const onChangeAxis = (val: string[]) => {
  if (val[val.length - 1] == 'all') {
    searchForm.varname = ['all']
  } else if (val[0] == 'all' && val.length > 1) {
    searchForm.varname = val.slice(1, val.length)
  }
}

// 重置筛选项
const resetSearch = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  groupOption.value = false
}

onBeforeRouteLeave(() => {
  // 清空上次图表数据
  resetSearch(ruleFormRef.value)

  showImage.value = false
  dataEmpty.value = false
})

// 筛选项平台侧或电池仓
const changeRadio = (val: string) => {
  searchTab.value = val
  searchForm.pl_step_num = ''
  searchForm.bc_step_num = ''
  searchForm.varname = []

  setTimeout(() => {
    ruleFormRef.value?.clearValidate(['varname'])
  }, 0)
}

//watch监听动态拿到值的变化,从而做出反应
watch(
  () => service_detail.value.service_start_time,
  (newVal, oldVal) => {
    if ($route.query.plc == 'di' && !!$route.query.axis) {
      speedSearch(ruleFormRef.value)
    }
  }
)
watch(
  () => locale.value,
  (newValue, oldValue) => speedSearch(ruleFormRef.value)
)

onBeforeMount(() => {
  let newPath = $router.currentRoute.value.path
  resetSearch(ruleFormRef.value)
  if ($route.query.plc == 'di' && !!$route.query.axis) {
    searchForm.pl_step_num = $route.query.pl_step_num ? String($route.query.pl_step_num) : ''
    searchForm.bc_step_num = $route.query.bc_step_num ? String($route.query.bc_step_num) : ''
    searchForm.varname = String($route.query.axis).split(',')
    searchTab.value = $route.query.pl_step_num ? 'platform' : 'battery'
  }

  step_axis.value.axis = varnameMap[project.value.version]
  let options = []
  let obj = platform_step[project.value.version]
  options.push({ label: '所有步骤号', value: '-1' })
  for (let i in obj) {
    options.push({ label: obj[i], value: i })
  }
  step_axis.value.platform_step_options = options

  options = []
  obj = battery_step[project.value.version]
  options.push({ label: '所有步骤号', value: '-1' })
  for (let i in obj) {
    options.push({ label: obj[i], value: i })
  }
  step_axis.value.battery_step_options = options

  options = []
  obj = step_axis.value.axis
  options.push({ label: '所有点', value: 'all' })
  for (let i in obj) {
    options.push({ label: obj[i], value: i })
  }
  step_axis.value.axis_options = options
})

const downloadCsv = async (formEl: FormInstance | undefined) => {
  if(!showImage.value) return
  const date = new Date().getTime()
  const step = searchTab.value == 'platform' ? searchForm.pl_step_num : searchForm.bc_step_num

  let step_name = ''
  if (step == -1) {
    step_name = '所有步骤号'
  } else {
    step_name = searchTab.value == 'platform' ? platform_step[project.value.version][step] : battery_step[project.value.version][step]
  }
  const title = `welkin_${date}_di_${step}` + '.xlsx'
  const len = chartData.value?.time?.length
  const groupData = chartData.value?.group[0]?.data
  let aoa = [['时间', '步骤号', ...chartData.value?.legend]]
  for (let i = 0; i < len; i++) {
    let arr = []
    // 插入时间
    arr.push(chartData.value?.time[i])
    // 插入步骤号名
    arr.push(step_name)

    for (let j = 0; j < groupData?.length; j++) {
      arr.push(groupData[j].data[i])
    }
    aoa.push(arr)
  }
  // 创建工作表
  const tdata = XLSX.utils.aoa_to_sheet(aoa)
  // 创建工作簿
  const wb = XLSX.utils.book_new()
  // 将工作表放入工作簿中
  XLSX.utils.book_append_sheet(wb, tdata, 'Sheet1')
  // 生成文件并下载
  XLSX.writeFile(wb, title)
}

const speedSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  await formEl.validate((valid, fields) => {
    if (valid) {
      let query = {
        pl_step_num: searchForm.pl_step_num,
        bc_step_num: searchForm.bc_step_num,
        axis: searchForm.varname.toString()
      }

      $router.push({
        path: $router.currentRoute.value.path,
        query: { ...$route.query, ...query }
      })

      loading.value = true
      // obj.row -> service_detail.value
      if (service_detail.value.service_end_time === '-') {
        delete searchForm.end_time
      } else {
        const endTime = service_detail.value.service_end_time
        searchForm.end_time = new Date(endTime).getTime()
      }
      if (service_detail.value.service_start_time === '-') {
        delete searchForm.start_time
      } else {
        const startTime = service_detail.value.service_start_time
        searchForm.start_time = new Date(startTime).getTime()
      }
      searchForm.service_id = service_detail.value.service_id

      return apiGetDi(project.value.project, props.deviceId, searchForm)
        .then((res) => {
          loading.value = false
          if (Object.keys(res.data).length === 0) {
            showImage.value = false
            dataEmpty.value = true
          } else {
            let varname_arr = [...searchForm.varname]

            if (varname_arr.indexOf('all') != -1) {
              varname_arr = Object.keys(step_axis.value.axis)
            }
            let testdata = getChartGroupData(res.data, varname_arr, step_axis.value.axis, ['value'], 'di', `plcRecord.${enNameMap[project.value.version]}`, true)
            chartData.value = testdata
            showImage.value = true
          }
        })
        .catch((err) => {
          loading.value = false
          dataEmpty.value = true
        })
    }
  })
}

// };
</script>

<style lang="scss" scoped>
.di-recording-container {
  .tabs-box {
    .left1 {
      left: 4px;
    }
    .left2 {
      left: 70px;
    }
    .left3 {
      left: 120px;
    }
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  :deep(.el-form) {
    margin-bottom: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr 128px;
    column-gap: 24px;
    .el-form-item {
      margin: 0;
      display: inline-flex;
      align-items: center;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
        align-items: center;
      }
      .el-select .el-select__tags .el-tag--info {
        color: #262626;
      }
    }
  }
}
</style>
