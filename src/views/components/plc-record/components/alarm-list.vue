<template>
  <div class="service-alarm-container">
    <el-form :model="form">
      <el-form-item :label="$t('alarmList.pp_alarm_time')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_alarm_description')">
        <el-select v-model="form.data_id" multiple collapse-tags collapse-tags-tooltip clearable filterable remote :placeholder="$t('alarmList.pp_keywords')" :remote-method="remoteMethod" :loading="remoteLoading" class="width-full">
          <el-option v-for="item in pointOptions" :key="item.value" :value="item.value" :label="item.label + '-' + item.value">
            <span style="float: left">{{ item.label }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.value }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_alarm_level')">
        <el-select v-model="form.alarm_level" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_battery_id')" v-if="form.alarm_type == 2">
        <el-input v-model="form.battery_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
      </el-form-item>
      <el-form-item :style="{ gridColumn: form.alarm_type == 2 ? 'span 2' : 'span 3' }">
        <span style="color: #595959">{{ $t('alarmList.pp_status') }}</span>
        <el-select v-model="form.state" clearable filterable @visible-change="handleStateVisibleChange" :class="[form.state !== '' || stateShow ? 'long-select' : 'short-select', 'remove-border']" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in stateOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
        <span class="margin_l-40" style="color: #595959">{{ $t('alarmList.pp_type') }}</span>
        <el-select v-model="form.alarm_type" clearable filterable @change="handleChangeAlarmType" @visible-change="handleAlarmTypeVisibleChange" :class="[form.alarm_type !== '' || alarmTypeShow ? 'long-select' : 'short-select', 'remove-border']" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in alarmTypeOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
        <el-button @click="handleSearch" class="welkin-primary-button margin_l-20" :loading="loading">
          {{ $t('common.pp_search') }}
        </el-button>
        <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">
          {{ $t('common.pp_reset') }}
        </el-button>
        <el-button @click="handleDownload" class="welkin-secondary-button width-108 height-32" style="padding: 5px 16px; margin-left: auto">
          <DownloadIcon />
          <span class="margin_l-4">{{ $t('alarmList.pp_alarm_download') }}</span>
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <div class="flex-box flex_a_i-center width-full">
            <span class="ellipse">{{ scope.row.data_id_description ? scope.row.data_id_description : '-' }}</span>
            <el-popover :width="520" placement="top" v-if="scope.row.servo_fault_list">
              <template #reference>
                <AlarmIcon class="margin_l-6" />
              </template>
              <el-table :data="scope.row.servo_fault_list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }">
                <el-table-column prop="id" :label="$t('alarmList.pp_fault_id')" min-width="120" show-overflow-tooltip />
                <el-table-column prop="code" :label="$t('alarmList.pp_native_code')" min-width="120" show-overflow-tooltip />
                <el-table-column prop="real_code" :label="$t('alarmList.pp_real_code')" min-width="120" show-overflow-tooltip />
                <el-table-column prop="code_name" :label="$t('alarmList.pp_fault_name')" min-width="120" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span>{{ row.code_name || '-' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="130" show-overflow-tooltip />
      <el-table-column prop="alarm_type" :label="$t('alarmList.pp_alarm_type')" min-width="130" show-overflow-tooltip>
        <template #default="scope">
          {{ $t(alarmTypeMap[scope.row.alarm_type]) }}
        </template>
      </el-table-column>
      <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="130" show-overflow-tooltip>
        <template #default="scope">
          <span class="alarm-level-container" v-if="alarmLevelMap[scope.row.alarm_level]" :style="{ color: alarmLevelMap[scope.row.alarm_level].color, background: alarmLevelMap[scope.row.alarm_level].background }">
            {{ $t(alarmLevelMap[scope.row.alarm_level].name) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" min-width="130" show-overflow-tooltip>
        <template #default="scope">
          <div class="alarm-status-container" :style="{ background: alarmStatusMap[scope.row.state].background }">
            <ClearIcon v-if="scope.row.state == 1" />
            <CreateIcon v-else-if="scope.row.state == 2" />
            <UnknownIcon v-else />
            <span>{{ $t(alarmStatusMap[scope.row.state].name) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatTime(scope.row.create_ts) }}
        </template>
      </el-table-column>
      <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="180" show-overflow-tooltip v-if="searchForm.alarm_type != 3">
        <template #default="scope">
          {{ formatTime(scope.row.clear_ts) }}
        </template>
      </el-table-column>
      <el-table-column prop="battery_id" :label="$t('alarmList.pp_battery_id')" min-width="260" v-if="searchForm.alarm_type == 2">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.battery_id" />
        </template>
      </el-table-column>
    </el-table>

    <div class="common-pagination" style="margin: 0; padding: 20px 0; background-color: #fff">
      <Page :page="pages" @change="handlePageChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount, shallowRef, h } from 'vue';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import { page } from '~/constvars/page';
import { alarmLevelMap, alarmStatusMap } from './constant';
import { getDisabledDate, formatTime, clearJson, removeNullProp, getShortcuts } from '~/utils';
import { apiGetAllAlarmList, apiGetPointList, apiDownloadAlarm } from '~/apis/alarm-list';
import { debounce } from 'lodash-es';
import ClearIcon from './icon/alarm-clear.vue';
import CreateIcon from './icon/alarm-create.vue';
import UnknownIcon from './icon/alarm-unknown.vue';
import DownloadIcon from './icon/download-icon.vue';
import AlarmIcon from '~/assets/svg/alarm-view.vue';
import TimeRangeIcon from '~/assets/svg/time-range.vue';
import DatePrefix from '~/assets/svg/date-prefix.vue';
import _ from 'lodash';

const props = defineProps({
  deviceId: {
    type: String,
    default: '',
  },
});

const customPrefix = shallowRef({
  render() {
    return h(DatePrefix);
  },
});

const { t } = useI18n();
const store = useStore();
const route = useRoute();
const router = useRouter();
const project = ref(computed(() => store.state.project));
const { locale } = useI18n({ useScope: 'global' });

const pages = ref(_.cloneDeep(page));
const loading = ref(false);
const remoteLoading = ref(false);
const stateShow = ref(false);
const alarmTypeShow = ref(false);
const datePicker = ref([] as any);
const list = ref([] as any);
const alarmTypeOptions = ref([
  {
    label: 'alarmList.pp_basic_alarm',
    value: 1,
  },
  {
    label: 'alarmList.pp_battery_alarm',
    value: 2,
  },
  {
    label: 'alarmList.pp_unknown_alarm',
    value: 3,
  },
]);
const alarmLevelOptions = ref([
  {
    label: 'alarmList.pp_first_level',
    value: 1,
  },
  {
    label: 'alarmList.pp_second_level',
    value: 2,
  },
  {
    label: 'alarmList.pp_third_level',
    value: 3,
  },
]);
const stateOptions = ref([
  {
    label: 'alarmList.pp_cleared',
    value: 1,
  },
  {
    label: 'alarmList.pp_alarming',
    value: 2,
  },
  {
    label: 'alarmList.pp_unknown',
    value: 3,
  },
]);
const pointOptions = ref([] as any);
const alarmTypeMap = ref({
  1: 'alarmList.pp_basic_alarm',
  2: 'alarmList.pp_battery_alarm',
  3: 'alarmList.pp_unknown_alarm',
} as any);
const form = ref({
  start_time: '' as number | string,
  end_time: '' as number | string,
  alarm_type: '' as number | string,
  alarm_level: '' as number | string,
  state: '' as number | string,
  data_id: [] as any,
  battery_id: '',
});
const searchForm = ref({
  start_time: '' as number | string,
  end_time: '' as number | string,
  alarm_type: '' as number | string,
  alarm_level: '' as number | string,
  state: '' as number | string,
  data_id: [] as any,
  battery_id: '',
});

const handleStateVisibleChange = (visible: boolean) => {
  stateShow.value = visible;
};

const handleAlarmTypeVisibleChange = (visible: boolean) => {
  alarmTypeShow.value = visible;
};

/**
 * @description: 时间
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  form.value.start_time = new Date(value[0]).getTime();
  form.value.end_time = new Date(value[1]).getTime();
};

/**
 * @description: 清空电池ID
 * @return {*}
 */
const handleChangeAlarmType = () => {
  form.value.battery_id = '';
};

/**
 * @description: 远程搜索告警描述点
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchPointList(query);
};
const searchPointList = debounce(async (val: any) => {
  if (val && val.length > 0) {
    remoteLoading.value = true;
    const params = typeof val == 'string' ? { description: val } : { data_ids: val.join(',') };
    const res = await apiGetPointList(params, project.value.project);
    pointOptions.value = [];
    Object.keys(res.data).forEach((key: any) => {
      pointOptions.value.push({
        label: res.data[key],
        value: key,
      });
    });
    remoteLoading.value = false;
  }
}, 500);

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  pointOptions.value = [];
  clearJson(form.value);
  let initParams: any = route.query;
  form.value.start_time = Number(initParams.start_time);
  form.value.end_time = initParams.end_time == 0 ? Number(initParams.start_time) + 1000 * 60 * 5 : Number(initParams.end_time);
  datePicker.value = [form.value.start_time, form.value.end_time];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  searchForm.value = { ...form.value };
  getList(true);
};

/**
 * @description: 获取数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  if (loading.value) return;
  let formData = {} as any;
  formData = { ...searchForm.value };
  formData.data_id = formData.data_id.join(',');
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.descending = true;
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: {
        ...removeNullProp(formData),
        start_time: route.query.start_time,
        end_time: route.query.end_time,
        alarm_start_time: form.value.start_time,
        alarm_end_time: form.value.end_time,
        plc: 'alarm_list',
      },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetAllAlarmList({ ...formData, device_id: props.deviceId }, project.value.project);
    list.value = res.data;
    pages.value.total = res.total;
  } catch (error) {}
  loading.value = false;
};

/**
 * @description: 下载告警数据
 * @return {*}
 */
const handleDownload = async () => {
  let formData = {} as any;
  formData = { ...searchForm.value };
  formData.data_id = formData.data_id.join(',');
  formData.descending = true;
  formData.download = true;
  formData.device_id = props.deviceId;
  removeNullProp(formData);
  await apiDownloadAlarm(formData, project.value.project);
};

watch(
  () => locale.value,
  (newValue, oldValue) => {
    getList(false);
    searchPointList(form.value.data_id);
  }
);

const initWeb = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query;
      if (initParams.alarm_start_time && initParams.alarm_end_time) {
        form.value.start_time = Number(initParams.alarm_start_time);
        form.value.end_time = Number(initParams.alarm_end_time);
        datePicker.value = [form.value.start_time, form.value.end_time];
      } else {
        form.value.start_time = Number(initParams.start_time);
        form.value.end_time = initParams.end_time == 0 ? Number(initParams.start_time) + 1000 * 60 * 5 : Number(initParams.end_time);
        datePicker.value = [form.value.start_time, form.value.end_time];
      }
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
      form.value.alarm_type = !!initParams.alarm_type ? Number(initParams.alarm_type) : '';
      form.value.alarm_level = !!initParams.alarm_level ? Number(initParams.alarm_level) : '';
      form.value.state = !!initParams.state ? Number(initParams.state) : '';
      form.value.data_id = !!initParams.data_id ? initParams.data_id.split(',') : [];
      form.value.battery_id = !!initParams.battery_id ? initParams.battery_id : '';
      searchForm.value = { ...form.value };
      if (form.value.data_id.length > 0) searchPointList(form.value.data_id);
      getList(false);
    }
  },
  { immediate: true }
);

onBeforeMount(() => {
  initWeb();
});

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList(true);
};
</script>

<style lang="scss" scoped>
.service-alarm-container {
  font-family: 'Blue Sky Standard';
  :deep(.el-form) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px 24px;
    margin-bottom: 20px;
    background: #fff;
    .el-input__inner {
      color: #262626;
    }
    .el-form-item {
      margin: 0;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
      }
      .el-select .el-select__tags .el-tag--info {
        color: #262626;
      }
    }
    .el-form-item__content {
      white-space: nowrap;
      align-items: normal;
      flex-wrap: nowrap;
    }
  }
  :deep(.long-select) {
    .el-input__wrapper {
      width: 100px;
    }
  }
  :deep(.short-select) {
    .el-input__wrapper {
      width: 16px;
    }
  }
  :deep(.remove-border) {
    .el-input__wrapper {
      box-shadow: none !important;
    }
    .el-input.is-focus .el-input__wrapper {
      box-shadow: none !important;
    }
  }
  :deep(.el-range-editor.el-input__wrapper) {
    padding: 0;
  }
  :deep(.el-date-editor .el-range__icon) {
    margin-right: 4px;
  }
  .alarm-level-container {
    display: inline-block;
    height: 26px;
    padding: 2px 8px;
    border-radius: 2px;
  }
  .alarm-status-container {
    height: 24px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 2px 10px;
    margin-top: 2px;
    border-radius: 14px;
    font-size: 13px;
    line-height: 20px;
    color: #fff;
  }
}
</style>
