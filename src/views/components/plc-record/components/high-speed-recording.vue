<template>
  <div class="high-speed-container">
    <div class="tabs-wrapper">
      <div class="tabs-box">
        <div class="tab-item" :class="{ ac: searchTab == 'platform' }" @click="changeRadio('platform')">{{ $t('serviceDetail.pp_platform_side') }}</div>
        <div class="tab-item" :class="{ ac: searchTab == 'battery' }" @click="changeRadio('battery')">{{ $t('serviceDetail.pp_battery_side') }}</div>
        <div :style="{ width: locale == 'zh' ? '66px' : searchTab == 'platform' ? '116px' : '106px' }" class="bg" :class="{ left1: searchTab == 'platform', left2: searchTab == 'battery' && locale == 'zh', left3: searchTab == 'battery' && locale == 'en' }"></div>
      </div>
    </div>

    <el-form ref="ruleFormRef" :model="searchForm" :rules="rules" inline label-position="left" hide-required-asterisk :class="formClass">
      <el-form-item prop="pl_step_num" :label="$t('serviceDetail.pp_platform_step')" v-if="searchTab == 'platform'">
        <el-select v-model="searchForm.pl_step_num" @change="onChangeStep(true)" multiple clearable filterable collapse-tags collapse-tags-tooltip class="width-full" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in high_speed[searchTab].step_options" :key="item.value" :label="$t(`plcRecord.platformStep.${project.version}.${item.value}`)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="bc_step_num" :label="$t('serviceDetail.pp_battery_step')" v-if="searchTab != 'platform'">
        <el-select v-model="searchForm.bc_step_num" @change="onChangeStep(true)" multiple clearable filterable collapse-tags collapse-tags-tooltip class="width-full" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in high_speed[searchTab].step_options" :key="item.value" :label="$t(`plcRecord.batteryStep.${project.version}.${item.value}`)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="axis" :label="$t('serviceDetail.pp_axis_name')">
        <el-select v-model="searchForm.axis" multiple clearable filterable collapse-tags collapse-tags-tooltip class="width-full" :placeholder="$t('common.pp_please_select')">
          <el-option-group v-for="group in high_speed[searchTab].axis_options" :key="group.label" :label="group.label ? $t(group.label) : ''">
            <el-option v-for="item in group.options" :key="item.value" :label="$t(`plcRecord.highSpeed.${project.version}.${item.value}`)" :value="item.value" :disabled="item.disabled" />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="speedSearch(ruleFormRef)" class="welkin-primary-button" :loading="loading" :disabled="isDuringRestoring">
          {{ $t('common.pp_search') }}
        </el-button>
        <el-button @click="resetSearch(ruleFormRef)" class="welkin-secondary-button" style="margin-left: 8px" :disabled="isDuringRestoring">
          {{ $t('common.pp_reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <div v-show="!showImage && !dataEmpty">
      <el-empty v-if="isArchived" :description="emptyDescription">
        <template #image>
          <ArchivedIcon />
        </template>
        <template #description>
          <div class="description-container">
            <span>{{ emptyDescription }}</span>
            <span class="restore-link" @click="restorePlc">{{ $t('common.pp_restore') }}</span>
          </div>
        </template>
      </el-empty>
      <el-empty v-else-if="isRestoring" :description="emptyDescription">
        <template #image>
          <RestoringIcon />
        </template>
      </el-empty>
      <el-empty v-else :description="emptyDescription" />
    </div>

    <div v-show="!showImage && dataEmpty">
      <el-empty v-if="isArchived">
        <template #image>
          <ArchivedIcon />
        </template>
        <template #description>
          <div class="description-container">
            <span>{{ $t('serviceDetail.pp_speed_archived') }}</span>
            <span class="restore-link" @click="restorePlc">{{ $t('common.pp_restore') }}</span>
          </div>
        </template>
      </el-empty>
      <el-empty v-else-if="isRestoring" :description="$t('serviceDetail.pp_speed_restoring')">
        <template #image>
          <RestoringIcon />
        </template>
      </el-empty>
      <el-empty v-else :description="$t('common.pp_empty')" />
    </div>

    <div v-show="showImage">
      <PlcChartGroup :data="chartData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeMount, computed } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import { apiGetHighSpeed, apiRestorePlc } from '~/apis/plc-record';
import { getChartGroupData, platformStepRules, batteryStepRules, axisRules } from '~/utils';
import { high_speed_dict } from '~/constvars/plc-record/high-speed-recording';
import { useStore } from 'vuex';
import { ServiceDetailConfig } from '~/constvars/plc-record/high-speed-recording/data';
import PlcChartGroup from '../../plc-chart-group/index.vue';
import ArchivedIcon from '~/assets/svg/archived.vue';
import RestoringIcon from '~/assets/svg/restoring.vue';

const { locale } = useI18n({ useScope: 'global' });
const { t } = useI18n();
const props = defineProps(['deviceId', 'serviceId', 'status', 'isRestored']);

const $router = useRouter();
const $route = useRoute();
const $store = useStore();
const project = ref(computed(() => $store.state.project));
const service_detail = ref(computed(() => $store.state.service_detail));
const formClass = computed(() => {
  if (locale.value == 'zh') {
    if (loading.value) {
      return 'zh-loading-form';
    } else {
      return 'zh-unloading-form';
    }
  } else {
    if (loading.value) {
      return 'en-loading-form';
    } else {
      return 'en-unloading-form';
    }
  }
});

const isArchived = computed(() => props.status === 'archived' && !restoring.value);
const isRestoring = computed(() => props.status === 'restoring' || restoring.value);

const emptyDescription = computed(() => {
  if (loading.value) {
    return t('common.pp_loading');
  } else if (isArchived.value) {
    return t('serviceDetail.pp_speed_archived');
  } else if (isRestoring.value) {
    return t('serviceDetail.pp_speed_restoring');
  } else {
    return t('serviceDetail.pp_speed_empty');
  }
});

interface query {
  step_num?: number | string;
  pl_step_num: number[] | string[];
  bc_step_num: number[] | string[];
  axis: number[] | string[];
  start_time?: number | string;
  end_time?: number | string;
  service_id: string;
}

const loading = ref(false);
const restoring = ref(false);

const isDuringRestoring = computed(() => props.status === 'restoring' || restoring.value || props.status === 'archived');

const high_speed = ref<ServiceDetailConfig>({
  platform: { step: {}, axis: {}, step_axis: {} },
  battery: { step: {}, axis: {}, step_axis: {} },
}) as any;
const searchTab = ref('platform');
const plStepNumMem = ref(computed(() => $store.state.fold.plStepNum));
const bcStepNumMem = ref(computed(() => $store.state.fold.bcStepNum));
const axisMem = ref(computed(() => $store.state.fold.axis));
const tabMem = ref(computed(() => $store.state.fold.searchTab));
searchTab.value = tabMem.value;
const searchForm: query = reactive({
  pl_step_num: plStepNumMem.value,
  bc_step_num: bcStepNumMem.value,
  axis: axisMem.value,
  start_time: '' as number | string,
  end_time: '' as number | string,
  service_id: '',
});

const rules = reactive<FormRules>({
  pl_step_num: [
    {
      validator: platformStepRules,
      required: true,
      trigger: 'change',
    },
  ],
  bc_step_num: [
    {
      validator: batteryStepRules,
      required: true,
      trigger: 'change',
    },
  ],
  axis: [
    {
      validator: axisRules,
      required: true,
      trigger: 'change',
    },
  ],
});
const ruleFormRef = ref<FormInstance>();
const showImage = ref(false);
const dataEmpty = ref(false);
const groupOption = ref(false);

// 重置筛选项
const resetSearch = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  searchForm.bc_step_num = [];
  searchForm.pl_step_num = [];
  searchForm.axis = [];
  groupOption.value = false;
};

onBeforeRouteLeave(() => {
  // 清空上次图表数据
  resetSearch(ruleFormRef.value);

  showImage.value = false;
  dataEmpty.value = false;
});

const onChangeStep = (flag: Boolean) => {
  const currentTab = searchTab.value;
  const data = high_speed.value[searchTab.value];

  let stepArr = searchTab.value == 'platform' ? searchForm.pl_step_num : searchForm.bc_step_num;

  const step = stepArr[0];

  const step_axis_pos: Array<number> = data.step_axis[step];

  if (Object.prototype.toString.call(step_axis_pos) === '[object Array]' && step_axis_pos.length > 0) {
    let step_related = [];
    let others = [];

    for (let pos in data.axis) {
      let tmp = {
        label: data.axis[pos],
        value: pos,
      };

      if (step_axis_pos.includes(Number(pos))) {
        step_related.push(tmp);
      } else {
        others.push(tmp);
      }
    }

    const axis_options = [
      // label 是i18的字段名
      // { label: data.step[step], options: step_related },
      {
        label: `plcRecord.${searchTab.value}Step.${project.value.version}.${step}`,
        options: step_related,
      },
      { label: 'plcRecord.otherAxis', options: others },
    ];
    high_speed.value[currentTab].axis_options = axis_options;
  } else {
    let axis_options = [];
    for (let pos in data.axis) {
      let tmp = {
        label: data.axis[pos],
        value: pos,
      };
      axis_options.push(tmp);
    }
    // 步骤号清空
    if (stepArr.length == 0) {
      high_speed.value[currentTab].axis_options = [{ label: '', options: axis_options }];
    } else {
      // 步骤号不为空,但是没有对应的轴号
      high_speed.value[currentTab].axis_options = [
        {
          value: step,
          label: data.step[step],
          options: [
            {
              label: 'plcRecord.noAxis',
              value: 'noAxis',
              disabled: true,
            },
          ],
        },
        { label: 'plcRecord.otherAxis', options: axis_options },
      ];
    }
  }
  // search 轴号为空
  if (flag) searchForm.axis = [];
};
// 筛选项平台侧或电池仓
const changeRadio = (val: string) => {
  searchTab.value = val;
  searchForm.pl_step_num = [];
  searchForm.bc_step_num = [];
  searchForm.axis = [];

  setTimeout(() => {
    ruleFormRef.value?.clearValidate(['axis']);
  }, 0);
};

//watch监听动态拿到值的变化,从而做出反应
watch(
  () => service_detail.value.service_start_time,
  (newVal, oldVal) => {
    if ((!$route.query.plc || $route.query.plc == 'high_speed') && !!$route.query.axis) {
      speedSearch(ruleFormRef.value);
    }
  }
);

watch(
  () => locale.value,
  (newValue, oldValue) => speedSearch(ruleFormRef.value)
);

onBeforeMount(() => {
  let newPath = $router.currentRoute.value.path;
  resetSearch(ruleFormRef.value);

  if ((!$route.query.plc || $route.query.plc == 'high_speed') && !!$route.query.axis) {
    searchForm.pl_step_num = $route.query.pl_step_num ? String($route.query.pl_step_num).split(',') : [];
    searchForm.bc_step_num = $route.query.bc_step_num ? String($route.query.bc_step_num).split(',') : [];
    searchForm.axis = String($route.query.axis).split(',');
    searchTab.value = $route.query.pl_step_num ? 'platform' : 'battery';
  }

  const data = high_speed_dict[project.value.version];
  for (let i in data) {
    let step_options = [];
    let axis_options = [];
    for (let j in data[i]?.step) {
      step_options.push({ label: data[i].step[j], value: j });
    }
    for (let j in data[i]?.axis) {
      axis_options.push({ label: data[i].axis[j], value: j });
    }
    data[i] = {
      ...data[i],
      step_options: step_options,
      axis_options: [{ label: '', options: axis_options }],
    };
  }
  high_speed.value = data;
  onChangeStep(false);
});

// 点击搜索按钮，展示echarts图像

const chartData = ref([] as any);

// 恢复PLC功能
const restorePlc = () => {
  const query = {
    pl_step_num: searchForm.pl_step_num.join(','),
    bc_step_num: searchForm.bc_step_num.join(','),
    axis: searchForm.axis.toString(),
  };
  $router.push({
    path: $router.currentRoute.value.path,
    query: { ...$route.query, ...query },
  });
  loading.value = true;
  restoring.value = true;
  apiRestorePlc(project.value.project, props.deviceId, { service_id: service_detail.value.service_id, service_start_time: service_detail.value.service_start_time })
    .then((res) => {
      loading.value = false;
    })
    .catch((err) => {
      loading.value = false;
      restoring.value = false;
    });
};

const speedSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 如果经过校验，同步修改url
      let query = {
        pl_step_num: searchForm.pl_step_num.join(','),
        bc_step_num: searchForm.bc_step_num.join(','),
        axis: searchForm.axis.toString(),
      };

      $router.push({
        path: $router.currentRoute.value.path,
        query: { ...$route.query, ...query },
      });

      loading.value = true;
      // obj.row -> service_detail.value
      if (service_detail.value.service_end_time === '-') {
        delete searchForm.end_time;
      } else {
        const endTime = service_detail.value.service_end_time;
        searchForm.end_time = new Date(endTime).getTime();
      }
      if (service_detail.value.service_start_time === '-') {
        delete searchForm.start_time;
      } else {
        const startTime = service_detail.value.service_start_time;
        searchForm.start_time = new Date(startTime).getTime();
      }
      searchForm.service_id = service_detail.value.service_id;
      const stepArr = [];
      const stepTemp = searchForm.pl_step_num.sort((a: any, b: any) => a - b);
      const minData = Number(stepTemp[0]);
      const maxData = Number(stepTemp[stepTemp.length - 1]);
      for (let i = minData; i <= maxData; i++) {
        stepArr.push(i);
      }
      searchForm.pl_step_num = stepArr.map(String);

      const batteryArr = [];
      const batteryTemp = searchForm.bc_step_num.sort((a: any, b: any) => a - b);
      const minBatteryData = Number(batteryTemp[0]);
      const maxBatteryData = Number(batteryTemp[batteryTemp.length - 1]);
      for (let i = minBatteryData; i <= maxBatteryData; i++) {
        batteryArr.push(i);
      }
      searchForm.bc_step_num = batteryArr.map(String);

      // 轴号记忆
      $store.commit('fold/SET_STEPNUM', { ...searchForm, searchTab: searchTab.value });
      // 查看高速录播
      return apiGetHighSpeed(props.isRestored, project.value.project, props.deviceId, searchForm)
        .then((res) => {
          loading.value = false;
          if (!res.data || Object.keys(res.data).length === 0) {
            showImage.value = false;
            dataEmpty.value = true;
          } else {
            let step_axis = high_speed.value.platform.axis;
            if (searchTab.value === 'battery') {
              step_axis = high_speed.value.battery.axis;
            }
            let testdata = getChartGroupData(res.data, searchForm.axis, step_axis, ['torque', 'position', 'speed'], 'high_speed', `plcRecord.highSpeed.${project.value.version}`);
            console.log('testdata', JSON.stringify(testdata));
            showImage.value = true;
            chartData.value = testdata;
          }
        })
        .catch((err) => {
          loading.value = false;
          dataEmpty.value = true;
        });
    }
  });
};
</script>

<style lang="scss" scoped>
.high-speed-container {
  .tabs-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
  .tabs-box {
    .left1 {
      left: 4px;
    }
    .left2 {
      left: 70px;
    }
    .left3 {
      left: 120px;
    }
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  :deep(.el-form) {
    margin-bottom: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr 128px;
    column-gap: 24px;
    .el-form-item {
      margin: 0;
      display: inline-flex;
      align-items: center;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
        align-items: center;
      }
      .el-select .el-select__tags .el-tag--info {
        color: #262626;
      }
    }
  }
  :deep(.el-empty__description) {
    // max-width: 260px;
    margin: 20px auto 0;
    line-height: 24px;
    white-space: pre-line;
  }
  .description-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    span:first-child {
      font-size: 14px;
      font-family: 'Blue Sky Standard', sans-serif;
      color: #909399;
      line-height: inherit;
    }
  }
  .restore-link {
    color: #00bebe;
    font-weight: 500;
    cursor: pointer;
    font-size: 14px;
  }
}
</style>
