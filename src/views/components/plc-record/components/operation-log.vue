<template>
  <div class="operation-recording-container">
    <div class="operation-search-container">
      <div class="tabs-box" v-if="project.project == 'PUS3'" :style="{ marginBottom: searchForm.type == 1 ? '16px' : '0px' }">
        <div class="tab-item" :class="{ ac: searchForm.type == 1 }" @click="handleChangeType(1)">{{ $t('serviceDetail.pp_log_local') }}</div>
        <div class="tab-item" :class="{ ac: searchForm.type == 2 }" @click="handleChangeType(2)">{{ $t('serviceDetail.pp_log_remote') }}</div>
        <div :style="{ width: locale == 'zh' ? '52px' : searchForm.type == 1 ? '61px' : '77px' }" class="bg" :class="{ left1: searchForm.type == 1, left2: searchForm.type == 2 && locale == 'zh', left3: searchForm.type == 2 && locale == 'en' }"></div>
      </div>

      <el-form :model="searchForm" inline label-position="left" :class="formClass" v-if="searchForm.type == 1">
        <el-form-item :label="$t('serviceDetail.pp_operation_interface')" class="width-full">
          <el-input v-model="searchForm.operation_interface" :placeholder="$t('common.pp_please_input')" clearable>
            <template #prefix><SearchIcon /></template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('serviceDetail.pp_operation_description')" class="width-full">
          <el-input v-model="searchForm.operation_description" :placeholder="$t('common.pp_please_input')" clearable>
            <template #prefix><SearchIcon /></template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('serviceDetail.pp_operation_people')" class="width-full">
          <el-select v-model="searchForm.operator" clearable filterable remote :placeholder="$t('stuckAnalysis.pp_user_placeholder')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
            <template #prefix><SearchIcon /></template>
            <el-option v-for="item in userOptions" :key="item.employee_id" :value="item.worker_user_id" :label="item.name">
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.worker_user_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="filterEvent" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
          <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="operation-table-box">
      <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" v-if="searchForm.type == 1">
        <el-table-column prop="timestamp" :label="$t('serviceDetail.pp_operation_trigger_time')" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatLocaleDate(row.timestamp, false) }}
          </template>
        </el-table-column>
        <el-table-column prop="page" :label="$t('serviceDetail.pp_operation_interface')" min-width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.page || row.operation }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="action" :label="$t('common.pp_operation')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.action || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="args" :label="`${$t('serviceDetail.pp_input_parameters')}/${$t('serviceDetail.pp_return_data')}`" min-width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.args || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="project.project == 'PUS3'" prop="module" :label="$t('serviceDetail.pp_log_module')" min-width="100" show-overflow-tooltip />
        <el-table-column prop="button" :label="$t('serviceDetail.pp_operation_description')" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.button || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="user_id" :label="$t('serviceDetail.pp_operation_people')" min-width="160" show-overflow-tooltip />
      </el-table>

      <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" v-else>
        <el-table-column prop="timestamp" :label="$t('serviceDetail.pp_log_time')" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatLocaleDate(row.timestamp, false) }}
          </template>
        </el-table-column>
        <el-table-column prop="ability_code" :label="$t('serviceDetail.pp_log_name')" min-width="220" show-overflow-tooltip />
        <el-table-column prop="is_fail" :label="$t('serviceDetail.pp_log_status')" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="status-tag" :class="row.is_fail ? 'fail-tag' : 'success-tag'">
              <FailIcon v-if="row.is_fail" />
              <SuccessIcon v-else />
              <span>{{ row.is_fail ? $t('serviceDetail.pp_log_fail') : $t('serviceDetail.pp_log_success') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="failure_reason" :label="$t('serviceDetail.pp_log_reason')" min-width="180" show-overflow-tooltip />
        <el-table-column :label="$t('common.pp_operation')" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="operate-column" @click="handleViewParams(row)">
              <EyeIcon />
              <span class="edit-text">{{ $t('serviceDetail.pp_log_params') }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination" v-if="!loading">
        <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>

      <el-dialog v-model="paramsDialogVisible" v-if="paramsDialogVisible" :title="$t('serviceDetail.pp_params_set')" width="620px" align-center draggable :close-on-press-escape="false">
        <div class="margin_b-16">
          <span class="color-59">{{ $t('serviceDetail.pp_log_name') }}：</span>
          <span class="color-26">{{ commandName }}</span>
        </div>
        <el-table :data="paramsList" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" max-height="440" class="margin_b-20 width-full">
          <el-table-column prop="param_code" :label="$t('serviceDetail.pp_params_code')" min-width="260" show-overflow-tooltip />
          <el-table-column prop="param_value" :label="$t('serviceDetail.pp_params_value')" min-width="300" show-overflow-tooltip />
        </el-table>
        <div class="flex-box flex_j_c-center">
          <el-button @click="paramsDialogVisible = false" class="welkin-secondary-button">{{ $t('common.pp_close') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, computed } from 'vue'
import { formatLocaleDate, removeNullProp } from '~/utils'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { apiGetUserList } from '~/apis/run-list'
import { apiGetOperationLog, apiGetPus3OperationLog } from '~/apis/plc-record'
import { pagination } from '~/constvars'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash-es'
import SearchIcon from '~/assets/svg/search-icon.vue'
import SuccessIcon from './icon/success-icon.vue'
import FailIcon from './icon/fail-icon.vue'
import EyeIcon from './icon/eye-icon.vue'
import _ from 'lodash'

const $route = useRoute()
const $router = useRouter()
const $store = useStore()
const { t } = useI18n()
const { locale } = useI18n({ useScope: 'global' })
const project = ref(computed(() => $store.state.project))
const formClass = computed(() => {
  if (locale.value == 'zh') {
    if (loading.value) {
      return 'zh-loading-form'
    } else {
      return 'zh-unloading-form'
    }
  } else {
    if (loading.value) {
      return 'en-loading-form'
    } else {
      return 'en-unloading-form'
    }
  }
})

const loading = ref(false)
const remoteLoading = ref(false)
const paramsDialogVisible = ref(false)
const commandName = ref('')

const totalNumber = ref(0)
const tableList = ref([])
const paramsList = ref([])
const userOptions = ref([] as any)

const searchForm = reactive({
  start_time: '' as number | string,
  end_time: '' as number | string,
  page: 1,
  size: 10,
  type: 1,
  operation_interface: '',
  operation_description: '',
  operator: '',
  descending: true
})

/**
 * @description: 远程搜索用户
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchUserList(query)
}
const searchUserList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { fuzzy_name: val }
    const res = await apiGetUserList(params)
    remoteLoading.value = false
    userOptions.value = res.data.people_list
  }
}, 500)

const handleViewParams = (row: any) => {
  commandName.value = row.ability_code
  paramsList.value = row.ability_params || []
  paramsDialogVisible.value = true
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  searchForm.size = val
  searchForm.page = 1
  getTabledata()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  searchForm.page = val
  getTabledata()
}

// 切换本地/云端
const handleChangeType = (val: number) => {
  tableList.value = []
  searchForm.type = val
  searchForm.page = 1
  getTabledata()
}

// 点击筛选按钮
const filterEvent = () => {
  searchForm.page = 1
  getTabledata()
}

// 重置
const resetSelect = () => {
  if (searchForm.type == 1) {
    searchForm.operation_description = ''
    searchForm.operation_interface = ''
    searchForm.operator = ''
  }
  filterEvent()
}

const getTabledata = async (updateRoute = true) => {
  let formData = _.cloneDeep(searchForm) as any
  if (formData.type == 2) {
    delete formData.operation_description
    delete formData.operation_interface
    delete formData.operator
  }
  if (project.value.project != 'PUS3') delete formData.type
  let queryForm = _.cloneDeep(formData) as any
  queryForm.search_start_time = formData.start_time
  queryForm.search_end_time = formData.end_time
  delete queryForm.start_time
  delete queryForm.end_time
  if (updateRoute) {
    $router.push({
      path: location.pathname,
      query: { start_time: $route.query.start_time, end_time: $route.query.end_time, plc: 'operation_log', ...removeNullProp(queryForm) }
    })
  }
  loading.value = true
  try {
    const res = project.value.project == 'PUS3' ? await apiGetPus3OperationLog(project.value.project, $route.params.deviceId, formData) : await apiGetOperationLog(project.value.project, $route.params.deviceId, formData)
    loading.value = false
    if (!res.err_code) {
      tableList.value = res.data
      totalNumber.value = res.total
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    loading.value = false
  }
}

onBeforeMount(() => {
  let init_params: any = $route.query
  searchForm.page = init_params.page ? Number(init_params.page) : 1
  searchForm.size = init_params.size ? Number(init_params.size) : 10
  searchForm.type = init_params.type ? Number(init_params.type) : 1
  if (init_params.search_start_time && init_params.search_end_time) {
    searchForm.start_time = Number(init_params.search_start_time)
    searchForm.end_time = Number(init_params.search_end_time)
  } else {
    searchForm.start_time = Number(init_params.start_time)
    searchForm.end_time = init_params.end_time == 0 ? Number(init_params.start_time) + 1000 * 60 * 5 : Number(init_params.end_time)
  }
  searchForm.operation_description = init_params.operation_description ? init_params.operation_description : ''
  searchForm.operation_interface = init_params.operation_interface ? init_params.operation_interface : ''
  searchForm.operator = init_params.operator ? init_params.operator : ''
  if (searchForm.operator) searchUserList(searchForm.operator)
  getTabledata(false)
})
</script>

<style lang="scss" scoped>
.operation-recording-container {
  width: 100%;
  font-family: 'Blue Sky Standard';
  .tabs-box {
    .left1 {
      left: 4px;
    }
    .left2 {
      left: 56px;
    }
    .left3 {
      left: 65px;
    }
  }
  :deep(.el-form) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 128px;
    column-gap: 24px;
    .el-form-item {
      margin: 0;
      display: inline-flex;
      align-items: center;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
        align-items: center;
      }
    }
  }
  :deep(.zh-loading-form) {
    grid-template-columns: 1fr 1fr 1fr 148px !important;
  }
  :deep(.zh-unloading-form) {
    grid-template-columns: 1fr 1fr 1fr 128px !important;
  }
  :deep(.en-loading-form) {
    grid-template-columns: 1fr 1fr 1fr 173px !important;
  }
  :deep(.en-unloading-form) {
    grid-template-columns: 1fr 1fr 1fr 153px !important;
  }
  :deep(.el-dialog__header) {
    padding: 24px 24px 16px;
    .el-dialog__title {
      color: #1f1f1f;
    }
  }
  :deep(.el-dialog__body) {
    padding: 0 24px 24px;
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  .operation-search-container {
    margin-bottom: 20px;
    background: #fff;
  }
  .operation-table-box {
    margin-top: 20px;
    border-radius: 4px;
    background-color: #fff;
    :deep(.status-tag) {
      height: 26px;
      display: inline-flex;
      align-items: center;
      gap: 4px;
      border-radius: 21px;
      padding: 2px 10px;
      font-size: 13px;
      margin-top: 1px;
    }
    :deep(.success-tag) {
      color: #2f9c74;
      background-color: #e8fcea;
    }
    :deep(.fail-tag) {
      color: #e83030;
      background-color: #fff2f0;
    }
    :deep(.operate-column) {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      margin-top: 2px;
      .edit-text {
        color: #01a0ac;
      }
    }
  }
}
</style>
