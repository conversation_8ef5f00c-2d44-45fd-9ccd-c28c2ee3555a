<template>
  <div class="plc-record-container">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane :label="$t('serviceDetail.pp_high_speed_recording')" name="high_speed" v-if="hasPermission(`${projectVersion}:service-detail:high_speed`)"> </el-tab-pane>
      <el-tab-pane :label="$t('serviceDetail.pp_sensor')" name="sensor" v-if="hasPermission(`${projectVersion}:service-detail:sensor`)"> </el-tab-pane>
      <el-tab-pane :label="$t('serviceDetail.pp_di')" name="di" v-if="hasPermission(`${projectVersion}:service-detail:di`)"> </el-tab-pane>
      <el-tab-pane :label="$t('serviceDetail.pp_converter')" name="converter" v-if="hasPermission(`${projectVersion}:service-detail:converter`)"> </el-tab-pane>
      <el-tab-pane :label="$t('serviceDetail.pp_operation_log')" name="operation_log" v-if="hasPermission(`${projectVersion}:service-detail:operation_log`)"> </el-tab-pane>
      <el-tab-pane :label="$t('serviceDetail.pp_service_status')" name="state_machine" v-if="hasPermission(`${projectVersion}:service-detail:state_machine`)"> </el-tab-pane>
      <el-tab-pane :label="$t('serviceDetail.pp_alarm_list')" name="alarm_list" v-if="hasPermission(`${projectVersion}:service-detail:alarm_list`)"> </el-tab-pane>
    </el-tabs>

    <div class="padding_t-16" v-if="hasPermission(`${projectVersion}:service-detail:${activeTab}`)">
      <div v-if="activeTab === 'high_speed'">
        <HighSpeedRecording :deviceId="deviceId" :serviceId="serviceId" :status="status" :isRestored="isRestored" />
      </div>
      <div v-else-if="activeTab === 'sensor'">
        <SensorRecording :deviceId="deviceId" :serviceId="serviceId" />
      </div>
      <div v-else-if="activeTab === 'di'">
        <DiRecording :deviceId="deviceId" :serviceId="serviceId" />
      </div>
      <div v-else-if="activeTab === 'converter'">
        <ConverterRecording :deviceId="deviceId" :serviceId="serviceId" />
      </div>
      <div v-else-if="activeTab === 'operation_log'">
        <OperationRecording />
      </div>
      <div v-else-if="activeTab === 'state_machine'">
        <StateMachine :deviceId="deviceId" />
      </div>
      <div v-else-if="activeTab === 'alarm_list'">
        <AlarmList :deviceId="deviceId" />
      </div>
    </div>

    <div class="lack-permission padding_t-16" v-else>{{ $t('common.pp_lack_permission') }}</div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { HighSpeedRecording, SensorRecording, ConverterRecording, OperationRecording, DiRecording, StateMachine, AlarmList } from './components'
import type { TabsPaneContext } from 'element-plus'
import { useStore } from 'vuex'
import { hasPermission } from '~/auth'

const $router = useRouter()
const $route = useRoute()
const $store = useStore()
const project = ref(computed(() => $store.state.project))
const projectVersion = ref(
  computed(() => {
    if (project.value.route === 'firefly1') {
      return 'firefly1'
    } else {
      return `powerSwap${project.value.version}`
    }
  })
)

const props = defineProps({
  deviceId: String,
  serviceId: String,
  status: String,
  isRestored: Boolean
})
const { deviceId, serviceId } = toRefs(props)
const activeTab = ref(!!$route.query.plc ? `${$route.query.plc}` : 'high_speed')

// 切换Tab
const handleClick = (tab: TabsPaneContext, event: Event) => {
  let query = {
    start_time: $route.query.start_time,
    end_time: $route.query.end_time,
    plc: tab.paneName
  }
  $router.push({
    path: $router.currentRoute.value.path,
    query: query
  })
}
</script>

<style lang="scss" scoped>
.plc-record-container {
  padding: 24px;
  border-radius: 4px;
  border: 1px solid #dcf2f3;
  background-color: #fff;
  :deep(.tabs-box) {
    height: 32px;
    display: inline-flex;
    padding: 4px;
    border-radius: 4px;
    background-color: #f6fafa;
    position: relative;
    .tab-item {
      z-index: 2;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2px 12px;
      font-size: 14px;
      line-height: 22px;
      color: #434343;
    }
    .ac {
      color: #01a0ac;
    }
    .bg {
      position: absolute;
      top: 2px;
      cursor: pointer;
      z-index: 1;
      height: 28px;
      border-radius: 2px;
      background: #fff;
      box-shadow: 0px 2px 3px -2px rgba(0, 0, 0, 0.16);
      transition: all 0.5s;
    }
  }
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    background-color: #ade8e8;
  }
  :deep(.el-tabs__item.is-active) {
    color: #00bebe;
    font-weight: bold;
  }
  :deep(.el-tabs__item) {
    font-size: 16px;
    font-weight: 400;
    color: #595959;
  }
  :deep(.zh-loading-form) {
    grid-template-columns: 1fr 1fr 148px;
  }
  :deep(.zh-unloading-form) {
    grid-template-columns: 1fr 1fr 128px;
  }
  :deep(.en-loading-form) {
    grid-template-columns: 1fr 1fr 173px;
  }
  :deep(.en-unloading-form) {
    grid-template-columns: 1fr 1fr 153px;
  }
}
</style>
