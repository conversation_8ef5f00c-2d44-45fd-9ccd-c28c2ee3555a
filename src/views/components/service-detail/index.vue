<template>
  <div class="service-detail-content">
    <div class="basic-info-container">
      <div class="flex-box flex_j_c-space-between flex_a_i-center">
        <div class="flex-box flex_a_i-center gap_12">
          <div class="font-size-16 line-height-24 color-26 font-weight-bold">{{ $t('menu.pp_service_detail') }}</div>
          <div class="tag-container" :class="isAutomatic ? 'is-automatic' : 'not-automatic'" v-if="!isEmpty && tagPermission">
            {{ isAutomatic ? $t('serviceDetail.pp_state_automatic') : $t('serviceDetail.pp_state_non_automatic') }}
          </div>
        </div>
        <div class="flex-box flex_a_i-center gap_4 cursor-pointer" @click="foldClick">
          <FoldIcon v-if="isFold" />
          <span class="fold-text" v-if="isFold">{{ $t('common.pp_collapse') }}</span>
          <ExpandIcon v-if="!isFold" />
          <span class="fold-text" v-if="!isFold">{{ $t('common.pp_expand') }}</span>
        </div>
      </div>
      <el-row :gutter="20" v-if="isFold">
        <el-col :span="locale == 'zh' ? 2 : 3">
          <span class="label-text">{{ $t('serviceDetail.pp_service_duration') }}</span>
        </el-col>
        <el-col :span="5">
          <span class="value-text">{{ service_detail.service_duration }}</span>
        </el-col>
        <el-col :span="locale == 'zh' ? 2 : 3" :offset="locale == 'zh' ? 1 : 0">
          <span class="label-text">{{ $t('serviceDetail.pp_service_time_frame') }}</span>
        </el-col>
        <el-col :span="7">
          <span class="value-text">{{ formatLocaleDate(service_detail.service_start_time) }} - {{ formatLocaleDate(service_detail.service_end_time) }}</span>
        </el-col>
        <el-col :span="2">
          <span class="label-text">{{ $t('serviceDetail.pp_service_id') }}</span>
        </el-col>
        <el-col :span="locale == 'zh' ? 5 : 4">
          <WelkinCopyBoard :text="service_detail.service_id" class="value-text" direction="rtl" />
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="isFold">
        <el-col :span="locale == 'zh' ? 2 : 3">
          <span class="label-text">{{ $t('serviceDetail.pp_car_identifier') }}</span>
        </el-col>
        <el-col :span="5">
          <WelkinCopyBoard :text="maskString(service_detail.ev_id)" class="value-text margin_r-30" direction="rtl" />
        </el-col>
        <el-col :span="locale == 'zh' ? 2 : 3" :offset="locale == 'zh' ? 1 : 0">
          <span class="label-text">{{ $t('serviceDetail.pp_service_battery_id') }}</span>
        </el-col>
        <el-col :span="7">
          <WelkinCopyBoard :text="service_detail.service_battery_id" class="value-text margin_r-30" direction="rtl" />
        </el-col>
        <el-col :span="2">
          <span class="label-text">{{ $t('serviceDetail.pp_car_battery_id') }}</span>
        </el-col>
        <el-col :span="locale == 'zh' ? 5 : 4">
          <WelkinCopyBoard :text="service_detail.ev_battery_id" class="value-text margin_r-30" direction="rtl" />
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="isFold">
        <el-col :span="locale == 'zh' ? 2 : 3">
          <span class="label-text">{{ $t('common.pp_vehicle_brand') }}</span>
        </el-col>
        <el-col :span="5">
          <span class="value-text">{{ service_detail.ev_brand ? service_detail.ev_brand : '' }}-{{ service_detail.ev_type ? service_detail.ev_type : '' }}</span>
        </el-col>
      </el-row>
    </div>

    <PlcRecord :deviceId="`${$route.params.deviceId}`" :serviceId="`${$route.params.serviceId}`" :status="archivePlcStatus.status" :isRestored="archivePlcStatus.isRestored" />
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, onBeforeMount, computed } from 'vue'
import { hasPermission } from '~/auth'
import { useI18n } from 'vue-i18n'
import { formatLocaleDate, getDuration, maskString } from '~/utils'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { stateMachineMap1, stateMachineMap2 } from '~/views/components/plc-record/components/constant'
import { PlcRecord } from '..'
import { ElMessage } from 'element-plus'
import { apiGetStateMachineList, apiGetArchiveStatus } from '~/apis/plc-record'
import { apiGetServiceDetail } from '~/apis/service-list'
import FoldIcon from './icon/fold.vue'
import ExpandIcon from './icon/expand.vue'

// 监听路由
const $route = useRoute()
const $store = useStore()

const props = defineProps({
  project: { type: Object, required: true }
})
const { project } = toRefs(props)
const { locale } = useI18n({ useScope: 'global' })

const service_detail = ref()
const isEmpty = ref(false)
const isAutomatic = ref(false)
const form = ref({
  start_time: '' as number | string,
  end_time: '' as number | string,
  data_id: 205109,
  page: 1,
  size: 9000,
  descending: false,
  download: false
})
const isFold = ref(computed(() => $store.state.fold.detailFold))
const tagPermission = hasPermission(`powerSwap${project.value.version}:service-detail:state_machine`)

const archivePlcStatus = ref({
  status: '',
  isRestored: false
})

const getStateMachine = async () => {
  if (!tagPermission) return
  let initParams: any = $route.query
  form.value.start_time = Number(initParams.start_time)
  form.value.end_time = initParams.end_time == 0 ? Number(initParams.start_time) + 1000 * 60 * 5 : Number(initParams.end_time)
  form.value.data_id = stateMachineMap1[project.value.project]
  try {
    const res = await apiGetStateMachineList(form.value, project.value.project, $route.params.deviceId)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      if (res.data) {
        isEmpty.value = false
        const list = res.data.map((item: any) => item[stateMachineMap1[project.value.project]])
        const noRepeatList = [...new Set(list)]
        isAutomatic.value = noRepeatList.length == 1 && noRepeatList[0] == 1
      } else {
        isEmpty.value = true
      }
    }
  } catch (error) {}
}

onBeforeMount(() => {
  getStateMachine()
  service_detail.value = $store.state.service_detail
  const query = {
    start_time: $route.query.start_time,
    end_time: $route.query.end_time
  }
  apiGetServiceDetail(query, project.value.project, $route.params.deviceId, $route.params.serviceId).then((res) => {
    if (res.data?.length > 0) {
      const detail = res.data[0]
      $store.commit('service_detail/SET', detail)

      detail.service_duration = getDuration(detail.service_start_time, detail.service_end_time)
      service_detail.value = detail
    }
  })
  const archiveQuery = {
    service_id: $route.params.serviceId,
    service_start_time: $route.query.start_time
  }
  apiGetArchiveStatus(project.value.project, $route.params.deviceId, archiveQuery).then((res) => {
    const archiveRes = res.data
    if (archiveRes && Array.isArray(archiveRes)) {
      archiveRes.forEach((item: any) => {
        const type = item.type
        if (type) {
          if (type === 'plc-record') {
            archivePlcStatus.value = item
          }
        }
      })
      console.log('archivePlcStatus:', archivePlcStatus.value)
    }
  })
})

// 展开和收缩服务详情
const foldClick = () => {
  $store.commit('fold/SET_DETAIL_FOLD')
}
</script>

<style lang="scss" scoped>
.service-detail-content {
  padding: 0 24px 24px;
  .basic-info-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 24px;
    margin-bottom: 16px;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    background-color: #fff;
    .tag-container {
      height: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      padding: 2px 6px;
      font-size: 12px;
      line-height: 18px;
    }
    .is-automatic {
      color: #1880f2;
      background-color: #e6f8ff;
    }
    .not-automatic {
      color: #ff772e;
      background-color: #fff4e8;
    }
    .fold-text {
      font-size: 14px;
      line-height: 22px;
      color: #01a0ac;
    }
    :deep(.label-text) {
      width: 70px;
      color: #8c8c8c;
      font-size: 14px;
      line-height: 22px;
      white-space: nowrap;
    }
    :deep(.value-text) {
      color: #262626;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
</style>
