<!--
 * @Author: zhenxing.chen
 * @Date: 2024-01-09 16:18:43
 * @LastEditors: zhenxing.chen <EMAIL>
 * @LastEditTime: 2025-08-05 15:52:28
 * @Email: <EMAIL>
 * @Description: 请填写简介
-->
<template>
  <div class="logout-iframe-container">
    <div v-if="refUrl">
      <iframe ref="refIframeLogout" :onload="iframeLoad" :src="refUrl" width="100%" height="100%" frameborder="0" scrolling="yes" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getEnv } from '~/utils';

const refUrl = ref();
const env = getEnv();
const getEnvName = (name: any) => {
  if (name == '-stg-eu') {
    return '-stg.eu';
  } else if (name == '-eu') {
    return '.eu';
  } else {
    return name;
  }
};
/**
 * https://${location.host}/web/welkin/core/system/sso/login?origin_url=${encodeURIComponent(
  location.href
)}&response_type=code
 */
const sso_url = `https://signin${getEnvName(env)}.nio.com/oauth2/authorize?client_id=101021&redirect_uri=${encodeURIComponent(`https://${location.host}/web/welkin/core/system/sso/login?origin_url=${encodeURIComponent(location.href)}&response_type=code`)}`;
const iframeLoad = () => {
  const ssoTimeout = setTimeout(() => {
    // window.location.href = sso_url;
    if (location.href.indexOf('localhost') > -1) {
      return;
    } else {
      window.location.href = sso_url;
    }
    clearTimeout(ssoTimeout);
  }, 30);
};
onMounted(() => {
  refUrl.value = `https://signin${getEnvName(env)}.nio.com/logout`;
});
</script>

<style lang="scss" scoped>
.logout-iframe-container {
  opacity: 0;
}
</style>
