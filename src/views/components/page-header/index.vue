<!--
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 13:31:19
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-02-02 10:22:20
 * @Email: <EMAIL>
 * @Description: 迁移
-->
<template>
  <div class="fixed-header">
    <div class="page-header">
      <slot>
        <div class="page-header_left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>
              {{ firTitle }}
            </el-breadcrumb-item>
            <el-breadcrumb-item>
              {{ secTitle }}
            </el-breadcrumb-item>
          </el-breadcrumb>
          <h1 class="page-header-title">{{ title }}</h1>
        </div>
      </slot>
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
export interface Props {
  title?: string
  firTitle?: string
  secTitle?: string
}
withDefaults(defineProps<Props>(), {
  title: 'hello',
  firTitle: '',
  secTitle: ''
})
</script>

<style lang="scss">
.fixed-header {
  width: 100%;
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  .page-header_left {
    flex: 1;
  }
  .page-header-title {
    font-size: 18px;
    font-weight: 400;
    color: #303133;
    margin: 10px 0px 0px;
  }
}
</style>
