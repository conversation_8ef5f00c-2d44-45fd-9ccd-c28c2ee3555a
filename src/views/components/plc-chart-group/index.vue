<template>
  <div class="plc-echarts">
    <div :id="`${item.key}Chart`" style="width: 100%; height: 240px" v-for="(item, index) in data.group">
      {{ `${item.key}Chart` }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, nextTick, toRefs, computed } from 'vue';
import { useStore } from 'vuex';
import * as echarts from 'echarts';
import { chart_options } from '../../../constvars/plc-record/chart';

const props = defineProps({
  data: { type: Object, required: true },
});
const { data } = toRefs(props);

const store = useStore();
const echartsArr = ref([] as any);
const isCollapse = computed(() => store.state.menus.collapse);

const setEcharts = () => {
  type EChartsOption = echarts.EChartsOption;
  for (let i = 0; i < data.value?.group?.length; i++) {
    const item = data.value.group[i];
    const itemkey = item.key;
    const itemDom = document.getElementById(`${itemkey}Chart`)!;
    const itemEchart = echarts.init(itemDom);
    echartsArr.value.push(itemEchart);
    let itemOption: EChartsOption;

    itemOption = {
      ...chart_options,
      title: {
        padding: 0,
        text: item.title,
        textStyle: {
          color: '#262626',
          fontSize: 16,
        },
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.value.time,
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#8C8C8C',
          align: 'left',
          alignMaxLabel: 'right',
        },
        axisLine: {
          lineStyle: {
            color: '#D9D9D9',
          },
        },
      },
      series: item.data,
    };
    if (item.yAxis) {
      itemOption.yAxis = item.yAxis;
    }
    itemOption.legend = { ...chart_options.legend, data: data.value.legend };

    if (item.legend) {
      itemOption.legend = item.legend;
    }
    if (item.tooltip) {
      itemOption.tooltip = item.tooltip;
    }

    if (item.color) {
      itemOption.color = item.color;
    }
    itemEchart.clear();

    document.getElementById(`${itemkey}Chart`)!.setAttribute('_echarts_instance_', '');
    itemOption && itemEchart.setOption(itemOption, true);
    itemEchart.group = 'echartGroup';
    echarts.connect('echartGroup');
    window.addEventListener('resize', () => itemEchart.resize());
  }
};

//watch监听动态拿到值的变化,从而做出反应
watch(
  () => data,
  (newVal, oldVal) => {
    nextTick(() => {
      console.log(33333333);
      setEcharts();
    });
  },
  { deep: true }
);

watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize());
    }, 300);
  }
);
</script>

<style lang="scss" scoped>
.plc-echarts {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
</style>
