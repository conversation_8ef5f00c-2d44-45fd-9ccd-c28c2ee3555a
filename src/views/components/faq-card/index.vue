<!--
 * @Author: zhenxing.chen
 * @Date: 2023-01-11 18:37:30
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-28 16:01:49
 * @Email: <EMAIL>
 * @Description: 迁移
-->
<template>
  <div class="faq-card-container">
    <div class="faq-body-container question-body">
      <div class="title">{{ `Q${order}` }}</div>
      <div class="text">
        {{ question }}
      </div>
    </div>
    <div class="faq-body-container answer-body" style="white-space: pre-line">
      {{ answer }}
      <div class="faq-body-quotation">“</div>
    </div>
  </div>
</template>

<script setup lang="ts">

export interface Props {
  order?:number,
  question?:string,
  answer?:string
 }
 withDefaults(defineProps<Props>(),{
   order:0,
   question:'',
   answer:''
 })

</script>

<style lang="scss" scoped>
.faq-card-container {
  width: 100%;
  display: flex;
  padding: 16px 10px;
  flex-direction: column;
  .faq-body-container {
    width: 100%;
    display: flex;
    border-radius: 10px;
    align-items: center;
  }
  .question-body {
    background-color: rgba(44, 115, 255, 0.1);
    .title {
      border-radius: 50%;
      height: 32px;
      width: 32px;
      background-color: #00BEBE;
      flex-shrink: 0;
      color: #ffffff;
      font-weight: 700;
      font-size: 14px;
      line-height: 32px;
      margin: 10px 16px 10px 16px;
      text-align: center;
      font-family: 'Noto Sans CJK SC';
      font-style: normal;
      align-items: center;
    }
    .text {
      font-weight: 700;
      font-size: 14px;
      line-height: 24px;
      color: rgba(75, 82, 95, 0.7);
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }
  .answer-body {
    border: 1px solid #e8eef2;
    margin-top: 20px;
    padding: 20px;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #4b525f;
    position: relative;
    .faq-body-quotation {
      width: 51px;
      height: 28px;
      font-weight: 800;
      font-size: 40px;
      line-height: 48px;
      color: #91a2bc;
      background-color: #ffffff;
      font-family: 'Blue Sky Standard';
      position: absolute;
      top: -14px;
      text-align: center;
    }
  }
}
</style>
