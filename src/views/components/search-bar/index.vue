<!--
 * @Author: zhenxing.chen
 * @Date: 2023-01-11 18:37:37
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-16 15:01:31
 * @Email: <EMAIL>
 * @Description: 迁移
-->
<template>
  <div class="search-bar">
    <input
      class="search-bar_input"
      type="text"
      :value="modelValue"
      :placeholder="placeholder"
      @input="onInput"
      @keyup.enter="onSearch"
    />
    <button class="search-bar_action" @click="onSearch">{{ action }}</button>
  </div>
</template>

<script setup lang="ts">

 export interface Props {
  placeholder?:string,
  action?:string,
  modelValue?:string
 }
 //定义响应式变量 
 withDefaults(defineProps<Props>(),{
   placeholder:'输入设备ID/设备名',
   action:'搜索',
   modelValue:''
 })

// type-based
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void,
  (e:'search',value:string):void
}>()

const onInput =  (e:any) =>{
   emit('update:modelValue', e.target.value)
}

 const onSearch = (e:any) =>{
   emit('search',e)
}

</script>

<style lang="scss" scoped>
$color-primary: var(--el-color-primary);
$color-placeholder: var(--wel-text-placeholder);
$color-text: var(--el-text-color);
$color-border: var(--el-border-color);

.search-bar {
  flex: 1;
  max-width: 500px;
  height: 50px;
  font-size: 1.6rem;
  color: $color-text;
  position: relative;

  .search-bar_input {
    height: 100%;
    width: 100%;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    padding: 0.5em 98px 0.5em 1em;
    outline: none;
    transition: border-color 0.25s ease-in, color 0.25s ease-in;
    border: 1px solid $color-border;
    border-radius: 12px;

    &::placeholder {
      color: $color-placeholder;
    }

    &:hover,
    &:focus {
      border-color: $color-primary;
      color: $color-text;
    }
  }

  .search-bar_action {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    height: calc(100% - 4px * 2);
    font-size: inherit;
    font-family: inherit;
    font-weight: bold;
    color: white;
    background-color: $color-primary;
    width: 90px;
    border-width: 0;
    text-align: center;
    border-radius: 8px;
    padding: 0 1em;
    cursor: pointer;
    min-width: 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
