<template>
  <div class="login-container">
    <Logout :url="logoutUrl" v-if="showLogout" />
  </div>
</template>

<script setup lang="ts">
import {ref, onBeforeMount} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {useI18n} from 'vue-i18n'
import {ElMessage, ElMessageBox} from 'element-plus'
import {getEnv, getUserId} from '~/utils'
import {apiGetMenuList, apiGetAccessToken} from '~/apis/user-management'
const queryString = window.location.search
const params = new URLSearchParams(queryString)
import Logout from './logout.vue'
import cookie from 'js-cookie'

const {t} = useI18n()
const store = useStore()
const router = useRouter()
const showLogout = ref(false)
const isLogin = async () => {
  const r = {user_id: cookie.get('user_id')}
  if (r && r.user_id) {
    router.push({name: 'redirect', replace: true})
  } else {
    let code = params.get('code')
    if(!code) showLogout.value = true
    try {
      const ssoRes = await apiGetAccessToken(code)
      const {err_code, user_id} = ssoRes
      cookie.set('user_id', user_id, {expires: 5})
      localStorage.setItem('user_id', user_id)
      if (!err_code) {
        try {
          let currMenu = await apiGetMenuList()
          store.commit('SET_MENUS', currMenu.data?.authorities)
          store.commit('SET_CONDITIONS', currMenu.data?.conditions)
          window.location.href = '/home'
        } catch (e: any) {
          ElMessage.error(e)
        }
      } else {
        ElMessageBox.alert(t('common.pp_lack_permission'), t('common.pp_reminder'), {
          confirmButtonText: t('common.pp_confirm')
        })
      }
    } catch (e: any) {
      ElMessage.error(e)
    }
  }
}

/**
 * @description: 获取 sso 登出地址
 * @param {*}
 * @return {*} 返回 sso 地址
 */
const env = getEnv()
const getEnvName = (name: any) => {
  if(name == '-stg-eu') {
    return '-stg.eu'
  } else if(name == '-eu') {
    return '.eu'
  } else {
    return name
  }
}
const logoutUrl = ref(`https://signin${getEnvName(env)}.nio.com/logout`)

onBeforeMount(() => {
  const userId = getUserId() || null
  let code = params.get('code')
  if (code) {
    isLogin()
    return
  }
  if (userId && code) {
    window.location.href = '/home'
  } else {
    showLogout.value = true
  }
})
</script>
