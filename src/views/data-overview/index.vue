<template>
  <div class="data-overview-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_station_management') }}</div>
    </div>

    <div class="content-container">
      <div class="data-card" v-if="!loading">
        <div class="color-a0 swap-title">{{ $t('stationManagement.pp_year_cumulative') }}</div>
        <div class="swap-value" style="color: #35798b">{{ formatData(list.total_swap_times) }}</div>

        <div class="main-title">{{ $t('stationManagement.pp_station') }}</div>
        <div class="device-gap">
          <span class="main-data">{{ formatData(list.device.value) }}</span>
          <span class="main-unit">{{ $t('stationManagement.pp_station_unit') }}</span>
        </div>
        <div class="sub-device-gap">
          <div v-for="item in list.device.detail" class="flex-box flex_d-column device-column">
            <div class="sub-title">{{ projectMap[item.project] }}{{ $t('stationManagement.pp_daily_order') }}</div>
            <div class="flex-box flex_a_i-flex-end device-vaule">
              <span class="sub-data">{{ formatData(item.total) }}</span>
              <span class="sub-unit">{{ $t('stationManagement.pp_station_unit') }}/</span>
              <span class="sub-data">{{ formatData(item.daily_order_count) }}</span>
              <span class="sub-unit">{{ $t('stationManagement.pp_order') }}</span>
            </div>
          </div>
        </div>

        <div class="flex-box flex_d-column other-column">
          <div v-for="item in typeMap">
            <div class="main-title">{{ item.name }}</div>
            <div class="flex-box flex_a_i-flex-end other-unit">
              <span class="main-data">{{ formatData(list[item.key].value) }}</span>
              <span class="main-unit" v-if="!isEmptyData(list[item.key].value)">{{ item.unit }}</span>
            </div>
            <div class="flex-box gap_24 other-sub">
              <div v-for="detail in list[item.key].detail" class="flex-box flex_d-column other-sub-detail">
                <div class="sub-title">{{ projectMap[detail.project] }}{{ item.subName || item.name }}</div>
                <div class="flex-box flex_a_i-flex-end other-sub-unit">
                  <span class="sub-data">{{ formatData(detail.value) }}</span>
                  <span class="sub-unit" v-if="!isEmptyData(detail.value)">{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="update-time" v-if="!loading">
        <span>{{ $t('stationManagement.pp_revenue_update') }}：{{ formatTimeToDay(list.revenue_update_time) }}</span>
        <span>{{ $t('stationManagement.pp_energy_update') }}：{{ formatTimeToDay(list.energy_update_time) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, onBeforeUnmount } from 'vue'
import { projectMap, typeMap } from './constant'
import { formatTimeToDay, isEmptyData } from '~/utils'
import { apiGetOverview } from '~/apis/station-management'
import { ElMessage } from 'element-plus'

const timer = ref()
const loading = ref(false)
const list = ref({} as any)

const formatData = (data: number) => {
  return isEmptyData(data) ? '-' : data.toLocaleString()
}

const getList = async () => {
  if (timer.value) clearTimeout(timer.value)
  try {
    const res = await apiGetOverview()
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      timer.value = setTimeout(() => {
        getList()
      }, 5000)
    }
  } catch (error: any) {
    loading.value = false
  }
}

onBeforeMount(() => {
  loading.value = true
  getList()
})

onBeforeUnmount(() => {
  if (timer.value) clearTimeout(timer.value)
})
</script>

<style lang="scss" scoped>
.data-overview-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #e2f9f9 -6.51%, #f8f8f8 12.89%);
  .header-container {
    padding: 24px 24px 20px;
    .header-title {
      color: #1f1f1f;
      font-size: 20px;
      font-weight: bold;
    }
  }
  .content-container {
    flex: 1;
    position: relative;
    margin: 0 24px 24px;
    background-image: url(https://cdn-welkin-public.nio.com/welkin/image/frontend/map.png);
    background-repeat: no-repeat;
    background-size: cover;
  }

  @media (max-width: 2000px) {
    .data-card {
      position: absolute;
      top: 24px;
      left: 24px;
      bottom: 24px;
      padding: 32px;
      border-radius: 8px;
      white-space: nowrap;
      overflow: auto;
      scrollbar-width: none;
      background: rgba(255, 255, 255, 0.4);
      .swap-title {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 12px;
      }
      .swap-value {
        font-size: 36px;
        line-height: 36px;
        margin-bottom: 24px;
      }
      .device-gap {
        display: flex;
        align-items: flex-end;
        gap: 6px;
        margin-bottom: 16px;
      }
      .sub-device-gap {
        display: flex;
        gap: 24px;
        margin-bottom: 24px;
        .device-column {
          gap: 8px;
        }
        .device-vaule {
          gap: 4px;
        }
      }
      .other-column {
        gap: 24px;
        .other-unit {
          gap: 6px;
          margin-bottom: 16px;
        }
        .other-sub {
          gap: 24px;
        }
        .other-sub-detail {
          gap: 8px;
        }
        .other-sub-unit {
          gap: 4px;
        }
      }
      .main-title {
        font-size: 16px;
        line-height: 16px;
        color: #595959;
        margin-bottom: 6px;
      }
      .main-data {
        font-size: 24px;
        line-height: 24px;
        color: #262626;
      }
      .main-unit {
        font-size: 16px;
        line-height: 18px;
        color: #595959;
        padding-bottom: 1px;
      }
      .sub-title {
        font-size: 12px;
        line-height: 12px;
        color: #595959;
      }
      .sub-data {
        font-size: 20px;
        line-height: 20px;
        color: #434343;
      }
      .sub-unit {
        font-size: 12px;
        line-height: 16px;
        color: #595959;
      }
    }
    .update-time {
      position: absolute;
      bottom: 36px;
      right: 32px;
      font-size: 14px;
      color: #262626;
      display: flex;
      gap: 32px;
    }
  }

  @media (min-width: 2001px) {
    .data-card {
      position: absolute;
      top: 45px;
      left: 45px;
      padding: 55px;
      border-radius: 8px;
      white-space: nowrap;
      overflow: auto;
      scrollbar-width: none;
      background: rgba(255, 255, 255, 0.4);
      .swap-title {
        font-size: 28px;
        line-height: 28px;
        margin-bottom: 20px;
      }
      .swap-value {
        font-size: 62px;
        line-height: 62px;
        margin-bottom: 42px;
      }
      .device-gap {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        margin-bottom: 26px;
      }
      .sub-device-gap {
        display: flex;
        gap: 40px;
        margin-bottom: 40px;
        .device-column {
          gap: 12px;
        }
        .device-vaule {
          gap: 6px;
        }
      }
      .other-column {
        gap: 40px;
        .other-unit {
          gap: 8px;
          margin-bottom: 26px;
        }
        .other-sub {
          gap: 40px;
        }
        .other-sub-detail {
          gap: 12px;
        }
        .other-sub-unit {
          gap: 6px;
        }
      }
      .main-title {
        font-size: 28px;
        line-height: 28px;
        color: #595959;
        margin-bottom: 12px;
      }
      .main-data {
        font-size: 42px;
        line-height: 42px;
        color: #262626;
      }
      .main-unit {
        font-size: 28px;
        line-height: 32px;
        color: #595959;
        padding-bottom: 1px;
      }
      .sub-title {
        font-size: 20px;
        line-height: 20px;
        color: #595959;
      }
      .sub-data {
        font-size: 34px;
        line-height: 34px;
        color: #434343;
      }
      .sub-unit {
        font-size: 20px;
        line-height: 26px;
        color: #595959;
      }
    }
    .update-time {
      position: absolute;
      bottom: 67px;
      right: 80px;
      font-size: 26px;
      color: #262626;
      display: flex;
      gap: 40px;
    }
  }
}
</style>
