import { i18n } from '~/i18n'

export const projectMap = {
  PowerSwap2: '2.0',
  PUS3: '3.0',
  PUS4: '4.0'
} as any

export const typeMap = [
  {
    name: i18n.global.t('stationManagement.pp_station_energy'),
    key: 'energy',
    unit: '%'
  },
  {
    name: i18n.global.t('stationManagement.pp_station_health'),
    key: 'health',
    unit: '%'
  },
  {
    name: i18n.global.t('stationManagement.pp_station_revenue'),
    key: 'profit',
    unit: i18n.global.t('stationManagement.pp_yuan_day'),
    subName: i18n.global.t('stationManagement.pp_single_revenue')
  }
] as any