<template>
  <div class="log-analysis-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_info_trace') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_log_analysis') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_log_analysis') }}</div>
      </div>
      <div class="header-right">
        <el-radio-group v-model="deviceType" @change="handleChangeType" class="margin_r-40">
          <el-radio :label="item.value" v-for="item in deviceTypeOptions">{{ $t(item.label) }}</el-radio>
        </el-radio-group>
        <el-button :icon="Plus" class="welkin-primary-button" v-if="hasPermission('function:fault-diagnosis:add')" @click="addDialogVisible = true">{{ $t('common.pp_create') }}</el-button>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="swap-table-container">
        <el-table :data="list" style="width: 100%" :header-cell-style="{fontSize: '14px', color: '#292C33', cursor: 'auto'}" v-loading="loading">
          <el-table-column type="expand">
            <template #default="scope">
              <div v-if="scope.row.logs">
                <div v-for="log in scope.row.logs">{{ log }}</div>
              </div>
              <div v-else>{{ $t('logAnalysis.pp_no_log') }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="$t('alarmList.pp_device')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.description ? scope.row.description : $t('common.pp_unnamed_device') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="device_id" :label="$t('batterySwap.pp_device_id')" min-width="200">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" v-if="scope.row.device_id" />
              <span class="unknown-text" v-else>{{ $t('batterySwap.pp_unknown') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="events" :label="$t('logAnalysis.pp_event_id')" min-width="240" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.events.join(', ') }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>
    </div>

    <el-dialog :title="$t('logAnalysis.pp_create_title')" width="600px" v-model="addDialogVisible" @close="handleCloseHeadDialog" :close-on-click-modal="false">
      <el-form ref="addFormRef" :model="form" label-position="left" label-width="100px">
        <el-form-item :label="$t('deviceManagement.pp_device')" prop="device_id">
          <el-select v-model="form.device_id" class="width-460" :placeholder="$t('common.pp_please_select')" clearable filterable>
            <el-option v-for="item in deviceOptions" :key="item.value" :value="item.value" :label="item.label" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('logAnalysis.pp_event_id')" prop="events" class="margin_b-0">
          <div v-for="(item, index) in form.events" :key="index" class="margin_b-20">
            <el-input v-model="form.events[index]" oninput="value=value.replace(/[^0-9]/g,'')" class="width-400 margin_r-20" :placeholder="$t('common.pp_please_input')" clearable />
            <el-button size="small" class="width-40 welkin-secondary-button" :icon="Plus" @click="addEvent" v-if="!index"></el-button>
            <el-button size="small" class="width-40 welkin-secondary-button" :icon="Delete" @click="removeEvent(index)" v-else></el-button>
          </div>
        </el-form-item>
        <el-form-item :label="$t('common.pp_time_frame')" prop="datePicker">
          <el-date-picker v-model="form.datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addDialogVisible = false" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button class="welkin-primary-button" :loading="addLoading" @click="handleSubmitForm">{{ $t('common.pp_confirm') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, onBeforeMount} from 'vue'
import {useI18n} from 'vue-i18n'
import {page} from '~/constvars/page'
import {hasPermission} from '~/auth'
import {ElMessage} from 'element-plus'
import {Plus, Delete} from '@element-plus/icons-vue'
import {apiGetDeviceNameMap} from '~/apis/device-management'
import {apiGetLogList, apiPostLog} from '~/apis/log-analysis'
import {getDisabledDate, getShortcuts} from '~/utils'
import _ from 'lodash'

const addFormRef = ref()
const loading = ref(false)
const addLoading = ref(false)
const addDialogVisible = ref(false)
const list = ref([] as any)
// const expandRows = ref([] as any)
const deviceOptions = ref([] as any)
const pages = ref(_.cloneDeep(page))
const deviceType = ref('PUS3')
const deviceTypeOptions = ref([
  {
    label: 'menu.pp_swap_station2',
    value: 'PowerSwap2'
  },
  {
    label: 'menu.pp_swap_station3',
    value: 'PUS3'
  }
])
const form = ref({
  key: 'LogAnalysis',
  device_id: '',
  events: [''] as any,
  datePicker: [] as any,
  type: 1,
  start_time: '' as number | string,
  end_time: '' as number | string
})

/**
 * @description: 每次只展开一行
 * @param {*} row
 * @param {*} expandedRows
 * @return {*}
 */
// const handleExpandChange = (row: any, expandedRows: any) => {
//   expandRows.value = []
//   if (expandedRows.length) {
//     expandRows.value = [row.device_id]
//   }
// }

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 增加事件
 * @return {*}
 */
const addEvent = () => {
  form.value.events.push('')
}

/**
 * @description: 移除事件
 * @param {*} index
 * @return {*}
 */
const removeEvent = (index: number) => {
  form.value.events.splice(index, 1)
}

/**
 * @description: 获取设备列表
 * @return {*}
 */
const getDeviceNameMap = async () => {
  deviceOptions.value = []
  const res = await apiGetDeviceNameMap(deviceType.value)
  res.data.map((item: any) => {
    deviceOptions.value.push({
      label: item.device_id + ' - ' + item.description,
      value: item.device_id
    })
  })
}

/**
 * @description: 关闭弹窗，清空表单
 * @return {*}
 */
const handleCloseHeadDialog = () => {
  addFormRef.value.resetFields()
  form.value.start_time = ''
  form.value.end_time = ''
}

/**
 * @description: 切换设备类型
 * @return {*}
 */
const handleChangeType = () => {
  getDeviceNameMap()
  pages.value.current = 1
  // expandRows.value = []
  getList()
}

/**
 * @description: 查询数据
 * @return {*}
 */
const getList = async () => {
  if (loading.value) return
  let formData = {} as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.descending = true
  loading.value = true
  try {
    const res = await apiGetLogList(formData, deviceType.value)
    list.value = res.data
    pages.value.total = res.total
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

/**
 * @description: 提交表单
 * @return {*}
 */
const handleSubmitForm = async () => {
  const params = _.cloneDeep(form.value) as any
  params.project = deviceType.value
  params.events = params.events.filter((item: any) => item).map((item: any) => Number(item))
  delete params.datePicker
  addLoading.value = true
  try {
    const res = await apiPostLog(params)
    if(!res.err_code) {
      ElMessage.success('下发成功')
      addDialogVisible.value = false
      pages.value.current = 1
      getList()
    } else {
      ElMessage.error(res.message)
    }
    addLoading.value = false
  } catch (error) {
    addLoading.value = false
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

onBeforeMount(() => {
  getDeviceNameMap()
  getList()
})
</script>

<style lang="scss" scoped>
.log-analysis-container {
  font-family: 'Noto Sans';
  .swap-page-header .header-left .header-title {
    font-weight: bold;
  }
  .swap-table-container .pagination-container {
    padding-right: 20px;
  }
  :deep(.el-form-item__label) {
    color: #292c33;
  }
  :deep(.el-table__expanded-cell) {
    padding-left: 60px;
  }
  :deep(.el-dialog__footer) {
    display: flex;
    justify-content: center;
  }
}
</style>
