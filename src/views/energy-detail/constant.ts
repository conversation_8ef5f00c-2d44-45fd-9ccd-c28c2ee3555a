export const lineBarOptionMock = {
  animation: false,
  color: ['#00BEBE', '#7FD9DE'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    top: '40',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    data: [],
    axisPointer: {
      type: 'shadow'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    }
  },
  yAxis: [
    {
      name: '',
      nameTextStyle: {
        padding: [0, 0, 4, 8]
      },
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    },
    {
      name: '',
      nameTextStyle: {
        padding: [0, 0, 4, 0]
      },
      type: 'value',
      alignTicks: true,
      min: (value: any) => Math.floor(value.min),
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}%'
      }
    }
  ],
  series: [
    {
      name: '',
      type: 'bar',
      barMaxWidth: 24,
      data: []
    },
    {
      name: '',
      type: 'line',
      tooltip: {
        valueFormatter: (value: any) => value + '%'
      },
      data: [],
      yAxisIndex: 1,
      lineStyle: {
        width: 1.5
      }
    }
  ]
} as any

export const formatTime = (timestamp: any) => {
  if (!timestamp) return '-'
  const timeDate = new Date(timestamp)
  const y = timeDate.getFullYear()
  const m = timeDate.getMonth() + 1
  const d = timeDate.getDate()
  const x = y.toString().slice(-2) + '/' + (m < 10 ? '0' + m : m) + '/' + (d < 10 ? '0' + d : d)
  return x
}

export const formatEmptyData = (data: any) => {
  return data === null || data === undefined || data === '' ? '-' + 'kWh' : data.toFixed(2) + 'kWh'
}

export const draw = (canvasRef: any, isPss2: boolean) => {
  const canvas = canvasRef!
  const ctx = canvas.getContext('2d')!
  ctx.clearRect(0, 0, 1200, 344)
  canvas.width = 2400
  canvas.height = 688
  ctx.scale(2, 2)
  ctx.strokeStyle = '#00BEBE'
  ctx.fillStyle = '#00BEBE'
  ctx.lineWidth = 1.5

  // 国家电网和变压器之间
  ctx.beginPath()
  ctx.moveTo(82, 171)
  ctx.lineTo(140, 171)
  ctx.stroke()
  ctx.beginPath()
  ctx.moveTo(134, 165)
  ctx.lineTo(140, 171)
  ctx.stroke()
  ctx.beginPath()
  ctx.moveTo(134, 177)
  ctx.lineTo(140, 171)
  ctx.stroke()

  // 变压器和换电站之间
  ctx.beginPath()
  ctx.moveTo(195, 171)
  ctx.lineTo(243, 171)
  ctx.stroke()
  ctx.arc(255, 171, 12, 0, (360 * Math.PI) / 180)
  ctx.fill()
  ctx.font = '14px Noto Sans'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillStyle = 'white'
  ctx.fillText('1', 255, 171)
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(267, 171)
  ctx.lineTo(315, 171)
  ctx.stroke()
  ctx.moveTo(309, 165)
  ctx.lineTo(315, 171)
  ctx.stroke()
  ctx.moveTo(309, 177)
  ctx.lineTo(315, 171)
  ctx.stroke()

  // 换电站和功率模块之间
  ctx.beginPath()
  ctx.moveTo(382, 171)
  ctx.lineTo(441, 171)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.arc(441, 165, 6, 0, (90 * Math.PI) / 180)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(447, 165)
  ctx.lineTo(447, 148)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.fillStyle = '#00BEBE'
  ctx.arc(447, 136, 12, 0, (360 * Math.PI) / 180)
  ctx.fill()
  ctx.font = '14px Noto Sans'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillStyle = 'white'
  ctx.fillText('2', 447, 136)
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(447, 124)
  ctx.lineTo(447, 107)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.arc(453, 107, 6, Math.PI, (3 * Math.PI) / 2)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(453, 101)
  ctx.lineTo(579, 101)
  ctx.moveTo(573, 95)
  ctx.lineTo(579, 101)
  ctx.moveTo(573, 107)
  ctx.lineTo(579, 101)
  ctx.stroke()
  ctx.closePath()

  ctx.beginPath()
  ctx.moveTo(664, 101)
  ctx.lineTo(716, 101)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.fillStyle = '#00BEBE'
  ctx.arc(728, 101, 12, 0, 2 * Math.PI)
  ctx.fill()
  ctx.font = '14px Noto Sans'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillStyle = 'white'
  ctx.fillText('3', 728, 101)
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(740, 101)
  ctx.lineTo(820, 101)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.fillStyle = '#00BEBE'
  ctx.arc(832, 101, 12, 0, 2 * Math.PI)
  ctx.fill()
  ctx.font = '14px Noto Sans'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillStyle = 'white'
  ctx.fillText('4', 832, 101)
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(844, 101)
  ctx.lineTo(896, 101)
  ctx.stroke()
  ctx.moveTo(890, 95)
  ctx.lineTo(896, 101)
  ctx.stroke()
  ctx.moveTo(890, 107)
  ctx.lineTo(896, 101)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.setLineDash([4, 4])
  ctx.moveTo(952, 101)
  ctx.lineTo(1008, 101)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.setLineDash([])
  ctx.moveTo(1002, 95)
  ctx.lineTo(1010, 101)
  ctx.stroke()
  ctx.moveTo(1002, 107)
  ctx.lineTo(1010, 101)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.fillStyle = '#00BEBE'
  ctx.arc(1044, 101, 12, 0, 2 * Math.PI)
  ctx.fill()
  ctx.font = '14px Noto Sans'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillStyle = 'white'
  ctx.fillText('5', 1044, 101)
  ctx.closePath()

  if (!isPss2) {
    ctx.beginPath()
    ctx.setLineDash([4, 4])
    ctx.lineDashOffset = 0
    ctx.moveTo(780, 101)
    ctx.lineTo(780, 16)
    ctx.stroke()
    ctx.lineTo(891, 16)
    ctx.stroke()
    ctx.closePath()
    ctx.beginPath()
    ctx.setLineDash([])
    ctx.moveTo(890, 10)
    ctx.lineTo(896, 16)
    ctx.stroke()
    ctx.moveTo(890, 22)
    ctx.lineTo(896, 16)
    ctx.stroke()
    ctx.closePath()
    ctx.beginPath()
    ctx.moveTo(952, 16)
    ctx.lineTo(1012, 16)
    ctx.stroke()
    ctx.moveTo(1006, 10)
    ctx.lineTo(1012, 16)
    ctx.stroke()
    ctx.moveTo(1006, 22)
    ctx.lineTo(1012, 16)
    ctx.stroke()

    ctx.beginPath()
    ctx.moveTo(780, 70)
    ctx.arc(780, 58, 12, 0, 2 * Math.PI)
    ctx.fillStyle = '#00BEBE'
    ctx.fill()
    ctx.font = '14px Noto Sans'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillStyle = 'white'
    ctx.fillText('6', 780, 58)
  }

  // 换电站和UPS用电之间
  ctx.setLineDash([])
  ctx.beginPath()
  ctx.arc(441, 177, 6, (3 * Math.PI) / 2, 2 * Math.PI)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(447, 177)
  ctx.lineTo(447, 194)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.fillStyle = '#00BEBE'
  ctx.arc(447, 206, 12, 0, (360 * Math.PI) / 180)
  ctx.fill()
  ctx.font = '14px Noto Sans'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillStyle = 'white'
  ctx.fillText("2'", 447, 206)
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(447, 218)
  ctx.lineTo(447, 295)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(447, 236)
  ctx.lineTo(579, 236)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(573, 230)
  ctx.lineTo(579, 236)
  ctx.moveTo(573, 242)
  ctx.lineTo(579, 236)
  ctx.stroke()
  ctx.closePath()

  // 换电站和配电回路之间
  ctx.beginPath()
  ctx.arc(453, 295, 6, Math.PI, Math.PI / 2, true)
  ctx.stroke()
  ctx.closePath()
  ctx.beginPath()
  ctx.moveTo(453, 301)
  ctx.lineTo(579, 301)
  ctx.moveTo(573, 295)
  ctx.lineTo(579, 301)
  ctx.moveTo(573, 307)
  ctx.lineTo(579, 301)
  ctx.stroke()
  ctx.closePath()

  ctx.beginPath()
  ctx.moveTo(670, 301)
  ctx.lineTo(742, 301)
  ctx.moveTo(736, 295)
  ctx.lineTo(742, 301)
  ctx.moveTo(736, 307)
  ctx.lineTo(742, 301)
  ctx.stroke()
}
