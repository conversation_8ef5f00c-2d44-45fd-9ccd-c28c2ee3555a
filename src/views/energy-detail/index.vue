<template>
  <div class="energy-detail-container">
    <div class="flex-box flex_a_i-center font-size-20 line-height-28 margin_b-20">
      <BackIcon @click="handleBack" class="cursor-pointer" />
      <span class="color-8c font-weight-480 margin_l-8">{{ $t('menu.pp_energy_list') }}</span>
      <span class="margin-n-8">/</span>
      <span class="color-1f font-weight-450">{{ route.query.description || $t('common.pp_unnamed_device') }}</span>
    </div>

    <div class="first-line" v-if="!loading1">
      <!-- 能效信息 -->
      <div class="content-card">
        <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-6">
          <span class="font-size-14 line-height-22 color-26 font-weight-bold">{{ $t('stationManagement.pp_energy_efficiency') }}</span>
          <div class="flex-box flex_a_i-center gap_4">
            <CalendarIcon />
            <span class="font-size-14 color-59">{{ formatTimeToDay(Number(route.query.day)) }}</span>
          </div>
        </div>
        <div class="color-a0 font-size-32 line-height-32 margin_b-24">{{ isEmptyData(list.energy) ? '-' : list.energy + '%' }}</div>
        <div class="consumption-data">
          <div class="flex-box flex_d-column">
            <ChargeConsumption class="margin_b-6" />
            <span class="font-size-12 line-height-18 color-59">{{ $t('stationManagement.pp_charge_consumption') }}（kWh）</span>
            <div class="flex-box gap_6">
              <span class="font-size-24 line-height-38 color-26">{{ isEmptyData(list.charge_consumption) ? '-' : list.charge_consumption }}</span>
              <div v-if="!isEmptyData(list.charge_consumption_change)" class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6" :style="{ color: list.charge_consumption_change >= 0 ? '#01A0AC' : '#F83535' }">
                <span>{{ $t('stationManagement.pp_today') }}&nbsp;</span>
                <span v-if="list.charge_consumption_change >= 0">+</span>
                <span>{{ list.charge_consumption_change }}</span>
              </div>
              <div v-else class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6 color-8c">{{ $t('stationManagement.pp_no_data_from_yesterday') }}</div>
            </div>
          </div>
          <div class="flex-box flex_d-column">
            <NonChargeConsumption class="margin_b-6" />
            <span class="font-size-12 line-height-18 color-59">{{ $t('stationManagement.pp_non_charge_consumption') }}（kWh）</span>
            <div class="flex-box gap_6">
              <span class="font-size-24 line-height-38 color-26">{{ isEmptyData(list.non_charge_consumption) ? '-' : list.non_charge_consumption }}</span>
              <div v-if="!isEmptyData(list.non_charge_consumption_change)" class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6" :style="{ color: list.non_charge_consumption_change >= 0 ? '#01A0AC' : '#F83535' }">
                <span>{{ $t('stationManagement.pp_today') }}&nbsp;</span>
                <span v-if="list.non_charge_consumption_change >= 0">+</span>
                <span>{{ list.non_charge_consumption_change }}</span>
              </div>
              <div v-else class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6 color-8c">{{ $t('stationManagement.pp_no_data_from_yesterday') }}</div>
            </div>
          </div>
        </div>
        <div class="font-size-14 line-height-22 color-26 padding_t-12">
          <span>{{ $t('stationManagement.pp_station_suggestion') }}：</span>
          <span>{{ list.suggestion || '-' }}</span>
        </div>
      </div>

      <!-- 能效变化趋势 -->
      <div class="content-card">
        <div class="flex-box flex_j_c-space-between">
          <div class="font-size-14 line-height-22 color-26 font-weight-bold">{{ $t('stationManagement.pp_energy_trend') }}</div>
          <div class="flex-box flex_a_i-center gap_8">
            <span class="font-size-14 line-height-22 color-59">{{ $t('common.pp_time_frame') }}</span>
            <el-date-picker v-model="datePicker" type="daterange" @change="getList(true)" value-format="x" unlink-panels :shortcuts="getYesterdayShortcuts()" :clearable="false" :disabledDate="getDisabledYesterdayDate" :prefix-icon="customPrefix">
              <template #range-separator>
                <TimeRangeIcon style="margin: 0 5px" />
              </template>
            </el-date-picker>
          </div>
        </div>

        <div class="width-full flex-item_f-1" v-if="!loading2">
          <div id="energyDetailChart" class="width-full height-full" v-show="hasChartData"></div>
          <div v-if="!hasChartData" class="width-full flex-item_f-1 padding_t-24">
            <el-empty :description="$t('common.pp_image_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyImageIcon />
              </template>
            </el-empty>
          </div>
        </div>

        <div class="width-full flex-item_f-1 flex-box flex_j_c-center flex_a_i-center" v-else>
          <div class="loader-17"></div>
        </div>
      </div>
    </div>

    <div class="second-line" v-if="!loading1">
      <div class="content-card height-420 relative overflow-auto" ref="secondLine">
        <div class="font-size-14 line-height-22 color-26 font-weight-bold absolute">{{ $t('stationManagement.pp_diagram_explanation') }}</div>
        <div class="icon-box" :style="{ position: 'absolute', top: isPss2 ? '151px' : '191px', left: '50px' }">
          <PowerGridIcon />
          <div class="icon-title">{{ $t('stationManagement.pp_state_grid') }}</div>
        </div>
        <div class="icon-box" :style="{ position: 'absolute', top: isPss2 ? '151px' : '191px', left: '169px' }">
          <TransformerIcon />
          <div class="icon-title">{{ $t('stationManagement.pp_transformer') }}</div>
        </div>
        <div class="flex-box flex_d-column flex_a_i-center" :style="{ position: 'absolute', top: isPss2 ? '193px' : '233px', left: '247px' }">
          <div class="font-size-10 line-height-16 color-59 font-weight-bold" style="zoom: 0.8">{{ $t('stationManagement.pp_consumption') }}</div>
          <div class="font-size-12 line-height-18 color-26">{{ formatEmptyData(list.total_consumption) }}</div>
        </div>
        <div :style="{ position: 'absolute', top: isPss2 ? '127px' : '167px', left: '489px' }">
          <div class="font-size-10 line-height-16 color-59 font-weight-bold" style="zoom: 0.8">{{ $t('stationManagement.pp_charge_consumption') }}</div>
          <div class="font-size-12 line-height-18 color-26">{{ formatEmptyData(list.charge_consumption) }}</div>
        </div>
        <div :style="{ position: 'absolute', top: isPss2 ? '197px' : '237px', left: '489px' }">
          <div class="font-size-10 line-height-16 color-59 font-weight-bold" style="zoom: 0.8">{{ $t('stationManagement.pp_non_charge_consumption') }}</div>
          <div class="font-size-12 line-height-18 color-26">{{ formatEmptyData(list.non_charge_consumption) }}</div>
        </div>
        <div class="flex-box flex_d-column flex_a_i-center" :style="{ position: 'absolute', top: isPss2 ? '127px' : '167px', left: '718px', zIndex: 2 }">
          <div class="font-size-10 line-height-16 color-59 font-weight-bold" style="zoom: 0.8">{{ $t('stationManagement.pp_module_output') }}</div>
          <div class="font-size-12 line-height-18 color-26">{{ formatEmptyData(list.module_output_energy_total) }}</div>
        </div>
        <div class="flex-box flex_d-column flex_a_i-center" :style="{ position: 'absolute', top: isPss2 ? '127px' : '167px', left: '820px', zIndex: 2 }">
          <div class="font-size-10 line-height-16 color-59 font-weight-bold" style="zoom: 0.8">{{ $t('stationManagement.pp_battery_input') }}</div>
          <div class="font-size-12 line-height-18 color-26">{{ formatEmptyData(list.charging_energy) }}</div>
        </div>
        <div class="flex-box flex_d-column flex_a_i-center" :style="{ position: 'absolute', top: isPss2 ? '127px' : '167px', left: '1040px', zIndex: 2 }">
          <div class="font-size-10 line-height-16 color-59 font-weight-bold" style="zoom: 0.8">{{ $t('stationManagement.pp_actual_charged') }}</div>
          <div class="font-size-12 line-height-18 color-26">{{ formatEmptyData(list.charging_energy_available) }}</div>
        </div>
        <div style="position: absolute; top: 89px; left: 819px; z-index: 2" v-if="!isPss2">
          <div class="font-size-10 line-height-16 color-59 font-weight-bold" style="zoom: 0.8">{{ $t('stationManagement.pp_pile_consumption') }}</div>
          <div class="font-size-12 line-height-18 color-26">{{ formatEmptyData(list.charging_pile_energy) }}</div>
        </div>
        <div class="icon-box" :style="{ position: 'absolute', top: isPss2 ? '151px' : '191px', left: '351px' }">
          <StationIcon />
          <div class="icon-title">{{ $t('stationManagement.pp_detail_station') }}</div>
        </div>
        <div class="icon-data-box" :style="{ position: 'absolute', top: isPss2 ? '200px' : '240px', left: '625px' }">
          <UpsConsumption />
          <div class="icon-title">{{ $t('stationManagement.pp_ups') }}</div>
          <div class="icon-value">{{ formatEmptyData(list.ups_consumption) }}</div>
        </div>
        <div class="icon-data-box" :style="{ position: 'absolute', top: isPss2 ? '282px' : '322px', left: '625px' }">
          <DistributionCircuit />
          <div class="icon-title">{{ $t('stationManagement.pp_circuit_consumption') }}</div>
          <div class="icon-value">{{ formatEmptyData(list.water_consumption + list.mechanical_consumption + list.cooling_consumption + list.operation_consumption + list.light_consumption) }}</div>
        </div>
        <div class="icon-data-box" :style="{ position: 'absolute', top: isPss2 ? '282px' : '322px', left: '769px' }">
          <WaterCool />
          <div class="icon-title">{{ $t('stationManagement.pp_water_cooling') }}</div>
          <div class="icon-value">{{ formatEmptyData(list.water_consumption) }}</div>
        </div>
        <div class="icon-data-box" :style="{ position: 'absolute', top: isPss2 ? '282px' : '322px', left: '851px' }">
          <MechanicalConsumption />
          <div class="icon-title">{{ $t('stationManagement.pp_mechanical') }}</div>
          <div class="icon-value">{{ formatEmptyData(list.mechanical_consumption) }}</div>
        </div>
        <div class="icon-data-box" :style="{ position: 'absolute', top: isPss2 ? '282px' : '322px', left: '926px' }">
          <CoolingConsumption />
          <div class="icon-title">{{ $t('stationManagement.pp_cooling_fan') }}</div>
          <div class="icon-value">{{ formatEmptyData(list.cooling_consumption) }}</div>
        </div>
        <div class="icon-data-box" :style="{ position: 'absolute', top: isPss2 ? '282px' : '322px', left: '1001px' }">
          <OperationConsumption />
          <div class="icon-title">{{ $t('stationManagement.pp_operation') }}</div>
          <div class="icon-value">{{ formatEmptyData(list.operation_consumption) }}</div>
        </div>
        <div class="icon-data-box" :style="{ position: 'absolute', top: isPss2 ? '282px' : '322px', left: '1082px' }">
          <LightConsumption />
          <div class="icon-title">{{ $t('stationManagement.pp_light') }}</div>
          <div class="icon-value">{{ formatEmptyData(list.light_consumption) }}</div>
        </div>
        <div class="icon-box" :style="{ position: 'absolute', top: isPss2 ? '87px' : '127px', left: '616px' }">
          <PowerModule />
          <div class="font-size-12 line-height-18 color-be">{{ $t('stationManagement.pp_power_module') }}*n</div>
        </div>
        <div class="icon-box" style="position: absolute; top: 48px; left: 925px" v-if="!isPss2">
          <PileIcon />
          <div class="font-size-12 line-height-18 color-be">{{ $t('stationManagement.pp_pile') }}*n</div>
        </div>
        <div class="icon-box" style="position: absolute; top: 44px; left: 1045px" v-if="!isPss2">
          <CarBattery />
          <div class="font-size-12 line-height-18 color-be">{{ $t('stationManagement.pp_car_battery') }}*n</div>
        </div>
        <div class="icon-box" :style="{ position: 'absolute', top: isPss2 ? '86px' : '126px', left: '920px' }">
          <StationBattery />
          <div class="font-size-12 line-height-18 color-be">{{ $t('stationManagement.pp_station_battery') }}*n</div>
        </div>
        <div style="position: absolute; top: 35px; left: 603px; z-index: 0" v-if="isPss2">
          <Pss2Rect />
        </div>
        <div style="position: absolute; top: 35px; left: 603px; z-index: 0" v-else>
          <Pss3Rect />
        </div>
        <div class="font-size-14 line-height-22 color-be font-weight-bold" style="position: absolute; top: 49px; left: 619px; z-index: 2">{{ $t('stationManagement.pp_breakdown') }}</div>
        <canvas ref="canvasRef" width="2400" height="688" :style="{ position: 'absolute', left: '24px', top: isPss2 ? '4px' : '44px' }"></canvas>
      </div>
    </div>

    <div class="loading-container" v-else>
      <div class="loader-17"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, shallowRef, h, watch, computed, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { apiGetEnergyDetail } from '~/apis/station-management'
import { ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { lineBarOptionMock, formatTime, draw, formatEmptyData } from './constant'
import { getYesterdayShortcuts, getDisabledYesterdayDate, getStartTimeOfDay, getFinalEndTime, formatTimeToDay, isEmptyData } from '~/utils'
import BackIcon from '~/views/single-station/icon/back-icon.vue'
import CalendarIcon from './icon/calendar.vue'
import ChargeConsumption from './icon/charge-consumption.vue'
import NonChargeConsumption from './icon/non-charge-consumption.vue'
import PowerGridIcon from './icon/power-grid.vue'
import TransformerIcon from './icon/transformer-icon.vue'
import StationIcon from './icon/station-icon.vue'
import PowerModule from './icon/power-module.vue'
import UpsConsumption from './icon/ups-consumption.vue'
import DistributionCircuit from './icon/distribution-circuit.vue'
import WaterCool from './icon/water-cool.vue'
import MechanicalConsumption from './icon/mechanical-consumption.vue'
import CoolingConsumption from './icon/cooling-consumption.vue'
import OperationConsumption from './icon/operation-consumption.vue'
import LightConsumption from './icon/light-consumption.vue'
import PileIcon from './icon/pile.vue'
import StationBattery from './icon/station-battery.vue'
import CarBattery from './icon/car-battery.vue'
import Pss2Rect from './icon/pss2-rect.vue'
import Pss3Rect from './icon/pss3-rect.vue'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import WhiteEmptyImageIcon from '~/assets/svg/white-empty-image.vue'
import * as echarts from 'echarts'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const loading1 = ref(false)
const loading2 = ref(false)
const hasChartData = ref(false)
const isPss2 = ref(false)
const secondLine = ref(null)
const canvasRef = ref<HTMLCanvasElement>()
const list = ref({} as any)
const datePicker = ref([] as any)
const echartsArr = ref([] as any)
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const chartOption1 = cloneDeep(lineBarOptionMock)
const isCollapse = computed(() => store.state.menus.collapse)

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  echartsArr.value.push(myChart)
  window.addEventListener('resize', () => myChart.resize())
}
const setCharts = () => {
  echartsArr.value = []
  if (hasChartData.value) echartRender('energyDetailChart', chartOption1)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 回到上级页面
 * @return {*}
 */
const handleBack = () => {
  let query: any = sessionStorage.getItem('energy-list')
  router.push({
    path: '/station-management/energy-list',
    query: JSON.parse(query)
  })
}

const formatData = (data: any) => {
  if (data.daily_energy && data.daily_energy.length > 0) {
    hasChartData.value = true
    chartOption1.xAxis.data = data.daily_energy.map((item: any) => formatTime(item.day))
    chartOption1.yAxis[0].name = t('stationManagement.pp_order_volume')
    chartOption1.yAxis[1].name = t('stationManagement.pp_energy_proportion1')
    chartOption1.series[0].name = t('stationManagement.pp_order_volume')
    chartOption1.series[1].name = t('stationManagement.pp_energy_proportion1')
    chartOption1.series[0].data = data.daily_energy.map((item: any) => item.order_count)
    chartOption1.series[1].data = data.daily_energy.map((item: any) => item.energy)
  } else {
    hasChartData.value = false
  }
  nextTick(() => setCharts())
}

const getList = async (updateRoute = true) => {
  const params = {
    start_time: getStartTimeOfDay(datePicker.value[0]),
    end_time: getFinalEndTime(datePicker.value[1]),
    day: Number(route.query.day)
  }
  if (updateRoute) {
    // 折线图loading
    loading2.value = true
    router.push({
      path: location.pathname,
      query: { ...route.query, ...params }
    })
  } else {
    // 整个页面loading
    loading1.value = true
  }
  try {
    const res = await apiGetEnergyDetail(params, route.query.project, route.query.device_id)
    loading1.value = false
    loading2.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      formatData(list.value)
      nextTick(() => draw(canvasRef.value, isPss2.value))
    }
  } catch (error: any) {
    loading1.value = false
    loading2.value = false
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  isPss2.value = initParams.project === 'PowerSwap2'
  datePicker.value = initParams.start_time ? [Number(initParams.start_time), Number(initParams.end_time)] : [Number(initParams.day) - 3600 * 1000 * 24 * 7, Number(initParams.day)]
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.energy-detail-container {
  font-family: 'Blue Sky Standard';
  padding: 24px;
  background: linear-gradient(180deg, #eef6f8 -6.51%, #f4f5f8 12.89%);
  .first-line {
    margin-bottom: 16px;
    display: grid;
    grid-template-columns: minmax(360px, 1fr) 2fr;
    gap: 16px;
  }
  .content-card {
    min-height: 257px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    padding: 24px;
    display: flex;
    flex-direction: column;
    .consumption-data {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;
      border-bottom: 1px solid #dcf2f3;
    }
    canvas {
      width: 1200px;
      height: 344;
    }
    .icon-box {
      white-space: nowrap;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      .icon-title {
        font-size: 14px;
        line-height: 22px;
        color: #00bebe;
      }
    }
    .icon-data-box {
      white-space: nowrap;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      .icon-title {
        color: #00bebe;
        font-size: 12px;
        line-height: 16px;
        zoom: 0.8;
      }
      .icon-value {
        color: #01a0ac;
        font-size: 12px;
        line-height: 18px;
      }
    }
  }
  .loading-container {
    height: calc(100vh - 100px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.el-empty) {
    padding: 0;
    .el-empty__image {
      width: 120px;
    }
  }
  :deep(.el-date-editor .el-range-input) {
    color: #262626;
  }
  :deep(.el-range-editor.el-input__wrapper) {
    width: 280px;
  }
}
</style>
