<template>
  <div class="single-station-container">
    <div class="header-container">
      <div class="flex-box flex_a_i-center font-size-20 line-height-28 margin_b-12">
        <BackIcon @click="changeToPre" class="cursor-pointer" />
        <span style="color: #8c8c8c; font-weight: 480; margin-left: 4px">{{ $t('menu.pp_device_detail') }} /</span>
        <span style="color: #1f1f1f; font-weight: 420">&nbsp;{{ data?.description + ' ' + data?.device_id }}</span>
        <div class="tag-container" :style="{ background: projectColorMap[project.project].background, color: projectColorMap[project.project].color }">
          <pss1Icon v-if="project.version == 1" />
          <pss2Icon v-else-if="project.version == 2" />
          <pss3Icon v-else-if="project.version == 3" />
          <pss4Icon v-else-if="project.version == 4" />
          <firefly1Icon v-else />
          <span>{{ $t(projectColorMap[project.project].name) }}</span>
        </div>
      </div>
      <el-tabs v-model="activeTab" @tab-click="handleClick" v-if="project.route !== 'firefly1'">
        <el-tab-pane :label="$t('menu.pp_service_list')" name="service" v-if="hasPermission(`powerSwap${project.version}:single-station:service`)"> </el-tab-pane>
        <el-tab-pane :label="$t('menu.pp_image_list')" name="image" v-if="hasPermission(`powerSwap${project.version}:single-station:image`)"> </el-tab-pane>
        <el-tab-pane :label="$t('menu.pp_tank_trans_list')" name="tank" v-if="hasPermission(`powerSwap${project.version}:single-station:tank`)"> </el-tab-pane>
        <el-tab-pane :label="$t('serviceDetail.pp_operation_log')" name="operation_log" v-if="hasPermission(`powerSwap${project.version}:single-station:operation_log`)"> </el-tab-pane>
        <el-tab-pane :label="$t('orderList.pp_order_list')" name="order" v-if="hasPermission(`powerSwap${project.version}:single-station:order`)"> </el-tab-pane>
        <el-tab-pane :label="$t('alarmList.pp_alarm_list')" name="alarm" v-if="hasPermission(`powerSwap${project.version}:single-station:alarm`)"> </el-tab-pane>
        <el-tab-pane :label="$t('menu.pp_flash_log')" name="flash" v-if="hasPermission(`powerSwap${project.version}:single-station:flash`)"> </el-tab-pane>
      </el-tabs>

      <el-tabs v-model="activeTab" @tab-click="handleClick" v-else>
        <el-tab-pane :label="$t('menu.pp_service_list')" name="service" v-if="hasPermission(`firefly1:single-station:service`)"> </el-tab-pane>
        <el-tab-pane :label="$t('menu.pp_image_list')" name="image" v-if="hasPermission(`firefly1:single-station:image`)"> </el-tab-pane>
        <el-tab-pane :label="$t('alarmList.pp_alarm_list')" name="alarm" v-if="hasPermission(`firefly1:single-station:alarm`)"> </el-tab-pane>
      </el-tabs>
    </div>

    <div class="content-container" v-if="isAuthorized">
      <div v-if="activeTab === 'service'">
        <ServiceList />
      </div>
      <div v-else-if="activeTab === 'image'">
        <ImageList />
      </div>
      <div v-else-if="activeTab === 'tank'">
        <TankList />
      </div>
      <div v-else-if="activeTab === 'operation_log'">
        <OperationRecording />
      </div>
      <div v-else-if="activeTab === 'order'">
        <OrderList />
      </div>
      <div v-else-if="activeTab === 'alarm'">
        <AlarmList />
      </div>
      <div v-else-if="activeTab === 'flash'">
        <FlashLog />
      </div>
    </div>
    <div v-else style="padding: 50px">
      <span class="lack-permission">{{ $t('common.pp_lack_permission') }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { projectColorMap } from './constant'
import { apiGetDeviceList } from '~/apis/device-management'
import { useStore } from 'vuex'
import { hasPermission } from '~/auth'
import type { TabsPaneContext } from 'element-plus'
import ServiceList from './station-service-list.vue'
import ImageList from './station-image-list.vue'
import TankList from './station-tank-list.vue'
import OperationRecording from './station-operation-log.vue'
import OrderList from './station-order-list.vue'
import AlarmList from './station-alarm-list.vue'
import FlashLog from './station-flash-log.vue'
import BackIcon from './icon/back-icon.vue'
import pss1Icon from './icon/pss1-icon.vue'
import pss2Icon from './icon/pss2-icon.vue'
import pss3Icon from './icon/pss3-icon.vue'
import pss4Icon from './icon/pss4-icon.vue'
import firefly1Icon from './icon/firefly1-icon.vue'
import 'swiper/css'

const activeTab = ref('service')

const $router = useRouter()
const $route = useRoute()
const $store = useStore()
const project = ref(computed(() => $store.state.project))
const data: any = ref({})
const isAuthorized = computed(() => (project.value.route !== 'firefly1' && hasPermission(`powerSwap${project.value.version}:single-station:${activeTab.value}`)) || (project.value.route === 'firefly1' && hasPermission(`firefly1:single-station:${activeTab.value}`)))

// 回到上级页面
const changeToPre = () => {
  let query: any = sessionStorage.getItem('device-management')
  $router.push({
    path: `/${project.value.route}/device-management`,
    query: JSON.parse(query)
  })
}

onBeforeMount(() => {
  if ($route.query.tab) {
    activeTab.value = `${$route.query.tab}`
  }
  data.device_id = `${$route.params.deviceId}`

  apiGetDeviceList({ device: data.device_id, page_no: 1, page_size: 10 }, project.value.project).then((res) => {
    data.value = res.data?.[0]
  })
})

// 切换Tab
const handleClick = (tab: TabsPaneContext, event: Event) => {
  let query = {
    tab: tab.paneName
  }
  $router.push({
    path: $router.currentRoute.value.path,
    query: query
  })
}
</script>

<style lang="scss" scoped>
.single-station-container {
  font-family: 'Blue Sky Standard';
  background: linear-gradient(180deg, #e2f9f9 -6.51%, rgba(255, 255, 255, 0.5) 12.89%);
  .header-container {
    padding: 24px 24px 20px;
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
    .tag-container {
      height: 26px;
      display: flex;
      align-items: center;
      gap: 6px;
      border-radius: 2px;
      padding: 2px 8px;
      font-size: 14px;
      line-height: 22px;
      margin-left: 8px;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-date-editor .el-range-input {
          color: #262626;
        }
      }
    }
  }
}
</style>
