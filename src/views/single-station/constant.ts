export const projectColorMap = {
  FYPUS1: {
    name: 'menu.pp_firefly1',
    color: '#2F54EB',
    background: '#F0F5FF',
  },
  PowerSwap: {
    name: 'common.pp_pss1',
    color: '#FD8C08',
    background: '#FFF8DD',
  },
  PowerSwap2: {
    name: 'common.pp_pss2',
    color: '#2F9C74',
    background: '#E8FCEA',
  },
  PUS3: {
    name: 'common.pp_pss3',
    color: '#01A0AC',
    background: '#E5F9F9',
  },
  PUS4: {
    name: 'common.pp_pss4',
    color: '#21819D',
    background: '#E6F8FF',
  },
} as any;

export const statusOptions = [
  {
    label: 'station.pp_normal',
    value: 1,
  },
  {
    label: 'station.pp_abnormal',
    value: 0,
  },
  {
    label: 'station.pp_unfinished',
    value: -1,
  },
];

export const statusNameMap = {
  '1': 'station.pp_normal',
  '0': 'station.pp_abnormal',
  '-1': 'station.pp_unfinished',
} as any;

export const isStuckOptions = [
  {
    label: 'common.pp_yes',
    value: true,
  },
  {
    label: 'common.pp_no',
    value: false,
  },
];

export const alarmLevelMap = {
  0: {
    name: 'alarmList.pp_unknown_alarm',
    color: '#595959',
    background: '#EBECEE',
  },
  1: {
    name: 'alarmList.pp_first_level',
    color: '#FD8C08',
    background: '#FFF8DD',
  },
  2: {
    name: 'alarmList.pp_second_level',
    color: '#FF772E',
    background: '#FFF4E8',
  },
  3: {
    name: 'alarmList.pp_third_level',
    color: '#E83030',
    background: '#FFF2F0',
  },
} as any;

export const alarmStatusMap = {
  1: {
    name: 'alarmList.pp_cleared',
    background: '#67C23A',
  },
  2: {
    name: 'alarmList.pp_alarming',
    background: '#FF7575',
  },
  3: {
    name: 'alarmList.pp_unknown',
    background: '#888AF7',
  },
} as any;

export const refreshResultOptions = [
  {
    label: 'station.pp_flash_success',
    value: '1',
  },
  {
    label: 'station.pp_flash_fail',
    value: '2',
  },
  {
    label: 'station.pp_flash_overdue',
    value: '3',
  },
];

export const refreshResultMap = {
  '1': {
    name: 'station.pp_flash_success',
    color: '#52C31C',
  },
  '2': {
    name: 'station.pp_flash_fail',
    color: '#FD4348',
  },
  '3': {
    name: 'station.pp_flash_overdue',
    color: '#FD8C08',
  },
} as any;

export const refreshTypeOptions = [
  {
    label: 'station.pp_ui',
    value: '0',
  },
  {
    label: 'station.pp_select_battery',
    value: '1',
  },
  {
    label: 'station.pp_preselect_battery',
    value: '2',
  },
  {
    label: 'station.pp_cloud_push',
    value: '3',
  },
  {
    label: 'station.pp_aes_key',
    value: '4',
  },
];

export const refreshTypeMap = {
  '0': {
    name: 'station.pp_ui',
    color: '#FF772E',
    background: '#FFF4E8',
  },
  '1': {
    name: 'station.pp_select_battery',
    color: '#2F9C74',
    background: '#E8FCEA',
  },
  '2': {
    name: 'station.pp_preselect_battery',
    color: '#1880F2',
    background: '#E6F8FF',
  },
  '3': {
    name: 'station.pp_cloud_push',
    color: '#FD8C08',
    background: '#FFF8DD',
  },
  '4': {
    name: 'station.pp_aes_key',
    color: '#2F54EB',
    background: '#F0F5FF',
  },
} as any;
