<template>
  <div class="operation-log-container">
    <el-radio-group v-model="searchForm.type" @change="handleChangeType" v-if="project.project == 'PUS3'">
      <el-radio :label="1">{{ $t('serviceDetail.pp_log_local') }}</el-radio>
      <el-radio :label="2">{{ $t('serviceDetail.pp_log_remote') }}</el-radio>
    </el-radio-group>

    <el-form :model="searchForm">
      <el-form-item :label="$t('common.pp_time_frame')" class="width-full">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="shortcuts" :clearable="false" :disabledDate="getDisabledDate">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="searchForm.type == 1" :label="$t('serviceDetail.pp_operation_interface')" class="width-full">
        <el-input v-model="searchForm.operation_interface" :placeholder="$t('common.pp_please_input')" clearable />
      </el-form-item>
      <el-form-item v-if="searchForm.type == 1" :label="$t('serviceDetail.pp_operation_description')" class="width-full">
        <el-input v-model="searchForm.operation_description" :placeholder="$t('common.pp_please_input')" clearable />
      </el-form-item>
      <el-form-item v-if="searchForm.type == 1" :label="$t('serviceDetail.pp_operation_people')" class="width-full">
        <el-select v-model="searchForm.operator" clearable filterable remote :placeholder="$t('stuckAnalysis.pp_user_placeholder')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
          <el-option v-for="item in userOptions" :key="item.employee_id" :value="item.worker_user_id" :label="item.name">
            <span style="float: left">{{ item.name }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.worker_user_id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="filterEvent" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
        <el-button @click="resetSelect" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 本地 -->
    <el-table :data="tableList" v-loading="loading" v-if="searchForm.type == 1" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="timestamp" :label="$t('serviceDetail.pp_operation_trigger_time')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatLocaleDate(scope.row.timestamp, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="page" :label="$t('serviceDetail.pp_operation_interface')" min-width="250" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.page || scope.row.operation }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="action" :label="$t('common.pp_operation')" min-width="90" show-overflow-tooltip />
      <el-table-column prop="args" :label="`${$t('serviceDetail.pp_input_parameters')}/${$t('serviceDetail.pp_return_data')}`" min-width="260" show-overflow-tooltip />
      <el-table-column v-if="project.project == 'PUS3'" prop="module" :label="$t('serviceDetail.pp_log_module')" min-width="100" show-overflow-tooltip />
      <el-table-column prop="button" :label="$t('serviceDetail.pp_operation_description')" min-width="180" show-overflow-tooltip />
      <el-table-column prop="user_id" :label="$t('serviceDetail.pp_operation_people')" min-width="180" show-overflow-tooltip />
    </el-table>

    <!-- 云端 -->
    <el-table :data="tableList" v-loading="loading" v-else :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="timestamp" :label="$t('serviceDetail.pp_log_time')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatLocaleDate(scope.row.timestamp, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="ability_code" :label="$t('serviceDetail.pp_log_name')" min-width="260" show-overflow-tooltip />
      <el-table-column prop="is_fail" :label="$t('serviceDetail.pp_log_status')" min-width="100" show-overflow-tooltip>
        <template #default="scope">
          <div class="flex-box flex_a_i-center">
            <el-icon :size="18" color="#F53F3F" v-if="scope.row.is_fail"><CircleCloseFilled /></el-icon>
            <el-icon :size="18" color="#00B42A" v-else><SuccessFilled /></el-icon>
            <span class="margin_l-6">{{ scope.row.is_fail ? $t('serviceDetail.pp_log_fail') : $t('serviceDetail.pp_log_success') }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="failure_reason" :label="$t('serviceDetail.pp_log_reason')" min-width="220" show-overflow-tooltip />
      <el-table-column :label="$t('common.pp_operation')" min-width="100" show-overflow-tooltip>
        <template #default="scope">
          <span @click="handleViewParams(scope.row)" class="light-column">{{ $t('serviceDetail.pp_log_params') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="common-pagination">
      <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <el-dialog v-model="paramsDialogVisible" v-if="paramsDialogVisible" :title="$t('serviceDetail.pp_params_set')" width="620px" align-center draggable :close-on-press-escape="false">
      <div class="dialog-title">
        <span>{{ $t('serviceDetail.pp_log_name') }}：</span>
        <span>{{ commandName }}</span>
      </div>
      <el-table :data="paramsList" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" max-height="440">
        <el-table-column prop="param_code" :label="$t('serviceDetail.pp_params_code')" min-width="260" show-overflow-tooltip class-name="columu-style" />
        <el-table-column prop="param_value" :label="$t('serviceDetail.pp_params_value')" min-width="300" show-overflow-tooltip class-name="columu-style" />
      </el-table>
      <div class="flex-box flex_j_c-center margin_t-20 margin_b-24">
        <el-button class="welkin-primary-button" @click="paramsDialogVisible = false">{{ $t('common.pp_close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, computed } from 'vue'
import { formatLocaleDate, getShortcuts, getDisabledDate, toQueryString } from '~/utils'
import { useRouter, useRoute } from 'vue-router'
import { apiGetUserList } from '~/apis/run-list'
import { apiGetOperationLog, apiGetPus3OperationLog } from '~/apis/plc-record'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { pagination } from '~/constvars'
import { ElMessage } from 'element-plus'
import { SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import 'swiper/css'
import _ from 'lodash'

const { t } = useI18n()
const $store = useStore()
const $router = useRouter()
const $route = useRoute()

const project = ref(computed(() => $store.state.project))

const loading = ref(false)
const remoteLoading = ref(false)
const paramsDialogVisible = ref(false)
const commandName = ref('')
const paramsList = ref([])
const userOptions = ref([] as any)
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date()] as any)

const searchForm = reactive({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  page: 1,
  size: 10,
  type: 1,
  operation_interface: '',
  operation_description: '',
  operator: '',
  descending: true
})

const tableList = ref([] as any)
const totalNumber = ref(0)
const shortcuts = ref(getShortcuts())

/**
 * @description: 远程搜索用户
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchUserList(query)
}
const searchUserList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { fuzzy_name: val }
    const res = await apiGetUserList(params)
    remoteLoading.value = false
    userOptions.value = res.data.people_list
  }
}, 500)

const handleViewParams = (row: any) => {
  commandName.value = row.ability_code
  paramsList.value = row.ability_params || []
  paramsDialogVisible.value = true
}

// 公用搜索事件
const publicSearch = async () => {
  let formData = _.cloneDeep(searchForm) as any
  if (formData.type == 2) {
    delete formData.operation_description
    delete formData.operation_interface
    delete formData.operator
  }
  if (project.value.project != 'PUS3') delete formData.type
  const query = {
    ...formData,
    tab: 'operation_log'
  }
  $router.push(`${$route.path + toQueryString(query)}`)
  loading.value = true
  try {
    const res = project.value.project == 'PUS3' ? await apiGetPus3OperationLog(project.value.project, $route.params.deviceId, formData) : await apiGetOperationLog(project.value.project, $route.params.deviceId, formData)
    loading.value = false
    if (!res.err_code) {
      tableList.value = res.data
      totalNumber.value = res.total
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    loading.value = false
  }
}

// 切换本地/云端
const handleChangeType = () => {
  searchForm.page = 1
  publicSearch()
}

// 重置
const resetSelect = () => {
  datePicker.value = [new Date(new Date().toLocaleDateString()).getTime(), new Date().getTime()]
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
  searchForm.end_time = new Date().getTime()
  if (searchForm.type == 1) {
    searchForm.operation_description = ''
    searchForm.operation_interface = ''
    searchForm.operator = ''
  }
  filterEvent()
}

const handleDateChange = (value: any) => {
  if (value) {
    searchForm.start_time = value[0].valueOf()
    searchForm.end_time = value[1].valueOf()
  } else {
    searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
    searchForm.end_time = new Date().getTime()
  }
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  searchForm.size = val
  searchForm.page = 1
  publicSearch()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  searchForm.page = val
  publicSearch()
}

// 点击筛选按钮
const filterEvent = () => {
  searchForm.page = 1
  publicSearch()
}

onBeforeMount(() => {
  let init_params: any = $route.query
  if (init_params.tab == 'operation_log') {
    searchForm.page = init_params.page ? Number(init_params.page) : 1
    searchForm.size = init_params.size ? Number(init_params.size) : 10
    searchForm.type = init_params.type ? Number(init_params.type) : 1
    searchForm.start_time = init_params.start_time ? Number(init_params.start_time) : new Date(new Date().toLocaleDateString()).getTime()
    searchForm.end_time = init_params.end_time ? Number(init_params.end_time) : new Date().getTime()
    datePicker.value = [searchForm.start_time, searchForm.end_time]
    searchForm.operation_description = init_params.operation_description ? init_params.operation_description : ''
    searchForm.operation_interface = init_params.operation_interface ? init_params.operation_interface : ''
    searchForm.operator = init_params.operator ? init_params.operator : ''
  }
  if (searchForm.operator) searchUserList(searchForm.operator)
  publicSearch()
})
</script>

<style lang="scss" scoped>
.operation-log-container {
  font-family: 'Blue Sky Standard';
  .dialog-title {
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 16px;
    color: #262626;
  }
  :deep(.el-dialog__header) {
    padding: 24px 24px 16px;
  }
  :deep(.el-dialog__body) {
    padding: 0px 24px;
  }
  :deep(.el-radio-group) {
    margin-top: -6px;
    margin-bottom: 12px;
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  :deep(.el-date-editor .el-range-input) {
    color: #262626;
  }
  :deep(.el-form) {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px 24px;
    .el-range-editor.el-input__wrapper {
      padding: 0;
    }
    .el-date-editor .el-range__icon {
      margin-right: 4px;
    }
  }
}
</style>
