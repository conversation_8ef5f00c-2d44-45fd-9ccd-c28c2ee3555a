<template>
  <div class="power-grid-container">
    <el-form :model="form" class="grid-search-form">
      <el-form-item :label="$t('orderList.pp_order_create_time')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :shortcuts="shortcuts" :disabledDate="getDisabledDate" />
      </el-form-item>

      <el-form-item :label="$t('orderList.pp_order_name')">
        <el-input v-model="form.name" class="width-full" :placeholder="$t('common.pp_please_input')" clearable />
      </el-form-item>

      <el-form-item :label="$t('orderList.pp_order_type')">
        <el-select v-model="form.type" class="width-full" :placeholder="$t('common.pp_please_select')" clearable filterable>
          <el-option v-for="item in typeOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('orderList.pp_order_status')">
        <el-select v-model="form.err_code" class="width-full" :placeholder="$t('common.pp_please_select')" clearable filterable>
          <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleSearch" class="welkin-primary-button">{{ $t('common.pp_search') }}</el-button>
        <el-button @click="handleReset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="list" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="name" :label="$t('orderList.pp_order_name')" min-width="180" show-overflow-tooltip />
      <el-table-column prop="type" :label="$t('orderList.pp_order_type')" min-width="150" show-overflow-tooltip>
        <template #default="scope">
          <span :style="{ background: typeColorMap[scope.row.type], display: 'inline-block', borderRadius: '20px', padding: '0 6px', color: '#212121' }">&nbsp;{{ $t(typeMap[scope.row.type]) }}&nbsp;</span>
        </template>
      </el-table-column>
      <el-table-column prop="err_code" :label="$t('orderList.pp_order_status')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          <div class="status-container">
            <el-icon :size="18" color="#666" v-if="scope.row.err_code === 1"><RemoveFilled /></el-icon>
            <el-icon :size="18" color="#00B42A" v-if="scope.row.err_code === 4 || scope.row.err_code === 7"><SuccessFilled /></el-icon>
            <el-icon :size="18" color="#F53F3F" v-if="scope.row.err_code === 2 || scope.row.err_code === 5"><CircleCloseFilled /></el-icon>
            <span v-if="scope.row.err_code === 3 || scope.row.err_code === 6"> <LoadingSvg /> </span>
            <el-popover v-if="scope.row.err_code === 2 || scope.row.err_code === 5" placement="top-start" :title="`${$t('orderList.pp_fail_reason')}`" :width="200" trigger="hover" :content="scope.row.fail_reason">
              <template #reference>
                <span class="status-text light-text">{{ $t(statusMap[scope.row.err_code]) }}</span>
              </template>
            </el-popover>
            <span class="status-text" v-else>{{ $t(statusMap[scope.row.err_code]) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" :label="$t('orderList.pp_order_create_time')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatTime(scope.row.create_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="command_push_time" :label="$t('orderList.pp_order_pushtime')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatTime(scope.row.command_push_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="push_finish_time" :label="$t('orderList.pp_order_finish_time')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatTime(scope.row.push_finish_time) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.pp_operation')" width="100" fixed="right">
        <template #default="scope">
          <span class="welkin-operation-button" @click="handleView(scope.row)">{{ $t('common.pp_view') }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="common-pagination">
      <Page :page="pages" @change="handlePageChange" />
    </div>

    <!-- 查看 -->
    <el-dialog v-model="viewDialogVisible" :title="$t('common.pp_view')" width="650px" :close-on-click-modal="false">
      <el-form ref="viewFormRef" label-position="left" :model="viewForm" label-width="170px">
        <el-form-item :label="$t('orderList.pp_order_name')" prop="name">
          <el-input v-model="viewForm.name" disabled />
        </el-form-item>
        <el-form-item :label="$t('orderList.pp_order_user')" prop="creator">
          <el-input v-model="viewForm.creator" disabled />
        </el-form-item>
        <el-form-item :label="$t('orderList.pp_res_t_start')" prop="start_time_reserved">
          <el-input v-model="viewForm.start_time_reserved" disabled />
        </el-form-item>
        <el-form-item :label="$t('orderList.pp_res_t_end')" prop="end_time_reserved">
          <el-input v-model="viewForm.end_time_reserved" disabled />
        </el-form-item>
        <el-form-item :label="$t('orderList.pp_max_capacity')" prop="winning_bid_up_capacity" v-if="viewForm.type === 'win'">
          <el-input v-model="viewForm.winning_bid_up_capacity" disabled>
            <template #append>kWh</template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('orderList.pp_price')" prop="winning_bid_price" v-if="viewForm.type === 'win'">
          <el-input v-model="viewForm.winning_bid_price" disabled>
            <template #append>€/MW</template>
          </el-input>
        </el-form-item>
        <el-form-item v-for="(item, key) in viewForm.reserved_batteries" :label="key + 'kwh' + $t('orderList.pp_battery_res_num')">
          <el-input :placeholder="item + ''" disabled>
            <template #append>{{ $t('orderList.pp_piece') }}</template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('orderList.pp_revoke_start')" prop="command_revoke_time" v-if="[5, 6, 7].includes(viewForm.err_code)">
          <el-input v-model="viewForm.command_revoke_time" disabled />
        </el-form-item>
        <el-form-item :label="$t('orderList.pp_revoke_end')" prop="revoke_finish_time" v-if="[5, 6, 7].includes(viewForm.err_code)">
          <el-input v-model="viewForm.revoke_finish_time" disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="viewDialogVisible = false" class="welkin-primary-button">{{ $t('common.pp_close') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onBeforeUnmount, computed } from 'vue'
import { getShortcuts, getDisabledDate, removeNullProp, clearJson, formatTime } from '~/utils'
import { page } from '~/constvars/page'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { statusMap, typeMap, typeColorMap } from '../order-list/constant'
import { SuccessFilled, CircleCloseFilled, RemoveFilled } from '@element-plus/icons-vue'
import { apiGetSingleStationList } from '~/apis/order-list'
import _ from 'lodash'

const $store = useStore()
const project = ref(computed(() => $store.state.project))

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const viewFormRef = ref()
const getListTimeout = ref()
const loading = ref(false)
const viewDialogVisible = ref(false)
const datePicker = ref([] as any)
const shortcuts = ref(getShortcuts())
const pages = ref(_.cloneDeep(page))
const list = ref([] as any)
const typeOptions = ref([
  {
    label: 'orderList.pp_reserve_order',
    value: 'reserve'
  },
  {
    label: 'orderList.pp_bid_order',
    value: 'win'
  }
])
const statusOptions = ref([
  {
    label: 'orderList.pp_stat_1',
    value: 1
  },
  {
    label: 'orderList.pp_stat_2',
    value: 2
  },
  {
    label: 'orderList.pp_stat_3',
    value: 3
  },
  {
    label: 'orderList.pp_stat_4',
    value: 4
  },
  {
    label: 'orderList.pp_stat_5',
    value: 5
  },
  {
    label: 'orderList.pp_stat_6',
    value: 6
  },
  {
    label: 'orderList.pp_stat_7',
    value: 7
  }
])
const form = ref({
  name: '',
  start_time: '' as number | string,
  end_time: '' as number | string,
  err_code: '' as number | string,
  type: ''
})
const searchForm = ref({
  name: '',
  start_time: '' as number | string,
  end_time: '' as number | string,
  err_code: '' as number | string,
  type: ''
})
const viewForm = ref({} as any)

/**
 * @description: 选择时间范围
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  if (value) {
    form.value.start_time = value[0].getTime()
    form.value.end_time = value[1].getTime()
  } else {
    form.value.start_time = ''
    form.value.end_time = ''
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  datePicker.value = []
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = { ...form.value }
  getList(true, 2)
}

/**
 * @description: 查看
 * @param {*} row
 * @return {*}
 */
const handleView = (row: any) => {
  viewForm.value = _.cloneDeep(row)
  viewForm.value.start_time_reserved = formatTime(viewForm.value.start_time_reserved)
  viewForm.value.end_time_reserved = formatTime(viewForm.value.end_time_reserved)
  viewForm.value.command_revoke_time = formatTime(viewForm.value.command_revoke_time)
  viewForm.value.revoke_finish_time = formatTime(viewForm.value.revoke_finish_time)
  viewDialogVisible.value = true
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList(true, 2)
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @param {*} status // 轮询是否实时带筛选项
 * @return {*}
 */
const getList = async (updateRoute = true, status: number) => {
  if (loading.value) return
  clearTimeout(getListTimeout.value)
  let formData = {} as any
  if (status === 2) {
    formData = { ...searchForm.value }
  } else {
    formData = { ...form.value }
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  if (updateRoute) {
    router.push({
      path: route.path,
      query: { ...removeNullProp(formData), tab: 'order' }
    })
  }
  loading.value = true
  try {
    const res = await apiGetSingleStationList(formData, project.value.project, route.params.deviceId)
    list.value = res.data
    pages.value.total = res.total
    getListTimeout.value = setTimeout(() => {
      getList(false, 2)
    }, 5000)
  } catch (error) {}
  loading.value = false
}

const stopWatch = watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log(newPath, oldPath, 111)
    if (!oldPath) {
      let initParams: any = route.query
      if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time) && initParams.tab == 'order') {
        form.value.start_time = Number(initParams.start_time)
        form.value.end_time = Number(initParams.end_time)
        datePicker.value[0] = Number(form.value.start_time)
        datePicker.value[1] = Number(form.value.end_time)
      }
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      form.value.name = !!initParams.name ? initParams.name : ''
      form.value.err_code = !!initParams.err_code ? Number(initParams.err_code) : ''
      form.value.type = initParams.type == 'win' || initParams.type == 'reserve' ? initParams.type : ''
      searchForm.value = { ...form.value }
      getList(false, 2)
    }
  },
  { immediate: true }
)

/**
 * @description: 组件销毁卸载轮询，定时器清空, 停止监听路由
 * @return {*}
 */
onBeforeUnmount(() => {
  clearTimeout(getListTimeout.value)
  stopWatch()
})
</script>

<style lang="scss" scoped>
.power-grid-container {
  font-family: 'Blue Sky Standard';
  :deep(.grid-search-form) {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px 24px;
  }
  :deep(.el-dialog__header) {
    padding: 24px 24px 16px;
  }
  :deep(.el-dialog__body) {
    padding: 0px 24px;
    .el-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }
  .status-container {
    display: flex;
    align-items: center;
    line-height: 17px;
    .status-text {
      padding-left: 8px;
    }
    .light-text {
      color: #f53f3f;
    }
  }
}
</style>
