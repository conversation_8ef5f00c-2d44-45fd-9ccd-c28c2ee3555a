<template>
  <div class="station-tank-container">
    <el-form :model="searchForm">
      <el-form-item :label="$t('common.pp_time_frame')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="shortcuts" :clearable="false" :disabledDate="getDisabledDate">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>

      <el-form-item :label="$t('tankTrans.pp_releated_slot')">
        <el-select v-model="searchForm.slot" multiple collapse-tags collapse-tags-tooltip clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in slotArr" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('batterySwap.pp_turnover_id')">
        <el-input v-model="searchForm.trans_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
      </el-form-item>

      <el-form-item>
        <el-button class="welkin-primary-button" :loading="loading" @click="filterEvent">
          {{ $t('common.pp_search') }}
        </el-button>
        <el-button @click="resetSelect" class="welkin-secondary-button">
          {{ $t('common.pp_reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="trans_start_ts" :label="$t('common.pp_start_time')" min-width="170" show-overflow-tooltip>
        <template #default="scope">
          {{ formatLocaleDate(scope.row.trans_start_ts, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="trans_end_ts" :label="$t('common.pp_end_time')" min-width="170" show-overflow-tooltip>
        <template #default="scope">
          {{ formatLocaleDate(scope.row.trans_end_ts, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="duration" :label="$t('tankTrans.pp_trans_duration')" min-width="140" show-overflow-tooltip />
      <el-table-column prop="full_charged_slot" :label="$t('tankTrans.pp_full_charged_slot')" min-width="140" show-overflow-tooltip />
      <el-table-column prop="drianed_slot" :label="$t('tankTrans.pp_drianed_slot')" min-width="140" show-overflow-tooltip />
      <el-table-column prop="empty_slot" :label="$t('tankTrans.pp_empty_slot')" min-width="140" show-overflow-tooltip />
      <el-table-column prop="trans_id" :label="$t('batterySwap.pp_turnover_id')" min-width="240">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.trans_id" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('common.pp_operation')" min-width="100">
        <template #default="scope">
          <div class="flex-box flex_a_i-center">
            <el-tooltip effect="dark" :content="$t('menu.pp_tank_trans_detail')" placement="bottom">
              <ServiceIcon @click="viewDetail(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="common-pagination">
      <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, computed } from 'vue'
import { formatLocaleDate, getDuration, getShortcuts, getDisabledDate, removeNullKeys, toQueryString, clearJson } from '~/utils'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { apiGetTankTransList } from '~/apis/tank-trans'
import { slotArr } from '~/constvars/tank-trans'
import { useStore } from 'vuex'
import { pagination } from '~/constvars'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import ServiceIcon from '~/assets/svg/service-icon.vue'
import 'swiper/css'

const $store = useStore()
const $router = useRouter()
const $route = useRoute()

const project = ref(computed(() => $store.state.project))

const loading = ref(false)

const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date()] as any)

const searchForm = reactive({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  slot: '',
  trans_id: '',
  page: 1,
  size: 10,
  descending: true
})

const tableList = ref([] as any)
const totalNumber = ref(0)
const shortcuts = ref(getShortcuts())

// 公用搜索事件
const publicSearch = () => {
  removeNullKeys(searchForm)
  const query = {
    ...searchForm,
    tab: 'tank'
  }
  $router.push(`${$route.path + toQueryString(query)}`)

  return apiGetTankTransList(project.value.project, $route.params.deviceId, searchForm)
    .then((res) => {
      tableList.value = res.data || []
      totalNumber.value = res.total
      tableList.value.map((item: any) => {
        item.duration = getDuration(item.trans_start_ts, item.trans_end_ts)
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置
const resetSelect = () => {
  clearJson(searchForm)
  datePicker.value = [new Date(new Date().toLocaleDateString()).getTime(), new Date().getTime()]
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
  searchForm.end_time = new Date().getTime()
  filterEvent()
}

// 查看倒仓详情
const viewDetail = (index: any, row: any) => {
  sessionStorage.setItem('station-tank-list', JSON.stringify($route.query))

  $router.push({
    path: `/${project.value.route}/device-management/single-station/${$route.params.deviceId}/tank-trans/${row.trans_id}`,
    query: {
      start_time: row.trans_start_ts,
      end_time: row.trans_end_ts
    }
  })
}

const handleDateChange = (value: any) => {
  if (value) {
    searchForm.start_time = value[0].valueOf()
    searchForm.end_time = value[1].valueOf()
  } else {
    searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
    searchForm.end_time = new Date().getTime()
  }
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true
  searchForm.size = val
  searchForm.page = 1
  publicSearch()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true
  searchForm.page = val
  publicSearch()
}

// 点击筛选按钮
const filterEvent = () => {
  loading.value = true
  searchForm.size = 10
  searchForm.page = 1
  publicSearch()
}

onBeforeMount(() => {
  loading.value = true
  let init_params: any = $route.query
  if (init_params.tab == 'tank' && !isNaN(init_params.start_time) && !isNaN(init_params.end_time)) {
    searchForm.start_time = Number(init_params.start_time)
    searchForm.end_time = Number(init_params.end_time)
    datePicker.value[0] = Number(init_params.start_time)
    datePicker.value[1] = Number(init_params.end_time)

    searchForm.page = !!init_params.page ? Number(init_params.page) : 1
    searchForm.size = !!init_params.size ? Number(init_params.size) : 10
    searchForm.trans_id = !!init_params.trans_id ? init_params.trans_id : ''
    if (!!init_params.slot) {
      searchForm.slot = init_params.slot.split(',')
    }
    publicSearch()
  } else {
    resetSelect()
  }
})
</script>

<style lang="scss" scoped>
.station-tank-container {
  :deep(.el-form) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 150px;
    column-gap: 24px;
    .el-range-editor.el-input__wrapper {
      padding: 0;
    }
    .el-date-editor .el-range__icon {
      margin-right: 4px;
    }
    .el-form-item__content {
      flex-wrap: nowrap;
    }
    .el-select .el-select__tags .el-tag--info {
      color: #262626;
    }
  }
}
</style>
