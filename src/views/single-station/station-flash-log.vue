<template>
  <div class="station-flash-log">
    <el-form :model="form">
      <div class="first-line-form">
        <el-form-item :label="$t('station.pp_time_frame')">
          <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :clearable="false" :disabledDate="getDisabledDate">
            <template #range-separator>
              <TimeRangeIcon style="margin: 0 5px" />
            </template>
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('station.pp_battery_id')">
          <el-input v-model="form.battery_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full">
            <template #prefix><SearchIcon /></template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('station.pp_flash_result')">
          <el-select v-model="form.refresh_result" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
            <el-option v-for="item in refreshResultOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
      </div>

      <div :class="formClass">
        <el-form-item :label="$t('station.pp_battery_capacity')">
          <el-select v-model="form.battery_capacity" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
            <el-option v-for="item in batteryCapacityOptions" :key="item" :value="item" :label="item + 'kW'" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('station.pp_trigger_mode')">
          <el-select v-model="form.refresh_type" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
            <el-option v-for="item in refreshTypeOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('station.pp_target_version')">
          <el-input v-model="form.refresh_target_version" :placeholder="$t('common.pp_please_input')" clearable class="width-full">
            <template #prefix><SearchIcon /></template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
          <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
        </el-form-item>
      </div>
    </el-form>

    <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="refresh_result" :label="$t('station.pp_status')" :width="locale == 'zh' ? 56 : 70" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tooltip :content="getResultMap(row.refresh_result)" placement="top">
            <div class="flex-box flex_j_c-center flex_a_i-center">
              <SuccessIcon v-if="row.refresh_result == '1'" />
              <FailIcon v-else-if="row.refresh_result == '2'" />
              <OvertimeIcon v-else-if="row.refresh_result == '3'" />
              <UnknownIcon v-else />
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="start_time" :label="$t('station.pp_flash_start_time')" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatTime(row.start_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="end_time" :label="$t('station.pp_flash_end_time')" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatTime(row.end_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="battery_id" :label="$t('station.pp_flash_battery_id')" min-width="240">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.battery_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="refresh_type" :label="$t('station.pp_trigger_mode')" :min-width="locale == 'zh' ? 120 : 150" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="refresh-type-tag" v-if="refreshTypeMap[row.refresh_type]" :style="{ color: refreshTypeMap[row.refresh_type].color, background: refreshTypeMap[row.refresh_type].background, width: locale == 'zh' ? '64px' : '120px' }">
            {{ $t(refreshTypeMap[row.refresh_type].name) }}
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column prop="battery_capacity" :label="$t('station.pp_flash_battery_capacity')" :min-width="locale == 'zh' ? 140 : 150" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.battery_capacity">{{ row.battery_capacity + 'kW' }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="refresh_target_version" :label="$t('station.pp_flash_target_version')" min-width="140" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.refresh_target_version || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="battery_road" :label="$t('station.pp_battery_branch_ownership')" :min-width="locale == 'zh' ? 130 : 140" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.battery_road || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('station.pp_detail')" width="100" fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="operate-column" @click="handleViewDetail(row)">
            <EyeIcon />
            <span class="edit-text">{{ $t('common.pp_view') }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="common-pagination">
      <Page :page="pages" @change="handlePageChange" />
    </div>

    <el-dialog v-model="detailVisible" :title="$t('station.pp_detail')" width="1141px" align-center @close="detailVisible = false">
      <div class="text-container">
        <div class="text-item">
          <div class="text-label">{{ $t('station.pp_flash_result') }}</div>
          <div class="text-value flex-box flex_a_i-center gap_6">
            <SuccessIcon v-if="rowInfo.refresh_result == '1'" />
            <FailIcon v-else-if="rowInfo.refresh_result == '2'" />
            <OvertimeIcon v-else-if="rowInfo.refresh_result == '3'" />
            <UnknownIcon v-else />
            <span :style="{ color: refreshResultMap[rowInfo.refresh_result].color }">{{ getResultMap(rowInfo.refresh_result) }}</span>
          </div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('common.pp_start_time') }}</div>
          <div class="text-value">{{ formatTime(rowInfo.start_time) }}</div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('common.pp_end_time') }}</div>
          <div class="text-value">{{ formatTime(rowInfo.end_time) }}</div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('station.pp_trigger_mode') }}</div>
          <div class="text-value">{{ $t(refreshTypeMap[rowInfo.refresh_type].name) }}</div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('station.pp_flash_battery_id') }}</div>
          <div class="text-value"><WelkinCopyBoard :text="rowInfo.battery_id" direction="rtl" /></div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('station.pp_flash_battery_capacity') }}</div>
          <div class="text-value">{{ rowInfo.battery_capacity ? rowInfo.battery_capacity + 'kW' : '-' }}</div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('station.pp_flash_target_version') }}</div>
          <div class="text-value">{{ rowInfo.refresh_target_version || '-' }}</div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('station.pp_battery_branch_ownership') }}</div>
          <div class="text-value">{{ rowInfo.battery_road || '-' }}</div>
        </div>
        <div class="text-item" v-if="rowInfo.refresh_type == '1'">
          <div class="text-label">{{ $t('station.pp_authentication_start') }}</div>
          <div class="text-value">{{ formatTime(rowInfo.authenticate_timestamp) }}</div>
        </div>
        <div class="text-item" v-if="rowInfo.refresh_type == '1' || rowInfo.refresh_type == '2'">
          <div class="text-label">{{ $t('station.pp_cloud_specified_battery_id') }}</div>
          <div class="text-value"><WelkinCopyBoard :text="rowInfo.oss_command_battery_id" direction="rtl" /></div>
        </div>
        <div class="text-item" v-if="rowInfo.refresh_type == '1' || rowInfo.refresh_type == '2'">
          <div class="text-label">{{ $t('station.pp_cloud_specified_battery_capacity') }}</div>
          <div class="text-value">{{ rowInfo.oss_command_battery_capacity ? rowInfo.oss_command_battery_capacity + 'kW' : '-' }}</div>
        </div>
        <div class="text-item" v-if="rowInfo.refresh_type == '1' || rowInfo.refresh_type == '2'">
          <div class="text-label">{{ $t('station.pp_order_id') }}</div>
          <div class="text-value"><WelkinCopyBoard :text="rowInfo.order_id" direction="rtl" /></div>
        </div>
        <div class="text-item" v-if="rowInfo.refresh_type == '3'">
          <div class="text-label">{{ $t('station.pp_request_id') }}</div>
          <div class="text-value"><WelkinCopyBoard :text="rowInfo.oss_request_id" direction="rtl" /></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import { page } from '~/constvars/page';
import { refreshResultOptions, refreshTypeOptions, refreshTypeMap, refreshResultMap } from './constant';
import { getDisabledDate, formatTime, clearJson, removeNullProp, getShortcuts } from '~/utils';
import { apiGetFlashLog } from '~/apis/plc-record';
import SearchIcon from '~/assets/svg/search-icon.vue';
import TimeRangeIcon from '~/assets/svg/time-range.vue';
import EyeIcon from '~/assets/svg/eye-icon.vue';
import SuccessIcon from './icon/success-icon.vue';
import FailIcon from './icon/fail-icon.vue';
import UnknownIcon from './icon/unknown-icon.vue';
import OvertimeIcon from './icon/overtime-icon.vue';
import _ from 'lodash';

const { t } = useI18n();
const store = useStore();
const route = useRoute();
const router = useRouter();
const project = ref(computed(() => store.state.project));
const { locale } = useI18n({ useScope: 'global' });

const pages = ref(_.cloneDeep(page));
const loading = ref(false);
const detailVisible = ref(false);
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date().getTime()] as any);
const list = ref([] as any);
const form = ref({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  battery_id: '',
  refresh_result: '',
  battery_capacity: '',
  refresh_type: '',
  refresh_target_version: '',
});
const searchForm = ref({} as any);
const rowInfo = ref({} as any);
const formClass = computed(() => {
  if (locale.value == 'zh') {
    if (loading.value) {
      return 'zh-loading-form';
    } else {
      return 'zh-unloading-form';
    }
  } else {
    if (loading.value) {
      return 'en-loading-form';
    } else {
      return 'en-unloading-form';
    }
  }
});
const batteryCapacityOptions = ref(computed(() => store.state.vehicle.batteryRefreshCapacity));

const getResultMap = (result: string) => {
  const findObj = refreshResultOptions.find((obj: any) => obj.value == result);
  return findObj ? t(findObj.label) : t('station.pp_unknown');
};

/**
 * @description: 查看详情
 * @param {*} row
 * @return {*}
 */
const handleViewDetail = (row: any) => {
  rowInfo.value = row;
  detailVisible.value = true;
};

/**
 * @description: 时间
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  form.value.start_time = new Date(value[0]).getTime();
  form.value.end_time = new Date(value[1]).getTime();
};

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value);
  form.value.start_time = new Date(new Date().toLocaleDateString()).getTime();
  form.value.end_time = new Date().getTime();
  datePicker.value = [form.value.start_time, form.value.end_time];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  searchForm.value = { ...form.value };
  getList();
};

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList();
};

/**
 * @description: 获取数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  if (loading.value) return;
  let formData = {} as any;
  formData = { ...searchForm.value };
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.descending = true;
  if (updateRoute) {
    router.push({
      path: route.path,
      query: { ...removeNullProp(formData), tab: 'flash' },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetFlashLog(formData, project.value.project, route.params.deviceId);
    list.value = res.data;
    pages.value.total = res.total;
  } catch (error) {}
  loading.value = false;
};

const initWeb = () => {
  let initParams: any = route.query;
  if (initParams.tab == 'flash') {
    if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
      form.value.start_time = Number(initParams.start_time);
      form.value.end_time = Number(initParams.end_time);
      datePicker.value[0] = form.value.start_time;
      datePicker.value[1] = form.value.end_time;
    }
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
    form.value.refresh_result = !!initParams.refresh_result ? initParams.refresh_result : '';
    form.value.battery_capacity = !!initParams.battery_capacity ? initParams.battery_capacity : '';
    form.value.refresh_type = !!initParams.refresh_type ? initParams.refresh_type : '';
    form.value.refresh_target_version = !!initParams.refresh_target_version ? initParams.refresh_target_version : '';
    form.value.battery_id = !!initParams.battery_id ? initParams.battery_id : '';
  }
  searchForm.value = { ...form.value };
  getList(false);
};

onBeforeMount(() => {
  initWeb();
});
</script>

<style lang="scss" scoped>
.station-flash-log {
  font-family: 'Blue Sky Standard';
  :deep(.el-form) {
    .first-line-form {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px 24px;
      margin-bottom: 16px;
    }

    .el-form-item__content {
      white-space: nowrap;
      align-items: normal;
      flex-wrap: nowrap;
    }
    .el-range-editor.el-input__wrapper {
      padding: 0;
    }
    .el-date-editor .el-range__icon {
      margin-right: 4px;
    }
  }
  :deep(.zh-loading-form) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 148px;
    gap: 16px 24px;
  }
  :deep(.zh-unloading-form) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 128px;
    gap: 16px 24px;
  }
  :deep(.en-loading-form) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 173px;
    gap: 16px 24px;
  }
  :deep(.en-unloading-form) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 153px;
    gap: 16px 24px;
  }
  :deep(.operate-column) {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    margin-top: 2px;
    .edit-text {
      color: #01a0ac;
    }
  }
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 28px 16px;
      .el-dialog__title {
        color: #1f1f1f;
      }
    }
    .el-dialog__body {
      padding: 0 28px 32px;
      .text-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px 88px;
        .text-item {
          display: flex;
          align-items: center;
          .text-label {
            width: 120px;
            font-size: 14px;
            line-height: 22px;
            color: #8c8c8c;
          }
          .text-value {
            width: 183px;
            font-size: 14px;
            line-height: 22px;
            color: #262626;
          }
        }
      }
    }
  }
  .refresh-type-tag {
    display: flex;
    height: 24px;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    font-size: 12px;
    line-height: 18px;
  }
}
</style>
