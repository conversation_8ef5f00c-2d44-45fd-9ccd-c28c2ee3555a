<template>
  <div class="station-image-container">
    <el-form :model="searchForm">
      <el-form-item :label="$t('common.pp_time_frame')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="shortcuts" :clearable="false" :disabledDate="getDisabledDate">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('station.pp_image_type')">
        <el-select v-model="searchForm.image_type" multiple collapse-tags collapse-tags-tooltip clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in imageTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('station.pp_image_status')">
        <el-select v-model="searchForm.abnormal" clearable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in abnormalOptions" :key="item.value" :label="$t(`imageList.imageStatus.${item.value}`)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="welkin-primary-button" :loading="loading" @click="filterEvent">
          {{ $t('common.pp_search') }}
        </el-button>
        <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">
          {{ $t('common.pp_reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="image_gen_time" :label="$t('station.pp_create_ts')" width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatLocaleDate(scope.row.image_gen_time, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="image_type" :label="$t('station.pp_image_type')" min-width="260" show-overflow-tooltip>
        <template #default="scope">
          {{ imageTypeObj[scope.row.image_type] }}
        </template>
      </el-table-column>
      <el-table-column prop="image_name" :label="$t('station.pp_image_name')" min-width="320">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.image_name" />
        </template>
      </el-table-column>
      <el-table-column prop="battery_id" :label="$t('station.pp_battery_id')" min-width="240">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.battery_id" />
        </template>
      </el-table-column>
      <el-table-column prop="abnormal" :label="$t('station.pp_image_status')" min-width="100" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.abnormal ? $t('common.pp_abnormal') : $t('common.pp_normal') }}
        </template>
      </el-table-column>
      <el-table-column prop="image_size" :label="$t('station.pp_image_size')" min-width="120" show-overflow-tooltip>
        <template #default="scope">
          {{ (scope.row.image_size / 1024).toFixed(2) + 'KB' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('station.pp_view_image')" width="100" fixed="right">
        <template #default="scope">
          <div class="flex-box flex_a_i-center">
            <NormalImage @click="viewImage(scope.$index, scope?.row)" class="cursor-pointer margin_l-12" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="common-pagination">
      <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <div v-if="showImageViewer">
      <WelkinImageViewer :data="[imageList]" @onClose="closeImageView" :showIndex="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, watch, computed } from 'vue'
import { apiGetImageList, apiGetImageType } from '~/apis/image-list'
import { getEnv } from '~/utils'
import { formatLocaleDate, getShortcuts, getDisabledDate, removeNullProp, toQueryString } from '~/utils'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { pagination } from '~/constvars'
import { useI18n } from 'vue-i18n'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import NormalImage from '~/assets/svg/normal-image.vue'

const { locale } = useI18n({ useScope: 'global' })
const $store = useStore()
const $route = useRoute()
const $router = useRouter()

const project = ref(computed(() => $store.state.project))

interface query {
  image_type?: number[]
  abnormal?: number | string
  size: number
  page: number
  start_time: number
  end_time: number
}
const loading = ref(false)
const imageList = ref([])
const totalNumber = ref(0)
const showImageViewer = ref(false)
const tableList = ref([])
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date()] as any)
const searchForm: query = reactive({
  image_type: [],
  abnormal: '' as number | string,
  size: 10,
  page: 1,
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  descending: true
})
const shortcuts = ref(getShortcuts())
const imageTypeOptions = ref([] as any)
const imageTypeObj = ref({} as any)
const abnormalOptions = [
  {
    label: '正常',
    value: '0'
  },
  {
    label: '异常',
    value: '1'
  }
]

const closeImageView = () => {
  showImageViewer.value = false
}

const getImageType = () => {
  return apiGetImageType().then((res) => {
    let options: any[] = []
    res.data.map((item: any) => {
      options.push({
        label: item.name,
        value: String(item.type)
      })
      imageTypeObj.value[String(item.type)] = item.name
    })
    imageTypeOptions.value = options
  })
}

const search = () => {
  if (searchForm.image_type?.length === 0) {
    delete searchForm.image_type
  }
  const query = {
    ...searchForm,
    tab: 'image'
  }
  $router.push(`${$route.path + toQueryString(removeNullProp(query))}`)

  return apiGetImageList(searchForm, $route.params.deviceId, project.value.project)
    .then((res) => {
      tableList.value = res.data
      totalNumber.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置
const resetSelect = () => {
  datePicker.value = [new Date(new Date().toLocaleDateString()).getTime(), new Date()]
  searchForm.abnormal = ''
  searchForm.image_type = []
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
  searchForm.end_time = new Date().getTime()
}

onBeforeMount(() => {
  loading.value = true
  let init_params: any = $route.query
  getImageType()
  if (init_params.tab == 'image' && !isNaN(init_params.start_time) && !isNaN(init_params.end_time)) {
    searchForm.start_time = Number(init_params.start_time)
    searchForm.end_time = Number(init_params.end_time)
    datePicker.value[0] = Number(init_params.start_time)
    datePicker.value[1] = Number(init_params.end_time)

    searchForm.page = !!init_params.page ? Number(init_params.page) : 1
    searchForm.size = !!init_params.size ? Number(init_params.size) : 10

    searchForm.abnormal = init_params.abnormal
    if (!!init_params.image_type) {
      searchForm.image_type = init_params.image_type.split(',')
    }
  } else {
    resetSelect()
  }

  search()
})

// 查看图像
const viewImage = (index: any, row: any) => {
  if (getEnv() == '-stg') {
    row.image_url = row.image_url.replace('http://api-pp-welkin-minio-test.nioint.com:9000', '/web/uriDownload')
  }
  imageList.value = row
  showImageViewer.value = true
}

const handleDateChange = (value: any) => {
  searchForm.start_time = value[0].getTime()
  searchForm.end_time = value[1].getTime()
}

// 筛选事件
const filterEvent = () => {
  loading.value = true
  searchForm.size = 10
  searchForm.page = 1
  search()
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true
  searchForm.size = val
  searchForm.page = 1
  search()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true
  searchForm.page = val
  search()
}

watch(
  () => locale.value,
  (newValue, oldValue) => {
    getImageType()
  }
)
</script>

<style lang="scss" scoped>
.station-image-container {
  :deep(.el-form) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 150px;
    column-gap: 24px;
    .el-range-editor.el-input__wrapper {
      padding: 0;
    }
    .el-date-editor .el-range__icon {
      margin-right: 4px;
    }
    .el-form-item__content {
      flex-wrap: nowrap;
      align-items: normal;
    }
    .el-select .el-select__tags .el-tag--info {
      color: #262626;
    }
  }
}
</style>
