<template>
  <div class="station-service-container">
    <el-form :model="searchForm" class="service-search-form">
      <el-form-item :label="$t('common.pp_time_frame')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="shortcuts" :clearable="false" :disabledDate="getDisabledDate">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('station.pp_car_id')">
        <el-input v-model.trim="searchForm.ev_id" :placeholder="$t('common.pp_please_input')" clearable />
      </el-form-item>
      <el-form-item :label="$t('station.pp_battery_id')">
        <el-input v-model.trim="searchForm.battery_id" :placeholder="$t('common.pp_please_input')" clearable />
      </el-form-item>
      <el-form-item :label="$t('station.pp_service_id')">
        <el-input v-model.trim="searchForm.service_id" :placeholder="$t('common.pp_please_input')" clearable />
      </el-form-item>
      <el-form-item :label="$t('common.pp_vehicle_brand')">
        <el-cascader v-model="brand" :options="brandOptions" :props="cascaderProps" separator="-" collapse-tags clearable @change="handleBrandChange" class="brand-cascader" :placeholder="t('common.pp_please_select')" />
      </el-form-item>
      <el-form-item :label="$t('station.pp_status_select')">
        <el-select v-model="searchForm.finish_result" clearable filterable class="width-full" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('station.pp_stuck')">
        <el-select v-model="searchForm.is_stuck" clearable filterable class="width-full" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in isStuckOptions" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="flex-box width-full">
          <el-button class="welkin-primary-button" @click="filterEvent" :loading="loading">
            {{ $t('common.pp_search') }}
          </el-button>
          <el-button @click="resetSelect" class="welkin-secondary-button">
            {{ $t('common.pp_reset') }}
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="finish_result" :label="$t('common.pp_status')" width="56" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip :content="[1, -1, 0].includes(scope.row.finish_result) ? $t(statusNameMap[scope.row.finish_result]) : $t('station.pp_unknown')" placement="top">
            <div class="flex-box flex_j_c-center flex_a_i-center">
              <SuccessIcon v-if="scope.row.finish_result == 1" />
              <FailIcon v-else-if="scope.row.finish_result == 0" />
              <UnfinishedIcon v-else-if="scope.row.finish_result == -1" />
              <UnknownIcon v-else />
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="service_start_time" :label="$t('common.pp_start_time')" width="170" show-overflow-tooltip>
        <template #default="scope">
          {{ formatLocaleDate(scope.row.service_start_time, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="service_end_time" :label="$t('common.pp_end_time')" width="170" show-overflow-tooltip>
        <template #default="scope">
          {{ formatLocaleDate(scope.row.service_end_time, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="service_duration" :label="$t('station.pp_service_duration')" width="100" show-overflow-tooltip />
      <el-table-column prop="ev_brand" :label="$t('common.pp_vehicle_brand')" width="100" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.ev_brand ? scope.row.ev_brand : '' }}-{{ scope.row.ev_type ? scope.row.ev_type : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="service_battery_id" :label="$t('station.pp_service_battery_id')" width="240">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.service_battery_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="ev_battery_id" :label="$t('station.pp_car_battery_id')" width="240">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.ev_battery_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="ev_id" :label="$t('station.pp_car_id')" width="200">
        <template #default="scope">
          <WelkinCopyBoard :text="maskString(scope.row.ev_id)" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="service_id" :label="$t('station.pp_service_id')" width="200">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.service_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('common.pp_operation')" width="180">
        <template #default="scope">
          <div class="flex-box flex_a_i-center gap_14">
            <el-tooltip effect="dark" :content="$t('menu.pp_service_detail')" placement="bottom">
              <ServiceIcon @click="serviceDetail(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
            <el-tooltip effect="dark" :content="$t('station.pp_view_normal_image')" placement="bottom">
              <NormalImage v-if="scope.row.has_normal_image" @click="viewNormalImage(scope.$index, scope.row)" class="cursor-pointer" />
              <NonNormalImage v-else @click="viewNormalImage(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
            <el-tooltip effect="dark" :content="$t('station.pp_view_abnormal_picture')" placement="bottom">
              <AbnormalImage v-if="scope.row.has_abnormal_image" @click="viewAbnormalImage(scope.$index, scope.row)" class="cursor-pointer" />
              <NonAbnormalImage v-else @click="viewAbnormalImage(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
            <el-tooltip :content="$t('station.pp_view_flash')" placement="bottom">
              <FlashIcon @click="goToFlashPage(scope.$index, scope.row)" class="cursor-pointer" />
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="common-pagination">
      <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <div v-if="showImageViewer">
      <WelkinImageViewer :data="imageList" @onClose="closeImageView" />
    </div>
    <el-dialog v-model="showFlashLogDialog" :title="$t('menu.pp_flash_log')" width="1200px" align-center @close="showFlashLogDialog = false">
      <div class="station-flash-log">
        <el-table :data="flashLogList" v-loading="flashLogLoading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column prop="refresh_result" :label="$t('station.pp_status')" :width="locale == 'zh' ? 56 : 70" show-overflow-tooltip>
            <template #default="{ row }">
              <el-tooltip :content="flashLogGetResultMap(row.refresh_result)" placement="top">
                <div class="flex-box flex_j_c-center flex_a_i-center">
                  <SuccessIcon v-if="row.refresh_result == '1'" />
                  <FailIcon v-else-if="row.refresh_result == '2'" />
                  <OvertimeIcon v-else-if="row.refresh_result == '3'" />
                  <UnknownIcon v-else />
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="start_time" :label="$t('station.pp_flash_start_time')" min-width="160" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatLocaleDate(row.start_time, false) }}
            </template>
          </el-table-column>
          <el-table-column prop="end_time" :label="$t('station.pp_flash_end_time')" min-width="160" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatLocaleDate(row.end_time, false) }}
            </template>
          </el-table-column>
          <el-table-column prop="battery_id" :label="$t('station.pp_flash_battery_id')" min-width="240">
            <template #default="{ row }">
              <WelkinCopyBoard :text="row.battery_id" direction="rtl" />
            </template>
          </el-table-column>
          <el-table-column prop="refresh_type" :label="$t('station.pp_trigger_mode')" :min-width="locale == 'zh' ? 120 : 150" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="refresh-type-tag" :style="{ color: refreshTypeMap[row.refresh_type].color, background: refreshTypeMap[row.refresh_type].background, width: locale == 'zh' ? '64px' : '120px' }">
                {{ $t(refreshTypeMap[row.refresh_type].name) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="battery_capacity" :label="$t('station.pp_flash_battery_capacity')" :min-width="locale == 'zh' ? 140 : 150" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.battery_capacity">{{ row.battery_capacity + 'kW' }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="refresh_target_version" :label="$t('station.pp_flash_target_version')" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.refresh_target_version || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="battery_road" :label="$t('station.pp_battery_branch_ownership')" :min-width="locale == 'zh' ? 130 : 140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.battery_road || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('station.pp_detail')" width="100" fixed="right" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="operate-column" @click="flashLogHandleViewDetail(row)">
                <EyeIcon />
                <span class="edit-text">{{ $t('common.pp_view') }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="common-pagination">
          <Page :page="flashLogPages" @change="flashLogHandlePageChange" />
        </div>
        <el-dialog v-model="flashLogDetailVisible" :title="$t('station.pp_detail')" width="1141px" align-center @close="flashLogDetailVisible = false">
          <div class="text-container">
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_flash_result') }}</div>
              <div class="text-value flex-box flex_a_i-center gap_6">
                <SuccessIcon v-if="flashLogRowInfo.refresh_result == '1'" />
                <FailIcon v-else-if="flashLogRowInfo.refresh_result == '2'" />
                <OvertimeIcon v-else-if="flashLogRowInfo.refresh_result == '3'" />
                <UnknownIcon v-else />
                <span :style="{ color: refreshResultMap[flashLogRowInfo.refresh_result]?.color }">{{ flashLogGetResultMap(flashLogRowInfo.refresh_result) }}</span>
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_start_time') }}</div>
              <div class="text-value">{{ formatLocaleDate(flashLogRowInfo.start_time, false) }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_end_time') }}</div>
              <div class="text-value">{{ formatLocaleDate(flashLogRowInfo.end_time, false) }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_trigger_mode') }}</div>
              <div class="text-value">{{ $t(refreshTypeMap[flashLogRowInfo.refresh_type]?.name) }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_flash_battery_id') }}</div>
              <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.battery_id" direction="rtl" /></div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_flash_battery_capacity') }}</div>
              <div class="text-value">{{ flashLogRowInfo.battery_capacity ? flashLogRowInfo.battery_capacity + 'kW' : '-' }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_flash_target_version') }}</div>
              <div class="text-value">{{ flashLogRowInfo.refresh_target_version || '-' }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_battery_branch_ownership') }}</div>
              <div class="text-value">{{ flashLogRowInfo.battery_road || '-' }}</div>
            </div>
            <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1'">
              <div class="text-label">{{ $t('station.pp_authentication_start') }}</div>
              <div class="text-value">{{ formatLocaleDate(flashLogRowInfo.authenticate_timestamp, false) }}</div>
            </div>
            <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1' || flashLogRowInfo.refresh_type == '2'">
              <div class="text-label">{{ $t('station.pp_cloud_specified_battery_id') }}</div>
              <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.oss_command_battery_id" direction="rtl" /></div>
            </div>
            <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1' || flashLogRowInfo.refresh_type == '2'">
              <div class="text-label">{{ $t('station.pp_cloud_specified_battery_capacity') }}</div>
              <div class="text-value">{{ flashLogRowInfo.oss_command_battery_capacity ? flashLogRowInfo.oss_command_battery_capacity + 'kW' : '-' }}</div>
            </div>
            <div class="text-item" v-if="flashLogRowInfo.refresh_type == '1' || flashLogRowInfo.refresh_type == '2'">
              <div class="text-label">{{ $t('station.pp_order_id') }}</div>
              <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.order_id" direction="rtl" /></div>
            </div>
            <div class="text-item" v-if="flashLogRowInfo.refresh_type == '3'">
              <div class="text-label">{{ $t('station.pp_request_id') }}</div>
              <div class="text-value"><WelkinCopyBoard :text="flashLogRowInfo.oss_request_id" direction="rtl" /></div>
            </div>
          </div>
        </el-dialog>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, watch, computed } from 'vue'
import { formatLocaleDate, getDuration, getShortcuts, getDisabledDate, removeNullProp, maskString, isEmptyData } from '~/utils'
import { statusOptions, isStuckOptions, statusNameMap } from './constant'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { apiGetServiceList } from '~/apis/service-list'
import { apiGetImageList } from '~/apis/image-list'
import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'
import { pagination } from '~/constvars'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import ServiceIcon from '~/assets/svg/service-icon.vue'
import NormalImage from '~/assets/svg/normal-image.vue'
import NonNormalImage from '~/assets/svg/non-normal-image.vue'
import AbnormalImage from '~/assets/svg/abnormal-image.vue'
import NonAbnormalImage from '~/assets/svg/non-abnormal-image.vue'
import SuccessIcon from './icon/success-icon.vue'
import FailIcon from './icon/fail-icon.vue'
import UnknownIcon from './icon/unknown-icon.vue'
import UnfinishedIcon from './icon/unfinished-icon.vue'
import FlashIcon from '~/assets/svg/flash-icon.vue'
import { apiGetFlashLog } from '~/apis/plc-record'
import { refreshResultOptions, refreshTypeOptions, refreshTypeMap, refreshResultMap } from '~/views/single-station/constant'
import Page from '~/components/global/page/index.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import EyeIcon from '~/assets/svg/eye-icon.vue'
import OvertimeIcon from '~/views/single-station/icon/overtime-icon.vue'
import _ from 'lodash'

const $store = useStore()
const $router = useRouter()
const $route = useRoute()

const { t } = useI18n()
const { locale } = useI18n({ useScope: 'global' })
const project = ref(computed(() => $store.state.project))
const brandOptions = ref(computed(() => $store.state.vehicle.brandList))
const cascaderProps = reactive({ multiple: true })

const loading = ref(false)
const showImageViewer = ref(false)
const imageList = ref([] as any)
const brand = ref([] as any)

const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date()] as any)
const deviceId = ref($route.params.deviceId)
const searchForm = reactive({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  ev_id: '',
  service_id: '',
  battery_id: '',
  isWarning: '',
  abnormalPictures: '',
  ev_brand: '',
  ev_type: '',
  finish_result: '' as number | string,
  is_stuck: '' as boolean | string,
  page: 1,
  size: 10,
  descending: true
})

const tableList = ref([] as any)
const totalNumber = ref(0)
const shortcuts = ref(getShortcuts())

/**
 * @description: 选择车辆品牌
 * @return {*}
 */
const handleBrandChange = () => {
  const result = brand.value.reduce(
    (acc: any, curr: any) => {
      const [brand, type] = curr
      if (!acc.ev_brand.includes(brand)) acc.ev_brand.push(brand)
      if (!acc.ev_type.includes(type)) acc.ev_type.push(type)
      return acc
    },
    { ev_brand: [], ev_type: [] }
  )
  searchForm.ev_brand = result.ev_brand.join(',')
  searchForm.ev_type = result.ev_type.join(',')
}

// 跳转到电池刷写页面
const goToFlashPage = (index: any, row: any) => {
  showFlashLogDialog.value = true
  flashLogDeviceId.value = row.device_id
  const serviceEndTime = row.service_end_time === 0 ? row.service_start_time : row.service_end_time
  const start = serviceEndTime - 2 * 60 * 60 * 1000
  const end = serviceEndTime
  flashLogForm.value = {
    start_time: start,
    end_time: end,
    battery_id: row.service_battery_id || '',
    refresh_result: '',
    battery_capacity: '',
    refresh_type: '',
    refresh_target_version: ''
  }
  flashLogDatePicker.value = [start, end]
  flashLogSearchForm.value = { ...flashLogForm.value }
  flashLogPages.value.current = 1
  getFlashLogList()
}

// 公用搜索事件
const publicSearch = (updateRoute = true) => {
  updateRoute &&
    $router.push({
      path: $route.path,
      query: {
        ...removeNullProp(searchForm),
        tab: 'service'
      }
    })

  return apiGetServiceList({ ...searchForm, deviceId: deviceId.value }, project.value.project)
    .then((res) => {
      tableList.value = res.data || []
      totalNumber.value = res.total
      tableList.value.map((item: any) => {
        item.service_duration = getDuration(item.service_start_time, item.service_end_time)
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置
const resetSelect = () => {
  brand.value = []
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
  searchForm.end_time = new Date().getTime()
  datePicker.value = [searchForm.start_time, searchForm.end_time]

  searchForm.battery_id = ''
  searchForm.ev_id = ''
  searchForm.service_id = ''
  searchForm.finish_result = ''
  searchForm.is_stuck = ''
  searchForm.ev_brand = ''
  searchForm.ev_type = ''
}

watch(
  () => locale.value,
  (newValue, oldValue) => {
    if (newValue === 'en') {
      searchForm.abnormalPictures = 'All'
      searchForm.isWarning = 'All'
    }
  },
  { immediate: true }
)

// 查看服务详情
const serviceDetail = (index: any, row: any) => {
  sessionStorage.setItem('station-service-list', JSON.stringify($route.query))

  $router.push({
    path: `/${project.value.route}/device-management/single-station/${row.device_id}/service-detail/${row.service_id}`,
    query: {
      start_time: row.service_start_time,
      end_time: row.service_end_time
    }
  })
}

const closeImageView = () => {
  showImageViewer.value = false
  imageList.value = []
}

const viewServiceImage = (query: any, device_id: any) => {
  query.end_time = Number(query.end_time) + 5 * 60 * 1000
  return apiGetImageList(query, device_id, project.value.project).then((res) => {
    if (res.data.length === 0) {
      showImageViewer.value = false
      ElMessage.warning(t('common.pp_lack_image'))
    } else {
      imageList.value = res.data
      showImageViewer.value = true
    }
  })
}

// 查看图像
const viewNormalImage = (index: any, row: any) => {
  const start_time = row.service_start_time ? row.service_start_time : Number(searchForm.start_time)
  const end_time = row.service_end_time ? row.service_end_time : Number(searchForm.end_time)

  const query = {
    abnormal: 0,
    service_id: row.service_id,
    start_time: start_time,
    end_time: end_time
  }
  return viewServiceImage(query, row.device_id)
}

// 查看异常图像
const viewAbnormalImage = (index: any, row: any) => {
  const start_time = row.service_start_time ? row.service_start_time : Number(searchForm.start_time)
  const end_time = row.service_end_time ? row.service_end_time : Number(searchForm.end_time)
  const query = {
    abnormal: 1,
    service_id: row.service_id,
    start_time: start_time,
    end_time: end_time
  }
  return viewServiceImage(query, row.device_id)
}

const handleDateChange = (value: any) => {
  if (value) {
    searchForm.start_time = value[0].valueOf()
    searchForm.end_time = value[1].valueOf()
  } else {
    searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
    searchForm.end_time = new Date().getTime()
  }
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true
  searchForm.size = val
  searchForm.page = 1
  publicSearch()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true
  searchForm.page = val
  publicSearch()
}

// 点击筛选按钮
const filterEvent = () => {
  loading.value = true
  searchForm.size = 10
  searchForm.page = 1
  publicSearch()
}

onBeforeMount(() => {
  loading.value = true
  let init_params: any = $route.query
  if (init_params.tab == 'service' && !isNaN(init_params.start_time) && !isNaN(init_params.end_time)) {
    brand.value = init_params.ev_type ? init_params.ev_type.split(',') : []
    searchForm.start_time = Number(init_params.start_time)
    searchForm.end_time = Number(init_params.end_time)
    datePicker.value[0] = Number(init_params.start_time)
    datePicker.value[1] = Number(init_params.end_time)

    searchForm.page = !!init_params.page ? Number(init_params.page) : 1
    searchForm.size = !!init_params.size ? Number(init_params.size) : 10

    searchForm.ev_id = init_params.ev_id
    searchForm.service_id = init_params.service_id
    searchForm.battery_id = init_params.battery_id
    searchForm.ev_brand = init_params.ev_brand
    searchForm.ev_type = init_params.ev_type
    searchForm.finish_result = !!init_params.finish_result ? Number(init_params.finish_result) : ''
    searchForm.is_stuck = !!init_params.is_stuck ? JSON.parse(init_params.is_stuck) : ''
    searchForm.abnormalPictures = init_params.abnormalPictures
  } else {
    resetSelect()
  }

  publicSearch(false)
})

const showFlashLogDialog = ref(false)
const flashLogLoading = ref(false)
const flashLogPages = ref(_.cloneDeep({ current: 1, size: 10, total: 0, sizes: [5, 10, 20, 50, 100] }))
const flashLogList = ref([] as any)
const flashLogForm = ref({
  start_time: 0,
  end_time: 0,
  battery_id: '',
  refresh_result: '',
  battery_capacity: '',
  refresh_type: '',
  refresh_target_version: ''
})
const flashLogDatePicker = ref([0, 0] as any)
const flashLogSearchForm = ref({} as any)
const flashLogRowInfo = ref({} as any)
const flashLogDetailVisible = ref(false)
const flashLogDeviceId = ref('')

function flashLogGetResultMap(result: string) {
  const findObj = refreshResultOptions.find((obj: any) => obj.value == result)
  return findObj ? t(findObj.label) : t('station.pp_unknown')
}

function flashLogHandleViewDetail(row: any) {
  flashLogRowInfo.value = row
  flashLogDetailVisible.value = true
}

function flashLogHandlePageChange(argPage: any) {
  flashLogPages.value.current = argPage.current
  flashLogPages.value.size = argPage.size
  getFlashLogList()
}

async function getFlashLogList() {
  if (flashLogLoading.value) return
  let formData = { ...flashLogSearchForm.value }
  formData.page = flashLogPages.value.current
  formData.size = flashLogPages.value.size
  formData.descending = true
  flashLogLoading.value = true
  try {
    const res = await apiGetFlashLog(formData, project.value.project, flashLogDeviceId.value)
    flashLogList.value = res.data
    flashLogPages.value.total = res.total
  } catch (error) {}
  flashLogLoading.value = false
}
</script>

<style lang="scss" scoped>
.station-service-container {
  font-family: 'Blue Sky Standard';
  :deep(.service-search-form) {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px 24px;
    .el-range-editor.el-input__wrapper {
      padding: 0;
    }
    .el-date-editor .el-range__icon {
      margin-right: 4px;
    }
    .brand-cascader {
      width: 100%;
      height: 32px;
      .el-input {
        height: 32px;
      }
    }
  }
  :deep(.operate-column) {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    margin-top: 2px;
    .edit-text {
      color: #01a0ac;
    }
  }
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 28px 16px;
      .el-dialog__title {
        color: #1f1f1f;
      }
    }
    .el-dialog__body {
      padding: 0 28px 32px;
      .text-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px 88px;
        .text-item {
          display: flex;
          align-items: center;
          .text-label {
            width: 120px;
            font-size: 14px;
            line-height: 22px;
            color: #8c8c8c;
          }
          .text-value {
            width: 183px;
            font-size: 14px;
            line-height: 22px;
            color: #262626;
          }
        }
      }
    }
  }
  .refresh-type-tag {
    display: flex;
    height: 24px;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    font-size: 12px;
    line-height: 18px;
  }
}
</style>
