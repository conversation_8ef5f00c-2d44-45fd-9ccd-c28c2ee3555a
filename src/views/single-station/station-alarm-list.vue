<template>
  <div class="station-alarm-container">
    <el-form :model="form">
      <el-form-item :label="$t('alarmList.pp_alarm_time')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :clearable="false" :disabledDate="getDisabledDate">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_alarm_description')">
        <el-select v-model="form.data_id" multiple collapse-tags collapse-tags-tooltip clearable filterable remote :placeholder="$t('alarmList.pp_keywords')" :remote-method="remoteMethod" :loading="remoteLoading" class="width-full">
          <el-option v-for="item in pointOptions" :key="item.value" :value="item.value" :label="item.label + '-' + item.value">
            <span style="float: left">{{ item.label }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.value }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_alarm_level')">
        <el-select v-model="form.alarm_level" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_status')">
        <el-select v-model="form.state" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full">
          <el-option v-for="item in stateOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_type')">
        <el-select v-model="form.alarm_type" :placeholder="$t('common.pp_please_select')" clearable filterable class="width-full" @change="handleChangeAlarmType">
          <el-option v-for="item in alarmTypeOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('alarmList.pp_battery_id')" v-if="!isFold">
        <el-input v-model="form.battery_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
      </el-form-item>
      <el-form-item v-if="!isFold">
        <template #label>
          <div class="flex-box flex_a_i-center gap_4">
            <span>{{ $t('common.pp_vehicle_brand') }}</span>
            <el-tooltip :content="$t('alarmList.pp_form_tip')" placement="top">
              <HelpIcon />
            </el-tooltip>
          </div>
        </template>
        <el-cascader v-model="form.brand" :options="brandOptions" :props="cascaderProps" collapse-tags collapse-tags-tooltip separator="-" clearable @change="handleBrandChange" class="brand-cascader" :placeholder="t('common.pp_please_select')" />
      </el-form-item>
      <el-form-item v-if="!isFold">
        <template #label>
          <div class="flex-box flex_a_i-center gap_4">
            <span>{{ $t('common.pp_vehicle_platform') }}</span>
            <el-tooltip :content="$t('alarmList.pp_form_tip')" placement="top">
              <HelpIcon />
            </el-tooltip>
          </div>
        </template>
        <el-select v-model="form.car_platform" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in platformOptions" :key="item" :value="item" :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="width-full height-32 flex-box flex_j_c-flex-end flex_a_i-center">
          <div @click="isFold = !isFold" class="margin_r-20 font-size-16 line-height-24">
            <div class="cursor-pointer flex-box flex_a_i-center gap_4 color-59" v-if="!isFold">
              <UpIcon />
              <span>{{ $t('common.pp_collapse') }}</span>
            </div>
            <div class="cursor-pointer flex-box flex_a_i-center gap_4 color-59" v-if="isFold">
              <DownIcon />
              <span>{{ $t('common.pp_expand') }}</span>
            </div>
          </div>
          <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
          <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
        </div>
      </el-form-item>
    </el-form>

    <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <div class="flex-box flex_a_i-center width-full">
            <span class="ellipse">{{ scope.row.data_id_description ? scope.row.data_id_description : '-' }}</span>
            <el-popover :width="520" placement="top" v-if="scope.row.servo_fault_list">
              <template #reference>
                <AlarmIcon class="margin_l-6" />
              </template>
              <el-table :data="scope.row.servo_fault_list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }">
                <el-table-column prop="id" :label="$t('alarmList.pp_fault_id')" min-width="120" show-overflow-tooltip />
                <el-table-column prop="code" :label="$t('alarmList.pp_native_code')" min-width="120" show-overflow-tooltip />
                <el-table-column prop="real_code" :label="$t('alarmList.pp_real_code')" min-width="120" show-overflow-tooltip />
                <el-table-column prop="code_name" :label="$t('alarmList.pp_fault_name')" min-width="120" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span>{{ row.code_name || '-' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="130" show-overflow-tooltip />
      <el-table-column prop="alarm_type" :label="$t('alarmList.pp_alarm_type')" min-width="130" show-overflow-tooltip>
        <template #default="scope">
          {{ $t(alarmTypeMap[scope.row.alarm_type]) }}
        </template>
      </el-table-column>
      <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" min-width="130" show-overflow-tooltip>
        <template #default="scope">
          <span class="alarm-level-container" v-if="alarmLevelMap[scope.row.alarm_level]" :style="{ color: alarmLevelMap[scope.row.alarm_level].color, background: alarmLevelMap[scope.row.alarm_level].background }">
            {{ $t(alarmLevelMap[scope.row.alarm_level].name) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" min-width="130" show-overflow-tooltip>
        <template #default="scope">
          <div class="alarm-status-container" :style="{ background: alarmStatusMap[scope.row.state].background }">
            <ClearIcon v-if="scope.row.state == 1" />
            <CreateIcon v-else-if="scope.row.state == 2" />
            <UnknownIcon v-else />
            <span>{{ $t(alarmStatusMap[scope.row.state].name) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="ev_brand" :label="$t('common.pp_vehicle_brand')" min-width="100" show-overflow-tooltip v-if="searchForm.car_platform || searchForm.brand.length > 0">
        <template #default="{ row }">
          <span>{{ row.ev_brand ? row.ev_brand : '' }}-{{ row.ev_type ? row.ev_type : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="car_platform" :label="$t('common.pp_vehicle_platform')" min-width="90" show-overflow-tooltip v-if="searchForm.car_platform || searchForm.brand.length > 0">
        <template #default="{ row }">
          <span>{{ row.car_platform || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ formatTime(scope.row.create_ts) }}
        </template>
      </el-table-column>
      <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="180" show-overflow-tooltip v-if="searchForm.alarm_type != 3">
        <template #default="scope">
          {{ formatTime(scope.row.clear_ts) }}
        </template>
      </el-table-column>
      <el-table-column prop="battery_id" :label="$t('alarmList.pp_battery_id')" min-width="260" v-if="searchForm.alarm_type == 2">
        <template #default="scope">
          <WelkinCopyBoard :text="scope.row.battery_id" />
        </template>
      </el-table-column>
    </el-table>
    <div class="common-pagination">
      <Page :page="pages" @change="handlePageChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeMount } from 'vue';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import { page } from '~/constvars/page';
import { alarmLevelMap, alarmStatusMap } from './constant';
import { getDisabledDate, formatTime, clearJson, removeNullProp, getShortcuts } from '~/utils';
import { apiGetAllAlarmList, apiGetPointList } from '~/apis/alarm-list';
import { debounce } from 'lodash-es';
import TimeRangeIcon from '~/assets/svg/time-range.vue';
import AlarmIcon from '~/assets/svg/alarm-view.vue';
import UpIcon from '~/assets/svg/up-icon.vue';
import DownIcon from '~/assets/svg/down-icon.vue';
import HelpIcon from '~/assets/svg/help.vue';
import ClearIcon from './icon/alarm-clear.vue';
import CreateIcon from './icon/alarm-create.vue';
import UnknownIcon from './icon/alarm-unknown.vue';
import _ from 'lodash';

const { t } = useI18n();
const store = useStore();
const route = useRoute();
const router = useRouter();
const project = ref(computed(() => store.state.project));
const brandOptions = ref(computed(() => store.state.vehicle.brandList));
const platformOptions = ref(computed(() => store.state.vehicle.carPlatform));
const cascaderProps = ref({ multiple: true });
const { locale } = useI18n({ useScope: 'global' });

const pages = ref(_.cloneDeep(page));
const loading = ref(false);
const remoteLoading = ref(false);
const isFold = ref(true);
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date().getTime()] as any);
const list = ref([] as any);
const alarmTypeOptions = ref([
  {
    label: 'alarmList.pp_basic_alarm',
    value: 1,
  },
  {
    label: 'alarmList.pp_battery_alarm',
    value: 2,
  },
  {
    label: 'alarmList.pp_unknown_alarm',
    value: 3,
  },
]);
const alarmLevelOptions = ref([
  {
    label: 'alarmList.pp_first_level',
    value: 1,
  },
  {
    label: 'alarmList.pp_second_level',
    value: 2,
  },
  {
    label: 'alarmList.pp_third_level',
    value: 3,
  },
]);
const stateOptions = ref([
  {
    label: 'alarmList.pp_cleared',
    value: 1,
  },
  {
    label: 'alarmList.pp_alarming',
    value: 2,
  },
  {
    label: 'alarmList.pp_unknown',
    value: 3,
  },
]);
const pointOptions = ref([] as any);
const alarmTypeMap = ref({
  1: 'alarmList.pp_basic_alarm',
  2: 'alarmList.pp_battery_alarm',
  3: 'alarmList.pp_unknown_alarm',
} as any);
const form = ref({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  alarm_type: '' as number | string,
  alarm_level: '' as number | string,
  state: '' as number | string,
  data_id: [] as any,
  brand: [] as any,
  battery_id: '',
  car_platform: '',
  ev_type: '',
});
const searchForm = ref({
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  alarm_type: '' as number | string,
  alarm_level: '' as number | string,
  state: '' as number | string,
  data_id: [] as any,
  brand: [] as any,
  battery_id: '',
  car_platform: '',
  ev_type: '',
});

/**
 * @description: 选择车辆品牌
 * @return {*}
 */
const handleBrandChange = () => {
  const result = form.value.brand.reduce(
    (acc: any, curr: any) => {
      const [brand, type] = curr;
      if (!acc.ev_type.includes(type)) acc.ev_type.push(type);
      return acc;
    },
    { ev_type: [] }
  );
  form.value.ev_type = result.ev_type.join(',');
};

/**
 * @description: 时间
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  form.value.start_time = new Date(value[0]).getTime();
  form.value.end_time = new Date(value[1]).getTime();
};

/**
 * @description: 清空电池ID
 * @return {*}
 */
const handleChangeAlarmType = () => {
  form.value.battery_id = '';
};

/**
 * @description: 远程搜索告警描述点
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchPointList(query);
};
const searchPointList = debounce(async (val: any) => {
  if (val && val.length > 0) {
    remoteLoading.value = true;
    const params = typeof val == 'string' ? { description: val } : { data_ids: val.join(',') };
    const res = await apiGetPointList(params, project.value.project);
    pointOptions.value = [];
    Object.keys(res.data).forEach((key: any) => {
      pointOptions.value.push({
        label: res.data[key],
        value: key,
      });
    });
    remoteLoading.value = false;
  }
}, 500);

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  pointOptions.value = [];
  clearJson(form.value);
  form.value.start_time = new Date(new Date().toLocaleDateString()).getTime();
  form.value.end_time = new Date().getTime();
  datePicker.value = [form.value.start_time, form.value.end_time];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  pages.value.size = 10;
  searchForm.value = { ...form.value };
  getList(true);
};

/**
 * @description: 获取单站数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  if (loading.value) return;
  let formData = {} as any;
  formData = { ...searchForm.value };
  formData.data_id = formData.data_id.join(',');
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.descending = true;
  delete formData.brand;
  if (updateRoute) {
    router.push({
      path: route.path,
      query: { ...removeNullProp(formData), tab: 'alarm' },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetAllAlarmList({ ...formData, device_id: route.params.deviceId }, project.value.project);
    list.value = res.data;
    pages.value.total = res.total;
  } catch (error) {}
  loading.value = false;
};

watch(
  () => locale.value,
  (newValue, oldValue) => {
    getList(false);
    searchPointList(form.value.data_id);
  }
);

const initWeb = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query;
      if (initParams.tab == 'alarm') {
        if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
          form.value.start_time = Number(initParams.start_time);
          form.value.end_time = Number(initParams.end_time);
          datePicker.value[0] = form.value.start_time;
          datePicker.value[1] = form.value.end_time;
        }
        pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
        pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
        form.value.brand = !!initParams.ev_type ? initParams.ev_type.split(',') : [];
        form.value.alarm_type = !!initParams.alarm_type ? Number(initParams.alarm_type) : '';
        form.value.alarm_level = !!initParams.alarm_level ? Number(initParams.alarm_level) : '';
        form.value.state = !!initParams.state ? Number(initParams.state) : '';
        form.value.data_id = !!initParams.data_id ? initParams.data_id.split(',') : [];
        form.value.battery_id = !!initParams.battery_id ? initParams.battery_id : '';
        form.value.car_platform = !!initParams.car_platform ? initParams.car_platform : '';
        form.value.ev_type = !!initParams.ev_type ? initParams.ev_type : '';
      }
      searchForm.value = { ...form.value };
      if (form.value.data_id.length > 0) searchPointList(form.value.data_id);
      getList(false);
    }
  },
  { immediate: true }
);

onBeforeMount(() => {
  initWeb();
});

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getList(true);
};
</script>

<style lang="scss" scoped>
.station-alarm-container {
  font-family: 'Blue Sky Standard';
  :deep(.el-form) {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px 24px;
    .el-range-editor.el-input__wrapper {
      padding: 0;
    }
    .el-date-editor .el-range__icon {
      margin-right: 4px;
    }
    .el-form-item__content {
      white-space: nowrap;
      align-items: normal;
      flex-wrap: nowrap;
    }
    .el-select .el-select__tags .el-tag--info {
      color: #262626;
    }
    .el-tag.el-tag--info {
      color: #262626;
    }
    .brand-cascader {
      width: 100%;
      &:hover {
        position: relative;
        z-index: 2;
      }
    }
  }
  .alarm-level-container {
    display: inline-block;
    height: 26px;
    padding: 2px 8px;
    border-radius: 2px;
  }
  .alarm-status-container {
    height: 24px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 2px 10px;
    margin-top: 2px;
    border-radius: 14px;
    font-size: 13px;
    line-height: 20px;
    color: #fff;
  }
}
</style>
