<template>
  <div class="portrait-detail-container">
    <div class="header-container">
      <div class="flex-box flex_a_i-center font-size-20 line-height-28">
        <BackIcon @click="handleBack" class="cursor-pointer" />
        <span class="color-8c font-weight-480 margin_l-8">{{ $t('menu.pp_portrait_list') }}</span>
        <span class="margin-n-8">/</span>
        <span class="color-1f font-weight-420">{{ $t('satisfaction.pp_order_id') }} {{ route.query.order }}</span>
        <div class="tag-container" v-if="orderInfo.service_result" :style="{ color: serviceResultMap[orderInfo.service_result].color, background: serviceResultMap[orderInfo.service_result].background }">
          {{ $t('swapPortrait.pp_order') }}{{ $t(serviceResultMap[orderInfo.service_result].name) }}
        </div>
      </div>
    </div>

    <div class="content-container" v-if="!loading">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane :label="$t('menu.pp_portrait_list')" name="portrait">
          <PortraitInfo v-if="activeTab == 'portrait'" :eventLineInfo="eventLineInfo" :stepInfo="stepInfo" :orderInfo="orderInfo" :userExperienceInfo="userExperienceInfo" @handleClickStep="handleClickStep" />
        </el-tab-pane>
        <el-tab-pane :label="$t('swapPortrait.pp_detail_info')" name="detail">
          <DetailInfo v-if="activeTab == 'detail'" :orderInfo="orderInfo" :diagnosisResult="diagnosisResult" :batteryInfo="batteryInfo" :userExperienceInfo="userExperienceInfo" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="loading-container" v-else>
      <div class="loader-17"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { serviceResultMap } from './components/constant'
import { apiGetDetailInfo } from '~/apis/portrait-list'
import { ElMessage } from 'element-plus'
import PortraitInfo from './components/portrait-info.vue'
import DetailInfo from './components/detail-info.vue'
import BackIcon from '~/views/single-station/icon/back-icon.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const orderInfo = ref({} as any)
const diagnosisResult = ref({} as any)
const batteryInfo = ref({} as any)
const userExperienceInfo = ref({} as any)
const eventLineInfo = ref([] as any)
const stepInfo = ref({} as any)
const activeTab = ref('' as any)

const formatStepInfo = () => {
  if (stepInfo.value.sub_events && stepInfo.value.sub_events.length > 0) {
    stepInfo.value.sub_events.forEach((item: any) => {
      if (item.expand === undefined) {
        item.expand = item.alarm_level === 3 || item.alarm_level === 2
      }
    })
  }
}

const handleClickStep = (item: any) => {
  stepInfo.value = item
  formatStepInfo()
}

/**
 * @description: 切换tab
 * @param {*} name
 * @return {*}
 */
const handleTabChange = (name: any) => {
  router.push({
    path: location.pathname,
    query: { ...route.query, tab: name }
  })
}

/**
 * @description: 回到上级页面
 * @return {*}
 */
const handleBack = () => {
  let query: any = sessionStorage.getItem('portrait-list')
  router.push({
    path: '/fault-diagnosis/portrait-list',
    query: JSON.parse(query)
  })
}

/**
 * @description: 获取数据
 * @return {*}
 */
const getList = async () => {
  loading.value = true
  try {
    const res = await apiGetDetailInfo(route.query.project, route.query.order)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      orderInfo.value = res.order_detail
      diagnosisResult.value = res.diagnosis_result
      batteryInfo.value = res.battery_info
      userExperienceInfo.value = res.user_experience_info
      eventLineInfo.value = res.event_line || []

      const thirdAlarmStep = eventLineInfo.value.filter((item: any) => item.alarm_level === 3)
      const secondAlarmStep = eventLineInfo.value.filter((item: any) => item.alarm_level === 2)
      const executedStep = eventLineInfo.value.filter((item: any) => item.executed)
      if (thirdAlarmStep.length > 0) {
        stepInfo.value = thirdAlarmStep[thirdAlarmStep.length - 1]
      } else if (secondAlarmStep.length > 0) {
        stepInfo.value = secondAlarmStep[secondAlarmStep.length - 1]
      } else {
        stepInfo.value = executedStep[executedStep.length - 1]
      }
      formatStepInfo()
    }
  } catch (error) {
    loading.value = false
  }
}

onBeforeMount(() => {
  activeTab.value = route.query.tab || 'portrait'
  getList()
})
</script>

<style lang="scss" scoped>
.portrait-detail-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #eef6f8 -6.51%, #f4f5f8 12.89%);
  .header-container {
    padding: 24px 24px 12px;
    .tag-container {
      height: 26px;
      display: flex;
      align-items: center;
      border-radius: 2px;
      padding: 2px 8px;
      font-size: 14px;
      line-height: 22px;
      margin-left: 8px;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
  }
  .loading-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
