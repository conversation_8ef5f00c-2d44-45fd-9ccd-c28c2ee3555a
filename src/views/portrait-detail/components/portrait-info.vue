<template>
  <div class="portrait-info-container">
    <div class="info-card">
      <div class="flex-box flex_j_c-space-between flex_a_i-center">
        <div class="head-title">{{ $t('swapPortrait.pp_swap_step') }}</div>
        <div class="flex-box flex_a_i-center" v-if="eventLineInfo.length > 0">
          <div class="second-alarm-icon"></div>
          <div class="font-size-12 line-height-18 color-59 margin_r-16">{{ $t('swapPortrait.pp_second_alarm') }}</div>
          <div class="third-alarm-icon"></div>
          <div class="font-size-12 line-height-18 color-59">{{ $t('swapPortrait.pp_third_alarm') }}</div>
        </div>
      </div>

      <div class="flex-box margin_t-16 padding_b-12 overflow-auto" v-if="eventLineInfo.length > 0">
        <div v-for="(item, index) in eventLineInfo" @click="handleClickStep(item)" class="flex-box flex_d-column flex_a_i-center flex-item_f-1" :style="{ minWidth: '120px', cursor: item.executed ? 'pointer' : 'not-allowed' }">
          <div class="width-full flex-box flex_a_i-center gap_16">
            <div class="horizontal-line" :style="{ opacity: index > 0 ? 1 : 0 }"></div>
            <div :class="['normal-index', { 'second-alarm-index': item.alarm_level === 2, 'third-alarm-index': item.alarm_level === 3, 'non-executed': !item.executed }]">
              {{ index + 1 }}
            </div>
            <div class="horizontal-line" :style="{ opacity: index < eventLineInfo.length - 1 ? 1 : 0 }"></div>
          </div>
          <WelkinCopyBoard :text="item.event_name" :showIcon="false" :cursorStyle="item.executed ? 'pointer' : 'not-allowed'" class="event-name" :style="{ color: item.alarm_level === 2 ? '#FF772E' : item.alarm_level === 3 ? '#F83535' : '#262626' }" />
          <div class="event-time" v-if="item.executed">{{ formatTimeToDay(item.event_timestamp) }}</div>
          <div class="event-time" v-if="item.executed && item.event_timestamp">{{ formatTimeWithoutDate(item.event_timestamp) }}</div>
        </div>
      </div>

      <el-empty :description="$t('common.pp_empty')" v-else>
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>

    <StepInfo :stepInfo="stepInfo" :orderInfo="orderInfo" :userExperienceInfo="userExperienceInfo" v-if="Object.keys(stepInfo).length > 0" />
  </div>
</template>

<script setup lang="ts">
import { formatTimeToDay, formatTimeWithoutDate } from '~/utils';
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue';
import StepInfo from './step-info.vue';

const props = defineProps({
  eventLineInfo: {
    type: Array as () => any,
    default: [],
  },
  stepInfo: {
    type: Object,
    default: {},
  },
  orderInfo: {
    type: Object,
    default: {},
  },
  userExperienceInfo: {
    type: Object,
    default: {},
  },
});
const emits = defineEmits(['handleClickStep']);

const handleClickStep = (item: any) => {
  if (!item.executed) return;
  emits('handleClickStep', item);
};
</script>

<style lang="scss" scoped>
.portrait-info-container {
  width: 100%;
  margin-top: 16px;
  :deep(.el-empty) {
    padding: 0;
    .el-empty__image {
      width: 100px;
    }
    .el-empty__description {
      margin-left: 20px;
    }
  }
  .info-card {
    width: 100%;
    padding: 24px 24px 12px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    .head-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #262626;
    }
    .second-alarm-icon {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #ff772e;
      margin-right: 8px;
    }
    .third-alarm-icon {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #f83535;
      margin-right: 8px;
    }
    %circle-index {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .normal-index {
      @extend %circle-index;
      color: #00bebe;
      border: 1px solid #00bebe;
      background-color: rgba(255, 255, 255, 0.5);
    }
    .second-alarm-index {
      @extend %circle-index;
      color: #ff772e;
      border: 1px solid #ff772e;
      background-color: rgba(255, 255, 255, 0.5);
    }
    .third-alarm-index {
      @extend %circle-index;
      color: #fff;
      border: 1px solid #f83535;
      background-color: #f83535;
    }
    .non-executed {
      @extend %circle-index;
      color: #262626;
      border: 1px solid #d9d9d9;
      background-color: rgba(255, 255, 255, 0.5);
    }
    .horizontal-line {
      flex: 1;
      height: 1px;
      background-color: #d9d9d9;
    }
    :deep(.event-name) {
      width: 100%;
      display: flex;
      justify-content: center;
      font-size: 16px;
      line-height: 24px;
      margin-top: 8px;
      margin-bottom: 4px;
    }
    .event-time {
      font-size: 12px;
      line-height: 18px;
      color: #595959;
    }
    ::-webkit-scrollbar {
      height: 4px;
    }
    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 10px;
    }
  }
}
</style>
