<template>
  <div class="detail-info-container">
    <!-- 订单详情 -->
    <div class="info-card order-card" v-if="orderInfo">
      <div class="flex-box flex_a_i-center gap_8">
        <OrderIcon />
        <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('swapPortrait.pp_order_detail') }}</span>
      </div>
      <div class="text-box">
        <div class="text-item">
          <div class="text-label">{{ $t('common.pp_device_name') }}</div>
          <div class="text-value">
            <WelkinCopyBoard :text="orderInfo.description || $t('common.pp_unnamed_device')" :showIcon="false" />
          </div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('swapPortrait.pp_order_start_time') }}</div>
          <div class="text-value">{{ formatTime(orderInfo.order_start_time) }}</div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('swapPortrait.pp_order_end_time') }}</div>
          <div class="text-value">{{ formatTime(orderInfo.order_end_time) }}</div>
        </div>
      </div>
      <div class="text-box">
        <div class="text-item">
          <div class="text-label">{{ $t('common.pp_device_id') }}</div>
          <div class="text-value">
            <WelkinCopyBoard :text="orderInfo.device_id" direction="rtl" />
          </div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('swapPortrait.pp_device_type') }}</div>
          <div class="text-value">{{ $t(projectMap[orderInfo.project]) }}</div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('swapPortrait.pp_vehicle_platform') }}</div>
          <div class="text-value">{{ orderInfo.vehicle_platform || '-' }}</div>
        </div>
      </div>
      <div class="text-box">
        <div class="text-item">
          <div class="text-label">{{ $t('satisfaction.pp_service_id') }}</div>
          <div class="text-value">
            <WelkinCopyBoard :text="orderInfo.service_id" direction="rtl" />
          </div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('satisfaction.pp_vehicle_id') }}</div>
          <div class="text-value">
            <WelkinCopyBoard :text="orderInfo.vehicle_id" direction="rtl" />
          </div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('common.pp_vehicle_brand') }}</div>
          <div class="text-value">{{ orderInfo.vehicle_brand ? orderInfo.vehicle_brand : '' }}-{{ orderInfo.vehicle_type ? orderInfo.vehicle_type : '' }}</div>
        </div>
      </div>
      <div class="text-box">
        <div class="text-item">
          <div class="text-label">{{ $t('satisfaction.pp_order_id') }}</div>
          <div class="text-value">
            <WelkinCopyBoard :text="orderInfo.order_id" direction="rtl" />
          </div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('swapPortrait.pp_vehicle_machine') }}</div>
          <div class="text-value">
            <WelkinCopyBoard :text="orderInfo.vehicle_global_version" direction="rtl" />
          </div>
        </div>
        <div class="text-item">
          <div class="text-label">{{ $t('swapPortrait.pp_software_version') }}</div>
          <div class="text-value">
            <WelkinCopyBoard :text="orderInfo.vehicle_software_version" direction="rtl" />
          </div>
        </div>
      </div>
      <div class="text-box">
        <div class="text-item">
          <div class="text-label">{{ $t('swapPortrait.pp_stuck_status') }}</div>
          <div class="text-value">
            <span v-if="isEmptyData(orderInfo.stuck_status_from_hive)">-</span>
            <span v-else class="tag-container" :style="{ color: stuckStatusMap[orderInfo.stuck_status_from_hive].color, background: stuckStatusMap[orderInfo.stuck_status_from_hive].background }">
              {{ orderInfo.stuck_status_from_hive ? $t('swapPortrait.pp_stuck') : $t('swapPortrait.pp_not_stuck') }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="info-card" v-else>
      <div class="flex-box flex_a_i-center gap_8">
        <OrderIcon />
        <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('swapPortrait.pp_order_detail') }}</span>
      </div>
      <el-empty :description="$t('common.pp_empty')">
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>

    <div class="flex-box gap_16 margin_t-16">
      <div class="left-container">
        <!-- 诊断结果 -->
        <div class="info-card" :class="batteryInfo ? 'height-240' : 'height-220'" v-if="diagnosisResult">
          <div class="flex-box flex_a_i-center gap_8 margin_b-10">
            <ResultIcon />
            <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('satisfaction.pp_diagnosis_result') }}</span>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('swapPortrait.pp_vdp_diagnosis_result') }}</div>
              <div class="text-value">
                <span>{{ isEmptyData(diagnosisResult.vdp_result) ? '-' : diagnosisResult.vdp_result }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="info-card" :class="batteryInfo ? 'height-240' : 'height-220'" v-else>
          <div class="flex-box flex_a_i-center gap_8 margin_b-10">
            <ResultIcon />
            <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('satisfaction.pp_diagnosis_result') }}</span>
          </div>
          <el-empty :description="$t('common.pp_empty')">
            <template #image>
              <WhiteEmptyDataIcon />
            </template>
          </el-empty>
        </div>

        <!-- 电池信息 -->
        <div class="info-card height-180" v-if="batteryInfo">
          <div class="flex-box flex_a_i-center gap_8">
            <BatteryIcon />
            <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('swapPortrait.pp_battery_info') }}</span>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('satisfaction.pp_old_battery_soc') }}</div>
              <div class="text-value">
                <span>{{ isEmptyData(batteryInfo.vehicle_battery_soc) ? '-' : batteryInfo.vehicle_battery_soc + '%' }}</span>
                <span v-if="!isEmptyData(batteryInfo.vehicle_battery_soc_oss)">（{{ batteryInfo.vehicle_battery_soc_oss + '%' }}）</span>
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('satisfaction.pp_new_battery_soc') }}</div>
              <div class="text-value">
                <span>{{ isEmptyData(batteryInfo.service_battery_soc) ? '-' : batteryInfo.service_battery_soc + '%' }}</span>
                <span v-if="!isEmptyData(batteryInfo.service_battery_soc_oss)">（{{ batteryInfo.service_battery_soc_oss + '%' }}）</span>
              </div>
            </div>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('swapPortrait.pp_old_battery_id') }}</div>
              <div class="text-value">
                <WelkinCopyBoard :text="batteryInfo.vehicle_battery_id" direction="rtl" />
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('swapPortrait.pp_new_battery_id') }}</div>
              <div class="text-value">
                <WelkinCopyBoard :text="batteryInfo.service_battery_id" direction="rtl" />
              </div>
            </div>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('satisfaction.pp_old_battery_degree') }}</div>
              <div class="text-value">{{ isEmptyData(batteryInfo.vehicle_battery_capacity) ? '-' : batteryInfo.vehicle_battery_capacity + $t('satisfaction.pp_degree') }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('satisfaction.pp_new_battery_degree') }}</div>
              <div class="text-value">{{ isEmptyData(batteryInfo.service_battery_capacity) ? '-' : batteryInfo.service_battery_capacity + $t('satisfaction.pp_degree') }}</div>
            </div>
          </div>
        </div>
        <div class="info-card height-200" v-else>
          <div class="flex-box flex_a_i-center gap_8">
            <BatteryIcon />
            <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('swapPortrait.pp_battery_info') }}</span>
          </div>
          <el-empty :description="$t('common.pp_empty')" style="margin-top: -14px">
            <template #image>
              <WhiteEmptyDataIcon />
            </template>
          </el-empty>
        </div>
      </div>

      <!-- 用户感知信息 -->
      <div class="flex-box flex_d-column">
        <div class="right-container" v-if="userExperienceInfo">
          <div class="flex-box flex_a_i-center gap_8 margin_b-4">
            <UserIcon />
            <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('satisfaction.pp_user_perception') }}</span>
          </div>
          <div class="text-item">
            <div class="text-label">{{ $t('satisfaction.pp_rating_stars') }}</div>
            <div class="flex-box gap_8 text-value" v-if="!isEmptyData(userExperienceInfo.score)">
              <StarIcon v-for="item in userExperienceInfo.score" v-if="userExperienceInfo.score > 0" />
              <NonStarIcon v-for="item in 5 - userExperienceInfo.score" v-if="userExperienceInfo.score < 5" />
            </div>
            <div v-else class="text-value">-</div>
          </div>
          <div class="text-item" style="align-items: flex-start">
            <div class="text-label">{{ $t('swapPortrait.pp_experience_tag') }}</div>
            <div class="flex-box flex_w-wrap gap_8" v-if="!isEmptyData(userExperienceInfo.user_tag)">
              <div class="tag-value" v-for="item in userExperienceInfo.user_tag">{{ item }}</div>
            </div>
            <div v-else class="text-value">-</div>
          </div>
          <div class="text-item">
            <div class="text-label">{{ $t('swapPortrait.pp_swap_reversed') }}</div>
            <div class="text-value">{{ isEmptyData(userExperienceInfo.is_reverse_swap) ? '-' : userExperienceInfo.is_reverse_swap ? $t('common.pp_yes') : $t('common.pp_no') }}</div>
          </div>
          <div class="text-item">
            <div class="text-label">{{ $t('swapPortrait.pp_swap_automated') }}</div>
            <div class="text-value">{{ isEmptyData(userExperienceInfo.is_automated_swap) ? '-' : userExperienceInfo.is_automated_swap ? $t('common.pp_yes') : $t('common.pp_no') }}</div>
          </div>
          <div class="text-item last-item">
            <div class="text-label">{{ $t('swapPortrait.pp_on_duty') }}</div>
            <div class="text-value">{{ isEmptyData(userExperienceInfo.is_on_duty) ? '-' : userExperienceInfo.is_on_duty ? $t('common.pp_yes') : $t('common.pp_no') }}</div>
          </div>
          <div class="flex-box flex_j_c-space-between margin_t-4">
            <div class="text-item">
              <div class="label">{{ $t('swapPortrait.pp_multiple_orders') }}</div>
              <div class="text-value flex-box flex_a_i-center" v-if="!isEmptyData(userExperienceInfo.is_multiple_swap)">
                <div v-if="!userExperienceInfo.is_multiple_swap">{{ $t('common.pp_no') }}</div>
                <div class="light-column" v-else @click="multiOrderVisible = true">{{ $t('swapPortrait.pp_details') }}</div>
              </div>
              <div v-else class="text-value">-</div>
            </div>
            <div class="vertical-line"></div>
            <div class="text-item">
              <div class="label">{{ $t('swapPortrait.pp_human_intervention') }}</div>
              <div class="text-value">{{ isEmptyData(userExperienceInfo.is_manual_intervention) ? '-' : userExperienceInfo.is_manual_intervention ? $t('common.pp_yes') : $t('common.pp_no') }}</div>
            </div>
          </div>
        </div>
        <div class="right-container" v-else>
          <div class="flex-box flex_a_i-center gap_8 margin_b-4">
            <UserIcon />
            <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('satisfaction.pp_user_perception') }}</span>
          </div>
          <div class="flex-item_f-1 flex-box flex_j_c-center flex_a_i-center">
            <el-empty :description="$t('common.pp_empty')">
              <template #image>
                <WhiteEmptyDataIcon />
              </template>
            </el-empty>
          </div>
        </div>

        <div class="flex-box gap_8 margin_t-8 height-120">
          <div class="info-card flex-item_f-1" style="gap: 2px">
            <div class="color-26 font-size-16">{{ $t('swapPortrait.pp_order_time') }}</div>
            <div class="color-26 font-size-28 margin_t-6">{{ getDuration(userExperienceInfo.order_start_time, userExperienceInfo.order_end_time, true) }}</div>
          </div>
          <div class="info-card flex-item_f-1" style="gap: 2px">
            <div class="color-26 font-size-16">{{ $t('swapPortrait.pp_service_time') }}</div>
            <div class="color-26 font-size-28 margin_t-6">{{ getDuration(userExperienceInfo.service_start_time, userExperienceInfo.service_end_time, true) }}</div>
          </div>
        </div>

        <MultiOrderDialog v-model:multiOrderVisible="multiOrderVisible" :multiOrderList="multiOrderList" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { projectMap, stuckStatusMap } from './constant'
import { getDuration, isEmptyData, formatTime } from '~/utils'
import MultiOrderDialog from './multi-order-dialog.vue'
import BatteryIcon from './icon/battery-icon.vue'
import ResultIcon from './icon/result-icon.vue'
import UserIcon from './icon/user-icon.vue'
import OrderIcon from './icon/order-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'
import StarIcon from '~/views/satisfaction-analysis/components/icon/star-icon.vue'
import NonStarIcon from '~/views/satisfaction-analysis/components/icon/non-star.vue'

const props = defineProps({
  orderInfo: {
    type: Object,
    default: {}
  },
  batteryInfo: {
    type: Object,
    default: {}
  },
  diagnosisResult: {
    type: Object,
    default: null
  },
  userExperienceInfo: {
    type: Object,
    default: {}
  }
})

const multiOrderVisible = ref(false)
const multiOrderList = ref(props.userExperienceInfo ? props.userExperienceInfo.multi_swap || [] : [])
</script>

<style lang="scss" scoped>
.detail-info-container {
  width: 100%;
  padding-top: 16px;
  .left-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
  }
  .info-card,
  .order-card,
  .right-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 24px;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    background-color: #fff;
    .text-box {
      display: grid;
      grid-template-columns: minmax(100px, 1fr) minmax(100px, 1fr);
      gap: 12px 96px;
    }
    .text-item {
      display: flex;
      align-items: center;
      .text-label {
        width: 110px;
        font-size: 14px;
        line-height: 22px;
        color: #8c8c8c;
        flex-shrink: 0;
      }
      .text-value {
        width: calc(100% - 120px);
        font-size: 14px;
        line-height: 22px;
        color: #262626;
        .tag-container {
          height: 26px;
          display: inline-flex;
          align-items: center;
          border-radius: 2px;
          padding: 2px 8px;
          font-size: 14px;
          line-height: 22px;
        }
      }
      .tag-value {
        height: 24px;
        display: flex;
        align-items: center;
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 2px;
        color: #262626;
        background-color: #e5f9f9;
      }
    }
    :deep(.el-empty) {
      padding: 0;
      .el-empty__image {
        width: 100px;
      }
      .el-empty__description {
        margin-left: 20px;
      }
    }
  }
  .right-container {
    width: 400px;
    height: 308px;
    & > .text-item > .text-label {
      width: 120px;
      font-size: 14px;
      line-height: 22px;
      color: #8c8c8c;
      flex-shrink: 0;
    }
    .label {
      width: 120px;
      font-size: 16px;
      line-height: 25px;
      color: #262626;
      flex-shrink: 0;
    }
    .last-item {
      padding-bottom: 16px;
      border-bottom: 1px solid #e6e7ec;
    }
    .vertical-line {
      width: 1px;
      height: 100%;
      background-color: #e6e7ec;
    }
  }
  .order-card {
    .text-box {
      grid-template-columns: minmax(100px, 5fr) minmax(100px, 5fr) minmax(100px, 4fr);
      & > .text-item > .text-value {
        white-space: nowrap;
      }
    }
  }
}
</style>
