export const projectMap = {
  PUS4: 'common.pp_pss4',
  PUS3: 'common.pp_pss3',
  PowerSwap2: 'common.pp_pss2',
  PowerSwap: 'common.pp_pss1'
} as any

export const statusNameMap = {
  '1': 'common.pp_yes',
  '0': 'common.pp_no',
  '-1': 'station.pp_unfinished'
} as any

export const serviceResultMap = {
  run: {
    name: 'swapPortrait.pp_run',
    color: '#2F54EB',
    background: '#F0F5FF'
  },
  cancel: {
    name: 'swapPortrait.pp_cancel',
    color: '#FF772E',
    background: '#FFF4E8'
  },
  success: {
    name: 'swapPortrait.pp_success',
    color: '#2F9C74',
    background: '#E8FCEA'
  },
  fail: {
    name: 'swapPortrait.pp_fail',
    color: '#E83030',
    background: '#FFF2F0'
  },
  unknown: {
    name: 'swapPortrait.pp_unknown',
    color: '#595959',
    background: '#EBECEE'
  }
} as any

export const stuckStatusMap = {
  false: {
    color: '#2F9C74',
    background: '#E8FCEA'
  },
  true: {
    color: '#E83030',
    background: '#FFF2F0'
  }
} as any

export const alarmColorMap = {
  3: {
    color: '#F83535',
    background: 'rgba(248, 53, 53, 0.5)'
  },
  2: {
    color: '#FF772E',
    background: 'rgba(255, 119, 46, 0.5)'
  }
} as any

export const getAlarmColor = (level: number) => {
  return alarmColorMap[level] || { color: '#262626', background: '#EFFBFB' }
}

export const getExpandIconColor = (level: number) => {
  const match = alarmColorMap[level] || { color: '#01A0AC', background: '#EFFBFB' }
  return match.color
}

const alarmCardColorMap = {
  3: {
    color: '#F83535',
    background: 'rgba(253, 67, 72, 0.05)'
  },
  2: {
    color: '#FF772E',
    background: 'rgba(253, 140, 8, 0.05)'
  }
} as any

export const getAlarmCardColor = (level: number) => {
  return alarmCardColorMap[level] || { color: '#262626', background: '#EFFBFB' }
}
