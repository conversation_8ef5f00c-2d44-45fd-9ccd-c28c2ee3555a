<template>
  <div class="step-info-container">
    <div class="flex-box flex_j_c-space-between margin_b-16">
      <div class="flex-box flex_d-column gap_8">
        <div class="font-size-16 line-height-24 font-weight-500" :style="{ color: getAlarmColor(stepInfo.alarm_level).color }">
          {{ stepInfo.event_name }}
        </div>
        <div class="font-size-16 line-height-24">
          <span class="color-8c">{{ $t('common.pp_start_time') }}：</span>
          <span class="color-26 margin_r-24">{{ formatTime(stepInfo.event_timestamp) }}</span>
          <span class="color-8c">{{ $t('common.pp_end_time') }}：</span>
          <span class="color-26 margin_r-24">{{ formatTime(stepInfo.estimate_event_end_timestamp) }}</span>
          <span class="color-8c">{{ $t('swapPortrait.pp_duration') }}：</span>
          <span class="color-26 margin_r-24">{{ getDuration(stepInfo.event_timestamp, stepInfo.estimate_event_end_timestamp, true) }}</span>
        </div>
      </div>

      <el-tooltip placement="top-start" effect="light">
        <template #content>
          <div class="flex-box flex_a_i-center gap_8">
            <TipIcon />
            <span class="font-size-12 line-height-18 color-26">{{ $t('swapPortrait.pp_tip') }}</span>
          </div>
        </template>
        <div class="jump-container" @click="handleJump">
          <div class="font-size-14 line-height-22 color-26">{{ $t('menu.pp_service_detail') }}</div>
          <JumpIcon />
        </div>
      </el-tooltip>
    </div>

    <div class="table-head margin_b-16" v-if="stepInfo.sub_events && stepInfo.sub_events.length > 0">
      <div class="flex-box flex_j_c-center flex_a_i-center gap_8">
        <ProcessIcon />
        <div class="head-title">{{ $t('swapPortrait.pp_process') }}</div>
      </div>
      <div class="flex-box flex_j_c-center flex_a_i-center gap_8">
        <TimeIcon />
        <div class="head-title">{{ $t('swapPortrait.pp_time') }}</div>
      </div>
      <div class="flex-box flex_j_c-center flex_a_i-center gap_8">
        <AlarmIcon />
        <div class="head-title">{{ $t('swapPortrait.pp_alarm') }}</div>
      </div>
    </div>

    <div v-if="stepInfo.sub_events && stepInfo.sub_events.length > 0">
      <div v-for="(item, index) in stepInfo.sub_events" class="table-content">
        <div class="left-content" :class="[{ 'second-alarm-left': item.alarm_level === 2, 'third-alarm-left': item.alarm_level === 3 }]">
          <div class="flex-box flex_j_c-space-between flex_a_i-center">
            <div class="flex-box flex_a_i-center gap_8" :class="{ 'cursor-pointer': item.event_context && Object.keys(item.event_context).length > 0 }" @click="item.expand = !item.expand">
              <span class="font-size-16 line-height-24 font-weight-500" :style="{ color: getAlarmColor(item.alarm_level).color }"> {{ index + 1 }}. {{ item.event_name }} </span>
              <template v-if="item.event_context && Object.keys(item.event_context).length > 0">
                <ExpandIcon v-if="!item.expand" :color="getExpandIconColor(item.alarm_level)" />
                <CollapseIcon v-else :color="getExpandIconColor(item.alarm_level)" />
              </template>
            </div>

            <div class="station-icon">
              <StationIcon />
              <span class="font-size-14 color-a0">{{ $t('swapPortrait.pp_pss') }}</span>
            </div>
          </div>

          <div v-if="item.expand && item.event_context && Object.keys(item.event_context).length > 0" class="margin_t-4">
            <div v-for="(value, key, eventIndex) in item.event_context" class="flex-box font-size-14 line-height-22 color-59">
              <div :style="{ width: eventIndex < 9 ? '20px' : '30px', flexShrink: 0 }">{{ eventIndex + 1 }}.</div>
              <div style="word-break: break-all">{{ key }}：{{ value }}</div>
            </div>
          </div>
        </div>

        <div class="mid-content">
          <div class="mid-time" :style="{ color: getExpandIconColor(item.alarm_level) }">{{ formatTimeWithoutDate(item.event_timestamp) }}</div>
          <div>
            <div class="mid-circle" :style="{ backgroundColor: getExpandIconColor(item.alarm_level) }"></div>
            <div class="mid-line"></div>
          </div>
        </div>

        <div class="right-content">
          <template v-if="item.alarms && item.alarms.filter((k: any) => k.alarm_level === 2 || k.alarm_level === 3).length > 0">
            <div v-for="(alarm, alarmIndex) in item.alarms.filter((k: any) => k.alarm_level === 2 || k.alarm_level === 3)" class="flex-box flex_a_i-center gap_8" :style="{ color: getAlarmCardColor(alarm.alarm_level).color }">
              <SecondAlarm v-if="alarm.alarm_level === 2" />
              <ThirdAlarm v-else-if="alarm.alarm_level === 3" />
              <div class="font-size-12 line-height-18">{{ formatTimeWithoutDate(alarm.alarm_create_time) }}</div>
              <div class="alarm-content" :style="{ backgroundColor: getAlarmCardColor(alarm.alarm_level).background }">
                <div>{{ $t('swapPortrait.pp_alarm') }}{{ alarmIndex + 1 }}: {{ alarm.alarm_name }}</div>
                <div class="alarm-box" :style="{ backgroundColor: alarm.alarm_level === 3 ? 'rgba(253, 67, 72, 0.1)' : 'rgba(253, 140, 8, 0.1)' }">{{ alarm.alarm_level === 3 ? $t('swapPortrait.pp_third_alarm') : $t('swapPortrait.pp_second_alarm') }}</div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <div v-else class="margin_t-40">
      <el-empty :description="$t('swapPortrait.pp_empty_text')">
        <template #image>
          <WhiteEmptyDataIcon />
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { getAlarmColor, getExpandIconColor, getAlarmCardColor } from './constant'
import { formatTime, getDuration, formatTimeWithoutDate } from '~/utils'
import { ElMessage } from 'element-plus'
import ProcessIcon from './icon/process-icon.vue'
import TimeIcon from './icon/time-icon.vue'
import AlarmIcon from './icon/alarm-icon.vue'
import JumpIcon from './icon/jump-icon.vue'
import TipIcon from './icon/tip-icon.vue'
import ExpandIcon from './icon/expand-icon.vue'
import CollapseIcon from './icon/collapse-icon.vue'
import StationIcon from './icon/station.vue'
import SecondAlarm from './icon/second-alarm.vue'
import ThirdAlarm from './icon/third-alarm.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'

const props = defineProps({
  stepInfo: {
    type: Object,
    default: {}
  },
  orderInfo: {
    type: Object,
    default: {}
  },
  userExperienceInfo: {
    type: Object,
    default: {}
  }
})

const { t } = useI18n()
const store = useStore()
const pathMap = ref(computed(() => store.state.vehicle.pathMap))

/**
 * @description: 跳转到服务详情
 * @return {*}
 */
const handleJump = () => {
  if(!props.orderInfo.service_id) {
    ElMessage.warning(t('swapPortrait.pp_jump_warn'))
    return
  }
  window.open(`//${location.host}/${pathMap.value[props.orderInfo.project]}/service-list/service-detail/${props.orderInfo.device_id}/${props.orderInfo.service_id}?start_time=${props.userExperienceInfo.service_start_time}&end_time=${props.userExperienceInfo.service_end_time}`)
}
</script>

<style lang="scss" scoped>
.step-info-container {
  width: 100%;
  padding: 24px;
  margin-top: 16px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dcf2f3;
  .jump-container {
    height: 32px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 4px;
    padding: 5px 16px;
    border: 1px solid #bfbfbf;
    cursor: pointer;
  }
  .table-head {
    height: 40px;
    display: grid;
    grid-template-columns: 17fr 138px 10fr;
    background-color: rgba(198, 239, 239, 0.7);
    .head-title {
      font-size: 14px;
      color: #262626;
      font-weight: bold;
    }
  }
  .table-content {
    display: grid;
    grid-template-columns: 17fr 138px 10fr;
    .left-content {
      padding: 16px;
      border-top: 1px solid rgba(0, 190, 190, 0.5);
      background-color: #effbfb;
      .station-icon {
        height: 32px;
        padding: 5px 12px;
        display: flex;
        align-items: center;
        gap: 6px;
        border-radius: 4px;
        border: 1px solid #01a0ac;
        background-color: #fff;
      }
    }
    .second-alarm-left {
      border-top: 1px solid rgba(255, 119, 46, 0.5);
      background-color: #fff4e8;
    }
    .third-alarm-left {
      border-top: 1px solid rgba(248, 53, 53, 0.5);
      background-color: #fff7f6;
    }
    .mid-content {
      display: flex;
      gap: 6px;
      .mid-time {
        width: 48px;
        font-size: 12px;
        line-height: 18px;
        margin-top: -9px;
        margin-left: 8px;
      }
      .mid-circle {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-top: -4px;
      }
      .mid-line {
        width: 1px;
        height: 100%;
        margin-left: 3.5px;
        background-color: #d9d9d9;
      }
    }
    .right-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      justify-content: space-around;
      margin-top: 10px;
      margin-left: -78px;
      .alarm-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        line-height: 24px;
        border-radius: 4px;
        padding: 4px 16px;
        .alarm-box {
          height: 26px;
          border-radius: 2px;
          padding: 2px 8px;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
