<template>
  <div class="multi-order-dialog">
    <el-dialog :title="$t('swapPortrait.pp_dialog_title')" v-model="multiOrderVisible" width="1200px" align-center @close="handleCloseDialog">
      <div v-for="item in multiOrderList" class="flex-box flex_w-wrap" style="column-gap: 12px">
        <div class="flex-box flex_d-column flex_a_i-center margin_t-6 vertical-line">
          <DotIcon />
        </div>
        <div class="flex-box flex_d-column flex-item_f-1 gap_12 margin_b-32">
          <div class="row-container">
            <span class="label-text">{{ $t('satisfaction.pp_order_start_time') }}</span>
            <span class="value-text">{{ formatTime(item.order_start_time) }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_order_end_time') }}</span>
            <span class="value-text">{{ formatTime(item.order_end_time) }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_order_id') }}</span>
            <span class="value-text">
              <WelkinCopyBoard :text="item.order_id" direction="rtl" />
            </span>
          </div>
          <div class="row-container">
            <span class="label-text">{{ $t('satisfaction.pp_order_duration') }}</span>
            <span class="value-text">{{ getDuration(item.order_start_time, item.order_end_time) }}</span>
            <span class="label-text">{{ $t('station.pp_service_start_time') }}</span>
            <span class="value-text">{{ formatTime(item.service_start_time) }}</span>
            <span class="label-text">{{ $t('station.pp_service_end_time') }}</span>
            <span class="value-text">{{ formatTime(item.service_end_time) }}</span>
          </div>
          <div class="row-container">
            <span class="label-text">{{ $t('station.pp_service_duration') }}</span>
            <span class="value-text">{{ getDuration(item.service_start_time, item.service_end_time) }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_swap_normal') }}</span>
            <span class="value-text">{{ [1, 0, -1].includes(item.finish_result) ? $t(statusNameMap[item.finish_result]) : '-' }}</span>
            <span class="label-text">{{ $t('satisfaction.pp_service_id') }}</span>
            <span class="value-text">
              <WelkinCopyBoard :text="item.service_id" direction="rtl" />
            </span>
          </div>
        </div>
        <div class="flex-box gap_12" style="flex: 100%; width: 100%" v-if="item.interval">
          <div class="flex-box flex_d-column flex_a_i-center margin_t-6 ellipsis-vertical-line">
            <DotIcon />
          </div>
          <div class="font-size-14 line-height-22 color-8c margin_b-32">{{ $t('satisfaction.pp_hidden1') + item.interval + $t('satisfaction.pp_hidden2') }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { statusNameMap } from './constant'
import { formatTime, getDuration } from '~/utils'
import DotIcon from './icon/dot-icon.vue'

interface OrderItem {
  order_start_time: number
  order_end_time: number
  order_id: string
  service_start_time: number
  service_end_time: number
  service_id: string
  finish_result: number
  project: string
  interval?: number
}

interface Props {
  multiOrderVisible: Boolean
  multiOrderList: OrderItem[]
}

const props = defineProps<Props>()

const emits = defineEmits(['update:multiOrderVisible'])

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleCloseDialog = () => {
  emits('update:multiOrderVisible', false)
}
</script>

<style lang="scss" scoped>
.multi-order-dialog {
  :deep(.el-dialog__header) {
    padding: 24px;
    .el-dialog__title {
      color: #1f1f1f;
      font-size: 18px;
    }
  }
  :deep(.el-dialog__body) {
    padding: 0 24px 24px;
    .vertical-line,
    .ellipsis-vertical-line {
      position: relative;
    }
    .vertical-line::before,
    .ellipsis-vertical-line::before {
      content: '';
      position: absolute;
      top: 9px;
      left: 4px;
      width: 1px;
      height: 94%;
      background-color: #d9d9d9;
    }
    .ellipsis-vertical-line::before {
      height: 88%;
    }
    .row-container {
      display: grid;
      grid-template-columns: 120px 1fr 120px 1fr 120px 1fr;
      .label-text {
        font-size: 14px;
        line-height: 22px;
        color: #8c8c8c;
        white-space: nowrap;
        overflow: hidden;
      }
      .value-text {
        font-size: 14px;
        line-height: 22px;
        color: #262626;
        overflow: hidden;
      }
    }
  }
}
</style>
