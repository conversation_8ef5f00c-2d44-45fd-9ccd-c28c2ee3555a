<template>
  <div class="satisfaction-analysis-container" :class="{ 'gray-background': activeTab == 'dashboard' }">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_satisfaction_analysis') }}</div>
      <SegmentedControl v-model="activeTab" :options="tabOptions" @handleChangeTab="handleChangeTab" />
    </div>

    <div class="content-container">
      <DataDashboard v-if="activeTab == 'dashboard'" :l1LabelList="l1LabelList" />
      <OrderList v-if="activeTab == 'order'" :diagnosisResultOptions="diagnosisResultOptions" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { removeNullKeys } from '~/utils'
import { convertToOptions } from './components/constant'
import { apiGetLabelTag } from '~/apis/satisfaction-analysis'
import OrderList from './components/order-list.vue'
import DataDashboard from './components/data-dashboard.vue'
import { ElMessage } from 'element-plus'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const activeTab = ref('' as any)
const l1LabelList = ref([] as any)
const diagnosisResultOptions = ref([] as any)
const tabOptions = ref([
  { label: 'satisfaction.pp_dashboard', value: 'dashboard' },
  { label: 'satisfaction.pp_order_list', value: 'order' }
])

const handleChangeTab = (tab: string) => {
  activeTab.value = tab
  router.push({
    path: location.pathname,
    query: removeNullKeys({ tab: activeTab.value, start_time: route.query.start_time, end_time: route.query.end_time })
  })
}

/**
 * @description: 获取L3/L2/L1标签列表
 * @return {*}
 */
const getLabelList = async () => {
  const res = await apiGetLabelTag()
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    l1LabelList.value = res.data ? Object.keys(res.data) : []
    diagnosisResultOptions.value = convertToOptions(res.data)
  }
}

onBeforeMount(() => {
  activeTab.value = route.query.tab || 'dashboard'
  getLabelList()
})
</script>

<style lang="scss" scoped>
.satisfaction-analysis-container {
  font-family: 'Blue Sky Standard';
  background: linear-gradient(180deg, #e2f9f9 -6.51%, #fff 10%);
  padding: 24px;
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px 24px;
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-form-item__label-wrap {
          margin: 0 !important;
        }
        .el-form-item__content {
          white-space: nowrap;
          align-items: flex-start;
          flex-wrap: nowrap;
        }
        .el-date-editor .el-range-input {
          color: #262626;
        }
        .el-date-editor .el-range__icon {
          margin-right: 10px;
        }
        .el-select .el-select__tags .el-tag--info {
          color: #262626;
        }
        .el-radio {
          margin-left: 16px;
          margin-right: 0px;
          .el-radio__label {
            color: #262626;
            font-weight: normal;
          }
        }
        .el-tag.el-tag--info {
          color: #262626;
        }
        .brand-cascader {
          width: 100%;
          &:hover {
            position: relative;
            z-index: 2;
          }
        }
      }
    }
    :deep(.tag-container) {
      height: 24px;
      background: #e5f9f9;
      padding: 3px 8px;
      border-radius: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      line-height: 19px;
    }
  }
}
.gray-background {
  background: linear-gradient(180deg, #e2f9f9 -6.51%, #f8f8f8 12.89%);
}
</style>
