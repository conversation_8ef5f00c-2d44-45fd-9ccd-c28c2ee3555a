<template>
  <div class="data-dashboard-container">
    <el-form :model="form">
      <el-form-item :label="$t('satisfaction.pp_evaluation_time')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getYesterdayShortcuts()" :clearable="false" :disabledDate="getDisabledYesterdayDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('common.pp_device_type')">
        <el-select v-model="form.project" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('satisfaction.pp_rating_level')">
        <el-select v-model="form.score" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in scoreOptions" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_vehicle_platform')">
        <el-select v-model="form.car_platform" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in platformOptions" :key="item" :value="item" :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_vehicle_brand')">
        <el-cascader v-model="brand" :options="brandOptions" :props="cascaderProps" collapse-tags collapse-tags-tooltip clearable @change="handleBrandChange" class="brand-cascader" :placeholder="$t('common.pp_please_select')" />
      </el-form-item>
      <el-form-item :label="$t('common.pp_urban_company')">
        <el-select v-model="form.city_companies" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in cityCompanyOptions" :key="item" :value="item" :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_company_division')">
        <el-select v-model="form.city_company_groups" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in cityGroupsOptions" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('satisfaction.pp_device_select')">
        <el-select v-model="form.device_ids" filterable remote clearable :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
          <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
            <span style="float: left">{{ item.description }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <div class="width-full flex-box flex_j_c-space-between flex_a_i-center">
          <div>
            <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
          </div>
          <el-button @click="handleDownload" class="welkin-secondary-button width-108 height-32" style="padding: 5px 16px">
            <DownloadIcon />
            <span class="margin_l-4">{{ $t('common.pp_download_data') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <div class="content-container" v-if="!loading">
      <el-row :gutter="16">
        <el-col :span="12">
          <div class="card-container height-304">
            <div id="chart1" class="width-full height-full cursor-default-canvas" v-show="hasChart1"></div>
            <div v-if="!hasChart1" class="width-full height-full">
              <div class="font-size-14 font-weight-bold color-26">{{ $t('satisfaction.pp_chart1_title') }}</div>
              <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
                <template #image>
                  <WhiteEmptyDataIcon />
                </template>
              </el-empty>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="card-container height-304">
            <div id="chart2" class="width-full height-full cursor-default-canvas" v-show="hasChart2"></div>
            <div v-if="!hasChart2" class="width-full height-full">
              <div class="font-size-14 font-weight-bold color-26">{{ $t('satisfaction.pp_chart2_title') }}</div>
              <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
                <template #image>
                  <WhiteEmptyDataIcon />
                </template>
              </el-empty>
            </div>
          </div>
        </el-col>
      </el-row>

      <div class="card-container flex-box flex_d-column gap_24 height-998">
        <div class="flex-box flex_j_c-flex-end flex_a_i-center gap_8 l1-label-select">
          <div class="font-size-14 color-59">L1标签</div>
          <el-select v-model="form.l1_labels" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" @change="handleLabelChange">
            <el-option v-for="item in l1LabelList" :key="item" :value="item" :label="item" />
          </el-select>
        </div>
        <div class="height-300" v-if="!loading2">
          <div id="chart3" class="width-full height-full" v-show="hasChart3"></div>
          <div v-if="!hasChart3" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('satisfaction.pp_chart3_title') }}</div>
            <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyDataIcon />
              </template>
            </el-empty>
          </div>
        </div>
        <div class="height-300" v-if="!loading2">
          <div id="chart4" class="width-full height-full" v-show="hasChart4"></div>
          <div v-if="!hasChart4" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('satisfaction.pp_chart4_title') }}</div>
            <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyDataIcon />
              </template>
            </el-empty>
          </div>
        </div>
        <div class="height-300" v-if="!loading2">
          <div id="chart5" class="width-full height-full" v-show="hasChart5"></div>
          <div v-if="!hasChart5" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('satisfaction.pp_chart5_title') }}</div>
            <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyDataIcon />
              </template>
            </el-empty>
          </div>
        </div>

        <div class="width-full height-300 flex-box flex_j_c-center flex_a_i-center" v-else>
          <div class="loader-17"></div>
        </div>
      </div>
    </div>

    <div class="loading-container" v-else>
      <div class="loader-17"></div>
    </div>

    <LabelDialog v-model:labelVisible="labelVisible" :l2Label="l2Label" :l3List="l3List" />
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef, h, onBeforeMount, computed, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import type { ParseConfig } from '~/types/common/query-parser'
import { apiGetDevices } from '~/apis/home'
import { apiGetDashboard, apiDownloadDashboard } from '~/apis/satisfaction-analysis'
import { removeNullKeys, clearJson, parseQueryParams } from '~/utils'
import { threeWeeksAgoStart, twoWeeksAgoEnd, getYesterdayShortcuts, getDisabledYesterdayDate, projectOptions, scoreOptions, pieOptionMock, barOptionMock1 } from './constant'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import DownloadIcon from '~/assets/svg/download-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'
import { debounce, cloneDeep } from 'lodash-es'
import { ElMessage } from 'element-plus'
import LabelDialog from './label-dialog.vue'
import * as echarts from 'echarts'

const props = defineProps({
  l1LabelList: {
    type: Array,
    default: () => []
  }
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const cascaderProps = ref({ multiple: true })
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const loading = ref(false)
const loading2 = ref(false)
const remoteDeviceLoading = ref(false)
const hasChart1 = ref(false)
const hasChart2 = ref(false)
const hasChart3 = ref(false)
const hasChart4 = ref(false)
const hasChart5 = ref(false)
const isL1Label = ref(false)
const labelVisible = ref(false)
const l2Label = ref('')
const l3List = ref([] as any)
const echartsArr = ref([] as any)
const brand = ref([] as any)
const deviceOptions = ref([] as any)
const datePicker = ref([threeWeeksAgoStart, twoWeeksAgoEnd] as any)
const form = ref({
  start_time: threeWeeksAgoStart,
  end_time: twoWeeksAgoEnd,
  project: [],
  score: [1, 2, 3],
  ev_brand: '',
  ev_type: '',
  city_companies: [],
  city_company_groups: [],
  device_ids: [],
  car_platform: '',
  l1_labels: []
})
const searchForm = ref({} as any)
const list = ref({} as any)
const brandOptions = ref(computed(() => store.state.vehicle.brandList))
const platformOptions = ref(computed(() => store.state.vehicle.carPlatform))
const cityCompanyOptions = ref(computed(() => store.state.vehicle.cityCompany))
const cityGroupsOptions = ref(computed(() => store.state.vehicle.cityCompanyGroup))
const isCollapse = computed(() => store.state.menus.collapse)
const chartOption1 = cloneDeep(pieOptionMock)
const chartOption2 = cloneDeep(barOptionMock1)
const chartOption3 = cloneDeep(barOptionMock1)
const chartOption4 = cloneDeep(barOptionMock1)
const chartOption5 = cloneDeep(barOptionMock1)

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  myChart.on('click', (params: any) => {
    if (['chart3', 'chart4', 'chart5'].includes(chartId)) {
      const seriesMap: Record<string, string> = {
        chart3: 'l2_label_satisfy_count',
        chart4: 'l2_label_satisfy_avg',
        chart5: 'l2_label_satisfy_loss'
      }
      l2Label.value = params.name
      l3List.value = list.value[seriesMap[chartId]].find((item: any) => item.name === params.name).l3_label_distribution || []
      labelVisible.value = true
    }
  })
  option && myChart.setOption(option)
  echartsArr.value.push(myChart)
  window.addEventListener('resize', () => myChart.resize())
}
const setCharts = () => {
  if (hasChart1.value && !isL1Label.value) echartRender('chart1', chartOption1)
  if (hasChart2.value && !isL1Label.value) echartRender('chart2', chartOption2)
  if (hasChart3.value) echartRender('chart3', chartOption3)
  if (hasChart4.value) echartRender('chart4', chartOption4)
  if (hasChart5.value) echartRender('chart5', chartOption5)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 下载
 * @return {*}
 */
const handleDownload = () => {
  if (searchForm.value.end_time - searchForm.value.start_time > 7 * 24 * 60 * 60 * 1000) {
    ElMessage.error(t('satisfaction.pp_time_error'))
    return
  }
  let formData = cloneDeep(searchForm.value) as any
  formData.project = formData.project.join(',')
  formData.score = formData.score.join(',')
  formData.device_ids = formData.device_ids.join(',')
  formData.city_companies = formData.city_companies.join(',')
  formData.city_company_groups = formData.city_company_groups.join(',')
  delete formData.l1_labels
  apiDownloadDashboard(formData)
}

const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true
    const params = { project: 'PowerSwap,PowerSwap2,PUS3,PUS4', name: val, device_ids: deviceIds, limit: 30 }
    const res = await apiGetDevices(params)
    remoteDeviceLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 选择车辆品牌
 * @return {*}
 */
const handleBrandChange = () => {
  const result = brand.value.reduce(
    (acc: any, curr: any) => {
      const [brand, type] = curr
      if (!acc.ev_brand.includes(brand)) acc.ev_brand.push(brand)
      if (!acc.ev_type.includes(type)) acc.ev_type.push(type)
      return acc
    },
    { ev_brand: [], ev_type: [] }
  )
  form.value.ev_brand = result.ev_brand.join(',')
  form.value.ev_type = result.ev_type.join(',')
}

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 选择L1标签
 * @param {*} val
 * @return {*}
 */
const handleLabelChange = debounce(async (val: any) => {
  isL1Label.value = true
  getList(false)
}, 800)

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  isL1Label.value = false
  clearJson(form.value)
  brand.value = []
  form.value.score = [1, 2, 3]
  datePicker.value = [threeWeeksAgoStart, twoWeeksAgoEnd]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  isL1Label.value = false
  form.value.l1_labels = []
  searchForm.value = cloneDeep(form.value)
  getList()
}

/**
 * @description: 处理数据
 * @param {*} data
 * @return {*}
 */
const formatData = (data: any) => {
  // 低分数量（L1-相关业务）
  if (!isL1Label.value) {
    if (data.l1_label_satisfy_count && data.l1_label_satisfy_count.length > 0) {
      hasChart1.value = true
      chartOption1.title.text = t('satisfaction.pp_chart1_title')
      chartOption1.series[0].data = data.l1_label_satisfy_count
    } else {
      hasChart1.value = false
    }
  }

  // 满意度损失（L1-相关业务）
  if (!isL1Label.value) {
    if (data.l1_label_satisfy_loss && data.l1_label_satisfy_loss.length > 0) {
      hasChart2.value = true
      chartOption2.series[0].cursor = 'default'
      chartOption2.dataZoom[0].disabled = true
      chartOption2.title.text = t('satisfaction.pp_chart2_title')
      chartOption2.xAxis.data = data.l1_label_satisfy_loss.map((item: any) => item.name)
      chartOption2.series[0].data = data.l1_label_satisfy_loss.map((item: any) => item.value)
    } else {
      hasChart2.value = false
    }
  }

  // 低分数量（L2-相关产品）
  if (data.l2_label_satisfy_count && data.l2_label_satisfy_count.length > 0) {
    hasChart3.value = true
    chartOption3.title.text = t('satisfaction.pp_chart3_title')
    chartOption3.xAxis.data = data.l2_label_satisfy_count.map((item: any) => item.name)
    chartOption3.series[0].data = data.l2_label_satisfy_count.map((item: any) => item.value)
  } else {
    hasChart3.value = false
  }

  // 低分均分（L2-相关产品）
  if (data.l2_label_satisfy_avg && data.l2_label_satisfy_avg.length > 0) {
    hasChart4.value = true
    chartOption4.color = ['#78D9EF']
    chartOption4.title.text = t('satisfaction.pp_chart4_title')
    chartOption4.xAxis.data = data.l2_label_satisfy_avg.map((item: any) => item.name)
    chartOption4.series[0].data = data.l2_label_satisfy_avg.map((item: any) => item.value)
  } else {
    hasChart4.value = false
  }

  // 满意度损失（L2-相关产品）
  if (data.l2_label_satisfy_loss && data.l2_label_satisfy_loss.length > 0) {
    hasChart5.value = true
    chartOption5.color = ['#68C789']
    chartOption5.title.text = t('satisfaction.pp_chart5_title')
    chartOption5.xAxis.data = data.l2_label_satisfy_loss.map((item: any) => item.name)
    chartOption5.series[0].data = data.l2_label_satisfy_loss.map((item: any) => item.value)
  } else {
    hasChart5.value = false
  }
  nextTick(() => setCharts())
}

/**
 * @description: 获取订单列表
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.project = formData.project.join(',')
  formData.score = formData.score.join(',')
  formData.device_ids = formData.device_ids.join(',')
  formData.city_companies = formData.city_companies.join(',')
  formData.city_company_groups = formData.city_company_groups.join(',')
  if (isL1Label.value) formData.l1_labels = form.value.l1_labels.join(',')
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys({ ...formData, score: formData.score || 'all', tab: 'dashboard' }) }
    })
  }
  if (isL1Label.value) {
    loading2.value = true
  } else {
    loading.value = true
  }
  try {
    const res = await apiGetDashboard(removeNullKeys(formData))
    if (isL1Label.value) {
      loading2.value = false
    } else {
      loading.value = false
    }
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      formatData(list.value)
    }
  } catch (error: any) {
    if (isL1Label.value) {
      loading2.value = false
    } else {
      loading.value = false
    }
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  const initParams: any = route.query
  const paramsConfig: ParseConfig = {
    params: [
      { key: 'ev_brand', type: 'string', defaultValue: '' },
      { key: 'ev_type', type: 'string', defaultValue: '' },
      { key: 'car_platform', type: 'string', defaultValue: '' },
      { key: 'project', type: 'array', arrayType: 'string', defaultValue: [] },
      { key: 'device_ids', type: 'array', arrayType: 'string', defaultValue: [] },
      { key: 'city_companies', type: 'array', arrayType: 'string', defaultValue: [] },
      { key: 'city_company_groups', type: 'array', arrayType: 'string', defaultValue: [] }
    ],
    target: form.value
  }
  if (!initParams.tab || initParams.tab == 'dashboard') {
    if (!!initParams.score) {
      form.value.score = initParams.score == 'all' ? [] : initParams.score.split(',').map(Number)
    } else {
      form.value.score = [1, 2, 3]
    }
    brand.value = !!initParams.ev_type ? initParams.ev_type.split(',') : []
    parseQueryParams(initParams, paramsConfig)
  }
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  }
  searchForm.value = cloneDeep(form.value)
  getList(false)
  searchDeviceList('NIO', form.value.device_ids.join(','))
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.data-dashboard-container {
  :deep(.brand-cascader) {
    .el-input__wrapper {
      height: 32px;
    }
  }
  :deep(.el-empty__description) {
    margin-left: 40px;
  }
  .content-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    .card-container {
      width: 100%;
      position: relative;
      border: 1px solid #dcf2f3;
      border-radius: 4px;
      padding: 24px;
      background-color: #fff;
      :deep(.l1-label-select) {
        width: 100%;
        position: absolute;
        right: 24px;
        top: 24px;
        .el-select {
          width: calc((100% - 300px) / 3);
        }
        .el-select .el-select__tags .el-tag--info {
          color: #262626;
        }
      }
    }
  }
  .loading-container {
    height: calc(100vh - 300px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
