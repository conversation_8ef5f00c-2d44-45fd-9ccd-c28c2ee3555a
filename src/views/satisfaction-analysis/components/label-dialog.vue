<template>
  <div class="l3-label-dialog">
    <el-dialog v-model="labelVisible" :title="l2Label" width="392px" @close="handleClose" center align-center :show-close="false">
      <el-table :data="l3List" max-height="240" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column type="index" label="No." width="56" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" :label="$t('satisfaction.pp_l3_label')" width="200" show-overflow-tooltip />
        <el-table-column prop="proportion" :label="$t('satisfaction.pp_proportion')" width="88" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.proportion ? (row.proportion * 100).toFixed(2) + '%' : '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-box flex_j_c-flex-end margin_t-16">
        <el-button @click="handleClose" class="welkin-ghost-button">{{ $t('common.pp_close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  labelVisible: Boolean,
  l2Label: {
    type: String,
    default: ''
  },
  l3List: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['update:labelVisible'])

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleClose = () => {
  emits('update:labelVisible', false)
}
</script>

<style lang="scss" scoped>
.l3-label-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
      .el-dialog__title {
        color: #1f1f1f;
      }
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
  }
}
</style>
