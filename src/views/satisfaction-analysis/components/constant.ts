import { i18n } from '~/i18n'

const now = new Date()
const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
yesterday.setHours(0, 0, 0, 0)
export const initYesterdayStartTime = yesterday.getTime()
export const initYesterdayEndTime = yesterday.getTime() + 86399999
export const getYesterdayShortcuts = () => {
  return [
    {
      text: i18n.global.t('common.pp_yesterday'),
      value: [initYesterdayStartTime, initYesterdayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: [initYesterdayStartTime - 3600 * 1000 * 24 * 6, initYesterdayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: [initYesterdayStartTime - 3600 * 1000 * 24 * 29, initYesterdayEndTime]
    }
  ]
}
export const getDisabledYesterdayDate = (time: Record<string, any>) => time.getTime() > initYesterdayEndTime

export const scoreOptions = [
  {
    label: '1星',
    value: 1
  },
  {
    label: '2星',
    value: 2
  },
  {
    label: '3星',
    value: 3
  },
  {
    label: '4星',
    value: 4
  },
  {
    label: '5星',
    value: 5
  }
]

const getTimestamp = (days: number, isEnd = false) => {
  const date = new Date()
  date.setDate(date.getDate() - days)
  date.setHours(isEnd ? 23 : 0, isEnd ? 59 : 0, isEnd ? 59 : 0, isEnd ? 999 : 0)
  return date.getTime()
}

export const threeWeeksAgoStart = getTimestamp(21)
export const twoWeeksAgoEnd = getTimestamp(15, true)

export const projectOptions = [
  {
    label: 'common.pp_pss1',
    value: 'PowerSwap'
  },
  {
    label: 'common.pp_pss2',
    value: 'PowerSwap2'
  },
  {
    label: 'common.pp_pss3',
    value: 'PUS3'
  },
  {
    label: 'common.pp_pss4',
    value: 'PUS4'
  }
]

export const pieOptionMock = {
  color: ['#01A0AC', '#00BEBE', '#39BEAE', '#68C789', '#72D896', '#55D7DB', '#8DE0F8', '#B5EEFF', '#C6EFEF', '#DCF2F3'],
  animation: false,
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: function (params: any) {
      return params.marker + '  ' + params.name + '<br />' + params.value + '<br />' + '(' + params.percent + '%)'
    },
    textStyle: {
      color: '#262626',
      align: 'center'
    }
  },
  series: [
    {
      type: 'pie',
      center: ['45%', '52%'],
      radius: ['40%', '70%'],
      label: {
        show: true,
        color: '#8C8C8C',
        formatter: (params: any) => `{a|${params.name}:} {b|${params.value}（${params.percent}%）}`,
        rich: {
          a: {
            color: '#8c8c8c',
            fontSize: 12
          },
          b: {
            color: '#262626',
            fontSize: 12,
          }
        }
      },
      labelLine: {
        length2: 0
      },
      itemStyle: {
        borderRadius: 2,
        borderColor: '#fff',
        borderWidth: 2
      },
      cursor: 'default',
      data: [] as any
    }
  ]
}

export const barOptionMock1 = {
  animation: false,
  color: ['#00BEBE'],
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    formatter: function (params: any) {
      return `<span>${params[0].name}<span>&nbsp;&nbsp;&nbsp;<span>${params[0].value}<span>`
    },
    textStyle: {
      color: '#262626'
    }
  },
  grid: {
    top: '50',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      interval: 0,
      color: '#8C8C8C',
      formatter: function (params: string) {
        var newParamsName = ''
        var paramsNameNumber = params.length
        var provideNumber = 5
        var rowNumber = Math.ceil(paramsNameNumber / provideNumber)

        if (paramsNameNumber > provideNumber) {
          for (var i = 0; i < rowNumber; i++) {
            var tempStr = ''
            var start = i * provideNumber
            var end = start + provideNumber
            if (i == rowNumber - 1) {
              tempStr = params.substring(start, paramsNameNumber)
            } else {
              tempStr = params.substring(start, end) + '\n'
            }
            newParamsName += tempStr
          }
        } else {
          newParamsName = params
        }
        return newParamsName
      }
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    data: []
  },
  yAxis: {
    type: 'value',
    // min: (value: any) => Math.floor(value.min),
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: ['#D9D9D9']
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    }
  },
  series: [
    {
      type: 'bar',
      barMaxWidth: 24,
      label: {
        show: true,
        fontSize: 12,
        color: '#8C8C8C',
        position: 'top'
      },
      cursor: 'pointer',
      data: []
    }
  ],
  dataZoom: [
    {
      type: 'inside',
      disabled: false,
      startValue: 0,
      endValue: 9
    }
  ]
} as any

export const convertToOptions = (obj: Record<string, any>) => {
  if (!obj) return []
  return Object.entries(obj).map(([key, value]) => ({
    value: key,
    label: key,
    children: Object.entries(value).map(([subKey, subValue]) => ({
      value: subKey,
      label: subKey,
      children: (subValue as string[]).map((item) => ({
        value: item,
        label: item
      }))
    }))
  }))
}
