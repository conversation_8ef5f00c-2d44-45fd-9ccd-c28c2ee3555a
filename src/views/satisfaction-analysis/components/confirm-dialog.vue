<template>
  <div class="confirm-dialog-container">
    <el-dialog v-model="confirmDialogVisible" :title="$t('common.pp_edit')" @close="handleCloseDialog" width="328px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="font-size-14 line-height-22 color-59 margin_b-4">{{ $t('satisfaction.pp_report_status') }}</div>
      <el-select v-model="editForm.report_status" :placeholder="$t('common.pp_please_select')" class="width-full">
        <el-option value="adopt" :label="$t('satisfaction.pp_adopt')" />
        <el-option value="reject" :label="$t('satisfaction.pp_unadopt')" />
      </el-select>
      <div class="flex-box flex_j_c-flex-end flex_a_i-center margin_t-16">
        <el-button @click="handleCloseDialog" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button @click="handleConfirm" class="welkin-primary-button" style="margin-left: 4px" :loading="confirmLoading">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { apiPostReportStatus } from '~/apis/satisfaction-analysis'

const props = defineProps({
  confirmDialogVisible: Boolean,
  editForm: {
    type: Object,
    default: {}
  }
})
const emits = defineEmits(['update:confirmDialogVisible', 'handleConfirm'])

const { t } = useI18n()
const confirmLoading = ref(false)

/**
 * @description: 关闭
 * @return {*}
 */
const handleCloseDialog = () => {
  emits('update:confirmDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirm = async () => {
  if (!props.editForm.report_status) ElMessage.warning(t('satisfaction.pp_warn'))
  confirmLoading.value = true
  const params = {
    comment_id: props.editForm.comment_id,
    report_status: props.editForm.report_status
  }
  try {
    const res = await apiPostReportStatus(params)
    confirmLoading.value = false
    if (res.err_code === 0) {
      ElMessage.success(t('common.pp_edit_success'))
      emits('handleConfirm')
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    confirmLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.confirm-dialog-container {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
      .el-dialog__title {
        font-size: 18px;
        font-weight: 420;
        line-height: 26px;
        color: #1f1f1f;
      }
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
  }
}
</style>