<template>
  <div class="order-list-container">
    <el-form :model="form">
      <el-form-item :label="$t('satisfaction.pp_evaluation_time')">
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getYesterdayShortcuts()" :clearable="false" :disabledDate="getDisabledYesterdayDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('satisfaction.pp_diagnosis_result')">
        <el-cascader v-model="diagnosisResult" :options="diagnosisResultOptions" :filter-method="customFilter" :props="cascaderProps" @change="handleResultChange" filterable collapse-tags collapse-tags-tooltip clearable class="brand-cascader" :placeholder="$t('common.pp_please_select')" />
      </el-form-item>
      <el-form-item :label="$t('satisfaction.pp_diagnostic_label')">
        <el-select v-model="form.diagnosis_tag" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in diagnosisTagOptions" :key="item.tag" :value="item.tag" :label="item.description" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('satisfaction.pp_device_select')">
        <el-select v-model="form.device_id" clearable filterable remote :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
          <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description">
            <span style="float: left">{{ item.description }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('satisfaction.pp_rating_level')">
        <el-select v-model="form.score" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in scoreOptions" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('station.pp_service_id')">
        <el-input v-model.trim="form.service_id" :placeholder="$t('common.pp_please_input')" clearable style="height: 32px">
          <template #prefix><SearchIcon /></template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('station.pp_order_id')">
        <el-input v-model.trim="form.order_id" :placeholder="$t('common.pp_please_input')" clearable>
          <template #prefix><SearchIcon /></template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('satisfaction.pp_is_valid')" style="grid-column: span 2">
        <div class="width-full flex-box flex_j_c-space-between flex_a_i-center">
          <div class="flex-box flex_a_i-center">
            <el-radio-group v-model="form.is_valid">
              <el-radio :label="1">{{ $t('satisfaction.pp_valid') }}</el-radio>
              <el-radio :label="2">{{ $t('satisfaction.pp_invalid') }}</el-radio>
              <el-radio :label="3">{{ $t('satisfaction.pp_all') }}</el-radio>
            </el-radio-group>
            <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading" style="margin-left: 24px">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
          </div>
          <el-button @click="handleDownload" class="welkin-secondary-button width-108 height-32" style="padding: 5px 16px">
            <DownloadIcon />
            <span class="margin_l-4">{{ $t('common.pp_download_data') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
      <el-table-column prop="comment_time" :label="$t('satisfaction.pp_evaluation_time')" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ formatTime(row.comment_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="diagnosis_tag" :label="$t('satisfaction.pp_diagnostic_label')" :min-width="flexColumnWidth(diagnosisTagList, maxDiagnosisTag, 12)" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box gap_8" v-if="row.diagnosis_tag">
            <span v-for="item in row.diagnosis_tag.slice(0, 3)" class="tag-container">{{ getDiagnosisTagName(item) }}</span>
            <el-tooltip effect="light" placement="top" v-if="row.diagnosis_tag && row.diagnosis_tag.length > 3">
              <template #content>
                <span>{{ getMultiDiagnosisTag(row.diagnosis_tag.slice(3)) }}</span>
              </template>
              <span class="tag-container">+{{ row.diagnosis_tag.length - 3 }}</span>
            </el-tooltip>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column prop="l3_label" :label="$t('satisfaction.pp_l3_label')" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.l3_label || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="l2_label" :label="$t('satisfaction.pp_l2_label')" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.l2_label || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="l1_label" :label="$t('satisfaction.pp_l1_label')" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.l1_label || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="score" :label="$t('satisfaction.pp_score')" min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box gap_8">
            <StarIcon v-for="item in row.score" v-if="row.score > 0" />
            <NonStarIcon v-for="item in 5 - row.score" v-if="row.score < 5" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="description" :label="$t('deviceManagement.pp_device_name')" min-width="220" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="vehicle_id" :label="$t('satisfaction.pp_vehicle_id')" min-width="220">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.vehicle_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="is_valid" :label="$t('satisfaction.pp_is_valid')" width="80" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box flex_a_i-center gap_4">
            <ValidIcon v-if="row.is_valid" />
            <InvalidIcon v-else />
            <span :style="{ color: row.is_valid ? '#2F9C74' : '#595959' }">{{ row.is_valid ? $t('satisfaction.pp_valid') : $t('satisfaction.pp_invalid') }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="report_status" :label="$t('satisfaction.pp_report_status')" min-width="140" fixed="right" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box flex_j_c-center flex_a_i-center" v-if="row.report_status">
            <div :class="row.report_status === 'adopt' ? 'adopt-tag' : 'reject-tag'" :style="{ cursor: hasPermission('function:satisfaction-analysis:report-status') ? 'pointer' : 'auto' }" @click="handleClickReportStatus(row)">
              <EditIcon color="#fff" v-if="hasPermission('function:satisfaction-analysis:report-status')" />
              <span>{{ row.report_status === 'adopt' ? $t('satisfaction.pp_adopt') : $t('satisfaction.pp_unadopt') }}</span>
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('satisfaction.pp_detail')" width="70" fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box flex_a_i-center">
            <DetailIcon @click="handleClickDetail(row)" class="cursor-pointer" />
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="common-pagination">
      <Page :page="pages" @change="handlePageChange" />
    </div>

    <!-- 切换是否在报告内采用 -->
    <ConfirmDialog v-model:confirmDialogVisible="confirmDialogVisible" :editForm="editForm" @handleConfirm="handleConfirm" />
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef, h, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { useRoute, useRouter } from 'vue-router'
import { hasPermission } from '~/auth'
import { formatTime, removeNullKeys, clearJson, flexColumnWidth, customFilter } from '~/utils'
import { apiGetDevices } from '~/apis/home'
import { apiGetOrderList, apiGetDiagnosisTag, apiDownloadOrderList } from '~/apis/satisfaction-analysis'
import { initYesterdayStartTime, initYesterdayEndTime, getYesterdayShortcuts, getDisabledYesterdayDate, scoreOptions } from './constant'
import { ElMessage } from 'element-plus'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import DownloadIcon from '~/assets/svg/download-icon.vue'
import EditIcon from '~/assets/svg/edit.vue'
import StarIcon from './icon/star-icon.vue'
import NonStarIcon from './icon/non-star.vue'
import ValidIcon from './icon/valid-icon.vue'
import InvalidIcon from './icon/invalid-icon.vue'
import DetailIcon from './icon/detail-icon.vue'
import { debounce, cloneDeep } from 'lodash-es'
import ConfirmDialog from './confirm-dialog.vue'

const props = defineProps({
  diagnosisResultOptions: Array
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const maxDiagnosisTag = ref(0)
const loading = ref(false)
const remoteLoading = ref(false)
const confirmDialogVisible = ref(false)
const list = ref([] as any)
const deviceOptions = ref([] as any)
const diagnosisTagList = ref([] as any)
const diagnosisTagOptions = ref([] as any)
const diagnosisResult = ref([] as any)
const datePicker = ref([initYesterdayStartTime, initYesterdayEndTime] as any)
const form = ref({
  start_time: initYesterdayStartTime,
  end_time: initYesterdayEndTime,
  diagnosis_tag: [],
  labels: [],
  device_id: '',
  score: [1, 2, 3],
  service_id: '',
  order_id: '',
  is_valid: 1
})
const editForm = ref({} as any)
const searchForm = ref({} as any)
const pages = ref(cloneDeep(page))
const cascaderProps = ref({ multiple: true })

const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})

const handleResultChange = () => {
  form.value.labels = diagnosisResult.value.map((item: any) => item.join('-'))
}

const getDiagnosisTagName = (tag: number) => {
  const matchedTag = diagnosisTagOptions.value.find((item: any) => item.tag == tag)
  return matchedTag ? matchedTag.description : '-'
}

const getMultiDiagnosisTag = (tagArr: any) => {
  const validTag = tagArr.map((item: any) => {
    const matchedTag = diagnosisTagOptions.value.find((k: any) => k.tag == item)
    return matchedTag ? matchedTag.description : '-'
  })
  return validTag.join('、')
}

/**
 * @description: 下载
 * @return {*}
 */
const handleDownload = () => {
  if (searchForm.value.end_time - searchForm.value.start_time > 7 * 24 * 60 * 60 * 1000) {
    ElMessage.error(t('satisfaction.pp_time_error'))
    return
  }
  let formData = cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.diagnosis_tag = formData.diagnosis_tag.join(',')
  formData.score = formData.score.join(',')
  formData.labels = formData.labels.join(',')
  if (formData.is_valid == 3) {
    delete formData.is_valid
  } else {
    formData.is_valid = formData.is_valid == 1
  }
  apiDownloadOrderList(formData)
}

/**
 * @description: 点击是否在报告内采用
 * @param {*} row
 * @return {*}
 */
const handleClickReportStatus = (row: any) => {
  if (!hasPermission('function:satisfaction-analysis:report-status')) return
  editForm.value = cloneDeep(row)
  confirmDialogVisible.value = true
}

/**
 * @description: 确认编辑是否在报告内采用
 * @return {*}
 */
const handleConfirm = () => {
  confirmDialogVisible.value = false
  getList()
}

/**
 * @description: 跳转至详情
 * @param {*} row
 * @return {*}
 */
const handleClickDetail = (row: any) => {
  sessionStorage.setItem('satisfaction-analysis', JSON.stringify(route.query))
  router.push({
    path: `/fault-diagnosis/satisfaction-analysis/satisfaction-detail`,
    query: { project: row.project, order: row.order_id, service: row.service_id }
  })
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: 'PowerSwap,PowerSwap2,PUS3,PUS4', name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 获取订单列表
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.diagnosis_tag = formData.diagnosis_tag.join(',')
  formData.score = formData.score.join(',')
  formData.labels = formData.labels.join(',')
  if (formData.is_valid == 3) {
    delete formData.is_valid
  } else {
    formData.is_valid = formData.is_valid == 1
  }
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys({ ...formData, is_valid: searchForm.value.is_valid, score: formData.score || 'all', tab: 'order' }) }
    })
  }
  loading.value = true
  try {
    const res = await apiGetOrderList(removeNullKeys(formData))
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
      if (!list.value) return
      const filterList = list.value.filter((item: any) => item.diagnosis_tag)
      if (filterList.length == 0) {
        maxDiagnosisTag.value = 120
        diagnosisTagList.value = []
      } else {
        const maxLength = Math.max(...filterList.map((item: any) => item.diagnosis_tag.length))
        maxDiagnosisTag.value = maxLength > 3 ? 120 : (3 * maxLength - 1) * 8 + 20
        diagnosisTagList.value = filterList.map((item: any) =>
          item.diagnosis_tag
            .slice(0, 3)
            .map((tag: any) => getDiagnosisTagName(tag))
            .join('')
        )
      }
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  diagnosisResult.value = []
  form.value.is_valid = 1
  form.value.score = [1, 2, 3]
  datePicker.value = [initYesterdayStartTime, initYesterdayEndTime]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = cloneDeep(form.value)
  getList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 获取诊断标签列表
 * @return {*}
 */
const getDiagnosisTag = async () => {
  const res = await apiGetDiagnosisTag()
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    diagnosisTagOptions.value = res.data || []
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (initParams.tab == 'order') {
    if (!!initParams.score) {
      form.value.score = initParams.score == 'all' ? [] : initParams.score.split(',').map(Number)
    } else {
      form.value.score = [1, 2, 3]
    }
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10
    form.value.diagnosis_tag = !!initParams.diagnosis_tag ? initParams.diagnosis_tag.split(',') : []
    form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
    form.value.service_id = !!initParams.service_id ? initParams.service_id : ''
    form.value.order_id = !!initParams.order_id ? initParams.order_id : ''
    form.value.is_valid = !!initParams.is_valid ? Number(initParams.is_valid) : 1
    diagnosisResult.value = !!initParams.labels ? initParams.labels.split(',').map((item: any) => item.split('-').pop()) : []
    form.value.labels = !!initParams.labels ? initParams.labels.split(',') : []
  }
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  }
  searchDeviceList(form.value.device_id || 'NIO')
  searchForm.value = cloneDeep(form.value)
  getDiagnosisTag()
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.order-list-container {
  .adopt-tag,
  .reject-tag {
    border-radius: 14px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 2px 10px;
    font-size: 13px;
    line-height: 20px;
    color: #fff;
  }
  .adopt-tag {
    background-color: #67c23a;
  }
  .reject-tag {
    background-color: #bfbfbf;
  }
}
</style>
