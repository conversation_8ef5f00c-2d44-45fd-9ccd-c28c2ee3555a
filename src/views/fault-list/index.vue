<template>
  <div>
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <WelkinStationBreadcrumb :version="project.version" />
          <el-breadcrumb-item>
            {{ $t('menu.pp_fault_list') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">
          {{ $t('menu.pp_fault_list') }}
        </div>
      </div>
    </div>

    <div class="swap-page-container">
      <el-row>
        <el-form :model="searchForm" label-width="110px" style="width: 100%">
          <div class="collapse-search-container">
            <el-form-item :label="`${$t('deviceManagement.pp_device')}`">
              <CommonDeviceSelect v-model="searchForm.device_id" />
            </el-form-item>

            <el-form-item :label="`${$t('common.pp_time_frame')}`">
              <el-date-picker
                v-model="datePicker"
                type="datetimerange"
                @change="handleDateChange"
                unlink-panels
                :range-separator="`${$t('common.pp_to')}`"
                :start-placeholder="`${
                  $t('common.pp_please_select') + $t('common.pp_start_time')
                }`"
                :end-placeholder="`${
                  $t('common.pp_please_select') + $t('common.pp_end_time')
                }`"
                :shortcuts="shortcuts"
                :clearable="false"
                :disabledDate="getDisabledDate"
              />
            </el-form-item>
            <div></div>

            <el-form-item :label="`${$t('faultDiagnosis.pp_fault_type')}`">
              <el-select
                v-model="searchForm.type"
                clearable
                filterable
                :placeholder="`${
                  $t('common.pp_please_select') +
                  $t('faultDiagnosis.pp_fault_type')
                }`"
              >
                <el-option
                  v-for="item in faultTypeArr"
                  :key="item.value"
                  :label="$t('menu.pp_' + item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="`${$t('faultDiagnosis.pp_fault_module')}`">
              <el-select
                v-model="searchForm.module"
                clearable
                filterable
                :placeholder="`${
                  $t('common.pp_please_select') +
                  $t('faultDiagnosis.pp_fault_module')
                }`"
              >
                <el-option
                  v-for="item in faultModuleArr"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>

            <div class="flex-box flex_a_i-center">
              <el-button
                @click="filterEvent"
                class="welkin-primary-button"
                :loading="loading"
              >
                {{ $t('common.pp_search') }}
              </el-button>
              <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">
                {{ $t('common.pp_reset') }}
              </el-button>
            </div>
          </div>
        </el-form>
      </el-row>

      <el-row class="swap-table-container" v-if="tableList.length > 0">
        <el-table
          :data="tableList"
          v-loading="loading"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto',
          }"
        >
          <el-table-column
            prop="timestamp"
            :label="`${$t('common.pp_start_time')}`"
          >
            <template #default="scope">
              {{ formatLocaleDate(scope.row.timestamp * 1000, false) }}
            </template>
          </el-table-column>

          <el-table-column
            prop="device_id"
            :label="`${$t('deviceManagement.pp_device_id')}`"
            width="240"
          >
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>

          <el-table-column
            prop="module"
            :label="`${$t('faultDiagnosis.pp_fault_module')}`"
            show-overflow-tooltip
          />

          <el-table-column
            prop="type"
            :label="`${$t('faultDiagnosis.pp_fault_type')}`"
          >
            <template #default="scope">
              {{
                faultTypeMap[scope.row.type]
                  ? $t('menu.pp_' + faultTypeMap[scope.row.type])
                  : null
              }}
            </template>
          </el-table-column>

          <el-table-column
            prop="can_cause_shutdown"
            :label="`${$t('faultDiagnosis.pp_can_cause_shutdown')}`"
          >
            <template #default="scope">
              <span class="user-role-tag">
                <el-tag
                  :color="scope.row.can_cause_shutdown ? '#be2900' : ''"
                  effect="dark"
                  disable-transitions
                  round
                >
                  {{ scope.row.can_cause_shutdown ? 'Yes' : 'No' }}
                </el-tag>
              </span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            :label="`${$t('common.pp_operation')}`"
            width="165"
            class-name="operation-column"
          >
            <template #default="scope">
              <div @click="viewDetail(scope.$index, scope.row)" class="cursor-pointer">
                <el-popover
                  width="auto"
                  placement="bottom"
                  trigger="hover"
                  effect="dark"
                  :content="`${$t('menu.pp_snapshot_diagnosis')}`"
                  popper-class="message-popover"
                >
                  <template #reference>
                    <ServiceIcon />
                  </template>
                </el-popover>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            v-model:currentPage="searchForm.page"
            v-model:page-size="searchForm.size"
            :page-sizes="pagination.pageSizes"
            :layout="pagination.layout"
            :total="totalNumber"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-row>
      <el-row class="empty-box" v-if="tableList.length == 0">
        <el-empty
          :description="
            loading === true
              ? `${$t('common.pp_loading')}`
              : `${$t('common.pp_empty')}`
          "
        />
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  watch,
  computed,
  onBeforeMount,
  onBeforeUnmount,
} from 'vue';
import { Icon } from '@iconify/vue/dist/iconify';
import {
  formatLocaleDate,
  getOptions,
  getShortcuts,
  getDisabledDate,
  removeNullProp,
} from '~/utils';
import { iconMap } from '~/auth';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { apiGetFaultList } from '~/apis/fault-diagnosis';
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import { pagination } from '~/constvars';
import { faultTypeMap, faultModuleArr } from '~/constvars/fault-diagnosis';
import ServiceIcon from '~/assets/svg/service-icon.vue'

const { t } = useI18n();
const $store = useStore();
const $route = useRoute();
const $router = useRouter();
const project = ref(computed(() => $store.state.project));

const faultTypeArr = getOptions(faultTypeMap);

const loading = ref(false);
const tableList = ref([] as any);
const totalNumber = ref(0);

const datePicker = ref([
  new Date(new Date().toLocaleDateString()).getTime(),
  new Date(),
] as any);
const searchForm = reactive({
  device_id: '',
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  module: '',
  type: '',
  page: 1,
  size: 10,
  descending: true,
});
const shortcuts = ref(getShortcuts());

// 重置
const resetSelect = () => {
  searchForm.device_id = '';
  datePicker.value = [
    new Date(new Date().toLocaleDateString()).getTime(),
    new Date().getTime(),
  ];
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime();
  searchForm.end_time = new Date().getTime();
  searchForm.module = '';
  searchForm.type = '';
};

// 查看故障诊断详情
const viewDetail = (index: any, row: any) => {
  sessionStorage.setItem('fault-list', JSON.stringify(searchForm));

  $router.push({
    path: `/${project.value.route}/fault-list/snapshot-diagnosis/${row.device_id}/${row.request_id}`,
    // query: {
    //   type: 1,
    //   module: row.module,
    // },
  });
};

// 公用搜索事件
const publicSearch = (updateRoute = true) => {
  updateRoute &&
    $router.push({
      path: $router.currentRoute.value.path,
      query: { ...removeNullProp(searchForm) },
    });

  // (searchForm, project.value.project)
  return apiGetFaultList(project.value.project, searchForm).then((res) => {
    loading.value = false;
    if (res.data !== null) {
      tableList.value = res.data;
      totalNumber.value = res.total;
    } else {
      tableList.value = [];
    }
  });
};

const handleDateChange = (value: any) => {
  if (value) {
    searchForm.start_time = value[0].valueOf();
    searchForm.end_time = value[1].valueOf();
  } else {
    searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime();
    searchForm.end_time = new Date().getTime();
  }
};

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true;
  searchForm.size = val;
  searchForm.page = 1;
  publicSearch();
};

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true;
  searchForm.page = val;
  publicSearch();
};

// 点击筛选按钮
const filterEvent = () => {
  loading.value = true;
  searchForm.size = 10;
  searchForm.page = 1;
  publicSearch();
};

const initPage = () => {
  loading.value = true;
  let init_params: any = $route.query;
  if (init_params.start_time && init_params.end_time) {
    searchForm.start_time = init_params.start_time;
    searchForm.end_time = init_params.end_time;
    datePicker.value[0] = Number(searchForm.start_time);
    datePicker.value[1] = Number(searchForm.end_time);
  }
  if (!!init_params.page && !!init_params.size) {
    searchForm.page = Number(init_params.page);
    searchForm.size = Number(init_params.size);
  }
  searchForm.device_id = init_params.device_id;
  searchForm.module = init_params.module;
  searchForm.type = init_params.type;

  publicSearch(false);
};

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    console.log('故障列表 watch', newPath, oldPath);
    if (!oldPath) {
      console.log('url进入');
      initPage();
    } else if (
      newPath.split('/').length == 3 &&
      newPath.split('/')[2] == oldPath.split('/')[2]
    ) {
      console.log('切换版本');
      resetSelect();
      publicSearch(false);
    }
  },
  { immediate: true }
);

onBeforeUnmount(() => {
  stopWatch();
});
</script>

<style lang="scss" scoped>
.collapse-search-container {
  grid-template-columns: 40% 40% 15%;
}
</style>
