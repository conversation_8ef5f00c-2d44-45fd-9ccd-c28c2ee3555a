export const tableData = [
  {
    device_id: 'PS-NIO-a07ecb45-bc8613ef',
    request_id: '11678428226',
    timestamp: 1678428226, // 10位时间戳，秒
    module: 'SCT1',
    type: 1,
    data_ids_list: [
      { data_id: '760011', timestamp: 1635721467090, can_cause_shutdown: true },
      {
        data_id: '760012',
        timestamp: 1635721467090,
        can_cause_shutdown: false,
      },
      { data_id: '760013', timestamp: 1635721467090, can_cause_shutdown: true },
      {
        data_id: '760014',
        timestamp: 1635721467090,
        can_cause_shutdown: false,
      },
    ],
  },
  {
    device_id: 'PS-NIO-a07ecb45-bc8613ef',
    request_id: '11678428238',
    timestamp: 1678428238, // 10位时间戳，秒
    module: 'SCT2',
    type: 2,
    data_ids_list: [
      { data_id: '760011', can_cause_shutdown: true },
      { data_id: '760012', can_cause_shutdown: false },
      { data_id: '760013', can_cause_shutdown: true },
      { data_id: '760014', can_cause_shutdown: false },
    ],
  },
];

export const descData = {
  data_ids: [
    {
      data_id: '760011',
      can_cause_shutdown: true,
    },
    {
      data_id: '760012',
      can_cause_shutdown: true,
    },
    {
      data_id: '760021',
      can_cause_shutdown: true,
    },
    {
      data_id: '760782',
      can_cause_shutdown: false,
    },
    {
      data_id: '762321',
      can_cause_shutdown: true,
    },
    {
      data_id: '762312',
      can_cause_shutdown: true,
    },
    {
      data_id: '744021',
      can_cause_shutdown: true,
    },
    {
      data_id: '744022',
      can_cause_shutdown: false,
    },
    {
      data_id: '756111',
      can_cause_shutdown: true,
    },
    {
      data_id: '774312',
      can_cause_shutdown: false,
    },
    {
      data_id: '860882',
      can_cause_shutdown: true,
    },
    {
      data_id: '862321',
      can_cause_shutdown: true,
    },
    {
      data_id: '862312',
      can_cause_shutdown: false,
    },
    {
      data_id: '844021',
      can_cause_shutdown: false,
    },
    {
      data_id: '844022',
      can_cause_shutdown: false,
    },
    {
      data_id: '856111',
      can_cause_shutdown: false,
    },
    {
      data_id: '884312',
      can_cause_shutdown: true,
    },
  ],
  request_id: '11678428226',
  device_id: 'PS-NIO-a07ecb45-bc8613ef',
  upload_ts: 1635721467088, //,设备端上报的时间，13位时间戳,
  type: 1,
  mode: 1,
  charging_elapsed: 10,
  integral_electricity: 10000.0,
  meter_electricity: 10000.0,
  limit_power: 60,
  status_of_charge: 80,
  status_of_pcu: true,
  communication_count_of_pcu: 15,
  raw_request_voltage_of_bms: 200.0,
  raw_request_current_of_bms: 100.0,
  limited_request_voltage: 200.0,
  limited_request_current: 100.0,
  input_pcu_voltage_of_SCT: 200.0,
  input_pcu_current_of_SCT: 100.0,
  output_pcu_voltage_of_SCT: 200.0,
  output_pcu_current_of_SCT: 100.0,
  measure_voltage_of_insulation_module: 200.0,
  measure_current_of_insulation_module: 100.0,
  temperature_of_gun_head_plus: 25,
  temperature_of_gun_head_minus: 25,
  inlet_temperature: 25,
  outlet_temperature: 25,
  temperature_of_gun_tail_plus: 25,
  temperature_of_gun_tail_minus: 25,
  temperature_of_bronze_medal_plus: 26,
  temperature_of_bronze_medal_minus: 26,
  air_inlet_temperature: 26,
  ambient_temperature: 26,
  coolant_pressure: 1000.0,
  cooling_fan_duty_cycle: 100.0,
  rotate_speed_of_fan1: 1200,
  rotate_speed_of_fan2: 1200,
  pump_duty_cycle: 100.0,
  rotate_speed_of_pump: 1200,
};
