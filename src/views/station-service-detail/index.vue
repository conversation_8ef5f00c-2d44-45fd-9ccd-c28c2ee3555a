<template>
  <div class="service-detail-container">
    <div class="header-container">
      <div class="flex-box flex_a_i-center font-size-20 line-height-28">
        <BackIcon @click="changeToPre" class="cursor-pointer" />
        <span style="color: #8c8c8c; font-weight: 480; cursor: pointer; margin-left: 4px" @click="changeToDevice">{{ $t('menu.pp_device_management') }}</span>
        <span class="margin-n-4 color-8c">/</span>
        <span style="color: #8c8c8c; font-weight: 480; cursor: pointer" @click="changeToPre">{{ $t('menu.pp_device_detail') }}</span>
        <span class="margin-n-4 color-8c">/</span>
        <span style="color: #1f1f1f; font-weight: 420">{{ detailInfo.description + ' ' + detailInfo.device_id }}</span>
        <div class="tag-container" :style="{ background: projectColorMap[project.project].background, color: projectColorMap[project.project].color }">
          <pss1Icon v-if="project.version == 1" />
          <pss2Icon v-else-if="project.version == 2" />
          <pss3Icon v-else-if="project.version == 3" />
          <pss4Icon v-else-if="project.version == 4" />
          <firefly1Icon v-else />
          <span>{{ $t(projectColorMap[project.project].name) }}</span>
        </div>
      </div>
    </div>
    <ServiceDetail :project="project" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ServiceDetail } from '../components'
import { useStore } from 'vuex'
import { projectColorMap } from '../single-station/constant'
import BackIcon from '~/views/single-station/icon/back-icon.vue'
import pss1Icon from '~/views/single-station/icon/pss1-icon.vue'
import pss2Icon from '~/views/single-station/icon/pss2-icon.vue'
import pss3Icon from '~/views/single-station/icon/pss3-icon.vue'
import pss4Icon from '~/views/single-station/icon/pss4-icon.vue'
import firefly1Icon from '~/views/single-station/icon/firefly1-icon.vue'

// 监听路由
const $route = useRoute()
const $store = useStore()
const $router = useRouter()
const project = ref(computed(() => $store.state.project))
const detailInfo = ref(computed(() => $store.state.service_detail))

// 回到上级页面
const changeToPre = () => {
  let query: any = sessionStorage.getItem('station-service-list')
  $router.push({
    path: `/${project.value.route}/device-management/single-station/${$route.params.deviceId}`,
    query: JSON.parse(query)
  })
}

// 回到设备管理页面
const changeToDevice = () => {
  let query: any = sessionStorage.getItem('device-management')
  $router.push({
    path: `/${project.value.route}/device-management`,
    query: JSON.parse(query)
  })
}
</script>

<style lang="scss" scoped>
.service-detail-container {
  font-family: 'Blue Sky Standard';
  background: linear-gradient(180deg, #eef6f8 -6.51%, #f4f5f8 12.89%);
  .header-container {
    padding: 24px 24px 20px;
    .tag-container {
      height: 26px;
      display: flex;
      align-items: center;
      gap: 6px;
      border-radius: 2px;
      padding: 2px 8px;
      font-size: 14px;
      line-height: 22px;
      margin-left: 8px;
    }
  }
}
</style>
