<template>
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.91663 16.1305V17.0825H17.0833V16.1305C17.0833 15.4401 17.6429 14.8805 18.3333 14.8805V17.9167C18.3333 18.1468 18.1467 18.3333 17.9166 18.3333H2.08329C1.85317 18.3333 1.66663 18.1468 1.66663 17.9167V14.8805C2.35698 14.8805 2.91663 15.4401 2.91663 16.1305ZM10.6258 2.91666V12.9658L13.3151 10.6125C13.8347 10.1578 14.6244 10.2104 15.0791 10.7299L9.99996 15.1775L4.60538 10.7436C5.04213 10.209 5.8296 10.1296 6.36424 10.5664L9.37579 13.0267V1.66666C10.0661 1.66666 10.6258 2.2263 10.6258 2.91666Z" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
defineProps({
  color: {
    type: String,
    default: '#01A0AC'
  }
})
</script>