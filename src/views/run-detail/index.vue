<template>
  <div class="task-detail-container">
    <div class="flex-box padding_b-20 flex_a_i-center">
      <span class="padding_r-8 cursor-pointer" @click="goBack"><BackArrow></BackArrow></span>
      <span class="title">任务详情</span>
    </div>
    <div class="search-container">
      <div class="flex-box flex_a_i-center">
        <span style="color: #595959; line-height: 22px" class="font-size-14 margin_r-8">{{ $t('deviceSimulation.pp_config_id') }}</span>
        <el-select v-model="searchForm.config_id" @change="searchList" clearable filterable class="width-240" :placeholder="$t('common.pp_please_select')">
          <el-option v-for="item in configOptions" :key="item" :value="item" :label="item" />
        </el-select>
      </div>
      <div class="search-button">
        <el-button @click="deviceDownload" class="welkin-secondary-button" :disabled="deviceFlag">
          <DownloadIcon :color="deviceFlag ? 'rgba(1, 160, 172, 0.5)' : '#01A0AC'" />
          <span class="margin_l-6">{{ $t('deviceSimulation.pp_device_download') }}</span>
        </el-button>
        <el-button @click="batteryDownload" class="welkin-secondary-button" :disabled="batteryFlag">
          <DownloadIcon :color="batteryFlag ? 'rgba(1, 160, 172, 0.5)' : '#01A0AC'" />
          <span class="margin_l-6">{{ $t('deviceSimulation.pp_battery_download') }}</span>
        </el-button>
        <el-button @click="orderDownload" class="welkin-secondary-button" :disabled="orderFlag">
          <DownloadIcon :color="orderFlag ? 'rgba(1, 160, 172, 0.5)' : '#01A0AC'" />
          <span class="margin_l-6">{{ $t('deviceSimulation.pp_order_download') }}</span>
        </el-button>
      </div>
    </div>
    <div class="chart-list">
      <el-row :gutter="20" class="margin_b-20">
        <el-col :span="8">
          <el-card class="list-card relative">
            <el-skeleton :rows="8" animated v-show="chartLoading" />
            <div style="left: 16px; top: 48px" class="absolute" v-show="!chartLoading">单位：分钟</div>
            <div class="absolute" v-show="!chartLoading" style="right: 186px; top: 46px">
              <svg width="12" height="2" viewBox="0 0 12 2" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 1H12" stroke="#00BEBE" stroke-width="2" stroke-dasharray="2 2" />
              </svg>
            </div>
            <div id="timeLineChart" v-show="!chartLoading" class="width-full height-260"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="list-card relative">
            <el-skeleton :rows="8" animated v-show="chartLoading" />
            <div style="left: 16px; top: 48px" class="absolute" v-show="!chartLoading">单位：元</div>
            <div class="absolute" v-show="!chartLoading" style="right: 176px; top: 46px">
              <svg width="12" height="2" viewBox="0 0 12 2" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 1H12" stroke="#00BEBE" stroke-width="2" stroke-dasharray="2 2" />
              </svg>
            </div>
            <div id="timeLineChart2" v-show="!chartLoading" class="width-full height-260"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="list-card relative">
            <el-skeleton :rows="8" animated v-show="chartLoading" />
            <div style="left: 16px; top: 48px" class="absolute" v-show="!chartLoading">单位：百分比</div>
            <div class="absolute" v-show="!chartLoading" style="right: 186px; top: 46px">
              <svg width="12" height="2" viewBox="0 0 12 2" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 1H12" stroke="#00BEBE" stroke-width="2" stroke-dasharray="2 2" />
              </svg>
            </div>
            <div id="timeLineChart3" v-show="!chartLoading" class="width-full height-260"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <el-table
        :data="tableList"
        @sort-change="deviceSort"
        :header-cell-style="{
          background: '#E5F9F9',
          fontSize: '14px',
          color: '#262626'
        }"
      >
        <el-table-column label="仿真报告" fixed width="80">
          <template #default="{ row }">
            <div class="flex-box flex_j_c-center flex_a_i-center"><DocumentIcon @click="handleJumpReport(row)" class="cursor-pointer" /></div>
          </template>
        </el-table-column>
        <el-table-column prop="simulation_id" :label="$t('deviceSimulation.pp_simulate_id')" fixed :min-width="250">
          <template #default="{ row }">
            <span @click="handleJumpDetail(row)" style="color: #01a0ac; cursor: pointer">{{ row.simulation_id }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="基于换电站" min-width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.is_real_device">{{ row.description ? row.description : $t('common.pp_unnamed_device') }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="avg_swapping_queue_time" :label="`${$t('deviceSimulation.pp_queue_time')}`" :min-width="120" sortable="custom">
          <template #default="scope">
            {{ getDateString(scope.row.avg_swapping_queue_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="avg_battery_charging_time" :label="`${$t('deviceSimulation.pp_charging_time')}`" :min-width="120" sortable="custom">
          <template #default="scope">
            {{ getDateString(scope.row.avg_battery_charging_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="battery_electricity_cost" :label="`电费（元）`" :min-width="120" sortable="custom">
          <template #default="scope">
            {{ scope.row.battery_electricity_cost.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="avg_capacity_utilization" :label="`${$t('deviceSimulation.pp_progress_rate')}`" :min-width="140" sortable="custom">
          <template #default="scope"> {{ (scope.row.avg_capacity_utilization * 100).toFixed(2) }}% </template>
        </el-table-column>
        <el-table-column prop="status" :label="`${$t('deviceSimulation.pp_status')}`" min-width="100">
          <template #default="{ row }">
            <div
              class="status-container"
              :style="{
                background: detailStatusMap[row.status].color,
                color: '#fff'
              }"
            >
              <span>{{ $t(`deviceSimulation.${detailStatusMap[row.status]?.label}`) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="service_count" :label="`${$t('deviceSimulation.pp_service_count')}`" min-width="100" />
        <el-table-column prop="battery_50_kw_count" label="50kWh" min-width="100">
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>50kWh</span>
              <el-tooltip effect="light" :content="$t('deviceSimulation.pp_50_battery')" placement="bottom">
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="battery_60_kw_count" label="60kWh" min-width="100" v-if="route.query.project !== 'PowerSwap2'">
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>60kWh</span>
              <el-tooltip effect="light" :content="$t('deviceSimulation.pp_60_battery')" placement="bottom">
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="battery_75_kw_count" label="75kWh" min-width="100">
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>75kWh</span>
              <el-tooltip effect="light" :content="$t('deviceSimulation.pp_75_battery')" placement="bottom">
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="battery_85_kw_count" label="85kWh" min-width="100" v-if="route.query.project !== 'PowerSwap2'">
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>85kWh</span>
              <el-tooltip effect="light" :content="$t('deviceSimulation.pp_85_battery')" placement="bottom">
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="battery_100_kw_count" label="100kWh" min-width="100">
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>100kWh</span>
              <el-tooltip effect="light" :content="$t('deviceSimulation.pp_100_battery')" placement="bottom">
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="battery_150_kw_count" label="150kWh" min-width="100">
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>150kWh</span>
              <el-tooltip effect="light" :content="$t('deviceSimulation.pp_150_battery')" placement="bottom">
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="config_id" :label="`${$t('deviceSimulation.pp_simulation_id')}`" :min-width="230" />
      </el-table>
    </div>
    <div class="pagination-box">
      <Page :page="pages" @change="handlePageChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, toRefs, onActivated, onBeforeUnmount, onBeforeMount, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { detailStatusMap } from './statusMap'
import { apiGetDetail, apiGetGraphData, apiGetConfigId } from '~/apis/run-list'
import { useRoute, useRouter } from 'vue-router'
import DownloadIcon from './component/icon/download-icon.vue'
import HelpIcon from './component/icon/help-icon.vue'
import BackArrow from './component/icon/back-arrow.vue'
import DocumentIcon from './component/icon/document-icon.vue'
import _ from 'lodash'
import * as echarts from 'echarts'
import { options, options2, options3 } from './chart-mock'
const pages = ref(_.cloneDeep(page))

const tableList = ref([] as any)
const route = useRoute()
const router = useRouter()
const deviceFlag = ref(true)
const batteryFlag = ref(true)
const orderFlag = ref(true)
const chartLoading = ref(false)
const getListTimeout = ref()
const propsInfor = ref(null) as any
const searchForm = reactive({
  task_id: route.query?.task_id,
  page: 1,
  size: 20,
  config_id: '',
  sort: '',
  descending: true
})
const configOptions = ref([] as any)

/**
 * @description: 跳转到仿真报告
 * @param {*} row
 * @return {*}
 */
const handleJumpReport = (row: any) => {
  router.push({
    path: '/device-simulation/run-list/run-task-detail',
    query: {
      task_id: route.query.task_id,
      project: route.query.project,
      simulation_id: row.simulation_id
    }
  })
}

/**
 * @description: 跳转到配置详情
 * @param {*} row
 * @return {*}
 */
const handleJumpDetail = (row: any) => {
  router.push({
    path: '/device-simulation/run-list/run-task-detail',
    query: {
      task_id: route.query.task_id,
      project: route.query.project,
      simulation_id: row.simulation_id,
      tab: '2'
    }
  })
}

const goBack = () => {
  router.push({
    path: '/device-simulation/run-list'
  })
}

// 分页
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  searchForm.page = argPage.current
  searchForm.size = argPage.size
  getDetailTable()
}

const getDateString = (date: any) => {
  let minute = Math.floor(date / 60)
    .toString()
    .padStart(2, '0')
  let second: any = (date % 60).toString().padStart(2, '0')
  return `${minute}:${second}`
}

const getConfigList = async () => {
  const id = searchForm.task_id
  const res = await apiGetConfigId({ task_id: id })
  const { err_code, data } = res
  if (!err_code) {
    configOptions.value = data
  }
}
const getDetailTable = async () => {
  getListTimeout.value && clearTimeout(getListTimeout.value)
  try {
    const res = await apiGetDetail(searchForm)
    tableList.value = res.data || []
    pages.value.total = res.total
    getListTimeout.value = setTimeout(() => {
      getDetailTable()
      // getChartData();
    }, 10000)
  } catch (error) {}
}

const deviceDownload = () => {
  // console.log(props.info,"props.info")
  if (!deviceFlag.value) {
    deviceFlag.value = false
    window.open(propsInfor.value?.device_result_url)
  } else {
    deviceFlag.value = true
  }
}
const batteryDownload = () => {
  if (!batteryFlag.value) {
    batteryFlag.value = false
    window.open(propsInfor.value?.battery_result_url)
  } else {
    batteryFlag.value = true
  }
}
const orderDownload = () => {
  if (!orderFlag.value) {
    orderFlag.value = false
    window.open(propsInfor.value?.service_result_url)
  } else {
    orderFlag.value = true
  }
}

const searchList = async () => {
  const res = await apiGetDetail(searchForm)
  tableList.value = res.data
  getChartData()
}

const deviceSort = (column: any) => {
  searchForm.sort = column.prop
  searchForm.descending = column.order === 'descending' ? true : false
  getDetailTable()
}

onBeforeMount(() => {
  pages.value.size = 20
  getDetailTable()
  getConfigList()
})
onMounted(() => {
  getChartData()
})
//获取图表数据
const getChartData = async () => {
  const id = route.query?.task_id
  const params = {
    config_id: searchForm.config_id
  }
  const res = await apiGetGraphData(id, params)
  const { err_code, data } = res
  if (!err_code) {
    const avg_queue_time_graph = data.avg_queue_time_graph
    const charge_cost_graph = data.charge_cost_graph
    const avg_capacity_utilization_graph = data.avg_capacity_utilization_graph
    const base_queue_time = data.base_queue_time
    const base_capacity_utilization = data.base_capacity_utilization
    const base_charge_cost = data.base_charge_cost
    // 平均值
    avg_queue_time_graph.x = avg_queue_time_graph.map((item: any) => item.x_value)
    avg_queue_time_graph.y = avg_queue_time_graph.map((item: any) => item.y_value.toFixed(2))
    avg_queue_time_graph.max = Math.max(...avg_queue_time_graph.y)

    charge_cost_graph.x = charge_cost_graph.map((item: any) => item.x_value)
    charge_cost_graph.y = charge_cost_graph.map((item: any) => item.y_value.toFixed(2))
    charge_cost_graph.max = Math.max(...charge_cost_graph.y)

    avg_capacity_utilization_graph.x = avg_capacity_utilization_graph.map((item: any) => item.x_value)
    avg_capacity_utilization_graph.y = avg_capacity_utilization_graph.map((item: any) => (item.y_value * 100).toFixed(0))
    avg_capacity_utilization_graph.max = Math.max(...avg_capacity_utilization_graph.y)
    if (!searchForm.config_id) {
      options.customParam.isConfigId = true
      options2.customParam.isConfigId = true
      options3.customParam.isConfigId = true

      options.customParam.x = avg_queue_time_graph.x.map((item: any) => item)
      options2.customParam.x = charge_cost_graph.x.map((item: any) => item)
      options3.customParam.x = avg_capacity_utilization_graph.x.map((item: any) => item)
      avg_queue_time_graph.x = avg_queue_time_graph.x.map((item: any) => item.substr(item.length - 3))
      charge_cost_graph.x = charge_cost_graph.x.map((item: any) => item.substr(item.length - 3))
      avg_capacity_utilization_graph.x = avg_capacity_utilization_graph.x.map((item: any) => item.substr(item.length - 3))
    } else {
      options.customParam.isConfigId = false
      options2.customParam.isConfigId = false
      options3.customParam.isConfigId = false
    }
    options.xAxis.data = avg_queue_time_graph.x
    options.yAxis.max = (avg_queue_time_graph.max * 1.05).toFixed(2)
    const timeEnd = (Math.min(5, avg_queue_time_graph.x.length) * 100) / avg_queue_time_graph.x.length
    options.dataZoom[0].end = timeEnd
    options.dataZoom[1].end = timeEnd
    options.series[0].markLine.data[0].yAxis = base_queue_time.toFixed(2)
    options.series[0].markArea.data[0][1].yAxis = base_queue_time.toFixed(2)
    options.series[1].data = avg_queue_time_graph.y

    options2.xAxis.data = charge_cost_graph.x
    options2.series[1].data = charge_cost_graph.y
    options2.yAxis.max = (charge_cost_graph.max * 1.05).toFixed(2)
    const costEnd = (Math.min(5, charge_cost_graph.x.length) * 100) / charge_cost_graph.x.length
    options2.dataZoom[0].end = costEnd
    options2.dataZoom[1].end = costEnd
    options2.series[0].markLine.data[0].yAxis = base_charge_cost
    options2.series[0].markArea.data[0][1].yAxis = base_charge_cost

    options3.xAxis.data = avg_capacity_utilization_graph.x
    options3.series[1].data = avg_capacity_utilization_graph.y
    options3.yAxis.max = (avg_capacity_utilization_graph.max * 1.05).toFixed(2)
    const capacityEnd = (Math.min(5, avg_capacity_utilization_graph.x.length) * 100) / avg_capacity_utilization_graph.x.length
    options3.dataZoom[0].end = capacityEnd
    options3.dataZoom[1].end = capacityEnd
    options3.series[0].markLine.data[0].yAxis = (base_capacity_utilization * 100).toFixed(0)
    options3.series[0].markArea.data[0][1].yAxis = (base_capacity_utilization * 100).toFixed(0)
  }
  setCharts()
  propsInfor.value = data.fms_compress_info
  if (propsInfor.value?.device_compress_task_status === 'success') {
    deviceFlag.value = false
  } else {
    deviceFlag.value = true
  }
  if (propsInfor.value?.battery_compress_task_status === 'success') {
    batteryFlag.value = false
  } else {
    batteryFlag.value = true
  }
  if (propsInfor.value?.service_compress_task_status === 'success') {
    orderFlag.value = false
  } else {
    orderFlag.value = true
  }
}
let myChart: any
const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  console.log('myChart', myChart)
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  console.log('options3', options3)
  nextTick(() => {
    echartRender('timeLineChart2', options2)
    echartRender('timeLineChart3', options3)
    echartRender('timeLineChart', options)
  })
}
onBeforeUnmount(() => {
  getListTimeout.value && clearTimeout(getListTimeout.value)
})
</script>

<style lang="scss" scoped>
.task-detail-container {
  padding: 24px;
  font-family: 'Blue Sky Standard';
  background-color: rgba(244, 245, 248, 1);
  .title {
    font-size: 20px;
    line-height: 28px;
    font-weight: 500;
    color: #1f1f1f;
  }
  :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
    font-size: 20px;
    color: rgba(31, 31, 31, 1);
    font-family: 'Blue Sky Standard', 'Noto Sans CJK SC', 'Noto Sans', Avenir, Helvetica, Arial, sans-serif;
    font-weight: normal;
  }
  :deep(.pagination-box) {
    padding: 20px 24px;
    display: flex;
    background-color: #fff;
    justify-content: flex-end;
    .el-input__inner {
      color: #595959;
    }
    .el-pager li {
      background: #f0f0f0;
      color: #595959;
    }
    .el-pager li.is-active {
      background: #00bebe;
      color: #fff;
    }
    .el-pagination__jump {
      color: #595959;
    }
  }
  .search-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    :deep(.search-button) {
      margin-left: 16px;
      .el-button {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }
    }
  }
  :deep(.table-container) {
    .el-table td.el-table__cell div {
      color: #262626;
    }
    .status-container {
      width: 71px;
      height: 24px;
      padding: 2px 16px;
      border-radius: 14px;
      font-size: 13px;
      line-height: 20px;
      font-weight: bold;
    }
  }
  :deep(.el-card.is-always-shadow) {
    border-color: rgba(220, 242, 243, 1);
  }
  :deep(.list-card:hover) {
    border-color: #00bebe;
  }
  :deep(.list-card .el-card__body) {
    padding: 15px;
  }
}
</style>
