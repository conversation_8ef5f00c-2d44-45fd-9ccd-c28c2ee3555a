import { Directive , type DirectiveBinding, type VNode} from 'vue'

const elBlur = (el: HTMLElement) => {
  return () => el?.blur()
}

const blur: Directive = {
  created (el: HTMLElement, binding: DirectiveBinding, vnode: VNode) {
    if (vnode?.type === 'button') {
      el.addEventListener('click', elBlur(el))
    }
  },
  unmounted (el: HTMLElement) {
      el.removeEventListener('click', elBlur(el))
  }
};

