/*
 * @Author: minghan.ma <EMAIL>
 * @Date: 2024-06-04 17:46:36
 * @LastEditors: minghan.ma <EMAIL>
 * @LastEditTime: 2024-06-24 14:03:08
 * @FilePath: /pp-welkin-fe/src/views/run-list/statusMap.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {useI18n} from 'vue-i18n'
// const { t } = useI18n.global;

export const statusOptions = [
  {
    label: 'pp_create',
    value: 'create',
  },
  {
    label: 'pp_run',
    value: 'run',
  },
  {
    label: 'pp_finished',
    value: 'finish',
  },
  {
    label: 'pp_stopped',
    value: 'stop',
  }
];
export const statusMap = {
  create: { label: 'pp_create', color: '#FFC031' },
  run: { label: 'pp_run', color: '#5EB0FF' },
  finish: { label: 'pp_finished', color: '#67C23A' },
  stop: { label: 'pp_stopped', color: '#BFBFBF' },
};

export const detailStatusMap = {
  create: { label: 'pp_create', color: '#FFC031' },
  run: { label: 'pp_run', color: '#5EB0FF' },
  success: { label: 'pp_finished', color: '#67C23A' },
  fail: { label: 'pp_stopped', color: '#FF7575' },
} as any;
export const projectOptions = [
  // {
  //   label: 'pp_swap_station1',
  //   value: 'PowerSwap',
  // },
  {
    label: 'pp_swap_station2',
    value: 'PowerSwap2',
  },
  {
    label: 'pp_swap_station3',
    value: 'PUS3',
  },
  // {
  //   label: 'pp_swap_station4',
  //   value: 'PUS4',
  // }
];
export const projectMap = {
  PUS4: {
    name: 'menu.pp_swap_station4',
    color: '#21819D'
  },
  PUS3: {
    name: 'menu.pp_swap_station3',
    color: '#13C2C2'
  },
  PowerSwap2: {
    name: 'menu.pp_swap_station2',
    color: '#52C41A'
  },
  PowerSwap: {
    name: 'menu.pp_swap_station1',
    color: '#FFC03D'
  }
}

export const contentMap = {
  create_count: { label: 'pp_create', color: '#FFC031' },
  run_count: { label: 'pp_running', color: '#5EB0FF' },
  success_count: { label: 'pp_run_success', color: '#67C23A' },
  fail_count: { label: 'pp_run_fail', color: '#FF7575' },
}

