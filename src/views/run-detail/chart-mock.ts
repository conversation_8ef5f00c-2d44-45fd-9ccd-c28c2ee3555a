const options = {
  customParam: {
    isConfigId: false,
    x: [],
  },
  color: ['#00BEBE', '#00BEBE'],
  title: {
    text: '不同电池配比下的用户等待时长',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14,
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    formatter: (params: any) => {
      // 多系列数据时，params 是一个数组
      let result = '';
      if (options.customParam.isConfigId) {
        const index = params[0].dataIndex;
        result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${options.customParam.x[index]}</div>`;
        // 遍历所有系列的数据
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
           ${params[0].marker}<span style="padding-right:17px;">${params[0].seriesName}</span> ${params[0].value} 
          </div>`;
      } else {
        result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${params[0].name}</div>`;
        // 遍历所有系列的数据
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
         ${params[0].marker}<span style="padding-right:17px;">${params[0].seriesName}</span> ${params[0].value} 
        </div>`;
      }
      return result;
    },
  },
  grid: {
    top: '70',
    left: '0',
    right: '32',
    bottom: '5%',
    containLabel: true,
  },
  legend: {
    selectedMode: false,
    padding: 0,
    right: 0,
    top: 35,
    textStyle: {
      color: '#595959',
    },
    itemWidth: 8,
    itemHeight: 8,
    data: [
      {
        name: '基准等待时间',
        icon: 'none',
      },
      {
        name: '平均等待时长',
        icon: 'circle',
      },
    ],
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9',
      },
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C',
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9',
      },
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    name: '',
    nameGap: 20,
    min: 0,
    nameTextStyle: {
      formatter: function (value: any, ecModel: any) {
        // 根据图表模型动态生成名称
        console.log(value);
        console.log(ecModel);
      },
    },
    axisLabel: {
      alignWithLabel: true,
      show: true,
      color: '#8C8C8C',
      formatter: '{value}',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: ['#D9D9D9'],
      },
    },
  },
  dataZoom: [
    {
      type: 'slider', // 启用滑动条
      show: true, // 显示滑动条
      xAxisIndex: 0, // 应用到x轴
      start: 0, // 滚动起始位置（0表示从第一条数据开始）
      end: 60, // 滚动结束位置（10表示显示前10条数据）
      height: 6,
      bottom: '0',
      handleStyle: {
        color: 'rgba(217, 217, 217, 1)', // 滑块手柄的填充颜色
        borderColor: 'transparent', // 滑块手柄的边框颜色设置为透明
        borderWidth: 0, // 滑块手柄的边框宽度设置为0
        borderRadius: '50%',
      },
      disabled: false,
      zoomLock: true,
      borderWidth: 0,
      fillerColor: 'rgba(217, 217, 217, 1)', // 选中区域的填充颜色，半透明效果
      backgroundColor: 'rgba(245, 245, 245, 1)',
      showDataShadow: false,
      brushSelect: false,
      showDetail: false,
      borderColor: 'transparent',
    },
    {
      type: 'inside', // 启用内置滚动
      xAxisIndex: 0, // 应用到x轴
      start: 0, // 滚动起始位置（0表示从第一条数据开始）
      end: 60, // 滚动结束位置（10表示显示前10条数据）
      disableZoom: true,
      zoomLock: true,
    },
  ],
  series: [
    {
      name: '基准等待时间',
      data: [],
      type: 'line',
      tooltip: {
        valueFormatter: (value: any) => value,
      },
      markLine: {
        silent: true,
        symbol: 'none',
        data: [
          {
            yAxis: 10,
            lineStyle: {
              type: 'dashed',
              color: '#00BEBE',
              width: 1,
            },
            label: {
              position: 'insideEndTop', // 标签位置
              distance: 4,
              formatter: function (params: any) {
                return params.value; // 格式化标签内容
              },
              color: '#00BEBE',
              fontWeight: 'bolder',
            },
          },
        ],
      },
      markArea: {
        silent: true,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 190, 190, 0.1)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0)',
              },
            ],
            global: false,
          },
        },
        data: [
          [
            {
              x: 'dataMin',
              yAxis: 0,
            },
            {
              x: 'dataMax',
              yAxis: 10,
            },
          ],
        ],
      },
    },
    {
      name: '平均等待时长',
      type: 'bar',
      barWidth: 18,
      barGap: '10%',
      barCategoryGap: '20%',
      label: {
        show: false,
        position: 'top',
        color: '#00BEBE',
      },
      data: [],
    },
  ],
} as any;

const options2 = {
  customParam: {
    isConfigId: false,
    x: [],
  },
  color: ['#00BEBE', '#00BEBE'],
  title: {
    text: '不同电池配比下的充电电费',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14,
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    formatter: (params: any) => {
      // 多系列数据时，params 是一个数组
      let result = '';
      if (options2.customParam.isConfigId) {
        const index = params[0].dataIndex;
        result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${options2.customParam.x[index]}</div>`;
        // 遍历所有系列的数据
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
           ${params[0].marker}<span style="padding-right:17px;">${params[0].seriesName}</span> ${params[0].value} 
          </div>`;
      } else {
        result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${params[0].name}</div>`;
        // 遍历所有系列的数据
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
         ${params[0].marker}<span style="padding-right:17px;">${params[0].seriesName}</span> ${params[0].value} 
        </div>`;
      }
      return result;
    },
  },
  grid: {
    top: '70',
    left: '0',
    right: '32',
    bottom: '5%',
    containLabel: true,
  },
  legend: {
    selectedMode: false,
    padding: 0,
    right: 0,
    top: 35,
    textStyle: {
      color: '#595959',
    },
    itemWidth: 8,
    itemHeight: 8,
    data: [
      {
        name: '基准充电电费',
        icon: 'none',
      },
      {
        name: '总充电电费',
        icon: 'circle',
      },
    ],
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9',
      },
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C',
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9',
      },
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    name: '',
    nameGap: 20,
    min: 0,
    max: 'dataMax',
    nameTextStyle: {
      formatter: function (value: any, ecModel: any) {
        // 根据图表模型动态生成名称
        console.log(value);
        console.log(ecModel);
      },
    },
    axisLabel: {
      alignWithLabel: true,
      show: true,
      color: '#8C8C8C',
      formatter: '{value}',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: ['#D9D9D9'],
      },
    },
  },
  dataZoom: [
    {
      type: 'slider', // 启用滑动条
      show: true, // 显示滑动条
      xAxisIndex: 0, // 应用到x轴
      start: 0, // 滚动起始位置（0表示从第一条数据开始）
      end: 60, // 滚动结束位置（10表示显示前10条数据）
      height: 6,
      bottom: '0',
      handleStyle: {
        color: 'rgba(217, 217, 217, 1)', // 滑块手柄的填充颜色
        borderColor: 'transparent', // 滑块手柄的边框颜色设置为透明
        borderWidth: 0, // 滑块手柄的边框宽度设置为0
        borderRadius: '50%',
      },
      disabled: false,
      zoomLock: true,
      borderWidth: 0,
      fillerColor: 'rgba(217, 217, 217, 1)', // 选中区域的填充颜色，半透明效果
      backgroundColor: 'rgba(245, 245, 245, 1)',
      borderColor: 'transparent', // 设置 dataZoom 边框颜色为透明
      showDataShadow: false,
      brushSelect: false,
      showDetail: false,
      // borderColor: '#fff',
    },
    {
      type: 'inside', // 启用内置滚动
      xAxisIndex: 0, // 应用到x轴
      start: 0, // 滚动起始位置（0表示从第一条数据开始）
      end: 60, // 滚动结束位置（10表示显示前10条数据）
      disableZoom: true,
      zoomLock: true,
    },
  ],
  series: [
    {
      name: '基准充电电费',
      data: [],
      type: 'line',
      tooltip: {
        valueFormatter: (value: any) => value,
      },
      markLine: {
        silent: true,
        symbol: 'none',
        data: [
          {
            yAxis: 10,
            lineStyle: {
              type: 'dashed',
              color: '#00BEBE',
              width: 1,
            },
            label: {
              position: 'insideEndTop', // 标签位置
              distance: 4,
              zIndex: 100,
              showMaxLabel: true,
              formatter: function (params: any) {
                console.log('params', params);
                return params.value; // 格式化标签内容
              },
              color: '#00BEBE',
              fontWeight: 'bolder',
            },
          },
        ],
      },
      markArea: {
        silent: true,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 190, 190, 0.1)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0)',
              },
            ],
            global: false,
          },
        },
        data: [
          [
            {
              x: 'dataMin',
              yAxis: 0,
            },
            {
              x: 'dataMax',
              yAxis: 10,
            },
          ],
        ],
      },
    },
    {
      name: '总充电电费',
      type: 'bar',
      barWidth: 18,
      barGap: '10%',
      barCategoryGap: '20%',
      label: {
        show: false,
        position: 'top',
        color: '#00BEBE',
      },
      data: [],
    },
  ],
} as any;

const options3 = {
  customParam: {
    isConfigId: false,
    x: [],
  },
  color: ['#00BEBE', '#00BEBE'],
  title: {
    text: '不同电池配比下的容量利用率',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14,
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    formatter: (params: any) => {
      // 多系列数据时，params 是一个数组
      let result = '';
      if (options3.customParam.isConfigId) {
        const index = params[0].dataIndex;
        result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${options3.customParam.x[index]}</div>`;
        // 遍历所有系列的数据
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
           ${params[0].marker}<span style="padding-right:17px;">${params[0].seriesName}</span> ${params[0].value}% 
          </div>`;
      } else {
        result = `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">${params[0].name}</div>`;
        // 遍历所有系列的数据
        result += `<div style="margin-bottom:8px;color: rgba(38, 38, 38, 1);">
         ${params[0].marker}<span style="padding-right:17px;">${params[0].seriesName}</span> ${params[0].value}% 
        </div>`;
      }
      return result;
    },
  },
  grid: {
    top: '70',
    left: '0',
    right: '32',
    bottom: '5%',
    containLabel: true,
  },
  legend: {
    selectedMode: false,
    padding: 0,
    right: 0,
    top: 35,
    textStyle: {
      color: '#595959',
    },
    itemWidth: 8,
    itemHeight: 8,
    data: [
      {
        name: '基准容量利用率',
        icon: 'none',
      },
      {
        name: '容量利用率',
        icon: 'circle',
      },
    ],
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9',
      },
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C',
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9',
      },
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    name: '',
    nameGap: 20,
    min: 0,
    max: 0,
    nameTextStyle: {
      formatter: function (value: any, ecModel: any) {
        // 根据图表模型动态生成名称
        console.log(value);
        console.log(ecModel);
      },
    },
    axisLabel: {
      alignWithLabel: true,
      show: true,
      color: '#8C8C8C',
      formatter: '{value}%',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: ['#D9D9D9'],
      },
    },
  },
  dataZoom: [
    {
      type: 'slider', // 启用滑动条
      show: true, // 显示滑动条
      xAxisIndex: 0, // 应用到x轴
      start: 0, // 滚动起始位置（0表示从第一条数据开始）
      end: 60, // 滚动结束位置（10表示显示前10条数据）
      height: 6,
      bottom: '0',
      handleStyle: {
        color: 'rgba(217, 217, 217, 1)', // 滑块手柄的填充颜色
        borderColor: 'transparent', // 滑块手柄的边框颜色设置为透明
        borderWidth: 0, // 滑块手柄的边框宽度设置为0
        borderRadius: '50%',
      },
      disabled: false,
      zoomLock: true,
      borderWidth: 0,
      fillerColor: 'rgba(217, 217, 217, 1)', // 选中区域的填充颜色，半透明效果
      backgroundColor: 'rgba(245, 245, 245, 1)',
      borderColor: 'transparent', // 设置 dataZoom 边框颜色为透明
      showDataShadow: false,
      brushSelect: false,
      showDetail: false,
      // borderColor: '#fff',
    },
    {
      type: 'inside', // 启用内置滚动
      xAxisIndex: 0, // 应用到x轴
      start: 0, // 滚动起始位置（0表示从第一条数据开始）
      end: 60, // 滚动结束位置（10表示显示前10条数据）
      disableZoom: true,
      zoomLock: true,
    },
  ],
  series: [
    {
      name: '基准容量利用率',
      data: [],
      type: 'line',
      tooltip: {
        valueFormatter: (value: any) => value,
      },
      markLine: {
        silent: true,
        symbol: 'none',
        showMaxLabel: '',
        data: [
          {
            yAxis: 10,
            lineStyle: {
              type: 'dashed',
              color: '#00BEBE',
              width: 1,
            },
            label: {
              position: 'insideEndTop', // 标签位置
              distance: 4,
              formatter: function (params: any) {
                return params.value + '%'; // 格式化标签内容
              },
              color: '#00BEBE',
              fontWeight: 'bolder',
            },
          },
        ],
      },
      markArea: {
        silent: true,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 190, 190, 0.1)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0)',
              },
            ],
            global: false,
          },
        },
        data: [
          [
            {
              x: 'dataMin',
              yAxis: 0,
            },
            {
              x: 'dataMax',
              yAxis: 10,
            },
          ],
        ],
      },
    },
    {
      name: '容量利用率',
      type: 'bar',
      barWidth: 18,
      barGap: '10%',
      barCategoryGap: '20%',
      label: {
        show: false,
        position: 'top',
        color: '#00BEBE',
      },
      data: [3, 9, 20, 15, 25, 3, 9, 20, 15, 25, 3, 9, 20, 15, 25],
    },
  ],
} as any;

export { options, options2, options3 };
