<template>
  <div class="run-list-container">
    <!-- 仿真列表 -->
    <div v-if="!isShow">
      <div class="page-header">
        <div class="header-title">{{ $t('menu.pp_run_list') }}</div>
        <el-button
          @click="createSimTask()"
          class="width-132 height-32 welkin-primary-button"
          style="padding: 5px 16px"
        >
          <AddIcon />
          <span class="margin_l-4">{{
            $t('deviceSimulation.pp_create_simulation_task')
          }}</span>
        </el-button>
      </div>
      <div class="page-container">
        <div class="search-container">
          <el-form :model="searchForm" ref="ruleFormRef" inline>
            <el-form-item :label="$t('deviceSimulation.pp_tasks_name')">
              <el-input
                v-model="searchForm.task_name"
                :placeholder="`${$t('deviceSimulation.pp_enter_task')}`"
                class="width-full"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('deviceSimulation.pp_tasks_id')">
              <el-input
                v-model.trim="searchForm.task_id"
                :placeholder="`${$t('deviceSimulation.pp_enter_task')}`"
                class="width-full"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('deviceSimulation.pp_running_status')">
              <el-select
                v-model="searchForm.status"
                :placeholder="`${$t('deviceSimulation.pp_select_account')}`"
                class="width-full"
                clearable
                filterable
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="$t(`deviceSimulation.${item.label}`)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('deviceSimulation.pp_creator')">
              <el-select
                v-model="searchForm.creator"
                clearable
                filterable
                remote
                :placeholder="$t('stuckAnalysis.pp_user_placeholder')"
                class="width-full"
                :remote-method="remoteMethod"
                :loading="remoteLoading"
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.employee_id"
                  :value="item.worker_user_id"
                  :label="item.name"
                >
                  <span style="float: left">{{ item.name }}</span>
                  <span
                    style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                    >{{ item.worker_user_id }}</span
                  >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item style="margin-right: 0">
              <div class="width-full search-button">
                <el-button class="welkin-primary-button" @click="searchTableList">{{
                  $t('deviceSimulation.pp_search')
                }}</el-button>
                <el-button @click="resetTableList" class="welkin-secondary-button">{{
                  $t('common.pp_reset')
                }}</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="table-container" v-if="!isShow">
          <el-table
            :data="tableList"
            :header-cell-style="{
              background: '#E5F9F9',
              fontSize: '14px',
              color: '#262626',
            }"
          >
            <el-table-column
              prop="task_name"
              :label="`${$t('deviceSimulation.pp_tasks_name')}`"
              min-width="200"
            >
              <template #default="scope">
                <span
                  style="color: #01a0ac; cursor: pointer"
                  @click="showDetail(scope.row)"
                >
                  {{ scope.row.task_name }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="task_id"
              :label="`${$t('deviceSimulation.pp_tasks_id')}`"
              min-width="240"
            />
            <el-table-column
              prop="remark"
              :label="`${$t('deviceSimulation.pp_remarks')}`"
              :width="remarkList.length > 0 ? 180 : 90"
            >
              <template #default="{ row }">
                <span>{{ row.remark || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="project"
              :label="`${$t('deviceSimulation.pp_device_type')}`"
              min-width="140"
            >
              <template #default="scope">
                <span
                  :style="`color: ${projectMap[scope.row.project].color}`"
                  >{{ $t(projectMap[scope.row.project].name) }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="status"
              :label="`${$t('deviceSimulation.pp_status')}`"
              min-width="140"
            >
              <template #default="{ row }">
                <div
                  class="status-container"
                  :style="{
                    background: statusMap[row.status].color,
                    color: '#fff',
                  }"
                >
                  <span>{{
                    $t(`deviceSimulation.${statusMap[row.status]?.label}`)
                  }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="status_detail"
              :label="`${$t('deviceSimulation.pp_run_detail')}`"
              min-width="200"
            >
              <template #default="scope">
                <div class="flex-box flex_a_i-center gap_12">
                  <div
                    class="flex-box flex_a_i-center"
                    v-for="(item, index) in scope.row.status_detail"
                  >
                    <el-tooltip effect="dark" placement="top">
                      <template #content>
                        <span>{{
                          $t(`deviceSimulation.${contentMap[index]?.label}`)
                        }}</span
                        >&nbsp;
                        <span class="font-weight-bold">{{ item }}</span>
                      </template>
                      <div class="flex-box flex_a_i-center gap_6">
                        <div
                          class="circle"
                          :style="{ background: contentMap[index]?.color }"
                        ></div>
                        <div>{{ item }}</div>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="creator"
              :label="`${$t('deviceSimulation.pp_creator')}`"
              :width="flexColumnWidth(creatorList, 100, 7)"
            >
              <template #default="{ row }">
                <div class="flex-box flex_a_i-center">
                  <span v-if="!row.creator">-</span>
                  <div class="flex-box flex_a_i-center avatar-container" v-else>
                    <el-avatar size="small" :src="row.creator_avatar" />
                    <span class="margin_l-8">{{ row.creator }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="create_ts"
              :label="`${$t('deviceSimulation.pp_create_time')}`"
              min-width="200"
            >
              <template #default="{ row }">
                <span> {{ formatTime(row.create_ts) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="update_ts"
              :label="`${$t('deviceSimulation.pp_update_time')}`"
              min-width="200"
            >
              <template #default="{ row }">
                <span> {{ formatTime(row.update_ts) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="`${$t('common.pp_operation')}`"
              min-width="100"
              class-name="operation-column"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  @click="stopTask(scope.row.task_id)"
                  style="color: #f83535; font-family: Blue Sky Noto"
                  v-if="scope.row.status === 'run'"
                >
                  {{ $t('deviceSimulation.pp_button_stop') }}
                </el-button>
                <span v-else> - </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-box">
            <Page :page="pages" :pagerCount="5" @change="handlePageChange" />
          </div>
        </div>
      </div>
    </div>

    <!-- 新建仿真 -->
    <div v-if="isShow" class="page-detail">
      <div class="page-detail-header">
        <BackArrow @click="returnRunList" class="cursor-pointer margin_r-6" />
        <span class="detail-title">{{
          $t('deviceSimulation.pp_create_simulation_task')
        }}</span>
      </div>
      <CreateTask
        v-model:isShow="isShow"
        class="page-detail-container"
        style="padding-top: 24px"
        ref="createTaskRef"
        v-if="isShow"
        @updateList="updateList"
      >
      </CreateTask>
    </div>

    <!-- 新建仿真任务 -->
    <el-dialog
      v-model="createDialog"
      :title="$t('deviceSimulation.pp_create_simulation_task')"
      top="30vh"
      :close-on-click-modal="false"
      @close="closeCreateSimuDialog()"
      class="create-dialog"
      width="352px"
    >
      <el-form
        ref="addRuleFormRef"
        :model="createDeviceForm"
        :rules="rules"
        label-position="top"
        require-asterisk-position="right"
      >
        <el-form-item
          :label="`${$t('deviceSimulation.pp_device_type')}`"
          prop="project"
        >
          <el-select
            v-model="createDeviceForm.project"
            :placeholder="`${$t('deviceSimulation.pp_select_account')}`"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="item in projectOptions"
              :key="item.value"
              :label="$t(`deviceSimulation.${item.label}`)"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="`${$t('deviceSimulation.pp_create_simulation_step')}`"
          prop="step"
        >
          <el-input
            v-model.number="createDeviceForm.step"
            style="width: 100%"
            class="short-append"
          >
            <template #append>
              <span style="color: #262626">{{
                $t('deviceSimulation.pp_seconds')
              }}</span>
            </template>
          </el-input>
          <div class="font-size-12" style="color: #8c8c8c; line-height: 23px">
            注：请输入范围在5-120内且为5的倍数
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-box flex_j_c-flex-end">
        <el-button
          @click="closeCreateSimuDialog()"
          class="welkin-text-button"
          >{{ $t('common.pp_cancel') }}</el-button
        >
        <el-button
          @click="createSimuTask(addRuleFormRef)"
          class="welkin-primary-button"
          style="margin-left: 4px"
          >{{ $t('common.pp_confirm') }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, onBeforeUnmount } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import {
  statusOptions,
  statusMap,
  projectMap,
  contentMap,
  projectOptions,
} from './statusMap';
import CreateTask from './create-task.vue';
import { page } from '~/constvars/page';
import {
  apiGetRunList,
  apiPostStopTasks,
  apiGetUserList,
} from '~/apis/run-list';
import { removeNullProp, formatTime, flexColumnWidth } from '~/utils';
import { ElMessage } from 'element-plus';
import _ from 'lodash';
import { debounce } from 'lodash-es';
import AddIcon from './component/icon/add-icon.vue';
import BackArrow from './component/icon/back-arrow.vue';

// 监听路由
const route = useRoute();
const router = useRouter();
const store = useStore();
const getListTimeout = ref();

const { t } = useI18n();
const loading = ref(false);

const createTaskRef = ref('');
const createDialog = ref(false);
const isShow = ref(false);
const tableList = ref([] as any);
const remarkList = ref([] as any);
const userOptions = ref([] as any);
const creatorList = ref([] as any);
const pages = ref(_.cloneDeep(page));
const addRuleFormRef = ref<FormInstance>();
const remoteLoading = ref(false);

/**
 * @description: 远程搜索用户
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return;
  searchUserList(query);
};
const searchUserList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true;
    const params = { fuzzy_name: val };
    const res = await apiGetUserList(params);
    userOptions.value = res.data.people_list;
    console.log(userOptions.value);
    remoteLoading.value = false;
  }
}, 500);

const searchForm = reactive({
  page: 1,
  size: 10,
  task_name: '',
  task_id: '',
  status: '',
  creator: '',
  sort: 'create_ts',
  descending: true,
});

const createDeviceForm = reactive({
  project: '',
  step: 10,
});

const createSimTask = () => {
  createDialog.value = true;
};

// 取消新建
const closeCreateSimuDialog = () => {
  createDialog.value = false;
  createDeviceForm.project = '';
  createDeviceForm.step = 10;
  setTimeout(() => {
    addRuleFormRef.value?.clearValidate();
  }, 0);
};

// 新建仿真任务
const createSimuTask = async (formEl: any) => {
  if (!formEl) return;
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      router.push({
        path: route.path,
        query: {
          ...removeNullProp(createDeviceForm),
        },
      });
      getListTimeout.value && clearTimeout(getListTimeout.value);
      isShow.value = true;
      createDialog.value = false;
    } else {
      console.log('error submit!', fields);
    }
  });
};
const stepValidator = (rule: any, value: any, callback: any) => {
  if (value >= 5 && value % 5 == 0 && value <= 120) {
    callback();
  } else {
    return callback(new Error());
  }
};
const rules = reactive<FormRules>({
  project: [
    {
      message: t('common.pp_please_select'),
      required: true,
      trigger: 'change',
    },
  ],
  step: [
    {
      validator: (rule, value, cb) => stepValidator(rule, value, cb),
      message: t('deviceSimulation.pp_step_prompt'),
      trigger: 'change',
    },
  ],
});

// 重置表单
const resetTableList = async () => {
  searchForm.creator = getUserID();
  searchForm.status = '';
  searchForm.task_id = '';
  searchForm.task_name = '';
  getInitUser();
  getDeviceList();
};

// 搜索表单
const searchTableList = () => {
  getDeviceList();
};

// 分页
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  getDeviceList();
};

// 获取任务列表，轮训
const getDeviceList = async (updateRoute = true) => {
  if (loading.value) return;
  getListTimeout.value && clearTimeout(getListTimeout.value);
  searchForm.page = pages.value.current;
  searchForm.size = pages.value.size;
  if (updateRoute) {
    router.push({
      path: route.path,
      query: {
        ...removeNullProp(searchForm),
      },
    });
  }
  loading.value = true;
  try {
    const res = await apiGetRunList(searchForm);
    tableList.value = res.data;
    remarkList.value = res.data.filter((item: any) => item.remark);
    pages.value.total = res.total;
    creatorList.value = [...new Set(res.data.map((item: any) => item.creator))];
    getListTimeout.value = setTimeout(() => {
      getDeviceList(false);
    }, 20000);
  } catch (error) {}
  loading.value = false;
};

const updateList = () => {
  isShow.value = false;
  pages.value.current = 1;
  resetTableList();
};

// 停止仿真任务
const stopTask = async (task_id: any) => {
  const res = await apiPostStopTasks(task_id);
  if (!res.err_code) {
    getDeviceList();
    ElMessage.success(t('deviceSimulation.pp_stop_success'));
  } else {
    ElMessage.error(t('deviceSimulation.pp_stop_failed'));
  }
};

const returnRunList = () => {
  isShow.value = false;
  getDeviceList();
};

const query = reactive({
  task_id: '',
  project: ''
});

const info = ref({});
// 仿真详情
const showDetail = (row: any) => {
  query.task_id = row.task_id;
  query.project = row.project
  info.value = row.fms_compress_info;
  router.push({
    path: '/device-simulation/run-list/run-detail',
    query: {
      ...removeNullProp(query),
    },
  });
  getListTimeout.value && clearTimeout(getListTimeout.value);
};

const getUserID = () => {
  const allCookie = document.cookie.split(';');
  const cookieObjet = {} as any;
  allCookie.forEach((item) => {
    const [key, value] = item.trim().split('=');
    cookieObjet[key] = value;
  });
  // console.log(cookieObjet.user_id)
  return cookieObjet.user_id;
};

const getInitUser = async () => {
  const params = { fuzzy_name: searchForm.creator };
  const res = await apiGetUserList(params);
  userOptions.value = res.data.people_list;
};

onBeforeMount(() => {
  searchForm.creator = getUserID();
  getInitUser();
  getDeviceList();
});

onBeforeUnmount(() => {
  getListTimeout.value && clearTimeout(getListTimeout.value);
});
</script>

<style lang="scss" scoped>
.run-list-container {
  font-family: 'Blue Sky Standard';
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    padding-bottom: 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .page-container {
    background-color: #fff;
    :deep(.el-input__inner) {
      color: #262626;
    }
    .search-container {
      padding: 0px 24px 20px;
      :deep(.el-form) {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 136px;
        .el-form-item {
          margin-bottom: 0;
          margin-right: 24px;
          .el-form-item__label {
            font-size: 14px;
            color: #595959;
          }
        }
      }
      .search-button {
        .el-button + .el-button {
          margin-left: 8px;
        }
      }
    }
    .table-container {
      padding: 0 24px 0 24px;
      :deep(.el-table) {
        td.el-table__cell div {
          color: #262626;
        }
        .el-button:hover {
          background: transparent !important;
          color: rgba(248, 53, 53, 0.8) !important;
        }
        .el-button:focus {
          background: transparent !important;
          color: rgba(248, 53, 53, 0.8) !important;
        }
        .status-container {
          width: 71px;
          height: 24px;
          padding: 2px 16px;
          border-radius: 14px;
          font-size: 13px;
          line-height: 20px;
          font-weight: bold;
        }
        .avatar-container {
          border-radius: 16px;
          padding: 3px 12px 3px 4px;
          background: #e5f9f9;
        }
      }
      :deep(.pagination-box) {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        .el-pagination__total {
          color: #595959;
        }
        .el-input__inner {
          color: #595959;
        }
        .el-pager li {
          background: #f0f0f0;
          color: #595959;
        }
        .el-pager li.is-active {
          background: #00bebe;
          color: #fff;
        }
        .el-pagination__jump {
          color: #595959;
        }
      }
      .circle {
        display: inline-flex;
        align-items: center;
        font-size: 14px;
        line-height: 22px;
        border-radius: 50%;
        height: 8px;
        width: 8px;
      }
    }
  }
  .page-detail {
    height: 100%;
    display: flex;
    flex-direction: column;
    :deep(.el-input__inner) {
      color: #262626;
    }
    .page-detail-header {
      padding: 24px;
      padding-bottom: 0;
      background: linear-gradient(180deg, #f6fefe, #f8f8f8);
      display: flex;
      align-items: center;
      .detail-title {
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        color: #1f1f1f;
      }
      .header-subtitle {
        font-size: 16px;
        line-height: 24px;
        margin-left: 8px;
        color: #8c8c8c;
      }
    }
    .page-detail-container {
      flex: 1;
      padding: 24px;
      padding-top: 20px;
      background: #f8f8f8;
    }
  }
}

:deep(.create-dialog) {
  .el-dialog__header {
    padding: 24px !important;
    padding-bottom: 16px !important;
    .el-dialog__title {
      color: #1f1f1f;
    }
  }
  .el-dialog__body {
    padding: 24px !important;
    padding-top: 0px !important;
    .el-form-item {
      margin-bottom: 16px;
      .el-form-item__label {
        font-size: 14px;
        line-height: 22px;
        color: #595959;
        margin-bottom: 4px;
      }
    }
    .el-input__inner {
      color: #262626;
    }
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 20px;
    color: #262626;
  }
}
</style>
