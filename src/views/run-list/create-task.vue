<template>
  <div>
    <Steps :steps="steps" :width="'100%'" :descMaxWidth="160" :current="current" @change="onChange" />
    <!-- 新建仿真任务表单 -->
    <div class="form-container">
      <!-- 基础设置 -->
      <div v-if="current < 2">
        <el-form :model="searchForm" label-position="left" label-width="80px" style="width: 100%" ref="ruleFormRef" inline>
          <el-row>
            <el-col :span="8" style="padding-right: 32px">
              <el-form-item class="width-full" :label="$t('deviceSimulation.pp_config_name')">
                <el-input v-model="searchForm.config_name" :prefix-icon="Search" :placeholder="`${$t('deviceSimulation.pp_enter_task')}`" class="width-full" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="8" style="padding-right: 32px">
              <el-form-item class="width-full" :label="$t('deviceSimulation.pp_config_id')">
                <el-input v-model.trim="searchForm.config_id" :prefix-icon="Search" :placeholder="`${$t('deviceSimulation.pp_enter_task')}`" class="width-full" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item class="width-full">
                <div class="width-full search-button">
                  <el-button class="welkin-primary-button" @click="searchConfig()">{{ $t('deviceSimulation.pp_search') }}</el-button>
                  <el-button class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="table-container">
          <el-table
            v-loading="loading"
            ref="multipleConfigTableRef"
            :row-key="getRowKey"
            :data="tableData"
            style="width: 100%"
            :header-cell-style="{
              background: '#E5F9F9',
              fontSize: '14px',
              color: '#262626',
              cursor: 'auto'
            }"
            :cell-style="{ color: '#262626' }"
            @selection-change="handleSelectionConfigChange"
          >
            <el-table-column :reserve-selection="true" type="selection" width="40" />
            <el-table-column prop="config_name" label="配方名称" min-width="140" show-overflow-tooltip />
            <el-table-column prop="config_id" label="配方ID" min-width="160" show-overflow-tooltip />
            <el-table-column prop="remark" label="备注" min-width="80" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ row.remark || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="基于换电站" min-width="180" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.is_real_device">{{ row.description ? row.description : $t('common.pp_unnamed_device') }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column :label="`${$t('deviceSimulation.pp_create_time')}`" width="180">
              <template #default="{ row }">
                <span class="span">{{ formatTime(row.create_ts) }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="`${$t('deviceSimulation.pp_update_time')}`" width="180">
              <template #default="{ row }">
                <span class="span">{{ formatTime(row.update_ts) }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination class="mini-page" background layout="total, sizes, prev, pager, next, jumper" v-model:current-page="searchForm.page" v-model:page-size="searchForm.size" @size-change="handleSizeChange" @current-change="handleCurrentChange" :total="page_config_total" />
          </div>
          <div v-if="multipleConfigSelection && multipleConfigSelection.length > 0" style="margin-top: 6px">
            <el-tag v-for="tag in multipleConfigSelection" :key="tag.config_id" closable @close="() => closeTag(tag)">
              {{ tag.config_name }}
            </el-tag>
          </div>
        </div>
      </div>
      <!-- 寻优配置 -->
      <el-form v-else-if="current === 2" :model="configForm" style="max-width: 600px" label-position="left">
        <el-form-item :label="$t('deviceSimulation.pp_open_config')">
          <el-switch v-model="settingForm.switch" @change="handleChangeSwitch" />
          <span class="span" style="margin-left: 8px; margin-right: 80px">{{ settingForm.switch ? $t('deviceSimulation.pp_open') : $t('deviceSimulation.pp_close') }}</span>
        </el-form-item>
        <el-form-item :label="$t('deviceSimulation.pp_select_config')" v-if="settingForm.switch">
          <el-radio-group v-model="configForm.optiFunction" @change="clicktest" size="large" class="select-button">
            <el-radio-button @click.stop="btntest" label="battery" :disabled="!settingForm.switch">{{ $t('deviceSimulation.pp_battery') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('deviceSimulation.pp_battery_function')" v-if="settingForm.switch && configForm.optiFunction == 'battery'">
          <div class="radio-group">
            <el-radio-group v-model="configForm.proFunction">
              <el-radio :label="1" size="large">{{ $t('deviceSimulation.pp_custom') }}</el-radio>
              <el-radio :label="2" size="large" style="margin-right: 10px">{{ $t('deviceSimulation.pp_enumeration') }}</el-radio>
            </el-radio-group>
            <el-tooltip effect="light" placement="right-start">
              <template #content>
                <div style="padding: 11px 5px; width: 360px">
                  <div class="flex-box flex_a_i-center gap_8 margin_b-8">
                    <InfoIcon />
                    <div style="font-size: 14px; line-height: 22px; color: #1f1f1f; font-weight: bold">
                      {{ $t('deviceSimulation.pp_enum') }}
                    </div>
                  </div>
                  <div style="font-size: 14px; line-height: 22px; color: #262626">
                    {{ $t('deviceSimulation.pp_enum_detail') }}
                  </div>
                </div>
              </template>
              <HelpIcon />
            </el-tooltip>
          </div>
          <!-- 自定义配比 -->
          <div v-if="configForm.proFunction === 1">
            <el-form ref="settingFormRef" :model="configForm.defineForm" class="config-input">
              <el-form-item v-for="(item, index) in configForm.defineForm" :key="index" style="margin-bottom: 24px">
                <el-form-item :prop="`defineForm.${index}.${0}`" :rules="settingRules.inputValid">
                  <span class="span">50kWh</span>
                  <el-input v-model.number="configForm.defineForm[index][0]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :prop="`defineForm.${index}.${1}`" :rules="settingRules.inputValid" v-if="query.project == 'PowerSwap2'">
                  <span class="span">75kWh</span>
                  <el-input v-model.number="item[1]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :prop="`defineForm.${index}.${2}`" :rules="settingRules.inputValid" v-if="query.project == 'PowerSwap2'">
                  <span class="span">100kWh</span>
                  <el-input v-model.number="item[2]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :prop="`defineForm.${index}.${3}`" :rules="settingRules.inputValid" v-if="query.project == 'PowerSwap2'">
                  <span class="span">150kWh</span>
                  <el-input v-model.number="item[3]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item :prop="`defineForm.${index}.${1}`" :rules="settingRules.inputValid" v-if="query.project != 'PowerSwap2'">
                  <span class="span">60kWh</span>
                  <el-input v-model.number="item[1]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :prop="`defineForm.${index}.${2}`" :rules="settingRules.inputValid" v-if="query.project != 'PowerSwap2'">
                  <span class="span">75kWh</span>
                  <el-input v-model.number="item[2]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :prop="`defineForm.${index}.${3}`" :rules="settingRules.inputValid" v-if="query.project != 'PowerSwap2'">
                  <span class="span">85kWh</span>
                  <el-input v-model.number="item[3]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :prop="`defineForm.${index}.${4}`" :rules="settingRules.inputValid" v-if="query.project != 'PowerSwap2'">
                  <span class="span">100kWh</span>
                  <el-input v-model.number="item[4]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item :prop="`defineForm.${index}.${5}`" :rules="settingRules.inputValid" v-if="query.project != 'PowerSwap2'">
                  <span class="span">150kWh</span>
                  <el-input v-model.number="item[5]" :disabled="index == 0" class="short-append">
                    <template #append>
                      {{ $t('deviceSimulation.pp_blcok') }}
                    </template>
                  </el-input>
                </el-form-item>
                <RubbishIcon class="cursor-pointer" @click="deleteForm(index)" v-if="configForm.defineForm.length !== 1 && index != 0" />
              </el-form-item>
              <div @click="addForm" class="cursor-pointer flex-box flex_a_i-center gap_6 width-112">
                <GreenAdd />
                <div style="color: #01a0ac; line-height: 22px; margin-top: 2px">
                  {{ $t('deviceSimulation.pp_add') }}
                </div>
              </div>
            </el-form>
          </div>

          <!-- 枚举方式配比 -->
          <div v-else>
            <el-form :model="configForm.enumForm" ref="enumFormRef">
              <div class="enum">
                <div v-for="(item, index) in configForm.enumForm" class="enum-content">
                  <el-form-item class="enum-block" :rules="enumRules.maxValid" :prop="`enumForm.${index}.min`" style="margin-bottom: 24px">
                    <span @click="seeform" class="enum-span">{{ item.label }}kWh </span>
                    <el-input @blur="checkForm" style="width: 110px" v-model.number="item.min" class="mid-append">
                      <template #append>
                        {{ $t('deviceSimulation.pp_blcok') }}
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item :prop="`enumForm.${index}.max`" :rules="enumRules.maxValid" class="enum-block enum-block-max" style="margin-bottom: 24px">
                    <div class="margin-n-16">
                      {{ $t('deviceSimulation.pp_to') }}
                    </div>
                    <el-input @blur="checkForm" style="width: 110px" v-model.number="item.max" class="mid-append">
                      <template #append>
                        {{ $t('deviceSimulation.pp_blcok') }}
                      </template>
                    </el-input>
                  </el-form-item>
                </div>
              </div>
              <el-form-item>
                <Eye style="margin-right: 3px" class="svg-icon" />
                <span style="color: #01a0ac; cursor: pointer" @click="previewEnumResult(true)">{{ $t('deviceSimulation.pp_enum_result') }}</span>
              </el-form-item>
            </el-form>
          </div>
        </el-form-item>
      </el-form>
      <!-- 完成创建 -->
      <el-form class="finish-create" ref="finishRef" v-else :model="createForm" label-position="left" require-asterisk-position="right">
        <el-form-item
          :label="$t('deviceSimulation.pp_task_name')"
          prop="task_name"
          :rules="{
            required: true,
            trigger: 'blur',
            message: `${$t('deviceSimulation.pp_input')}`
          }"
        >
          <el-input v-model="createForm.task_name" style="width: 100%; margin-right: 80px" :placeholder="`${$t('common.pp_please_input')}`" clearable />
        </el-form-item>
        <el-form-item :label="$t('deviceSimulation.pp_remark')">
          <el-input v-model="createForm.reamark" style="width: 100%" :autosize="{ minRows: 4 }" type="textarea" :placeholder="`${$t('common.pp_please_input')}`" />
        </el-form-item>
      </el-form>

      <div class="container-footer">
        <div class="left-footer">
          <el-button class="welkin-secondary-button" v-if="current > 1" @click="onPrevious">
            <PreIcon />
            <div class="margin_l-4">
              {{ $t('deviceSimulation.pp_prev_step') }}
            </div>
          </el-button>
          <el-button class="welkin-secondary-button" @click="cancelDialogVisible = true">
            {{ $t('deviceSimulation.pp_cancel_create') }}
          </el-button>
        </div>
        <div class="right-footer">
          <el-button v-if="current < 3" @click="onNext" :disabled="nextDisabled" class="welkin-primary-button">
            <div class="margin_r-4">
              {{ $t('deviceSimulation.pp_next_step') }}
            </div>
            <NextIcon />
          </el-button>
          <el-button class="welkin-primary-button" v-else :loading="createLoading" @click="createTask">
            {{ $t('deviceSimulation.pp_finish_des') }}
          </el-button>
        </div>
      </div>
    </div>
    <!-- 放弃创建 -->
    <el-dialog v-model="cancelDialogVisible" :title="$t('deviceSimulation.pp_prompt')" top="30vh" :close-on-click-modal="false" @close="cancelDialogVisible = false" class="giveup-dialog" width="340px">
      <div class="flex-box flex_a_i-center gap_6">
        <WarningIcon />
        <span class="dialog_content">{{ $t('deviceSimulation.pp_prompt_content') }}</span>
      </div>
      <div class="flex-box flex_j_c-flex-end margin_t-24">
        <el-button @click="cancelDialogVisible = false" class="welkin-text-button">{{ $t('deviceSimulation.pp_cancel_giveup') }}</el-button>
        <el-button class="welkin-primary-button" style="margin-left: 4px" @click="giveUpCreate">{{ $t('deviceSimulation.pp_confirm_giveup') }}</el-button>
      </div>
    </el-dialog>
    <!-- 设备选择 -->
    <el-dialog v-model="deviceDialogVisible" :title="$t('deviceSimulation.pp_select_device')" top="30vh" :close-on-click-modal="false" @close="deviceDialogVisible = false" class="device-dialog" width="568px">
      <div class="flex-box flex_j_c-flex-end margin_t-16">
        <el-button @click="cancelSelect" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button class="welkin-primary-button" style="margin-left: 4px" @click="confirmSelectDevice">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
    <!-- 批量导入 -->
    <!-- 上传csv -->
    <el-dialog v-model="importDialogVisible" :title="$t('deviceSimulation.pp_import')" top="30vh" :close-on-click-modal="false" @close="deviceDialogVisible = false" class="import-dialog" width="400px">
      <div class="dialog-header">
        <span> {{ $t('deviceSimulation.pp_import_file') }} </span>
        <el-button style="height: 24px; border-radius: 2px">
          <a style="text-decoration: none; font-weight: normal" href="https://cdn-welkin-public.nio.com/welkin/2024/06/20/cc542230-7deb-4613-b6a2-e9a4f6beb5af.csv"> {{ $t('deviceSimulation.pp_download') }}</a>
        </el-button>
      </div>
      <el-upload ref="uploadRef" :on-success="uploadResponse" class="upload-demo" drag action="/web/welkin/device/v1/devices/upload-csv" :data="uploadquery" multiple>
        <div class="el-upload__text">
          <UploadIcon />
          <span class="margin_l-6">{{ $t('deviceSimulation.pp_upload') }}</span
          >&nbsp;
          <em>{{ $t('deviceSimulation.pp_csv') }}</em>
        </div>
      </el-upload>
      <div class="flex-box flex_j_c-flex-end margin_t-16">
        <el-button @click="cancelImport" style="color: #595959; border: none">{{ $t('common.pp_cancel') }}</el-button>
        <el-button type="primary" style="margin-left: 4px" @click="importFile">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
    <!-- 设备导入 -->
    <el-dialog v-model="importNextDialogVisible" :title="$t('deviceSimulation.pp_import')" top="30vh" :close-on-click-modal="false" @close="deviceDialogVisible = false" class="import-dialog" width="520px">
      <div class="dialog-header">
        <span> {{ $t('deviceSimulation.pp_device') }} {{ allimportTableData.length }} </span>
        <el-button>
          <a style="text-decoration: none" href="https://cdn-welkin-public.nio.com/welkin/2024/06/20/cc542230-7deb-4613-b6a2-e9a4f6beb5af.csv"> {{ $t('deviceSimulation.pp_download') }}</a>
        </el-button>
      </div>
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="importTableData"
          ref="importmultipleTableRef"
          style="width: 100%"
          :header-cell-style="{
            background: '#E5F9F9',
            fontSize: '14px',
            color: '#262626',
            cursor: 'auto'
          }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="description" :label="`${$t('common.pp_device_name')}`" width="150" />
          <el-table-column prop="device_id" :label="`${$t('deviceSimulation.pp_station_id')}`" width="150" />
        </el-table>
        <div class="pagination-container">
          <el-pagination small background layout="prev, pager, next" class="mt-4" v-model:current-page="pageForm.page_no" v-model:page-size="pageForm.page_size" @size-change="myhandleSizeChange" @current-change="myhandleCurrentChange" :total="allimportTableData.length" />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDeviceDialog">
            {{ $t('common.pp_cancel') }}
          </el-button>
          <el-button @click="confirmImportDialog">
            {{ $t('common.pp_confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 枚举结果预览 -->
    <el-dialog v-model="enumResultDialogVisible" :title="$t('deviceSimulation.pp_preview_enum')" top="30vh" :close-on-click-modal="false" @close="deviceDialogVisible = false" class="import-dialog" width="520px">
      <div class="dialog-header">
        <span>
          <span>{{ $t('deviceSimulation.pp_total') }}</span
          >&nbsp; <span style="color: #262626; font-weight: bold">{{ allenumTableData.length }}</span
          >&nbsp;
          <span>{{ $t('deviceSimulation.pp_items') }}</span>
        </span>
      </div>
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="enumTableData"
          style="width: 100%"
          :header-cell-style="{
            background: '#E5F9F9',
            fontSize: '14px',
            color: '#262626',
            cursor: 'auto'
          }"
        >
          <el-table-column align="center" prop="id" :label="`${$t('deviceSimulation.pp_no')}`" width="55" />
          <el-table-column align="center" prop="battery_config[0]" :label="`${$t('deviceSimulation.pp_50')}`" width="105" />
          <el-table-column align="center" prop="battery_config[1]" :label="`${$t('deviceSimulation.pp_75')}`" width="105" />
          <el-table-column align="center" prop="battery_config[2]" :label="`${$t('deviceSimulation.pp_100')}`" width="105" />
          <el-table-column align="center" prop="battery_config[3]" :label="`${$t('deviceSimulation.pp_150')}`" width="105" />
        </el-table>
        <div class="pagination-container">
          <el-pagination class="mini-page" :pager-count="5" background layout="prev, pager, next" v-model:current-page="pageForm.page_no" v-model:page-size="pageForm.page_size" @size-change="enumhandleSizeChange" @current-change="enumhandleCurrentChange" :total="allenumTableData.length" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, toRefs, onBeforeUnmount, onBeforeMount, nextTick } from 'vue'
import { batteryLimit } from '~/views/create-config/components/constant'
import { ElMessage, ElTable, type FormInstance, type FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { Search } from '@element-plus/icons-vue'
import Steps from './component/steps.vue'
import Eye from './component/eye.vue'
import { apiGetEnumresult, apiGetAverage, apiPostCreateFromReal, apiGetConfigList } from '~/apis/run-list'
import { useRoute, useRouter } from 'vue-router'
import _ from 'lodash'
import { formatTime, getUserId } from '~/utils'
import './index'
import NextIcon from './component/icon/next-icon.vue'
import PreIcon from './component/icon/pre-icon.vue'
import UploadIcon from './component/icon/upload-icon.vue'
import InfoIcon from './component/icon/info-icon.vue'
import HelpIcon from './component/icon/help-icon.vue'
import RubbishIcon from './component/icon/rubbish-icon.vue'
import GreenAdd from './component/icon/green-add.vue'
import WarningIcon from './component/icon/warning-icon.vue'

const props = defineProps({
  isShow: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['updateList'])

const { t } = useI18n()
const current = ref(1)
const cancelDialogVisible = ref(false)
const deviceDialogVisible = ref(false)
const importDialogVisible = ref(false)
const enumResultDialogVisible = ref(false)
const importNextDialogVisible = ref(false)
const loading = ref(false)
const deviceShow = ref(false)
const createLoading = ref(false)
const route = useRoute()
const uploadquery = ref({})
let { query } = toRefs(route)
const enumFormRef = ref()
const settingFormRef = ref()
// const defineFormRef = ref();
const finishRef = ref()
const page_total = ref(0)
const page_config_total = ref(0)
const router = useRouter()
const nextDisabled = ref(false)

// 设备选择选中列表
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const multipleConfigTableRef = ref<InstanceType<typeof ElTable>>()
const importmultipleTableRef = ref<InstanceType<typeof ElTable>>()
const uploadRef = ref()
const multipleSelection = ref([] as any)
const multipleConfigSelection = ref([] as any)
const tableData = ref([] as any)
const importTableData = ref([] as any)
const allimportTableData = ref([] as any)
const enumTableData = ref([] as any)
const allenumTableData = ref([] as any)

const searchForm = reactive({
  config_name: '',
  config_id: '',
  page: 1,
  size: 10,
  page_no: 1,
  page_size: 5
})
const settingForm = reactive({
  date: '',
  step: 10,
  switch: false,
  totalCapacity: 500,
  line1Capacity: 250
})
const line2Capacity = ref(settingForm.totalCapacity - settingForm.line1Capacity)
const stopWatch = watch(settingForm, (newValue, oldValue) => {
  line2Capacity.value = settingForm.totalCapacity - settingForm.line1Capacity
})
const handleChangeSwitch = async (val: any) => {
  if (val) {
    configForm.optiFunction = 'battery'
  }
}
const clicktest = () => {
  console.log('configForm.optiFunction', configForm.optiFunction)
}
const btntest = () => {
  if (configForm.optiFunction) {
    let btnTimeout = setTimeout(() => {
      configForm.optiFunction = ''
      console.log('configForm.optiFunction', configForm.optiFunction)
      clearTimeout(btnTimeout)
    }, 50)
  }
  console.log('configForm.optiFunction', configForm.optiFunction)
}
const configForm = reactive({
  switch: true,
  optiFunction: '',
  proFunction: 1,
  defineForm: [[0, 0, 0, 0]],
  enumForm: [
    { max: 0, min: 0, label: '50' },
    { max: 0, min: 0, label: '75' },
    { max: 0, min: 0, label: '100' },
    { max: 0, min: 0, label: '150' }
  ]
}) as any
const createForm = reactive({
  task_name: '',
  reamark: ''
})
const pageForm = reactive({
  page_no: 1,
  page_size: 5,
  total: 0
})
const steps = ref([
  {
    title: t('deviceSimulation.pp_basic_settings'),
    description: false ? t('deviceSimulation.pp_basic_des') : t('deviceSimulation.pp_real_basic_des')
  },
  {
    title: t('deviceSimulation.pp_opti_config'),
    description: t('deviceSimulation.pp_opti_des')
  },
  {
    title: t('deviceSimulation.pp_finish_creating'),
    description: t('deviceSimulation.pp_finish_des')
  }
])

// 基础设置表单验证
const dateValidator = (rule: any, value: any, callback: any) => {
  let daterange = Date.parse(value[1]) - Date.parse(value[0])
  if (daterange > 86400000 || daterange < 300000) {
    return callback(new Error())
  } else {
    callback()
  }
}
const inputValidator = (rule: any, value: any, callback: any) => {
  const fields = rule.field.split('.')
  const val: any = Number(configForm[fields[0]][fields[1]][fields[2]])
  const curItem = configForm[fields[0]][fields[1]]
  if (!/^\d+$/.test(val)) {
    nextDisabled.value = true
    return callback(new Error('请输入大于0的数字'))
  }
  let total = 0
  curItem.map((item: any) => {
    if (item && item > 0) {
      total = total + item
    }
  })
  const project = query.value.project as string
  if (total > batteryLimit[project]) {
    nextDisabled.value = true
    return callback(new Error(`上限为${batteryLimit[project]}`))
  }
  nextDisabled.value = false
}
const totalValidator = (rule: any, value: any, callback: any) => {
  if (value < settingForm.line1Capacity) {
    return callback(new Error())
  } else {
    callback()
  }
}
const line1Validator = (rule: any, value: any, callback: any) => {
  if (value > settingForm.totalCapacity) {
    return callback(new Error())
  } else {
    callback()
  }
}
const stepValidator = (rule: any, value: any, callback: any) => {
  if (value >= 5 && value % 5 == 0 && value <= 120) {
    callback()
  } else {
    return callback(new Error())
  }
}
const checkForm = () => {
  enumFormRef.value.validate((valid: any) => {
    if (valid) {
      // 执行提交逻辑
    } else {
      console.error(t('deviceSimulation.pp_form_fail'))
    }
  })
}

// 删除选中内容
const closeTag = (tag: any) => {
  console.log(tag)
  multipleConfigSelection.value = multipleConfigSelection.value.filter((item: any) => item.config_id != tag.config_id)
  const allSectionList: any = multipleConfigSelection.value
  multipleConfigTableRef.value!.clearSelection()
  allSectionList.map((item: any) => {
    multipleConfigTableRef.value!.toggleRowSelection(item, true)
  })
}
const enumBatteryValidator = (rule: any, value: any, callback: any) => {
  const fields = rule.field.split('.')
  const val = configForm[fields[0]][fields[1]][fields[2]]
  const curItem = configForm[fields[0]][fields[1]]
  const enumList = configForm[fields[0]]
  let totalRight = 0
  enumList.map((item: any) => {
    totalRight = totalRight + item.max
  })
  const project = query.value.project as string
  if (!/^\d+$/.test(val)) {
    return callback(new Error(t('deviceSimulation.pp_number')))
  }
  if (val < 0) {
    return callback(new Error(t('deviceSimulation.pp_greater_zero')))
  }
  if (val > batteryLimit[project]) {
    return callback(new Error(`上限为${batteryLimit[project]}`))
  }
  if (curItem.min > curItem.max) {
    return callback(t('deviceSimulation.pp_min_max'))
  }
  return callback()
}
// 基础设置表单校验规则
const settingRules = reactive<FormRules>({
  date: [
    {
      required: true,
      message: t('deviceSimulation.pp_date_prompt'),
      trigger: 'blur'
    },
    {
      validator: (rule, value, cb) => dateValidator(rule, value, cb),
      message: t('deviceSimulation.pp_date_rangeprompt'),
      trigger: 'change'
    }
  ],
  totalCapacity: [
    {
      pattern: /^([0-9]|[1-5][0-9][0-9]{0,2}|[6][0-4][0-9]{0-2}|[6][5][0])$/,
      message: t('deviceSimulation.pp_total_prompt'),
      trigger: 'blur'
    },
    {
      validator: (rule, value, cb) => totalValidator(rule, value, cb),
      message: t('deviceSimulation.pp_totalcap_prompt'),
      trigger: 'change'
    }
  ],
  line1Capacity: [
    {
      validator: (rule, value, cb) => line1Validator(rule, value, cb),
      message: t('deviceSimulation.pp_capacity_prompt'),
      trigger: 'change'
    }
  ],
  step: [
    {
      validator: (rule, value, cb) => stepValidator(rule, value, cb),
      message: t('deviceSimulation.pp_step_prompt'),
      trigger: 'change'
    }
  ],
  inputValid: [
    {
      validator: (rule, value, cb) => inputValidator(rule, value, cb),
      trigger: 'change'
    }
  ]
})
const seeform = () => {
  console.log('configForm.enumForm', configForm.enumForm)
}
// 寻优配置表单验证规则
const enumRules = reactive<FormRules>({
  maxValid: [
    {
      validator: (rule, value, cb) => enumBatteryValidator(rule, value, cb),
      trigger: 'change'
    }
  ]
})
watch(current, (to) => {
  if (to == 1) {
    if (multipleConfigSelection.value.length > 0) {
      setTimeout(() => {
        multipleConfigSelection.value.forEach((row: any) => {
          multipleConfigTableRef.value!.toggleRowSelection(row, true)
        })
      }, 50)
    }
  }
})

// 新增
const addForm = () => {
  configForm.defineForm.push(query.value.project == 'PowerSwap2' ? [0, 0, 0, 0] : [0, 0, 0, 0, 0, 0])
}

// 删除
const deleteForm = (index: any) => {
  configForm.defineForm.splice(index, 1)
}

// 进度条
const onChange = (index: number) => {
  // 父组件获取切换后的选中步骤
  console.log('current:', index)
}
// 自定义配比验证
const configValid = async () => {
  current.value++
}

// 枚举方式配比验证
const enumValid = async () => {
  let total = computeBatteryNumber()
  if (total * multipleSelection.value.length > 240) {
    ElMessage.error(t('deviceSimulation.pp_over'))
    return
  }
  if (!enumFormRef) return
  await enumFormRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      current.value++
    }
  })
}

// 枚举配比验证
const onPrevious = () => {
  if (current.value > 1) {
    current.value--
  }
}
const onNext = async () => {
  // 基础设置表单验证
  if (current.value == 1) {
    if (multipleConfigSelection.value.length === 0) {
      ElMessage.warning('请先选择配方！')
      return
    }
    if (multipleConfigSelection.value.length > 10) {
      ElMessage.warning('配方不能超过10个！')
      return
    }
    // 设置默认值
    const res: any = await getAverage()
    const defaultVal = [] as any
    defaultVal.push(res.data)
    configForm.defineForm = defaultVal
    const list = res.data
    list.map((item: any, index: number) => {
      if (configForm.enumForm[index]) {
        configForm.enumForm[index].min = item
        configForm.enumForm[index].max = item
      }
    })
    current.value++
    return
  }
  if (current.value == 2) {
    if (configForm.proFunction === 1) {
      configValid()
    } else {
      enumValid()
    }
    return
  }
}

const giveUpCreate = () => {
  emit('updateList')
}

// 多选分页绑定id
const getRowKey = (row: any) => {
  return row.config_id
}

const handleSelectionChange = (val: any) => {
  multipleSelection.value = val
}

const handleSelectionConfigChange = (val: any) => {
  multipleConfigSelection.value = val
}

// 配方搜索
const searchConfig = async () => {
  searchForm.page_no = 1
  const params = {
    ...searchForm,
    creator: getUserId(),
    project: route.query.project
  }
  return await apiGetConfigList(params)
    .then((res) => {
      if (res.data) {
        tableData.value = res.data
        page_config_total.value = res.total
      } else {
        tableData.value = []
      }
    })
    .finally(() => {
      loading.value = false
    })
}
// 分页
const handleSizeChange = (val: number) => {
  searchForm.size = val
  searchForm.page = 1
  searchConfig()
}
const handleCurrentChange = (val: number) => {
  searchForm.page = val
  searchConfig()
}
const getImportData = (size: any, no: any) => {
  importTableData.value = allimportTableData.value.slice((no - 1) * size, no * size)
}
const myhandleSizeChange = (val: number) => {
  pageForm.page_size = val
  pageForm.page_no = 1
  getImportData(pageForm.page_size, pageForm.page_no)
}

const myhandleCurrentChange = (val: number) => {
  pageForm.page_no = val
  getImportData(pageForm.page_size, pageForm.page_no)
}

const getEnumData = (size: any, no: any) => {
  enumTableData.value = allenumTableData.value.slice((no - 1) * size, no * size)
}

const enumhandleSizeChange = (val: number) => {
  pageForm.page_size = val
  pageForm.page_no = 1
  getEnumData(pageForm.page_size, pageForm.page_no)
}

const enumhandleCurrentChange = (val: number) => {
  pageForm.page_no = val
  getEnumData(pageForm.page_size, pageForm.page_no)
}

// 设备选择确认
const confirmSelectDevice = () => {
  deviceDialogVisible.value = false
  deviceShow.value = true
}
// 打开批量导入
const openimportDialog = () => {
  uploadquery.value = { project: query.value.project }
  importDialogVisible.value = true
}
// 上传文件
const uploadResponse = (response: any) => {
  if (response.err_code === -1) {
    ElMessage.error(t('deviceSimulation.pp_notin_list'))
  } else {
    allimportTableData.value = response.data
    getImportData(pageForm.page_size, pageForm.page_no)
  }
}
// 取消设备选择
const cancelSelect = () => {
  // searchForm.device = '';
  multipleTableRef.value!.clearSelection()
  deviceDialogVisible.value = false
}
// 批量导入
const importFile = async () => {
  importDialogVisible.value = false
  importNextDialogVisible.value = true
}
// 取消上传csv
const cancelImport = () => {
  uploadRef.value?.clearFiles()
  importDialogVisible.value = false
}
// 确认批量导入
const confirmImportDialog = () => {
  deviceShow.value = true
  importNextDialogVisible.value = false
}

// 取消批量导入
const closeDeviceDialog = () => {
  uploadRef.value?.clearFiles()
  importmultipleTableRef.value!.clearSelection()
  importNextDialogVisible.value = false
}

// 计算自定义配比电池数量
const computeBatteryNumber = () => {
  let batteryTotal
  batteryTotal = configForm.defineForm.reduce((batteryTotal: any, obj: any) => (batteryTotal += obj[0]), 0)
  batteryTotal = configForm.defineForm.reduce((batteryTotal: any, obj: any) => (batteryTotal += obj[1]), batteryTotal)
  batteryTotal = configForm.defineForm.reduce((batteryTotal: any, obj: any) => (batteryTotal += obj[2]), batteryTotal)
  batteryTotal = configForm.defineForm.reduce((batteryTotal: any, obj: any) => (batteryTotal += obj[3]), batteryTotal)
  if(query.value.project !== 'PowerSwap2') {
    batteryTotal = configForm.defineForm.reduce((batteryTotal: any, obj: any) => (batteryTotal += obj[4]), batteryTotal)
    batteryTotal = configForm.defineForm.reduce((batteryTotal: any, obj: any) => (batteryTotal += obj[5]), batteryTotal)
  }
  return batteryTotal
}

let battery_config = [] as any
// 获取平均
const getAverage = async () => {
  let configs = [] as any
  const project = query.value.project
  multipleConfigSelection.value.forEach((item: any) => {
    configs.push(item.config_id)
  })
  const res = await apiGetAverage({ configs, project })
  battery_config = res.data
  return res
}
// 预览枚举结果
const previewEnumResult = async (status: any) => {
  const config_count = multipleConfigSelection.value.length
  const project = query.value.project
  await getAverage()
  let lower_limit = [] as any
  let upper_limit = [] as any
  configForm.enumForm.forEach((item: any) => {
    lower_limit.push(item.min)
    upper_limit.push(item.max)
  })
  enumResultDialogVisible.value = status

  await apiGetEnumresult({
    battery_config,
    config_count,
    project,
    lower_limit,
    upper_limit
  })
    .then((res) => (allenumTableData.value = res.data))
    .finally(() => {
      loading.value = false
    })
  getEnumData(pageForm.page_size, pageForm.page_no)
}

const getBatteryConfig = async () => {
  if (configForm.proFunction === 1) {
    return configForm.defineForm
  } else {
    await previewEnumResult(false)
    let battery = [] as any
    allenumTableData.value.forEach((item: any) => {
      battery.push(Object.values(item.battery_config))
    })
    return battery
  }
}
// 新建仿真
const createTask = async () => {
  let battery_config = [] as any
  if (createLoading.value) return
  if (!finishRef) return
  await finishRef.value.validate(async (valid: any, fields: any) => {
    if (valid) {
      let configs = [] as any
      multipleConfigSelection.value.forEach((item: any) => {
        configs.push(item.config_id)
      })
      createLoading.value = true
      await getBatteryConfig().then((value) => {
        battery_config = value
      })
      enumResultDialogVisible.value = false
      const data = {
        ...createForm,
        configs,
        project: query.value.project,
        start_ts: Date.parse(settingForm.date[0]),
        end_ts: Date.parse(settingForm.date[1]),
        station_capacity: settingForm.totalCapacity,
        circuit_capacity: settingForm.line1Capacity,
        simulation_step: settingForm.step,
        eps_params: settingForm.switch ? {
          eps_model_mode: 3,
          eps_high_price_peak_shift_enable: 1,
          eps_peak_shift_power_effective_time: '23:00-07:00',
          eps_peak_shift_power_limit: 20
        } : null,
        optimize_switch: configForm.optiFunction ? 1 : 0,
        optimize_mode: configForm.optiFunction ? 1 : null,
        battery_config
      }
      const res = await apiPostCreateFromReal(data)
      createLoading.value = false
      if (res.err_code) {
        ElMessage.error(res.message)
      } else {
        emit('updateList')
        ElMessage.success(t('deviceSimulation.pp_create_success'))
      }
    }
  })
}
onBeforeMount(() => {
  let timeOut = setTimeout(() => {
    configForm.enumForm =
      route.query.project == 'PowerSwap2'
        ? [
            { max: 0, min: 0, label: '50' },
            { max: 0, min: 0, label: '75' },
            { max: 0, min: 0, label: '100' },
            { max: 0, min: 0, label: '150' }
          ]
        : [
            { max: 0, min: 0, label: '50' },
            { max: 0, min: 0, label: '60' },
            { max: 0, min: 0, label: '75' },
            { max: 0, min: 0, label: '85' },
            { max: 0, min: 0, label: '100' },
            { max: 0, min: 0, label: '150' }
          ]
    searchConfig()
    clearTimeout(timeOut)
  }, 100)
})
</script>

<style lang="scss" scoped>
.form-container {
  font-family: 'Blue Sky Standard';
  font-size: 14px;
  font-weight: 400;
  background-color: #fff;
  margin-top: 24px;
  padding: 24px;

  .span {
    display: inline-flex;
    // width: 98px;
    height: 30px;
    font-size: 14px;
    font-weight: 400;
    line-height: 30px;
    color: #262626;
  }
  .radio-group {
    height: 32px;
    color: #262626;
    font-size: 14px;
    font-weight: 400 !important;
    line-height: 32px;
    text-align: left;
    margin: -4px 0 18px 0;
  }
  .finish-create {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    :deep(.el-form-item__label) {
      color: #595959;
      padding-right: 24px;
      width: auto;
    }
    :deep(.el-form-item__content) {
      align-items: flex-start;
    }
    :deep(.el-form-item__error) {
      top: 35%;
    }
  }
  .container-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
  }
}
:deep(.el-dialog__headerbtn .el-dialog__close) {
  font-size: 20px;
  color: #262626;
}
:deep(.el-dialog .el-dialog__header) {
  padding-bottom: 16px;
}
:deep(.el-form-item__label) {
  width: 128px;
  font-family: 'Blue Sky Standard';
  font-size: 14px;
  font-weight: 400;
  color: #262626;
}
:deep(.el-form-item) {
  margin-bottom: 23px;
}
:deep(.config-input) {
  .el-form-item__content {
    flex-wrap: nowrap;
  }
  .el-form-item__error {
    text-indent: 60px !important;
  }
  span {
    width: auto;
    color: #595959;
    margin-right: 16px;
  }
  .el-input {
    width: 80px;
    margin-right: 24px;
  }
}
:deep(.giveup-dialog) {
  .dialog_content {
    font-family: 'Blue Sky Standard';
    font-size: 14px;
    line-height: 22px;
    color: #262626;
  }
  .el-dialog__footer {
    padding: 0px 24px 24px;
  }
}
:deep(.el-dialog__header) {
  padding: 24px;
}
:deep(.el-dialog__body) {
  padding: 0 24px 24px 24px;
}
:deep(.el-dialog__title) {
  font-family: Blue Sky Standard;
  font-size: 18px;
  font-weight: 420;
  line-height: 26px;
  color: #1f1f1f;
}
:deep(.el-dialog__footer) {
  padding: 0 24px 24px 24px;
}
:deep(.el-upload-dragger) {
  border: 1px dashed #d9d9d9;
  width: 356px;
  height: 96px;
  border-radius: 4px;
  .el-upload__text {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #262626;
  }
  .el-upload__text em {
    color: #01a0ac;
  }
}
.device-dialog {
  .search-container {
    margin-bottom: 16px;
    display: flex;
    span {
      font-family: 'Blue Sky Standard';
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #595959;
      margin-right: 8px;
    }
    :deep(.el-dialog) {
      el-dialog {
        padding: 24px;
      }
    }
  }
}
.import-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: 'Blue Sky Standard';
    font-weight: 400;
    color: #595959;
    margin-bottom: 8px;
    .el-button {
      padding: 3px 12px;
      font-size: 12px;
      line-height: 18px;
      color: #01a0ac;
      border: 1px solid #00bebe;
    }
  }
  .dialog-footer {
    .el-button:last-child {
      background-color: var(--el-color-primary);
      color: #fff;
    }
  }
}
.pagination-container .el-pagination {
  margin-bottom: 0px;
}
:deep(.select-button) {
  color: #262626;
  font-family: 'Blue Sky Standard';
  font-weight: 400;
  .el-radio-button {
    width: 104px;
    height: 32px;
    margin-right: 50px;
  }
  .el-radio-button--large {
    // border: 1px solid #D9D9D9;
    border-radius: 4px !important;
    .el-radio-button__inner {
      width: 104px;
      height: 32px;
      display: inline-block;
      border: 1px solid #d9d9d9;
      border-radius: 4px !important;
      font-size: 14px;
      line-height: 7px;
    }
  }

  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    background-color: transparent;
    color: #262626;
    font-family: 'Blue Sky Standard';
    font-weight: 400;
    border: 1px solid #00bebe;
  }
  // 对勾
  .el-radio-button__original-radio:checked + .el-radio-button__inner::after {
    top: 0px;
    right: 0px;
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-top: 12px solid #00bebe;
    border-left: 18px solid transparent;
  }
  .el-radio-button__original-radio:checked + .el-radio-button__inner::before {
    content: '';
    position: absolute;
    width: 6px;
    height: 4.12px;
    background: transparent;
    top: 0px;
    right: 1px;
    border: 1px solid #fff;
    border-top: none;
    border-right: none;
    -webkit-transform: rotate(-55deg);
    -ms-transform: rotate(-55deg);
    transform: rotate(-55deg);
    z-index: 9;
  }
}
:deep(.el-radio-group) {
  flex-wrap: nowrap;
}
:deep(.el-input-group__append) {
  color: #262626;
}
:deep(.card) {
  margin-left: 128px;
  display: flex;
  flex-wrap: wrap;
  .card-content {
    height: 32px;
    padding: 3px 8px 3px 8px;
    border-radius: 4px;
    color: #262626;
    background: #e5f9f9;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-right: 8px;
    margin-bottom: 16px;
  }
}
:deep(.three-capacity) {
  display: flex;
  .el-form-item__content {
    flex-wrap: nowrap;
  }
  .el-form-item__error {
    left: 98px;
    top: 103%;
  }
}
:deep(.el-input) {
  .el-input__wrapper.is-focus {
    margin-right: 1px;
  }
}
:deep(.enum) {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  column-gap: 56px;
  width: 732px;
  .el-form-item__error {
    padding-left: 71px;
  }
  .enum-block-max {
    .el-form-item__error {
      padding-left: 45px !important;
    }
  }
  .enum-content {
    display: grid;
    grid-template-columns: 1fr 0.85fr;
    .enum-block {
      .enum-span {
        width: 56px;
        margin-right: 16px;
      }
      .el-form-item__content {
        flex-wrap: nowrap;
      }
      span {
        color: #595959;
      }
    }
  }
}
:deep(.long-append .el-input-group__append) {
  width: 50px;
}
:deep(.short-append .el-input-group__append) {
  width: 34px;
  padding: 0 16px;
}
:deep(.mid-append .el-input-group__append) {
  width: 34px;
  padding: 0 18px;
}
:deep(.el-textarea__inner) {
  color: #262626;
}
</style>
