<template>
  <div class="task-detail-container">
    <div class="search-container">
      <div class="flex-box flex_a_i-center">
        <span
          style="color: #595959; line-height: 22px"
          class="font-size-14 margin_r-8"
          >{{ $t('deviceSimulation.pp_config_id') }}</span
        >
        <el-select
          v-model="searchForm.config_id"
          @change="searchList"
          clearable
          filterable
          class="width-400"
          :placeholder="$t('common.pp_please_select')"
        >
          <el-option
            v-for="item in configOptions"
            :key="item"
            :value="item"
            :label="item"
          />
        </el-select>
      </div>
      <div class="search-button">
        <el-button @click="deviceDownload" class="welkin-primary-button" :disabled="deviceFlag">
          <DownloadIcon />
          <span class="margin_l-6">{{
            $t('deviceSimulation.pp_device_download')
          }}</span>
        </el-button>
        <el-button @click="batteryDownload" :disabled="batteryFlag">
          <DownloadIcon />
          <span class="margin_l-6">{{
            $t('deviceSimulation.pp_battery_download')
          }}</span>
        </el-button>
        <el-button @click="orderDownload" :disabled="orderFlag">
          <DownloadIcon />
          <span class="margin_l-6">{{
            $t('deviceSimulation.pp_order_download')
          }}</span>
        </el-button>
      </div>
    </div>

    <div class="table-container">
      <el-table
        :data="tableList"
        @sort-change="deviceSort"
        :header-cell-style="{
          background: '#E5F9F9',
          fontSize: '14px',
          color: '#262626',
        }"
      >
        <el-table-column
          prop="simulation_id"
          :label="$t('deviceSimulation.pp_simulate_id')"
          :min-width="250"
        />
        <el-table-column
          prop="avg_swapping_queue_time"
          :label="`${$t('deviceSimulation.pp_queue_time')}`"
          :min-width="120"
          sortable="custom"
        >
          <template #default="scope">
            {{ getDateString(scope.row.avg_swapping_queue_time) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="avg_battery_charging_time"
          :label="`${$t('deviceSimulation.pp_charging_time')}`"
          :min-width="120"
          sortable="custom"
        >
          <template #default="scope">
            {{ getDateString(scope.row.avg_battery_charging_time) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="hourly_avg_capacity_utilization"
          :label="`${$t('deviceSimulation.pp_progress_rate')}`"
          :min-width="140"
          sortable="custom"
        >
          <template #default="scope">
            {{ (scope.row.hourly_avg_capacity_utilization * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          :label="`${$t('deviceSimulation.pp_status')}`"
          min-width="130"
        >
          <template #default="{ row }">
            <div
              class="status-container"
              :style="{
                background: detailStatusMap[row.status].color,
                color: '#fff',
              }"
            >
              <span>{{
                $t(`deviceSimulation.${detailStatusMap[row.status]?.label}`)
              }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="service_count"
          :label="`${$t('deviceSimulation.pp_service_count')}`"
          min-width="130"
        />
        <el-table-column
          prop="battery_50_kw_count"
          label="50kWh"
          min-width="130"
        >
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>50kWh</span>
              <el-tooltip
                effect="light"
                :content="$t('deviceSimulation.pp_50_battery')"
                placement="bottom"
              >
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="battery_75_kw_count"
          label="75kWh"
          min-width="130"
        >
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>75kWh</span>
              <el-tooltip
                effect="light"
                :content="$t('deviceSimulation.pp_75_battery')"
                placement="bottom"
              >
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="battery_100_kw_count"
          label="100kWh"
          min-width="130"
        >
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>100kWh</span>
              <el-tooltip
                effect="light"
                :content="$t('deviceSimulation.pp_100_battery')"
                placement="bottom"
              >
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="battery_150_kw_count"
          label="150kWh"
          min-width="130"
        >
          <template #header>
            <div class="flex-box flex_a_i-center gap_4">
              <span>150kWh</span>
              <el-tooltip
                effect="light"
                :content="$t('deviceSimulation.pp_150_battery')"
                placement="bottom"
              >
                <HelpIcon />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="config_id"
          :label="`${$t('deviceSimulation.pp_simulation_id')}`"
          :min-width="450"
        />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  watch,
  toRefs,
  onBeforeUnmount,
  onBeforeMount,
  nextTick,
} from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { page } from '~/constvars/page';
import { detailStatusMap } from './statusMap';
import {
  Download,
  Delete,
  WarningFilled,
  Close,
} from '@element-plus/icons-vue';
import { apiGetDetail } from '~/apis/run-list';
import { useRoute, useRouter } from 'vue-router';
import DownloadIcon from './component/icon/download-icon.vue';
import HelpIcon from './component/icon/help-icon.vue';
const pages = ref(_.cloneDeep(page));
import _ from 'lodash';

const tableList = ref([] as any);
const simulation_id = ref();
const route = useRoute();
let { query } = toRefs(route);
const deviceFlag = ref(true);
const batteryFlag = ref(true);
const orderFlag = ref(true);
const getListTimeout = ref();
// let {info} = toRefs(route);
const props = defineProps({
  isDetail: {
    type: Boolean,
    default: true,
  },
  query: {
    type: Object,
  },
  info: {
    type: Object,
  },
});
const searchForm = reactive({
  task_id: props.query?.task_id,
  config_id: '',
  sort: '',
  descending: true,
});
const configOptions = ref([] as any);
const emit = defineEmits(['update:isDetail']);
const { t } = useI18n();

// 分页
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  // getDeviceList()
};

const getDateString = (date: any) => {
  let minute = Math.floor(date / 60)
    .toString()
    .padStart(2, '0');
  let second = (date % 60).toString().padStart(2, '0');
  return `${minute}:${second}`;
};

const getDetailTable = async () => {
  getListTimeout.value && clearTimeout(getListTimeout.value);
  if (props.info?.device_compress_task_status === 'success') {
    deviceFlag.value = false;
  }
  if (props.info?.battery_compress_task_status === 'success') {
    batteryFlag.value = false;
  }
  if (props.info?.service_compress_task_status === 'success') {
    orderFlag.value = false;
  }
  try {
    const res = await apiGetDetail(searchForm);
    tableList.value = res.data || [];
    configOptions.value = [
      ...new Set(tableList.value.map((item: any) => item.config_id)),
    ];
    getListTimeout.value = setTimeout(() => {
      getDetailTable();
    }, 10000);
  } catch (error) {}
};

const getQuery = () => {
  const task_id = query.value.task_id;
};

const deviceDownload = () => {
  // console.log(props.info,"props.info")
  if (!deviceFlag.value) {
    deviceFlag.value = false;
    window.open(props.info?.device_result_url);
  } else {
    deviceFlag.value = true;
  }
};
const batteryDownload = () => {
  if (!batteryFlag.value) {
    batteryFlag.value = false;
    window.open(props.info?.battery_result_url);
  } else {
    batteryFlag.value = true;
  }
};
const orderDownload = () => {
  if (!orderFlag.value) {
    orderFlag.value = false;
    window.open(props.info?.service_result_url);
  } else {
    orderFlag.value = true;
  }
};

const searchList = async () => {
  const res = await apiGetDetail(searchForm);
  tableList.value = res.data;
};

const deviceSort = (column: any) => {
  searchForm.sort = column.prop;
  searchForm.descending = column.order === 'descending' ? true : false;
  getDetailTable();
};

onBeforeMount(() => {
  getDetailTable();
});

onBeforeUnmount(() => {
  getListTimeout.value && clearTimeout(getListTimeout.value);
});
</script>

<style lang="scss" scoped>
.task-detail-container {
  .search-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    :deep(.search-button) {
      margin-left: 16px;
    }
  }
  :deep(.table-container) {
    .el-table td.el-table__cell div {
      color: #262626;
    }
    .status-container {
      width: 71px;
      height: 24px;
      padding: 2px 16px;
      border-radius: 14px;
      font-size: 13px;
      line-height: 20px;
      font-weight: bold;
    }
  }
}
</style>
