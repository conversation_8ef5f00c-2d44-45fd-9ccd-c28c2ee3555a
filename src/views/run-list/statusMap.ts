import { useI18n } from 'vue-i18n'

export const statusOptions = [
  {
    label: 'pp_create',
    value: 'create'
  },
  {
    label: 'pp_run',
    value: 'run'
  },
  {
    label: 'pp_finished',
    value: 'finish'
  },
  {
    label: 'pp_stopped',
    value: 'stop'
  }
]

export const statusMap = {
  create: { label: 'pp_create', color: '#FFC031' },
  run: { label: 'pp_run', color: '#5EB0FF' },
  finish: { label: 'pp_finished', color: '#67C23A' },
  stop: { label: 'pp_stopped', color: '#BFBFBF' }
} as any

export const detailStatusMap = {
  create: { label: 'pp_create', color: '#FFC031' },
  run: { label: 'pp_run', color: '#5EB0FF' },
  success: { label: 'pp_finished', color: '#67C23A' },
  fail: { label: 'pp_stopped', color: '#FF7575' }
}

export const projectOptions = [
  {
    label: 'pp_swap_station2',
    value: 'PowerSwap2'
  },
  {
    label: 'pp_swap_station3',
    value: 'PUS3'
  },
  {
    label: 'pp_swap_station4',
    value: 'PUS4',
  }
]

export const projectMap = {
  PUS4: {
    name: 'menu.pp_swap_station4',
    color: '#21819D'
  },
  PUS3: {
    name: 'menu.pp_swap_station3',
    color: '#13C2C2'
  },
  PowerSwap2: {
    name: 'menu.pp_swap_station2',
    color: '#52C41A'
  },
  PowerSwap: {
    name: 'menu.pp_swap_station1',
    color: '#FFC03D'
  }
} as any

export const contentMap = {
  create_count: { label: 'pp_create', color: '#FFC031' },
  run_count: { label: 'pp_running', color: '#5EB0FF' },
  success_count: { label: 'pp_run_success', color: '#67C23A' },
  fail_count: { label: 'pp_run_fail', color: '#FF7575' }
} as any
