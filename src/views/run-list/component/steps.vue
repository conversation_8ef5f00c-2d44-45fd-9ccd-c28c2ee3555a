<template>
  <div class="m-steps-area" :style="`width: ${totalWidth};`">
    <div class="m-steps">
      <div
        :class="[
          'm-steps-item',
          {
            finish: currentStep >= index + 1,
            process: currentStep > 1 && currentStep > index + 1,
            wait: currentStep < index + 1
          }
        ]"
        v-for="(step, index) in steps"
        :key="index"
      >
        <div class="m-info-wrap" @click="onChange(index + 1)">
          <div class="m-steps-icon">
            <span class="u-num" v-if="index < steps.length - 1 || !withCompletedIcon">{{ index + 1 }}</span>
            <svg class="u-icon" v-else viewBox="64 64 896 896" data-icon="check" aria-hidden="true" focusable="false"><path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"></path></svg>
          </div>
          <div class="m-steps-content">
            <div class="u-steps-title margin_b-2">{{ step.title }}</div>
            <div class="u-steps-description" v-show="step.description" style="max-width: 100%">{{ step.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
interface Step {
  title?: string // 标题
  description?: string // 描述
}
interface Props {
  steps: Step[] // 步骤数组
  current?: number // 当前选中的步骤（v-model），设置 v-model 后，Steps 变为可点击状态。从1开始计数
  width?: number | string // 步骤条总宽度
  descMaxWidth?: number // 描述文本最大宽度
  withCompletedIcon?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  steps: () => [],
  current: 1,
  width: '100%',
  descMaxWidth: 120,
  withCompletedIcon: true
})
const totalWidth = computed(() => {
  if (typeof props.width === 'number') {
    return props.width + 'px'
  } else {
    return props.width
  }
})
const totalSteps = computed(() => {
  // 步骤总数
  return props.steps.length
})
const currentStep = computed(() => {
  if (props.current < 1) {
    return 1
  } else if (props.current > totalSteps.value + 1) {
    return totalSteps.value + 1
  } else {
    return props.current
  }
})
// 若当前选中步骤超过总步骤数，则默认选择步骤1
const emits = defineEmits(['update:current', 'change'])
function onChange(index: number) {
  // 点击切换选择步骤
  if (currentStep.value !== index) {
    emits('update:current', index)
    emits('change', index)
  }
}
</script>

<style lang="scss" scoped>
$primary-color: #00bebe;
.m-steps-area {
  font-family: 'Blue Sky Standard';
  margin: 0 auto;
  .m-steps {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    .m-steps-item {
      display: inline-block;
      overflow: hidden;
      font-size: 14px;
      &:not(:last-child) {
        flex: 1;
        .u-steps-title {
          &::after {
            background: #d9d9d9;
            position: absolute;
            top: 14px;
            left: 100%;
            display: block;
            width: 3000px;
            height: 1px;
            content: '';
            cursor: auto;
            transition: all 0.3s;
          }
        }
      }
      .m-info-wrap {
        display: flex;
        .m-steps-icon {
          border: 1px solid #d9d9d9;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          text-align: center;
          transition: all 0.3s;
          .u-num {
            display: inline-block;
            font-size: 14px;
            line-height: 1;
            transition: all 0.3s;
            font-weight: 400;
          }
          .u-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            fill: #d9d9d9;
          }
        }
        .m-steps-content {
          display: inline-block;
          vertical-align: top;
          .u-steps-title {
            position: relative;
            font-size: 16px;
            display: inline-block;
            padding-right: 16px;
            color: #262626;
            line-height: 24px;
            font-weight: bold;
          }
          .u-steps-description {
            width: 100%;
            color: #595959;
            font-size: 12px;
            line-height: 18px;
          }
        }
      }
    }
    .finish {
      .m-info-wrap {
        .m-steps-icon {
          background: $primary-color;
          border: 1px solid #00bebe;
          .u-num {
            color: #fff;
            font-size: 14px;
            font-weight: 400;
          }
          .u-icon {
            fill: #fff;
            font-size: 14px;
            font-weight: 400;
          }
        }
        // .m-steps-content {
        //   .u-steps-title {
        //     color: rgba(0, 0, 0, .88);
        //     &::after {
        //       background: $primary-color;
        //     }
        //   }
        //   .u-steps-description {
        //     color: rgba(0, 0, 0, .45);
        //   }
        // }
      }
    }
    .process {
      .m-steps-content {
        .u-steps-title {
          &::after {
            background: $primary-color;
          }
        }
      }
    }
  }
}
</style>
