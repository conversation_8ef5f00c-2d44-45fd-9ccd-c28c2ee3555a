<template>
  <el-dialog width="1200px" align-center v-model="dialogVisible" @close="close" :show-close="false">
    <template #header>
      <div class="dialog-title">
        <span>{{ $t('serviceTrace.pp_alarm_info') }}</span>
        <span class="font-size-14">{{ $t('station.pp_service_id') }}： {{ rowInfo.service_id }}</span>
      </div>
    </template>
    <div class="swap-table-container">
      <el-table
        :data="alarmList"
        style="width: 100%"
        :header-cell-style="{
          fontSize: '14px',
          color: '#292C33',
          cursor: 'auto'
        }"
        v-loading="loading"
      >
        <el-table-column prop="data_id_description" :label="$t('alarmList.pp_alarm_description')" min-width="220" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.data_id_description ? scope.row.data_id_description : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="data_id" :label="$t('alarmList.pp_alarm_id')" min-width="110" show-overflow-tooltip />
        <el-table-column prop="alarm_type" :label="$t('alarmList.pp_alarm_type')" width="130" show-overflow-tooltip>
          <template #default="scope">
            {{ $t(alarmTypeMap[scope.row.alarm_type]) }}
          </template>
        </el-table-column>
        <el-table-column prop="alarm_level" :label="$t('alarmList.pp_alarm_level')" width="140" show-overflow-tooltip>
          <template #default="scope">
            <div class="status-container">
              <Icon icon="ep:warn-triangle-filled" :style="{fontSize: '18px', color: alarmLevelMap[scope.row.alarm_level].color}"></Icon>
              <span class="status-text">{{ $t(alarmLevelMap[scope.row.alarm_level].name) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="state" :label="$t('alarmList.pp_alarm_status')" width="130" show-overflow-tooltip>
          <template #default="scope">
            <div class="status-container">
              <el-icon :size="18" color="#00B42A" v-if="scope.row.state == 1"><Finished /></el-icon>
              <el-icon :size="18" color="#F53F3F" v-if="scope.row.state == 2"><Loading /></el-icon>
              <el-icon :size="18" color="#808080" v-if="scope.row.state == 3"><Hide /></el-icon>
              <span class="status-text">{{ $t(stateMap[scope.row.state]) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_ts" :label="$t('alarmList.pp_create_time')" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatTime(scope.row.create_ts) }}
          </template>
        </el-table-column>
        <el-table-column prop="clear_ts" :label="$t('alarmList.pp_clear_time')" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatTime(scope.row.clear_ts) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container" v-if="alarmList.length > 0">
        <Page :page="pages" @change="handlePageChange" />
      </div>

      <div class="flex-box flex_j_c-center flex_a_i-center" v-if="alarmList.length > 0">
        <el-button class="welkin-primary-button" @click="close">{{ $t('common.pp_close') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, toRefs} from 'vue'
import {Icon} from '@iconify/vue/dist/iconify'
import {Loading, Finished, Hide} from '@element-plus/icons-vue'
import {formatTime} from '~/utils'
import _ from 'lodash'

const props = defineProps({
  alarmDialogVisible: {
    type: Boolean,
    default: false
  },
  alarmList: {
    type: Array,
    default: []
  },
  inPage: {
    type: Object,
    default: {}
  },
  inLoading: {
    type: Boolean,
    default: false
  },
  rowInfo: {
    type: Object,
    default: {}
  }
})
const emits = defineEmits(['close', 'getAlarmList'])

const {alarmDialogVisible, alarmList, inPage, inLoading, rowInfo} = toRefs(props)
const dialogVisible = alarmDialogVisible
const pages = inPage
const loading = inLoading
const alarmTypeMap = ref({
  1: 'alarmList.pp_basic_alarm',
  2: 'alarmList.pp_battery_alarm',
  3: 'alarmList.pp_unknown_alarm'
})
const alarmLevelMap = ref({
  0: {
    name: 'alarmList.pp_unknown_alarm',
    color: '#9e9e9e'
  },
  1: {
    name: 'alarmList.pp_first_level',
    color: '#0000FF'
  },
  2: {
    name: 'alarmList.pp_second_level',
    color: '#FFA500'
  },
  3: {
    name: 'alarmList.pp_third_level',
    color: '#FF0000'
  }
})
const stateMap = ref({
  1: 'alarmList.pp_cleared',
  2: 'alarmList.pp_alarming',
  3: 'alarmList.pp_unknown'
})

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  emits('getAlarmList', argPage)
}

const close = () => {
  emits('close')
}
</script>

<style lang="scss" scoped>
.dialog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #303133;
  font-size: 18px;
}
</style>
