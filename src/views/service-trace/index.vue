<template>
  <div class="service-trace-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_info_trace') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_service_trace') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_service_trace') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" inline style="width: 100%">
          <el-form-item :label="$t('station.pp_battery_id')" class="upper-form-item" style="width: 45%">
            <el-input v-model="form.battery_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
          </el-form-item>
          <el-form-item :label="$t('serviceTrace.pp_car_vin')" class="upper-form-item" style="width: 45%">
            <el-input v-model="form.ev_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
          </el-form-item>
          <el-form-item :label="$t('serviceTrace.pp_abnormal_images')" class="upper-form-item">
            <el-select v-model="form.image_status_code" clearable :placeholder="$t('common.pp_please_select')" class="width-150">
              <el-option v-for="item in abnormalImageOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('serviceTrace.service_end_normally')" class="upper-form-item">
            <el-select v-model="form.finish_result" clearable :placeholder="$t('common.pp_please_select')" class="width-150">
              <el-option v-for="item in abnormalServiceOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('common.pp_time')" class="upper-form-item">
            <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="setCuts()" :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
          </el-form-item>
          <el-form-item class="upper-form-item">
            <el-button class="welkin-primary-button" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="swap-table-container">
        <el-table :data="list" style="width: 100%" :header-cell-style="{fontSize: '14px', color: '#292C33', cursor: 'auto'}" v-loading="loading" v-show="hasData">
          <el-table-column prop="finish_result" min-width="40" show-overflow-tooltip>
            <template #default="scope">
              <el-tooltip effect="dark" :content="$t(finalResultMap[scope.row.finish_result.toString()])" placement="top">
                <div :class="finalResultClassMap[scope.row.finish_result.toString()]"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="service_start_time" :label="$t('common.pp_start_time')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              {{ formatTime(scope.row.service_start_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="service_end_time" :label="$t('common.pp_end_time')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              {{ formatTime(scope.row.service_end_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="$t('alarmList.pp_device')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.description ? scope.row.description : $t('common.pp_unnamed_device') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="device_id" :label="$t('deviceManagement.pp_device_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>
          <el-table-column prop="service_battery_id" :label="$t('serviceTrace.service_battery_id')" min-width="300">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.service_battery_id" :class="searchForm.battery_id == scope.row.service_battery_id ? 'light-background' : 'normal-background'" />
            </template>
          </el-table-column>
          <el-table-column prop="ev_battery_id" :label="$t('serviceTrace.car_battery_id')" min-width="300">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.ev_battery_id" :class="searchForm.battery_id == scope.row.ev_battery_id ? 'light-background' : 'normal-background'" />
            </template>
          </el-table-column>
          <el-table-column prop="ev_id" :label="$t('serviceTrace.pp_car_vin')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="maskString(scope.row.ev_id)" />
            </template>
          </el-table-column>
          <el-table-column prop="project" :label="$t('serviceTrace.swap_station_type')" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <span :style="`color: ${projectMap[scope.row.project].color}`">{{ $t(projectMap[scope.row.project].name) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="service_id" :label="$t('station.pp_service_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.service_id" />
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="150" class-name="operation-column">
            <template #header>
              <span class="padding-n-6">{{ $t('common.pp_operation') }}</span>
            </template>
            <template #default="scope">
              <div @click.prevent="handleJumpService(scope.row)">
                <a :href="`${protocol}//${host}/${pathMap[scope.row.project]}/service-list/service-detail/${scope.row.device_id}/${scope.row.service_id}?start_time=${scope.row.service_start_time}&end_time=${scope.row.service_end_time}`">
                  <el-popover width="auto" placement="bottom" trigger="hover" effect="dark" :content="$t('menu.pp_service_detail')" popper-class="message-popover">
                    <template #reference>
                      <ServiceIcon />
                    </template>
                  </el-popover>
                </a>
              </div>
              <div @click="handleViewImage(scope.row)" class="cursor-pointer">
                <el-popover width="auto" placement="bottom" trigger="hover" effect="dark" :content="$t('station.pp_view_image')" popper-class="message-popover">
                  <template #reference>
                    <NormalImage />
                  </template>
                </el-popover>
              </div>
              <div v-if="scope.row.project != 'PowerSwap'">
                <el-popover width="auto" placement="bottom" trigger="hover" effect="dark" :content="$t('serviceTrace.pp_alarm_info')" popper-class="message-popover">
                  <template #reference>
                    <div @click="handleViewAlarm(scope.row)">
                      <el-icon class="operation-icon">
                        <Icon :icon="iconMap['view-alarm']" />
                      </el-icon>
                    </div>
                  </template>
                </el-popover>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-empty :description="$t('common.pp_empty')" v-show="!hasData"></el-empty>
        <div class="pagination-container" v-show="hasData">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>

      <div v-if="showImageViewer">
        <WelkinImageViewer :data="imageList" @onClose="closeImageView" />
      </div>
    </div>

    <AlarmDialog :alarmDialogVisible="alarmDialogVisible" :inPage="inPage" :rowInfo="rowInfo" :inLoading="inLoading" :alarmList="alarmList" @close="handleCloseAlarmDialog" @getAlarmList="getAlarmList" />
  </div>
</template>

<script setup lang="ts">
import {ref, watch, onBeforeUnmount, computed} from 'vue'
import {iconMap} from '~/auth'
import { useStore } from 'vuex'
import {formatTime, formatTimeToDayWithDash, setCuts, getDisabledDate, handleDayTimeChange, clearJson, removeNullKeys, maskString} from '~/utils'
import {apiGetAllAlarmList} from '~/apis/alarm-list'
import {apiGetServiceList} from '~/apis/service-trace'
import {apiGetImageList} from '~/apis/image-list'
import {projectMap} from '~/constvars'
import {page} from '~/constvars/page'
import {Icon} from '@iconify/vue/dist/iconify'
import {useI18n} from 'vue-i18n'
import {ElMessage} from 'element-plus'
import {useRoute, useRouter} from 'vue-router'
import _ from 'lodash'
import AlarmDialog from './alarm-dialog.vue'
import ServiceIcon from '~/assets/svg/service-icon.vue'
import NormalImage from '~/assets/svg/normal-image.vue'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const pathMap = ref(computed(() => store.state.vehicle.pathMap))
const ruleFormRef = ref()
const loading = ref(false)
const inLoading = ref(false)
const hasData = ref(false)
const showImageViewer = ref(false)
const alarmDialogVisible = ref(false)
const form = ref({
  battery_id: '',
  ev_id: '',
  image_status_code: '' as number | string,
  finish_result: '' as number | string,
  start_time: new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7,
  end_time: new Date().getTime()
})
const searchForm = ref({} as any)
const rowInfo = ref({} as any)
const abnormalImageOptions = ref([
  {
    label: 'common.pp_yes',
    value: 1
  },
  {
    label: 'common.pp_no',
    value: 0
  }
])
const abnormalServiceOptions = ref([
  {
    label: 'common.pp_yes',
    value: 1
  },
  {
    label: 'common.pp_no',
    value: 0
  }
])
const finalResultMap = ref({
  '-1': 'serviceTrace.no_service_info',
  '1': 'serviceTrace.service_ended_normally',
  '0': 'serviceTrace.service_ended_abnormally'
})
const finalResultClassMap = ref({
  '-1': 'no-service-info',
  '1': 'normal-finish',
  '0': 'abnormal-finish'
})
const pages = ref(_.cloneDeep(page))
const inPage = ref(_.cloneDeep(page))
const protocol = ref(window.location.protocol)
const host = ref(window.location.host)
const list = ref([] as any)
const alarmList = ref([] as any)
const imageList = ref([] as any)
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7, new Date().getTime()] as any)

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  form.value.start_time = new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7
  form.value.end_time = new Date().getTime()
  datePicker.value = [form.value.start_time, form.value.end_time]
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @param {*} status // 是否实时带筛选项
 * @return {*}
 */
const getList = async (updateRoute = true, status: number) => {
  if (loading.value) return
  let formData = {} as any
  if (status === 2) {
    formData = _.cloneDeep(searchForm.value)
  } else {
    formData = _.cloneDeep(form.value)
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.start_time = formatTimeToDayWithDash(formData.start_time)
  formData.end_time = formatTimeToDayWithDash(formData.end_time)
  formData.descending = true
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: removeNullKeys(formData)
    })
  }
  loading.value = true
  try {
    const res = await apiGetServiceList(formData)
    list.value = res.data
    hasData.value = list.value && list.value.length > 0
    pages.value.total = res.total
  } catch (error) {}
  loading.value = false
}

/**
 * @description: 点击筛选按钮
 * @return {*}
 */
const handleSearch = () => {
  if (form.value.battery_id || form.value.ev_id) {
    pages.value.current = 1
    searchForm.value = {...form.value}
    getList(true, 2)
  } else {
    ElMessage.warning(t('serviceTrace.pp_validate'))
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList(true, 2)
}

/**
 * @description: 跳转到详情页
 * @param {*} row
 * @return {*}
 */
const handleJumpService = (row: any) => {
  router.push({
    path: `/${pathMap.value[row.project]}/service-list/service-detail/${row.device_id}/${row.service_id}`,
    query: {
      start_time: row.service_start_time,
      end_time: row.service_end_time
    }
  })
}

/**
 * @description: 获取告警信息
 * @param {*} argPage
 * @return {*}
 */
const getAlarmList = async (argPage: any) => {
  inPage.value.current = argPage.current
  inPage.value.size = argPage.size
  try {
    const params = {
      start_time: rowInfo.value.service_start_time,
      end_time: rowInfo.value.service_end_time,
      device_id: rowInfo.value.device_id,
      page: inPage.value.current,
      size: inPage.value.size,
      descending: true
    }
    inLoading.value = true
    const res = await apiGetAllAlarmList(params, rowInfo.value.project)
    inLoading.value = false
    alarmList.value = res.data
    if (res.data.length == 0) {
      ElMessage.warning(t('serviceTrace.pp_lack_alarm'))
      alarmDialogVisible.value = false
    } else {
      alarmDialogVisible.value = true
    }
    inPage.value.total = res.total
  } catch (error) {
    inLoading.value = false
  }
}

/**
 * @description: 查看告警
 * @param {*} row
 * @return {*}
 */
const handleViewAlarm = (row: any) => {
  rowInfo.value = row
  inPage.value.current = 1
  alarmList.value = []
  getAlarmList(inPage.value)
}

/**
 * @description: 关闭告警信息弹窗
 * @return {*}
 */
const handleCloseAlarmDialog = () => {
  alarmDialogVisible.value = false
}

/**
 * @description: 查看图片
 * @param {*} row
 * @return {*}
 */
const handleViewImage = async (row: any) => {
  const start_time = row.service_start_time ? row.service_start_time : searchForm.value.start_time
  const end_time = row.service_end_time ? row.service_end_time + 5 * 60 * 1000 : searchForm.value.end_time + 5 * 60 * 1000
  const query = {
    service_id: row.service_id,
    start_time: start_time,
    end_time: end_time
  }
  try {
    const res = await apiGetImageList(query, row.device_id, row.project)
    if (res.data.length == 0) {
      ElMessage.warning(t('common.pp_lack_image'))
      showImageViewer.value = false
    } else {
      imageList.value = res.data
      showImageViewer.value = true
    }
  } catch (error) {}
}

/**
 * @description: 关闭图片弹窗
 * @return {*}
 */
const closeImageView = () => {
  showImageViewer.value = false
}

/**
 * @description: 时间
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  datePicker.value = handleDayTimeChange(value)
  form.value.start_time = datePicker.value[0].getTime()
  form.value.end_time = datePicker.value[1].getTime()
}

const stopWatch = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      form.value.start_time = !!initParams.start_time ? new Date(initParams.start_time).getTime() : new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7
      form.value.end_time = !!initParams.end_time ? new Date(initParams.end_time).getTime() : new Date().getTime()
      datePicker.value[0] = form.value.start_time
      datePicker.value[1] = form.value.end_time
      form.value.battery_id = !!initParams.battery_id ? initParams.battery_id : ''
      form.value.ev_id = !!initParams.ev_id ? initParams.ev_id : ''
      form.value.image_status_code = !!initParams.image_status_code ? Number(initParams.image_status_code) : ''
      form.value.finish_result = !!initParams.finish_result ? Number(initParams.finish_result) : ''
      searchForm.value = {...form.value}
      if (form.value.battery_id || form.value.ev_id) getList(false, 2)
    }
  },
  {immediate: true}
)

onBeforeUnmount(() => {
  stopWatch()
})
</script>

<style lang="scss" scoped>
.service-trace-container {
  :deep(.swap-table-container .el-table .operation-column .cell) {
    justify-content: flex-start;
  }
  :deep(.upper-form-item) {
    margin-bottom: 15px;
  }
  :deep(.pagination-container .el-pagination) {
    margin-bottom: 20px;
  }
  .search-form-container {
    padding-bottom: 5px;
  }
  .light-background {
    color: #00bebe;
  }
  .normal-finish,
  .abnormal-finish,
  .no-service-info {
    position: relative;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: radial-gradient(#006400, #008000);
    box-shadow: inset -2px -2px 4px #008000, inset 2px 2px 4px #006400;
  }
  .abnormal-finish {
    background: radial-gradient(#8b0000, #ff0000);
    box-shadow: inset -2px -2px 4px #ff0000, inset 2px 2px 4px #8b0000;
  }
  .no-service-info {
    background: radial-gradient(#616161, #bdbdbd);
    box-shadow: inset -2px -2px 4px #bdbdbd, inset 2px 2px 4px #616161;
  }
  .normal-finish:after,
  .abnormal-finish:after,
  .no-service-info:after {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background: radial-gradient(circle at 20% 20%, white, transparent);
  }
}
</style>
