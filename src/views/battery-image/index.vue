<template>
  <div class="battery-history-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_info_trace') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_battery_image') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_battery_image') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" :rules="rules" inline style="width: 100%">
          <el-form-item prop="battery_id" :label="$t('station.pp_battery_id')" class="upper-form-item" style="width: 45%">
            <el-input v-model="form.battery_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
          </el-form-item>
          <el-form-item :label="$t('common.pp_time_frame')" class="upper-form-item" style="width: 45%">
            <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="$t('common.pp_please_select') + $t('common.pp_start_time')" :end-placeholder="$t('common.pp_please_select') + $t('common.pp_end_time')" :clearable="false" :disabledDate="getDisabledDate" />
          </el-form-item>
          <el-form-item :label="$t('station.pp_image_type')" class="upper-form-item" style="width: 45%">
            <el-select v-model="form.image_type" multiple collapse-tags collapse-tags-tooltip clearable :placeholder="$t('common.pp_please_select')" class="width-full">
              <el-option v-for="item in imageTypeOptions" :key="item.type" :value="item.type" :label="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('station.pp_image_status')" class="upper-form-item">
            <el-select v-model="form.abnormal" clearable :placeholder="$t('common.pp_please_select')" class="width-300">
              <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>
          <el-form-item class="upper-form-item">
            <el-button class="welkin-primary-button" :loading="loading" @click="handleSearch(ruleFormRef)">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="swap-table-container">
        <el-table :data="list" style="width: 100%" :header-cell-style="{fontSize: '14px', color: '#292C33', cursor: 'auto'}" v-loading="loading">
          <el-table-column prop="image_type" :label="$t('station.pp_image_type')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ getImageName(scope.row.image_type) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="service_id" :label="$t('batteryHistory.service_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.service_id" />
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="$t('camera.pp_station_name')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.description ? scope.row.description : $t('batteryHistory.unnamed_station') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="image_gen_time" :label="$t('common.pp_time')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              {{ formatTime(scope.row.image_gen_time) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="150" class-name="operation-column">
            <template #header>
              <span class="padding-n-6">{{ $t('common.pp_operation') }}</span>
            </template>
            <template #default="scope">
              <div @click="handleViewImage(scope.row)" class="cursor-pointer">
                <el-popover width="auto" placement="bottom" trigger="hover" effect="dark" :content="$t('batteryHistory.preview_image')" popper-class="message-popover">
                  <template #reference>
                    <NormalImage v-if="!scope.row.abnormal" />
                    <NonNormalImage v-else />
                  </template>
                </el-popover>
              </div>
              <div @click="handleDownloadSingleImage(scope.row)" class="cursor-pointer">
                <el-popover width="auto" placement="bottom" trigger="hover" effect="dark" :content="$t('batteryHistory.download_image')" popper-class="message-popover">
                  <template #reference>
                    <el-icon class="operation-icon">
                      <Icon :icon="iconMap['download-image']" />
                    </el-icon>
                  </template>
                </el-popover>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container" v-if="list && list.length > 0">
          <el-button class="welkin-primary-button" :loading="downLoading" @click="handleDownLoadAll">{{ $t('batteryHistory.full_download') }}</el-button>
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>

      <div v-if="showImageViewer">
        <WelkinImageViewer :data="imageList" @onClose="closeImageView" :showIndex="false" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onBeforeUnmount, watch, computed} from 'vue'
import {iconMap} from '~/auth'
import {useI18n} from 'vue-i18n'
import {useRoute, useRouter} from 'vue-router'
import {formatTime, getShortcuts, getDisabledDate, removeNullKeys, clearJson} from '~/utils'
import {apiGetImageType, apiGetImageList, apiDownloadImage} from '~/apis/owl'
import {page} from '~/constvars/page'
import {Icon} from '@iconify/vue/dist/iconify'
import type {FormInstance} from 'element-plus'
import NormalImage from '~/assets/svg/normal-image.vue'
import NonNormalImage from '~/assets/svg/non-normal-image.vue'
import _ from 'lodash'

const ruleFormRef = ref()
const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const {locale} = useI18n({useScope: 'global'})
const loading = ref(false)
const downLoading = ref(false)
const showImageViewer = ref(false)
const pages = ref(_.cloneDeep(page))
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date().getTime()] as any)
const imageTypeOptions = ref([] as any)
const list = ref([] as any)
const imageList = ref([] as any)
const statusOptions = ref([
  {
    label: 'common.pp_normal',
    value: 0
  },
  {
    label: 'common.pp_abnormal',
    value: 1
  }
])
const form = ref({
  battery_id: '',
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  image_type: [] as any,
  abnormal: '' as number | string
})
const searchForm = ref({
  battery_id: '',
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  image_type: [] as any,
  abnormal: '' as number | string
})

// rules配置为计算属性，可以更好国际化
const rules = computed(() => {
  return {
    battery_id: [{required: true, message: t('batteryHistory.input_battery_id'), trigger: 'blur'}]
  }
})

/**
 * @description: 切换时间
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  form.value.start_time = new Date(value[0]).getTime()
  form.value.end_time = new Date(value[1]).getTime()
}

/**
 * @description: 切换语言
 * @return {*}
 */
watch(
  () => locale.value,
  (newValue, oldValue) => {
    getImageType()
  }
)

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @param {*} status // 是否实时带筛选项
 * @return {*}
 */
const getList = async (updateRoute = true, status: number) => {
  if (loading.value) return
  let formData = {} as any
  if (status === 2) {
    formData = _.cloneDeep(searchForm.value)
  } else {
    formData = _.cloneDeep(form.value)
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.descending = true
  removeNullKeys(formData)
  if(formData.image_type) formData.image_type = formData.image_type.join(',')
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: formData
    })
  }
  loading.value = true
  try {
    const res = await apiGetImageList(formData)
    list.value = res.data
    pages.value.total = res.total
    if (list.value) list.value.forEach((item: any) => (item.image_description = getImageName(item.image_type)))
  } catch (error) {}
  loading.value = false
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  form.value.start_time = new Date(new Date().toLocaleDateString()).getTime()
  form.value.end_time = new Date().getTime()
  datePicker.value = [form.value.start_time, form.value.end_time]
}

/**
 * @description: 点击筛选按钮
 * @param {*} formEl
 * @return {*}
 */
const handleSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      pages.value.current = 1
      searchForm.value = {...form.value}
      getList(true, 2)
    }
  })
}

/**
 * @description: 查看图片
 * @param {*} row
 * @return {*}
 */
const handleViewImage = (row: any) => {
  imageList.value = []
  imageList.value.push(row)
  showImageViewer.value = true
}

const downloadImage = async (param: any) => {
  try {
    await apiDownloadImage(param)
  } catch (error) {
    console.log(error)
  }
}

/**
 * @description: 下载单张图片
 * @param {*} row
 * @return {*}
 */
const handleDownloadSingleImage = (row: any) => {
  const params = {record_id: row._id}
  downloadImage(params)
}

/**
 * @description: 全量下载
 * @return {*}
 */
const handleDownLoadAll = () => {
  const initParams = {
    ...searchForm.value,
    record_id: 'all'
  }
  const params = _.cloneDeep(initParams)
  removeNullKeys(params)
  downLoading.value = true
  downloadImage(params)
  downLoading.value = false
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList(true, 2)
}

/**
 * @description: 获取图片类型
 * @return {*}
 */
const getImageType = async () => {
  const res = await apiGetImageType()
  imageTypeOptions.value = res.data.slice(0, 4)
}

/**
 * @description: 表格内映射图片类型
 * @param {*} num
 * @return {*}
 */
const getImageName = (num: number) => {
  return imageTypeOptions.value.filter((item: any) => item.type == num)[0].name
}

/**
 * @description: 关闭图片弹窗
 * @return {*}
 */
const closeImageView = () => {
  showImageViewer.value = false
}

const stopWatch = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
        form.value.start_time = Number(initParams.start_time)
        form.value.end_time = Number(initParams.end_time)
        datePicker.value[0] = form.value.start_time
        datePicker.value[1] = form.value.end_time
      }
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      form.value.image_type = !!initParams.image_type ? initParams.image_type.split(',').map((item: any) => Number(item)) : []
      form.value.battery_id = !!initParams.battery_id ? initParams.battery_id : ''
      form.value.abnormal = !!initParams.abnormal ? Number(initParams.abnormal) : ''
      searchForm.value = {...form.value}
      getImageType()
      if (form.value.battery_id) getList(false, 2)
    }
  },
  {immediate: true}
)

onBeforeUnmount(() => {
  stopWatch()
})
</script>

<style lang="scss" scoped>
.battery-history-container {
  font-family: 'Blue Sky Standard';
  :deep(.upper-form-item) {
    margin-bottom: 15px;
  }
  :deep(.el-input__inner) {
    color: #262626;
  }
  :deep(.el-select .el-select__tags .el-tag--info) {
    color: #262626;
  }
  .header-title {
    font-weight: bold !important;
  }
  .search-form-container {
    padding-bottom: 5px;
  }
  .pagination-container {
    display: flex;
    justify-content: space-between;
    .download-button {
      background-color: var(--el-color-primary);
      color: #fff;
      width: 95px;
      margin-top: -10px;
    }
  }
}
</style>
