<template>
  <div class="service-detail-container">
    <div class="header-container">
      <div class="flex-box flex_a_i-center font-size-20 line-height-28">
        <BackIcon @click="changeToPre" class="cursor-pointer" />
        <span style="color: #8c8c8c; font-weight: 480; margin-left: 4px">{{ $t('menu.pp_service_list') }} /</span>
        <span style="color: #1f1f1f; font-weight: 420">&nbsp;{{ detailInfo.description + ' ' + detailInfo.device_id }}</span>
        <div class="tag-container" :style="{ background: projectColorMap[project.project].background, color: projectColorMap[project.project].color }">
          <pss1Icon v-if="project.version == 1" />
          <pss2Icon v-else-if="project.version == 2" />
          <pss3Icon v-else-if="project.version == 3" />
          <pss4Icon v-else-if="project.version == 4" />
          <firefly1Icon v-else />
          <span>{{ $t(projectColorMap[project.project].name) }}</span>
        </div>
      </div>
    </div>
    <ServiceDetail :project="project" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { ServiceDetail } from '../components'
import { projectColorMap } from '~/views/single-station/constant'
import BackIcon from '~/views/single-station/icon/back-icon.vue'
import pss1Icon from '~/views/single-station/icon/pss1-icon.vue'
import pss2Icon from '~/views/single-station/icon/pss2-icon.vue'
import pss3Icon from '~/views/single-station/icon/pss3-icon.vue'
import pss4Icon from '~/views/single-station/icon/pss4-icon.vue'
import firefly1Icon from '~/views/single-station/icon/firefly1-icon.vue'

const $router = useRouter()
const $store = useStore()
const project = ref(computed(() => $store.state.project))
const detailInfo = ref(computed(() => $store.state.service_detail))

// 回到上级页面
const changeToPre = () => {
  let query: any = sessionStorage.getItem('service-list')
  $router.push({
    path: `/${project.value.route}/service-list`,
    query: JSON.parse(query)
  })
}
</script>

<style lang="scss" scoped>
.service-detail-container {
  font-family: 'Blue Sky Standard';
  background: linear-gradient(180deg, #EEF6F8 -6.51%, #F4F5F8 12.89%);
  .header-container {
    padding: 24px 24px 20px;
    .tag-container {
      height: 26px;
      display: flex;
      align-items: center;
      gap: 6px;
      border-radius: 2px;
      padding: 2px 8px;
      font-size: 14px;
      line-height: 22px;
      margin-left: 8px;
    }
  }
}
</style>
