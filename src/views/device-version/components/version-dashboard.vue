<template>
  <div v-if="loading" class="flex-box flex_j_c-center flex_a_i-center height-full">
    <div class="loader-17"></div>
  </div>
  <div class="version-dashboard-container" v-else-if="list.length > 0">
    <div class="flex-box flex_j_c-space-between flex_a_i-center">
      <div class="flex-box flex_a_i-center gap_16">
        <el-radio-group v-model="method" @change="handleChangeMethod">
          <el-radio-button :label="1">{{ $t('deviceVersion.pp_version_tab') }}</el-radio-button>
          <el-radio-button :label="2">{{ $t('deviceVersion.pp_issue_work_order') }}</el-radio-button>
        </el-radio-group>
        <div v-if="method === 2" class="tip-text">
          {{ $t('deviceVersion.pp_selected') }} <span class="font-weight-bold">{{ checkedList.length }}</span> {{ $t('deviceVersion.pp_dashboard_tip') }}
        </div>
      </div>
      <el-button v-if="method === 2" @click="handleClickOrder" class="welkin-primary-button" :loading="orderLoading">{{ $t('deviceVersion.pp_issue_work_order') }}</el-button>
    </div>

    <div class="card-list">
      <div v-for="item in list" :key="item.version" :class="['dashboard-card', { 'active-dashboard-card': item.checked }]" @click="handleClickCard(item)">
        <div class="version-name">{{ item.version }}</div>
        <div>
          <span class="version-count">{{ item.count }}</span>
          <span class="version-unit">{{ $t('deviceVersion.pp_piece') }}</span>
        </div>
        <CheckIcon v-if="item.checked" class="checked-icon" />
      </div>
    </div>

    <!-- 选择设备版本下发工单弹窗 -->
    <OrderDialog v-model:orderDialogVisible="orderDialogVisible" :project="project" :deviceList="deviceList" :loading1="loading1" @handleConfirmOrder="handleConfirmOrder" />

    <!-- 已在期望升级的目标版本内的设备弹窗 -->
    <TargetDialog v-model:targetDialogVisible="targetDialogVisible" :targetList="targetList" :loading3="loading3" @handleConfirmTarget="handleConfirmTarget" />
  </div>
  <div v-else class="flex-box flex_j_c-center flex_a_i-center height-full">
    <el-empty :description="$t('common.pp_empty')">
      <template #image>
        <GrayEmptyDataIcon />
      </template>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { apiGetDashboard, apiGetVersionList, apiPostCheckTargetVersion, apiPostOrder } from '~/apis/device-version'
import { ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import GrayEmptyDataIcon from '~/assets/svg/gray-empty-data.vue'
import OrderDialog from './order-dialog.vue'
import TargetDialog from './target-dialog.vue'
import CheckIcon from './icon/check-icon.vue'

const props = defineProps({
  project: {
    type: String,
    default: 'PUS4'
  }
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const loading1 = ref(false)
const loading3 = ref(false)
const orderLoading = ref(false)
const orderDialogVisible = ref(false)
const targetDialogVisible = ref(false)
const method = ref(1)
const list = ref([] as any)
const deviceList = ref([] as any)
const targetList = ref([] as any)
const targetParams = ref({} as any)
const checkedList = computed(() => list.value.filter((item: any) => item.checked))

/**
 * @description: 点击下发工单按钮，获取设备列表
 * @return {*}
 */
const handleClickOrder = async () => {
  if (!checkedList.value.length) {
    ElMessage.warning(t('deviceVersion.pp_select_version'))
    return
  }
  try {
    orderLoading.value = true
    const params = {
      is_active: true,
      in_blacklist: false,
      PLM_list: checkedList.value.map((item: any) => item.version)
    }
    const res = await apiGetVersionList(props.project, params)
    orderLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      deviceList.value = res.data || []
      orderDialogVisible.value = true
    }
  } catch (error) {
    orderLoading.value = false
  }
}

/**
 * @description: 确认下发工单
 * @param {*} params
 * @return {*}
 */
const handleConfirmOrder = async (params: any) => {
  loading1.value = true
  targetParams.value = cloneDeep(params)
  try {
    const res = await apiPostCheckTargetVersion(props.project, params)
    if (res.err_code) {
      loading1.value = false
      ElMessage.error(res.message)
    } else {
      if (res.data.ignore_devices && res.data.ignore_devices.length > 0) {
        loading1.value = false
        targetList.value = res.data.ignore_devices
        targetDialogVisible.value = true
      } else {
        // 直接下发
        const result = await apiPostOrder(props.project, params)
        loading1.value = false
        if (result.err_code) {
          ElMessage.error(result.message)
        } else {
          ElMessage.success(t('deviceVersion.pp_issue_successful'))
          orderDialogVisible.value = false
          list.value.forEach((item: any) => {
            item.checked = false
          })
        }
      }
    }
  } catch (error) {
    loading1.value = false
  }
}

/**
 * @description: 有设备在目标升级版本内，确认下发
 * @return {*}
 */
const handleConfirmTarget = async () => {
  loading3.value = true
  try {
    const result = await apiPostOrder(props.project, targetParams.value)
    loading3.value = false
    if (result.err_code) {
      ElMessage.error(result.message)
    } else {
      ElMessage.success(t('deviceVersion.pp_issue_successful'))
      orderDialogVisible.value = false
      targetDialogVisible.value = false
      list.value.forEach((item: any) => {
        item.checked = false
      })
    }
  } catch (error) {
    loading3.value = false
  }
}

/**
 * @description: 切换版本列表/下发工单
 * @return {*}
 */
const handleChangeMethod = () => {
  list.value.forEach((item: any) => {
    item.checked = false
  })
}

/**
 * @description: 点击卡片，跳转到设备版本列表
 * @param {*} card
 * @return {*}
 */
const handleClickCard = (card: any) => {
  if (method.value === 1) {
    window.open(`//${location.host}/info-trace/device-version?tab=list&project=${props.project}&PLM=${card.version}&is_active=true&in_blacklist=false`)
  } else {
    list.value.forEach((item: any) => {
      if (item.version === card.version) {
        item.checked = !item.checked
      }
    })
  }
}

const getList = async (updateRoute = true) => {
  loading.value = true
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...route.query, project: props.project }
    })
  }
  try {
    const res = await apiGetDashboard(props.project)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data || []
      if (list.value.length > 0) {
        list.value.forEach((item: any) => {
          item.checked = false
        })
      }
    }
  } catch (error: any) {
    loading.value = false
  }
}

watch(
  () => props.project,
  (newVal, oldVal) => {
    getList()
  }
)

onBeforeMount(() => {
  getList(false)
})
</script>

<style lang="scss" scoped>
.version-dashboard-container {
  :deep(.el-radio-button__inner) {
    height: 32px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #262626;
    font-weight: bold;
    padding: 8px 12px;
  }
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-right > .el-form-item__label:after {
        color: #fd434a;
        font-weight: 500;
      }
    }
  }
  .tip-text {
    font-size: 14px;
    line-height: 22px;
    color: #303133;
  }
  .card-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(225px, 1fr));
    gap: 16px;
    margin-top: 16px;
    .dashboard-card {
      height: 128px;
      padding: 24px;
      cursor: pointer;
      border-radius: 4px;
      border: 1px solid #dcf2f3;
      background-color: #fff;
      position: relative;
      &:hover {
        border: 1px solid #00bebe;
        box-shadow: 0 2px 4px 0 #0000001a;
        background-color: rgba(229, 249, 249, 0.5);
      }
      .version-name {
        font-size: 16px;
        line-height: 24px;
        color: #595959;
        margin-bottom: 8px;
      }
      .version-count {
        font-size: 32px;
        line-height: 48px;
        color: #262626;
      }
      .version-unit {
        font-size: 14px;
        line-height: 36px;
        color: #595959;
        margin-left: 8px;
      }
      .checked-icon {
        position: absolute;
        right: -1px;
        top: 0;
      }
    }
    .active-dashboard-card {
      border: 1px solid #00bebe;
      box-shadow: 0 2px 4px 0 #0000001a;
      background-color: rgba(229, 249, 249, 0.5);
    }
  }
}
:deep(.el-empty) {
  padding: 0;
  .el-empty__description {
    margin-left: 30px;
  }
}
</style>
