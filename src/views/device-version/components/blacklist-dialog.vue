<template>
  <el-dialog v-model="blacklistDialogVisible" :title="$t('common.pp_prompt')" :width="batchEdit ? '350px' : '403px'" @close="handleCancel" align-center :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="flex-box flex_a_i-flex-start gap_8 margin_b-16">
      <WarningIcon class="margin_t-3" style="flex-shrink: 0" />
      <span class="font-size-14 line-height-22 color-26" v-if="enterBlacklist && batchEdit">
        {{ $t('deviceVersion.pp_blacklist_dialog_tip1') }} <span class="font-weight-bold">{{ deviceList.length }}</span> {{ $t('deviceVersion.pp_blacklist_dialog_tip2') }}
      </span>
      <span class="font-size-14 line-height-22 color-26" v-else-if="!enterBlacklist && batchEdit">
        {{ $t('deviceVersion.pp_blacklist_dialog_tip1') }} <span class="font-weight-bold">{{ deviceList.length }}</span> {{ $t('deviceVersion.pp_blacklist_dialog_tip3') }}
      </span>
      <span class="font-size-14 line-height-22 color-59" v-else-if="enterBlacklist && !batchEdit">
        {{ $t('deviceVersion.pp_blacklist_dialog_tip4') }} <span class="color-26 font-weight-450">{{ deviceList[0].description || $t('common.pp_unnamed_device') }}</span> {{ $t('deviceVersion.pp_blacklist_dialog_tip5') }}
      </span>
      <span class="font-size-14 line-height-22 color-59" v-else>
        {{ $t('deviceVersion.pp_blacklist_dialog_tip6') }} <span class="color-26 font-weight-450">{{ deviceList[0].description || $t('common.pp_unnamed_device') }}</span> {{ $t('deviceVersion.pp_blacklist_dialog_tip7') }}
      </span>
    </div>
    <div class="flex-box flex_j_c-flex-end flex_a_i-center">
      <el-button class="welkin-text-button" @click="handleCancel">{{ $t('common.pp_cancel') }}</el-button>
      <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="blackLoading" @click="handleConfirm">{{ $t('common.pp_confirm') }}</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, PropType } from 'vue'
import { useI18n } from 'vue-i18n'
import { apiPostBlacklist } from '~/apis/device-version'
import { ElMessage } from 'element-plus'
import WarningIcon from './icon/warning-icon.vue'

interface IDevice {
  device_id: string
  description: string
  [key: string]: any // 用于处理可能的其他动态属性
}

const props = defineProps({
  blacklistDialogVisible: Boolean,
  enterBlacklist: Boolean,
  batchEdit: Boolean,
  project: String,
  deviceList: {
    type: Array as PropType<IDevice[]>,
    default: () => []
  }
})

const emits = defineEmits(['update:blacklistDialogVisible', 'handleConfirmBlacklist'])

const { t } = useI18n()
const blackLoading = ref(false)

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:blacklistDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirm = async () => {
  blackLoading.value = true
  const params = {
    blacklist: props.enterBlacklist,
    device_list: props.deviceList.map((item: any) => item.device_id)
  }
  try {
    const res = await apiPostBlacklist(props.project, params)
    blackLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      ElMessage.success(`${t('deviceVersion.pp_blacklist_result_tip1')}${props.enterBlacklist ? t('deviceVersion.pp_blacklist_result_tip2') : t('deviceVersion.pp_blacklist_result_tip3')}${t('deviceVersion.pp_blacklist_result_tip4')}`)
      emits('update:blacklistDialogVisible', false)
      emits('handleConfirmBlacklist')
    }
  } catch (error) {
    blackLoading.value = false
  }
}
</script>
