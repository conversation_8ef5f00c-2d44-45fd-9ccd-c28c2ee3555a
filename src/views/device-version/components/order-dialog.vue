<template>
  <div class="order-dialog-container">
    <el-dialog v-model="orderDialogVisible" :title="$t('deviceVersion.pp_issue_work_order')" @open="handleOpen" @close="handleClose" width="513px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="createForm" ref="createFormRef" label-position="top" :rules="rules" require-asterisk-position="right">
        <el-form-item :label="$t('deviceVersion.pp_target_version')" prop="target_version">
          <el-select v-model="createForm.target_version" filterable :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option v-for="item in versionOptions" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.pp_remark')" prop="worksheet_description">
          <el-input v-model="createForm.worksheet_description" type="textarea" autosize clearable :placeholder="$t('common.pp_please_input')" />
          <div class="font-size-12 line-height-18 color-8c margin_t-2">工单描述：{{ worksheetDescription }}</div>
        </el-form-item>
      </el-form>
      <div class="info-box">
        <WarningIcon class="margin_t-3" style="flex-shrink: 0" />
        <span class="font-size-14 line-height-22 color-59">
          {{ $t('deviceVersion.pp_order_dialog_tip1') }} <span class="color-26 font-weight-bold">{{ deviceList.length }}</span> {{ $t('deviceVersion.pp_order_dialog_tip2') }} <span class="color-26 font-weight-bold">{{ inBlacklistDevice }}</span> {{ $t('deviceVersion.pp_order_dialog_tip3') }} <span class="color-26 font-weight-bold">{{ deviceList.length - inBlacklistDevice }}</span> {{ $t('deviceVersion.pp_order_dialog_tip4') }}
        </span>
      </div>
      <div class="flex-box flex_j_c-flex-end flex_a_i-center">
        <el-button class="welkin-text-button" @click="handleCancel">{{ $t('common.pp_cancel') }}</el-button>
        <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="loading1" @click="handleConfirm">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, PropType, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { apiGetVersionOptions } from '~/apis/device-version'
import { ElMessage } from 'element-plus'
import WarningIcon from './icon/warning-icon.vue'

interface IDevice {
  device_id: string
  description: string
  [key: string]: any // 用于处理可能的其他动态属性
}

const props = defineProps({
  orderDialogVisible: Boolean,
  project: String,
  loading1: Boolean,
  deviceList: {
    type: Array as PropType<IDevice[]>,
    default: () => []
  }
})

const emits = defineEmits(['update:orderDialogVisible', 'handleConfirmOrder'])

const { t } = useI18n()
const createFormRef = ref()
const createForm = ref({
  target_version: '',
  worksheet_description: ''
})
const rules = ref({
  target_version: [{ required: true, message: t('deviceVersion.pp_error_tip'), trigger: 'change' }]
})
const versionOptions = ref([] as any)
const inBlacklistDevice = computed(() => {
  const blacklist = props.deviceList.filter((item: any) => item.in_blacklist)
  return blacklist.length
})
const worksheetDescription = computed(() => {
  return `该换电站需要升级至${createForm.value.target_version || 'xx'}，请注意并尽快升级。` + createForm.value.worksheet_description
})

/**
 * @description: 打开弹窗
 * @return {*}
 */
const handleOpen = () => {
  getVersionList()
}

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleClose = () => {
  createForm.value = { target_version: '', worksheet_description: '' }
  createFormRef.value && createFormRef.value.resetFields()
  emits('update:orderDialogVisible', false)
}

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:orderDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirm = async () => {
  if (!createFormRef.value) return
  await createFormRef.value.validate(async (valid: any, fields: any) => {
    if (valid) {
      const params = {
        device_list: props.deviceList.map((item: any) => item.device_id),
        target_version: createForm.value.target_version,
        worksheet_description: worksheetDescription.value
      }
      emits('handleConfirmOrder', params)
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 获取版本号列表
 * @return {*}
 */
const getVersionList = async () => {
  try {
    const res = await apiGetVersionOptions(props.project)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      versionOptions.value = res.data || []
    }
  } catch (error: any) {
    versionOptions.value = []
  }
}
</script>

<style lang="scss" scoped>
.order-dialog-container {
  :deep(.el-dialog) {
    .el-form-item {
      margin-bottom: 16px;
      .el-form-item__label {
        color: #595959;
        margin-bottom: 4px;
      }
      .el-textarea__inner {
        color: #262626;
      }
    }
    .info-box {
      width: 100%;
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 8px 16px;
      margin-bottom: 16px;
      border-radius: 4px;
      border: 1px solid rgba(253, 140, 8, 0.5);
      background-color: rgba(253, 140, 8, 0.05);
    }
  }
}
</style>
