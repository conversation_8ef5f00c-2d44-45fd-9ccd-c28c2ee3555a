<template>
  <div class="target-dialog-container">
    <el-dialog v-model="targetDialogVisible" :title="$t('common.pp_prompt')" @close="handleClose" width="596px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="info-box">
        <WarningIcon class="margin_t-3" style="flex-shrink: 0" />
        <span class="font-size-14 line-height-22 color-26">
          {{ $t('deviceVersion.pp_target_dialog_tip1') }} <span class="color-26 font-weight-bold">{{ targetList.length }}</span> {{ $t('deviceVersion.pp_target_dialog_tip2') }}
        </span>
      </div>

      <el-table :data="targetList" max-height="245" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="device_id" :label="$t('common.pp_device_id')" width="156" show-overflow-tooltip />
        <el-table-column prop="description" :label="$t('common.pp_device_name')" width="224" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" :label="$t('deviceVersion.pp_device_activation')" width="88" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="isEmptyData(row.is_active)">-</span>
            <span v-else>{{ row.is_active ? 'TRUE' : 'FALSE' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="online" :label="$t('deviceVersion.pp_link_status')" width="80" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="isEmptyData(row.online)">-</span>
            <span v-else :class="row.online ? 'online-state' : 'offline-state'">{{ row.online ? $t('deviceVersion.pp_on_line') : $t('deviceVersion.pp_off_line') }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex-box flex_j_c-flex-end flex_a_i-center margin_t-16">
        <el-button class="welkin-text-button" @click="handleCancel">{{ $t('common.pp_cancel') }}</el-button>
        <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="loading3" @click="handleConfirm">{{ $t('deviceVersion.pp_confirm_issuance') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue'
import { isEmptyData } from '~/utils'
import WarningIcon from './icon/warning-icon.vue'

interface IDevice {
  device_id: string
  description: string
  [key: string]: any // 用于处理可能的其他动态属性
}

const props = defineProps({
  targetDialogVisible: Boolean,
  loading3: Boolean,
  targetList: {
    type: Array as PropType<IDevice[]>,
    default: () => []
  }
})

const emits = defineEmits(['update:targetDialogVisible', 'handleConfirmTarget'])

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleClose = () => {
  emits('update:targetDialogVisible', false)
}

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:targetDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirm = async () => {
  emits('handleConfirmTarget')
}
</script>

<style lang="scss" scoped>
.target-dialog-container {
  :deep(.el-dialog) {
    .info-box {
      width: 100%;
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 16px;
    }
    %state {
      height: 24px;
      border-radius: 2px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      padding: 0 12px;
      font-size: 13px;
      line-height: 20px;
    }
    .online-state {
      @extend %state;
      color: #2f9c74;
      background-color: #e8fcea;
    }
    .offline-state {
      @extend %state;
      color: #595959;
      background-color: rgba(140, 140, 140, 0.1);
    }
  }
}
</style>
