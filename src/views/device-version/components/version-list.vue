<template>
  <div class="version-list-container">
    <el-form :model="form" class="search-form">
      <el-form-item :label="$t('deviceVersion.pp_activation')">
        <el-select v-model="form.is_active" clearable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in isActiveOptions" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('deviceVersion.pp_link_status')">
        <el-select v-model="form.online" clearable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in onlineOptions" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="PLM">
        <el-input v-model="form.PLM" :placeholder="$t('common.pp_please_input')" clearable>
          <template #prefix><SearchIcon /></template>
        </el-input>
      </el-form-item>
      <el-form-item label="PLC">
        <el-input v-model="form.PLC" :placeholder="$t('common.pp_please_input')" clearable>
          <template #prefix><SearchIcon /></template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('deviceVersion.pp_in_blacklist')">
        <el-select v-model="form.in_blacklist" clearable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in inBlacklistOptions" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item style="grid-column: span 2">
        <div class="flex-box flex_a_i-center">
          <el-popover v-model:visible="isShowAdd" @before-enter="handleOpenPopover" :width="496" trigger="click" :show-arrow="false" placement="bottom-start" popper-class="search-popper">
            <template #reference>
              <div class="flex-box flex_a_i-center cursor-pointer">
                <SearchMoreIcon />
                <span class="margin_l-4 margin_r-8 color-59">{{ $t('deviceVersion.pp_add') }}</span>
                <span v-if="activeFilterCount > 0" class="active-filter-count">+{{ activeFilterCount }}</span>
                <ArrowDownIcon v-if="!isShowAdd" />
                <ArrowUpIcon v-else />
              </div>
            </template>
            <div class="flex-box flex_d-column gap_16">
              <div class="flex-box flex_j_c-space-between flex_a_i-center">
                <div class="font-size-14 line-height-22 color-26">{{ $t('deviceVersion.pp_add_filter') }}</div>
                <PopoverClose class="cursor-pointer" @click="isShowAdd = false" />
              </div>
              <!-- 添加动态筛选项 -->
              <div v-for="(item, index) in filterItems" :key="index" class="flex-box flex_a_i-center gap_16">
                <el-select v-model="item.selectedKey" :teleported="false" clearable filterable :placeholder="$t('deviceVersion.pp_select_filter')" class="width-200">
                  <el-option v-for="option in getAvailableFields(index)" :key="option.key" :label="option.key" :value="option.key" />
                </el-select>

                <el-select v-if="getFieldType(item.selectedKey) === 'radio'" v-model="item.value" :teleported="false" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-200">
                  <el-option v-for="opt in inBlacklistOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
                </el-select>
                <el-input v-else v-model="item.value" clearable :placeholder="$t('common.pp_please_input')" class="width-200" />

                <DeleteRubbish class="cursor-pointer" @click="removeFilterItem(index)" />
              </div>

              <div class="width-48 flex-box flex_a_i-center gap_4 cursor-pointer" @click="addFilterItem">
                <AddIcon />
                <span class="font-size-14 line-height-22 color-a0">{{ $t('deviceVersion.pp_add') }}</span>
              </div>

              <div class="flex-box flex_j_c-flex-end">
                <el-button class="welkin-text-button" @click="handleFilterReset">{{ $t('common.pp_reset') }}</el-button>
                <el-button class="welkin-primary-button" @click="handleFilterConfirm" style="margin-left: 4px">{{ $t('common.pp_confirm') }}</el-button>
              </div>
            </div>
          </el-popover>
          <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading" style="margin-left: 24px">{{ $t('common.pp_search') }}</el-button>
          <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
        </div>
      </el-form-item>
      <el-form-item v-if="hasPermission('function:device-version:edit')">
        <div class="width-full flex-box flex_j_c-flex-end">
          <el-button class="welkin-secondary-button" @click="isBatchEdit = true">
            <BatchEdit />
            <span class="margin_l-4">{{ $t('deviceVersion.pp_batch_editing') }}</span>
          </el-button>
          <el-button class="welkin-ghost-button" @click="templateDialogVisible = true">
            <TemplateUpload />
            <span class="margin_l-4">{{ $t('deviceVersion.pp_template_import') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <div class="content-container">
      <div class="batch-edit-container" v-if="isBatchEdit">
        <div class="text">
          <span>{{ $t('deviceVersion.pp_selected') }}</span
          >&nbsp;
          <span class="font-weight-bold">{{ multipleSelection.length }}</span>
        </div>
        <div>
          <el-button class="welkin-primary-button" @click="handleClickOrder">{{ $t('deviceVersion.pp_issue_work_order') }}</el-button>
          <el-button class="welkin-secondary-button" @click="handleBatchEnterBlacklist">{{ $t('deviceVersion.pp_add_to_blacklist') }}</el-button>
          <el-button class="welkin-secondary-button" @click="handleBatchRemoveBlacklist">{{ $t('deviceVersion.pp_remove_from_blacklist') }}</el-button>
          <el-button class="welkin-ghost-button" @click="handleCancelBatchEdit">{{ $t('common.pp_cancel') }}</el-button>
        </div>
      </div>

      <el-table :data="list.slice((pages.current - 1) * pages.size, pages.current * pages.size)" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" v-loading="loading" row-key="device_id" ref="multipleTableRef" @select="handleSelectionChange" @select-all="handleSelectAll">
        <el-table-column type="selection" :reserve-selection="true" width="40" v-if="isBatchEdit" />
        <el-table-column prop="device_id" :label="$t('common.pp_device_id')" min-width="200" show-overflow-tooltip fixed="left" />
        <el-table-column prop="description" :label="$t('common.pp_device_name')" min-width="200" show-overflow-tooltip fixed="left">
          <template #default="{ row }">
            <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" :label="$t('deviceVersion.pp_device_activation')" min-width="90" show-overflow-tooltip key="is_active">
          <template #default="{ row }">
            <span v-if="isEmptyData(row.is_active)">-</span>
            <span v-else>{{ row.is_active ? 'TRUE' : 'FALSE' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="online" :label="$t('deviceVersion.pp_link_status')" min-width="90" show-overflow-tooltip key="online">
          <template #default="{ row }">
            <span v-if="isEmptyData(row.online)">-</span>
            <span v-else :class="row.online ? 'online-state' : 'offline-state'">{{ row.online ? $t('deviceVersion.pp_on_line') : $t('deviceVersion.pp_off_line') }}</span>
          </template>
        </el-table-column>

        <!-- 动态列 -->
        <el-table-column v-for="col in visibleColumns" :key="col.prop" :prop="col.prop" :label="col.label" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row[col.prop] || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column width="100" fixed="right" show-overflow-tooltip>
          <template #header>
            <div class="flex-box flex_j_c-space-between flex_a_i-center">
              <span>{{ $t('common.pp_set_up') }}</span>
              <el-popover v-model:visible="isShowSetting" @before-enter="handleOpenTablePopover" :width="200" trigger="click" :show-arrow="false" placement="bottom-end" popper-class="table-setting-popper">
                <template #reference>
                  <TableSetting class="cursor-pointer" />
                </template>

                <div class="table-setting-container">
                  <div class="flex-box flex_j_c-center margin_b-12">
                    <el-input v-model="columnSearchKey" :placeholder="$t('common.pp_please_search')" clearable class="width-175">
                      <template #prefix>
                        <SearchIcon />
                      </template>
                    </el-input>
                  </div>

                  <div class="setting-content">
                    <el-checkbox-group v-model="tempSelectedColumns" class="column-list">
                      <el-checkbox v-for="col in filteredColumns" :key="col.prop" :label="col.prop" class="column-item">
                        {{ col.label }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>

                  <div class="setting-footer">
                    <el-button class="welkin-secondary-button" @click="handleResetTable">{{ $t('common.pp_reset') }}</el-button>
                    <el-button class="welkin-primary-button" @click="handleConfirmTable">{{ $t('common.pp_confirm') }}</el-button>
                  </div>
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="{ row }">
            <div v-if="hasPermission('function:device-version:edit')">
              <span v-if="isEmptyData(row.in_blacklist)">-</span>
              <span v-else :class="row.in_blacklist ? 'in-blacklist' : 'out-blacklist'" @click="handleSingleBlacklist(row)">{{ row.in_blacklist ? $t('deviceVersion.pp_remove_from_blacklist') : $t('deviceVersion.pp_add_to_blacklist') }}</span>
            </div>
            <div v-else>
              <span v-if="isEmptyData(row.in_blacklist)">-</span>
              <span v-else>{{ row.in_blacklist ? $t('deviceVersion.pp_blacklist') : $t('deviceVersion.pp_out_blacklist') }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination" style="justify-content: space-between">
        <div>
          <el-tooltip placement="top-start" effect="light">
            <template #content>
              <div class="flex-box flex_a_i-center gap_4">
                <InfoIcon />
                <span class="font-size-14 color-26">{{ $t('deviceVersion.pp_jump_tip') }}</span>
              </div>
            </template>
            <el-button class="welkin-secondary-button" @click="handleViewHistory">{{ $t('deviceVersion.pp_send_history') }}</el-button>
          </el-tooltip>
          <el-button class="welkin-secondary-button" @click="handleDownload" :loading="downloading">{{ $t('deviceVersion.pp_download_the_full_table') }}</el-button>
        </div>
        <Page :page="pages" @change="handlePageChange" />
      </div>

      <!-- 加入/移出黑名单弹窗 -->
      <BlacklistDialog v-model:blacklistDialogVisible="blacklistDialogVisible" :enterBlacklist="enterBlacklist" :batchEdit="batchEdit" :deviceList="deviceList" :project="project" @handleConfirmBlacklist="handleConfirmBlacklist" />

      <!-- 选择设备下发工单弹窗 -->
      <OrderDialog v-model:orderDialogVisible="orderDialogVisible" :project="project" :deviceList="multipleSelection" :loading1="loading1" @handleConfirmOrder="handleConfirmOrder" />

      <!-- 模板上传下发工单弹窗 -->
      <TemplateDialog v-model:templateDialogVisible="templateDialogVisible" :project="project" :loading2="loading2" @handleConfirmTemplateOrder="handleConfirmOrder" />

      <!-- 已在期望升级的目标版本内的设备弹窗 -->
      <TargetDialog v-model:targetDialogVisible="targetDialogVisible" :targetList="targetList" :loading3="loading3" @handleConfirmTarget="handleConfirmTarget" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { isActiveOptions, onlineOptions, inBlacklistOptions } from './constant'
import { page } from '~/constvars/page'
import { apiGetFilterOption, apiGetVersionList, apiPostCheckTargetVersion, apiPostOrder } from '~/apis/device-version'
import { ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { hasPermission } from '~/auth'
import { removeNullKeys, clearJson, isEmptyData } from '~/utils'
import axios from 'axios'
import BlacklistDialog from './blacklist-dialog.vue'
import OrderDialog from './order-dialog.vue'
import TemplateDialog from './template-dialog.vue'
import TargetDialog from './target-dialog.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import InfoIcon from '~/assets/svg/info-tip.vue'
import SearchMoreIcon from './icon/search-more.vue'
import ArrowUpIcon from './icon/arrow-up.vue'
import ArrowDownIcon from './icon/arrow-down.vue'
import PopoverClose from './icon/popover-close.vue'
import DeleteRubbish from './icon/delete-rubbish.vue'
import AddIcon from './icon/add-icon.vue'
import BatchEdit from './icon/batch-edit.vue'
import TemplateUpload from './icon/template-upload.vue'
import TableSetting from './icon/table-setting.vue'

const props = defineProps({
  project: {
    type: String,
    default: 'PUS4'
  }
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const multipleTableRef = ref()
const loading = ref(false)
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)
const isShowAdd = ref(false)
const isBatchEdit = ref(false)
const blacklistDialogVisible = ref(false)
const orderDialogVisible = ref(false)
const templateDialogVisible = ref(false)
const targetDialogVisible = ref(false)
const enterBlacklist = ref(false)
const batchEdit = ref(false)
const downloading = ref(false)
const deviceList = ref([] as any)
const targetList = ref([] as any)
const targetParams = ref({} as any)
const list = ref([] as any)
const form = ref({
  is_active: '' as string | boolean,
  online: '' as string | boolean,
  in_blacklist: '' as string | boolean,
  PLM: '',
  PLC: ''
} as any)
const searchForm = ref({} as any)
const pages = ref(cloneDeep(page))
const activeFilterCount = ref(0)
// 从后端获取的筛选项
const filterFields = ref([] as any)
// 编辑时的筛选项数组
const filterItems = ref([{ selectedKey: '', value: '' }])
// 点击确认后的实际筛选项数组
const realFilterItems = ref([{ selectedKey: '', value: '' }])

// 表格设置相关
const isShowSetting = ref(false)
const columnSearchKey = ref('')
const selectedColumns = ref<string[]>(['PLM', 'PLC', 'MATRIX']) // 实际应用到表格的列
const tempSelectedColumns = ref<string[]>(['PLM', 'PLC', 'MATRIX']) // 临时存储编辑状态的列选择
// 固定不可选的列
const FIXED_COLUMNS = ['device_id', 'description', 'is_active', 'online', 'in_blacklist']
// 所有可选的列配置
const allColumns = ref([] as any)
// 过滤后的列选项
const filteredColumns = computed(() => {
  if (!columnSearchKey.value) return allColumns.value
  const searchKey = columnSearchKey.value.toLowerCase()
  return allColumns.value.filter((col: any) => col.label.toLowerCase().includes(searchKey) || col.prop.toLowerCase().includes(searchKey))
})
// 当前可见的列
const visibleColumns = computed(() => allColumns.value.filter((col: any) => selectedColumns.value.includes(col.prop)))
// 重置按钮处理函数
const handleResetTable = () => {
  selectedColumns.value = ['PLM', 'PLC', 'MATRIX']
  tempSelectedColumns.value = ['PLM', 'PLC', 'MATRIX']
  isShowSetting.value = false
}
// 确定按钮处理函数
const handleConfirmTable = () => {
  selectedColumns.value = cloneDeep(tempSelectedColumns.value)
  isShowSetting.value = false
}
const handleOpenTablePopover = () => {
  tempSelectedColumns.value = cloneDeep(selectedColumns.value)
}

// 计算当前选中的数据
const multipleSelection = ref([] as any)
// 处理表格选择变化
const handleSelectionChange = (val: any, row: any) => {
  multipleSelection.value = cloneDeep(val)
}
// 处理全选事件
const handleSelectAll = (val: any) => {
  const currentPageData = list.value.slice((pages.value.current - 1) * pages.value.size, pages.value.current * pages.value.size)
  if (val.find((item: any) => item.device_id === currentPageData[0].device_id)) {
    // 全选
    multipleSelection.value = cloneDeep(list.value)
    list.value.forEach((item: any) => {
      multipleTableRef.value.toggleRowSelection(item, true)
    })
  } else {
    // 取消全选
    multipleSelection.value = []
    multipleTableRef.value.clearSelection()
  }
}
// 分页
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
}
// 取消批量编辑
const handleCancelBatchEdit = () => {
  isBatchEdit.value = false
  multipleSelection.value = []
  multipleTableRef.value && multipleTableRef.value.clearSelection()
}

/**
 * @description: 从后端获取筛选项
 * @return {*}
 */
const getFilterOption = async (init = true) => {
  try {
    const res = await apiGetFilterOption(props.project)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      allColumns.value = res.data
        .map((item: any) => item.key)
        .filter((key: any) => !FIXED_COLUMNS.includes(key))
        .map((key: any) => ({ prop: key, label: key }))
      filterFields.value = res.data || []
      filterFields.value = filterFields.value.filter((item: any) => !['is_active', 'online', 'in_blacklist', 'PLM', 'PLC'].includes(item.key))
      if (init) initForm()
    }
  } catch (error: any) {
    ElMessage.error(error)
  }
}

const handleOpenPopover = () => {
  filterItems.value = cloneDeep(realFilterItems.value)
}

// 获取可用的筛选字段
const getAvailableFields = (currentIndex: number) => {
  const selectedKeys = filterItems.value.map((item, index) => (index !== currentIndex ? item.selectedKey : '')).filter((key) => key)
  return filterFields.value.filter((field: any) => !selectedKeys.includes(field.key))
}

// 获取字段类型
const getFieldType = (key: string) => {
  const field = filterFields.value.find((f: any) => f.key === key)
  return field?.type
}

// 添加筛选项
const addFilterItem = () => {
  filterItems.value.push({ selectedKey: '', value: '' })
}

// 删除筛选项
const removeFilterItem = (index: number) => {
  filterItems.value.splice(index, 1)
}

// 重置筛选
const handleFilterReset = () => {
  filterItems.value = [{ selectedKey: '', value: '' }]
  handleFilterConfirm()
}

// 确认筛选
const handleFilterConfirm = () => {
  realFilterItems.value = cloneDeep(filterItems.value)
  const allPossibleFields = filterFields.value.map((field: any) => field.key)
  allPossibleFields.forEach((field: any) => delete form.value[field])
  const validFilters = filterItems.value
    .filter((item) => item.selectedKey && item.value)
    .reduce((acc, item) => {
      acc[item.selectedKey] = item.value
      return acc
    }, {} as Record<string, any>)

  activeFilterCount.value = Object.keys(validFilters).length
  Object.assign(form.value, validFilters)
  isShowAdd.value = false
}

/**
 * @description: 点击下发工单
 * @return {*}
 */
const handleClickOrder = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t('deviceVersion.pp_please_select_device'))
    return
  }
  orderDialogVisible.value = true
}

/**
 * @description: 确认下发工单
 * @param {*} params
 * @return {*}
 */
const handleConfirmOrder = async (params: any) => {
  loading1.value = true
  loading2.value = true
  targetParams.value = cloneDeep(params)
  try {
    const res = await apiPostCheckTargetVersion(props.project, params)
    if (res.err_code) {
      loading1.value = false
      loading2.value = false
      ElMessage.error(res.message)
    } else {
      if (res.data.ignore_devices && res.data.ignore_devices.length > 0) {
        loading1.value = false
        loading2.value = false
        targetList.value = res.data.ignore_devices
        targetDialogVisible.value = true
      } else {
        // 直接下发
        const result = await apiPostOrder(props.project, params)
        loading1.value = false
        loading2.value = false
        if (result.err_code) {
          ElMessage.error(result.message)
        } else {
          ElMessage.success(t('deviceVersion.pp_issue_successful'))
          orderDialogVisible.value = false
          templateDialogVisible.value = false
          handleCancelBatchEdit()
          getList(false)
        }
      }
    }
  } catch (error) {
    loading1.value = false
    loading2.value = false
  }
}

const handleConfirmTarget = async () => {
  loading3.value = true
  try {
    const result = await apiPostOrder(props.project, targetParams.value)
    loading3.value = false
    if (result.err_code) {
      ElMessage.error(result.message)
    } else {
      ElMessage.success(t('deviceVersion.pp_issue_successful'))
      orderDialogVisible.value = false
      templateDialogVisible.value = false
      targetDialogVisible.value = false
      handleCancelBatchEdit()
      getList(false)
    }
  } catch (error) {
    loading3.value = false
  }
}

/**
 * @description: 批量加入黑名单
 * @return {*}
 */
const handleBatchEnterBlacklist = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t('deviceVersion.pp_please_select_device'))
    return
  }
  blacklistDialogVisible.value = true
  enterBlacklist.value = true
  batchEdit.value = true
  deviceList.value = multipleSelection.value
}

/**
 * @description: 批量移出黑名单
 * @return {*}
 */
const handleBatchRemoveBlacklist = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t('deviceVersion.pp_please_select_device'))
    return
  }
  blacklistDialogVisible.value = true
  enterBlacklist.value = false
  batchEdit.value = true
  deviceList.value = multipleSelection.value
}

/**
 * @description: 单个加入/移出黑名单
 * @param {*} row
 * @return {*}
 */
const handleSingleBlacklist = (row: any) => {
  blacklistDialogVisible.value = true
  enterBlacklist.value = !row.in_blacklist
  batchEdit.value = false
  deviceList.value = [row]
}

/**
 * @description: 完成加入/移出黑名单
 * @return {*}
 */
const handleConfirmBlacklist = () => {
  handleCancelBatchEdit()
  getList(false)
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  handleFilterReset()
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = cloneDeep(form.value)
  getList()
}

/**
 * @description: 查看发送历史
 * @return {*}
 */
const handleViewHistory = () => {
  window.open('https://powerpro.nio.com/bohu/tabs/magic_technical_task_tabs')
}

/**
 * @description: 下载
 * @return {*}
 */
const handleDownload = async () => {
  if (downloading.value) return
  // let formData = cloneDeep(searchForm.value) as any
  // formData.download = true
  let formData = { download: true }
  downloading.value = true
  try {
    const response = await axios({
      url: `/web/welkin/device/v1/version/${props.project}/list`,
      method: 'POST',
      data: formData,
      responseType: 'blob' // 重要：设置响应类型为 blob
    })

    // 处理文件名
    let filename = 'download.csv'
    const disposition = response.headers['content-disposition']
    if (disposition) {
      // 处理 filename*=utf-8'' 格式
      const filenameRegex = /filename\*=utf-8''([\w-\.]+)/i
      const matches = disposition.match(filenameRegex)

      if (matches?.[1]) {
        filename = decodeURIComponent(matches[1])
      } else {
        // 回退到普通 filename= 格式
        const defaultMatch = /filename=([\w-\.]+)/i.exec(disposition)
        if (defaultMatch?.[1]) {
          filename = defaultMatch[1]
        }
      }
    }
    // 创建下载
    const blob = new Blob([response.data], {
      type: 'text/csv;charset=utf-8'
    })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    ElMessage.success(t('deviceVersion.pp_download_successful'))
  } catch (error: any) {
    ElMessage.error(error.message || t('deviceVersion.pp_download_failed'))
  } finally {
    downloading.value = false
  }
}

/**
 * @description: 获取数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: 'list', project: props.project, ...removeNullKeys(formData) }
    })
  }
  formData.download = false
  loading.value = true
  multipleSelection.value = []
  multipleTableRef.value && multipleTableRef.value.clearSelection()
  try {
    const res = await apiGetVersionList(props.project, { ...removeNullKeys(formData) })
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data || []
      pages.value.total = res.data ? res.data.length : 0
    }
  } catch (error: any) {
    loading.value = false
  }
}

watch(
  () => props.project,
  (newVal, oldVal) => {
    handleReset()
    getFilterOption(false)
  }
)

// 固定参数
const FIXED_FIELDS = {
  is_active: 'boolean',
  online: 'boolean',
  in_blacklist: 'boolean',
  PLM: 'string',
  PLC: 'string'
} as any

// 定义需要排除的参数
const EXCLUDED_PARAMS = ['tab', 'project']

// 初始化表单
const initForm = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const formData: Record<string, any> = {}

  // 遍历 URL 参数
  for (const [key, value] of urlParams.entries()) {
    // 跳过被排除的参数
    if (EXCLUDED_PARAMS.includes(key)) continue

    // 处理固定字段
    if (key in FIXED_FIELDS) {
      formData[key] = FIXED_FIELDS[key] === 'boolean' ? value === 'true' : value
      continue
    }

    // 处理动态筛选字段
    if (filterFields.value.some((field: any) => field.key === key)) {
      formData[key] = value
    }
  }

  form.value = formData

  // 初始化筛选项
  const activeFilters = Object.entries(formData)
    .filter(([key, value]) => {
      // 排除固定字段和空值
      return !Object.keys(FIXED_FIELDS).includes(key) && value !== null && value !== undefined
    })
    .map(([key, value]) => ({
      selectedKey: key,
      value: String(value)
    }))

  // 如果有动态筛选项，则初始化它们
  if (activeFilters.length > 0) {
    realFilterItems.value = activeFilters
    filterItems.value = cloneDeep(activeFilters)
    activeFilterCount.value = activeFilters.length
  } else {
    realFilterItems.value = [{ selectedKey: '', value: '' }]
    filterItems.value = [{ selectedKey: '', value: '' }]
    activeFilterCount.value = 0
  }

  let initParams: any = route.query
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  searchForm.value = cloneDeep(form.value)
  getList(false)
}

onBeforeMount(() => {
  getFilterOption()
})
</script>

<style lang="scss" scoped>
.version-list-container {
  :deep(.search-form) {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px 24px;
    margin-bottom: 20px;
    .el-form-item {
      margin: 0;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
      }
      .el-form-item__label-wrap {
        margin: 0 !important;
      }
      .active-filter-count {
        height: 24px;
        padding: 0px 8px;
        margin-right: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 2px;
        background-color: #f0f0f0;
      }
    }
  }
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-right>.el-form-item__label:after {
        color: #FD434A;
        font-weight: 500;
      }
    }
  }
  %state {
    height: 24px;
    border-radius: 2px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    font-size: 13px;
    line-height: 20px;
  }
  .online-state {
    @extend %state;
    color: #2f9c74;
    background-color: #e8fcea;
  }
  .offline-state {
    @extend %state;
    color: #595959;
    background-color: rgba(140, 140, 140, 0.1);
  }
  .in-blacklist {
    font-weight: 420;
    color: #595959;
    cursor: pointer;
  }
  .out-blacklist {
    font-weight: 420;
    color: #01a0ac;
    cursor: pointer;
  }
  .batch-edit-container {
    height: 48px;
    padding: 8px 24px;
    background-color: rgba(229, 249, 249, 0.5);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .text {
      font-size: 14px;
      line-height: 22px;
      color: #303133;
    }
  }
}
</style>
