<template>
  <div class="order-dialog-container">
    <el-dialog v-model="templateDialogVisible" :title="$t('deviceVersion.pp_template_title')" @open="handleOpen" @close="handleClose" width="513px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="createForm" ref="templateFormRef" label-position="top" :rules="rules" require-asterisk-position="right">
        <el-form-item :label="$t('deviceVersion.pp_target_version')" prop="target_version">
          <el-select v-model="createForm.target_version" filterable :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option v-for="item in versionOptions" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.pp_remark')" prop="worksheet_description">
          <el-input v-model="createForm.worksheet_description" type="textarea" autosize clearable :placeholder="$t('common.pp_please_input')" />
          <div class="font-size-12 line-height-18 color-8c margin_t-2">工单描述：{{ worksheetDescription }}</div>
        </el-form-item>
        <el-form-item :style="{ marginBottom: file && file.length > 0 ? '0px' : '16px' }">
          <div class="width-full flex-box flex_j_c-space-between flex_a_i-center margin_b-12">
            <span class="color-59 font-size-14 line-height-22">{{ $t('deviceVersion.pp_import_file') }}</span>
            <el-button class="welkin-secondary-button" @click="handleDownloadTemplate" style="height: 24px">{{ $t('deviceVersion.pp_download_template') }}</el-button>
          </div>
          <el-upload v-model:file-list="file" :on-change="handleChangeFile" drag accept=".csv" ref="uploadRef" :limit="1" :on-exceed="handleExceed" class="width-full" action="#" :auto-upload="false">
            <div class="el-upload__text">
              <UploadIcon />
              <span class="margin_l-6"
                >{{ $t('stuckAnalysis.pp_please_upload') }} <em>{{ $t('stuckAnalysis.pp_csv') }}</em></span
              >
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="info-box" v-if="csvList && csvList.length > 0">
        <WarningIcon class="margin_t-3" style="flex-shrink: 0" />
        <span class="font-size-14 line-height-22 color-59">
          {{ $t('deviceVersion.pp_template_dialog_tip1') }} <span class="color-26 font-weight-bold">{{ blackInfo.total_count }}</span> {{ $t('deviceVersion.pp_order_dialog_tip2') }} <span class="color-26 font-weight-bold">{{ blackInfo.blacklist_count }}</span> {{ $t('deviceVersion.pp_order_dialog_tip3') }} <span class="color-26 font-weight-bold">{{ blackInfo.total_count - blackInfo.blacklist_count }}</span> {{ $t('deviceVersion.pp_order_dialog_tip4') }}
        </span>
      </div>
      <div class="flex-box flex_j_c-flex-end flex_a_i-center">
        <el-button class="welkin-text-button" @click="handleCancel">{{ $t('common.pp_cancel') }}</el-button>
        <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="loading2" @click="handleConfirm">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { apiGetDevices } from '~/apis/home'
import { apiGetVersionOptions, apiPostBlackNumber } from '~/apis/device-version'
import { ElMessage, genFileId } from 'element-plus'
import { confirmBatchUploadDevice } from '~/utils'
import WarningIcon from './icon/warning-icon.vue'
import UploadIcon from '~/views/run-list/component/icon/upload-icon.vue'

const props = defineProps({
  templateDialogVisible: Boolean,
  project: String,
  loading2: Boolean
})

const emits = defineEmits(['update:templateDialogVisible', 'handleConfirmTemplateOrder'])

const { t } = useI18n()
const uploadRef = ref()
const templateFormRef = ref()
const createForm = ref({
  target_version: '',
  worksheet_description: ''
})
const rules = ref({
  target_version: [{ required: true, message: t('deviceVersion.pp_error_tip'), trigger: 'change' }]
})
const file = ref([] as any)
const csvList = ref([] as any)
const versionOptions = ref([] as any)
const deviceOptions = ref([] as any)
const blackInfo = ref({
  total_count: 0,
  blacklist_count: 0
} as any)
const worksheetDescription = computed(() => {
  return `该换电站需要升级至${createForm.value.target_version || 'xx'}，请注意并尽快升级。` + createForm.value.worksheet_description
})

/**
 * @description: 上传文件
 * @param {*} uploadFile
 * @return {*}
 */
const handleChangeFile = async (uploadFile: any) => {
  const params = { file: uploadFile.raw }
  csvList.value = await confirmBatchUploadDevice(params, deviceOptions.value, false)
  if (csvList.value && csvList.value.length > 0) {
    const res = await apiPostBlackNumber(props.project, { device_list: csvList.value })
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      blackInfo.value = res.data
    }
  }
}

/**
 * @description: 下载模板
 * @return {*}
 */
const handleDownloadTemplate = () => {
  window.open('https://cdn-welkin-public.nio.com/welkin/2024/07/30/template_device_id_csv.csv')
}

/**
 * @description: 覆盖上一个文件
 * @param {*} files
 * @return {*}
 */
const handleExceed = (files: any) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
}

/**
 * @description: 打开弹窗
 * @return {*}
 */
const handleOpen = () => {
  getVersionList()
  getDeviceOptions()
}

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleClose = () => {
  file.value = []
  csvList.value = null
  createForm.value = { target_version: '', worksheet_description: '' }
  templateFormRef.value && templateFormRef.value.resetFields()
  emits('update:templateDialogVisible', false)
}

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:templateDialogVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirm = async () => {
  if (!templateFormRef.value) return
  await templateFormRef.value.validate(async (valid: any, fields: any) => {
    if (valid) {
      if (!csvList.value || csvList.value.length === 0) {
        ElMessage.warning(t('deviceVersion.pp_please_select_device'))
        return
      }
      const params = {
        device_list: csvList.value,
        target_version: createForm.value.target_version,
        worksheet_description: worksheetDescription.value
      }
      emits('handleConfirmTemplateOrder', params)
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 获取版本号列表
 * @return {*}
 */
const getVersionList = async () => {
  try {
    const res = await apiGetVersionOptions(props.project)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      versionOptions.value = res.data || []
    }
  } catch (error: any) {
    versionOptions.value = []
  }
}

/**
 * @description: 获取设备列表，用于校验上传文件中的设备
 * @return {*}
 */
const getDeviceOptions = async () => {
  const params = { project: props.project }
  const res = await apiGetDevices(params)
  deviceOptions.value = res.data
}
</script>

<style lang="scss" scoped>
.order-dialog-container {
  :deep(.el-dialog) {
    .el-form-item {
      margin-bottom: 16px;
      .el-form-item__label {
        color: #595959;
        margin-bottom: 4px;
      }
      .el-textarea__inner {
        color: #262626;
      }
    }
    .el-upload-dragger {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px dashed #d9d9d9;
      height: 96px;
      border-radius: 4px;
      .el-upload__text {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #262626;
      }
      .el-upload__text em {
        color: #01a0ac;
      }
    }
    .el-upload-list__item-file-name {
      color: #595959;
      font-weight: normal;
    }
    .el-upload-list {
      margin: 0;
    }
    .info-box {
      width: 100%;
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 8px 16px;
      margin-bottom: 16px;
      border-radius: 4px;
      border: 1px solid rgba(253, 140, 8, 0.5);
      background-color: rgba(253, 140, 8, 0.05);
    }
  }
}
</style>
