<template>
  <div class="device-version-container" :class="activeTab === 'dashboard' ? 'version-dashboard' : 'version-list'">
    <div class="header-section">
      <div class="font-size-20 font-weight-bold margin_b-12 color-1f">{{ $t('menu.pp_device_version') }}</div>
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane :label="$t('deviceVersion.pp_version_dashboard')" name="dashboard"></el-tab-pane>
        <el-tab-pane :label="$t('deviceVersion.pp_version_list')" name="list"></el-tab-pane>
      </el-tabs>
      <el-radio-group v-model="project" class="radio-group">
        <el-radio label="PowerSwap2">{{ $t('common.pp_pss2') }}</el-radio>
        <el-radio label="PUS3">{{ $t('common.pp_pss3') }}</el-radio>
        <el-radio label="PUS4">{{ $t('common.pp_pss4') }}</el-radio>
      </el-radio-group>
    </div>

    <div class="content-section">
      <VersionDashboard :project="project" v-if="activeTab == 'dashboard'" />
      <VersionList :project="project" v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import VersionDashboard from './components/version-dashboard.vue'
import VersionList from './components/version-list.vue'

const route = useRoute()
const router = useRouter()
const activeTab = ref('dashboard' as any)
const project = ref('PUS4' as any)

/**
 * @description: 切换Tab
 * @return {*}
 */
const handleTabChange = () => {
  router.push({
    path: location.pathname,
    query: { tab: activeTab.value, project: project.value }
  })
}

onBeforeMount(() => {
  activeTab.value = route.query.tab || 'dashboard'
  project.value = route.query.project || 'PUS4'
})
</script>

<style lang="scss" scoped>
.device-version-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  .header-section {
    position: relative;
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
    :deep(.radio-group) {
      position: absolute;
      right: 24px;
      bottom: 3px;
    }
  }
  .content-section {
    :deep(.el-dialog__title) {
      color: #1f1f1f;
    }
    :deep(.el-form-item__label) {
      color: #595959;
    }
    :deep(.el-dialog__headerbtn .el-dialog__close) {
      font-size: 20px;
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
  }
}
.version-dashboard {
  .header-section {
    padding: 24px;
    padding-bottom: 0;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
  }
  .content-section {
    flex: 1;
    padding: 20px 24px 24px;
    background: #f8f8f8;
  }
}
.version-list {
  .header-section {
    padding: 24px;
    padding-bottom: 0;
    background: linear-gradient(180deg, #f6fefe, #fff);
  }
  .content-section {
    flex: 1;
    padding: 20px 24px 24px;
    background: #fff;
  }
}
</style>