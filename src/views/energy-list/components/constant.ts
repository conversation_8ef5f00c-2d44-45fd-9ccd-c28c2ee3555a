import { i18n } from '~/i18n'

export const projectOptions = [
  {
    label: 'common.pp_pss2',
    value: 'PowerSwap2'
  },
  {
    label: 'common.pp_pss3',
    value: 'PUS3'
  }
]

export const projectMap = {
  PowerSwap: { label: 'menu.pp_swap_station1', color: '#FFC031' },
  PowerSwap2: { label: 'menu.pp_swap_station2', color: '#67C23A' },
  PUS3: { label: 'menu.pp_swap_station3', color: '#00BEBE' },
  PUS4: { label: 'menu.pp_swap_station4', color: '#21819D' }
} as any

export const pieOptionMock = {
  color: ['#C6EFEF', '#DCF2F3', '#01A0AC', '#03BEC9', '#39BEAE', '#72D896', '#55D7DB', '#8DE0F8', '#B5EEFF'],
  animation: false,
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: function (params: any) {
      return params.marker + '  ' + params.name + '<br />' + params.value + '<br />' + '(' + params.percent + '%)'
    },
    textStyle: {
      color: '#262626',
      align: 'center'
    }
  },
  series: [
    {
      type: 'pie',
      center: ['50%', '57%'],
      radius: ['40%', '70%'],
      label: {
        show: true,
        color: '#8C8C8C',
        formatter: function (params: any) {
          return params.name + '  ' + params.value
        }
      },
      labelLine: {
        length2: 0
      },
      itemStyle: {
        borderRadius: 2,
        borderColor: '#fff',
        borderWidth: 2
      },
      data: []
    }
  ]
}

export const multilineOptionMock = {
  animation: false,
  color: ['#7FD9DE', '#00BEBE'],
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    padding: 0,
    right: 0,
    top: 3,
    textStyle: {
      color: '#595959'
    },
    itemWidth: 14,
    itemHeight: 8,
    itemGap: 112,
    data: [
      { name: i18n.global.t('stationManagement.pp_energy_proportion1'), icon: 'path://M11.9691 3.50008C11.7231 1.52689 10.0398 0 8 0C5.96016 0 4.27695 1.52689 4.03094 3.50008H0.5C0.223858 3.50008 0 3.72394 0 4.00008C0 4.27622 0.223858 4.50008 0.5 4.50008H4.03096C4.27704 6.4732 5.96022 8 8 8C10.0398 8 11.723 6.4732 11.969 4.50008H15.5C15.7761 4.50008 16 4.27622 16 4.00008C16 3.72394 15.7761 3.50008 15.5 3.50008H11.9691ZM11 4C11 5.65685 9.65685 7 8 7C6.34315 7 5 5.65685 5 4C5 2.34315 6.34315 1 8 1C9.65685 1 11 2.34315 11 4Z' },
      { name: i18n.global.t('stationManagement.pp_order_volume'), icon: 'circle' }
    ]
  },
  grid: {
    top: '40',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    data: [],
    axisPointer: {
      type: 'shadow'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    }
  },
  yAxis: [
    {
      type: 'value',
      alignTicks: true,
      min: (value: any) => (value.min < 88 ? Math.floor(value.min) : 86),
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}%'
      }
    },
    {
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    }
  ],
  series: [
    {
      name: i18n.global.t('stationManagement.pp_energy_proportion1'),
      type: 'line',
      tooltip: {
        valueFormatter: (value: any) => value + '%'
      },
      data: [],
      lineStyle: {
        width: 1.5
      },
      markLine: {
        silent: true,
        symbol: 'none',
        label: {
          position: 'insideStartTop'
        },
        data: [
          {
            yAxis: 88,
            lineStyle: {
              type: 'dashed',
              color: '#7FD9DE',
              width: 1
            },
            label: {
              formatter: '{c}' + '%',
              color: '#7FD9DE',
              fontWeight: 'bolder'
            }
          }
        ]
      },
      markArea: {
        silent: true,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(127, 217, 222, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0)'
              }
            ],
            global: false
          }
        },
        data: [
          [
            {
              x: 'dataMin',
              yAxis: 0
            },
            {
              x: 'dataMax',
              yAxis: 88
            }
          ]
        ]
      }
    },
    {
      name: i18n.global.t('stationManagement.pp_order_volume'),
      type: 'bar',
      barMaxWidth: 24,
      yAxisIndex: 1,
      data: []
    }
  ]
} as any
