<!--
 * @Author: zhenxing.chen
 * @Date: 2023-02-08 14:44:18
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-04-12 14:48:23
 * @Email: <EMAIL>
 * @Description: 版本号管理
-->
<template>
  <div class="version-management-container">
    <div class="breadcrumb-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ 'OWL' }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ '版本号管理' }}
        </el-breadcrumb-item>
      </el-breadcrumb>
      <div class="card-title">{{ '版本号管理' }}</div>
      <div>
        <el-tabs
          v-model="activeVersionTab"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane
            v-for="(item, index) in tabList"
            :key="index"
            :label="item.label"
            :name="item.name"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div class="version-search-form">
      <div class="flex version-form-item">
        <span class="label-text">置顶算法:</span>
        <el-select
          @change="algorithmChangeHandle"
          v-model="datas.algorithm_name"
          placeholder="请选择"
          :multiple-limit="3"
          multiple
          style="width: 260px; margin-right: 15px"
          filterable
          clearable
        >
          <el-option
            v-for="(option, index) in datas.algorithmNameOptions"
            :key="index"
            :label="option"
            :value="option"
          ></el-option>
        </el-select>
        <el-button
          class="welkin-primary-button"
          v-if="datas.hasPermission"
          @click="
            dialogVisible = true;
            dialogForm.publish_ts = new Date().getTime();
            dialogForm.big_version = '';
          "
          >新增发行版</el-button
        >
      </div>
      <div class="version-table-container">
        <el-table
          @row-click="tableRow"
          v-loading="datas.loading"
          :data="datas.tableData"
        >
          <el-table-column prop="version" label="大版本" width="100" />
          <el-table-column label="更新时间/数量" width="150">
            <template #default="{ row }">
              <div :class="datas.hasPermission ? 'can-edit' : ''">
                <span class="update-time">{{
                  formatLocaleDay(row.publish_ts)
                }}</span>
                <span class="update-count"> {{ row.update_count }} </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in datas.tableHead"
            :key="item + index"
            :label="item"
            width="100"
          >
            <template #default="{ row }">
              <div
                v-if="row[item + '_value']"
                style="display: flex; align-items: bottom; line-height: 18px"
                :class="{
                  'can-edit': datas.hasPermission,
                  underline: row[item + '_shadow'],
                }"
              >
                <span
                  v-for="(spanItem, index) in row[item + '_value']"
                  :class="
                    row[item + '_update_point'][index] == '1'
                      ? 'algorithm-update-count'
                      : ''
                  "
                  :key="spanItem + index"
                >
                  {{ spanItem ? spanItem : '-' }}
                  <span v-if="index + 1 != row[item + '_value'].length">
                    .
                  </span>
                </span>

                <span
                  v-if="row[item + '_test_report'] == 1 && datas.testReport"
                  style="padding-left: 3px"
                  @click="handleTest(row)"
                >
                  <el-icon><Document /></el-icon>
                </span>

                <span
                  v-if="row[item + '_test_report'] == 2"
                  style="padding-left: 3px; color: #00bebe"
                  @click.stop="handleTestUrl(item, row)"
                >
                  <el-icon><Document /></el-icon>
                </span>
              </div>
              <div :class="datas.hasPermission ? 'can-edit' : ''" v-else>
                {{ '-' }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          v-model:currentPage="datas.currentPage"
          v-model:page-size="datas.currentSize"
          :disabled="false"
          :layout="pagination.layout"
          :total="datas.totalNum"
          :page-sizes="pagination.pageSizes"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog
      v-model="dialogVisible"
      align-center
      title="新增发行版"
      width="30%"
      draggable
    >
      <el-form
        ref="addFormRef"
        label-position="left"
        :rules="rules"
        :model="dialogForm"
      >
        <el-form-item label="发布时间" prop="publish_ts" :label-width="80">
          <el-date-picker
            v-model="dialogForm.publish_ts"
            style="width: 100%"
            type="date"
            placeholder="请选择时间"
          />
        </el-form-item>
        <el-form-item label="大版本号" prop="big_version" :label-width="80">
          <el-input
            v-model="dialogForm.big_version"
            placeholder="请输入版本号"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" class="welkin-text-button">取消</el-button>
          <el-button
            class="welkin-primary-button"
            style="margin-left: 4px"
            :loading="datas.buttonLoading"
            @click="addReleaseVersion(addFormRef)"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="editAlgorithmVisible"
      align-center
      :title="datas.editAlgorithmTitle"
      width="30%"
      @close="datas.showReportUrl = false"
      draggable
    >
      <el-form
        ref="editFormRef"
        label-position="left"
        :rules="rules"
        :model="dialogForm"
      >
        <el-form-item
          label="版本号"
          :disabled="datas.canEditAlgorithm"
          prop="version"
          :label-width="80"
        >
          <el-input
            v-model="dialogForm.version"
            :disabled="datas.canEditAlgorithm"
            placeholder="请输入版本号"
          />
        </el-form-item>

        <el-form-item
          label="影子模式"
          prop="shadow"
          v-if="!datas.showReportUrl"
          :label-width="80"
        >
          <el-select
            v-model="dialogForm.shadow"
            placeholder="请选择"
            :limit="3"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="(option, index) in datas.algorithShadowOptions"
              :key="index"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="测试报告"
          v-if="datas.showReportUrl"
          prop="test_report_url"
          :label-width="80"
        >
          <el-input
            v-model="dialogForm.test_report_url"
            :disabled="datas.canEditAlgorithm"
            placeholder="请输入测试报告链接"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editAlgorithmVisible = false" class="welkin-text-button">取消</el-button>
          <el-button
            class="welkin-primary-button"
            style="margin-left: 4px"
            :loading="datas.buttonLoading"
            @click="editAlgorithmHandle(editFormRef)"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="editRelaseVisible"
      align-center
      :title="datas.editeRelaseTitle"
      width="710px"
      draggable
    >
      <el-form
        ref="releaseFormRef"
        inline
        label-position="left"
        :rules="rules"
        :model="dialogForm"
      >
        <el-form-item label="发布时间" prop="publish_ts" :label-width="80">
          <el-date-picker
            v-model="dialogForm.publish_ts"
            type="date"
            placeholder="请选择时间"
          />
        </el-form-item>
        <el-form-item label="大版本号" prop="big_version" :label-width="80">
          <el-input
            style="width: 220px"
            v-model="dialogForm.big_version"
            placeholder="请输入大版本号"
          />
        </el-form-item>

        <el-form-item label="算法名称" prop="algorithm_name" :label-width="80">
          <el-input
            style="width: 220px"
            v-model="dialogForm.algorithm_name"
            placeholder="请输入算法名称"
          />
          <span
            @click="addAlgorithm"
            style="margin-left: 10px; padding-top: 3px; cursor: pointer"
          >
            <el-icon><Plus /></el-icon>
          </span>
        </el-form-item>
      </el-form>

      <div class="algorithm-list">
        <div
          class="item flex"
          :key="index + list.algorithm_name"
          v-for="(list, index) in dialogForm.models"
        >
          <el-input
            style="width: 220px"
            disabled
            v-model="list.algorithm_name"
            placeholder=""
          />
          <span
            @click="delAlgorithm(index)"
            style="margin-left: 10px; padding-top: 8px; cursor: pointer"
          >
            <el-icon><Minus /></el-icon>
          </span>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editRelaseVisible = false" class="welkin-text-button">取消</el-button>
          <el-button
            class="welkin-primary-button"
            style="margin-left: 4px"
            :loading="datas.buttonLoading"
            @click="submitForm(releaseFormRef)"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Plus, Minus, Document } from '@element-plus/icons-vue';
import {
  apiListFetch,
  apiTableHeadFetch,
  apiTestReportFetch,
  apiEditVersionFetch,
  apiEditAlgorithmFetch,
  apiPublishVersionFetch,
} from '~/apis/owl';
import { formatLocaleDay } from '~/utils';
import { hasPermission } from '~/auth';
import { pagination } from '~/constvars';

const activeVersionTab = ref('PowerSwap2');
const dialogVisible = ref(false);
const editAlgorithmVisible = ref(false);
const editRelaseVisible = ref(false);
const addFormRef = ref<FormInstance>();
const editFormRef = ref<FormInstance>();
const releaseFormRef = ref<FormInstance>();
const tabList = reactive([
  {
    name: 'PowerSwap2',
    label: '换电站2.0',
  },
  {
    name: 'PUS3',
    label: '换电站3.0',
  },
]);
const datas = reactive({
  algorithmNameOptions: [] as any,
  editeRelaseTitle: '',
  editAlgorithmTitle: '',
  showReportUrl: false,
  canEditAlgorithm: true,
  algorithShadowOptions: [
    {
      label: '是',
      value: true,
    },
    {
      label: '否',
      value: false,
    },
  ],
  tempRow: null,
  curAlgorithm: null,
  algorithm_name: [],
  currentPage: 1,
  currentSize: 10,
  totalNum: 0,
  tableHead: [],
  tableData: [],
  loading: false,
  buttonLoading: false,
  hasPermission: hasPermission('version-management:add-release-version'),
  testReport: hasPermission('version-management:test-report-icon'),
});

/**
 * @description: 校验版本
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @return {*}
 */
const checkVersion = (rule: any, value: any, callback: Function) => {
  if (!value) {
    callback('版本不能为空');
  }
  if (/^\d+\.\d+\.\d+$/.test(value)) {
    callback();
  } else {
    callback('请输入正确的版本号');
  }
};
/**
 * @description: 表单弹窗
 * @return {*}
 */
const dialogForm = reactive({
  publish_ts: '' as any,
  version: '',
  big_version: '',
  algorithm_name: '',
  models: [] as any,
  shadow: false,
  test_report_url: '',
});

const rules = reactive<FormRules>({
  publish_ts: [
    {
      required: true,
      message: '请选择时间',
      trigger: 'change',
    },
  ],
  big_version: [
    {
      required: true,
      message: '请选择大版本',
      trigger: 'change',
    },
  ],
  test_report_url: [
    {
      required: true,
      message: '请填写测试报告',
      trigger: 'change',
    },
  ],
  version: [
    {
      required: true,
      trigger: 'change',
      validator: checkVersion,
    },
  ],
  algorithm_name: [
    {
      required: false,
      message: '',
      trigger: 'change',
    },
  ],
  models: [
    {
      required: false,
      message: '',
      trigger: 'change',
    },
  ],
});
/**
 * @description: 置顶算法select监听事件
 * @param {*} val
 * @return {*}
 */
const algorithmChangeHandle = (val: any) => {
  getTableList(activeVersionTab.value);
};

/**
 * @description: tab点击事件回调
 * @return {*}
 */
const handleClick = (curTab: any) => {
  const tabsVal: string = tabList[curTab.index].name;
  datas.currentSize = 10;
  datas.currentPage = 1;
  getTableList(tabsVal);
};

/**
 * @description: 分页大小点击回调
 * @return {*}
 */
const handleSizeChange = (size: number) => {
  getTableList(activeVersionTab.value);
};

/**
 * @description: 分页点击当前页回调
 * @return {*}
 */
const handleCurrentChange = (cur: number) => {
  getTableList(activeVersionTab.value);
};

const handleTest = (row: any) => {
  datas.canEditAlgorithm = !row.can_edit;
  dialogForm.test_report_url = '';
  datas.showReportUrl = true;
};

/**
 * @description: 打开测试报告
 * @param {*} item
 * @param {*} row
 * @return {*}
 */
const handleTestUrl = (item: any, row: any) => {
  const url = row[item + '_test_report_url'];
  datas.canEditAlgorithm = false;
  if (url && !datas.testReport) {
    window.open(url, '_target');
  } else {
    datas.tempRow = row;
    if (row.models) {
      datas.curAlgorithm = row.models.filter((itemModel: any) => {
        return itemModel.algorithm_name == item;
      });
    }
    datas.editAlgorithmTitle = `编辑算法 - ${item}`;
    editAlgorithmVisible.value = true;
    datas.showReportUrl = true;
    dialogForm.version = row[item + '_version'];
    dialogForm.test_report_url = url;
    datas.canEditAlgorithm = !row.can_edit;
  }
};

/**
 * @description: 表格单元点击
 * @return {*}
 */
const tableRow = (row: any, column: any) => {
  if (!row.can_edit) {
    ElMessage.error('该版本已发布，无法编辑');
    return;
  }
  const columnIndex = column.getColumnIndex();
  datas.tempRow = row;

  if (row.models) {
    datas.curAlgorithm = row.models.filter((item: any) => {
      return item.algorithm_name == column.label;
    });
  }

  if (!datas.hasPermission) {
    return;
  }
  if (columnIndex < 2) {
    editRelaseVisible.value = true;
    dialogForm.big_version = row.version;
    dialogForm.publish_ts = row.publish_ts;
    datas.editeRelaseTitle = `编辑发行版 - ${row.version}`;
  } else {
    editAlgorithmVisible.value = true;
    const cur: any = datas.curAlgorithm;
    dialogForm.version = cur[0].algorithm_version;
    dialogForm.shadow = cur[0].shadow;
    datas.canEditAlgorithm = !row.can_edit;
    datas.editAlgorithmTitle = `编辑算法 - ${column.label}`;
  }
};

/**
 * @description: 编辑算法
 * @return {*}
 */
const editAlgorithmHandle = (formEl: any) => {
  formEl.validate(async (valid: any) => {
    const project = activeVersionTab.value;
    const cur: any = datas.curAlgorithm;
    const curRow: any = datas.tempRow;
    const version_id = curRow.publish_version_id;
    const algorithm_id = cur[0].algorithm_id;
    const params = {
      algorithm_version: dialogForm.version,
      algorithm_name: cur[0].algorithm_name,
      shadow: dialogForm.shadow,
    };
    if (valid) {
      datas.buttonLoading = true;
      try {
        let res = null;
        if (datas.showReportUrl) {
          const rePeportParmas = {
            test_report_url: dialogForm.test_report_url,
          };
          res = await apiTestReportFetch(project, algorithm_id, rePeportParmas);
        } else {
          res = await apiEditAlgorithmFetch(
            project,
            version_id,
            algorithm_id,
            params
          );
        }
        datas.showReportUrl = false;
        dialogForm.test_report_url = '';
        const { err_code, message } = res;
        datas.buttonLoading = false;
        editAlgorithmVisible.value = false;
        if (!err_code) {
          getTableList(activeVersionTab.value);
        } else {
          ElMessage.error(message);
        }
      } catch (error) {
        datas.buttonLoading = false;
        editAlgorithmVisible.value = false;
      }
    }
  });
};
/**
 * @description: 确定提交发行版
 * @param {*} formEl
 * @return {*}
 */
const addReleaseVersion = (formEl: any) => {
  formEl.validate(async (valid: any) => {
    const project = activeVersionTab.value;
    const params = {
      publish_ts: new Date(dialogForm.publish_ts).getTime(),
      version: dialogForm.big_version,
    };
    if (valid) {
      datas.buttonLoading = true;
      try {
        const res = await apiPublishVersionFetch(project, params);
        const { err_code, message } = res;
        datas.buttonLoading = false;
        dialogVisible.value = false;
        if (!err_code) {
          getTableList(activeVersionTab.value);
        } else {
          ElMessage.error(message);
        }
      } catch (error) {
        datas.buttonLoading = false;
        dialogVisible.value = false;
      }
    }
  });
};

/**
 * @description: 编辑发行版增加算法
 * @return {*}
 */
const addAlgorithm = () => {
  const name = dialogForm.algorithm_name;
  if (!name) {
    return;
  }
  const obj = {
    algorithm_name: name,
    algorithm_version: '1.0.0',
    shadow: false,
  };
  dialogForm.models.push(obj);
  dialogForm.algorithm_name = '';
};
/**
 * @description: 编辑发行版删除算法
 * @return {*}
 */
const delAlgorithm = (index: number) => {
  dialogForm.models.splice(index, 1);
};
//提交发行版
const submitForm = (formEl: any) => {
  if (!formEl) return;
  formEl.validate(async (valid: any) => {
    if (valid) {
      const project = activeVersionTab.value;
      const curRow: any = datas.tempRow;
      const versionId = curRow.publish_version_id;
      const params = {
        publish_ts: new Date(dialogForm.publish_ts).getTime(),
        version: dialogForm.big_version,
        models: dialogForm.models,
      };
      if (dialogForm.algorithm_name) {
        const inputValObj = {
          algorithm_name: dialogForm.algorithm_name,
          algorithm_version: '1.0.0',
          shadow: false,
        };
        params.models.push(inputValObj);
      }
      try {
        datas.buttonLoading = true;
        const res = await apiEditVersionFetch(project, versionId, params);
        datas.buttonLoading = false;
        const { err_code, message } = res;
        editRelaseVisible.value = false;
        if (!err_code) {
          getTableList(activeVersionTab.value);
          dialogForm.algorithm_name = '';
          dialogForm.models = [];
        } else {
          dialogForm.models = [];
          getTableList(activeVersionTab.value);
          ElMessage.error(message);
        }
      } catch (error) {
        datas.buttonLoading = false;
      }
    }
  });
  // editRelaseVisible.value = false;
};

const getTableList = async (porject: string) => {
  datas.tableData = [];
  datas.loading = true;
  const thParams = {
    project: porject,
    top: datas.algorithm_name.join(','),
  };
  const headerRes = await apiTableHeadFetch(thParams);
  const { data } = headerRes;
  datas.algorithmNameOptions = data;
  datas.tableHead = data;
  const params = {
    page: datas.currentPage,
    size: datas.currentSize,
  };
  const listRes = await apiListFetch(porject, params);
  datas.loading = false;
  const tableData = listRes.data;
  tableData.forEach((item: any) => {
    if (item.models && item.models.length > 0) {
      item.models.forEach((modItem: any) => {
        item[modItem.algorithm_name + '_value'] =
          modItem.algorithm_version.split('.');
        item[modItem.algorithm_name + '_update_point'] =
          modItem.update_point.split(',');
        item[modItem.algorithm_name + '_shadow'] = modItem.shadow;
        item[modItem.algorithm_name + '_test_report'] = modItem.has_test_report;
        item[modItem.algorithm_name + '_test_report_url'] =
          modItem.test_report_url;
        item[modItem.algorithm_name + '_version'] = modItem.algorithm_version;
      });
    }
  });
  datas.totalNum = listRes.total;
  datas.tableData = tableData;
};

/**
 * @description: 页面初始化
 * @return {*}
 */
onBeforeMount(async () => {
  getTableList(activeVersionTab.value);
});
</script>

<style lang="scss">
.version-management-container {
  .el-table thead {
    color: #4b525f;
    font-family: 'Blue Sky Standard';
  }
  background-color: #f8f8fa;
  .can-edit {
    cursor: pointer;
  }
  .underline {
    text-decoration: underline;
  }
  .algorithm-list {
    .item {
      margin-left: 80px;
      margin-bottom: 10px;
    }
  }
  .version-table-container {
    margin-top: 20px;
    .update-time {
      display: inline-block;
      width: 85px;
    }
    .update-count {
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 9px;
      text-align: center;
      background-color: #00bebe;
      line-height: 18px;
      color: #fff;
      opacity: 0.6;
    }
    .algorithm-update-count {
      color: #00bebe;
    }
  }
  .version-search-form {
    background-color: #fff;
    margin: 20px;
    .label-text {
      font-size: 14px;
      margin-right: 10px;
    }
    .version-form-item {
      line-height: 32px;
      padding: 10px;
      justify-content: flex-end;
      border-bottom: 1px solid #ebeef5;
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__header {
    margin: 0px;
    padding-top: 10px;
  }
  .card-title {
    font-size: 18px;
    margin-top: 10px;
    font-weight: 400;
  }
  .breadcrumb-header {
    background-color: #fff;
    padding: 20px 20px 0px 20px;
  }
}
</style>
