<template>
  <el-container class="data-fetch-outside">
    <div class="breadcrumb-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ 'OWL' }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ '图像数据' }}
        </el-breadcrumb-item>
      </el-breadcrumb>
      <div class="card-title">{{ '图像数据' }}</div>
      <div>
        <el-tabs v-model="activeVersionTab" class="demo-tabs" @tab-click="handleChangeTab">
          <el-tab-pane label="换电站2.0" name="PowerSwap2"></el-tab-pane>
          <el-tab-pane label="换电站3.0" name="PUS3"></el-tab-pane>
          <el-tab-pane label="换电站4.0" name="PUS4" v-if="!isEu() || hasPermission('function:data-fetch:pus4')"></el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div class="data-fetch-container">
      <div class="data-fetch-filter-container">
        <el-form ref="refSearchFilter" :model="searchQuery" label-width="80px" :rules="rules">
          <el-row :gutter="40">
            <el-col :span="11">
              <el-form-item label="算法" prop="algorithm_name">
                <el-select v-model="searchQuery.algorithm_name" placeholder="请选择" filterable clearable class="width-full">
                  <el-option v-for="(option, index) in algorithmNameOptions" :key="index" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="站点" prop="device_id">
                <RemoteDeviceSelect ref="remoteDeviceSelectRef" v-model="searchQuery.device_id" :project="activeVersionTab" :isMultiple="true" @handleDeviceChange="handleDeviceChange" :multipleLimit="100" class="width-full" />
              </el-form-item>
              <el-form-item label="车型类型" prop="vehicle_type" v-if="!['-stg-eu', '-eu'].includes(env as string)">
                <el-select v-model="searchQuery.vehicle_type" placeholder="请选择" collapse-tags collapse-tags-tooltip filterable clearable multiple class="width-full">
                  <el-option v-for="(item, index) in vehicleTypeArr" :key="index" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="车型类型" prop="vehicle_type" v-else>
                <el-select v-model="searchQuery.vehicle_type" placeholder="请选择" collapse-tags collapse-tags-tooltip filterable clearable multiple>
                  <el-option v-for="(item, index) in activeVersionTab == 'PowerSwap2' ? euPss2VehicleType : euPss3VehicleType" :key="index" :label="item" :value="item"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="time_set">
                <template #label>
                  <el-tooltip effect="dark" placement="top">
                    <template #content>
                      <span>全量站点时间可选范围为一周，非全量站点时间可选范围为两个月</span>
                    </template>
                    <div class="flex-box flex_a_i-center white-space-nowrap">
                      时间段 &nbsp;<el-icon color="#757575" :size="16"><QuestionFilled /></el-icon>
                    </div>
                  </el-tooltip>
                </template>
                <el-date-picker v-model="searchQuery.time_set" type="datetimerange" start-placeholder="开始时间" end-placeholder="结束时间" prefix-icon="el-icon-date" :disabled-date="disabledDate" :shortcuts="searchQuery.device_id.length > 0 ? ShortTwoMonthcuts() : ShortOneWeekcuts()" @calendar-change="handleCalendarChange" clearable> </el-date-picker>
              </el-form-item>
              <el-form-item label="摄像头" prop="camera_type">
                <el-select class="width-full" v-model="searchQuery.camera_type" placeholder="请选择" collapse-tags collapse-tags-tooltip filterable clearable multiple>
                  <el-option v-for="option in cameraOptions[activeVersionTab]" :label="option" :value="option"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="failure" class="search-button">
                <el-select v-model="searchQuery.failure_value" placeholder="请选择" clearable>
                  <el-option v-for="(option, index) in failureOptions" :key="index" :label="option.label" :value="option.value"></el-option>
                </el-select>
                <el-button class="welkin-primary-button margin_l-12" @click="handleSearchEvent(refSearchFilter)" :loading="loading">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="swap-table-container" style="margin-top: 20px">
        <!-- <div class="data-fetch-table"> -->
        <el-table :data="tableData" style="width: 100%" height="460" v-loading="loading">
          <el-table-column v-for="(option, index) in tableColumnSet" :key="index" :prop="option.name" :label="option.label" :width="option.width" :formatter="option.formatter" show-overflow-tooltip> </el-table-column>
          <el-table-column label="操作" class-name="operation-column">
            <template #default="scope">
              <span v-if="!scope.row.image_url">已删除</span>
              <el-icon class="operation-icon" @click="handleSingleView(scope.row, scope.$index)" v-if="!!scope.row.image_url">
                <View />
              </el-icon>
              <el-icon class="operation-icon" @click="handleSingleDownload(scope.row)" v-if="!!scope.row.image_url">
                <Download />
              </el-icon>
              <el-icon class="operation-icon-error" @click="showDeleteDialog(scope.row)" v-if="canDeleteImage && !!scope.row.image_url">
                <Delete />
              </el-icon>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-part" v-if="tableData.length > 0">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="searchQuery.page_no" :page-size.sync="searchQuery.page_size" :page-sizes="pagination.pageSizes" background :layout="pagination.layout" :total="totalNum"> </el-pagination>
      </div>
    </div>
    <el-footer style="height: 40px">
      <div class="data-fetch-footer" v-if="tableData.length > 0">
        <div class="total-number">
          <!-- {{ `总计${totalNum}张` }} -->
        </div>
        <div class="file-total-size">
          {{ `总计${totalNum}张` }}
          <el-button class="welkin-primary-button" @click="handleZipDownload"> 打包下载 </el-button>
        </div>
      </div>
      <!--<div class="file-total-size">筛选文件总大小为</div>-->
    </el-footer>
    <el-dialog v-model="dialogVisible" top="0px" width="90%" :append-to-body="true" class="image-portal-dialog" :lock-scroll="true" @close="closeDialog">
      <el-carousel trigger="click" height="95vh" :autoplay="false" :initial-index="imageIndex" ref="carousel" indicator-position="none" @change="changeCarousel" :loop="false">
        <el-carousel-item v-for="(item, index) in tableData" :key="item.image_url">
          <div class="view-info-container">
            <el-descriptions :column="3" :border="true" direction="vertical" :title="`图片预览 (当前页面第${index + 1}张 / 共${tableData.length}张)`">
              <el-descriptions-item label="图片名称">
                {{ item.image_name }}
              </el-descriptions-item>
              <el-descriptions-item label="设备ID">
                {{ item.device_id }}
              </el-descriptions-item>
              <el-descriptions-item label="产生图片时间">
                {{ formatLocaleDate(item.image_gen_time) }}
              </el-descriptions-item>
            </el-descriptions>

            <div class="image-deleted" v-if="!item.image_url">
              <el-icon><Picture /></el-icon>
              <el-icon><Delete /></el-icon>
              <div>Deleted</div>
            </div>
            <el-image v-if="!!item.image_url" :src="item.image_url" style="padding-top: 20px; flex: 1"> </el-image>
          </div>
        </el-carousel-item>
      </el-carousel>
    </el-dialog>

    <el-dialog v-model="deleteDialogVisible" title="确认删除图像？" draggable @close="closeDeteleDialog(deleteFormRef)">
      <el-descriptions :column="1" :border="true">
        <el-descriptions-item label="图片名称">
          {{ deleteImage.image_name }}
        </el-descriptions-item>
        <el-descriptions-item label="设备ID">
          {{ deleteImage.device_id }}
        </el-descriptions-item>
        <el-descriptions-item label="产生图片时间">
          {{ formatLocaleDate(deleteImage.image_gen_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="所属摄像头">
          {{ deleteImage.camera_type ? deleteImage.camera_type : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="图像大小">
          {{ fileSizeCal(deleteImage.image_size) }}
        </el-descriptions-item>
      </el-descriptions>
      <el-form ref="deleteFormRef" :model="deleteForm" :rules="deleteRules" label-position="left" label-width="140px" class="delete-ruleForm">
        <el-form-item label="删除原因" prop="delete_reason">
          <el-select v-model="deleteForm.delete_reason" clearable placeholder="请选择">
            <el-option v-for="item in deleteOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false" class="welkin-text-button">取消</el-button>
          <el-button class="welkin-primary-button" style="margin-left: 4px" @click="handleSingleDelete(deleteFormRef)"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>
  </el-container>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onBeforeMount, watch } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { View, Download, Delete, Picture, QuestionFilled } from '@element-plus/icons-vue'
import { apiGetMinioPicList, apiGetAlgorithmName, apiDeleteAlgorithmImage } from '~/apis/data-fetch'
import { cameraOptions, vehicleTypeArr, euPss2VehicleType, euPss3VehicleType } from '~/constvars/data-fetch'
import { getShortcuts, ShortOneWeekcuts, ShortTwoMonthcuts, formatLocaleDate, fileSizeCal, getEnv, toQueryString, isEu } from '~/utils'
import { hasPermission } from '~/auth'
import { pagination } from '~/constvars'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n({ useScope: 'global' })

const activeVersionTab = ref('PowerSwap2')
//定义响应式数据
const state = reactive({
  vehicle_typeOptions: [] as any,
  tableData: [] as any,
  tableColumnSet: [] as any,
  searchQueryTemp: [] as any,
  algorithmNameOptions: [] as any,
  changeIndex: '' as any,
  dialogVisible: false,
  flag: false,
  totalNum: 0,
  loading: false,
  // currentPage: 1,
  // currentSize: 10,
  deviceQuery: {
    totalResults: 0,
    query: {
      pageNo: 1,
      pageSize: 99999,
      filter: ''
    }
  },
  searchQuery: {
    algorithm_name: '',
    time_set: '',
    device_id: '',
    camera_type: '',
    vehicle_type: '',
    failure_value: 2,
    page_no: 1,
    page_size: 10
  } as any,
  failureOptions: [
    {
      label: '是',
      value: 1
    },
    {
      label: '否',
      value: 2
    }
  ],
  rules: {
    algorithm_name: {
      required: true,
      message: '请输入算法名称',
      trigger: 'blur'
    },
    time_set: {
      required: true,
      message: '请选择时间',
      trigger: 'blur'
    }
  } as any,
  imageIndex: '' as any
})
const remoteDeviceSelectRef = ref()

const deleteFormRef = ref<FormInstance>()
const deleteOptions = ref([
  {
    label: '人脸匿名化失败',
    value: '1'
  },
  {
    label: '车牌匿名化失败',
    value: '2'
  }
])
const deleteForm = reactive({
  delete_reason: ''
})
const deleteRules = reactive<FormRules>({
  delete_reason: [{ required: true, message: '请选择删除原因', trigger: 'change' }]
})
const minDate = ref()
const carousel = ref<any>()
const refSearchFilter = ref<FormInstance>()

const deleteDialogVisible = ref<boolean>(false)
const deleteImage = ref<any>({})
// 在海外需要申请通过才能下载
const env = getEnv()
// const canDeleteImage = true;
console.log(env)
const canDeleteImage = !!env && ['-stg-eu', '-eu'].indexOf(env) !== -1

//定义响应式数据结束

const disabledDate = (time: any) => {
  if (state.searchQuery.device_id.length > 0) {
    return new Date(time).getTime() > Date.now() + 3600 * 1000 * 24 || new Date(time).getTime() < minDate.value - 3600 * 1000 * 24 * 61 || new Date(time).getTime() > minDate.value + 3600 * 1000 * 24 * 61
  } else {
    return new Date(time).getTime() > Date.now() + 3600 * 1000 * 24 || new Date(time).getTime() < minDate.value - 3600 * 1000 * 24 * 7 || new Date(time).getTime() > minDate.value + 3600 * 1000 * 24 * 7
  }
}

const handleCalendarChange = (val: any) => {
  minDate.value = new Date(val[0]).getTime()
  if (val[1]) minDate.value = undefined
}

const handleDeviceChange = (e: any) => {
  if (e.length == 0) {
    state.searchQuery.time_set = ''
    minDate.value = undefined
  }
}

const handleSearchEvent = (formName: any) => {
  state.searchQuery.page_no = 1
  state.searchQuery.page_size = 10
  // state.currentPage = 1;
  // state.currentSize = 10;
  handleSearchClick(formName)
}

const handleSearchClick = (formName: any) => {
  formName.validate((valid: any) => {
    if (valid) {
      state.loading = true
      const tempQuery = JSON.parse(JSON.stringify(state.searchQuery))
      const timeArr = tempQuery.time_set
      // tempQuery.start_time = Math.floor(new Date(timeArr[0]).getTime() / 1000);
      // tempQuery.end_time = Math.floor(new Date(timeArr[1]).getTime() / 1000);
      tempQuery.start_time = new Date(timeArr[0]).getTime()
      tempQuery.end_time = new Date(timeArr[1]).getTime()

      delete tempQuery.time_set
      state.searchQueryTemp = tempQuery

      console.log('tempQuery', activeVersionTab.value, tempQuery)
      return apiGetMinioPicList(activeVersionTab.value, tempQuery).then((res: any) => {
        console.log(' apiGetMinioPicList res', res)
        if (res.data?.err_code == 0) {
          state.tableData = !!res.data?.data ? res.data.data : []
          state.totalNum = res.data.total
        }
        state.loading = false
      })
    }
  })
}

const handleSingleView = (row: any, index: any) => {
  state.imageIndex = index
  state.changeIndex = index
  state.flag = true
  state.dialogVisible = true
  setTimeout(() => {
    if (carousel) {
      ;(carousel.value as any & { setActiveItem: (index: number) => any }).setActiveItem(index)
    }
  }, 50)
}

// 单张下载
const handleSingleDownload = (row: any) => {
  const query = {
    image_url: row.image_url,
    image_size: row.image_size
  }
  window.open(`/web/welkin/device/v1/file/download` + toQueryString(query))
}

const showDeleteDialog = (row: any) => {
  deleteImage.value = row
  deleteDialogVisible.value = true
}

const handleSingleDelete = async (formEl: FormInstance | undefined) => {
  console.log(deleteImage.value)
  console.log(deleteImage.value.device_id)
  const query = {
    id: deleteImage.value.id,
    device_id: deleteImage.value.device_id,
    delete_reason: deleteForm.delete_reason
  }

  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      apiDeleteAlgorithmImage(activeVersionTab.value, query).then((res) => {
        deleteDialogVisible.value = false
        ElMessage.success('删除成功')
        handleSearchClick(refSearchFilter.value)
      })
    }
  })
}

/**
 * @description: 关闭删除弹窗清空表单
 * @param {*} formEl
 * @return {*}
 */
const closeDeteleDialog = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const handleSizeChange = (val: any) => {
  state.searchQuery.page_size = val
  // state.currentPage = 1;
  state.searchQuery.page_no = 1
  handleSearchClick(refSearchFilter.value)
}
//当前页变更更新内容
const handleCurrentChange = (val: any) => {
  state.searchQuery.page_no = val
  handleSearchClick(refSearchFilter.value)
}
//下载文件
const handleZipDownload = () => {
  // apiGetZipDownload(state.searchQueryTemp);
  console.log(state.searchQueryTemp, '***')
  const param = toQueryString({
    ...state.searchQueryTemp,
    download: 1,
    page_no: 1
  })
  window.open(`/web/welkin/image/v1/${activeVersionTab.value}/list` + param)
}
const handleChangeTab = () => {
  resetSearchForm()
  if (remoteDeviceSelectRef.value) remoteDeviceSelectRef.value.searchDeviceList('NIO')
  state.tableData = []
  state.totalNum = 0
}

const resetSearchForm = () => {
  state.searchQuery.algorithm_name = ''
  state.searchQuery.time_set = ''
  state.searchQuery.device_id = ''
  state.searchQuery.camera_type = ''
  state.searchQuery.vehicle_type = ''
  state.searchQuery.failure_value = 2
  state.searchQuery.page_no = 1
  // state.searchQuery.page_size = 10;
}

//关闭弹窗
const closeDialog = () => {
  state.flag = false
}
// 切换图片获取当前索引
const changeCarousel = (newIndex: number, oldIndex: number) => {
  state.changeIndex = newIndex
}

//搜索对象
const keyDown = () => {
  document.onkeydown = (e) => {
    //事件对象兼容
    let e1 = e || window.event
    if (state.flag === true) {
      if (e1 && e1.keyCode === 37) {
        // 按下左箭头
        if (state.changeIndex > 0) {
          state.changeIndex = state.changeIndex - 1
        }
        if (carousel) {
          ;(carousel.value as any & { setActiveItem: (index: number) => any }).setActiveItem(state.changeIndex)
        }
      } else if (e1 && e1.keyCode == 39) {
        // 按下右箭头
        if (state.changeIndex < state.tableData.length - 1) {
          state.changeIndex = state.changeIndex + 1
        }
        if (carousel) {
          ;(carousel.value as any & { setActiveItem: (index: number) => any }).setActiveItem(state.changeIndex)
        }
      }
    }
  }
}

const initTableColumns = () => {
  state.tableColumnSet = [
    {
      name: 'image_name',
      label: '图片名称',
      width: 360
    },
    {
      name: 'device_id',
      label: '设备ID',
      width: 220
    },
    {
      name: 'camera_type',
      label: '所属摄像头',
      width: 110,
      formatter: (row: any) => (row.camera_type ? row.camera_type : '-')
    },
    {
      name: 'image_gen_time',
      label: '产生图片时间',
      width: 200,
      formatter: (row: any) => formatLocaleDate(row.image_gen_time, false)
    },
    {
      name: 'image_size',
      label: '图片大小',
      width: 120,
      formatter: (row: any) => fileSizeCal(row.image_size)
    }
  ]
}

const getAlgorithmNameOptions = () => {
  return apiGetAlgorithmName().then((res) => {
    res.data.map((item: any) => {
      state.algorithmNameOptions.push({
        label: item.name,
        value: item.value
      })
    })
  })
}

onBeforeMount(() => {
  keyDown()
  initTableColumns()
  getAlgorithmNameOptions()
})

watch(
  () => locale.value,
  (newValue, oldValue) => {
    handleSearchClick(refSearchFilter.value)
  }
)

//解构
const {
  loading,
  tableData,
  tableColumnSet,
  dialogVisible,
  // currentPage,
  // currentSize,
  imageIndex,
  searchQuery,
  algorithmNameOptions,
  rules,
  vehicle_typeOptions,
  failureOptions,
  totalNum
} = toRefs(state)
</script>

<style lang="scss">
.data-fetch-outside {
  overflow-y: auto;
  background-color: #f8f8fa;

  .card-title {
    font-size: 18px;
    margin-top: 10px;
    font-weight: 400;
  }
  .breadcrumb-header {
    background-color: #fff;
    padding: 20px 20px 0px 20px;
    width: 100%;
    // z-index: 100;
    // position: fixed;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__header {
    margin: 0px;
    padding-top: 10px;
  }
}
.data-fetch-container {
  // margin: 140px 20px 0px 20px;
  margin: 20px;
}

.data-fetch-filter-container {
  .device-filter {
    .multiselect__tags {
      width: 398px;
    }
  }
  border-width: 1px;
  border-color: #e8eef2;
  border-right-style: none;
  border-bottom-style: solid;
  border-left-style: none;
  background-color: #fff;
  padding: 20px;
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    width: 398px;
  }
  .el-input__icon {
    height: 32px;
    line-height: 32px;
  }
  .el-form-item__label {
    line-height: 32px;
    padding: 0 16px 0 0;
    font-family: 'Blue Sky Standard';
  }
  .el-date-editor .el-range-separator {
    line-height: 25px;
  }
  // .el-date-editor--datetimerange.el-input__inner {
  //   width: 350px;
  // }
  .multiselect__tags {
    line-height: 32px;
    height: 32px;
    min-height: 32px;
    padding: 0 40px 0 15px;
    overflow: auto;
  }
  .multiselect__select {
    height: 32px;
  }
  .multiselect__placeholder {
    margin-bottom: 0;
    padding-top: 0;
  }
  .multiselect__input,
  .multiselect__single {
    min-height: 32px;
    line-height: 32px;
  }
  .multiselect__option--selected {
    background: #9abcff;
    color: #ffffff;
    font-weight: 400;
  }
  .multiselect__option--highlight {
    content: attr(data-select);
    background: linear-gradient(0deg, rgba(44, 115, 255, 0.1), rgba(44, 115, 255, 0.1)), #ffffff;
    color: #363c54;
  }
  .multiselect__tag {
    margin-right: 2px;
    margin-bottom: 0;
    margin-top: 4px;
    background: #f8f8fa;
    color: #4b525f;
    width: 110px;
  }
  .multiselect__tag-icon:hover {
    background: #2c73ff;
  }
  .camera {
    .multiselect {
      width: 350px;
    }
  }
  .search-button {
    .el-input__inner {
      width: 318px;
    }
  }
}
.data-fetch-footer {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding-bottom: 20px;
  .total-number {
    padding-left: 33px;
    font-size: 14px;
    line-height: 40px;
    font-family: 'Blue Sky Standard';
  }
}
.file-total-size {
  height: 40px;
  line-height: 40px;
  font-weight: 400;
  font-size: 14px;
  margin-left: 10px;
  margin-right: 20px;
}
.data-fetch-table {
  margin: 20px 0 0 0;
  // height: 100%;
  background-color: #fff;
  .cursor-hand {
    cursor: pointer;
    margin-right: 10px;
    color: #4b525f;
  }
  .el-table thead {
    color: #4b525f;
    font-family: 'Blue Sky Standard';
  }
  .el-table th.el-table__cell {
    // background: #f8f8fa;
    border-style: none;
  }
  .el-table {
    font-size: 12px;
    line-height: 18px;
    color: #4b525f;
    font-family: 'Blue Sky Standard';
  }
}
.pagination-part {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  background-color: #fff;
}
.image-portal-dialog {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    // justify-content: center;
    padding-top: 10px;
    .el-carousel {
      width: 100%;
      // margin: 0 auto;
      margin: 0px;
      .el-carousel__arrow i {
        font-size: 40px;
      }
      .view-info-container {
        margin: 0 80px;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .image-deleted {
        height: 100%;
        width: 100%;
        text-align: center;
        font-size: 100px;
        align-items: center;
        display: flex;
        justify-content: center;
      }
      .title-div {
        margin-bottom: 10px;
        .title-info {
          margin-right: 50px;
        }
      }
    }
  }
}
.delete-ruleForm {
  margin-top: 20px;
}
</style>
