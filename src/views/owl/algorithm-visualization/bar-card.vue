<template>
  <div class="bar-card-container">
    <el-card class="border-radius-8">
      <div class="flex-box flex_j_c-space-between flex_a_i-center">
        <div>
          <span class="font-size-18 title">{{ t('aiPortrait.pp_yesterday_rate') }}</span>
          <span style="color: #9f9f9f;" class="font-size-12 margin_l-8 title">- {{ t('aiPortrait.pp_click_column') }}</span>
        </div>
        <el-radio-group v-model="if_FTT" @change="changeRadio">
          <el-radio-button :label="`${$t('aiPortrait.pp_ftt')}`" />
          <el-radio-button :label="`${$t('aiPortrait.pp_non_ftt')}`" />
        </el-radio-group>
      </div>

      <!-- loading时展示的骨架屏 -->
      <el-skeleton :rows="7" animated v-show="loading" class="margin_t-24" />

      <!-- loading结束且有数据时展示的echarts图像 -->
      <div id="barCharts" class="width-full height-300" v-show="!loading && barOption.series[0].data.length > 0"></div>

      <!-- loading结束且数据为null时展示的暂无数据 -->
      <el-empty class="width-full height-300" :description="`${$t('common.pp_empty')}`" v-if="!loading && barOption.series[0].data.length === 0"></el-empty>

      <!-- 点击单根柱子展示具体算法情况 -->
      <Dialog v-if="showDialog" :showDialog="showDialog" :activeVersionTab="activeVersionTab" :dialogHeaderData="dialogHeaderData" @closeDialog="closeDialog"></Dialog>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import {ref, reactive, onMounted, nextTick} from 'vue'
import { useI18n } from 'vue-i18n'
import {formatFTTData, formatNotFTTData} from '~/utils'
import { apiGetAlgorithmList } from '~/apis/owl'
import { ElMessage } from 'element-plus'
import Dialog from './dialog.vue'
import * as echarts from 'echarts'


const props = defineProps({
  activeVersionTab: {
    type: String,
    default: 'PowerSwap2'
  }
})

const { t } = useI18n()
const activeVersionTab = props.activeVersionTab
const if_FTT = ref(t('aiPortrait.pp_ftt'))
const loading = ref(false)
const showDialog = ref(false)
const dialogHeaderData = ref('' as any)
let myChart: any
const barOption = reactive({
  color: '#00bebe',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    top: '10%',
    bottom: '10%',
    right: '2%',
    left: '4%'
  },
  xAxis: {
    type: 'category',
    axisTick: {show: false},
    data: [] as any,
    axisLabel: {
      show: true,
      interval: 0
    }
  },
  yAxis: {
    type: 'value',
    min: (value: any) => (value.min < 4 ? 0 : Math.floor(value.min) - 4),
  },
  dataZoom: [
    {
      type: 'inside'
    }
  ],
  series: [
    {
      data: [] as any,
      type: 'bar',
      barWidth: 40,
      label: {
        show: true,
        position: 'top'
      }
    }
  ]
})

const closeDialog = () => {
  showDialog.value = false
}

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  myChart.on('click', (params: any) => {
    dialogHeaderData.value = params.data
    showDialog.value = true
  })
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  echartRender('barCharts', barOption)
}

/**
 * @description: 切换FTT和非FTT
 * @return {*}
 */
const changeRadio = () => {
  myChart && myChart.dispose()
  getAlgorithmList()
}

/**
 * @description: 获取数据
 * @return {*}
 */
const getAlgorithmList = async () => {
  loading.value = true
  const ftt = (if_FTT.value === 'FTT算法' || if_FTT.value === 'FTT') ? 0 : 1
  const yesterday = new Date().getTime() - 24 * 60 * 60 * 1000
  const yesterdayZero = new Date(new Date(yesterday).toLocaleDateString()).getTime()
  const todayZero = new Date(new Date().toLocaleDateString()).getTime()
  const params = {
    ftt: ftt,
    day: yesterdayZero
  }
  try {
    const res = await apiGetAlgorithmList(activeVersionTab, params)
    const { err_code, message } = res
    if(!err_code) {
      loading.value = false
      barOption.series[0].data = ftt === 0 ?  formatFTTData(res.data, res.expect_success_rate) : formatNotFTTData(res.data)
      barOption.xAxis.data = res.data.map((item: any) => item.name)
      nextTick(() => setCharts())
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    ElMessage.error(t('aiPortrait.pp_error'))
  }
}

onMounted(() => {
  getAlgorithmList()
})
</script>
<style lang="scss">
.bar-card-container {
  .title {
    font-family: 'Blue Sky Noto';
  }
}
</style>
