<template>
  <div class="dialog-container">
    <el-dialog v-model="dialogVisible" draggable align-center width="70%" @close="closeDialog">
      <!-- 头部信息 -->
      <template #header>
        <el-row :gutter="20" class="height-60">
          <el-col :span="8">
            <div class="font-size-40 text-align-center">
              {{ dialogHeaderData.name }}<span class="font-size-14 margin_l-10">{{ dialogHeaderData.version }}</span>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="height-full font-size-14 flex-box flex_d-column flex_j_c-space-between">
              <span>{{ dialogHeaderData.shadow ? 'shadow' : 'online' }}</span>
              <span>{{ $t('aiPortrait.pp_update_time') }}: {{ formatTimeToDay(dialogHeaderData.update_ts) }}</span>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="height-full font-size-14 flex-box flex_d-column flex_j_c-space-between">
              <span>{{ $t('aiPortrait.pp_backhaul') }}: {{ dialogHeaderData.image_count }}</span>
              <span>{{ $t('aiPortrait.pp_upload_sites') }}: {{ dialogHeaderData.device_count }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="height-full font-size-14 flex-box flex_d-column flex_j_c-space-between">
              <div class="flex-box flex_a_i-center">
                <span class="margin_r-10">{{ $t('aiPortrait.pp_aec_effective') }}:</span>
                <el-tooltip effect="dark" placement="bottom" popper-class="aec-tooltip" :show-arrow="false">
                  <template #content>
                    <span>{{ $t('aiPortrait.pp_aec_return') }}</span>
                    <hr style="margin-top: 0; margin-bottom: 0; height: 1px; border: none; background: #fff" />
                    <span style="display: flex; justify-content: center">{{ $t('aiPortrait.pp_total_order') }}</span>
                  </template>
                  <el-icon color="#757575" :size="16"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span class="margin_l-10"> {{ (dialogHeaderData.aec_effective_rate * 100).toFixed(2) + '%' }}</span>
              </div>
              <div class="flex-box flex_a_i-center">
                <span class="margin_r-10">{{ $t('aiPortrait.pp_ai_rate') }}:</span>
                <el-tooltip effect="dark" placement="bottom" popper-class="aec-tooltip" :show-arrow="false">
                  <template #content>
                    <span>{{ $t('aiPortrait.pp_ai_return') }}</span>
                    <hr style="margin-top: 0; margin-bottom: 0; height: 1px; border: none; background: #fff" />
                    <span style="display: flex; justify-content: center">{{ $t('aiPortrait.pp_total_order') }}</span>
                  </template>
                  <el-icon color="#757575" :size="16"><QuestionFilled /></el-icon>
                </el-tooltip>
                <span class="margin_l-10"> {{ (dialogHeaderData.aec_correct_rate * 100).toFixed(2) + '%' }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-divider />
      </template>

      <!-- 全局搜索 -->
      <div class="flex-box flex_a_i-center margin_b-20">
        <span class="margin_r-10" v-if="algorithmMap[dialogHeaderData.name] == 0 || algorithmMap[dialogHeaderData.name] == 1">{{ algorithmMap[dialogHeaderData.name] == 0 ? $t('aiPortrait.pp_vehicle_model') : $t('aiPortrait.pp_battery_type') }}</span>
        <el-select v-model="allPartsSelect" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable v-if="algorithmMap[dialogHeaderData.name] == 0 || algorithmMap[dialogHeaderData.name] == 1" style="width: 180px">
          <el-option v-for="item in allPartsOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <span class="margin_r-10">{{ $t('common.pp_time_frame') }}:</span>
        <el-select v-model="dayModeSelect" :placeholder="`${$t('common.pp_please_select')}`" filterable style="width: 180px">
          <el-option v-for="item in dayModeOption" :key="item.value" :label="`${$t(item.label)}`" :value="item.value" />
        </el-select>
        <span class="margin_r-10" v-if="dialogHeaderData.pfs">{{ $t('aiPortrait.pp_parameter_recipe') }}:</span>
        <el-select v-model="use_pfs" :placeholder="`${$t('common.pp_please_select')}`" filterable v-if="dialogHeaderData.pfs" style="width: 220px">
          <el-option v-for="item in pfsOption" :key="item.label" :label="`${$t(item.label)}${item.value ? dialogHeaderData.pfs_devices : dialogHeaderData.total_devices}）`" :value="item.value" />
        </el-select>
        <el-button class="welkin-primary-button" @click="commonSearch" v-loading="loading">{{ $t('common.pp_search') }}</el-button>
      </div>

      <!-- 主体部分echarts -->
      <div class="echarts-container">
        <el-card class="pie-container" v-loading="pieLoading">
          <div class="pie-container-header">
            <span class="pie-title">{{ dialogDate }} - {{ $t('aiPortrait.pp_pass_rate') }}</span>
          </div>
          <div id="pieChart" style="height: 90%; width: 100%" v-show="!pieEmpty"></div>
          <el-empty :description="`${$t('common.pp_empty')}`" v-if="pieEmpty"></el-empty>
        </el-card>
        <el-card class="line-container" v-loading="lineLoading">
          <div class="line-container-header">
            <span>{{ $t('aiPortrait.pp_trend') }}</span>
            <div class="line-header-right">
              <span class="line-select-tips">{{ $t('aiPortrait.pp_sites') }}:</span>
              <RemoteDeviceSelect v-model="lineQuery.device_id" :project="activeVersionTab" />
              <el-date-picker v-model="dateSelect" type="daterange" unlink-panels @change="handleDateChange" :clearable="false" :range-separator="`${$t('common.pp_to')}`" :shortcuts="shortcuts" :disabled-date="disabledDate" style="width: 240px" />
            </div>
          </div>
          <div id="lineChart" style="height: 90%; width: 100%" v-show="!lineEmpty"></div>
          <el-empty :description="`${$t('common.pp_empty')}`" v-if="lineEmpty"></el-empty>
        </el-card>
      </div>
      <div class="echarts-container">
        <el-card class="bar-container" v-loading="barLoading">
          <div class="bar-container-header">
            <span>{{ formatTimeToDay(barQuery.start_time) }} - {{ $t('aiPortrait.pp_top_10') }}</span>
            <div class="bar-header-right">
              <span class="bar-select-tips">{{ $t('aiPortrait.pp_block_sites') }}:</span>
              <RemoteDeviceSelect v-model="barQuery.ignore_devices" :project="activeVersionTab" :isMultiple="true" />
              <el-date-picker v-model="barQuery.start_time" type="date" :clearable="false" :disabled-date="getDisabledDate" />
            </div>
          </div>
          <div id="barChart" style="height: 90%; width: 100%" v-show="!barEmpty"></div>
          <el-empty :description="`${$t('common.pp_empty')}`" v-if="barEmpty"></el-empty>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {ref, reactive, onBeforeMount, nextTick} from 'vue'
import {formatTimeToDay, yesterdayDate} from '~/utils'
import {useI18n} from 'vue-i18n'
import {algorithmMap} from './algorithm-map'
import {apiGetVehicleAndBattery, apiGetVisualSuccessRate, apiGetSuccessRateTrend, apiGetTop10SuccessRate} from '~/apis/owl'
import * as echarts from 'echarts'
import {QuestionFilled} from '@element-plus/icons-vue'

interface CommonQuery {
  algorithm_id: number | string
  vehicle_type: string
  battery_type: string
  time_range: number
  name: string
  use_pfs?: boolean
}

const props = defineProps({
  showDialog: Boolean,
  activeVersionTab: {
    type: String,
    default: 'PowerSwap2'
  },
  dialogHeaderData: Object
})
const emits = defineEmits(['closeDialog'])

const {t} = useI18n()
const dialogVisible = props.showDialog
const activeVersionTab = props.activeVersionTab
const dialogHeaderData = props.dialogHeaderData!
const loading = ref(false)
const pieLoading = ref(false)
const lineLoading = ref(false)
const barLoading = ref(false)
const barEmpty = ref(false)
const pieEmpty = ref(false)
const lineEmpty = ref(false)
const dialogDate = ref(yesterdayDate)
const allPartsSelect = ref('' as number | string)
const dayModeSelect = ref(0)
const use_pfs = ref(true)
const allPartsOptions = ref([] as any)
const dayModeOption = ref([
  {
    value: 0,
    label: 'aiPortrait.pp_all_day'
  },
  {
    value: 1,
    label: 'aiPortrait.pp_day'
  },
  {
    value: 2,
    label: 'aiPortrait.pp_night'
  }
])
const pfsOption = ref([
  {
    label: 'aiPortrait.pp_full_calculation',
    value: false
  },
  {
    label: 'aiPortrait.pp_open_sites',
    value: true
  }
])
const shortcuts = ref([
  {
    text: t('aiPortrait.pp_last_week'),
    value: () => [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7, new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24]
  },
  {
    text: t('aiPortrait.pp_last_month'),
    value: () => [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 31, new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24]
  }
])
const dateSelect = ref([new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7, new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24] as any)
const commonQuery: CommonQuery = reactive({
  algorithm_id: '' as number | string,
  vehicle_type: '',
  battery_type: '',
  time_range: 0,
  name: '',
  use_pfs: true
})
const lineQuery = reactive({
  device_id: '',
  start_time: dateSelect.value[0],
  end_time: new Date(new Date().toLocaleDateString()).getTime()
})
const barQuery = reactive({
  ignore_devices: [] as any,
  start_time: new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24
})
let pieOption = reactive({
  animation: false,
  color: ['#00bebe', '#E8EEF2'],
  title: {
    show: true,
    text: '',
    subtext: '',
    itemGap: 20,
    x: 'center',
    y: 'center',
    top: '40%',
    textStyle: {
      left: '40%',
      fontSize: '20',
      color: '#91A2BC',
      fontWeight: '700'
    },
    subtextStyle: {
      fontSize: '14',
      fontWeight: '400',
      color: '#91A2BC'
    }
  },
  grid: {
    bottom: '10%'
  },
  series: [
    {
      silent: true,
      type: 'pie',
      radius: ['60%', '80%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      labelLine: {
        show: false
      },
      data: [] as any
    }
  ]
})
let lineOption = reactive({
  animation: false,
  color: '#00bebe',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    formatter: function (params: any) {
      return params[0].name + '<br/>' + params[0].marker + ' ' + params[0].value + '%'
    }
  },
  grid: {
    top: '8%',
    bottom: '15%',
    right: '2%',
    left: '10%'
  },
  xAxis: {
    type: 'category',
    data: [] as any,
    axisTick: {show: false}
  },
  yAxis: {
    type: 'value',
    name: t('aiPortrait.pp_pass'),
    nameLocation: 'middle',
    nameGap: 50,
    min: (value: any) => value.min,
    max: (value: any) => value.max,
    axisLabel: {
      margin: 14
    }
  },
  dataZoom: [
    {
      type: 'inside'
    }
  ],
  series: [
    {
      data: [] as any,
      type: 'line',
      label: {
        show: true,
        position: 'top',
        color: '#91a2bc',
        formatter: function (params: any) {
          return params.value + '%'
        }
      },
      labelLayout: {
        hideOverlap: true
      }
    }
  ]
})
let barOption = reactive({
  animation: false,
  color: '#00bebe',
  grid: {
    top: '10%',
    bottom: '30%',
    left: '5%',
    right: '5%'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
    formatter: function (params: any) {
      return params[0].name + '<br/>' + params[0].marker + ' ' + params[0].value + '%'
    }
  },
  xAxis: {
    type: 'category',
    data: [] as any,
    axisTick: {show: false},
    axisLabel: {
      formatter: function (params: string) {
        var newParamsName = ''
        var paramsNameNumber = params.length
        var provideNumber = 5
        var rowNumber = Math.ceil(paramsNameNumber / provideNumber)

        if (paramsNameNumber > provideNumber) {
          for (var i = 0; i < rowNumber; i++) {
            var tempStr = ''
            var start = i * provideNumber
            var end = start + provideNumber
            if (i == rowNumber - 1) {
              tempStr = params.substring(start, paramsNameNumber)
            } else {
              tempStr = params.substring(start, end) + '\n'
            }
            newParamsName += tempStr
          }
        } else {
          newParamsName = params
        }
        return newParamsName
      }
    }
  },
  yAxis: {
    type: 'value'
  },
  dataZoom: [
    {
      type: 'inside'
    }
  ],
  series: [
    {
      data: [] as any,
      type: 'bar',
      barWidth: 40,
      barGap: '35%',
      label: {
        show: true,
        position: 'top',
        color: '#91a2bc',
        formatter: function (params: any) {
          return params.value + '%'
        }
      }
    }
  ]
})

/**
 * @description: 折线图时间限制
 * @param {*} time
 * @return {*}
 */
const disabledDate = (time: any) => {
  return new Date(time).getTime() >= Date.now() - 3600 * 1000 * 24 || new Date(time).getTime() <= Date.now() - 3600 * 1000 * 24 * 180
}

/**
 * @description: 柱状图时间限制
 * @param {*} time
 * @return {*}
 */
const getDisabledDate = (time: any) => {
  return new Date(time).getTime() >= Date.now() - 3600 * 1000 * 24
}

const handleDateChange = (value: any) => {
  if (value) {
    lineQuery.start_time = new Date(value[0]).getTime()
    lineQuery.end_time = new Date(value[1]).getTime() + 86400000
  }
}

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  myChart.resize()
  window.addEventListener('resize', () => myChart.resize())
}

/**
 * @description: 全局搜索
 * @return {*}
 */
const commonSearch = async () => {
  commonQuery.name = dialogHeaderData.name
  commonQuery.use_pfs = use_pfs.value
  if (!dialogHeaderData.pfs) delete commonQuery.use_pfs
  commonQuery.time_range = dayModeSelect.value
  commonQuery.algorithm_id = dialogHeaderData.algorithm_id
  if (algorithmMap[dialogHeaderData.name] == 0) {
    commonQuery.vehicle_type = allPartsSelect.value as string
  } else if (algorithmMap[dialogHeaderData.name] == 1) {
    commonQuery.battery_type = allPartsSelect.value as string
  }
  barQuery.start_time = barQuery.start_time.valueOf()
  Object.assign(lineQuery, commonQuery)
  Object.assign(barQuery, commonQuery)
  getSuccessRate()
  getSuccessRateTrend()
  getTop10SuccessRate()
}

/**
 * @description: 关闭弹窗后重置数据
 * @return {*}
 */
const closeDialog = () => {
  commonQuery.battery_type = ''
  commonQuery.vehicle_type = ''
  allPartsSelect.value = ''
  dayModeSelect.value = 0
  lineQuery.device_id = ''
  dateSelect.value = [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7, new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24]
  lineQuery.start_time = dateSelect.value[0]
  lineQuery.end_time = new Date(new Date().toLocaleDateString()).getTime()
  barQuery.ignore_devices = []
  barQuery.start_time = new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24
  emits('closeDialog')
}

/**
 * @description: 获取所有车辆类型和电池类型
 * @return {*}
 */
const getVehicleAndBattery = (id: number) => {
  const yesterday = new Date().getTime() - 24 * 60 * 60 * 1000
  const yesterdayZero = new Date(new Date(yesterday).toLocaleDateString()).getTime()
  const params = {
    algorithm_id: id,
    day: yesterdayZero
  }
  return apiGetVehicleAndBattery(activeVersionTab, params).then((res) => {
    if (algorithmMap[dialogHeaderData.name] == 0) {
      allPartsOptions.value = res.data.vehicle_list
    } else {
      allPartsOptions.value = res.data.battery_list
    }
  })
}

/**
 * @description: 获取所有站点成功率
 * @return {*}
 */
const getSuccessRate = () => {
  pieLoading.value = true
  const yesterday = new Date().getTime() - 24 * 60 * 60 * 1000
  const yesterdayZero = new Date(new Date(yesterday).toLocaleDateString()).getTime()
  const params = {
    ...commonQuery,
    start_time: yesterdayZero
  }
  return apiGetVisualSuccessRate(activeVersionTab, params).then((res) => {
    pieOption.title.text = (res.data.success_rate * 100).toFixed(2) + '%'
    pieOption.title.subtext = t('aiPortrait.pp_total') + ': ' + res.data.total
    pieOption.series[0].data = [{value: res.data.total * res.data.success_rate}, {value: res.data.total - res.data.total * res.data.success_rate}]
    if (!res.data) {
      pieEmpty.value = true
    } else {
      pieEmpty.value = false
    }
    nextTick(() => {
      pieLoading.value = false
      if (!pieEmpty.value) echartRender('pieChart', pieOption)
    })
  })
}

/**
 * @description: 获取通过率变化趋势
 * @return {*}
 */
const getSuccessRateTrend = () => {
  lineLoading.value = true
  return apiGetSuccessRateTrend(activeVersionTab, lineQuery).then((res) => {
    lineOption.xAxis.data = []
    lineOption.series[0].data = []
    res.data.map((item: any) => {
      lineOption.xAxis.data.push(formatTimeToDay(item.day))
      lineOption.series[0].data.push((item.success_rate * 100).toFixed(2))
    })
    lineEmpty.value = lineOption.series[0].data.length === 0 ? true : false
    nextTick(() => {
      lineLoading.value = false
      if (!lineEmpty.value) echartRender('lineChart', lineOption)
    })
  })
}

/**
 * @description: 获取成功率最低TOP10站点
 * @return {*}
 */
const getTop10SuccessRate = () => {
  if (barQuery.ignore_devices?.length == 0) {
    delete barQuery.ignore_devices
  }
  barLoading.value = true
  return apiGetTop10SuccessRate(activeVersionTab, barQuery).then((res) => {
    barOption.xAxis.data = []
    barOption.series[0].data = []
    res.data.map((item: any) => {
      barOption.xAxis.data.push(item.description.replace(/\s*/g, ''))
      barOption.series[0].data.push((item.success_rate * 100).toFixed(2))
    })
    barEmpty.value = barOption.series[0].data.length === 0 ? true : false
    nextTick(() => {
      barLoading.value = false
      if (!barEmpty.value) echartRender('barChart', barOption)
    })
  })
}

onBeforeMount(() => {
  getVehicleAndBattery(dialogHeaderData.algorithm_id)
  commonSearch()
})
</script>

<style lang="scss">
.dialog-container {
  .el-dialog {
    border-radius: 5px;
    font-family: 'Noto Sans CJK SC';
    .el-dialog__header {
      padding-bottom: 0;
    }
    .el-dialog__body {
      padding-top: 0;
      .el-range-editor.el-input__wrapper {
        margin: 0.5rem;
      }
      .search-button {
        background-color: #00bebe;
        color: #fff;
        margin-left: 20px;
      }
      .echarts-container {
        height: 300px;
        display: flex;
        margin-top: 10px;
        .el-card__body {
          height: 100%;
        }
        .pie-container {
          height: 100%;
          width: 30%;
          box-shadow: none;
          .pie-container-header {
            height: 42px;
            display: flex;
            align-items: center;
            .pie-title {
              color: #4b525f;
            }
          }
        }
        .line-container {
          flex: 1;
          margin-left: 15px;
          box-shadow: none;
          .line-container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .line-header-right {
              display: flex;
              align-items: center;
              .line-select-tips {
                margin-right: 10px;
              }
            }
          }
        }
        .bar-container {
          height: 100%;
          width: 100%;
          box-shadow: none;
          .bar-container-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .bar-header-right {
              display: flex;
              align-items: center;
              .bar-select-tips {
                margin-right: 10px;
              }
              .el-date-editor.el-input,
              .el-date-editor.el-input__wrapper {
                width: 240px;
              }
            }
          }
          .el-empty {
            padding: 0;
          }
        }
      }
    }
    .el-select {
      width: 200px;
      margin-right: 10px;
    }
  }
}
</style>
