<template>
  <div class="line-card-container">
    <el-card class="border-radius-8">
      <div class="flex-box flex_j_c-space-between flex_a_i-center">
        <span class="font-size-18 title">{{ t('aiPortrait.pp_ftt_trend') }}</span>
        <div>
          <el-date-picker v-model="dateSelect" type="daterange" :clearable="false" unlink-panels @change="handleDateChange" @calendar-change="handleCalendarChange" :range-separator="`${$t('common.pp_to')}`" :shortcuts="shortcuts" :disabled-date="disabledDate" style="width: 400px" />
        </div>
      </div>

      <!-- loading时展示的骨架屏 -->
      <el-skeleton :rows="7" animated v-show="loading" class="margin_t-24" />

      <!-- loading结束且有数据时展示的echarts图像 -->
      <div id="lineCharts" class="width-full height-300" v-show="!loading && lineOption.series[0].data.length > 0"></div>

      <!-- loading结束且数据为null时展示的暂无数据 -->
      <el-empty class="width-full height-300" :description="`${$t('common.pp_empty')}`" v-if="!loading && lineOption.series[0].data.length === 0"></el-empty>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import {ref, reactive, onMounted, nextTick, watch} from 'vue'
import {formatLineYdata, formatLineXdata} from '~/utils'
import { useI18n } from 'vue-i18n'
import {apiGetFttData} from '~/apis/owl'
import {ElMessage} from 'element-plus'
import * as echarts from 'echarts'

const props = defineProps({
  activeVersionTab: {
    type: String,
    default: 'PowerSwap2'
  }
})

const activeVersionTab = props.activeVersionTab
const { t } = useI18n()
const { locale } = useI18n({ useScope: 'global' })
const loading = ref(false)
const dateSelect = ref([new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7, new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24] as any)
const shortcuts = ref([
  {
    text: t('aiPortrait.pp_last_week'),
    value: () => [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7, new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24]
  },
  {
    text: t('aiPortrait.pp_last_month'),
    value: () => [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 31, new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24]
  }
])
const minDate = ref()
const disabledDate = (time: any) => {
  return new Date(time).getTime() >= Date.now() - 3600 * 1000 * 24 || new Date(time).getTime() <= minDate.value - 3600 * 1000 * 24 * 31 || new Date(time).getTime() >= minDate.value + 3600 * 1000 * 24 * 31
}
const lineQuery = reactive({
  start_time: new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7,
  end_time: new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24,
  update: false
})
let myChart: any
const lineOption = reactive({
  animation: false,
  color: '#00bebe',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    },
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  grid: {
    top: '10%',
    bottom: '10%',
    right: '2%',
    left: '4%'
  },
  xAxis: {
    type: 'category',
    axisTick: {show: false},
    data: [] as any
  },
  yAxis: {
    type: 'value',
    min: (value: any) => (value.min < 4 ? 0 : Math.floor(value.min) - 4),
    max: 100,
    axisLabel: {
      margin: 14
    }
  },
  dataZoom: [
    {
      type: 'inside'
    }
  ],
  series: [
    {
      data: [] as any,
      type: 'line',
      label: {
        show: true,
        position: 'top'
      },
      labelLayout: {
        hideOverlap: true
      },
      markLine: {
        silent: true,
        symbol: ['none', 'none'],
        label: {
          position: 'end',
          silent: true
        },
        data: [
          {
            yAxis: '' as any,
            lineStyle: {
              type: 'dashed',
              color: '#3491fa',
              width: 2
            },
            label: {
              formatter: t('aiPortrait.pp_theoretical') + ' {c}',
              color: '#3491fa',
              position: 'insideEndBottom'
            }
          },
          {
            yAxis: 99,
            lineStyle: {
              type: 'dashed',
              color: '#f7ba1e',
              width: 2
            },
            label: {
              formatter: t('aiPortrait.pp_target') + ' {c}',
              color: '#f7ba1e',
              position: 'insideEndTop'
            }
          }
        ]
      }
    }
  ]
})

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  echartRender('lineCharts', lineOption)
}

/**
 * @description: 改变时间重新搜索
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  if (value) {
    lineQuery.start_time = new Date(value[0]).getTime()
    lineQuery.end_time = new Date(value[1]).getTime()
    getLineData()
  }
}

/**
 * @description: 限制时间范围为31天
 * @param {*} val
 * @return {*}
 */
const handleCalendarChange = (val: any) => {
  minDate.value = new Date(val[0]).getTime()
}

/**
 * @description: 获取数据
 * @return {*}
 */
const getLineData = async () => {
  loading.value = true
  lineOption.series[0].data = []
  lineOption.xAxis.data = []
  try {
    const res = await apiGetFttData(activeVersionTab, lineQuery)
    loading.value = false
    if (!res.err_code && res.ftt_trend) {
      lineOption.series[0].data = formatLineYdata(res.ftt_trend)
      lineOption.xAxis.data = formatLineXdata(res.ftt_trend)
      lineOption.series[0].markLine.data[0].yAxis = Math.round(res.theoretical_ftt * 100)
      myChart && myChart.dispose()
      nextTick(() => setCharts())
    }
  } catch (error) {
    ElMessage.error(t('aiPortrait.pp_error'))
  }
}

watch(() => locale.value, (newValue, oldValue) => {
  if(newValue == 'en') {
    lineOption.series[0].markLine!.data[0].label.formatter = 'Theoretical Value {c}'
    lineOption.series[0].markLine!.data[1].label.formatter = 'Target Value {c}'
    getLineData()
  } else {
    lineOption.series[0].markLine!.data[0].label.formatter = '理论值 {c}'
    lineOption.series[0].markLine!.data[1].label.formatter = '目标值 {c}'
    getLineData()
  }
})

onMounted(() => {
  getLineData()
})
</script>
<style lang="scss">
.line-card-container {
  .title {
    font-family: 'Blue Sky Noto';
  }
}
</style>
