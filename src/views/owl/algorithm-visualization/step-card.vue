<template>
  <div class="step-card-container">
    <el-card class="border-radius-8">
      <div class="flex-box flex_j_c-space-between flex_a_i-center">
        <span class="font-size-18 title">{{ $t('aiPortrait.pp_cpu') }}</span>
        <div>
          <el-date-picker :clearable="false" v-model="datePicker" type="date" @change="handleDateChange" :disabled-date="disabledDate" style="width: 400px" />
        </div>
      </div>

      <!-- loading时展示的骨架屏 -->
      <el-skeleton :rows="7" animated v-show="loading" class="margin_t-24" />

      <!-- loading结束且有数据时展示的echarts图像 -->
      <div id="stepCharts" class="width-full height-290 margin_t-10" v-show="!loading && stepOption.series[0].data.length > 0"></div>

      <!-- loading结束且数据为null时展示的暂无数据 -->
      <el-empty class="width-full height-300" :description="`${$t('common.pp_empty')}`" v-if="!loading && stepOption.series[0].data.length === 0"></el-empty>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import {ref, reactive, onMounted, nextTick, watch} from 'vue'
import { useI18n } from 'vue-i18n'
import {apiGetAecData} from '~/apis/owl'
import {ElMessage} from 'element-plus'
import * as echarts from 'echarts'

const props = defineProps({
  activeVersionTab: {
    type: String,
    default: 'PowerSwap2'
  }
})

const activeVersionTab = props.activeVersionTab
const { t } = useI18n()
const { locale } = useI18n({ useScope: 'global' })
const loading = ref(false)
const datePicker = ref(new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24)
const disabledDate = (time: any) => {
  return new Date(time).getTime() >= Date.now() - 3600 * 1000 * 24 || new Date(time).getTime() <= Date.now() - 3600 * 1000 * 24 * 180
}
const lineQuery = reactive({
  day: new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24
})

let myChart: any
const stepOption = reactive({
  animation: false,
  color: ['#00bebe','#3491fa'],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    data: ['max', 'avg'],
    icon: 'circle',
    left: 'left',
    // top: 20
  },
  grid: {
    top: '14%',
    bottom: '10%',
    right: '2%',
    left: '4%'
  },
  xAxis: {
    type: 'category',
    axisTick: {show: false},
    data: ['泊车过程', '鉴权', '下电', '满电电池出舱', '上电', '亏电电池出舱', '车辆驶离', '等待下一单']
  },
  yAxis: {
    type: 'value',
    min: (value: any) => (value.min < 4 ? 0 : Math.floor(value.min) - 4),
    max: (value: any) => ((value.max > 80 && value.max < 96) ? Math.floor(value.max) + 4 : 100),
    axisLabel: {
      margin: 14
    }
  },
  dataZoom: [
    {
      type: 'inside'
    }
  ],
  series: [
    {
      name: 'max',
      data: [] as any,
      step: 'middle',
      type: 'line',
      // label: {
      //   show: true,
      //   position: 'top',
      // },
      markLine: {
        silent: true,
        symbol: ['none', 'none'],
        data: [
          {
            yAxis: 80,
            lineStyle: {
              type: 'solid',
              color: 'red',
              width: 2
            },
            label: {
              formatter: t('aiPortrait.pp_risk') + ' {c}',
              color: 'red',
              position: 'insideEndTop'
            }
          }
        ]
      }
    },
    {
      name: 'avg',
      data: [] as any,
      step: 'middle',
      type: 'line',
    }
  ]
})

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  echartRender('stepCharts', stepOption)
}

/**
 * @description: 改变时间重新搜索
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  if (value) {
    lineQuery.day = new Date(value).getTime()
    getLineData()
  }
}

/**
 * @description: 获取数据
 * @return {*}
 */
const getLineData = async () => {
  loading.value = true
  stepOption.series[0].data = []
  stepOption.series[1].data = []
  try {
    const {err_code, cpu_used} = await apiGetAecData(activeVersionTab, lineQuery)
    loading.value = false
    if (!err_code) {
      for(let key in cpu_used) {
        stepOption.series[0].data.push((cpu_used[key].max).toFixed(2))
        stepOption.series[1].data.push((cpu_used[key].avg).toFixed(2))
      }
      myChart && myChart.dispose()
      nextTick(() => setCharts())
    }
  } catch (error) {
    ElMessage.error(t('aiPortrait.pp_error'))
  }
}

watch(() => locale.value, (newValue, oldValue) => {
  if(newValue == 'en') {
    stepOption.xAxis.data = ['Parking Process','Authentication','Power Off','Fully Charged Battery Unloading','Power On', 'Discharged Battery Unloading', 'Vehicle Departure', 'Waiting for the Next Order']
    stepOption.series[0].markLine!.data[0].label.formatter = 'Risk Threshold {c}'
    getLineData()
  } else {
    stepOption.xAxis.data = ['泊车过程', '鉴权', '下电', '满电电池出舱', '上电', '亏电电池出舱', '车辆驶离', '等待下一单']
    stepOption.series[0].markLine!.data[0].label.formatter = '风险阈值 {c}'
    getLineData()
  }
}, {immediate: true})

onMounted(() => {
  getLineData()
})
</script>
<style lang="scss">
.step-card-container {
  .title {
    font-family: 'Blue Sky Noto';
  }
}
</style>
