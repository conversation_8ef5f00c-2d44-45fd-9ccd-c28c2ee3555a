<template>
  <div class="algorithm-visualization-container">
    <!-- 头部 -->
    <div class="swap-page-header padding_b-10 height-90">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_owl') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_algorithm_visualization') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_algorithm_visualization') }}</div>
      </div>
    </div>

    <!-- tab栏 -->
    <div class="height-40 padding-n-20 tab-container">
      <el-tabs v-model="activeVersionTab">
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="`${$t(item.label)}`" :name="item.name"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 二代站 -->
    <div class="swap-page-container flex-box" v-if="activeVersionTab == 'PowerSwap2'">
      <div class="flex-item_f-1">
        <BarCard :activeVersionTab="activeVersionTab" />
        <br />
        <LineCard :activeVersionTab="activeVersionTab" />
        <br />
        <StepCard :activeVersionTab="activeVersionTab" />
      </div>
      <div class="width-350 margin_l-20">
        <ListCard :activeVersionTab="activeVersionTab" />
      </div>
    </div>

    <!-- 三代站 -->
    <div class="swap-page-container flex-box" v-else>
      <div class="flex-item_f-1">
        <BarCard :activeVersionTab="activeVersionTab" />
        <br />
        <LineCard :activeVersionTab="activeVersionTab" />
        <br />
        <StepCard :activeVersionTab="activeVersionTab" />
      </div>
      <div class="width-350 margin_l-20">
        <ListCard :activeVersionTab="activeVersionTab" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from 'vue'
import { useI18n } from 'vue-i18n'
import BarCard from './bar-card.vue'
import ListCard from './list-card.vue'
import LineCard from './line-card.vue'
import StepCard from './step-card.vue'

const { t } = useI18n()
const activeVersionTab = ref('PowerSwap2')
const tabList = reactive([
  {
    name: 'PowerSwap2',
    label: 'menu.pp_swap_station2'
  },
  {
    name: 'PUS3',
    label: 'menu.pp_swap_station3'
  }
])

</script>
<style lang="scss">
.empty-container {
  height: calc(100% - 130px);
}
</style>