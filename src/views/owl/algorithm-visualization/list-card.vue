<template>
  <div class="list-card-container">
    <span class="font-size-16 title">{{ $t('aiPortrait.pp_last_ftt') }}</span>
    <el-skeleton :rows="25" animated v-if="loading" class="margin_t-25" />
    <el-empty class="width-full height-full" :description="`${$t('common.pp_empty')}`" v-if="!loading && deviceList.length === 0" style="background: #fff;"></el-empty>
    <el-popover placement="bottom" :width="350" trigger="hover" v-else v-for="(item, index) in deviceList" :key="index">
      <template #reference>
        <el-card class="margin_t-10 border-radius-8">
          <div class="height-60 width-full flex-box flex_j_c-space-between">
            <div class="flex-box">
              <div class="flex-box flex_a_i-center font-size-16 margin_r-20 font-weigth index">{{ index + 1 }}</div>
              <div class="flex-box flex_d-column flex_j_c-space-around width-220">
                <span class="device-name font-size-14 ellipse">{{ item.description }}</span>
                <span class="device-id font-size-12">{{ item.device_id }}</span>
              </div>
            </div>
            <div class="flex-box flex_d-column flex_j_c-space-around">
              <span class="pass-rate font-size-14">{{ (item.ftt * 100).toFixed(2) + '%' }}</span>
              <span class="total">{{ item.total }} {{ $t('aiPortrait.pp_order') }}</span>
            </div>
          </div>
        </el-card>
      </template>
      <el-descriptions :column="2">
        <template #title>
          <div class="font-size-14 margin_b-10 description-title">{{ item.description }}</div>
          <div class="font-size-14 description-title">{{ $t('aiPortrait.pp_pass_rate') }}</div>
        </template>
        <el-descriptions-item v-for="description in item.algorithm_success_rates" label-class-name="descriptions-label">
          <template #label>
            <span class="description-name">{{ description.name }}</span>
          </template>
          {{ (description.success_rate * 100).toFixed(2) + '%' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import {ref, reactive, onMounted} from 'vue'
import { useI18n } from 'vue-i18n'
import { apiGetFttData } from '~/apis/owl'
import { ElMessage } from 'element-plus'

const props = defineProps({
  activeVersionTab: {
    type: String,
    default: 'PowerSwap2'
  }
})

const activeVersionTab = props.activeVersionTab
const { t } = useI18n()
const loading = ref(false)
const deviceList = ref([] as any)
const lineQuery = reactive({
  start_time: new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7,
  end_time: new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24,
  update: false,
})

/**
 * @description: 获取数据
 * @return {*}
 */
const getDeviceList = async () => {
  loading.value = true
  try {
    const res = await apiGetFttData(activeVersionTab, lineQuery)
    const { err_code, message } = res
    if(!err_code) {
      loading.value = false
      deviceList.value = res.device_ftt
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    ElMessage.error(t('aiPortrait.pp_error'))
  }
}

onMounted(() => {
  getDeviceList()
})
</script>
<style lang="scss">
.list-card-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .title {
    color: #333333;
    font-family: 'Noto Sans';
    font-weight: bold;
  }
  .index {
    color: #ff8a4a;
    font-weight: bold;
  }
  .device-name {
    color: #275a71;
    display: inline-block;
  }
  .device-id {
    color: #666666;
    display: inline-block;
  }
  .pass-rate {
    color: #ff8a4a;
    font-weight: bold;
    display: inline-block;
  }
  .total {
    color: #6dc8ec;
    font-weight: bold;
    display: inline-block;
  }
  .el-card__body {
    height: 90px;
    padding: 10px;
    display: flex;
    align-items: center;
  }
}
.description-title,
.descriptions-label .description-name {
  color: #275a71;
  font-weight: bold;
}
</style>
