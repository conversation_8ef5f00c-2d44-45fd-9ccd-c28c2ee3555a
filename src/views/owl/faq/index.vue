<!--
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 14:16:14
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-02-02 10:25:12
 * @Email: <EMAIL>
 * @Description: 迁移
-->
<template>
  <content-view class="faq-container" flex="true">
    <page-header title="FAQ" firTitle="OWL" secTitle="FAQ">
      <template #right>
        <search-bar
          placeholder="根据问题搜索"
          v-model="state.question"
          @search="handleSearch"
        />
      </template>
    </page-header>
    <div class="main-container">
      <div class="body-container">
        <faq-card
          class="card-wrapper"
          v-for="(item, index) in qaList"
          :order="index + 1"
          :question="item.question"
          :answer="item.answer"
          :key="index"
        />
      </div>
    </div>
  </content-view>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount } from 'vue';
import { apiFaqFetch } from '~/apis/owl';
import { PageHeader, SearchBar, FaqCard } from '~/views/components';
//owl全局组件
import ContentView from '~/components/owl/content-view';
//定义变量
type ArryType = {
  question: string;
  answer: string;
};
const qaList = ref<ArryType[] | null>();
const state = reactive({ question: '' });
/**
 * @description: 搜索
 * @param {*} value
 * @return {*}
 */
const handleSearch = () => {
  apiQuestionSearch();
};

/**
 * @description: 发送搜索接口
 * @return {*}
 */
const apiQuestionSearch = () => {
  return apiFaqFetch(state).then((res) => {
    qaList.value = res.data;
  });
};
/**
 * @description: 初始化加载
 * @return {*}
 */
onBeforeMount(() => {
  apiQuestionSearch();
});
</script>
<style lang="scss">
.faq-container {
  .main-container {
    // height: calc(100% - 60px);
    background-color: #fff;
    // margin: 120px 20px 50px 20px;
    margin: 20px;
    width: auto;
    .body-container {
      border-color: #e8eef2;
      // width: 100%;
      margin-top: 10px;
      // height: 100%;
      // .card-wrapper:first-child {
      //   margin-top: 20px;
      // }
    }
  }
}
</style>
