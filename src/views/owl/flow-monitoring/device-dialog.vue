<template>
  <div class="device-dialog-container">
    <el-dialog :title="$t('flowMonitoring.pp_used_site')" v-model="dialogVisible" width="740px" align-center @close="handleClose" @open="handleOpen">
      <el-table :data="list" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" :cell-style="{ color: '#262626' }" v-loading="loading">
        <el-table-column prop="id" label="No." width="50" show-overflow-tooltip />
        <el-table-column prop="device_id" :label="$t('stuckAnalysis.pp_device_id')" min-width="250">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('parkingOccupancy.pp_device_name')" min-width="250">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.description || $t('common.pp_unnamed_device')" />
          </template>
        </el-table-column>
        <el-table-column prop="duration_day" :label="$t('flowMonitoring.pp_duration')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.duration_day }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="margin_t-20 flex-box flex_j_c-flex-end">
        <Page :page="pages" :pagerCount="5" @change="handlePageChange" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { page } from '~/constvars/page'
import { ElMessage } from 'element-plus'
import { apiGetDevices } from '~/apis/owl/flowMonitoring'
import _ from 'lodash'

const props = defineProps({
  dialogVisible: Boolean,
  project: String
})

const emits = defineEmits(['update:dialogVisible'])

const loading = ref(false)
const list = ref([] as any)
const pages = ref(_.cloneDeep(page))

/**
 * @description: 获取数据
 * @return {*}
 */
const getList = async () => {
  const params = {
    page: pages.value.current,
    size: pages.value.size
  }
  loading.value = true
  try {
    const res = await apiGetDevices(params, props.project)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data || []
      pages.value.total = res.total
    }
  } catch (error) {
    loading.value = false
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 打开
 * @return {*}
 */
const handleOpen = () => {
  pages.value.current = 1
  getList()
}

/**
 * @description: 关闭
 * @return {*}
 */
const handleClose = () => {
  emits('update:dialogVisible', false)
}
</script>

<style lang="scss" scoped>
.device-dialog-container {
  :deep(.el-dialog__headerbtn .el-dialog__close) {
    font-size: 20px;
  }
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
  }
  :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 420;
    line-height: 26px;
    color: #1f1f1f;
  }
}
</style>