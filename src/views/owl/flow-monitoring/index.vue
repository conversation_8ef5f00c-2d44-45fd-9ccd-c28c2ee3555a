<template>
  <div class="flow-monitoring-container">
    <div class="header-container">
      <div class="font-size-20 font-weight-bold margin_b-11" style="color: #1f1f1f">{{ $t('menu.pp_flow_monitoring') }}</div>
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="$t(item.label)" :name="item.name"></el-tab-pane>
      </el-tabs>
    </div>

    <div class="content-container">
      <el-row :gutter="20">
        <el-col :span="17">
          <div class="left-content">
            <el-form :model="form" inline class="margin_b-2">
              <el-form-item :label="$t('flowMonitoring.pp_time')">
                <el-date-picker v-model="form.datePicker" type="daterange" unlink-panels value-format="x" :clearable="false" :start-placeholder="$t('common.pp_start_time')" :end-placeholder="$t('common.pp_end_time')" :shortcuts="getShortcuts()" :disabledDate="getDisabledDate">
                  <template #range-separator>
                    <TimeRangeIcon />
                  </template>
                </el-date-picker>
              </el-form-item>
              <el-form-item label="failure">
                <el-select v-model="form.failure_value" clearable :placeholder="$t('common.pp_please_select')">
                  <el-option :value="1" :label="$t('common.pp_yes')" />
                  <el-option :value="2" :label="$t('common.pp_no')" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button @click="handleClickSearch" class="welkin-primary-button" :loading="leftLoading">{{ $t('common.pp_search') }}</el-button>
              </el-form-item>
            </el-form>
            <el-skeleton :rows="15" animated v-if="leftLoading" />
            <div class="left-card-container" v-else-if="!leftLoading && leftList.length > 0">
              <div v-for="item in leftList" class="left-card">
                <div class="algorithm-name">{{ item.algorithm }}</div>
                <div class="flex-box flex_d-column flex_a_i-flex-end gap_10">
                  <span class="algorithm-value"
                    >{{ $t('flowMonitoring.pp_upload_volume') }}：<span style="color: #262626; font-weight: 500; font-size: 14px">{{ fileSizeCal(item.upload_total) }}</span></span
                  >
                  <span class="algorithm-value"
                    >{{ $t('flowMonitoring.pp_usage_rate') }}：<span style="color: #262626; font-weight: 500; font-size: 14px">{{ (item.usage_rate * 10000).toFixed(2) + '‱' }}</span></span
                  >
                </div>
              </div>
            </div>
            <el-empty class="width-full height-full" :description="$t('common.pp_empty')" v-else />
          </div>
        </el-col>

        <el-col :span="7">
          <div class="font-size-16 font-weight-500 margin_b-8" style="color: #262626" v-if="rightLoading">{{ $t('flowMonitoring.pp_used_site') }}</div>
          <el-skeleton :rows="16" animated v-if="rightLoading" />
          <div v-else style="padding: 16px; border-radius: 4px; background: linear-gradient(180deg, #c6efef, #e5f9f9)">
            <div class="flex-box flex_j_c-space-between margin_b-8">
              <span class="font-size-16 font-weight-500" style="color: #262626">{{ $t('flowMonitoring.pp_used_site') }} {{ '（' + t('flowMonitoring.pp_total') + totalNum + '）' }}</span>
              <span class="font-size-14 cursor-pointer" style="color: #00bebe" @click="dialogVisible = true">{{ $t('stuckAnalysis.pp_more') }}</span>
            </div>
            <div style="height: calc(100vh - 252px); overflow-y: auto" v-if="rightList.length > 0">
              <div class="flex-box flex_d-column gap_8">
                <el-card class="list-card" v-for="item in rightList">
                  <div class="flex-box flex_a_i-center flex_j_c-space-between" style="color: #434343">
                    <div class="flex-box flex_a_i-center">
                      <span class="font-weight-480 font-weight-450">{{ item.id }}</span>
                      <VerticalLine class="margin-n-8" />
                      <div>
                        <div class="margin_b-6 font-weight-450">{{ item.description ? item.description : $t('common.pp_unnamed_device') }}</div>
                        <div class="font-size-13" style="color: #8c8c8c">{{ item.device_id }}</div>
                      </div>
                    </div>
                    <span class="font-weight-480 white-space-nowrap">{{ item.duration_day + t('flowMonitoring.pp_day') }}</span>
                  </div>
                </el-card>
              </div>
            </div>
            <el-empty style="height: calc(100vh - 252px); overflow-y: auto" :description="$t('common.pp_empty')" v-else />
          </div>
        </el-col>
      </el-row>
    </div>

    <DeviceDialog v-model:dialogVisible="dialogVisible" :project="activeTab" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { getDisabledDate, getFinalEndTime, fileSizeCal } from '~/utils'
import { initStartTime, initEndTime } from './constant'
import { apiGetStats, apiGetDevices } from '~/apis/owl/flowMonitoring'
import { ElMessage } from 'element-plus'
import DeviceDialog from './device-dialog.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import VerticalLine from '~/assets/svg/vertical-line.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const totalNum = ref(0)
const leftLoading = ref(false)
const rightLoading = ref(false)
const dialogVisible = ref(false)
const activeTab = ref('PowerSwap2')
const form = ref({
  datePicker: [initStartTime, initEndTime] as any,
  failure_value: '' as number | string
})
const tabList = ref([
  {
    name: 'PowerSwap2',
    label: 'menu.pp_swap_station2'
  },
  {
    name: 'PUS3',
    label: 'menu.pp_swap_station3'
  },
  {
    name: 'PUS4',
    label: 'menu.pp_swap_station4'
  }
])
const leftList = ref([] as any)
const rightList = ref([] as any)

const getShortcuts = () => {
  return [
    {
      text: t('common.pp_last_week'),
      value: [initStartTime, initEndTime]
    },
    {
      text: t('common.pp_last_month'),
      value: [initStartTime - 3600 * 1000 * 24 * 23, initEndTime]
    }
  ]
}

/**
 * @description: 查询数据量统计数据
 * @return {*}
 */
const getLeftList = async (updateRoute = true) => {
  const params = {
    start_time: form.value.datePicker[0],
    end_time: getFinalEndTime(form.value.datePicker[1]),
    failure_value: form.value.failure_value ? Number(form.value.failure_value) : 0
  }
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...params, project: activeTab.value }
    })
  }
  leftLoading.value = true
  try {
    const res = await apiGetStats(params, activeTab.value)
    leftLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      leftList.value = res.data || []
    }
  } catch (error) {
    leftLoading.value = false
  }
}

/**
 * @description: 查询流量用完站点名单
 * @return {*}
 */
const getRightList = async () => {
  const params = {
    page: 1,
    size: 10
  }
  rightLoading.value = true
  try {
    const res = await apiGetDevices(params, activeTab.value)
    rightLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      rightList.value = res.data
      totalNum.value = res.total
    }
  } catch (error) {
    rightLoading.value = false
  }
}

/**
 * @description: 点击搜索
 * @return {*}
 */
const handleClickSearch = () => {
  getLeftList()
}

/**
 * @description: 切换Tab
 * @return {*}
 */
const handleTabChange = () => {
  getLeftList()
  getRightList()
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    const start = Number(initParams.start_time)
    const end = Number(initParams.end_time)
    form.value.datePicker = [start, end]
  }
  form.value.failure_value = initParams.failure_value == 1 || initParams.failure_value == 2 ? Number(initParams.failure_value) : ''
  activeTab.value = !!initParams.project ? initParams.project : 'PowerSwap2'
  getLeftList(false)
  getRightList()
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.flow-monitoring-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .header-container {
    padding: 24px 24px 0px;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 15px;
      font-weight: 400;
      color: #595959;
    }
  }
  .content-container {
    flex: 1;
    padding: 20px 24px;
    background: #f8f8f8;
    :deep(.el-form--inline .el-form-item) {
      margin-right: 24px;
      .el-form-item__label {
        color: #595959;
        padding-right: 10px;
      }
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-date-editor .el-range-input) {
      color: #262626;
    }
    :deep(.el-card.is-always-shadow) {
      border-color: rgba(220, 242, 243, 1);
      font-size: 14px;
    }
    :deep(.list-card:hover) {
      border-color: #00bebe;
    }
    :deep(.list-card .el-card__body) {
      padding: 15px;
    }
    .left-content {
      height: 100%;
      border: 1px solid #dcf2f3;
      border-radius: 4px;
      background: #fff;
      padding: 24px;
      .left-card-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 16px;
        max-height: calc(100vh - 290px);
        overflow-y: auto;
        .left-card {
          border: 1px solid #dcf2f3;
          border-radius: 4px;
          padding: 12px 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          &:hover {
            border: 1px solid #00bebe;
          }
          .algorithm-name {
            color: #1f1f1f;
            font-size: 24px;
            line-height: 32px;
            font-weight: 500;
          }
          .algorithm-value {
            font-size: 13px;
            color: #8c8c8c;
          }
        }
      }
    }
  }
}
::-webkit-scrollbar {
  width: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
</style>
