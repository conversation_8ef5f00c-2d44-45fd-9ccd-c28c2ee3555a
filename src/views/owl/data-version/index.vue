<template>
  <div class="data-version-outside">
    <page-header title="数据版本" firTitle="OWL" secTitle="数据版本">
      <template #right>
        <el-button
          class="welkin-primary-button"
          v-if="state.hasPermission"
          @click="createManageData()"
        >
          创建质量管理数据集</el-button
        >
      </template>
    </page-header>
    <div class="data-version-container">
      <div class="data-version-filter-container">
        <el-form inline :model="searchQuery" label-width="90px">
          <el-form-item label="算法名称">
            <el-select
              v-model="searchQuery.algorithm_name"
              placeholder="请选择"
              collapse-tags
              collapse-tags-tooltip
              multiple
              filterable
              clearable
            >
              <el-option
                v-for="(option, index) in algorithmNameOptions"
                :key="index"
                :label="option"
                :value="option"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发布时间">
            <el-select
              v-model="searchQuery.publish_ts"
              placeholder="请选择"
              collapse-tags
              collapse-tags-tooltip
              multiple
              filterable
              clearable
            >
              <el-option
                v-for="(option, index) in tsOptions"
                :key="index"
                :label="option"
                :value="option"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="质量管理" v-if="state.hasPermission">
            <el-select
              v-model="searchQuery.is_failure"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(option, index) in failureOptions"
                :key="index"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="">
            <div class="search-button">
              <el-button
                @click="handleSearchEvent()"
                class="welkin-primary-button"
                :loading="loading"
                >搜索</el-button
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="data-version-table">
        <el-table :data="tableData" style="width: 100%" v-loading="loading">
          <el-table-column
            v-if="state.hasPermission"
            width="90"
            label="质量管理"
          >
            <template #default="{ row }">
              <el-icon v-if="row.has_qm_dataset"><Link /></el-icon>
              <span style="padding-left: 10px" v-else> </span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(option, index) in tableColumnSet"
            :key="index"
            :prop="option.name"
            :label="option.label"
            :width="option.width"
            :formatter="option.formatter"
          >
          </el-table-column>
          <el-table-column fixed="right" width="90" label="操作">
            <template #default="{ row }">
              <div v-if="row.has_qm_dataset && state.hasPermission">
                <el-icon
                  class="cursor-hand"
                  v-if="
                    row.if_can_edit &&
                    hasSingleAlgorithm(state.conditions, row.algorithm_name)
                  "
                  @click="handleUpload(row)"
                >
                  <UploadFilled style="color: #00bebe" />
                </el-icon>
                <el-icon
                  v-if="row.file_info && row.file_info.length > 0"
                  class="cursor-hand"
                  :disabled="true"
                  @click="handleDownload(row)"
                >
                  <View style="color: #00bebe" />
                </el-icon>
              </div>
              <div v-else>
                <el-icon
                  class="cursor-hand"
                  v-if="
                    row.if_can_edit &&
                    !row.has_qm_dataset &&
                    hasSingleAlgorithm(state.conditions, row.algorithm_name)
                  "
                  @click="handleUpload(row)"
                >
                  <UploadFilled style="color: #00bebe" />
                </el-icon>
                <el-icon
                  v-if="row.file_info && row.file_info.length > 0"
                  class="cursor-hand"
                  :disabled="true"
                  @click="handleDownload(row)"
                >
                  <View style="color: #00bebe" />
                </el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-part" v-if="tableData.length > 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          v-model:currentPage="searchQuery.page_no"
          v-model:page-size="searchQuery.page_size"
          background
          layout="sizes, prev, pager, next"
          :total="totalNum"
          :page-sizes="pagination.pageSizes"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog
      v-model="dialogVisible"
      align-center
      :title="dialogTitle"
      @close="uploadVersion.clearFiles()"
      width="30%"
      draggable
    >
      <el-form
        ref="creatFormRef"
        label-position="left"
        :rules="rules"
        :model="dialogForm"
      >
        <el-form-item label="算法名称" prop="algorithm_name" :label-width="80">
          <el-input
            v-model="dialogForm.algorithm_name"
            :disabled="state.uploadDisabled"
            placeholder="请输入算法名称"
          />
        </el-form-item>
        <el-form-item
          label="算法版本"
          prop="algorithm_version"
          :label-width="80"
        >
          <el-input
            v-model="dialogForm.algorithm_version"
            :disabled="state.uploadDisabled"
            placeholder="请输入算法版本"
          />
        </el-form-item>
        <el-form-item label="发布日期" prop="publish_ts" :label-width="80">
          <el-date-picker
            v-model="dialogForm.publish_ts"
            style="width: 100%"
            :disabled="state.uploadDisabled"
            type="date"
            placeholder="请选择时间"
          />
        </el-form-item>

        <el-form-item label="附件" style="padding-left: 40px" prop="file">
          <el-upload
            ref="uploadVersion"
            style="width: 100%"
            action=""
            :limit="10"
            :on-change="handleChangeed"
            multiple
            :auto-upload="false"
          >
            <template #trigger>
              <el-button class="welkin-secondary-button">选择文件</el-button>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button
            class="welkin-text-button"
            @click="
              dialogVisible = false;
              uploadVersion.clearFiles();
            "
            >取消</el-button
          >
          <el-button
            class="welkin-primary-button"
            :loading="state.buttonLoading"
            @click="submitDataMap(creatFormRef)"
            style="margin-left: 4px"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="dialogFileVisible"
      align-center
      :close-on-click-modal="false"
      title="文件上传显示"
      width="30%"
      draggable
    >
      <div v-if="dialogForm.file">
        <div
          class="upload-item-progress"
          v-for="(item, index) in dialogForm.file"
          :key="index"
        >
          <div>
            <el-tooltip :content="item.name" placement="top">
              <span class="file-name">{{ item.name }}</span></el-tooltip
            >
          </div>
          <div>
            <el-progress :percentage="item.percentage" :status="item.statu" />
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            v-if="!state.uploadDisabled"
            class="welkin-primary-button"
            @click="dialogFileVisible = false"
            >确定</el-button
          >
          <el-button
            class="welkin-primary-button"
            :disabled="!state.sucessFile"
            :loading="state.buttonLoading"
            @click="handleSureFileUpload(creatFormRef)"
            v-else
          >
            数据集上传完成
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="fileDialogVisible"
      align-center
      :close-on-click-modal="false"
      @close="state.sucessFile = false"
      title="文件列表"
      width="500px"
    >
      <el-table :data="state.currFileList">
        <el-table-column
          v-for="(option, index) in fileTableColumnSet"
          :key="index"
          :prop="option.name"
          :label="option.label"
          :width="option.width"
          :show-overflow-tooltip="option.showTooltip"
          :formatter="option.formatter"
        />
        <el-table-column width="80" align="center" label="操作">
          <template #default="scope">
            <span v-if="state.has_qm_dataset && state.hasPermission">
              <el-icon
                class="cursor-hand"
                style="cursor: pointer; margin-left: 5px"
                v-if="
                  state.if_can_edit &&
                  hasSingleAlgorithm(state.conditions, state.cur_algorithm_name)
                "
              >
                <Delete style="color: #00bebe" @click="delFile(scope.row)" />
              </el-icon>

              <el-icon
                class="cursor-hand"
                style="cursor: pointer"
                v-if="
                  hasSingleAlgorithm(state.conditions, state.cur_algorithm_name)
                "
                @click="handleSingleDownload(scope.row)"
              >
                <Download style="color: #00bebe" />
              </el-icon>
            </span>
            <span v-else>
              <el-icon
                class="cursor-hand"
                style="cursor: pointer; margin-left: 5px"
                v-if="
                  state.if_can_edit &&
                  !state.has_qm_dataset &&
                  hasSingleAlgorithm(state.conditions, state.cur_algorithm_name)
                "
              >
                <Delete style="color: #00bebe" @click="delFile(scope.row)" />
              </el-icon>

              <el-icon
                class="cursor-hand"
                style="cursor: pointer"
                v-if="
                  !state.has_qm_dataset &&
                  hasSingleAlgorithm(state.conditions, state.cur_algorithm_name)
                "
                @click="handleSingleDownload(scope.row)"
              >
                <Download style="color: #00bebe" />
              </el-icon>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer" v-if="state.currFileList.length > 1">
          <el-button
            class="welkin-primary-button"
            v-if="
              state.if_can_edit &&
              !state.has_qm_dataset &&
              hasSingleAlgorithm(state.conditions, state.cur_algorithm_name)
            "
            @click="handleAllDownLoad"
          >
            全部下载
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onBeforeMount } from 'vue';
import type { FormInstance } from 'element-plus';
import {
  Download,
  UploadFilled,
  Link,
  View,
  Delete,
} from '@element-plus/icons-vue';
import { PageHeader } from '~/views/components';
import { ElMessage, ElMessageBox } from 'element-plus';
import { hasPermission, hasSingleAlgorithm } from '~/auth';

import {
  apiQmDataSet,
  apiDataSetConfirm,
  apiUploadDataSet,
  apiTableHeadFetch,
  apiPublistTsFetch,
  apiDataVersionFetch,
  apiDelFileFetch,
} from '~/apis/owl';
import { formatLocaleDate, formatLocaleDay, fileSizeCal } from '~/utils';
import { useStore } from 'vuex';
import { pagination } from '~/constvars';

const dialogVisible = ref(false);
const dialogFileVisible = ref(false);
const fileDialogVisible = ref(false);
const creatFormRef = ref<FormInstance>();
const uploadVersion = ref();
const $store = useStore();
//定义响应式数据
const state = reactive({
  tableData: [] as any,
  tableColumnSet: [] as any,
  fileTableColumnSet: [] as any,
  algorithmNameOptions: [] as any,
  tsOptions: [] as any,
  buttonLoading: false,
  sucessFile: false,
  totalNum: 0,
  loading: false,
  uploadDisabled: false,
  currFileList: [] as any,
  currFilePublishTs: '',
  if_can_edit: false,
  has_qm_dataset: false,
  cur_algorithm_name: '',
  dialogTitle: '创建质量管理数据集',
  conditions: $store.state.menus.conditions,
  hasPermission: hasPermission('data-version:quality-data'),
  searchQuery: {
    algorithm_name: [],
    publish_ts: [],
    is_failure: 0,
    page_no: 1,
    page_size: 10,
  } as any,
  failureOptions: [
    {
      label: '全部',
      value: 0,
    },
    {
      label: '是',
      value: 1,
    },
    {
      label: '否',
      value: 2,
    },
  ],
  rules: {
    algorithm_name: [
      {
        required: true,
        message: '请输入算法名称',
        trigger: 'blur',
      },
    ],
    algorithm_version: [
      {
        required: true,
        message: '请输入算法版本',
        trigger: 'blur',
      },
    ],
    publish_ts: [
      {
        required: true,
        message: '请选择时间',
        trigger: 'blur',
      },
    ],
  } as any,
  dialogForm: {
    algorithm_name: '',
    has_qm_dataset: false,
    algorithm_version: '',
    publish_ts: '',
    file: null as any,
    id: '',
  },
});
/**
 * @description: 创建数据质量管理集
 * @return {*}
 */
const createManageData = () => {
  dialogVisible.value = true;
  state.uploadDisabled = false;
  state.dialogTitle = '创建数据集';
  state.dialogForm.algorithm_name = '';
  state.dialogForm.algorithm_version = '';
  state.dialogForm.publish_ts = '';
};

//定义响应式数据结束
const handleSearchEvent = () => {
  state.searchQuery.page_no = 1;
  state.searchQuery.page_size = 10;
  handleSearch();
};

/**
 * @description: 获取文件
 * @param {*} files
 * @return {*}
 */
const handleChangeed = (_: any, filesList: any) => {
  state.dialogForm.file = filesList;
};

/**
 * @description: 统计所有文件数量
 * @return {*}
 */
const cumputerFileSucess = () => {
  let sucess_number = 0;
  state.dialogForm.file.map((item: any) => {
    if (item.statu) {
      ++sucess_number;
    }
  });
  return sucess_number == state.dialogForm.file.length;
};

/**
 * @description: 提交数据
 * @return {*}
 */
const submitDataMap = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      if (state.dialogForm.file) {
        state.dialogForm.file.forEach((item: any, index: number) => {
          item.statu = '';
          item.index = index;
          const paramsData = new FormData();
          const dateTime: any = new Date(state.dialogForm.publish_ts).getTime();
          paramsData.append('algorithm_name', state.dialogForm.algorithm_name);
          paramsData.append(
            'algorithm_version',
            state.dialogForm.algorithm_version
          );
          paramsData.append('publish_ts', dateTime);
          paramsData.append('file', state.dialogForm.file[index].raw);
          if (state.uploadDisabled) {
            paramsData.append('id', state.dialogForm.id);
            apiUploadDataSet(paramsData, (progressEvent: any) => {
              let percent =
                (
                  ((progressEvent.loaded - 20) / progressEvent.total) *
                  100
                ).toFixed() || 0;
              Number(percent) >= 100 ? (percent = 99) : percent;
              item.percentage = percent;
            })
              .then(() => {
                item.statu = 'success';
                state.sucessFile = cumputerFileSucess();
              })
              .catch(() => {
                item.statu = 'exception';
              });
          } else {
            apiQmDataSet(paramsData, (progressEvent: any) => {
              let percent =
                (
                  ((progressEvent.loaded - 20) / progressEvent.total) *
                  100
                ).toFixed() || 0;
              Number(percent) >= 100 ? (percent = 99) : percent;
              item.percentage = percent;
            })
              .then(() => {
                item.statu = 'success';
                state.sucessFile = cumputerFileSucess();
              })
              .catch(() => {
                item.statu = 'exception';
              });
          }
        });
        dialogVisible.value = false;
        dialogFileVisible.value = true;
      }
    }
  });
};

/**
 * @description: 文件二次确定同步版本号管理
 * @return {*}
 */
const handleSureFileUpload = async (formEl: any) => {
  const params = {
    id: state.dialogForm.id,
  };
  const defaultDialogForm = {
    algorithm_name: '',
    has_qm_dataset: false,
    algorithm_version: '',
    publish_ts: '',
    file: null as any,
    id: '',
  };
  if (state.dialogForm.has_qm_dataset) {
    dialogFileVisible.value = false;
    state.dialogForm = { ...defaultDialogForm };
  } else {
    state.buttonLoading = true;
    try {
      const res = await apiDataSetConfirm(params);
      state.sucessFile = false;
      state.buttonLoading = false;
      handleSearch();
      const { err_code, message } = res;
      if (err_code) {
        ElMessage.error(message);
      }
      if (uploadVersion && uploadVersion.value) {
        uploadVersion.value.clearFiles();
      }
      state.dialogForm = { ...defaultDialogForm };
      dialogFileVisible.value = false;
    } catch (error) {
      state.buttonLoading = false;
      state.sucessFile = false;
    }
  }
};

/**
 * @description: 查询列表
 * @return {*}
 */
const handleSearch = async () => {
  state.loading = true;
  const query = state.searchQuery;
  let algorithms = '';
  if (query.algorithm_name.length) {
    algorithms = query.algorithm_name.join(',');
  }
  const params = {
    algorithms: algorithms,
    algorithm_version: query.algorithm_version,
    publish_ts: '' as any,
    page: query.page_no,
    size: query.page_size,
    if_qm: query.is_failure,
  };
  if (query.publish_ts && query.publish_ts.length > 0) {
    const ts_list = query.publish_ts.map((item: any) => {
      return new Date(item).getTime();
    });
    params.publish_ts = ts_list.join(',');
  }
  const res = await apiDataVersionFetch(params);
  state.loading = false;
  const { err_code, data, message, total } = res;
  if (!err_code) {
    if (data) {
      state.tableData = data;
    } else {
      state.tableData = [];
    }
    state.totalNum = total;
  } else {
    ElMessage.error(message);
  }
};

/**
 * @description: 下载文件
 * @param {*} row
 * @return {*}
 */
const handleDownload = (row: any) => {
  console.log(row);
  const fileList = row.file_info;
  state.currFilePublishTs = row.publish_ts;
  state.if_can_edit = row.if_can_edit;
  state.has_qm_dataset = row.has_qm_dataset;
  state.cur_algorithm_name = row.algorithm_name;
  if (fileList) {
    fileDialogVisible.value = true;
    state.currFileList = fileList;
  }
};

/**
 * @description: 单个文件下载
 * @param {*} row
 * @return {*}
 */
const handleSingleDownload = (row: any) => {
  window.open(row.file_url);
};

const downLoadFile = (file_url: any) => {
  setTimeout(() => {
    window.open(file_url);
  }, 500);
};

/**
 * @description: 删除文件
 * @param {*} row
 * @return {*}
 */
const delFile = (row: any) => {
  ElMessageBox.confirm('您确定要删除?', '提醒', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const params = {
        publish_ts: state.currFilePublishTs,
        file_url: row.file_url,
      };
      const res = await apiDelFileFetch(params);
      fileDialogVisible.value = false;
      const { err_code, message } = res;
      if (!err_code) {
        ElMessage.success('删除成功！');
        handleSearch();
      } else {
        ElMessage.error(message);
      }
    })
    .catch((e) => {
      console.log(e);
    });
};

/**
 * @description: 下载所有文件
 * @return {*}
 */
const handleAllDownLoad = () => {
  const list = state.currFileList;
  list.map((item: any) => {
    downLoadFile(item.file_url);
  });
};

/**
 * @description: 上传数据集
 * @return {*}
 */
const handleUpload = (row: any) => {
  dialogVisible.value = true;
  state.uploadDisabled = true;
  state.dialogForm.algorithm_name = row.algorithm_name;
  state.dialogForm.algorithm_version = row.algorithm_version;
  state.dialogForm.publish_ts = row.publish_ts;
  state.dialogForm.has_qm_dataset = row.has_qm_dataset;
  state.dialogForm.id = row.id;
  state.dialogTitle = '上传数据集';
};

const handleSizeChange = (val: any) => {
  state.searchQuery.page_size = val;
  state.searchQuery.page_no = 1;
  handleSearch();
};
//当前页变更更新内容
const handleCurrentChange = (val: any) => {
  state.searchQuery.page_no = val;
  handleSearch();
};
const initTableColumns = () => {
  state.tableColumnSet = [
    {
      name: 'algorithm_name',
      label: '算法名称',
      width: 150,
    },
    {
      name: 'algorithm_version',
      label: '算法版本',
      width: 150,
    },
    {
      name: 'publish_ts',
      label: '发布时间',
      width: 150,
      formatter: (row: any) =>
        row.publish_ts ? formatLocaleDay(row.publish_ts) : '-',
    },
    {
      name: 'create_ts',
      label: '创建时间',
      formatter: (row: any) =>
        row.create_ts ? formatLocaleDate(row.create_ts, false) : '-',
    },
    {
      name: 'size',
      label: '大小',
      width: 150,
      formatter: (row: any) =>
        row.total_size ? fileSizeCal(row.total_size) : '-',
    },
  ];
  state.fileTableColumnSet = [
    {
      name: 'file_name',
      label: '文件名称',
      showTooltip: true,
    },
    {
      name: 'size',
      label: '大小',
      width: 120,
      showTooltip: false,
      formatter: (row: any) =>
        row.file_size ? fileSizeCal(row.file_size) : '-',
    },
  ];
};

/**
 * @description: 获取算法下拉框选项
 * @return {*}
 */
const getAlgorithmNameOptions = async () => {
  const res = await apiTableHeadFetch();
  const { err_code, data, message } = res;
  if (!err_code) {
    state.algorithmNameOptions = data;
  } else {
    ElMessage.error(message);
  }
};

/**
 * @description: 获取时间下拉框
 * @return {*}
 */
const getTsOptions = async () => {
  const res = await apiPublistTsFetch();
  const { err_code, data, message } = res;
  if (!err_code) {
    const formatDayList = data.map((item: any) => formatLocaleDay(item));
    state.tsOptions = formatDayList;
  } else {
    ElMessage.error(message);
  }
};

onBeforeMount(() => {
  initTableColumns();
  getTsOptions();
  getAlgorithmNameOptions();
  handleSearch();
});

//解构
const {
  loading,
  tableData,
  tableColumnSet,
  fileTableColumnSet,
  searchQuery,
  algorithmNameOptions,
  tsOptions,
  rules,
  dialogTitle,
  failureOptions,
  totalNum,
  dialogForm,
} = toRefs(state);
</script>
<style lang="scss" scoped>
.data-version-outside {
  // overflow-y: auto;
  background-color: #f8f8fa;
}
.data-version-file-item {
  margin-bottom: 10px;
}
.data-version-container {
  // margin: 120px 20px 0px 20px;
  margin: 20px;
}
.upload-item-progress {
  margin-top: 5px;
  .file-name {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.data-version-filter-container {
  background-color: #fff;
  vertical-align: middle;
  padding-top: 18px;
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    // width: 398px;
  }
  .el-input__icon {
    height: 32px;
    line-height: 32px;
  }
  .el-form-item__label {
    line-height: 32px;
    padding: 0 16px 0 0;
    font-family: 'Blue Sky Standard';
  }
  .el-date-editor .el-range-separator {
    line-height: 25px;
  }
  // .el-date-editor--datetimerange.el-input__inner {
  //   width: 350px;
  // }
  .multiselect__tags {
    line-height: 32px;
    height: 32px;
    min-height: 32px;
    padding: 0 40px 0 15px;
    overflow: auto;
  }
  .multiselect__select {
    height: 32px;
  }
  .multiselect__placeholder {
    margin-bottom: 0;
    padding-top: 0;
  }
  .multiselect__input,
  .multiselect__single {
    min-height: 32px;
    line-height: 32px;
  }
  .multiselect__option--selected {
    background: #9abcff;
    color: #ffffff;
    font-weight: 400;
  }
  .multiselect__option--highlight {
    content: attr(data-select);
    background: linear-gradient(
        0deg,
        rgba(44, 115, 255, 0.1),
        rgba(44, 115, 255, 0.1)
      ),
      #ffffff;
    color: #363c54;
  }
  .multiselect__tag {
    margin-right: 2px;
    margin-bottom: 0;
    margin-top: 4px;
    background: #f8f8fa;
    color: #4b525f;
    width: 110px;
  }
  .multiselect__tag-icon:hover {
    background: #2c73ff;
  }
  .camera {
    .multiselect {
      width: 350px;
    }
  }
  .search-button {
    // width: 100px;
    text-align: right;
  }
}
.data-version-footer {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding-bottom: 20px;
  .total-number {
    padding-left: 33px;
    font-size: 14px;
    line-height: 40px;
    font-family: 'Blue Sky Standard';
  }
}
.file-total-size {
  height: 40px;
  line-height: 40px;
  font-weight: 400;
  font-size: 14px;
  margin-left: 10px;
  margin-right: 20px;
}
.data-version-table {
  margin: 20px 0 0 0;
  // height: 100%;
  background-color: #fff;
  .cursor-hand {
    cursor: pointer;
    margin-right: 10px;
    color: #4b525f;
  }
  .el-table thead {
    color: #4b525f;
    font-family: 'Blue Sky Standard';
  }
  .el-table th.el-table__cell {
    // background: #f8f8fa;
    border-style: none;
  }
  .el-table {
    color: #4b525f;
    font-family: 'Blue Sky Standard';
  }
}
.pagination-part {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  background-color: #fff;
}
.portal-dialog {
  .el-dialog__body {
    display: flex;
    justify-content: center;
    padding-top: 10px;
    .el-carousel {
      width: 1000px;
      margin: 0 auto;
      .el-carousel__arrow i {
        font-size: 40px;
      }
      .title-div {
        margin-bottom: 10px;
        .title-info {
          margin-right: 50px;
        }
      }
    }
  }
}
</style>
