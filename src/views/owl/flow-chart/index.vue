<template>
  <content-view class="flow-chart-container" flex="true">
    <page-header title="算法流程" firTitle="OWL" secTitle="算法流程">
      <template #right>
        <div v-if="hasPermission('owl:flow-chart:add')">
          <el-button class="welkin-primary-button" @click="handleFlowChartClicked"
            >上传流程图</el-button
          >
        </div>
      </template>
    </page-header>
    <div class="body-container">
      <div class="dotted-line-container">
        <div v-if="flowChartApi === ''">
          <!-- <img class="empty-figure" src="/empty.svg" alt="empty figure" /> -->
          <div class="empty-text">暂无流程图</div>
        </div>
        <div v-else style="width: 100%; height: 100%">
          <img
            alt="flowchartFigure"
            :src="flowChartApi"
            style="width: 100%; height: 100%; object-fit: fill"
          />
        </div>
      </div>
    </div>
    <el-dialog
      title="上传流程图"
      v-model="uploadDialogVisible"
      :show-close="false"
      class="upload-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div style="margin-top: 10px">
        <el-upload
          action=""
          :auto-upload="false"
          drag
          :on-change="handleFileChange"
        >
          <div class="upload-body">
            <div class="upload-font">将文件拖到此处，或</div>
            <div class="click-upload">点击上传</div>
          </div>
        </el-upload>
      </div>
      <div class="flex-box flex_j_c-flex-end margin_t-20">
        <el-button @click="handleCancelClicked" class="welkin-text-button">取消</el-button>
        <el-button class="welkin-primary-button" @click="handleComfirmClicked">提交</el-button>
      </div>
    </el-dialog>
  </content-view>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue';
import { PageHeader } from '~/views/components';
import { apiFlowChartUpload, apiAlgorithmReview } from '~/apis/owl';
import { hasPermission } from '~/auth';
//全局组件
import ContentView from '~/components/owl/content-view';

const uploadDialogVisible = ref(false);
const flowChartApi = ref('');
const file = ref('');
/**
 * @description: 展示上传图片弹窗
 * @return {*}
 */
const handleFlowChartClicked = () => {
  uploadDialogVisible.value = true;
};

/**
 * @description: 文件change监听
 * @param {*} files 二进制文件
 * @return {*}
 */
const handleFileChange = (files: any) => {
  file.value = files.raw;
};

/**
 * @description: 确定提交文件
 * @return {*}
 */
const handleComfirmClicked = () => {
  let formData = new FormData();
  formData.append('file_name', file.value);
  formData.append('algorithm', 'algorithm');
  return apiFlowChartUpload(formData).then(() => {
    uploadDialogVisible.value = false;
  });
};
/**
 * @description: 弹窗文件取消
 * @return {*}
 */
const handleCancelClicked = () => {
  uploadDialogVisible.value = false;
  file.value = '';
};

/**
 * @description: 初始化调用获取flowChartApi值
 * @return {*}
 */
const handleFlowChartFetch = () => {
  flowChartApi.value = apiAlgorithmReview();
};

onBeforeMount(() => {
  handleFlowChartFetch();
});
</script>
<style lang="scss" scoped>
.flow-chart-container {
  .dialog-search {
    .el-button {
      padding-bottom: 0;
    }
  }
  .body-container {
    // margin: 125px 20px 50px 20px;
    margin: 20px;
    border-color: #e8eef2;
    // height: 100%;
    .dotted-line-container {
      height: 100%;
      width: 100%;
      border: 2px dashed rgba(145, 162, 188, 0.5);
      border-radius: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      .empty-figure {
        width: 212px;
        height: 144px;
      }
      .empty-text {
        font-size: 16px;
        line-height: 23px;
        text-align: center;
        color: rgba(145, 162, 188, 0.5);
        margin-top: 5px;
        font-weight: 500;
        font-family: 'Noto Sans SC';
        user-select: none;
        cursor: default;
      }
    }
  }
  .upload-dialog {
    border: 1px solid #e8eef2;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 24px;
  }
  .upload-body {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    .upload-font {
      color: #c6c7c8;
      font-weight: 400;
      font-size: 14px;
      font-family: 'Noto Sans SC';
      line-height: 20px;
    }
    .click-upload {
      color: #00bebe;
    }
  }
  .el-upload-dragger,
  .el-upload,
  .el-dialog__body {
    width: 100%;
  }
  .el-upload-dragger {
    display: flex;
    height: 100px;
  }
  .el-dialog__body {
    padding: 0;
  }
}
</style>
