import {i18n} from '~/i18n'
import {getTwoDecimalPlaces} from '~/utils'
import * as echarts from 'echarts'

const now = new Date()
const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
yesterday.setHours(0, 0, 0, 0)
export const initStartTime = yesterday.getTime()
export const initEndTime = yesterday.getTime() + 86399999

export const getShortcuts = () => {
  return [
    {
      text: i18n.global.t('common.pp_yesterday'),
      value: [initStartTime, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_7_days'),
      value: [initStartTime - 3600 * 1000 * 24 * 6, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_30_days'),
      value: [initStartTime - 3600 * 1000 * 24 * 29, initEndTime]
    }
  ]
}

export const getDisabledDate = (time: Record<string, any>) => time.getTime() > initEndTime

export const getFinalEndTime = (timestamp: number) => {
  const date = new Date(timestamp)
  date.setHours(23, 59, 59, 999)
  return date.getTime()
}

function roundUpToNearestTen(num: any) {
  return num == 0 ? 10 : Math.ceil(num / 10) * 10
}

export const doubleBarOption = {
  color: ['#fac858', '#91cc75', '#5470c6', '#73c0de', '#ea7ccc'],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.8)',
    axisPointer: {
      type: 'shadow'
    },
    formatter: (params: any) => {
      if (!params.length) return ''
      let res = params[0].axisValueLabel + '<br/>'
      for (const iterator of params) {
        res += `<div style="display: flex; justify-content: space-between; gap: 15px">
          <span>${iterator.marker} ${iterator.seriesName}</span>
          <span>${iterator.componentIndex == 0 || iterator.componentIndex == 1 ? getTwoDecimalPlaces(Number((Math.abs(iterator.data) * 100).toFixed(2))) + '%' : Math.abs(iterator.data)}</span>
        </div>`
      }
      return res
    }
  },
  legend: {},
  grid: {
    left: '3%',
    right: '6%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'value',
      alignTicks: true,
      axisLabel: {
        formatter: (value: any) => Math.abs(value) * 100 + '%'
      },
      max: 1,
      min: -1,
      splitNumber: 5,
      interval: 0.2
    },
    {
      type: 'value',
      alignTicks: true,
      axisLabel: {
        formatter: (value: any) => Math.abs(value)
      },
      max: (value: any) => roundUpToNearestTen(Math.max(Math.abs(value.min), Math.abs(value.max))),
      min: (value: any) => roundUpToNearestTen(Math.max(Math.abs(value.min), Math.abs(value.max))) * -1,
      splitNumber: 5
    }
  ],
  yAxis: {
    type: 'category',
    axisTick: {
      show: false
    },
    axisLabel: {
      margin: 40
    },
    data: []
  },
  series: [
    {
      name: '',
      type: 'bar',
      stack: 'Float',
      barMaxWidth: 25,
      label: {
        show: true,
        position: 'left',
        formatter: (value: any) => (value.data == 0 ? '' : getTwoDecimalPlaces(Number((Math.abs(value.data) * 100).toFixed(2))) + '%')
      },
      data: []
    },
    {
      name: '',
      type: 'bar',
      stack: 'Float',
      barMaxWidth: 25,
      label: {
        show: true,
        position: 'right',
        formatter: (value: any) => (value.data == 0 ? '' : getTwoDecimalPlaces(Number((Math.abs(value.data) * 100).toFixed(2))) + '%')
      },
      data: []
    },
    {
      name: '',
      type: 'bar',
      stack: 'Int',
      barMaxWidth: 25,
      xAxisIndex: 1,
      data: []
    },
    {
      name: '',
      type: 'bar',
      stack: 'Int',
      barMaxWidth: 25,
      label: {
        show: true,
        position: 'left'
      },
      xAxisIndex: 1,
      data: []
    },
    {
      name: '',
      type: 'bar',
      stack: 'Int',
      barMaxWidth: 25,
      label: {
        show: true,
        position: 'right',
        formatter: (value: any) => (value.data == 0 ? '' : Math.abs(value.data))
      },
      xAxisIndex: 1,
      data: []
    }
  ]
}
