<template>
  <div class="parking-occupancy-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_owl') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_parking_occupancy') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_parking_occupancy') }}</div>
      </div>
      <div class="header-right">
        <el-button class="welkin-primary-button" @click="handleDownload" :disabled="loading">{{ $t('common.pp_download') }}</el-button>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" :rules="rules" inline style="width: 100%">
          <el-row :gutter="25">
            <el-col :span="7">
              <el-form-item :label="$t('common.pp_time')" class="upper-form-item width-full">
                <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item :label="$t('common.pp_regional_company')" class="upper-form-item width-full">
                <el-select v-model="form.city_company" @change="handleChangeCompany" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
                  <el-option v-for="item in companyOptions" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="device_id" ref="deviceRef" :label="$t('deviceManagement.pp_device')" class="upper-form-item width-full">
                <el-select v-model="form.device_id" filterable remote clearable :placeholder="$t('common.pp_enter')" :remote-method="remoteMethod" :loading="remoteLoading" class="width-full">
                  <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                    <span style="float: left">{{ item.description }}</span>
                    <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item class="upper-form-item width-full">
                <el-button class="welkin-primary-button" :loading="loading" @click="handleSearch(ruleFormRef)">{{ $t('common.pp_search') }}</el-button>
                <el-button @click="handleReset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="swap-table-container">
        <div class="flex-box flex_j_c-space-between flex_a_i-center margin-10">
          <div class="flex-box flex_a_i-center font-size-14" :style="{ opacity: Number(switchVal) }">
            <el-icon color="#E6A23C" :size="14" class="margin-n-10"><WarningFilled /></el-icon>
            <span>{{ $t('parkingOccupancy.pp_tip') }}</span>
          </div>
          <el-switch v-model="switchVal" @change="handleChangeSwitch" :active-text="$t('parkingOccupancy.pp_occupancy_status_dashboard')" :inactive-text="$t('parkingOccupancy.pp_alarm_list')" style="--el-switch-on-color: #00bebe; --el-switch-off-color: #409eff" />
        </div>

        <div class="voc-num" v-show="!loading && hasData && switchVal">{{ $t('parkingOccupancy.pp_voc_num') }}：{{ vocNum }}</div>
        <div id="boardChart" class="width-full height-1400" v-show="!loading && hasData && switchVal"></div>

        <el-skeleton :rows="10" animated v-show="loading && switchVal" class="margin_b-20" />

        <el-empty :description="$t('common.pp_empty')" v-show="!loading && !hasData && switchVal" class="height-500"></el-empty>

        <el-table :data="list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }" v-loading="loading" v-if="!switchVal">
          <el-table-column prop="alarm_time_stamp" :label="$t('parkingOccupancy.pp_parking_alarm_time')" min-width="170" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ formatTime(row.alarm_time_stamp) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="device_name" :label="$t('parkingOccupancy.pp_device_name')" min-width="250" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.device_name || $t('common.pp_unnamed_device') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="device_id" :label="$t('parkingOccupancy.pp_device_id')" min-width="250">
            <template #default="{ row }">
              <WelkinCopyBoard :text="row.device_id" />
            </template>
          </el-table-column>

          <el-table-column prop="city_company" :label="$t('parkingOccupancy.pp_city_company')" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.city_company || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column :label="$t('parkingOccupancy.pp_view_image')" min-width="90">
            <template #default="{ row }">
              <div class="flex-box flex_a_i-center">
                <NormalImage @click="handleViewImage(row)" class="cursor-pointer" />
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container" v-if="!switchVal">
          <Page :page="pages" @change="handlePageChange" />
        </div>

        <el-dialog v-model="imageVisible" :title="$t('parkingOccupancy.pp_alarm_image_review')" width="800px" draggable align-center @close="closeDialog" :close-on-press-escape="false">
          <div class="flex-box flex_j_c-space-between margin_b-20">
            <div>
              <span class="margin_r-20 title-color">{{ $t('parkingOccupancy.pp_device_name') }}</span>
              <span class="common-color">{{ rowInfo.device_name || $t('common.pp_unnamed_device') }}</span>
            </div>
            <div>
              <span class="margin_r-20 title-color">{{ $t('parkingOccupancy.pp_parking_alarm_time') }}</span>
              <span class="common-color">{{ formatTime(rowInfo.alarm_time_stamp) }}</span>
            </div>
          </div>

          <img :src="rowInfo.image_url" class="alarm-image" />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { formatTime, removeNullKeys } from '~/utils'
import { page } from '~/constvars/page'
import { apiGetDevices } from '~/apis/home'
import { WarningFilled } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { initStartTime, initEndTime, getShortcuts, getDisabledDate, getFinalEndTime, doubleBarOption } from './constant'
import { apiGetCityCompanyMapping, apiGetAlarmList, apiGetDashboardList, apiDownloadBoard, apiDownloadTable } from '~/apis/owl/parkingOccupancy'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash-es'
import NormalImage from '~/assets/svg/normal-image.vue'
import * as echarts from 'echarts'
import _ from 'lodash'

const { t } = useI18n()
const store = useStore()
const route = useRoute()
const router = useRouter()
const deviceRef = ref()
const ruleFormRef = ref()
const loading = ref(false)
const imageVisible = ref(false)
const switchVal = ref(true)
const hasData = ref(false)
const remoteLoading = ref(false)
const vocNum = ref(0)
const datePicker = ref([initStartTime, initEndTime] as any)
const list = ref([] as any)
const echartsArr = ref([] as any)
const deviceOptions = ref([] as any)
const companyOptions = ref([] as any)
const form = ref({
  start_time: initStartTime,
  end_time: initEndTime,
  city_company: '',
  device_id: ''
})
const searchForm = ref({} as any)
const rowInfo = ref({} as any)
const initForm = _.cloneDeep(form.value)
const pages = ref(_.cloneDeep(page))
const isCollapse = computed(() => store.state.menus.collapse)
const rules = computed(() => {
  return {
    device_id: [{ required: true, message: t('parkingOccupancy.pp_select_device'), trigger: 'change' }]
  }
})
const boardOption = _.cloneDeep(doubleBarOption) as any

let myChart: any
const echartRender = (chartId: string, option: any) => {
  myChart && myChart.dispose()
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  echartsArr.value = [myChart]
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  if (hasData.value) echartRender('boardChart', boardOption)
}

/**
 * @description: 下载数据
 * @return {*}
 */
const handleDownload = () => {
  let formData = _.cloneDeep(searchForm.value) as any
  const params1 = {
    start_time: formData.start_time,
    end_time: getFinalEndTime(formData.end_time),
    device_id: formData.device_id
  }
  const params2 = {
    start_time: formData.start_time,
    end_time: getFinalEndTime(formData.end_time),
    device_id: formData.device_id,
    page: 1,
    size: pages.value.total
  }
  switchVal.value ? apiDownloadBoard(params1) : apiDownloadTable(params2)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

const handleViewImage = (row: any) => {
  rowInfo.value = row
  imageVisible.value = true
}

const closeDialog = () => {
  imageVisible.value = false
}

/**
 * @description: 切换区域公司，重置设备
 * @return {*}
 */
const handleChangeCompany = () => {
  form.value.device_id = ''
  searchDeviceList('NIO')
}

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = _.cloneDeep(searchForm.value) as any
  if (!switchVal.value) {
    formData.page = pages.value.current
    formData.size = pages.value.size
  }
  formData.end_time = getFinalEndTime(formData.end_time)
  removeNullKeys(formData)
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...formData, switchVal: switchVal.value }
    })
  }
  loading.value = true
  try {
    const res = switchVal.value ? await apiGetDashboardList(formData) : await apiGetAlarmList(formData)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      if (switchVal.value) {
        vocNum.value = res.data.total_voc_num
        list.value = res.data.hourly_detail || []
        hasData.value = list.value && list.value.length > 0
        boardOption.yAxis.data = list.value.map((item: any) => item.interval)
        boardOption.series[0].name = t('parkingOccupancy.pp_order_concentration')
        boardOption.series[0].data = list.value.map((item: any) => item.order)
        boardOption.series[1].name = t('parkingOccupancy.pp_occupancy_status')
        boardOption.series[1].data = list.value.map((item: any) => item.occupation)
        boardOption.series[2].name = t('parkingOccupancy.pp_cancel_order_num')
        boardOption.series[2].data = list.value.map((item: any) => -item.cancel_order_num)
        boardOption.series[3].name = t('parkingOccupancy.pp_finish_order_num')
        boardOption.series[3].data = list.value.map((item: any) => -item.finish_order_num)
        boardOption.series[3].label.formatter = function (params: any) {
          return Math.abs(params.value + boardOption.series[2].data[params.dataIndex]) == 0 ? '' : Math.abs(params.value + boardOption.series[2].data[params.dataIndex])
        }
        boardOption.series[4].name = t('parkingOccupancy.pp_interrupt_num')
        boardOption.series[4].data = list.value.map((item: any) => item.interrupt_num)
        nextTick(() => setCharts())
      } else {
        list.value = res.data || []
        pages.value.total = res.total
      }
    }
  } catch (error) {
    loading.value = false
  }
}

/**
 * @description: 切换告警列表/看板
 * @return {*}
 */
const handleChangeSwitch = () => {
  list.value = []
  if (searchForm.value.device_id) getList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid, fields) => {
    if (valid) {
      pages.value.current = 1
      searchForm.value = _.cloneDeep(form.value)
      getList()
    }
  })
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  form.value = _.cloneDeep(initForm)
  datePicker.value = [initStartTime, initEndTime]
  if (ruleFormRef.value) ruleFormRef.value.resetFields()
}

const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: 'PowerSwap2,PUS3', name: val, city_companies: form.value.city_company, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 获取站点列表
 * @return {*}
 */
const getDeviceNameMap = async () => {
  searchDeviceList(route.query.device_id || 'NIO')
  const params = { projects: 'PUS3,PowerSwap2' }
  const res = await apiGetCityCompanyMapping(params)
  companyOptions.value = Object.keys(res.data)
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  }
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
  form.value.city_company = !!initParams.city_company ? initParams.city_company : ''
  switchVal.value = initParams.switchVal === 'false' ? false : true
  searchForm.value = _.cloneDeep(form.value)
  getDeviceNameMap()
  if (form.value.device_id) getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.parking-occupancy-container {
  .search-form-container {
    padding-bottom: 5px;
    :deep(.upper-form-item) {
      margin-bottom: 15px;
    }
  }
  .swap-table-container {
    position: relative;
    :deep(.pagination-container .el-pagination) {
      margin-bottom: 20px;
    }
    :deep(.el-switch__label--left.is-active) {
      color: #409eff;
    }
    :deep(.el-switch__label--right.is-active) {
      color: #00bebe;
    }
    :deep(.el-dialog) {
      font-family: 'Noto Sans';
      .el-dialog__body {
        padding-top: 10px;
        padding-bottom: 20px;
        .alarm-image {
          width: 100%;
          height: 100%;
          max-width: 100%;
          max-height: 100%;
        }
      }
    }
    .voc-num {
      width: 280px;
      position: absolute;
      left: 20px;
      font-size: 14px;
      z-index: 2;
      color: #f56c6c;
    }
    .title-color {
      color: #22252b;
    }
    .common-color {
      color: #828d9e;
    }
  }
}
</style>
