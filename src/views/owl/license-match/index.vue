<template>
  <div class="license-match-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_owl') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_license_match') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_license_match') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" inline style="width: 100%">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('licenseMatch.pp_ds_plateno')" class="upper-form-item width-full">
                <el-input v-model="form.ds_plateno" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('licenseMatch.pp_lpr_plateno')" class="upper-form-item width-full">
                <el-input v-model="form.lpr_plateno" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('licenseMatch.pp_car_id')" class="upper-form-item width-full">
                <el-input v-model="form.vehicle_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('licenseMatch.pp_equity_status')" class="upper-form-item width-full">
                <el-select v-model="form.equity_stock_status" multiple collapse-tags collapse-tags-tooltip clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in equityOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('licenseMatch.pp_purchase_method')" class="upper-form-item width-full">
                <el-select v-model="form.owner_status" multiple collapse-tags collapse-tags-tooltip clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="upper-form-item width-full">
                <el-button class="welkin-primary-button" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
                <el-button @click="handleReset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="swap-table-container">
        <el-table :data="list" style="width: 100%" :header-cell-style="{fontSize: '14px', color: '#292C33', cursor: 'auto'}" v-loading="loading" v-show="hasData">
          <el-table-column prop="ds_plateno" :label="$t('licenseMatch.pp_ds_plateno')" min-width="140" fixed>
            <template #default="scope">
              <WelkinCopyBoard :text="maskString(scope.row.ds_plateno)" />
            </template>
          </el-table-column>

          <el-table-column prop="lpr_plateno" :label="$t('licenseMatch.pp_lpr_plateno')" min-width="140" fixed>
            <template #default="scope">
              <WelkinCopyBoard :text="maskString(scope.row.lpr_plateno)" />
            </template>
          </el-table-column>

          <el-table-column prop="vehicle_id" :label="$t('licenseMatch.pp_car_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="maskString(scope.row.vehicle_id)" />
            </template>
          </el-table-column>

          <el-table-column prop="specific_equity_name" :label="$t('licenseMatch.pp_equity_name')" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.specific_equity_name ? scope.row.specific_equity_name : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="equity_stock_status" :label="$t('licenseMatch.pp_equity_status')" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ equityMap.hasOwnProperty(scope.row.equity_stock_status) ? $t(equityMap[scope.row.equity_stock_status]) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="owner_status" :label="$t('licenseMatch.pp_purchase_method')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ statusMap.hasOwnProperty(scope.row.owner_status) ? $t(statusMap[scope.row.owner_status]) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="dateoflicensed" :label="$t('licenseMatch.pp_registration_time')" min-width="170" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.dateoflicensed) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="service_id" :label="$t('licenseMatch.pp_service_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.service_id" />
            </template>
          </el-table-column>

          <el-table-column prop="device_id" :label="$t('licenseMatch.pp_device_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>

          <el-table-column prop="description" :label="$t('licenseMatch.pp_device_name')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.description ? scope.row.description : $t('common.pp_unnamed_device') }}</span>
            </template>
          </el-table-column>

          <el-table-column :label="`${$t('home.pp_project')}`" width="150">
            <template #default="scope">
              <div class="flex-box flex_a_i-center">
                <span :style="`color: ${projectMap[scope.row.project].color}`">{{ $t(projectMap[scope.row.project].name) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="service_start_time" :label="$t('licenseMatch.pp_start_time')" min-width="170" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.service_start_time) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="service_end_time" :label="$t('licenseMatch.pp_end_time')" min-width="170" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.service_end_time) }}</span>
            </template>
          </el-table-column>

          <el-table-column :label="$t('common.pp_operation')" min-width="80" fixed="right" class-name="operation-column">
            <template #default="scope">
              <el-popover width="auto" placement="top" trigger="hover" effect="dark" :content="$t('licenseMatch.pp_view_image')" popper-class="message-popover">
                <template #reference>
                  <NormalImage @click="handleViewImage(scope.row)" class="cursor-pointer"/>
                </template>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>

        <el-empty :description="$t('common.pp_empty')" v-show="!hasData"></el-empty>

        <div class="pagination-container" v-show="hasData">
          <el-button class="welkin-primary-button download-button" :loading="downLoading" @click="handleDownLoadAll">{{ $t('licenseMatch.pp_full_download') }}</el-button>
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>

      <div v-if="showImageViewer">
        <WelkinImageViewer :data="imageList" @onClose="closeImageView" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onBeforeMount} from 'vue'
import {iconMap} from '~/auth'
import {useI18n} from 'vue-i18n'
import {useRoute, useRouter} from 'vue-router'
import {page} from '~/constvars/page'
import {Icon} from '@iconify/vue/dist/iconify'
import {equityOptions, statusOptions, equityMap, statusMap} from './constant'
import {formatTime, clearJson, removeNullKeys, maskString} from '~/utils'
import {apiGetLicenseList, apiDownloadLicenseList, apiGetLicenseImageList} from '~/apis/owl'
import {projectMap} from '~/constvars'
import {ElMessage} from 'element-plus'
import NormalImage from '~/assets/svg/normal-image.vue'
import _ from 'lodash'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const ruleFormRef = ref()
const loading = ref(false)
const downLoading = ref(false)
const hasData = ref(false)
const showImageViewer = ref(false)
const form = ref({
  ds_plateno: '',
  lpr_plateno: '',
  vehicle_id: '',
  equity_stock_status: [] as any,
  owner_status: [] as any
})
const searchForm = ref(_.cloneDeep(form.value))
const pages = ref(_.cloneDeep(page))
const list = ref([] as any)
const imageList = ref([] as any)

/**
 * @description: 查看图片
 * @param {*} row
 * @return {*}
 */
const handleViewImage = async (row: any) => {
  const params = {
    image_type: 27,
    start_time: row.service_start_time - 5 * 60 * 1000,
    end_time: row.service_end_time ? row.service_end_time : row.service_end_time + 180000
  }
  try {
    const res = await apiGetLicenseImageList(row.project, row.device_id, params)
    if (res.err_code) {
      showImageViewer.value = false
      ElMessage.error(res.message)
    } else {
      if (res.data.length == 0) {
        ElMessage.warning(t('common.pp_lack_image'))
      } else {
        imageList.value = res.data
        showImageViewer.value = true
      }
    }
  } catch (error: any) {
    showImageViewer.value = false
    ElMessage.error(error)
  }
}

/**
 * @description: 关闭图片弹窗
 * @return {*}
 */
const closeImageView = () => {
  showImageViewer.value = false
}

/**
 * @description: 全量下载
 * @return {*}
 */
const handleDownLoadAll = () => {
  let downloadForm = {} as any
  downloadForm = _.cloneDeep(searchForm.value)
  downloadForm.page = pages.value.current
  downloadForm.size = pages.value.size
  downloadForm.download = true
  removeNullKeys(downloadForm)
  if (downloadForm.equity_stock_status) downloadForm.equity_stock_status = downloadForm.equity_stock_status.join(',')
  if (downloadForm.owner_status) downloadForm.owner_status = downloadForm.owner_status.join(',')
  downLoading.value = true
  try {
    apiDownloadLicenseList(downloadForm)
    downLoading.value = false
  } catch (error) {
    downLoading.value = false
  }
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  if (loading.value) return
  let formData = {} as any
  formData = _.cloneDeep(searchForm.value)
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.download = false
  removeNullKeys(formData)
  if (formData.equity_stock_status) formData.equity_stock_status = formData.equity_stock_status.join(',')
  if (formData.owner_status) formData.owner_status = formData.owner_status.join(',')
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: formData
    })
  }
  loading.value = true
  try {
    const res = await apiGetLicenseList(formData)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      hasData.value = list.value && list.value.length > 0
      pages.value.total = res.total
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = _.cloneDeep(form.value)
  getList(true)
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList(true)
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.ds_plateno = initParams.ds_plateno ? initParams.ds_plateno : ''
  form.value.lpr_plateno = initParams.lpr_plateno ? initParams.lpr_plateno : ''
  form.value.vehicle_id = initParams.vehicle_id ? initParams.vehicle_id : ''
  form.value.equity_stock_status = initParams.equity_stock_status ? initParams.equity_stock_status.split(',').map((item: any) => Number(item)) : []
  form.value.owner_status = initParams.owner_status ? initParams.owner_status.split(',').map((item: any) => Number(item)) : []
  searchForm.value = _.cloneDeep(form.value)
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.license-match-container {
  font-family: 'Blue Sky Standard';
  .header-title {
    font-weight: bold !important;
  }
  .search-form-container {
    padding-bottom: 5px;
    :deep(.upper-form-item) {
      margin-bottom: 15px;
    }
    :deep(.el-select .el-select__tags .el-tag--info) {
      color: #262626;
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
  }
  .pagination-container {
    display: flex;
    justify-content: space-between;
    .download-button {
      margin-top: -10px;
    }
  }
}
</style>
