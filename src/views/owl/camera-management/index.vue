<template>
  <div class="camera-management-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_camera_management') }}</div>
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane v-for="item in tabList" :key="item.name" :label="$t(item.label)" :name="item.name"> </el-tab-pane>
      </el-tabs>
    </div>

    <div class="content-container">
      <el-form :model="state.cameraForm">
        <el-row :gutter="20">
          <el-col :span="8" v-if="!isEu()">
            <el-form-item :label="`${$t('common.pp_region')}`">
              <el-select v-model="state.cameraForm.region" filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full" clearable @change="changeArea">
                <el-option v-for="item in state.regionOptions" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`${$t('camera.pp_sites')}`">
              <el-select v-model="state.cameraForm.station" filterable remote clearable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteMethod" :loading="remoteLoading" class="width-full">
                <el-option v-for="item in state.stationOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                  <span style="float: left">{{ item.description }}</span>
                  <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="isEu() ? 6 : 8">
            <el-form-item :label="`${$t('camera.pp_camera')}`">
              <el-select v-model="state.cameraForm.camera" collapse-tags collapse-tags-tooltip filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full" clearable multiple>
                <el-option v-for="item in state.cameraOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="isEu() ? 6 : 8">
            <el-form-item :label="`${$t('camera.pp_status')}`">
              <el-select v-model="state.cameraForm.status" :placeholder="`${$t('common.pp_please_select')}`" class="width-full" clearable>
                <el-option v-for="(item, index) in state.statusOptions" :key="item.value" :label="$t(item.label)" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="!isEu()">
            <el-form-item :label="`${$t('camera.pp_algorithmic_result')}`">
              <el-select v-model="state.cameraForm.predict_result" :placeholder="`${$t('common.pp_please_select')}`" class="width-full" clearable>
                <el-option v-for="(item, index) in state.predictResultOptions" :key="item.value" :label="$t(item.label)" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="!isEu()">
            <el-form-item :label="`${$t('camera.pp_algorithmic_status')}`">
              <el-select v-model="state.cameraForm.in_blacklist" :placeholder="`${$t('common.pp_please_select')}`" class="width-full" clearable>
                <el-option v-for="(item, index) in state.blackListOptions" :key="item.value" :label="$t(item.label)" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="!isEu()">
            <el-form-item :label="`${$t('camera.pp_pending_acceptance')}`">
              <el-select v-model="state.cameraForm.need_acceptance" :placeholder="`${$t('common.pp_please_select')}`" class="width-full" clearable>
                <el-option :label="$t('common.pp_yes')" :value="true" />
                <el-option :label="$t('common.pp_no')" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16" v-if="!isEu()">
            <el-button class="welkin-primary-button" @click="searchCamera" :loading="state.loading">{{ $t('common.pp_search') }}</el-button>
            <el-button class="welkin-secondary-button" @click="resetCamera">{{ $t('common.pp_reset') }}</el-button>
          </el-col>
          <el-col :span="4" v-if="isEu()">
            <el-button class="welkin-primary-button margin_l-12" @click="searchCamera" :loading="state.loading">{{ $t('common.pp_search') }}</el-button>
            <el-button class="welkin-secondary-button" @click="resetCamera">{{ $t('common.pp_reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <el-table :data="tableData.data" :row-key="getRowKeys" :expand-row-keys="state.expands" @expand-change="expandColumn" @row-click="getCameraDetails" ref="expandTable" v-loading="state.loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column type="expand">
          <template v-if="tableData.columnConfig.length !== 0">
            <el-row v-for="(item, index) in tableData.columnConfig" :key="`${item.image_url} + ${new Date().getTime()}`" class="row-style">
              <el-col :span="3" style="padding-left: 40px; margin-top: 10px">
                <span style="color: #91a2bc">{{ item.camera_name }}</span>
              </el-col>
              <el-col :span="2" style="margin-top: 10px">
                <img :src="item.image_url" class="small-img image" @click="changeImgSize(item, index)" />
              </el-col>
              <el-col :span="5" style="margin-top: 10px">
                <span style="color: #91a2bc">{{ $t('camera.pp_shooting_time') }}：</span>
                <span style="margin-left: 10px; color: #91a2bc">{{ formatLocaleDate(item.create_ts) }}</span>
              </el-col>
              <el-col :span="5" style="margin-top: 10px; display: flex; align-items: center" v-if="!isEu()">
                <span style="color: #91a2bc">{{ $t('camera.pp_algorithmic_status') }}：</span>
                <el-select v-model="item.in_blacklist" style="width: 110px" :disabled="state.editeTag" size="small" :class="state.blackListClass[item.in_blacklist ? 1 : 2]" @change="judgeBlack(item, $event)">
                  <el-option v-for="item in state.blackListOptions" :key="item.value" :label="$t(item.label)" :value="item.value"> </el-option>
                </el-select>
              </el-col>
              <el-col :span="3" style="margin-top: 10px" v-if="!isEu()">
                <span style="color: #91a2bc">{{ $t('camera.pp_algorithm_results') }}：</span>
                <span :style="{ marginLeft: '10px', color: item.predict_result == 1 ? '#52c41a' : '#91a2bc', textDecoration: item.in_blacklist ? 'line-through' : 'none', textDecorationColor: item.predict_result == 1 ? '#52c41a' : '#91a2bc', textDecorationThickness: '2px' }">{{ $t(state.outputResult[item.predict_result]) }}</span>
              </el-col>
              <el-col :span="2" style="margin-top: 10px; margin-left: 20px">
                <el-select v-model="item.judge_result" style="width: 130px" :disabled="state.editeTag" size="small" :class="state.modeClass[item.judge_result]" @change="judgeByPeople($event, item, index)" :placeholder="`${$t('camera.pp_unverified')}`">
                  <el-option v-for="item in state.modeOptions" :key="item.value" :label="$t(item.label)" :value="item.value"> </el-option>
                </el-select>
              </el-col>
              <el-col :span="6" style="margin-top: 10px; margin-left: 40px; padding-left: 10px">
                <span style="color: #91a2bc">{{ $t('camera.pp_judgment_time') }}：</span>
                <span style="margin-left: 10px; color: #91a2bc" v-if="item.judge_ts !== 0">{{ formatLocaleDate(item.judge_ts) }}</span>
              </el-col>
              <el-col :span="4" style="margin-top: 10px">
                <span style="color: #91a2bc">{{ $t('camera.pp_operator') }}：</span>
                <span style="margin-left: 10px; color: #91a2bc">{{ item.judge_by }}</span>
              </el-col>
            </el-row>
          </template>
          <template v-else>
            <div class="empty-block">
              <span>{{ $t('common.pp_empty') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="`${$t('camera.pp_index')}`" prop="id" min-width="40" show-overflow-tooltip></el-table-column>
        <el-table-column v-if="!isEu()" :label="`${$t('common.pp_region')}`" prop="area" min-width="80" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.area || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="`${$t('camera.pp_station_name')}`" prop="device_name" min-width="240" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.device_name || $t('common.pp_unnamed_device') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="`${$t('camera.pp_station_id')}`" prop="device_id" min-width="240" show-overflow-tooltip></el-table-column>
        <el-table-column v-if="!isEu()" :label="$t('camera.pp_pass_rate')" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.normal_camera_total }}/{{ row.camera_total }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="!isEu() && hasPermission('camera-management:edit-tag')" :label="$t('camera.pp_pending_acceptance')" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <el-icon class="operation-icon" v-if="row.need_acceptance" @click.stop="handleViewCard(row)">
                <Postcard />
              </el-icon>
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="common-pagination">
        <el-pagination v-model:current-page="state.query.page_no" v-model:page-size="state.query.page_size" @current-change="handleCurrentChange" @size-change="handleSizeChange" background :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="state.total"></el-pagination>
      </div>

      <div class="flex-box flex_j_c-flex-end margin_t-20" v-if="tableData.data.length !== 0">
        <el-button class="welkin-primary-button" v-if="hasPermission('camera-management:edit-black')" :disabled="state.cameraForm.camera.length == 0" @click="handleRestore" :loading="state.restoreLoading">{{ $t('camera.pp_restore') }}</el-button>
        <el-button class="welkin-primary-button" @click="getDownload" :loading="state.exportLoading">{{ $t('camera.pp_export') }}</el-button>
      </div>
    </div>

    <!-- 图片弹窗 -->
    <el-dialog :title="state.cameraName" width="60%" align-center v-model="state.imgDialogVisible" :close-on-click-modal="false" @close="handleCloseDialog" style="height: 80%">
      <el-carousel trigger="click" :autoplay="false" :initial-index="state.imageIndex" ref="carouselRef" indicator-position="none" @change="changeCarousel" :loop="false">
        <el-carousel-item v-for="item in tableData.columnConfig" :key="item.image_url">
          <img :src="item.image_url" class="real-img" />
          <img :src="item.mask_url" class="mask-img" />
        </el-carousel-item>
      </el-carousel>
      <div class="flex-box flex_j_c-flex-end flex_a_i-center">
        <el-select v-model="state.judge_result" @change="judgeInDialog" :disabled="state.editeTag" size="small" :class="state.modeClass[state.judge_result]" :placeholder="`${$t('camera.pp_unverified')}`">
          <el-option v-for="item in state.modeOptions" :key="item.value" :label="$t(item.label)" :value="item.value"> </el-option>
        </el-select>
      </div>
    </el-dialog>

    <!-- 验收弹窗 -->
    <el-dialog v-model="detailVisible" :title="$t('camera.pp_dialog_title')" width="60%" align-center :close-on-click-modal="false" @close="handleClose">
      <div>
        <el-descriptions :column="1" border>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('common.pp_device_name') }}</span>
            </template>
            {{ rowInfo.device_name }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('common.pp_device_id') }}</span>
            </template>
            {{ rowInfo.device_id }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('camera.pp_initiator_of_acceptance') }}</span>
            </template>
            <div class="flex-box flex_a_i-center gap_8">
              <div>{{ rowInfo.user_id }}</div>
              <div>{{ rowInfo.user_name }}</div>
              <div v-if="rowInfo.is_outside" class="outside-tag">{{ $t('camera.pp_third_party_personnel') }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span>{{ $t('camera.pp_judgment_result') }}</span>
            </template>
            <div v-if="rowInfo.acceptance_pass">{{ $t('camera.pp_acceptance_passed') }}</div>
            <div v-else>
              <div v-for="item in rowInfo.camera_status" class="flex-box flex_d-column gap_12">
                <div v-if="item.has_image" class="result-line">
                  <span>{{ item.camera_name }}</span>
                  <span>{{ item.camera_type }}</span>
                  <span>{{ $t(state.judgeResult[item.judge_result]) }}</span>
                </div>
                <div v-else class="result-line">
                  <span>{{ item.camera_name }}</span>
                  <span>{{ item.camera_type }}</span>
                  <span>{{ $t('camera.pp_no_image') }}</span>
                </div>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="flex-box margin_t-20">
          <div class="color-59 white-space-nowrap">{{ $t('camera.pp_remark') }}：</div>
          <el-input v-model="postCardForm.remark" :autosize="{ minRows: 2 }" type="textarea" :placeholder="$t('common.pp_please_input')" class="width-full" />
        </div>
        <div class="flex-box flex_j_c-flex-end margin_t-20">
          <el-button @click="detailVisible = false" class="welkin-secondary-button">{{ $t('common.pp_cancel') }}</el-button>
          <el-button @click="handleConfirm" class="welkin-primary-button" :loading="confirmLoading" style="margin-left: 8px">{{ $t('common.pp_confirm') }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, watch } from 'vue'
import { PageHeader } from '~/views/components'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Postcard } from '@element-plus/icons-vue'
import { hasPermission } from '~/auth'
import { useRoute } from 'vue-router'
import { useRouter } from 'vue-router'
import { apiGetDevices } from '~/apis/home'
import { apiGetAllInfo, apiGetCameraInfo, apiGetCameraDetails, apiDownload, apiJudge, apiClearBlack, apiGetAcceptanceResult, apiPostResult } from '~/apis/owl'
import { formatLocaleDate, isEu, clearJson, removeNullProp } from '~/utils'
import { saveAs } from 'file-saver'
import { Parser } from 'json2csv/dist/json2csv.umd'
import { pagination } from '~/constvars'
import { useI18n } from 'vue-i18n'
import { debounce } from 'lodash-es'
import _ from 'lodash'

//定义响应式数据
const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const state = reactive({
  loading: false,
  exportLoading: false,
  restoreLoading: false,
  flag: false,
  pageNum: 1,
  mode: [],
  isChanged: false,
  imgDialogVisible: false as boolean,
  expands: [] as any,
  imageIndex: '' as any,
  judge_result: 0,
  camera_type: '',
  cameraName: '',
  total: 10,
  editeTag: !hasPermission('camera-management:edit-tag'),
  query: {
    area: '',
    device_id: '',
    camera_type: '',
    page_no: 1,
    page_size: 10,
    judge_result: '' as number | string,
    predict_result: '' as number | string,
    in_blacklist: '' as string | boolean,
    need_acceptance: '' as string | boolean
  },
  cameraForm: {
    region: '',
    camera: [],
    station: [],
    status: '' as number | string,
    predict_result: '' as number | string,
    in_blacklist: '' as string | boolean,
    need_acceptance: '' as string | boolean
  },
  cameraQuery: {
    device_id: '',
    camera_type: '',
    area: ''
  },
  downloadQuery: {
    area: '',
    device_id: '',
    camera_type: '',
    judge_result: '' as number | string,
    predict_result: '' as number | string,
    in_blacklist: '' as string | boolean,
    need_acceptance: '' as string | boolean
  },
  judgeQuery: {
    judge_result: 0,
    device_id: '',
    camera_type: ''
  },
  regionOptions: ['上海', '东北', '北京', '华中', '华北', '华南', '山东', '浙闽', '深汕', '苏皖', '西北', '西南'],
  cameraOptions: [] as any,
  stationOptions: [] as any,
  modeOptions: [
    {
      label: 'camera.pp_imaging_qualified',
      value: 1
    },
    {
      label: 'camera.pp_imaging_distortion',
      value: 2
    },
    {
      label: 'camera.pp_with_text',
      value: 3
    },
    {
      label: 'camera.pp_imaging_blur',
      value: 4
    },
    {
      label: 'camera.pp_incorrect_resolution',
      value: 5
    },
    {
      label: 'camera.pp_unverified',
      value: 0
    }
  ],
  statusOptions: [
    {
      label: 'camera.pp_unverified',
      value: 0
    },
    {
      label: 'camera.pp_qualified',
      value: 1
    },
    {
      label: 'camera.pp_unqualified',
      value: 2
    }
  ],
  predictResultOptions: [
    {
      label: 'camera.pp_empty',
      value: 0
    },
    {
      label: 'camera.pp_pass',
      value: 1
    },
    {
      label: 'camera.pp_fail',
      value: 2
    },
    {
      label: 'camera.pp_exception',
      value: 3
    }
  ],
  blackListOptions: [
    {
      label: 'camera.pp_normal',
      value: false
    },
    {
      label: 'camera.pp_invalid',
      value: true
    }
  ],
  modeClass: {
    1: 'qualified',
    2: 'deflection',
    3: 'characters',
    4: 'vague',
    5: 'resolution',
    0: 'notVerified'
  } as any,
  blackListClass: {
    1: 'in-black-list',
    2: 'out-black-list'
  },
  judgeResult: {
    1: 'camera.pp_imaging_qualified',
    2: 'camera.pp_imaging_distortion',
    3: 'camera.pp_with_text',
    4: 'camera.pp_imaging_blur',
    5: 'camera.pp_incorrect_resolution',
    0: 'camera.pp_unverified'
  } as any,
  outputResult: {
    0: 'camera.pp_empty',
    1: 'camera.pp_pass',
    2: 'camera.pp_fail',
    3: 'camera.pp_exception'
  } as any,
  blackListMap: {
    false: 'camera.pp_normal',
    true: 'camera.pp_invalid'
  } as any
})
const carouselRef = ref<any>()
const activeTab = ref('PowerSwap2' as any)
const tabList = ref([
  {
    name: 'PowerSwap2',
    label: 'menu.pp_swap_station2'
  },
  {
    name: 'PUS3',
    label: 'menu.pp_swap_station3'
  },
  {
    name: 'PUS4',
    label: 'menu.pp_swap_station4'
  }
])
const tableData = reactive({
  data: [] as any,
  columnConfig: [] as any
})

const expandFlag = ref<any>('')
const temp = ref<any>('')
const tempArr = ref([] as any)

const expandTable = ref<FormInstance>()
const project = ref('PowerSwap2' as any)
const detailVisible = ref(false)
const confirmLoading = ref(false)
const remoteLoading = ref(false)
const rowInfo = ref({} as any)
const postCardForm = ref({
  remark: ''
})

/**
 * @description: 确认发送卡片
 * @return {*}
 */
const handleConfirm = async () => {
  let resultArr = []
  if (rowInfo.value.acceptance_pass) {
    resultArr.push(t('camera.pp_acceptance_passed'))
  } else {
    rowInfo.value.camera_status.map((item: any) => {
      if (item.has_image) {
        resultArr.push(item.camera_name + ' ' + item.camera_type + ' ' + t(state.judgeResult[item.judge_result]))
      } else {
        resultArr.push(item.camera_name + ' ' + item.camera_type + ' ' + t('camera.pp_no_image'))
      }
    })
  }
  const params = {
    device_id: rowInfo.value.device_id,
    description: rowInfo.value.device_name,
    user_id: rowInfo.value.user_id,
    is_outside: rowInfo.value.is_outside,
    acceptance_result: resultArr,
    remark: postCardForm.value.remark
  }
  confirmLoading.value = true
  const res = await apiPostResult(project.value, params)
  confirmLoading.value = false
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    ElMessage.success(t('camera.pp_send_success'))
    detailVisible.value = false
    publicSearch(false)
  }
}

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleClose = () => {
  detailVisible.value = false
  postCardForm.value.remark = ''
}

/**
 * @description: 点击查看卡片
 * @param {*} row
 * @return {*}
 */
const handleViewCard = async (row: any) => {
  const params = {
    device_id: row.device_id
  }
  const res = await apiGetAcceptanceResult(project.value, params)
  if (res.err_code === -1) {
    ElMessage.error(t('camera.pp_error_text'))
  } else if (res.err_code !== 0) {
    ElMessage.error(res.message)
  } else {
    rowInfo.value = { ...row, ...res.data }
    detailVisible.value = true
  }
}

const changeCarousel = (newIndex: number, oldIndex: number) => {
  state.imageIndex = newIndex
  const currentItem = tableData.columnConfig[newIndex]
  state.judge_result = currentItem.judge_result
  state.camera_type = currentItem.camera_type
  state.cameraName = currentItem.camera_name
}

const keyDown = () => {
  document.onkeydown = (e) => {
    let e1 = e || window.event
    if (state.flag) {
      if (e1 && e1.keyCode === 37) {
        // 按下左箭头
        if (state.imageIndex > 0) state.imageIndex = state.imageIndex - 1
        if (carouselRef.value) carouselRef.value.setActiveItem(state.imageIndex)
      } else if (e1 && e1.keyCode == 39) {
        // 按下右箭头
        if (state.imageIndex < tableData.columnConfig.length - 1) state.imageIndex = state.imageIndex + 1
        if (carouselRef.value) carouselRef.value.setActiveItem(state.imageIndex)
      }
      const currentItem = tableData.columnConfig[state.imageIndex]
      state.judge_result = currentItem.judge_result
      state.camera_type = currentItem.camera_type
      state.cameraName = currentItem.camera_name
    }
  }
}

/**
 * @description: 切换二/三/四代站
 * @param {*} val
 * @return {*}
 */
const handleTabChange = (val: any) => {
  project.value = val
  state.query.page_no = 1
  resetCamera()
  getCameraInfo()
  searchDeviceList('NIO')
}

/**
 * @description: 重置搜索
 * @return {*}
 */
const resetCamera = () => {
  clearJson(state.cameraForm)
  state.query.page_no = 1
  publicSearch()
}

/**
 * @description: 搜索
 * @return {*}
 */
const searchCamera = () => {
  state.query.page_no = 1
  state.query.page_size = 10
  publicSearch()
}

// 公共筛选
const publicSearch = (updateRoute = true) => {
  tableData.data = []
  state.loading = true
  state.query.area = state.cameraForm.region
  state.query.device_id = state.cameraForm.station.join(',')
  state.query.camera_type = state.cameraForm.camera.join(',')
  state.query.judge_result = state.cameraForm.status
  state.query.predict_result = state.cameraForm.predict_result
  state.query.in_blacklist = state.cameraForm.in_blacklist
  state.query.need_acceptance = state.cameraForm.need_acceptance
  if (updateRoute) {
    router.push({
      path: route.path,
      query: { ...removeNullProp(state.query), project: project.value }
    })
  }
  return apiGetAllInfo(project.value, state.query).then((res) => {
    state.loading = false
    tableData.data = res.data || []
    state.total = res.total
    tableData.data.forEach((val: any) => {
      val.expanded = false
    })
    state.expands = []
    tableData.data.map((item: any) => {
      if (item.device_id === expandFlag.value.device_id) {
        const form = expandTable as any
        form.value.toggleRowExpansion(item, true)
        item.expanded = true
      }
    })
  })
}

// 每次只能展开一行
const getRowKeys = (row: any) => {
  return row.device_id
}

// 每次只展开一行
const expandColumn = (row: any, expandedRows: any) => {
  state.expands = []
  if (expandedRows.length > 0) {
    state.expands.push(row ? row.device_id : [])
  }
  state.cameraQuery.area = row.area
  state.cameraQuery.device_id = row.device_id
  state.cameraQuery.camera_type = state.query.camera_type
  temp.value = JSON.parse(JSON.stringify(row))
  getExpandCamera(state.cameraQuery)
}

// 获取展开行内容
const getExpandCamera = (query: any) => {
  tempArr.value = []
  tableData.columnConfig = []
  return apiGetCameraDetails(project.value, query).then((res) => {
    if (res.data) {
      tempArr.value = []
      tableData.columnConfig = []
      res.data.map((itemOut: any, index: number) => {
        tableData.data.map((itemIn: any) => {
          if (itemIn.device_id === query.device_id) {
            if (itemIn.camera_info)
              itemIn.camera_info.map((item: any) => {
                if (item.camera_type === itemOut.camera_type) {
                  itemOut.camera_type = item.camera_type
                  itemOut.camera_name = item.camera_name
                  itemOut.judge_result = item.judge_result
                  itemOut.judge_ts = item.judge_ts
                  itemOut.judge_by = item.judge_by
                  itemOut.mask_url = item.mask_url.replace('https://api-owl-minio.nioint.com', '/web/minio').replace('http://api-owl-minio.nioint.com', '/web/minio')
                  itemOut.predict_result = item.predict_result
                  itemOut.in_blacklist = item.in_blacklist
                }
              })
          }
        })
      })
      res.data.map((item: any, index: number) => {
        if (item.camera_name) {
          tempArr.value.push(item)
        }
      })
      tableData.columnConfig = tempArr.value
    } else {
      tableData.columnConfig = []
    }
  })
}

// 点击每行的任意位置都能展开
const getCameraDetails = (row: any) => {
  row.expanded = !row.expanded
  const form = expandTable as any
  form.value.toggleRowExpansion(row, row.expanded)
  if (row.expanded === true) {
    temp.value = JSON.parse(JSON.stringify(row))
  }
}

//查看大图
const changeImgSize = (val: any, index: number) => {
  state.imageIndex = index
  state.judge_result = val.judge_result
  state.camera_type = val.camera_type
  state.cameraName = val.camera_name
  state.flag = true
  state.isChanged = false
  state.imgDialogVisible = true
  setTimeout(() => {
    if (carouselRef.value) carouselRef.value.setActiveItem(index)
  }, 50)
}

const judgeByPeople = (e: any, item: any, index: number) => {
  expandFlag.value = ''
  expandFlag.value = JSON.parse(JSON.stringify(temp.value))
  state.judgeQuery.judge_result = item.judge_result
  state.judgeQuery.camera_type = item.camera_type
  state.judgeQuery.device_id = state.cameraQuery.device_id
  return apiJudge(project.value, state.judgeQuery).then(() => {
    publicSearch()
  })
}

/**
 * @description: 判定是否加入黑名单
 * @param {*} item
 * @param {*} in_blacklist
 * @return {*}
 */
const judgeBlack = async (item: any, in_blacklist: boolean) => {
  expandFlag.value = ''
  expandFlag.value = JSON.parse(JSON.stringify(temp.value))
  const params = {
    area: state.cameraQuery.area,
    device_id: state.cameraQuery.device_id,
    camera_type: item.camera_type,
    in_blacklist: in_blacklist
  }
  try {
    const res = await apiJudge(project.value, params)
    if (!res.err_code) {
      publicSearch()
    } else {
      ElMessage.error(res.message)
    }
  } catch (error: any) {
    ElMessage.error(error)
  }
}

// 分页
const handleCurrentChange = (val: number) => {
  state.query.page_no = val
  publicSearch()
}

const handleSizeChange = (val: number) => {
  state.query.page_no = 1
  state.query.page_size = val
  publicSearch()
}

/**
 * @description: 批量恢复算法
 * @return {*}
 */
const handleRestore = async () => {
  expandFlag.value = ''
  expandFlag.value = JSON.parse(JSON.stringify(temp.value))
  ElMessageBox.confirm(`${t('camera.pp_confirm')} ?`, t('stuckAnalysis.pp_prompt'), {
    confirmButtonText: t('common.pp_confirm'),
    cancelButtonText: t('common.pp_cancel'),
    type: 'warning',
    customClass: 'camera-messagebox'
  })
    .then(async () => {
      state.restoreLoading = true
      try {
        const params = {} as any
        params.area = state.cameraForm.region
        params.device_id = state.cameraForm.station.join(',')
        params.camera_type = state.cameraForm.camera.join(',')
        params.judge_result = state.cameraForm.status
        params.predict_result = state.cameraForm.predict_result
        params.in_blacklist = state.cameraForm.in_blacklist
        params.need_acceptance = state.cameraForm.need_acceptance
        const res = await apiClearBlack(project.value, removeNullProp(params))
        state.restoreLoading = false
        if (!res.err_code) {
          ElMessage.success(t('camera.pp_restore_success'))
          publicSearch()
        } else {
          ElMessage.error(res.message)
        }
      } catch (error: any) {
        state.restoreLoading = false
        ElMessage.error(error)
      }
    })
    .catch(() => {})
}

/**
 * @description: 获取下载
 * @return {*}
 */
const getDownload = () => {
  state.exportLoading = true
  state.downloadQuery.area = state.cameraForm.region
  state.downloadQuery.device_id = state.cameraForm.station.join(',')
  state.downloadQuery.camera_type = state.cameraForm.camera.join(',')
  state.downloadQuery.judge_result = state.cameraForm.status
  state.downloadQuery.predict_result = state.cameraForm.predict_result
  state.downloadQuery.in_blacklist = state.cameraForm.in_blacklist
  state.downloadQuery.need_acceptance = state.cameraForm.need_acceptance
  return apiDownload(project.value, state.downloadQuery).then((res: any) => {
    const list = res.data.map((item: any) => {
      return {
        [t('common.pp_region')]: item.area,
        [t('camera.pp_station_name')]: item.device_name,
        [t('camera.pp_station_id')]: item.device_id,
        [t('camera.pp_camera')]: item.camera_name,
        [t('camera.pp_judgment_time')]: item.judge_ts === 0 ? '' : formatLocaleDate(item.judge_ts, false),
        [t('camera.pp_status')]: t(state.judgeResult[item.judge_result]),
        [t('camera.pp_judge')]: item.judge_by,
        [t('camera.pp_algorithm_results')]: t(state.outputResult[item.predict_result]),
        [t('camera.pp_algorithmic_status')]: t(state.blackListMap[item.in_blacklist.toString()])
      }
    })
    if (isEu()) {
      list.forEach((item: any) => {
        delete item[t('common.pp_region')]
        delete item[t('camera.pp_algorithm_results')]
        delete item[t('camera.pp_algorithmic_status')]
      })
    }
    const parser = new Parser()
    const csvData = parser.parse(list)
    const exportContent = '\uFEFF'
    const blob = new Blob([exportContent + csvData], {
      type: 'text/plain;charset=utf-8'
    })
    saveAs(blob, t('camera.pp_camera_form') + formatLocaleDate(new Date().getTime(), false) + '.csv')
    state.exportLoading = false
  })
}

const judgeInDialog = () => {
  expandFlag.value = ''
  expandFlag.value = JSON.parse(JSON.stringify(temp.value))
  state.judgeQuery.judge_result = state.judge_result
  state.judgeQuery.camera_type = state.camera_type
  state.judgeQuery.device_id = state.cameraQuery.device_id
  state.isChanged = true
  tableData.columnConfig[state.imageIndex].judge_result = JSON.parse(JSON.stringify(state.judge_result))
  return apiJudge(project.value, state.judgeQuery).then(() => {
    // state.imgDialogVisible = false
    // publicSearch()
  })
}

const handleCloseDialog = () => {
  state.flag = false
  if(state.isChanged) publicSearch()
}

// 获取摄像头信息
const getCameraInfo = () => {
  state.cameraOptions = []
  return apiGetCameraInfo(project.value).then((res) => {
    if (res.data) {
      res.data.map((item: any) => {
        state.cameraOptions.push({
          label: item.camera_name,
          value: item.camera_type
        })
      })
    }
  })
}

// 远程搜索设备
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteLoading.value = true
    const params = { project: project.value, name: val, areas: state.cameraForm.region, device_ids: deviceIds, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    state.stationOptions = res.data
  }
}, 500)

// 改变区域时同步变化站点信息
const changeArea = () => {
  state.cameraForm.station = []
  searchDeviceList('NIO')
}

const { locale } = useI18n({ useScope: 'global' })
watch(
  () => locale.value,
  (newValue, oldValue) => {
    getCameraInfo()
    publicSearch()
  }
)

const initWeb = () => {
  let initParams: any = route.query
  state.query.page_no = !!initParams.page_no ? Number(initParams.page_no) : 1
  state.query.page_size = !!initParams.page_size ? Number(initParams.page_size) : 10
  state.cameraForm.region = !!initParams.area ? initParams.area : ''
  state.cameraForm.station = !!initParams.device_id ? initParams.device_id.split(',') : []
  state.cameraForm.camera = !!initParams.camera_type ? initParams.camera_type.split(',') : []
  state.cameraForm.status = !!initParams.judge_result ? Number(initParams.judge_result) : ''
  state.cameraForm.predict_result = !!initParams.predict_result ? Number(initParams.predict_result) : ''
  state.cameraForm.in_blacklist = !!initParams.in_blacklist ? JSON.parse(initParams.in_blacklist) : ''
  state.cameraForm.need_acceptance = !!initParams.need_acceptance ? JSON.parse(initParams.need_acceptance) : ''
  publicSearch(false)
}

/**
 * @description: 页面初始化...
 * @return {*}
 */
onBeforeMount(() => {
  project.value = route.query.project || 'PowerSwap2'
  activeTab.value = route.query.project || 'PowerSwap2'
  keyDown()
  getCameraInfo()
  searchDeviceList('NIO', route.query.device_id)
  initWeb()
})
</script>

<style lang="scss" scoped>
.camera-management-container {
  font-family: 'Blue Sky Standard';
  background: linear-gradient(180deg, #e2f9f9 -6.51%, rgba(255, 255, 255, 0.5) 12.89%);
  .header-container {
    padding: 24px 24px 20px;
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
      margin-bottom: 12px;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    :deep(.el-form) {
      margin-bottom: 4px;
      .el-form-item {
        margin: 0 24px 16px 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-select .el-select__tags .el-tag--info {
          color: #262626;
        }
      }
      .el-input__inner {
        color: #262626;
      }
    }
    :deep(.el-table) {
      margin-bottom: 20px;
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      ::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 10px;
      }
      .el-scrollbar__view {
        overflow-x: hidden;
      }
      .el-table__expanded-cell {
        overflow-x: auto;
      }
      .el-select .el-input.is-focus .el-input__wrapper {
        box-shadow: none !important;
      }
      .el-input__wrapper {
        box-shadow: none !important;
      }
      .operation-icon {
        font-size: 20px;
        color: #01a0ac;
        cursor: pointer;
      }
    }
    :deep(.card-wrapper:first-child) {
      margin-top: 20px;
    }
    .small-img {
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;
      position: absolute;
      margin-bottom: 10px;
      cursor: pointer;
    }
    .empty-block {
      width: 70%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    .el-select .el-input.is-focus .el-input__wrapper {
      box-shadow: none !important;
    }
    .el-input__wrapper {
      box-shadow: none !important;
    }
    .el-dialog__header {
      z-index: 2;
      padding: 24px;
    }
    .el-dialog__body {
      padding: 0 24px 24px;
      flex: 1;
      display: flex;
      flex-direction: column;
      .el-carousel {
        flex: 1;
        margin-bottom: 16px;
        .el-carousel__container {
          height: 100%;
          .el-carousel__item {
            display: flex;
            justify-content: center;
            .real-img {
              position: absolute;
              z-index: 1;
              max-width: 100%;
              max-height: 100%;
              width: 100%;
              height: auto;
              object-fit: contain;
            }
            .mask-img {
              position: absolute;
              z-index: 2;
              max-width: 100%;
              max-height: 100%;
              width: 100%;
              height: auto;
              object-fit: contain;
            }
          }
          .el-carousel__arrow i {
            font-size: 40px;
          }
        }
      }
      .outside-tag {
        display: inline-block;
        height: 24px;
        padding: 3px 8px;
        border-radius: 2px;
        font-size: 12px;
        font-weight: bold;
        line-height: 18px;
        color: #fd8c08;
        background-color: #fff8dd;
      }
      .result-line {
        display: grid;
        grid-template-columns: 2fr 2fr 3fr;
        gap: 12px 8px;
        white-space: nowrap;
      }
      .el-descriptions__label.el-descriptions__cell.is-bordered-label {
        width: 200px;
        color: #595959;
      }
      .el-descriptions__content.el-descriptions__cell.is-bordered-content {
        color: #262626;
      }
    }
  }
  :deep(.el-table__empty-block) {
    width: 100% !important;
  }
  :deep(.row-style) {
    margin-top: 10px;
    margin-bottom: 10px;
    flex-wrap: nowrap;
  }
  :deep(.row-style:first-child) {
    margin-top: 0;
  }
  :deep(.row-style:last-child) {
    margin-bottom: 20px;
  }
  :deep(.in-black-list .el-input__wrapper) {
    background-color: rgba(255, 77, 79, 0.2);
    color: #ff4d4f;
    border: none;
  }
  :deep(.out-black-list .el-input__wrapper) {
    background-color: rgba(82, 196, 26, 0.2);
    color: #52c41a;
    border: none;
  }
  :deep(.qualified .el-input__wrapper) {
    background-color: rgba(82, 196, 26, 0.2);
    color: #52c41a;
    border: none;
  }
  :deep(.deflection .el-input__wrapper) {
    background-color: rgba(250, 173, 20, 0.2);
    color: #faad14;
    border: none;
  }
  :deep(.characters .el-input__wrapper) {
    background-color: rgba(255, 77, 79, 0.2);
    color: #ff4d4f;
    border: none;
  }
  :deep(.vague .el-input__wrapper) {
    background-color: rgba(0, 190, 190, 0.2);
    color: var(--el-color-primary);
    border: none;
  }
  :deep(.resolution .el-input__wrapper) {
    background-color: rgba(44, 115, 255, 0.2);
    color: #2c73ff;
    border: none;
  }
  :deep(.notVerified .el-input__wrapper) {
    background-color: rgba(75, 82, 95, 0.2);
    color: #4b525f;
    border: none;
  }
}
</style>
