<template>
  <div class="afrr-info-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_power_grid') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_afrr_dashboard') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_afrr_dashboard') }}</div>
      </div>
      <div class="header-right">
        <div class="font-size-14">{{ 'NIO Power Swap Station | NL Dordrecht' + ' - ' + 'PS-NIO-93f5d774-93a8160e' }}</div>
      </div>
    </div>

    <!-- 图形 -->
    <div class="swap-page-container">
      <div class="swap-table-container padding-20 margin_b-20 relative">
        <div class="time-section absolute">
          <el-radio-group v-model="form.time_limit" @change="handleChangeTime">
            <el-radio-button :label="item.value" v-for="item in timeOptions">{{ $t(item.label) }}</el-radio-button>
          </el-radio-group>
        </div>
        <div id="Chart1" class="width-full height-400" v-show="!isEmpty1"></div>
        <el-empty :description="$t('common.pp_empty')" class="width-full height-400" v-if="isEmpty1"></el-empty>

        <div id="Chart2" class="width-full height-240"></div>
      </div>

      <!-- 电池仓数据 -->
      <div class="swap-table-container padding-20" v-if="!isEmpty3">
        <div class="flex-box flex_w-wrap gap_12 margin_b-20">
          <el-card v-for="item in dataNameOptions" class="upper-card">
            <div class="font-size-13 margin_b-8">{{ $t(item.label) }}</div>
            <div class="font-size-14">{{ list[item.value] }} {{ list[item.value] == $t('common.pp_no_data') ? '' : item.unit }}</div>
          </el-card>
        </div>
        <div>
          <div class="font-size-18 margin_b-15 flex-box flex_j_c-space-between flex_a_i-center">
            <span>{{ $t('gridInfo.pp_battery_slot') }}</span>
            <div class="font-size-14 black-class">
              <span class="margin_r-20"
                >{{ $t('gridInfo.pp_reserve_75') }}：<span class="green-class">{{ list.battery_70_reserved_nums }}</span></span
              >
              <span
                >{{ $t('gridInfo.pp_reserve_100') }}：<span class="green-class">{{ list.battery_100_reserved_nums }}</span></span
              >
            </div>
          </div>
          <el-row :gutter="20" class="margin_b-20">
            <el-col :span="7" v-for="(item, index) in list.battery_info.slice(0, 6)">
              <el-card class="lower-card width-full font-size-14" :style="{backgroundColor: item.branch_work_status == 1 ? '#bef5e2' : '#fff'}">
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="font-weight-bold">{{ batteryTypeMap[item.bms_battery_type] ? $t(batteryTypeMap[item.bms_battery_type]) : $t('gridInfo.pp_reserve') }}</span>
                  <span class="soc-class">{{ getOneDecimalPlaces(item.bms_customer_usage_soc) }}%</span>
                </div>
                <div class="flex-box flex_j_c-center flex_a_i-center margin-8-n">
                  <div class="flex-box flex_d-column">
                    <span>{{ $t('gridInfo.pp_request') }}：</span>
                    <span>{{ $t('gridInfo.pp_distribute') }}：</span>
                    <span>{{ $t('gridInfo.pp_actual') }}：</span>
                  </div>
                  <div class="flex-box flex_d-column">
                    <span>{{ getOneDecimalPlaces(item.bms_chrg_power_limit_lt) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.distribute_power) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.battery_pack_power) }} kW</span>
                  </div>
                </div>
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="index-class">{{ item.slot_id }}#</span>
                  <span class="font-size-12">{{ $t('gridInfo.pp_limit') }}：{{ item.limit_power }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="list.battery_info.length > 6">
            <el-col :span="7" v-for="(item, index) in list.battery_info.slice(6)">
              <el-card class="lower-card width-full font-size-14" :style="{backgroundColor: item.branch_work_status == 1 ? '#bef5e2' : '#fff'}">
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="font-weight-bold">{{ batteryTypeMap[item.bms_battery_type] ? $t(batteryTypeMap[item.bms_battery_type]) : $t('gridInfo.pp_reserve') }}</span>
                  <span class="soc-class">{{ getOneDecimalPlaces(item.bms_customer_usage_soc) }}%</span>
                </div>
                <div class="flex-box flex_j_c-center flex_a_i-center margin-8-n">
                  <div class="flex-box flex_d-column">
                    <span>{{ $t('gridInfo.pp_request') }}：</span>
                    <span>{{ $t('gridInfo.pp_distribute') }}：</span>
                    <span>{{ $t('gridInfo.pp_actual') }}：</span>
                  </div>
                  <div class="flex-box flex_d-column">
                    <span>{{ getOneDecimalPlaces(item.bms_chrg_power_limit_lt) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.distribute_power) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.battery_pack_power) }} kW</span>
                  </div>
                </div>
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="index-class">{{ item.slot_id }}#</span>
                  <span class="font-size-12">{{ $t('gridInfo.pp_limit') }}：{{ item.limit_power }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-empty :description="$t('common.pp_empty')" class="swap-table-container padding-20" v-else></el-empty>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, onBeforeMount, onBeforeUnmount, computed, watch, nextTick} from 'vue'
import {useI18n} from 'vue-i18n'
import {useStore} from 'vuex'
import {formatTime, getOneDecimalPlaces} from '~/utils'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {apiGetTopList, apiGetBottomList, apiGetNetherlandsEPrice, apiGetNetherlandsImbalancePrice} from '~/apis/power-grid'
import {multiLineOption, timeOptions, dataNameOptions, modeMap, controlEnableMap, protocolVersionMap, batteryTypeMap} from '../grid-info/constant'
import * as echarts from 'echarts'
import _ from 'lodash'

const {t} = useI18n()
const store = useStore()
const route = useRoute()
const router = useRouter()
const webSocket1 = ref()
const webSocket2 = ref()
const webSocket3 = ref()
const isCollapse = computed(() => store.state.menus.collapse)
const form = ref({
  project: 'PowerSwap2',
  time_limit: '1h',
  deviceId: 'PS-NIO-93f5d774-93a8160e'
})
const isEmpty1 = ref(true)
const isEmpty2 = ref(true)
const isEmpty3 = ref(true)
const ePrice = ref([] as any)
const echartsArr1 = ref([] as any)
const echartsArr2 = ref([] as any)
const list = ref({} as any)
const option1 = _.cloneDeep(multiLineOption)
const option2 = _.cloneDeep(multiLineOption)

let chartDom1: any
let chartDom2: any

const echartRender1 = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  chartDom1 && chartDom1.dispose()
  chartDom1 = echarts.init(chartDom)
  chartDom.setAttribute('_echarts_instance_', '')
  option && chartDom1.setOption(option, true)
  window.addEventListener('resize', () => chartDom1.resize())
}

const echartRender2 = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  chartDom2 && chartDom2.dispose()
  chartDom2 = echarts.init(chartDom)
  chartDom.setAttribute('_echarts_instance_', '')
  option && chartDom2.setOption(option, true)
  window.addEventListener('resize', () => chartDom2.resize())
}

const formatTopData = (data: any) => {
  if (data && data.length > 0) {
    isEmpty1.value = false
    option1.color = ['#6697ff', '#ff7575', '#5ecfff']
    option1.xAxis.data = data.map((item: any) => formatTime(item.timestamp))
    option1.series[0].name = t('gridInfo.pp_station_use_power')
    option1.series[1].name = t('gridInfo.pp_charge_power_max')
    option1.series[2].name = t('gridInfo.pp_charge_power_min')
    option1.series[0].data = data.map((item: any) => item.station_use_power)
    option1.series[1].data = data.map((item: any) => item.need_charge_power_min)
    option1.series[2].data = data.map((item: any) => item.allow_charge_power_max)
    nextTick(() => {
      if (chartDom1) option1.legend.selected = chartDom1.getOption().legend[0].selected
      echartRender1('Chart1', option1)
      echartsArr1.value = [chartDom1]
    })
  } else {
    isEmpty1.value = true
  }
}

/**
 * @description: 整合电价和最低/最高电价
 * @param {*} arr1 电价
 * @param {*} arr2 最低/最高电价
 * @return {*}
 */
const mergeTimeArr = (arr1: any, arr2: any) => {
  arr2 = arr2.map((item: any) => {
    const filtered = arr1.filter((k: any) => k.time > item.ts)
    item.priceAmount = filtered.length > 0 ? filtered[0].priceAmount : null
    return item
  })
  return arr2
}

const formatMidData = (data: any) => {
  if (data && data.length > 0) {
    const resultList = mergeTimeArr(ePrice.value, data) as any
    option2.series[0].name = t('gridInfo.pp_EPrice')
    option2.series[1].name = t('gridInfo.pp_min_price')
    option2.series[2].name = t('gridInfo.pp_max_price')
    option2.xAxis.data = resultList.map((item: any) => formatTime(item.ts, 10))
    option2.series[0].data = resultList.map((item: any) => item.priceAmount)
    option2.series[1].data = resultList.map((item: any) => item.min_price)
    option2.series[2].data = resultList.map((item: any) => item.max_price)
    nextTick(() => {
      if (chartDom2) option2.legend.selected = chartDom2.getOption().legend[0].selected
      echartRender2('Chart2', option2)
      echartsArr2.value = [chartDom2]
    })
  } else {
    option2.series[0].name = t('gridInfo.pp_EPrice')
    option2.series[1].name = ''
    option2.series[2].name = ''
    option2.xAxis.data = ePrice.value.map((item: any) => formatTime(item.time - 3600, 10))
    option2.series[0].data = ePrice.value.map((item: any) => item.priceAmount)
    option2.series[1].data = []
    option2.series[2].data = []
    nextTick(() => {
      if (chartDom2) option2.legend.selected = chartDom2.getOption().legend[0].selected
      echartRender2('Chart2', option2)
      echartsArr2.value = [chartDom2]
    })
  }
}

const formatBottomData = (data: any) => {
  if (JSON.stringify(data) != '{}') {
    isEmpty3.value = false

    const {battery_info, ...obj} = data
    battery_info.forEach((item: any) => {
      for (let key in item) {
        if (item[key] <= -9999) {
          item[key] = t('common.pp_no_data')
        }
      }
    })

    const newObj = {...obj}
    for (let key in newObj) {
      if (newObj[key] <= -9999) {
        newObj[key] = t('common.pp_no_data')
      }
    }
    list.value = {battery_info, ...newObj}
    list.value.power_schedule_mode = list.value.power_schedule_mode == t('common.pp_no_data') ? t('common.pp_no_data') : t(modeMap[data.power_schedule_mode])
    list.value.remote_control_enable = list.value.remote_control_enable == t('common.pp_no_data') ? t('common.pp_no_data') : controlEnableMap[data.remote_control_enable]
    list.value.protocol_version = list.value.protocol_version == t('common.pp_no_data') ? t('common.pp_no_data') : t(protocolVersionMap[data.protocol_version])
  } else {
    isEmpty3.value = true
  }
}

/**
 * @description: 关闭ws
 * @return {*}
 */
const closeSocket = () => {
  if (!!webSocket1.value && webSocket1.value.websocket != null) {
    webSocket1.value.close()
  }
  if (!!webSocket2.value && webSocket2.value.websocket != null) {
    webSocket2.value.close()
  }
  if (!!webSocket3.value && webSocket3.value.websocket != null) {
    webSocket3.value.close()
  }
}

/**
 * @description: 搜索
 * @return {*}
 */
const handleSearch = () => {
  router.push({
    path: location.pathname,
    query: {
      project: form.value.project,
      device_id: form.value.deviceId,
      time_limit: form.value.time_limit
    }
  })
  const params = {time_limit: form.value.time_limit}
  webSocket1.value = apiGetTopList(form.value.project, form.value.deviceId, params)
  webSocket1.value.init(
    (res: any) => {
      formatTopData(res.data)
    },
    () => {}
  )

  getNetherlandsEPrice(params)

  webSocket3.value = apiGetBottomList(form.value.project, form.value.deviceId)
  webSocket3.value.init(
    (res: any) => {
      formatBottomData(res.data)
    },
    () => {}
  )
}

/**
 * @description: 切换时间
 * @param {*} val
 * @return {*}
 */
const handleChangeTime = () => {
  closeSocket()
  handleSearch()
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    setTimeout(() => {
      echartsArr1.value.map((item: any) => item.resize())
      echartsArr2.value.map((item: any) => item.resize())
    }, 300)
  }
)

/**
 * @description: 获取电价
 * @param {*} params
 * @return {*}
 */
const getNetherlandsEPrice = async (params: any) => {
  try {
    const res = await apiGetNetherlandsEPrice()
    ePrice.value = res.data.points
    if (!res.err_code) {
      webSocket2.value = apiGetNetherlandsImbalancePrice(params)
      webSocket2.value.init(
        (res: any) => {
          formatMidData(res.data)
        },
        () => {}
      )
    } else {
      ElMessage.error(res.message)
    }
  } catch (error: any) {
    ElMessage.error(error)
  }
}

const initWeb = () => {
  let initParams: any = route.query
  form.value.time_limit = !!initParams.time_limit ? initParams.time_limit : '1h'
  handleSearch()
}

onBeforeMount(() => {
  initWeb()
})

onBeforeUnmount(() => {
  closeSocket()
})
</script>

<style lang="scss" scoped>
.afrr-info-container {
  font-family: 'Noto Sans';
  :deep(.upper-card) {
    .el-card__body {
      padding: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
  :deep(.lower-card) {
    .el-card__body {
      padding: 8px;
      display: flex;
      flex-direction: column;
    }
  }
  :deep(.el-col-7) {
    max-width: 14.28571429%;
    flex: 0 0 14.28571429%;
  }
  .header-right {
    display: flex;
    align-items: center;
  }
  .time-section {
    right: 20px;
    z-index: 2;
  }
  .soc-class {
    font-size: 16px;
    font-weight: 700;
    color: #9197a6;
  }
  .index-class {
    color: #5470c6;
  }
  .black-class {
    color: #303133;
  }
  .green-class {
    color: #15c483;
  }
}
</style>
