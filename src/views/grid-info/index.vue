<template>
  <div class="grid-info-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_power_grid') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_grid_info') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_grid_info') }}</div>
      </div>
      <div class="header-right">
        <div class="flex-box flex_a_i-center flex_j_c-flex-end width-full">
          <el-radio-group v-model="form.project" @change="handleChangeType">
            <el-radio :label="item.value" v-for="item in deviceTypeOptions">{{ $t(item.label) }}</el-radio>
          </el-radio-group>
          <el-button class="welkin-primary-button margin_l-40" v-if="hasPermission('function:power-grid:setting')" @click="handleOpenDialog">{{ $t('gridInfo.pp_setting') }}</el-button>
        </div>
        <div class="font-size-14" v-if="JSON.stringify(initRow) != '{}'">{{ initRow.description + ' - ' + initRow.device_id }}</div>
      </div>
    </div>

    <!-- 数据部分 -->
    <div class="swap-page-container">
      <div class="swap-table-container padding-20 margin_b-20 relative">
        <div class="time-section absolute flex-box flex_a_i-center">
          <el-button class="welkin-primary-button margin_r-20" @click="handleDownload">{{ $t('common.pp_download') }}</el-button>
          <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="$t('common.pp_to')" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
        </div>
        <div id="Chart0" class="width-full height-400" v-show="!isEmpty0"></div>
        <el-empty :description="$t('common.pp_empty')" class="width-full height-400" v-if="isEmpty0"></el-empty>
      </div>

      <div class="swap-table-container padding-20 margin_b-20 relative">
        <div class="time-section absolute">
          <el-radio-group v-model="form.time_limit" @change="handleChangeTime">
            <el-radio-button :label="item.value" v-for="item in timeOptions">{{ $t(item.label) }}</el-radio-button>
          </el-radio-group>
        </div>
        <div id="Chart1" class="width-full height-400" v-show="!isEmpty1"></div>
        <div id="Chart2" class="width-full height-240 margin_b-8" v-show="!isEmpty1"></div>
        <el-empty :description="$t('common.pp_empty')" class="width-full height-400" v-if="isEmpty1"></el-empty>
        <div id="Chart3" class="width-full height-240"></div>
      </div>

      <div class="swap-table-container padding-20" v-if="!isEmpty3">
        <div class="flex-box flex_w-wrap gap_12 margin_b-20">
          <el-card v-for="item in dataNameOptions" class="upper-card" :style="{backgroundColor: setCardBackground(item)}">
            <div class="font-size-13 margin_b-8">{{ $t(item.label) }}</div>
            <div class="font-size-14">{{ list[item.value] }} {{ list[item.value] == $t('common.pp_no_data') ? '' : item.unit }}</div>
          </el-card>
        </div>
        <div>
          <div class="font-size-18 margin_b-15 flex-box flex_j_c-space-between flex_a_i-center">
            <span>{{ $t('gridInfo.pp_battery_slot') }}</span>
            <div class="font-size-14 black-class">
              <span class="margin_r-20"
                >{{ $t('gridInfo.pp_reserve_75') }}：<span class="green-class">{{ list.battery_70_reserved_nums }}</span></span
              >
              <span
                >{{ $t('gridInfo.pp_reserve_100') }}：<span class="green-class">{{ list.battery_100_reserved_nums }}</span></span
              >
            </div>
          </div>
          <el-row :gutter="20" class="margin_b-20">
            <el-col :span="7" v-for="(item, index) in list.battery_info.slice(0, 6)">
              <el-card class="lower-card width-full font-size-14" :style="{backgroundColor: item.branch_work_status == 1 ? '#bef5e2' : '#fff'}">
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="font-weight-bold">{{ batteryTypeMap[item.bms_battery_type] ? $t(batteryTypeMap[item.bms_battery_type]) : $t('gridInfo.pp_reserve') }}</span>
                  <span class="soc-class">{{ getOneDecimalPlaces(item.bms_customer_usage_soc) }}%</span>
                </div>
                <div class="flex-box flex_j_c-center flex_a_i-center margin-8-n">
                  <div class="flex-box flex_d-column">
                    <span>{{ $t('gridInfo.pp_request') }}：</span>
                    <span>{{ $t('gridInfo.pp_distribute') }}：</span>
                    <span>{{ $t('gridInfo.pp_actual') }}：</span>
                  </div>
                  <div class="flex-box flex_d-column">
                    <span>{{ getOneDecimalPlaces(item.bms_chrg_power_limit_lt) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.distribute_power) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.battery_pack_power) }} kW</span>
                  </div>
                </div>
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="index-class">{{ item.slot_id }}#</span>
                  <span class="font-size-12">{{ $t('gridInfo.pp_limit') }}：{{ item.limit_power }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="list.battery_info.length > 6">
            <el-col :span="7" v-for="(item, index) in list.battery_info.slice(6)">
              <el-card class="lower-card width-full font-size-14" :style="{backgroundColor: item.branch_work_status == 1 ? '#bef5e2' : '#fff'}">
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="font-weight-bold">{{ batteryTypeMap[item.bms_battery_type] ? $t(batteryTypeMap[item.bms_battery_type]) : $t('gridInfo.pp_reserve') }}</span>
                  <span class="soc-class">{{ getOneDecimalPlaces(item.bms_customer_usage_soc) }}%</span>
                </div>
                <div class="flex-box flex_j_c-center flex_a_i-center margin-8-n">
                  <div class="flex-box flex_d-column">
                    <span>{{ $t('gridInfo.pp_request') }}：</span>
                    <span>{{ $t('gridInfo.pp_distribute') }}：</span>
                    <span>{{ $t('gridInfo.pp_actual') }}：</span>
                  </div>
                  <div class="flex-box flex_d-column">
                    <span>{{ getOneDecimalPlaces(item.bms_chrg_power_limit_lt) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.distribute_power) }} kW</span>
                    <span>{{ getOneDecimalPlaces(item.battery_pack_power) }} kW</span>
                  </div>
                </div>
                <div class="flex-box flex_j_c-space-between flex_a_i-center">
                  <span class="index-class">{{ item.slot_id }}#</span>
                  <span class="font-size-12">{{ $t('gridInfo.pp_limit') }}：{{ item.limit_power }}</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-empty :description="$t('common.pp_empty')" class="swap-table-container padding-20" v-else></el-empty>
    </div>

    <el-dialog width="800px" align-center v-model="settingDialogVisible" :title="$t('gridInfo.pp_device_list')" :close-on-click-modal="false" :show-close="false" class="device-dialog">
      <div>{{ $t('gridInfo.pp_selected_device') }}：{{ selected[0].description + ' - ' + selected[0].device_id }}</div>
      <div class="search-form-container padding_l-8 margin_b-10">
        <el-form :model="dialogForm" inline style="width: 100%">
          <el-form-item :label="$t('deviceManagement.pp_device')" style="width: 70%">
            <el-select v-model="dialogForm.device" filterable clearable :placeholder="$t('deviceManagement.pp_select_device_id')" class="width-full">
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + ' - ' + item.device_id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleDialogSearch" class="welkin-primary-button" :loading="dialogLoading">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="swap-table-container">
        <el-table
          ref="singleTableRef"
          style="width: 100%"
          row-key="device_id"
          v-loading="dialogLoading"
          :data="deviceList"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" reserve-selection />
          <el-table-column prop="device_id" :label="$t('deviceManagement.pp_device_id')" min-width="25%">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="$t('deviceManagement.pp_device_name')" min-width="25%" show-overflow-tooltip />
        </el-table>
        <div class="pagination-container" v-if="deviceList && deviceList.length > 0">
          <Page :page="pages" @change="handlePageChange" :pagerCount="5" />
        </div>
        <div class="flex-box flex_j_c-flex-end margin_b-20">
          <el-button @click="handleCancelSeletion" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
          <el-button @click="handleConfirmSelection" class="welkin-primary-button" style="margin-left: 4px">{{ $t('common.pp_confirm') }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {ref, onBeforeUnmount, computed, watch, nextTick} from 'vue'
import {useI18n} from 'vue-i18n'
import {useStore} from 'vuex'
import {formatTime, formatTimeToDay, clearJson, getOneDecimalPlaces, getEnv, getFinalEndTime, getDisabledDate} from '~/utils'
import {hasPermission} from '~/auth'
import {page} from '~/constvars/page'
import {ElMessage, ElTable} from 'element-plus'
import {useRoute, useRouter} from 'vue-router'
import {apiGetTopList, apiGetBottomList, apiGetWinningBidHistory, apiGetFcrdPrice, apiGetIncome} from '~/apis/power-grid'
import {apiGetDeviceNameMap, apiGetDeviceList} from '~/apis/device-management'
import {multiLineOption, lineOption, barOption, deviceTypeOptions, timeOptions, dataNameOptions, modeMap, controlEnableMap, protocolVersionMap, batteryTypeMap, initStartTime, initEndTime, getShortcuts} from './constant'
import * as echarts from 'echarts'
import _ from 'lodash'

const {t} = useI18n()
const store = useStore()
const route = useRoute()
const router = useRouter()
const webSocket1 = ref()
const webSocket2 = ref()
const webSocket3 = ref()
const isCollapse = computed(() => store.state.menus.collapse)
const singleTableRef = ref<InstanceType<typeof ElTable>>()
const datePicker = ref([initStartTime, initEndTime] as any)
const form = ref({
  project: 'PowerSwap2',
  time_limit: '1h',
  start_time: initStartTime,
  end_time: initEndTime
})
const dialogForm = ref({
  device: ''
})
const dialogSearchForm = ref({
  device: ''
})
const pages = ref(_.cloneDeep(page))
const isEmpty0 = ref(true)
const isEmpty1 = ref(true)
const isEmpty3 = ref(true)
const dialogLoading = ref(false)
const settingDialogVisible = ref(false)
const echartsArr0 = ref([] as any)
const echartsArr1 = ref([] as any)
const echartsArr2 = ref([] as any)
const deviceOptions = ref([] as any)
const deviceList = ref([] as any)
const selected = ref([] as any)
const priceList = ref([] as any)
const initRow = ref({} as any)
const list = ref({} as any)
const option0 = _.cloneDeep(barOption)
const option1 = _.cloneDeep(multiLineOption)
const option2 = _.cloneDeep(lineOption)
const option3 = _.cloneDeep(multiLineOption)

let chartDom0: any
let chartDom1: any
let chartDom2: any
let chartDom3: any

const echartRender0 = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  chartDom0 && chartDom0.dispose()
  chartDom0 = echarts.init(chartDom)
  chartDom.setAttribute('_echarts_instance_', '')
  option && chartDom0.setOption(option, true)
  window.addEventListener('resize', () => chartDom0.resize())
}

const echartRender1 = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  chartDom1 && chartDom1.dispose()
  chartDom1 = echarts.init(chartDom)
  chartDom.setAttribute('_echarts_instance_', '')
  option && chartDom1.setOption(option, true)
  window.addEventListener('resize', () => chartDom1.resize())
}

const echartRender2 = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  chartDom2 && chartDom2.dispose()
  chartDom2 = echarts.init(chartDom)
  chartDom.setAttribute('_echarts_instance_', '')
  option && chartDom2.setOption(option, true)
  window.addEventListener('resize', () => chartDom2.resize())
}

const echartRender3 = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  chartDom3 && chartDom3.dispose()
  chartDom3 = echarts.init(chartDom)
  chartDom.setAttribute('_echarts_instance_', '')
  option && chartDom3.setOption(option, true)
  window.addEventListener('resize', () => chartDom3.resize())
}

/**
 * @description: 下载收益数据
 * @return {*}
 */
const handleDownload = () => {
  window.open(`/web/welkin/fcrd/v1/fcrd-management/${form.value.project}/${initRow.value.device_id}/revenue/download?start_time=${form.value.start_time}&end_time=${form.value.end_time}`)
}

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = getFinalEndTime(new Date(val[1]).getTime())
  router.push({
    path: location.pathname,
    query: {
      device_id: initRow.value.device_id,
      ...form.value
    }
  })
  getIncome()
}

/**
 * @description: 站运行模式：fcrd(绿色),其他(红色)；换电可控状态：enable(绿色),其他(红色)
 * @param {*} item
 * @return {*}
 */
const setCardBackground = (item: any) => {
  if (item.label == 'gridInfo.pp_mode' && list.value[item.value] == t('gridInfo.pp_fcr_d')) {
    return '#bef5e2'
  } else if (item.label == 'gridInfo.pp_mode') {
    return '#ef9a9a'
  } else if (item.label == 'gridInfo.pp_control_enable' && list.value[item.value] == 'enable') {
    return '#bef5e2'
  } else if (item.label == 'gridInfo.pp_control_enable' && list.value[item.value] != 'enable') {
    return '#ef9a9a'
  } else {
    return '#fff'
  }
}

/**
 * @description: 获取设备下拉框选项
 * @return {*}
 */
const getDeviceMapList = async () => {
  const res = await apiGetDeviceNameMap(form.value.project)
  deviceOptions.value = res.data
}

/**
 * @description: 获取设备列表
 * @return {*}
 */
const getDeviceList = async (flag = true) => {
  const params = {
    device: dialogSearchForm.value.device,
    page_no: pages.value.current,
    page_size: pages.value.size
  }
  dialogLoading.value = true
  try {
    const res = await apiGetDeviceList(params, form.value.project)
    deviceList.value = res.data
    if (!flag) singleTableRef.value!.toggleRowSelection(initRow.value, true)
    pages.value.total = res.total
  } catch (error: any) {
    ElMessage.error(error)
  }
  dialogLoading.value = false
}

/**
 * @description: 获取默认的设备情况，用来回显选中状态
 * @param {*} device_id
 * @return {*}
 */
const getInitDevice = async (device_id: string) => {
  const params = {
    page_no: 1,
    page_size: 9999
  }
  try {
    const res = await apiGetDeviceList(params, form.value.project)
    initRow.value = res.data.filter((item: any) => item.device_id == device_id)[0]
    // initRow.value = {
    //   description: 'NIO Power Swap Station | DK Slagelse',
    //   device_id: 'PS-NIO-7db7c5aa-ff6ccc64'
    // }
    closeSocket()
    handleSearch()
  } catch (error: any) {
    ElMessage.error(error)
  }
}

/**
 * @description: 选中值变化
 * @param {*} selection
 * @return {*}
 */
const handleSelectionChange = (selection: any) => {
  if (selection.length > 1) {
    selection.shift()
    singleTableRef.value!.clearSelection()
    singleTableRef.value!.toggleRowSelection(selection[0], true)
  }
  selected.value = selection
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirmSelection = () => {
  if (selected.value.length > 0) {
    settingDialogVisible.value = false
    initRow.value = selected.value[0]
    closeSocket()
    handleSearch()
  } else {
    ElMessage.warning(t('gridInfo.pp_message'))
  }
}

/**
 * @description: 取消
 * @return {*}
 */
const handleCancelSeletion = () => {
  singleTableRef.value!.clearSelection()
  singleTableRef.value!.toggleRowSelection(initRow.value, true)
  settingDialogVisible.value = false
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(dialogForm.value)
  handleDialogSearch()
}

/**
 * @description: 搜索
 * @return {*}
 */
const handleDialogSearch = () => {
  dialogSearchForm.value = _.cloneDeep(dialogForm.value)
  pages.value.current = 1
  getDeviceList()
}

/**
 * @description: 打开弹窗
 * @return {*}
 */
const handleOpenDialog = () => {
  selected.value = _.cloneDeep([initRow.value])
  getDeviceMapList()
  getDeviceList(false)
  settingDialogVisible.value = true
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getDeviceList()
}

/**
 * @description: 切换二代站/三代站
 * @return {*}
 */
const handleChangeType = (val: any) => {
  const env = getEnv()
  dialogForm.value.device = ''
  dialogSearchForm.value.device = ''
  pages.value.current = 1
  if (val == 'PowerSwap2') {
    getInitDevice(env == '-eu' || env == '-stg-eu' ? 'PS-NIO-7db7c5aa-ff6ccc64' : 'PS-NIO-912d94fa-786c13b9')
  } else {
    getInitDevice(env == '-eu' || env == '-stg-eu' ? 'PS-NIO-cfc99e66-31c4ebb4' : 'PS-NIO-881d84b0-9b20682e')
  }
}

const formatTopData = (data: any) => {
  if (data && data.length > 0) {
    isEmpty1.value = false
    option1.xAxis.data = data.map((item: any) => formatTime(item.timestamp))
    option2.xAxis.data = data.map((item: any) => formatTime(item.timestamp))
    option1.series[0].name = t('gridInfo.pp_station_use_power')
    option1.series[1].name = t('gridInfo.pp_base_line_power')
    option1.series[2].name = t('gridInfo.pp_remote_distribute_power')
    option1.series[3].name = t('gridInfo.pp_charge_power_max')
    option1.series[4].name = t('gridInfo.pp_charge_power_min')
    option1.series[0].data = data.map((item: any) => item.station_use_power)
    option1.series[1].data = data.map((item: any) => item.base_line_power)
    option1.series[2].data = data.map((item: any) => item.remote_distribute_power)
    option1.series[3].data = data.map((item: any) => item.need_charge_power_min)
    option1.series[4].data = data.map((item: any) => item.allow_charge_power_max)
    option2.series[0].name = t('gridInfo.pp_gird_frequency')
    option2.series[0].data = data.map((item: any) => item.grid_frequency)
    nextTick(() => {
      if (chartDom1) option1.legend.selected = chartDom1.getOption().legend[0].selected
      if (chartDom2) option2.legend.selected = chartDom2.getOption().legend[0].selected
      echartRender1('Chart1', option1)
      echartRender2('Chart2', option2)
      echarts.connect([chartDom1, chartDom2])
      echartsArr1.value = [chartDom1, chartDom2]
    })
  } else {
    isEmpty1.value = true
  }
}

const mergeTimeArr = (arr1: any, arr2: any) => {
  let result = [] as any
  arr1.forEach((item: any, index: number) => {
    for (let time = item.start_time_reserved; time <= item.end_time_reserved; time += 1000) {
      let winning_bid_up_capacity = item.winning_bid_up_capacity
      if (time == item.end_time_reserved && index < arr1.length - 1 && arr1[index + 1].start_time_reserved == time) {
        winning_bid_up_capacity = arr1[index].winning_bid_up_capacity
      }
      result.push({
        time: time,
        winning_bid_up_capacity: winning_bid_up_capacity,
        fcrd_down_d_1_price: null,
        fcrd_down_d_2_price: null
      })
    }
  })
  arr2.forEach((item: any) => {
    let ts = item.ts * 1000
    result.forEach((res: any) => {
      if (res.time >= ts && res.time < ts + 7200000) {
        res.fcrd_down_d_1_price = item['fcrd_down_d-1_price']
        res.fcrd_down_d_2_price = item['fcrd_down_d-2_price']
      }
    })
  })
  return result
}

const formatMidData = (data: any) => {
  option3.color = ['#00bebe', '#67c23a', '#ffc031']
  if (data && data.length > 0) {
    const resultList = mergeTimeArr(data, priceList.value) as any
    option3.series[0].name = t('gridInfo.pp_bid_capactiy')
    option3.series[1].name = t('gridInfo.pp_d_1_price')
    option3.series[2].name = t('gridInfo.pp_d_2_price')
    option3.xAxis.data = resultList.map((item: any) => formatTime(item.time))
    option3.series[0].data = resultList.map((item: any) => item.winning_bid_up_capacity)
    option3.series[1].data = resultList.map((item: any) => item.fcrd_down_d_1_price)
    option3.series[2].data = resultList.map((item: any) => item.fcrd_down_d_2_price)
    nextTick(() => {
      if (chartDom3) option3.legend.selected = chartDom3.getOption().legend[0].selected
      echartRender3('Chart3', option3)
      echartsArr2.value = [chartDom3]
    })
  } else {
    option3.series[0].name = t('gridInfo.pp_d_1_price')
    option3.series[1].name = t('gridInfo.pp_d_2_price')
    option3.series[2].name = ''
    option3.xAxis.data = priceList.value.map((item: any) => formatTime(item.ts, 10))
    option3.series[0].data = priceList.value.map((item: any) => item['fcrd_down_d-1_price'])
    option3.series[1].data = priceList.value.map((item: any) => item['fcrd_down_d-2_price'])
    option3.series[2].data = []
    nextTick(() => {
      if (chartDom3) option3.legend.selected = chartDom3.getOption().legend[0].selected
      echartRender3('Chart3', option3)
      echartsArr2.value = [chartDom3]
    })
  }
}

const formatBottomData = (data: any) => {
  if (JSON.stringify(data) != '{}') {
    isEmpty3.value = false

    const {battery_info, ...obj} = data
    battery_info.forEach((item: any) => {
      for (let key in item) {
        if (item[key] <= -9999) {
          item[key] = t('common.pp_no_data')
        }
      }
    })

    const newObj = {...obj}
    for (let key in newObj) {
      if (newObj[key] <= -9999) {
        newObj[key] = t('common.pp_no_data')
      }
    }
    list.value = {battery_info, ...newObj}
    list.value.power_schedule_mode = list.value.power_schedule_mode == t('common.pp_no_data') ? t('common.pp_no_data') : t(modeMap[data.power_schedule_mode])
    list.value.remote_control_enable = list.value.remote_control_enable == t('common.pp_no_data') ? t('common.pp_no_data') : controlEnableMap[data.remote_control_enable]
    list.value.protocol_version = list.value.protocol_version == t('common.pp_no_data') ? t('common.pp_no_data') : t(protocolVersionMap[data.protocol_version])
  } else {
    isEmpty3.value = true
  }
}

/**
 * @description: 关闭ws
 * @return {*}
 */
const closeSocket = () => {
  if (!!webSocket1.value && webSocket1.value.websocket != null) {
    webSocket1.value.close()
  }
  if (!!webSocket2.value && webSocket2.value.websocket != null) {
    webSocket2.value.close()
  }
  if (!!webSocket3.value && webSocket3.value.websocket != null) {
    webSocket3.value.close()
  }
}

const getPrice = async (params: any) => {
  try {
    const res = await apiGetFcrdPrice(form.value.project)
    if (!res.err_code) {
      priceList.value = res.data.records
      webSocket2.value = apiGetWinningBidHistory(form.value.project, initRow.value.device_id, params)
      webSocket2.value.init(
        (res: any) => {
          formatMidData(res.data)
        },
        () => {}
      )
    } else {
      ElMessage.error(res.message)
    }
  } catch (error: any) {
    ElMessage.error(error)
  }
}

/**
 * @description: 获取收益数据
 * @return {*}
 */
const getIncome = async () => {
  const incomeParams = {
    start_time: form.value.start_time,
    end_time: form.value.end_time
  }
  try {
    const res = await apiGetIncome(incomeParams, form.value.project, initRow.value.device_id)
    if (!res.err_code) {
      if (res.history && res.total > 0) {
        isEmpty0.value = false
        option0.xAxis.data = res.history.map((item: any) => formatTimeToDay(item.timestamp))
        option0.yAxis[0].name = t('gridInfo.pp_bid') + ' (MW)'
        option0.yAxis[1].name = t('gridInfo.pp_revenue') + ' (Euro)'
        option0.series[0].name = t('gridInfo.pp_actual_bid')
        option0.series[0].data = res.history.map((item: any) => item.actual_bid_capacity)
        option0.series[1].name = t('gridInfo.pp_ideal_bid')
        option0.series[1].data = res.history.map((item: any) => item.ideal_bid_capacity)
        option0.series[2].name = t('gridInfo.pp_actual_revenue')
        option0.series[2].data = res.history.map((item: any) => (item.revenue / 100).toFixed(2))
        option0.series[3].name = t('gridInfo.pp_ideal_revenue')
        option0.series[3].data = res.history.map((item: any) => (item.ideal_revenue / 100).toFixed(2))
        nextTick(() => {
          echartRender0('Chart0', option0)
          echartsArr0.value = [chartDom0]
        })
      } else {
        isEmpty0.value = true
      }
    } else {
      ElMessage.error(res.message)
    }
  } catch (error: any) {
    ElMessage.error(error)
  }
}

const handleSearch = async (update = true) => {
  form.value.end_time = getFinalEndTime(form.value.end_time)
  router.push({
    path: location.pathname,
    query: {
      device_id: initRow.value.device_id,
      ...form.value
    }
  })

  if (update) getIncome()

  const params = {time_limit: form.value.time_limit}
  webSocket1.value = apiGetTopList(form.value.project, initRow.value.device_id, params)
  webSocket1.value.init(
    (res: any) => {
      formatTopData(res.data)
    },
    () => {}
  )

  getPrice(params)

  webSocket3.value = apiGetBottomList(form.value.project, initRow.value.device_id)
  webSocket3.value.init(
    (res: any) => {
      formatBottomData(res.data)
    },
    () => {}
  )
}

/**
 * @description: 切换时间
 * @param {*} val
 * @return {*}
 */
const handleChangeTime = (val: any) => {
  closeSocket()
  handleSearch(false)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    setTimeout(() => {
      echartsArr0.value.map((item: any) => item.resize())
      echartsArr1.value.map((item: any) => item.resize())
      echartsArr2.value.map((item: any) => item.resize())
    }, 300)
  }
)

const stopWatch = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
        form.value.start_time = Number(initParams.start_time)
        form.value.end_time = Number(initParams.end_time)
        datePicker.value = [form.value.start_time, form.value.end_time]
      }
      form.value.time_limit = !!initParams.time_limit ? initParams.time_limit : '1h'
      form.value.project = !!initParams.project ? initParams.project : 'PowerSwap2'
      if (initParams.device_id) {
        getInitDevice(initParams.device_id)
      } else if (form.value.project == 'PowerSwap2') {
        handleChangeType('PowerSwap2')
      } else {
        handleChangeType('PUS3')
      }
    }
  },
  {immediate: true}
)

onBeforeUnmount(() => {
  closeSocket()
  stopWatch()
})
</script>

<style lang="scss" scoped>
.grid-info-container {
  font-family: 'Noto Sans';
  :deep(.upper-card) {
    .el-card__body {
      padding: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
  :deep(.lower-card) {
    .el-card__body {
      padding: 8px;
      display: flex;
      flex-direction: column;
    }
  }
  :deep(.el-col-7) {
    max-width: 14.28571429%;
    flex: 0 0 14.28571429%;
  }
  :deep(.device-dialog) {
    .el-dialog__body {
      padding: 10px 20px;
    }
  }
  :deep(.el-table__header-wrapper .el-table__header .el-checkbox) {
    display: none;
  }
  .header-right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .time-section {
    right: 20px;
    z-index: 2;
  }
  .soc-class {
    font-size: 16px;
    font-weight: 700;
    color: #9197a6;
  }
  .index-class {
    color: #5470c6;
  }
  .black-class {
    color: #303133;
  }
  .green-class {
    color: #15c483;
  }
}
</style>
