import {i18n} from '~/i18n'

export const multiLineOption = {
  color: ['#6697ff', '#67c23a', '#ffc031', '#ff7575', '#5ecfff'],
  animation: false,
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    left: 'left',
    selected: {}
  },
  grid: {
    left: '0%',
    right: '0%',
    bottom: '6%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {show: false},
    axisLabel: {
      show: true,
      fontFamily: 'Noto Sans',
      color: '#22252B'
    },
    data: [] as any
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: true,
      fontFamily: 'Noto Sans',
      color: '#22252B'
    }
  },
  series: [
    {
      name: '',
      type: 'line',
      showSymbol: false,
      data: [] as any
    },
    {
      name: '',
      type: 'line',
      showSymbol: false,
      data: [] as any
    },
    {
      name: '',
      type: 'line',
      showSymbol: false,
      data: [] as any
    },
    {
      name: '',
      type: 'line',
      step: 'middle',
      showSymbol: false,
      data: [] as any
    },
    {
      name: '',
      type: 'line',
      step: 'middle',
      showSymbol: false,
      data: [] as any
    }
  ]
}

export const lineOption = {
  color: ['#00bebe'],
  animation: false,
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    left: 'left',
    selected: {}
  },
  grid: {
    left: '0%',
    right: '0%',
    bottom: '6%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {show: false},
    axisLabel: {
      show: true,
      fontFamily: 'Noto Sans',
      color: '#22252B'
    },
    data: [] as any
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: true,
      fontFamily: 'Noto Sans',
      color: '#22252B'
    }
  },
  series: [
    {
      name: '',
      type: 'line',
      showSymbol: false,
      data: [] as any
    }
  ]
}

export const barOption = {
  color: ['#00bebe', '#ffc031', '#ade8e8', '#ffc031'],
  animation: false,
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    left: 'left'
  },
  grid: {
    left: '2%',
    right: '3%',
    bottom: '6%',
    top: '20%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {show: false},
    axisLabel: {
      show: true,
      fontFamily: 'Noto Sans',
      color: '#22252B'
    },
    data: [] as any,
    axisPointer: {
      type: 'shadow'
    }
  },
  yAxis: [
    {
      name: '',
      nameLocation: 'middle',
      nameGap: 35,
      type: 'value',
      alignTicks: true,
      axisLabel: {
        show: true,
        fontFamily: 'Noto Sans',
        color: '#22252B'
      }
    },
    {
      name: '',
      nameLocation: 'middle',
      nameGap: 35,
      type: 'value',
      alignTicks: true,
      axisLabel: {
        show: true,
        fontFamily: 'Noto Sans',
        color: '#22252B'
      }
    }
  ],
  dataZoom: [
    {
      height: 12,
      bottom: 6,
      zoomOnMouseWheel: false,
      fillerColor: 'rgba(0, 190, 190, 0.1)',
      borderColor: '#DCE2E5',
      backgroundColor: '#fff',
      selectedDataBackground: {
        areaStyle: {
          color: 'rgba(0, 190, 190, 0.4)'
        },
        lineStyle: {
          color: 'rgba(0, 190, 190, 0.4)'
        }
      }
    }
  ],
  series: [
    {
      name: '',
      type: 'bar',
      barMaxWidth: 30,
      data: [] as any
    },
    {
      name: '',
      type: 'line',
      showSymbol: false,
      lineStyle: {
        type: 'dashed'
      },
      data: [] as any
    },
    {
      name: '',
      type: 'line',
      yAxisIndex: 1,
      showSymbol: false,
      data: [] as any
    },
    {
      name: '',
      type: 'line',
      yAxisIndex: 1,
      showSymbol: false,
      data: [] as any
    }
  ]
}

export const deviceTypeOptions = [
  {
    label: 'menu.pp_swap_station2',
    value: 'PowerSwap2'
  },
  {
    label: 'menu.pp_swap_station3',
    value: 'PUS3'
  }
]

export const timeOptions = [
  {
    label: 'gridInfo.pp_last_hour',
    value: '1h'
  },
  {
    label: 'gridInfo.pp_last_2_hours',
    value: '2h'
  },
  {
    label: 'gridInfo.pp_last_6_hours',
    value: '6h'
  },
  {
    label: 'gridInfo.pp_last_24_hours',
    value: '24h'
  }
]

export const dataNameOptions = [
  {
    label: 'gridInfo.pp_mode',
    value: 'power_schedule_mode',
    unit: ''
  },
  {
    label: 'gridInfo.pp_control_enable',
    value: 'remote_control_enable',
    unit: ''
  },
  {
    label: 'gridInfo.pp_protocol_version',
    value: 'protocol_version',
    unit: ''
  },
  {
    label: 'gridInfo.pp_power_limit',
    value: 'station_power_limit',
    unit: 'KVA'
  },
  {
    label: 'gridInfo.pp_charge_power_max',
    value: 'need_charge_power_min',
    unit: 'kW'
  },
  {
    label: 'gridInfo.pp_charge_power_min',
    value: 'allow_charge_power_max',
    unit: 'kW'
  },
  {
    label: 'gridInfo.pp_charge_energy_max',
    value: 'can_charge_energy_max',
    unit: 'kWh'
  },
  {
    label: 'gridInfo.pp_discharge_energy_max',
    value: 'can_discharge_energy_max',
    unit: 'kWh'
  },
  {
    label: 'gridInfo.pp_station_use_power',
    value: 'station_use_power',
    unit: 'kW'
  },
  {
    label: 'gridInfo.pp_base_line_power',
    value: 'base_line_power',
    unit: 'kW'
  },
  {
    label: 'gridInfo.pp_remote_distribute_power',
    value: 'remote_distribute_power',
    unit: 'kW'
  },
  {
    label: 'gridInfo.pp_min_power_coe',
    value: 'min_power_coe',
    unit: ''
  }
]

export const modeMap = {
  0: 'gridInfo.pp_off_peak',
  1: 'gridInfo.pp_a_frr',
  2: 'gridInfo.pp_peak_shaving',
  3: 'gridInfo.pp_normal_mode',
  4: 'gridInfo.pp_fcr_d',
  5: 'gridInfo.pp_vdb_mode'
}

export const controlEnableMap = {
  0: 'disable',
  1: 'enable'
}

export const protocolVersionMap = {
  0: 'gridInfo.pp_standard_version',
  1: 'gridInfo.pp_huaneng_version'
}

export const batteryTypeMap = {
  0: 'gridInfo.pp_battery_0',
  1: 'gridInfo.pp_battery_1',
  2: 'gridInfo.pp_battery_2',
  3: 'gridInfo.pp_battery_3',
  4: 'gridInfo.pp_battery_4',
  5: 'gridInfo.pp_battery_5',
  6: 'gridInfo.pp_battery_6',
  7: 'gridInfo.pp_battery_7',
  8: 'gridInfo.pp_battery_8',
  9: 'gridInfo.pp_battery_9',
  10: 'gridInfo.pp_battery_10',
  11: 'gridInfo.pp_battery_11',
  13: 'gridInfo.pp_battery_13',
  14: 'gridInfo.pp_battery_14'
}

const now = new Date()
const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30)
yesterday.setHours(0, 0, 0, 0)
export const initStartTime = yesterday.getTime()
export const initEndTime = now.getTime()

export const getShortcuts = () => {
  return [
    {
      text: i18n.global.t('common.pp_last_week'),
      value: [initStartTime + 3600 * 1000 * 24 * 24, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: [initStartTime, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_2_month'),
      value: [initStartTime - 3600 * 1000 * 24 * 31, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_3_month'),
      value: [initStartTime - 3600 * 1000 * 24 * 61, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_6_month'),
      value: [initStartTime - 3600 * 1000 * 24 * 152, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_year'),
      value: [initStartTime - 3600 * 1000 * 24 * 335, initEndTime]
    }
  ]
}
