<template>
  <div class="log-tree-container">
    <el-button @click="getLog" class="welkin-primary-button">
      <el-icon :size="'16px'">
        <Icon :icon="iconMap['download']" />
      </el-icon>
      {{ $t('logExport.pp_log_access') }}
    </el-button>
    <el-drawer v-model="drawerVisible" direction="rtl" @close="closeDrawer" size="40%">
      <template #header>
        <span class="drawer-title">{{ $t('logExport.pp_log_access') }}</span>
      </template>
      <template #default>
        <el-form ref="searchFormRef" :model="searchForm" :rules="drawRules" label-width="110px" label-position="left">
          <el-form-item :label="$t('common.pp_device')" prop="deviceId">
            <el-select v-model="searchForm.deviceId" filterable remote clearable :placeholder="$t('common.pp_enter')" :remote-method="remoteMethod" :loading="remoteLoading">
              <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="`${$t('logExport.pp_log_catalog')}`" prop="logCatalog">
            <WelkinSearchInput v-model="searchForm.logCatalog" :placeholder="`${$t('logExport.pp_select_log_catalog')}`" :options="logCatalogOptions?.[project.version]" @change="handleLogCommand" v-if="drawerVisible" />
          </el-form-item>

          <!-- 下发中 : 确认 -->
          <div class="button-container">
            <el-button @click="submitForm(searchFormRef)" :disabled="status === 1" class="welkin-primary-button">
              {{ status === 1 ? $t('logExport.pp_confirming') : $t('logExport.pp_confirm_button') }}
            </el-button>
            <el-button @click="clearDrawerData" class="welkin-secondary-button">
              {{ $t('common.pp_reset') }}
            </el-button>
          </div>

          <el-form-item :label="`${$t('logExport.pp_update_time')}`">
            <div class="drawer-form-time" v-if="status === 0">--</div>
            <div class="drawer-form-time">
              <div class="time-updating" v-if="status === 1">
                {{ loadingTime }}
              </div>
              <div class="time-success" v-if="status === 2">
                {{ loadingTime }}
              </div>
              <div class="time-error" v-if="status === 3">
                {{ loadingTime }}
              </div>

              <!-- status:3 更新超时,已拉超过10次 -->
              <el-popover placement="bottom-start" :width="200" trigger="hover" :content="`${$t('logExport.pp_tooltip')}`" v-if="status === 3">
                <template #reference>
                  <div :style="{ display: 'flex', alignItems: 'center' }">
                    <!-- <span class="time-error"> -->
                    {{ $t('logExport.pp_update_timeout') }}
                    <!-- </span> -->
                    <!-- #f53f3f  #00bebe-->
                    <el-icon :color="'#f53f3f'"><QuestionFilled /></el-icon>
                  </div>
                </template>
              </el-popover>

              <!-- status:1 更新中 -->
              <div class="updating-icon" v-if="status === 1">
                <el-icon :size="'20px'">
                  <Icon :icon="iconMap['loading']" />
                </el-icon>
                <div class="loading-title">
                  {{ $t('logExport.pp_updating') }}
                </div>
              </div>

              <!-- status:2 sync_ts>提交时间，还在拉取 -->
              <div class="updateSuccess-icon" v-if="status === 2 || status === 3">
                <el-icon :size="'20px'">
                  <Icon :icon="iconMap['refresh']" />
                </el-icon>
                <!-- 更新目录(绿text) -->
                <div class="loading-title" @click="submitForm(searchFormRef)">
                  {{ $t('logExport.pp_update_catalog') }}
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>

        <div>
          <el-form-item>
            <el-input v-model="filterText" :placeholder="`${$t('logExport.pp_file_name')}`">
              <template #suffix>
                <el-icon class="el-input__icon"><search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-tree :data="treeData.slice((pages.current - 1) * pages.size, pages.current * pages.size)" ref="treeRef" node-key="path" :default-expanded-keys="defaultExpandKeys.defaultKeys" :props="defaultProps" :filter-node-method="filterNode" @node-click="handleNodeClick" @node-expand="nodeExpand" @node-collapse="nodeCollapse" highlight-current>
            <template #default="{ node, data }">
              <el-icon v-if="data.type === 1" class="node-icon">
                <Folder />
              </el-icon>
              <el-icon v-if="data.type === 2" class="node-icon">
                <Document />
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
            </template>
          </el-tree>
          <div class="pagination-container" v-if="treeData && treeData.length > 0">
            <Page :page="pages" @change="handlePageChange" :pagerCount="5" :layout="'total, sizes, prev, pager, next'" />
          </div>
        </div>
      </template>
    </el-drawer>

    <el-dialog v-model="confirmDialogVisible" :title="`${$t('logExport.pp_transfer_log_files')}`" width="520px" top="30vh" :close-on-click-modal="false" class="confirm-dialog">
      <div class="first-line">
        <span class="normal-text">{{ $t('logExport.pp_confirm_one') }}</span>
        <span class="highlight-text">{{ logName }}</span>
        <span class="normal-text">{{ $t('logExport.pp_confirm_two') }}</span>
      </div>
      <div class="normal-text">{{ $t('logExport.pp_confirm_three') }}</div>
      <div>
        <span class="normal-text">{{ $t('logExport.pp_confirm_four') }}</span>
        <span class="highlight-text">
          {{ $t('logExport.pp_confirm_five') }}
        </span>
        <span class="normal-text">{{ $t('logExport.pp_confirm_six') }}</span>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="confirmDialogVisible = false" class="welkin-text-button">
            {{ $t('common.pp_cancel') }}
          </el-button>
          <el-button @click="confirmDownload" class="welkin-primary-button">
            {{ $t('logExport.pp_confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, PropType, reactive, computed, watch } from 'vue'
import { Search, Document, Folder, QuestionFilled } from '@element-plus/icons-vue'
import { onBeforeRouteLeave } from 'vue-router'
import { Icon } from '@iconify/vue/dist/iconify'
import { iconMap } from '~/auth'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElTree } from 'element-plus'
import { formatLocaleDate } from '~/utils'
import { useStore } from 'vuex'
import { apiGetDevices } from '~/apis/home'
import { logCatalogOptions } from '~/constvars/log-export'
import { apiGetDirectoryTree, apiPostLogInfo } from '~/apis/log-export'
import { useI18n } from 'vue-i18n'
import { debounce } from 'lodash-es'
import emitter from '~/utils/emitter'
const { t } = useI18n()
const $store = useStore()
const project = ref(computed(() => $store.state.project))

const defaultProps = {
  children: 'children',
  label: 'name',
  path: '',
  type: '' as number | string
}

interface DeviceObject {
  device_id: string
  description: string
}

const drawerVisible = ref(false)
const pages = ref({
  current: 1,
  size: 50,
  total: 0,
  sizes: [50, 100, 200, 500]
})

const treeRef = ref<InstanceType<typeof ElTree>>()
const treeData = ref([] as any)
const status = ref(0)
const submit_time = ref(0)
const loadingTime = ref('' as number | string)
const confirmDialogVisible = ref(false)
const remoteLoading = ref(false)
const logName = ref('')
const filterText = ref('')
const unfilterData = ref([] as any)
const deviceOptions = ref([] as any)

let leefPath = ref('')
let treeTimer: any = null
// 当检索字变化时，重新筛选
watch(filterText, (val) => {
  treeData.value = unfilterData.value.filter((item: any) => {
    return item.name.toLowerCase().includes(filterText.value.toLowerCase())
  })
  pages.value.current = 1
  pages.value.total = treeData.value.length
})

// 当目录树的data变化时，仍然保持过滤检索状态
watch(unfilterData, (newData) => {
  treeData.value = newData.filter((item: any) => {
    return item.name.toLowerCase().includes(filterText.value.toLowerCase())
  })
  pages.value.total = treeData.value.length
})

const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
}

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.name.includes(value)
}

const deviceIdRules = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error(t('logExport.pp_select_power_swap')))
  } else {
    return callback()
  }
}

const logCatalogRules = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error(t('logExport.pp_select_log_catalog')))
  } else {
    return callback()
  }
}

const drawRules = reactive<FormRules>({
  deviceId: [
    {
      validator: deviceIdRules,
      required: true,
      trigger: 'change'
    }
  ],
  logCatalog: [
    {
      validator: logCatalogRules,
      required: true,
      trigger: 'change'
    }
  ]
})

const searchFormRef = ref<FormInstance>()
const searchForm = reactive({
  deviceId: '',
  logCatalog: ''
})

let defaultExpandKeys = reactive({
  defaultKeys: [] as any
})

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: project.value.project, name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

// 可输入
const selectBlur = (e: any) => {
  if (e.target.value !== '') {
    searchForm.logCatalog = e.target.value
    defaultExpandKeys.defaultKeys = []
    defaultExpandKeys.defaultKeys.push(e.target.value)
  }
}

// 可选择
const selectChange = (val: any) => {
  searchForm.logCatalog = val
  defaultExpandKeys.defaultKeys = []
  defaultExpandKeys.defaultKeys.push(val)
}

const handleLogCommand = (val: any) => {
  defaultExpandKeys.defaultKeys = []
  defaultExpandKeys.defaultKeys.push(val)
}

// 发起生成文件树请求command
const refreshTree = async () => {
  const deviceId = searchForm.deviceId
  let query = {
    configuration: {
      key: 'syncFilePath',
      value: searchForm.logCatalog
    }
  }
  try {
    const { data } = (await apiPostLogInfo(deviceId, project.value.project, query)) as any
    if (data.err_code) {
      ElMessage.error(data.message)
    } else {
      status.value = 1
      loadingTime.value = '--'
    }
  } catch (error: any) {
    ElMessage.error(error)
  }
}

// 日志获取按钮
const getLog = () => {
  drawerVisible.value = true
  searchDeviceList(searchForm.deviceId || 'NIO')
}

// 查询文件目录树
const loopCount = ref(0)
const getTreeInfoQuery = reactive({
  device_id: ''
})

// 获取文件目录树
const getTreeInfo = () => {
  loopCount.value += 1
  getTreeInfoQuery.device_id = searchForm.deviceId
  return apiGetDirectoryTree(getTreeInfoQuery, project.value.project).then((res) => {
    if (loopCount.value > 10) {
      status.value = 3
      if (treeTimer) {
        clearInterval(treeTimer)
      }
    } else if (res.data !== null) {
      loadingTime.value = formatLocaleDate(res.data[0].sync_ts, false)

      if (res.data[0].is_refresh) {
        unfilterData.value = res.data[0].log_node
        status.value = 2
        if (treeTimer) {
          clearInterval(treeTimer)
        }
      } else {
        status.value = 1
      }
    }
  })
}

// 文件目录树循环请求
const treeLoop = () => {
  treeTimer = window.setInterval(() => {
    setTimeout(() => {
      getTreeInfo()
    }, 0)
  }, 5000)
}

// 点击确认按钮
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      treeData.value = []
      unfilterData.value = []
      if (treeTimer) {
        clearInterval(treeTimer)
      }
      pages.value.current = 1
      loopCount.value = 0
      submit_time.value = new Date().getTime()
      // 发送command
      refreshTree()
      // 循环拉取目录树
      treeLoop()
    }
  })
}

// 在节点展开时添加到默认展开数组
const nodeExpand = (data: any) => {
  defaultExpandKeys.defaultKeys.push(data.path)
}

// 收起时删除数组里对应选项
const nodeCollapse = (data: any) => {
  defaultExpandKeys.defaultKeys.splice(defaultExpandKeys.defaultKeys.indexOf(data.path), 1)
}

// 判断是否为叶子节点
const handleNodeClick = (data: any) => {
  logName.value = data.name
  if (!data.children) {
    confirmDialogVisible.value = true
    leefPath.value = data.path
  }
}

// 确认传输
const confirmDownload = async () => {
  const deviceId = searchForm.deviceId
  let query = {
    configuration: {
      key: 'uploadLogFile',
      value: leefPath.value
    }
  }
  try {
    const { data } = (await apiPostLogInfo(deviceId, project.value.project, query)) as any
    confirmDialogVisible.value = false
    if (data.err_code) ElMessage.error(data.message)
  } catch (error: any) {
    confirmDialogVisible.value = false
    ElMessage.error(error)
  }
}

// 重置按钮 清空Drawer
const clearDrawerData = () => {
  searchFormRef.value?.resetFields()
  status.value = 0
  loopCount.value = 0
  if (treeTimer) {
    clearInterval(treeTimer)
  }
  // 清空目录树
  treeData.value = []
  // 清空目录树的搜索关键词
  filterText.value = ''
  // 拉取缓存的时间
  loadingTime.value = ''
}

emitter.on('handleClearDrawer', clearDrawerData)

onBeforeRouteLeave(() => {
  emitter.off('handleClearDrawer')
  clearDrawerData()
})

// 关闭弹框
const closeDrawer = () => {
  drawerVisible.value = false
}
</script>

<style lang="scss" scoped>
.log-tree-container {
  .green-button {
    color: #fff;
    background-color: var(--el-color-primary);
    margin-left: 10px;
  }
  .button-container {
    justify-content: center;
  }
  .el-drawer {
    .el-drawer__header {
      .drawer-title {
        color: #22252b;
        font-size: 16px;
      }
      .el-drawer__close-btn:hover .el-icon {
        color: var(--el-color-primary);
      }
      .el-icon:hover {
        color: var(--el-color-primary);
      }
    }
    .el-drawer__body {
      display: flex;
      flex-direction: column;
      padding-left: 40px;
      .el-form {
        .el-form-item {
          height: 42px;
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          .el-form-item__label {
            color: #292c33;
            font-size: 14px;
            font-family: 'Noto Sans';
            padding: 0;
          }
          .el-button {
            margin-left: 10px;
            height: 30px;
          }
          .drawer-form-time {
            width: 90%;
            display: flex;
            align-items: center;
            .time-updating {
              color: #f7ba1e;
              margin: 0 8px;
            }
            .time-success {
              color: #666f7f;
              margin: 0 8px;
            }
            .time-error {
              color: #f53f3f;
              margin: 0 8px;
            }
            .updating-icon,
            .updateSuccess-icon {
              margin-left: 10px;
              display: flex;
              align-items: center;
              .el-icon {
                color: var(--el-color-primary);
                margin-right: 5px;
              }
              .loading-title {
                color: var(--el-color-primary);
                font-size: 14px;
              }
            }
            .updateSuccess-icon {
              cursor: pointer;
            }
            .el-only-child__content.el-tooltip__trigger.el-tooltip__trigger {
              color: #f53f3f;
              font-size: 14px;
              margin-left: 10px;
            }
          }
        }
      }
      .el-empty {
        flex: 1;
        .el-empty__description p {
          color: #91a2bc;
          font-family: 'Noto Sans';
        }
      }
      .el-select {
        width: 100%;
        .el-input {
          width: 100%;
        }
      }
    }
  }
  .confirm-dialog {
    border-radius: 4px;
    .device-title {
      font-size: 16px;
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }
    .el-dialog__header {
      .el-dialog__headerbtn:hover .el-dialog__close {
        color: var(--el-color-primary);
      }
      .el-dialog__headerbtn .el-dialog__close:hover {
        color: var(--el-color-primary);
      }
    }
    .el-dialog__body {
      padding-top: 10px;
      .first-line {
        margin-bottom: 20px;
      }
      .normal-text {
        color: #303133;
        font-weight: 400;
        font-family: 'Noto Sans';
      }
      .highlight-text {
        color: var(--el-color-primary);
        font-weight: 400;
        font-family: 'Noto Sans';
      }
    }
  }
}
</style>
