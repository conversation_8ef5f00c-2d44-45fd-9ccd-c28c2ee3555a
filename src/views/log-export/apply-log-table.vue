<template>
  <div>
    <el-table
      style="height: 250px; overflow-y: scroll"
      :data="data"
      :header-cell-style="{
        // 'text-align': 'center',
        fontSize: '14px',
        color: '#292C33',
        cursor: 'auto',
      }"
    >
      <!-- :cell-style="{ 'text-align': 'center' }" -->
      <el-table-column
        prop="file_path"
        :label="`${$t('logExport.pp_file_path')}`"
        min-width="20%"
        class-name="other-column-style"
        show-overflow-tooltip
      />
      <el-table-column
        prop="file_gen_status"
        :label="`${$t('logExport.pp_status')}`"
        min-width="10%"
        class-name="other-column-style"
      >
        <template #default="scope">
          <div
            class="status-column"
            v-if="scope.row.file_gen_status === '已上传'"
          >
            <el-icon :size="'20px'" class="check-icon">
              <Icon :icon="iconMap['check']" />
            </el-icon>
            <span>{{ $t('logExport.pp_done') }}</span>
          </div>
          <div
            class="status-column"
            v-if="scope.row.file_gen_status === '生成中'"
          >
            <time-logo />
            <span>{{ $t('logExport.pp_transmitting') }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="file_gen_time"
        :label="`${$t('logExport.pp_request_time')}`"
        min-width="20%"
        class-name="other-column-style"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ formatLocaleDate(scope.row.file_gen_time) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { ref, toRefs } from 'vue';
import { Icon } from '@iconify/vue/dist/iconify';
import { ArrowRight, Search, Document, Folder } from '@element-plus/icons-vue';
import { iconMap } from '~/auth';
import { formatLocaleDate } from '~/utils';

const props = defineProps<{
  data: any[];
}>();

const { data } = toRefs(props);
</script>
<style lang="scss">
.other-column-style {
  color: #22252b;
  font-family: 'Noto Sans';
  .status-column {
    display: flex;
    align-items: center;
    .check-icon {
      color: #00b42a;
      margin-right: 5px;
    }
    .close-icon {
      color: #f53f3f;
      margin-right: 5px;
    }
    .warning-icon {
      color: #f59d3f;
      margin-right: 5px;
    }
  }
}
</style>
