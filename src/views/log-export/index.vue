<template>
  <div class="log-export-container">
    <div class="header-container">
      <div class="header-left">
        <div class="header-title">{{ $t('menu.pp_log_list') }}</div>
        <el-tabs v-model="activeTab" @tab-change="handleTabChange" v-if="isPile">
          <el-tab-pane :label="$t('common.pp_psc2')" name="PowerThor" />
          <el-tab-pane :label="$t('common.pp_psc4')" name="PSC4" />
          <el-tab-pane :label="$t('common.pp_psc1')" name="PAC1" />
        </el-tabs>
      </div>

      <div :class="isPile ? 'header-right-pile' : 'header-right-station'" v-if="hasPilePermission">
        <!-- 国内环境申请下载不需要审批 -->
        <el-button @click="openWorkflowPage" class="welkin-secondary-button" v-if="needApplyToDown">
          {{ $t('logExport.pp_view_wf') }}
        </el-button>
        <LogTree :deviceOptions="deviceOptions" />
      </div>
    </div>

    <div class="content-container" v-if="hasPilePermission">
      <div class="search-container">
        <span class="search-item-label">{{ $t('common.pp_device') }}</span>
        <el-select v-model="searchForm.device_id" class="width-500" filterable remote clearable :placeholder="$t('common.pp_enter')" @change="searchEvent" :remote-method="remoteMethod" :loading="remoteLoading">
          <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
            <span style="float: left">{{ item.description }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
          </el-option>
        </el-select>
      </div>
      <el-table ref="multipleTableRef" :data="tableData" :row-key="rowKey" @selection-change="handleSelectionChange" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column type="selection" width="50" v-if="isApply" :reserve-selection="true" :selectable="selectable" />
        <el-table-column prop="file_path" :label="$t('logExport.pp_file_path')" min-width="300" show-overflow-tooltip />
        <el-table-column prop="file_gen_status" :label="$t('logExport.pp_status')" min-width="110" class-name="other-column-style">
          <template #default="scope">
            <div class="status-column" v-if="scope.row.file_gen_status === '已上传'">
              <el-icon :size="'20px'" class="check-icon">
                <Icon :icon="iconMap['check']" />
              </el-icon>
              <span>{{ $t('logExport.pp_done') }}</span>
            </div>
            <div class="status-column" v-if="scope.row.file_gen_status === '生成中'">
              <time-logo />
              <span>{{ $t('logExport.pp_transmitting') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="device_id" :label="$t('common.pp_device_id')" min-width="220">
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.device_id" />
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('common.pp_device_name')" min-width="240" show-overflow-tooltip />
        <el-table-column prop="md5_check_pass" :label="$t('logExport.pp_md5_check')" min-width="160" show-overflow-tooltip v-if="project.project == 'PowerSwap2'">
          <template #default="{row}">
            {{ isEmptyData(row.md5_check_pass) ? '-' : row.md5_check_pass ? $t('common.pp_yes') : $t('common.pp_no') }}
          </template>
        </el-table-column>
        <el-table-column prop="file_gen_time" :label="$t('logExport.pp_request_time')" min-width="170" show-overflow-tooltip>
          <template #default="scope">
            {{ formatLocaleDate(scope.row.file_gen_time) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.pp_operation')" width="100" align="center" class-name="operation-column" fixed="right">
          <template #default="scope">
            <div class="flex-box flex_j_c-center flex_a_i-center">
              <DownloadIcon v-if="scope.row.allow_download" style="cursor: pointer" color="#01A0AC" @click="downloadFile(scope.row)" />
              <DownloadIcon v-else color="#BFBFBF" style="cursor: not-allowed" />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-box flex_a_i-center margin_t-20" :class="needApplyToDown ? 'flex_j_c-space-between' : 'flex_j_c-flex-end'" v-if="tableData.length > 0">
        <!-- 国内环境 申请下载 不需要审批 -->
        <div v-if="needApplyToDown">
          <el-tooltip effect="dark" :content="`${$t('logExport.pp_batch_download_tooltip')}`" :disabled="!!searchForm.device_id">
            <span>
              <el-button class="welkin-primary-button" v-if="!isApply" :disabled="!searchForm.device_id" @click="logApply">
                {{ $t('logExport.pp_batch_download') }}
              </el-button>
            </span>
          </el-tooltip>
          <el-button class="welkin-secondary-button" v-if="isApply" @click="cancelApply">
            {{ $t('common.pp_cancel') }}
          </el-button>

          <el-button class="welkin-primary-button" @click="openApprovalDialog" v-if="multipleSelection.length > 0">
            {{ $t('logExport.pp_approval') }}
          </el-button>
        </div>
        <div class="common-pagination" style="margin-top: 0">
          <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>
      <el-dialog v-model="applicationDialogVisible" v-if="multipleSelection.length > 0" :title="`${$t('logExport.pp_approval')} ${multipleSelection[0].device_id}`" width="600px" :close-on-click-modal="false" class="confirm-dialog" @close="closeApplicationDialog(applicationFormRef)">
        <div class="device-title">{{ multipleSelection[0].description }}</div>
        <div v-loading="applicationLoading">
          <el-form ref="applicationFormRef" :model="applicationForm" :rules="applicationRules" label-width="80px">
            <el-form-item :label="`${$t('logExport.pp_reason')}`" prop="reason">
              <el-input v-model="applicationForm.reason" :placeholder="`${$t('logExport.pp_enter_reason')}`" />
            </el-form-item>
          </el-form>
          <applyLogTable :data="multipleSelection" />
        </div>
        <template #footer>
          <span class="dialog-footer" v-loading="applicationLoading">
            <el-button @click="applicationDialogVisible = false" class="welkin-text-button">
              {{ $t('common.pp_cancel') }}
            </el-button>
            <el-button @click="sendApproval(applicationFormRef)" class="welkin-primary-button">
              {{ $t('logExport.pp_confirm_button') }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
    <div class="font-size-14 color-26 padding_l-24" v-else>{{ $t('common.pp_lack_permission') }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, onBeforeUnmount, watch, computed } from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { Icon } from '@iconify/vue/dist/iconify'
import { iconMap, hasPermission } from '~/auth'
import { formatLocaleDate, getEnv, getUserId, removeNullProp, isEmptyData } from '~/utils'
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules, ElTable } from 'element-plus'
import { ElMessage } from 'element-plus'
import { apiGetDevices } from '~/apis/home'
import { apiGetUploadHistory, apiDownLoadLogFile, apiPostDownloadApproval } from '~/apis/log-export'
import { useStore } from 'vuex'
import LogTree from './log-tree.vue'
import applyLogTable from './apply-log-table.vue'
import DownloadIcon from '~/views/run-detail/component/icon/download-icon.vue'
import { pagination } from '~/constvars'
import emitter from '~/utils/emitter'
import { debounce } from 'lodash-es'

const { t } = useI18n()
const $route = useRoute()
const $router = useRouter()
const $store = useStore()

const project = ref(computed(() => $store.state.project))
const env = getEnv()
const deviceOptions = ref([] as any)
// 在海外需要申请通过才能下载
const needApplyToDown = !!env && ['-test', '-stg'].indexOf(env) == -1
// 查询换电站日志上传记录
const searchForm = reactive({
  user_id: '' as any,
  device_id: '',
  page: 1,
  size: 10,
  descending: true
})
const activeTab = ref('PowerThor' as any)
const totalNumber = ref(100)
const tableData = ref([] as any)
let tableTimer: any = null
const reasonRules = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error(t('logExport.pp_enter_reason')))
  } else {
    return callback()
  }
}
const isPile = ref(false)
// 是否显示申请按钮
const isApply = ref(false)
const remoteLoading = ref(false)
const applicationFormRef = ref<FormInstance>()
const applicationRules = reactive<FormRules>({
  reason: [
    {
      validator: reasonRules,
      required: true,
      trigger: 'blur'
    }
  ]
})
const applicationForm = reactive({
  reason: ''
})
const applicationDialogVisible = ref(false)
const applicationLoading = ref(false)
const multipleSelection = ref([] as any)
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const tabMap = {
  PAC1: 'powerCharge1',
  PowerThor: 'powerCharge2',
  PSC4: 'powerCharge4'
} as any
const hasPilePermission = computed(() => {
  if(isPile.value) {
    return hasPermission(`function:${tabMap[activeTab.value]}:log-export`)
  } else {
    return true
  }
})

const handleTabChange = () => {
  emitter.emit('handleClearDrawer')
  $store.commit('project/setProject', tabMap[activeTab.value])
  searchForm.device_id = ''
  searchDeviceList('NIO')
  searchEvent()
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: project.value.project, name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

// 下载
const downloadFile = (row: any) => {
  let query = {
    log_url: row.log_url
  }
  apiDownLoadLogFile(query)
}

// 打开workflow页面
const openWorkflowPage = () => {
  let url = 'https://groot.nioint.com/wf3/lark/instance-list'
  // 国内生产环境
  if (env != '' && !env) {
    return
  } else if (['', '-dev', '-test', '-stg'].indexOf(env) != -1) {
    url = `https://groot${env}.nioint.com/wf3/lark/instance-list`
  } else if (env == '-stg-eu') {
    url = `https://groot-stg.eu.nio.com/wf3/lark/instance-list`
  } else if (env == '-eu') {
    url = `https://groot.eu.nio.com/wf3/lark/instance-list`
  } else {
    return
  }
  window.open(url, '_blank')
}

const getUploadHistory = () => {
  if(!hasPilePermission.value) return
  searchForm.user_id = getUserId()
  return apiGetUploadHistory(searchForm, project.value.project).then((res) => {
    if (res.data !== null) {
      totalNumber.value = res.total
      tableData.value = res.data
    } else {
      tableData.value = []
    }
  })
}

const changeSearchUrl = () => {
  let query = {
    type: isPile.value ? activeTab.value : '',
    device_id: searchForm.device_id,
    page: searchForm.page,
    size: searchForm.size
  }
  $router.push({
    path: $router.currentRoute.value.path,
    query: { ...removeNullProp(query) }
  })
}

// 查询换电站日志上传记录循环请求
const tableLoop = () => {
  tableTimer = window.setInterval(() => {
    setTimeout(() => {
      getUploadHistory()
    }, 0)
  }, 5000)
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  searchForm.size = val
  searchForm.page = 1
  changeSearchUrl()
  getUploadHistory()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  searchForm.page = val
  changeSearchUrl()
  getUploadHistory()
}

// 筛选
const searchEvent = () => {
  isApply.value = false
  clearSelected()
  searchForm.page = 1
  searchForm.size = 10

  if (tableTimer) {
    clearInterval(tableTimer)
  }
  changeSearchUrl()
  getUploadHistory()
  tableLoop()
}

// 勾选
const handleSelectionChange = (val: any) => {
  multipleSelection.value = val
}

// 勾选状态
const rowKey = (row: any) => {
  return row._id
}

// 禁止勾选
const selectable = (row: any, index: any) => {
  if (row.file_gen_status === '已上传') {
    return true
  }
  return false
}

const clearSelected = () => {
  if (!!multipleTableRef?.value) {
    multipleTableRef.value!.clearSelection()
  }
}

// 显示table申请勾选列
const logApply = () => {
  isApply.value = true
}

const cancelApply = () => {
  isApply.value = false
  clearSelected()
}

// 打开申请二次确定调用框
const openApprovalDialog = () => {
  applicationDialogVisible.value = true
}

// 确认申请
const sendApproval = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  await formEl.validate((valid, fields) => {
    if (valid) {
      applicationLoading.value = true
      const d_name = multipleSelection.value?.[0].description
      const body = {
        description: d_name ? d_name : '',
        device_id: multipleSelection.value[0].device_id,
        project: project.value.project,
        reason: applicationForm.reason,
        request_files: multipleSelection.value.map((item: any) => {
          return {
            file_gen_time: item.file_gen_time,
            file_path: item.file_path
          }
        })
      }
      return apiPostDownloadApproval(body).then((res) => {
        if (res.err_code == 0) {
          ElMessage.success(t('logExport.pp_approve_success'))
          applicationDialogVisible.value = false
          clearSelected()
          isApply.value = false
          closeApplicationDialog(formEl)
        } else {
          ElMessage.error(res.message)
        }
        applicationLoading.value = false
      })
    }
  })
}

// 关闭申请调用框后清空
const closeApplicationDialog = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const onChangeVersion = () => {
  searchForm.page = 1
  searchForm.size = 10
  searchForm.device_id = ''
  if (tableTimer) {
    clearInterval(tableTimer)
  }
}

onBeforeRouteLeave(() => {
  if (tableTimer) {
    clearInterval(tableTimer)
  }
})

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    isPile.value = newPath == '/powerCharge/log-export'
    if (isPile.value) {
      activeTab.value = $route.query.type || 'PowerThor'
    }
    if (!oldPath) {
      let init_params: any = $route.query
      if (!!init_params.page && !!init_params.size) {
        searchForm.page = Number(init_params.page)
        searchForm.size = Number(init_params.size)
      }
      searchForm.device_id = init_params.device_id
      getUploadHistory()
      tableLoop()
    } else if (newPath.split('/')[2] == oldPath.split('/')[2]) {
      onChangeVersion()
      getUploadHistory()
      tableLoop()
    }
    searchDeviceList($route.query.device_id || 'NIO')
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  stopWatch()
})

onBeforeMount(() => {})
</script>

<style lang="scss" scoped>
.log-export-container {
  font-family: 'Blue Sky Standard';
  background: linear-gradient(180deg, #e2f9f9 -6.51%, rgba(255, 255, 255, 0.5) 12.89%);
  .header-container {
    padding: 24px 24px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
    .header-left {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 12px;
      .header-title {
        font-size: 20px;
        line-height: 28px;
        font-weight: 500;
        color: #1f1f1f;
      }
    }
    .header-right-pile {
      position: absolute;
      right: 24px;
      bottom: 30px;
      display: flex;
      align-items: center;
      gap: 16px;
    }
    .header-right-station {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.confirm-dialog) {
      .device-title {
        font-size: 16px;
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
      }
    }
    .search-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .search-item-label {
        font-size: 14px;
        color: #595959;
        margin-right: 8px;
      }
    }
  }
}
</style>
