<template>
  <div :class="props.isCharging ? 'rect-container light-rect-container' : 'rect-container'">
    <div class="flex-box flex_j_c-space-between margin-15">
      <ChargeSvg />
      <div class="flex-box flex_d-column flex_j_c-space-between font-size-14" v-if="!props.isEmpty && hasData">
        <span class="font-weight-bold">{{ props.chargeNum }} {{ $t('chargeModule.pp_pile') }}</span>
        <span class="font-weight-bold">{{ Number(props.output_energy) }} kW</span>
        <span>{{ Number(props.output_voltage).toFixed(2) }} V {{ Number(props.output_current).toFixed(2) }} A</span>
      </div>
      <div class="flex-box flex_d-column flex_j_c-space-between font-size-14 font-weight-bold" v-else-if="props.isEmpty">
        <span class="font-weight-bold">{{ props.chargeNum }} {{ $t('chargeModule.pp_pile') }}</span>
        <span>{{ $t('chargeModule.pp_pile_empty') }}</span>
      </div>
      <div class="flex-box flex_d-column flex_j_c-space-between font-size-14 font-weight-bold" v-else>
        <span class="font-weight-bold">{{ props.chargeNum }} {{ $t('chargeModule.pp_pile') }}</span>
        <span>{{ $t('chargeModule.pp_module_empty') }}</span>
      </div>
    </div>

    <el-divider border-color="#20cadb" class="margin-0" v-if="props.isCharging && !props.isEmpty && hasData"/>

    <div class="font-size-12" v-if="props.isCharging && !props.isEmpty && hasData">
      <div class="flex-box flex_j_c-space-between soc-box">
        <span>{{ $t('chargeModule.pp_car') }} SOC</span>
        <span>{{ props.bms_current_soc }}%</span>
      </div>
      <div class="flex-box flex_j_c-space-between common-box">
        <span>{{ $t('chargeModule.pp_bms_request') }}</span>
        <span>{{ $t('chargeModule.pp_output') }}</span>
      </div>
      <div class="flex-box flex_j_c-space-between common-box">
        <span>{{ Number(props.bms_request_voltage).toFixed(2) }} V</span>
        <span>{{ Number(props.output_voltage).toFixed(2) }} V</span>
      </div>
      <div class="flex-box flex_j_c-space-between last-box">
        <span>{{ Number(props.bms_request_current).toFixed(2) }} A</span>
        <span>{{ Number(props.output_current).toFixed(2) }} A</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ChargeSvg from './charge-svg.vue'

const props = defineProps({
  chargeNum: String,
  output_energy: Number,
  output_voltage: Number,
  output_current: Number,
  bms_current_soc: Number,
  bms_request_voltage: Number,
  bms_request_current: Number,
  isCharging: {
    type: Boolean,
    default: false
  },
  isEmpty: {
    type: Boolean,
    default: false
  },
  hasData: {
    type: Boolean,
    default: true
  }
})
</script>

<style lang="scss" scoped>
.rect-container {
  // height: 136px;
  width: 160px;
  border: 1px solid #000;
  border-radius: 4px;
  .soc-box {
    background-color: rgba(32, 202, 219, 0.1);
    padding: 10px 15px;
    font-weight: 900;
  }
  .common-box {
    margin: 6px 15px;
  }
  .last-box {
    margin: 6px 15px 15px;
  }
}
.light-rect-container {
  // height: 260px;
  border: 2px solid #20cadb;
}
</style>
