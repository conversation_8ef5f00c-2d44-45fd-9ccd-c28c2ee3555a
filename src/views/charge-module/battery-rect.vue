<template>
  <div :class="props.isCharging ? 'rect-container light-rect-container' : 'rect-container'" v-if="props.hasData && props.battery_nomal_capacity != -1">
    <div class="left-container">
      <span class="font-weight-bold font-size-14">{{ props.battery_nomal_capacity }} kWh</span>
      <span class="font-weight-bold font-size-20 gray-text">{{ props.slot }}</span>
    </div>
    <div class="left-container right-container">
      <span class="font-weight-bold font-size-18 gray-text">{{ getOneDecimalPlaces(Number(props.bms_real_soc)) }}<span class="font-size-12">({{ getOneDecimalPlaces(Number(bms_user_soc)) }})</span>%</span>
      <span>{{ props.battery_pack_voltage }} V</span>
      <span>{{ props.charging_request_current }} A</span>
    </div>
  </div>
  <div class="rect-container flex_a_i-center" v-else-if="props.hasData && props.battery_nomal_capacity == -1">
    <span class="font-weight-bold font-size-20 gray-text">{{ props.slot }}</span>
    <span class="font-weight-bold">{{ $t('chargeModule.pp_battery_empty') }}</span>
  </div>
  <div class="rect-container flex_a_i-center" v-else>
    <span class="font-weight-bold font-size-20 gray-text">{{ props.slot }}</span>
    <span class="font-weight-bold">{{ $t('chargeModule.pp_module_empty') }}</span>
  </div>
</template>

<script setup lang="ts">
import {getOneDecimalPlaces} from '~/utils'

const props = defineProps({
  slot: String,
  battery_nomal_capacity: Number,
  battery_pack_voltage: Number,
  charging_request_current: Number,
  bms_real_soc: Number,
  bms_user_soc: Number,
  isCharging: {
    type: Boolean,
    default: false
  },
  hasData: {
    type: Boolean,
    default: true
  }
})
</script>

<style lang="scss" scoped>
.rect-container {
  width: 160px;
  height: 90px;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border: 1px solid #000;
  border-radius: 4px;
  .left-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .right-container {
    align-items: center;
  }
  .gray-text {
    color: rgba(0, 0, 0, 0.20);
  }
}
.light-rect-container {
  border: 2px solid #20cadb;
  background-color: rgba(32, 202, 219, 0.05);
}
</style>