<template>
  <div class="data-container" v-if="props.hasData">
    <div class="left-container">
      <div class="sub-module width-full flex-box flex_j_c-center">
        <span class="margin_r-4">{{props.sub1_voltage}}V /</span>
        <span>{{props.sub1_current}}A</span>
      </div>
      <div class="sub-module margin-8-n width-full flex-box flex_j_c-center">
        <span class="margin_r-4">{{props.sub2_voltage}}V /</span>
        <span>{{props.sub2_current}}A</span>
      </div>
      <div class="sub-module width-full flex-box flex_j_c-center">
        <span class="margin_r-4">{{props.sub3_voltage}}V /</span>
        <span>{{props.sub3_current}}A</span>
      </div>
    </div>
    <div class="middle-container">
      <span>-</span>
      <span>-</span>
      <span>-</span>
    </div>
    <div class="right-container sub-module">
      <span class="font-weight-bold font-size-14">{{ props.name }}</span>
      <span class="white-space-nowrap">{{Number(props.output_voltage?.toFixed(2))}}V</span>
      <span class="white-space-nowrap">{{Number(props.output_current?.toFixed(2))}}A</span>
    </div>
  </div>
  <div class="rect-container flex_a_i-center" v-else>
    <span>{{ $t('chargeModule.pp_module_empty') }}</span>
    <span class="font-weight-bold">{{ props.name }}</span>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  name: String,
  output_current: Number,
  output_voltage: Number,
  sub1_voltage: Number,
  sub2_voltage: Number,
  sub3_voltage: Number,
  sub1_current: Number,
  sub2_current: Number,
  sub3_current: Number,
  hasData: Boolean
})
</script>

<style lang="scss" scoped>
.rect-container {
  width: 160px;
  height: 90px;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border: 1px solid #000;
  border-radius: 4px;
}
.data-container {
  width: 160px;
  height: 90px;
  display: flex;
  justify-content: space-between;
  .left-container,
  .middle-container,
  .right-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
  }
  .left-container {
    width: 102px;
    font-size: 12px;
    transform: scale(0.833);
  }
  .middle-container {
    flex: 1;
    margin-left: -8px;
  }
  .right-container {
    width: 60px;
  }
  .sub-module {
    border: 1px solid #000;
    border-radius: 4px;
    white-space: nowrap;
    padding: 4px;
  }
}
</style>
