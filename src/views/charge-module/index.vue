<template>
  <div class="charge-module-container">
    <div class="padding-20 header-container">
      <el-breadcrumb separator="/">
        <WelkinStationBreadcrumb :version="project.version" />
        <el-breadcrumb-item>{{ $t('chargeModule.pp_charge_module') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="search-container">
      <el-form :model="form" :rules="rules" inline ref="formRef" label-width="60px" class="width-full">
        <div class="collapse-search-container">
          <el-form-item :label="`${$t('deviceManagement.pp_device')}`" prop="device_id">
            <CommonDeviceSelect v-model="form.device_id" class="width-full" ref="commonDeviceSelectRef" />
          </el-form-item>
          <el-form-item :label="`${$t('common.pp_time')}`" prop="ts" class="required-form-item">
            <el-date-picker v-model="datePicker" type="datetime" :placeholder="$t('common.pp_please_select')" :shortcuts="dateShortcuts" :disabledDate="getDisabledDate" :clearable="false" />
            <el-tooltip effect="dark" :content="t('chargeModule.pp_tooltip')" placement="top">
              <div class="flex-box flex_a_i-center margin_l-10">
                <el-icon :size="18" color="#292C33"><InfoFilled /></el-icon>
              </div>
            </el-tooltip>
            <el-button @click="handleSearch(formRef)" class="welkin-primary-button margin_l-40" :loading="loading">{{ $t('common.pp_search') }}</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <div class="section-container flex-box flex_j_c-space-between" v-if="showMore">
      <div>
        <span class="font-weight-bold">{{ $t('chargeModule.pp_nio_pss') }}｜</span>
        <span>{{ deviceLabel }}</span>
      </div>
      <div class="flex-box">
        <span>{{ $t('chargeModule.pp_power_swap') }} &nbsp;&nbsp; {{ orderList.swap }} &nbsp;&nbsp;</span>
        <span>{{ $t('chargeModule.pp_charge') }} &nbsp;&nbsp; {{ orderList.charge }} &nbsp;&nbsp;</span>
        <span>{{ $t('chargeModule.pp_predict') }} &nbsp;&nbsp; {{ orderList.predict }} &nbsp;&nbsp;</span>
        <span>{{ $t('chargeModule.pp_staggered') }} &nbsp;&nbsp; {{ orderList.stagger }} &nbsp;&nbsp;</span>
        <span class="font-weight-bold">{{ $t('chargeModule.pp_order') }}： {{ orderList.total }} </span>
      </div>
    </div>

    <div class="section-container" v-if="showMore">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-row :gutter="10">
            <el-col :span="8">
              <div style="background-color: #f7f7f7; border-radius: 6px; padding: 15px 10px; height: 190px">
                <div class="font-weight-bold margin_b-10">{{ $t('chargeModule.pp_total_power') }}</div>
                <div class="font-weight-bold margin_b-25">{{ totalPower }} kW</div>
                <div class="flex-box flex_j_c-space-between margin_b-8 padding_b-5 with-border">
                  <span>{{ $t('chargeModule.pp_remaining') }}</span
                  ><span class="with-color">{{ getTwoDecimalPlaces(totalPower - swapPower - chargePower) }} kW</span>
                </div>
                <div class="flex-box flex_j_c-space-between margin_b-8">
                  <span>{{ $t('chargeModule.pp_power_swap') }}</span
                  ><span>{{ swapPower }} kW</span>
                </div>
                <div class="flex-box flex_j_c-space-between">
                  <span>{{ $t('chargeModule.pp_charge') }}</span
                  ><span>{{ chargePower }} kW</span>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div style="background-color: #f7f7f7; border-radius: 6px; padding: 15px 10px; height: 190px">
                <div class="font-weight-bold margin_b-10">{{ $t('chargeModule.pp_total_module') }}</div>
                <div class="font-weight-bold margin_b-25">10</div>
                <div class="flex-box flex_j_c-space-between margin_b-8 padding_b-5 with-border">
                  <span>{{ $t('chargeModule.pp_free') }}</span
                  ><span class="with-color">{{ 10 - swapModule - chargeModule }}</span>
                </div>
                <div class="flex-box flex_j_c-space-between margin_b-8">
                  <span>{{ $t('chargeModule.pp_power_swap') }}</span
                  ><span>{{ swapModule }}</span>
                </div>
                <div class="flex-box flex_j_c-space-between">
                  <span>{{ $t('chargeModule.pp_charge') }}</span
                  ><span>{{ chargeModule }}</span>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div style="background-color: #f7f7f7; border-radius: 6px; padding: 15px 10px; height: 190px">
                <div class="font-weight-bold margin_b-10">{{ $t('chargeModule.pp_total_battery') }}</div>
                <div class="font-weight-bold margin_b-25">{{ batteryNum }}</div>
                <div class="flex-box flex_j_c-space-between margin_b-8 padding_b-5 with-border">
                  <span>{{ $t('chargeModule.pp_full') }}</span
                  ><span class="with-color">{{ fullBattery }}</span>
                </div>
                <div class="flex-box flex_j_c-space-between margin_b-8">
                  <span>{{ $t('chargeModule.pp_a_full') }}</span
                  ><span>{{ fullBatteryA }}</span>
                </div>
                <div class="flex-box flex_j_c-space-between">
                  <span>{{ $t('chargeModule.pp_c_full') }}</span
                  ><span>{{ fullBatteryC }}</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="tableList.length > 5 ? 8 : 16">
          <el-table
            :data="tableList.slice(0, 5)"
            style="width: 100%"
            :row-style="{height: '0'}"
            :cell-style="{padding: '3px'}"
            :header-cell-style="{
              fontSize: '14px',
              color: '#292C33',
              cursor: 'auto',
              background: '#f7f7f7'
            }"
          >
            <el-table-column type="index" :label="$t('chargeModule.pp_index')" width="40" show-overflow-tooltip />
            <el-table-column prop="type" :label="$t('chargeModule.pp_type')" width="40" show-overflow-tooltip />
            <el-table-column prop="time" :label="$t('chargeModule.pp_order_time')" width="55" show-overflow-tooltip />
            <el-table-column prop="position" :label="$t('chargeModule.pp_position')" width="50" show-overflow-tooltip />
            <el-table-column prop="module" :label="$t('chargeModule.pp_module')" width="60" show-overflow-tooltip />
          </el-table>
        </el-col>
        <el-col :span="8" v-if="tableList.length > 5">
          <el-table
            :data="tableList.slice(5)"
            style="width: 100%"
            :row-style="{height: '0'}"
            :cell-style="{padding: '3px'}"
            :header-cell-style="{
              fontSize: '14px',
              color: '#292C33',
              cursor: 'auto',
              background: '#f7f7f7'
            }"
          >
            <el-table-column type="index" :index="indexMethod" :label="$t('chargeModule.pp_index')" width="40" show-overflow-tooltip />
            <el-table-column prop="type" :label="$t('chargeModule.pp_type')" width="40" show-overflow-tooltip />
            <el-table-column prop="time" :label="$t('chargeModule.pp_order_time')" width="55" show-overflow-tooltip />
            <el-table-column prop="position" :label="$t('chargeModule.pp_position')" width="50" show-overflow-tooltip />
            <el-table-column prop="module" :label="$t('chargeModule.pp_module')" width="60" show-overflow-tooltip />
          </el-table>
        </el-col>
      </el-row>
    </div>

    <div class="padding-20 flex-box relative draw-dom" v-if="showMore">
      <ChargeRect :chargeNum="'#1'" :hasData="hasChargeData1" :output_energy="chargeList['SCT1'].output_energy" :output_voltage="chargeList['SCT1'].output_voltage" :output_current="chargeList['SCT1'].output_current" :isCharging="isCharging1" :isEmpty="isEmpty1" :bms_current_soc="chargeList['SCT1'].bms_current_soc" :bms_request_voltage="chargeList['SCT1'].bms_request_voltage" :bms_request_current="chargeList['SCT1'].bms_request_current" style="position: absolute; left: 20px" />
      <canvas ref="canvasRef" width="1524" height="3440" style="position: absolute; left: 180px"></canvas>
      <ChargeRect :chargeNum="'#2'" :hasData="hasChargeData2" :output_energy="chargeList['SCT2'].output_energy" :output_voltage="chargeList['SCT2'].output_voltage" :output_current="chargeList['SCT2'].output_current" :isCharging="isCharging2" :isEmpty="isEmpty2" :bms_current_soc="chargeList['SCT2'].bms_current_soc" :bms_request_voltage="chargeList['SCT2'].bms_request_voltage" :bms_request_current="chargeList['SCT2'].bms_request_current" style="position: absolute; left: 942px" />

      <ChargeRect :chargeNum="'#3'" :hasData="hasChargeData3" :output_energy="chargeList['SCT3'].output_energy" :output_voltage="chargeList['SCT3'].output_voltage" :output_current="chargeList['SCT3'].output_current" :isCharging="isCharging3" :isEmpty="isEmpty3" :bms_current_soc="chargeList['SCT3'].bms_current_soc" :bms_request_voltage="chargeList['SCT3'].bms_request_voltage" :bms_request_current="chargeList['SCT3'].bms_request_current" style="position: absolute; left: 20px; top: 350px" />
      <ChargeRect :chargeNum="'#4'" :hasData="hasChargeData4" :output_energy="chargeList['SCT4'].output_energy" :output_voltage="chargeList['SCT4'].output_voltage" :output_current="chargeList['SCT4'].output_current" :isCharging="isCharging4" :isEmpty="isEmpty4" :bms_current_soc="chargeList['SCT4'].bms_current_soc" :bms_request_voltage="chargeList['SCT4'].bms_request_voltage" :bms_request_current="chargeList['SCT4'].bms_request_current" style="position: absolute; left: 942px; top: 350px" />

      <ModuleRect v-for="(item, index) in moduleList" :hasData="item.hasData" :name="item.name" :sub1_voltage="item.sub1_voltage" :sub2_voltage="item.sub2_voltage" :sub3_voltage="item.sub3_voltage" :sub1_current="item.sub1_current" :sub2_current="item.sub2_current" :sub3_current="item.sub3_current" :output_current="item.output_current" :output_voltage="item.output_voltage" :style="{position: 'absolute', left: '20px', top: `${640 + index * 110}px`}" />

      <div v-if="batteryList.length != 0">
        <BatteryRect v-for="(item, index) in batteryList.slice(10)" :hasData="true" :isCharging="(index + 1) % 2 == 0 ? switchList[`${(index + 1) / 2}#_KM5`] == 1 : switchList[`${(index + 2) / 2}#_KM1`] == 1" :slot="item.slot" :charging_request_current="item.charging_request_current" :battery_pack_voltage="item.battery_pack_voltage" :bms_real_soc="item.bms_real_soc" :bms_user_soc="item.bms_user_soc" :battery_nomal_capacity="item.battery_nomal_capacity" :style="{position: 'absolute', left: '942px', top: `${640 + index * 110}px`}" />
        <BatteryRect v-for="(item, index) in batteryList.slice(0, 10)" :hasData="true" :slot="item.slot" :charging_request_current="item.charging_request_current" :battery_pack_voltage="item.battery_pack_voltage" :bms_real_soc="item.bms_real_soc" :bms_user_soc="item.bms_user_soc" :battery_nomal_capacity="item.battery_nomal_capacity" :style="{position: 'absolute', left: '1122px', top: `${640 + index * 110}px`}" />
      </div>
      <div v-else>
        <BatteryRect v-for="(item, index) in 11" :hasData="false" :slot="`A${item}`" :style="{position: 'absolute', left: '942px', top: `${640 + index * 110}px`}" />
        <BatteryRect v-for="(item, index) in 10" :hasData="false" :slot="`C${item}`" :style="{position: 'absolute', left: '1122px', top: `${640 + index * 110}px`}" />
      </div>
    </div>

    <el-skeleton :rows="25" animated v-if="loading" class="margin-20" />
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch} from 'vue'
import {useStore} from 'vuex'
import {useI18n} from 'vue-i18n'
import {tableMockList} from './mock'
import {useRouter, useRoute} from 'vue-router'
import ChargeRect from './charge-rect.vue'
import ModuleRect from './module-rect.vue'
import BatteryRect from './battery-rect.vue'
import {FormInstance} from 'element-plus'
import {InfoFilled} from '@element-plus/icons-vue'
import {dateShortcuts, sortWithLetter, removeNullProp, getTwoDecimalPlaces} from '~/utils'
import {apiGetSwitchList, apiGetBatteryList, apiGetModuleList, apiGetChargeList, apiGetElectricityList} from '~/apis/charge-module'

const {t} = useI18n()
const store = useStore()
const route = useRoute()
const router = useRouter()
const project = ref(computed(() => store.state.project))
const formRef = ref<FormInstance>()
const commonDeviceSelectRef = ref()
const totalPower = ref(0)
const swapPower = ref(0)
const chargePower = ref(0)
const pilePower = ref(0)
const swapModule = ref(0)
const chargeModule = ref(0)
const batteryNum = ref(0)
const fullBattery = ref(0)
const fullBatteryA = ref(0)
const fullBatteryC = ref(0)
const datePicker = ref(new Date())
const loading = ref(false)
const showMore = ref(false)
const deviceLabel = ref('')
const form = ref({
  device_id: '',
  ts: '' as number | string
})
const moduleList = ref([] as any)
const switchList = ref({} as any)
const batteryList = ref([] as any)
const chargeList = ref({} as any)
const orderList = ref({} as any)
const tableList = ref([] as any)
const isEmpty1 = ref(false)
const isEmpty2 = ref(false)
const isEmpty3 = ref(false)
const isEmpty4 = ref(false)
const isCharging1 = ref(false)
const isCharging2 = ref(false)
const isCharging3 = ref(false)
const isCharging4 = ref(false)
const hasChargeData1 = ref(false)
const hasChargeData2 = ref(false)
const hasChargeData3 = ref(false)
const hasChargeData4 = ref(false)
const rules = reactive({
  device_id: [{required: true, message: t('chargeModule.pp_select_device'), trigger: 'change'}]
})

const canvasRef = ref<HTMLCanvasElement>()
const draw = () => {
  const canvas = canvasRef.value!
  const ctx = canvas.getContext('2d')!
  ctx.clearRect(0, 0, 762, 1720)
  canvas.width = 1524
  canvas.height = 3440
  ctx.scale(2, 2)
  ctx.strokeStyle = '#000'
  ctx.lineWidth = 1

  // #1、#2 横向连接
  for (let i = 0; i < 5; i++) {
    ctx.beginPath()
    if (i == 0) {
      ctx.moveTo(0, 50)
    } else {
      ctx.moveTo(50, 50 + (i - 1) * 50)
      ctx.lineTo(50, 100 + (i - 1) * 50)
      ctx.stroke()
    }
    ctx.lineTo(100, 50 + i * 50)
    ctx.stroke()
    ctx.lineTo(130, 30 + i * 50)
    ctx.stroke()
    ctx.moveTo(130, 50 + i * 50)
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i + 1} | 2`, 140, 40 + i * 50)
    ctx.lineTo(632, 50 + i * 50)
    ctx.fillText(`# ${i + 1} | 3`, 580, 40 + i * 50)
    ctx.stroke()
    ctx.lineTo(662, 30 + i * 50)
    ctx.stroke()
    ctx.moveTo(662, 50 + i * 50)
    if (i == 0) {
      ctx.lineTo(762, 50)
    } else {
      ctx.lineTo(712, 100 + (i - 1) * 50)
      ctx.stroke()
      ctx.lineTo(712, 50 + (i - 1) * 50)
    }
    ctx.stroke()
  }

  // #1、#2 纵向连接
  for (let i = 0; i < 5; i++) {
    ctx.beginPath()
    ctx.moveTo(230 + i * 60, 50 + i * 50)
    ctx.arc(230 + i * 60, 50 + i * 50, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    ctx.moveTo(230 + i * 60, 50 + i * 50)
    ctx.lineTo(230 + i * 60, 665 + i * 220)
    ctx.stroke()
    ctx.moveTo(230 + i * 60, 665 + i * 220)
    ctx.arc(230 + i * 60, 665 + i * 220, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
  }

  // #3、#4 横向连接
  for (let i = 0; i < 5; i++) {
    ctx.beginPath()
    if (i == 0) {
      ctx.moveTo(0, 380)
    } else {
      ctx.moveTo(50, 380 + (i - 1) * 50)
      ctx.lineTo(50, 430 + (i - 1) * 50)
      ctx.stroke()
    }
    ctx.lineTo(100, 380 + i * 50)
    ctx.stroke()
    ctx.lineTo(130, 360 + i * 50)
    ctx.stroke()
    ctx.moveTo(130, 380 + i * 50)
    ctx.fillText(`# ${i + 1} | 6`, 140, 370 + i * 50)
    ctx.lineTo(632, 380 + i * 50)
    ctx.fillText(`# ${i + 1} | 7`, 580, 370 + i * 50)
    ctx.stroke()
    ctx.lineTo(662, 360 + i * 50)
    ctx.stroke()
    ctx.moveTo(662, 380 + i * 50)
    if (i == 0) {
      ctx.lineTo(762, 380)
    } else {
      ctx.lineTo(712, 430 + (i - 1) * 50)
      ctx.stroke()
      ctx.lineTo(712, 380 + (i - 1) * 50)
    }
    ctx.stroke()
  }

  // #3、#4 纵向连接
  for (let i = 0; i < 5; i++) {
    ctx.beginPath()
    ctx.moveTo(260 + i * 60, 380 + i * 50)
    ctx.arc(260 + i * 60, 380 + i * 50, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    ctx.moveTo(260 + i * 60, 380 + i * 50)
    ctx.lineTo(260 + i * 60, 775 + i * 220)
    ctx.stroke()
    ctx.moveTo(260 + i * 60, 775 + i * 220)
    ctx.arc(260 + i * 60, 775 + i * 220, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
  }

  // 电池 横向连接
  for (let i = 0; i < 10; i++) {
    ctx.beginPath()
    ctx.moveTo(0, 665 + i * 110)
    ctx.lineTo(662, 665 + i * 110)
    ctx.font = '24px Noto Sans'
    ctx.fillText('+', 642, 660 + i * 110)
    ctx.stroke()
    ctx.lineTo(692, 645 + i * 110)
    ctx.stroke()
    ctx.moveTo(692, 665 + i * 110)
    ctx.fillText('-', 702, 660 + i * 110)
    ctx.lineTo(762, 665 + i * 110)
    ctx.stroke()
  }

  // 电池 纵向连接
  for (let i = 0; i < 5; i++) {
    ctx.beginPath()
    ctx.moveTo(592, 665 + i * 220)
    ctx.lineTo(592, 705 + i * 220)
    ctx.font = '24px Noto Sans'
    ctx.fillText('+', 572, 695 + i * 220)
    ctx.stroke()
    ctx.lineTo(612, 735 + i * 220)
    ctx.stroke()
    ctx.moveTo(592, 735 + i * 220)
    ctx.fillText('-', 572, 755 + i * 220)
    ctx.lineTo(592, 775 + i * 220)
    ctx.stroke()
  }

  // KM2开关动态闭合
  const drawLightCharge1 = (i: number) => {
    ctx.clearRect(100, 30 + (i - 1) * 50, 30, 20)
    ctx.beginPath()
    if (i == 1) {
      ctx.moveTo(0, 50)
      ctx.lineTo(230, 50)
      ctx.lineTo(230, 665)
      ctx.lineTo(0, 665)
    } else {
      ctx.moveTo(0, 50)
      ctx.lineTo(50, 50)
      ctx.lineTo(50, 100 + (i - 2) * 50)
      ctx.lineTo(290 + (i - 2) * 60, 100 + (i - 2) * 50)
      ctx.lineTo(290 + (i - 2) * 60, 885 + (i - 2) * 220)
      ctx.lineTo(0, 885 + (i - 2) * 220)
    }
    ctx.lineWidth = 2
    ctx.strokeStyle = '#20CADB'
    ctx.stroke()
    ctx.beginPath()
    ctx.fillStyle = '#20CADB'
    ctx.moveTo(230 + (i - 1) * 60, 50 + (i - 1) * 50)
    ctx.arc(230 + (i - 1) * 60, 50 + (i - 1) * 50, 2, 0, 2 * Math.PI)
    ctx.moveTo(230 + (i - 1) * 60, 665 + (i - 1) * 220)
    ctx.arc(230 + (i - 1) * 60, 665 + (i - 1) * 220, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 2`, 140, 40 + (i - 1) * 50)
  }

  // KM3开关动态闭合
  const drawLightCharge2 = (i: number) => {
    ctx.clearRect(632, 30 + (i - 1) * 50, 30, 20)
    ctx.beginPath()
    if (i == 1) {
      ctx.moveTo(762, 50)
      ctx.lineTo(230, 50)
      ctx.lineTo(230, 665)
      ctx.lineTo(0, 665)
    } else {
      ctx.moveTo(762, 50)
      ctx.lineTo(712, 50)
      ctx.lineTo(712, 100 + (i - 2) * 50)
      ctx.lineTo(290 + (i - 2) * 60, 100 + (i - 2) * 50)
      ctx.lineTo(290 + (i - 2) * 60, 885 + (i - 2) * 220)
      ctx.lineTo(0, 885 + (i - 2) * 220)
    }
    ctx.lineWidth = 2
    ctx.strokeStyle = '#20CADB'
    ctx.stroke()
    ctx.beginPath()
    ctx.fillStyle = '#20CADB'
    ctx.moveTo(230 + (i - 1) * 60, 50 + (i - 1) * 50)
    ctx.arc(230 + (i - 1) * 60, 50 + (i - 1) * 50, 2, 0, 2 * Math.PI)
    ctx.moveTo(230 + (i - 1) * 60, 665 + (i - 1) * 220)
    ctx.arc(230 + (i - 1) * 60, 665 + (i - 1) * 220, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 3`, 580, 40 + (i - 1) * 50)
  }

  // KM6开关动态闭合
  const drawLightCharge3 = (i: number) => {
    ctx.clearRect(100, 360 + (i - 1) * 50, 30, 20)
    ctx.beginPath()
    if (i == 1) {
      ctx.moveTo(0, 380)
      ctx.lineTo(260, 380)
      ctx.lineTo(260, 775)
      ctx.lineTo(0, 775)
    } else {
      ctx.moveTo(0, 380)
      ctx.lineTo(50, 380)
      ctx.lineTo(50, 430 + (i - 2) * 50)
      ctx.lineTo(320 + (i - 2) * 60, 430 + (i - 2) * 50)
      ctx.lineTo(320 + (i - 2) * 60, 995 + (i - 2) * 220)
      ctx.lineTo(0, 995 + (i - 2) * 220)
    }
    ctx.lineWidth = 2
    ctx.strokeStyle = '#20CADB'
    ctx.stroke()
    ctx.beginPath()
    ctx.fillStyle = '#20CADB'
    ctx.moveTo(260 + (i - 1) * 60, 380 + (i - 1) * 50)
    ctx.arc(260 + (i - 1) * 60, 380 + (i - 1) * 50, 2, 0, 2 * Math.PI)
    ctx.moveTo(260 + (i - 1) * 60, 775 + (i - 1) * 220)
    ctx.arc(260 + (i - 1) * 60, 775 + (i - 1) * 220, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 6`, 140, 370 + (i - 1) * 50)
  }

  // KM7开关动态闭合
  const drawLightCharge4 = (i: number) => {
    ctx.clearRect(632, 360 + (i - 1) * 50, 30, 20)
    ctx.beginPath()
    if (i == 1) {
      ctx.moveTo(762, 380)
      ctx.lineTo(260, 380)
      ctx.lineTo(260, 775)
      ctx.lineTo(0, 775)
    } else {
      ctx.moveTo(762, 380)
      ctx.lineTo(712, 380)
      ctx.lineTo(712, 430 + (i - 2) * 50)
      ctx.lineTo(320 + (i - 2) * 60, 430 + (i - 2) * 50)
      ctx.lineTo(320 + (i - 2) * 60, 995 + (i - 2) * 220)
      ctx.lineTo(0, 995 + (i - 2) * 220)
    }
    ctx.lineWidth = 2
    ctx.strokeStyle = '#20CADB'
    ctx.stroke()
    ctx.beginPath()
    ctx.fillStyle = '#20CADB'
    ctx.moveTo(260 + (i - 1) * 60, 380 + (i - 1) * 50)
    ctx.arc(260 + (i - 1) * 60, 380 + (i - 1) * 50, 2, 0, 2 * Math.PI)
    ctx.moveTo(260 + (i - 1) * 60, 775 + (i - 1) * 220)
    ctx.arc(260 + (i - 1) * 60, 775 + (i - 1) * 220, 2, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 7`, 580, 370 + (i - 1) * 50)
  }

  // KM1横向开关动态闭合
  const drawLightBattery1 = (i: number) => {
    ctx.clearRect(662, 645 + (i - 1) * 220, 30, 20)
    ctx.beginPath()
    ctx.moveTo(762, 665 + (i - 1) * 220)
    ctx.lineTo(0, 665 + (i - 1) * 220)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#20CADB'
    ctx.fillStyle = '#20CADB'
    ctx.font = '24px Noto Sans'
    ctx.fillText('+', 642, 660 + (i - 1) * 220)
    ctx.fillText('-', 702, 660 + (i - 1) * 220)
    ctx.stroke()
  }

  // KM4纵向开关动态闭合
  const drawLightBattery4 = (i: number) => {
    ctx.clearRect(592, 705 + (i - 1) * 220, 20, 30)
    ctx.beginPath()
    ctx.moveTo(0, 665 + (i - 1) * 220)
    ctx.lineTo(592, 665 + (i - 1) * 220)
    ctx.lineTo(592, 775 + (i - 1) * 220)
    ctx.lineTo(0, 775 + (i - 1) * 220)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#20CADB'
    ctx.fillStyle = '#20CADB'
    ctx.font = '24px Noto Sans'
    ctx.fillText('+', 572, 695 + (i - 1) * 220)
    ctx.fillText('-', 572, 755 + (i - 1) * 220)
    ctx.stroke()
  }

  // KM5横向开关动态闭合
  const drawLightBattery5 = (i: number) => {
    ctx.clearRect(662, 755 + (i - 1) * 220, 30, 20)
    ctx.beginPath()
    ctx.moveTo(762, 775 + (i - 1) * 220)
    ctx.lineTo(0, 775 + (i - 1) * 220)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#20CADB'
    ctx.fillStyle = '#20CADB'
    ctx.font = '24px Noto Sans'
    ctx.fillText('+', 642, 770 + (i - 1) * 220)
    ctx.fillText('-', 702, 770 + (i - 1) * 220)
    ctx.stroke()
  }

  // KM2开关异常
  const drawAbnormalCharge1 = (i: number) => {
    ctx.beginPath()
    ctx.moveTo(100, 50 + (i - 1) * 50)
    ctx.lineTo(130, 30 + (i - 1) * 50)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#EF5350'
    ctx.fillStyle = '#EF5350'
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 2`, 140, 40 + (i - 1) * 50)
    ctx.stroke()
  }

  // KM3开关异常
  const drawAbnormalCharge2 = (i: number) => {
    ctx.beginPath()
    ctx.moveTo(632, 50 + (i - 1) * 50)
    ctx.lineTo(662, 30 + (i - 1) * 50)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#EF5350'
    ctx.fillStyle = '#EF5350'
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 3`, 580, 40 + (i - 1) * 50)
    ctx.stroke()
  }

  // KM6开关异常
  const drawAbnormalCharge3 = (i: number) => {
    ctx.beginPath()
    ctx.moveTo(100, 380 + (i - 1) * 50)
    ctx.lineTo(130, 360 + (i - 1) * 50)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#EF5350'
    ctx.fillStyle = '#EF5350'
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 6`, 140, 370 + (i - 1) * 50)
    ctx.stroke()
  }

  // KM7开关异常
  const drawAbnormalCharge4 = (i: number) => {
    ctx.beginPath()
    ctx.moveTo(632, 380 + (i - 1) * 50)
    ctx.lineTo(662, 360 + (i - 1) * 50)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#EF5350'
    ctx.fillStyle = '#EF5350'
    ctx.font = 'bold 14px Noto Sans'
    ctx.fillText(`# ${i} | 7`, 580, 370 + (i - 1) * 50)
    ctx.stroke()
  }

  // KM4开关异常
  const drawAbnormalBattery4 = (i: number) => {
    ctx.beginPath()
    ctx.moveTo(592, 705 + (i - 1) * 220)
    ctx.lineTo(612, 735 + (i - 1) * 220)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#EF5350'
    ctx.fillStyle = '#EF5350'
    ctx.font = 'bold 24px Noto Sans'
    ctx.fillText('+', 572, 695 + (i - 1) * 220)
    ctx.fillText('-', 572, 755 + (i - 1) * 220)
    ctx.stroke()
  }

  // KM1开关异常
  const drawAbnormalBattery1 = (i: number) => {
    ctx.beginPath()
    ctx.moveTo(662, 665 + (i - 1) * 220)
    ctx.lineTo(692, 645 + (i - 1) * 220)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#EF5350'
    ctx.fillStyle = '#EF5350'
    ctx.font = 'bold 24px Noto Sans'
    ctx.fillText('+', 642, 660 + (i - 1) * 220)
    ctx.fillText('-', 702, 660 + (i - 1) * 220)
    ctx.stroke()
  }

  // KM5开关异常
  const drawAbnormalBattery5 = (i: number) => {
    ctx.beginPath()
    ctx.moveTo(662, 775 + (i - 1) * 220)
    ctx.lineTo(692, 755 + (i - 1) * 220)
    ctx.lineWidth = 2
    ctx.strokeStyle = '#EF5350'
    ctx.fillStyle = '#EF5350'
    ctx.font = 'bold 24px Noto Sans'
    ctx.fillText('+', 642, 770 + (i - 1) * 220)
    ctx.fillText('-', 702, 770 + (i - 1) * 220)
    ctx.stroke()
  }

  // #1 桩的动态高亮 / 异常
  for (let i = 1; i < 6; i++) {
    if (switchList.value[`${i}#_KM2`] == 1) {
      drawLightCharge1(i)
    }
    if (switchList.value[`${i}#_KM2`] == 2) {
      drawAbnormalCharge1(i)
    }
  }

  // #2 桩的动态高亮 / 异常
  for (let i = 1; i < 6; i++) {
    if (switchList.value[`${i}#_KM3`] == 1) {
      drawLightCharge2(i)
    }
    if (switchList.value[`${i}#_KM3`] == 2) {
      drawAbnormalCharge2(i)
    }
  }

  // #3 桩的动态高亮 / 异常
  for (let i = 1; i < 6; i++) {
    if (switchList.value[`${i}#_KM6`] == 1) {
      drawLightCharge3(i)
    }
    if (switchList.value[`${i}#_KM6`] == 2) {
      drawAbnormalCharge3(i)
    }
  }

  // #4 桩的动态高亮 / 异常
  for (let i = 1; i < 6; i++) {
    if (switchList.value[`${i}#_KM7`] == 1) {
      drawLightCharge4(i)
    }
    if (switchList.value[`${i}#_KM7`] == 2) {
      drawAbnormalCharge4(i)
    }
  }

  // KM1 电池的动态高亮
  for (let i = 1; i < 6; i++) {
    if (switchList.value[`${i}#_KM1`] == 1) {
      drawLightBattery1(i)
    }
    if (switchList.value[`${i}#_KM1`] == 2) {
      drawAbnormalBattery1(i)
    }
  }

  // KM5 电池的动态高亮
  for (let i = 1; i < 6; i++) {
    if (switchList.value[`${i}#_KM5`] == 1) {
      drawLightBattery5(i)
    }
    if (switchList.value[`${i}#_KM5`] == 2) {
      drawAbnormalBattery5(i)
    }
  }

  // KM4 电池的动态高亮 / 异常
  for (let i = 1; i < 6; i++) {
    if (switchList.value[`${i}#_KM4`] == 1) {
      drawLightBattery4(i)
    } else if (switchList.value[`${i}#_KM4`] == 2) {
      drawAbnormalBattery4(i)
    }
  }
}

const getDisabledDate = (time: Record<string, any>) => time.getTime() > Date.now()

/**
 * @description: 自定义索引
 * @param {*} index
 * @return {*}
 */
const indexMethod = (index: number) => {
  return index + 6
}

/**
 * @description: 处理模块数据
 * @param {*} obj
 * @return {*}
 */
const formatModuleData = (obj: any) => {
  moduleList.value = []
  Object.keys(obj).forEach((item) => {
    let objArr = obj[item]
    if (obj[item].length == 0) {
      moduleList.value.push({
        name: item,
        hasData: false
      })
    } else {
      const sub1_voltage = getTwoDecimalPlaces(objArr[0].output_voltage)
      const sub1_current = getTwoDecimalPlaces(objArr[0].output_current)
      const sub2_voltage = getTwoDecimalPlaces(objArr[1].output_voltage)
      const sub2_current = getTwoDecimalPlaces(objArr[1].output_current)
      const sub3_voltage = getTwoDecimalPlaces(objArr[2].output_voltage)
      const sub3_current = getTwoDecimalPlaces(objArr[2].output_current)
      moduleList.value.push({
        name: item,
        sub1_voltage: sub1_voltage,
        sub1_current: sub1_current,
        sub2_voltage: sub2_voltage,
        sub2_current: sub2_current,
        sub3_voltage: sub3_voltage,
        sub3_current: sub3_current,
        output_voltage: Number(((sub1_voltage + sub2_voltage + sub3_voltage) / 3).toFixed(2)),
        output_current: sub1_current + sub2_current + sub3_current,
        hasData: true
      })
    }
  })
}

/**
 * @description: 换电模块数量
 * @param {*} obj
 * @return {*}
 */
const getSwapModule = (obj: any) => {
  swapModule.value = 0
  for (let i = 1; i < 6; i++) {
    if (obj[`${i}#_KM1`] == 1 && obj[`${i}#_KM4`] == 1) {
      swapModule.value += 2
    } else if (obj[`${i}#_KM1`] == 1) {
      swapModule.value += 1
    }
    if (obj[`${i}#_KM5`] == 1 && obj[`${i}#_KM4`] == 1) {
      swapModule.value += 2
    } else if (obj[`${i}#_KM5`] == 1) {
      swapModule.value += 1
    }
  }
}

/**
 * @description: 充电模块数量
 * @param {*} obj
 * @return {*}
 */
const getChargeModule = (obj: any) => {
  chargeModule.value = 0
  for (let i = 1; i < 6; i++) {
    if (obj[`${i}#_KM2`] == 1 && obj[`${i}#_KM4`] == 1) {
      chargeModule.value += 2
    } else if (obj[`${i}#_KM2`] == 1) {
      chargeModule.value += 1
    }
    if (obj[`${i}#_KM3`] == 1 && obj[`${i}#_KM4`] == 1) {
      chargeModule.value += 2
    } else if (obj[`${i}#_KM3`] == 1) {
      chargeModule.value += 1
    }
    if (obj[`${i}#_KM6`] == 1 && obj[`${i}#_KM4`] == 1) {
      chargeModule.value += 2
    } else if (obj[`${i}#_KM6`] == 1) {
      chargeModule.value += 1
    }
    if (obj[`${i}#_KM7`] == 1 && obj[`${i}#_KM4`] == 1) {
      chargeModule.value += 2
    } else if (obj[`${i}#_KM7`] == 1) {
      chargeModule.value += 1
    }
  }
}

/**
 * @description: 满电电池数
 * @param {*} arr
 * @return {*}
 */
const getBatteryNum = (arr: any) => {
  batteryNum.value = arr.length
  fullBatteryA.value = arr.slice(10).filter((item: any) => item.charging_status == 2).length
  fullBatteryC.value = arr.slice(0, 10).filter((item: any) => item.charging_status == 2).length
  fullBattery.value = fullBatteryA.value + fullBatteryC.value
}

/**
 * @description: 桩是否为空 / 是否有数据 / 桩总功率 / 总功率 / 换电功率 / 充电功率
 * @param {*} obj
 * @param {*} eleObj
 * @return {*}
 */
const getEmptyCharge = (obj: any, eleObj: any) => {
  const sct1 = obj['SCT1']
  const sct2 = obj['SCT2']
  const sct3 = obj['SCT3']
  const sct4 = obj['SCT4']
  isEmpty1.value = sct1.output_voltage == 0 && sct1.output_current == 0 && sct1.output_energy == 0 && sct1.bms_request_voltage == 0 && sct1.bms_request_current == 0 && sct1.bms_current_soc == 0
  isEmpty2.value = sct2.output_voltage == 0 && sct2.output_current == 0 && sct2.output_energy == 0 && sct2.bms_request_voltage == 0 && sct2.bms_request_current == 0 && sct2.bms_current_soc == 0
  isEmpty3.value = sct3.output_voltage == 0 && sct3.output_current == 0 && sct3.output_energy == 0 && sct3.bms_request_voltage == 0 && sct3.bms_request_current == 0 && sct3.bms_current_soc == 0
  isEmpty4.value = sct4.output_voltage == 0 && sct4.output_current == 0 && sct4.output_energy == 0 && sct4.bms_request_voltage == 0 && sct4.bms_request_current == 0 && sct4.bms_current_soc == 0
  hasChargeData1.value = JSON.stringify(sct1) != '{}' && sct1.bms_current_soc > -9999
  hasChargeData2.value = JSON.stringify(sct2) != '{}' && sct2.bms_current_soc > -9999
  hasChargeData3.value = JSON.stringify(sct3) != '{}' && sct3.bms_current_soc > -9999
  hasChargeData4.value = JSON.stringify(sct4) != '{}' && sct4.bms_current_soc > -9999
  const output_energy_1 = JSON.stringify(sct1) == '{}' || sct1.bms_current_soc <= -9999 ? 0 : sct1.output_energy
  const output_energy_2 = JSON.stringify(sct2) == '{}' || sct2.bms_current_soc <= -9999 ? 0 : sct2.output_energy
  const output_energy_3 = JSON.stringify(sct3) == '{}' || sct3.bms_current_soc <= -9999 ? 0 : sct3.output_energy
  const output_energy_4 = JSON.stringify(sct4) == '{}' || sct4.bms_current_soc <= -9999 ? 0 : sct4.output_energy
  pilePower.value = output_energy_1 + output_energy_2 + output_energy_3 + output_energy_4
  chargePower.value = Number((pilePower.value / 0.95).toFixed(2))
  swapPower.value = JSON.stringify(eleObj) == '{}' ? Number(((0 - pilePower.value) / 0.95).toFixed(2)) : Number((eleObj.line1_total_power + eleObj.line2_total_power - eleObj.powerswap_total_power - pilePower.value / 0.95).toFixed(2))
  swapPower.value = swapPower.value < 0 ? 0 : swapPower.value
  totalPower.value = JSON.stringify(eleObj) == '{}' ? 0 : Number((eleObj.total_power).toFixed(2))
}

/**
 * @description: 桩是否充电
 * @param {*} obj
 * @return {*}
 */
const getIsCharging = (obj: any) => {
  isCharging1.value = obj['1#_KM2'] == 1 || obj['2#_KM2'] == 1 || obj['3#_KM2'] == 1 || obj['4#_KM2'] == 1 || obj['5#_KM2'] == 1
  isCharging2.value = obj['1#_KM3'] == 1 || obj['2#_KM3'] == 1 || obj['3#_KM3'] == 1 || obj['4#_KM3'] == 1 || obj['5#_KM3'] == 1
  isCharging3.value = obj['1#_KM6'] == 1 || obj['2#_KM6'] == 1 || obj['3#_KM6'] == 1 || obj['4#_KM6'] == 1 || obj['5#_KM6'] == 1
  isCharging4.value = obj['1#_KM7'] == 1 || obj['2#_KM7'] == 1 || obj['3#_KM7'] == 1 || obj['4#_KM7'] == 1 || obj['5#_KM7'] == 1
}

const handleSearch = async (formEl: FormInstance | undefined) => {
  form.value.ts = new Date(datePicker.value).getTime()
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      router.push({
        path: route.path,
        query: {
          ...removeNullProp(form.value)
        }
      })
      const selectedLabel = commonDeviceSelectRef.value.deviceOptions.find((item: any) => item.device_id == form.value.device_id)
      deviceLabel.value = selectedLabel.device_id + ' - ' + selectedLabel.description
      orderList.value = {
        swap: '-',
        charge: '-',
        predict: '-',
        stagger: '-',
        total: '-'
      }
      tableList.value = tableMockList
      loading.value = true
      showMore.value = false
      const switchRes = await apiGetSwitchList(form.value, project.value.project)
      const batteryRes = await apiGetBatteryList(form.value, project.value.project)
      const moduleRes = await apiGetModuleList(form.value, project.value.project)
      const chargeRes = await apiGetChargeList(form.value, project.value.project)
      const electricityRes = await apiGetElectricityList(form.value, project.value.project)
      loading.value = false
      showMore.value = true
      const sortModuleData = sortWithLetter(moduleRes.data)
      formatModuleData(sortModuleData)
      switchList.value = switchRes.data
      batteryList.value = batteryRes.data
      chargeList.value = chargeRes.data
      getSwapModule(switchList.value)
      getChargeModule(switchList.value)
      getBatteryNum(batteryList.value)
      getEmptyCharge(chargeList.value, electricityRes.data)
      getIsCharging(switchList.value)
      nextTick(() => draw())
    }
  })
}

const initPage = () => {
  let init_params: any = route.query
  form.value.device_id = init_params.device_id ?? ''
  if (!isNaN(init_params.ts)) {
    form.value.ts = Number(init_params.ts)
    datePicker.value = new Date(Number(init_params.ts))
  }
}

const stopWatch = watch(
  () => route.path,
  (newPath: any, oldPath: any) => {
    if (!oldPath) initPage()
  },
  {immediate: true}
)

onBeforeUnmount(() => {
  stopWatch()
})
</script>

<style lang="scss" scoped>
.charge-module-container {
  font-family: 'Noto Sans';
  color: #292c33;
  .header-container {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    :deep(.el-breadcrumb__item span) {
      color: #1c1c1c;
      font-weight: bold;
    }
  }
  .search-container {
    .collapse-search-container {
      margin-bottom: 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      :deep(.el-form-item) {
        margin-left: 0;
      }
    }
  }
  .section-container {
    padding: 20px;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .with-border {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .with-color {
    color: #00bebe;
  }
  canvas {
    width: 762px;
    height: 1720px;
  }
  :deep(.el-table .cell) {
    color: #22252b;
    font-size: 12px;
    font-family: 'Noto Sans';
  }
}
</style>
