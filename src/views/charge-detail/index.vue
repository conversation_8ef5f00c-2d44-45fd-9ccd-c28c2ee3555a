<template>
  <div class="charge-detail-container">
    <div class="header-container">
      <div class="flex-box flex_a_i-center font-size-20 line-height-28">
        <BackIcon @click="handleBack" class="cursor-pointer" />
        <span class="color-8c font-weight-480 margin_l-8">{{ $t('menu.pp_charge_list') }}</span>
        <span class="margin-n-8">/</span>
        <span class="color-1f font-weight-420">{{ $t('satisfaction.pp_order_id') }} {{ route.query.order }}</span>
        <div class="tag-container" v-if="orderInfo.order_status">{{ getLabel(orderInfo.order_status, orderStatusOptions) }}</div>
      </div>
    </div>

    <div class="content-container">
      <el-skeleton :rows="5" animated v-if="loading1" />
      <div class="info-card" v-else>
        <div class="flex-box flex_j_c-space-between flex_a_i-center">
          <div class="flex-box flex_a_i-center gap_8">
            <OrderIcon />
            <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('swapPortrait.pp_order_detail') }}</span>
          </div>
          <span class="font-size-14 color-26 cursor-pointer" @click="moreInfoVisible = true" v-if="Object.keys(orderInfo).length > 0">{{ $t('satisfaction.pp_view_more') }}</span>
        </div>
        <div class="flex-box flex_d-column gap_12" v-if="Object.keys(orderInfo).length > 0">
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_order_start_time') }}</div>
              <div class="text-value">{{ formatTime(orderInfo.order_start_timestamp) }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_order_end_time') }}</div>
              <div class="text-value">{{ formatTime(orderInfo.order_end_timestamp) }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('swapPortrait.pp_order_time') }}</div>
              <div class="text-value">{{ getDuration(orderInfo.order_start_timestamp, orderInfo.order_end_timestamp) }}</div>
            </div>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_service_start_time') }}</div>
              <div class="text-value">{{ formatTime(orderInfo.service_start_timestamp) }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_service_end_time') }}</div>
              <div class="text-value">{{ formatTime(orderInfo.service_end_timestamp) }}</div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('station.pp_service_duration') }}</div>
              <div class="text-value">{{ getDuration(orderInfo.service_start_timestamp, orderInfo.service_end_timestamp) }}</div>
            </div>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_device_name') }}</div>
              <div class="text-value">
                <WelkinCopyBoard :text="orderInfo.description || $t('common.pp_unnamed_device')" :showIcon="false" />
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_device_id') }}</div>
              <div class="text-value">
                <WelkinCopyBoard :text="orderInfo.device_id" direction="rtl" />
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('swapPortrait.pp_device_type') }}</div>
              <div class="text-value">{{ $t(projectMap[orderInfo.project]) }}</div>
            </div>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('satisfaction.pp_order_id') }}</div>
              <div class="text-value">
                <WelkinCopyBoard :text="orderInfo.order_id" direction="rtl" />
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_resource_id') }}</div>
              <div class="text-value">
                <WelkinCopyBoard :text="orderInfo.resource_id" direction="rtl" />
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_vehicle_brand') }}</div>
              <div class="text-value">{{ orderInfo.vehicle_type || '-' }}</div>
            </div>
          </div>
          <div class="text-box">
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_service_id') }}</div>
              <div class="text-value">
                <WelkinCopyBoard :text="orderInfo.service_id" direction="rtl" />
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('common.pp_channel') }}</div>
              <div class="text-value">
                <span>{{ orderInfo.channel || '-' }}</span>
                <span v-if="orderInfo.client">（{{ orderInfo.client }}）</span>
              </div>
            </div>
            <div class="text-item">
              <div class="text-label">{{ $t('chargeList.pp_stop_reason') }}</div>
              <div class="text-value">{{ getLabel(orderInfo.service_stop_reason, stopOptions) }}</div>
            </div>
          </div>
        </div>
        <el-empty :description="$t('common.pp_empty')" class="width-full height-full" v-else>
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>

      <el-skeleton :rows="5" animated v-if="loading2" />
      <div class="info-card" v-else>
        <div class="flex-box flex_a_i-center gap_8">
          <EventIcon />
          <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('chargeList.pp_event_info') }}</span>
        </div>
        <div class="card-container" v-if="eventInfo.length > 0">
          <div v-for="(item, index) in eventInfo" class="flex-box">
            <div class="flex-box flex_d-column flex_a_i-center gap_8 width-132">
              <div @click="handleViewAlarm(item)" :class="['name-container', { 'alarm-container': item.alarm_level !== 0 }]" :style="{ color: eventStatusMap[item.alarm_level].color, background: eventStatusMap[item.alarm_level].background }">
                <WelkinCopyBoard :text="item.event_name" :showIcon="false" class="width-full flex_j_c-center" />
              </div>
              <div class="font-size-13 color-59">{{ formatTimestamp(item.event_timestamp) }}</div>
            </div>
            <div class="margin_l-12 width-80" v-if="index < eventInfo.length - 1">
              <div class="duration-time">{{ getDuration(item.event_timestamp, eventInfo[index + 1].event_timestamp, true) }}</div>
              <ArrowIcon />
            </div>
          </div>
        </div>
        <el-empty :description="$t('common.pp_empty')" class="width-full height-full" v-else>
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>

      <el-skeleton :rows="5" animated v-if="loading3" />
      <div class="info-card" v-else>
        <div class="flex-box flex_a_i-center gap_8">
          <RealtimeIcon />
          <span class="color-26 font-size-16 line-height-24 font-weight-700">{{ $t('chargeList.pp_realtime_data') }}</span>
        </div>
        <div id="realtimeDataChart" class="width-full height-400" v-show="realtimeInfo.length > 0"></div>
        <el-empty :description="$t('common.pp_empty')" class="width-full height-full" v-if="realtimeInfo.length === 0">
          <template #image>
            <WhiteEmptyDataIcon />
          </template>
        </el-empty>
      </div>

      <MoreInfoDialog v-model:moreInfoVisible="moreInfoVisible" :orderInfo="orderInfo" />

      <AlarmDetailDialog v-model:alarmDetailVisible="alarmDetailVisible" :alarmList="alarmList" />

      <!-- <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane :label="$t('menu.pp_charge_list')" name="portrait">
          <ChargePortrait v-if="activeTab == 'portrait'" :events="orderInfo.events || []" />
        </el-tab-pane>
        <el-tab-pane :label="$t('chargeList.pp_realtime_data')" name="realtime">
          <RealtimeData v-if="activeTab == 'realtime'" :realtimeInfo="realtimeInfo || []" />
        </el-tab-pane>
      </el-tabs> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { formatTime, getLabel, getOptions } from '~/utils'
import { projectMap, getDuration, eventStatusMap, formatTimestamp, multilineOptionMock, chartItem } from './components/constant'
import { apiGetChargeEvent, apiGetRealtimeData, apiGetChargeOrderInfo, apiGetStatusOptions } from '~/apis/charge-list'
import { ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import * as echarts from 'echarts'
// import ChargePortrait from './components/charge-portrait.vue'
// import RealtimeData from './components/realtime-data.vue'
import MoreInfoDialog from './components/more-info-dialog.vue'
import AlarmDetailDialog from './components/alarm-detail-dialog.vue'
import BackIcon from '~/views/single-station/icon/back-icon.vue'
import OrderIcon from './components/icon/order-icon.vue'
import EventIcon from './components/icon/event-icon.vue'
import RealtimeIcon from './components/icon/realtime-icon.vue'
import ArrowIcon from './components/icon/arrow-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'

const store = useStore()
const route = useRoute()
const router = useRouter()
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)
const moreInfoVisible = ref(false)
const alarmDetailVisible = ref(false)
const orderInfo = ref({} as any)
const eventInfo = ref([] as any)
const realtimeInfo = ref([] as any)
const orderStatusOptions = ref([] as any)
const stopOptions = ref([] as any)
const activeTab = ref('' as any)
const echartsArr = ref([] as any)
const alarmList = ref([] as any)
const isCollapse = computed(() => store.state.menus.collapse)
const chartOption1 = cloneDeep(multilineOptionMock)

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  echartsArr.value.push(myChart)
  window.addEventListener('resize', () => myChart.resize())
}
const setCharts = () => {
  echartsArr.value = []
  if (realtimeInfo.value.length > 0) echartRender('realtimeDataChart', chartOption1)
}

const formatChartData = () => {
  chartOption1.xAxis.data = realtimeInfo.value.map((item: any) => formatTime(item.timestamp))
  chartOption1.series = []
  chartItem.map((item: any) => {
    chartOption1.series.push({
      name: item.name + '（' + item.unit + '）',
      type: 'line',
      lineStyle: {
        width: 1.5
      },
      symbol: 'none',
      data: realtimeInfo.value.map((data: any) => data[item.key])
    })
  })
  nextTick(() => {
    setCharts()
  })
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 切换tab
 * @param {*} name
 * @return {*}
 */
const handleTabChange = (name: any) => {
  router.push({
    path: location.pathname,
    query: { ...route.query, tab: name }
  })
}

/**
 * @description: 下钻查看告警信息
 * @param {*} row
 * @return {*}
 */
const handleViewAlarm = (row: any) => {
  if (row.alarm_level === 0) return
  alarmList.value = row.alarm_details
  alarmDetailVisible.value = true
}

/**
 * @description: 回到上级页面
 * @return {*}
 */
const handleBack = () => {
  let query: any = sessionStorage.getItem('charge-list')
  router.push({
    path: '/fault-diagnosis/charge-list',
    query: JSON.parse(query)
  })
}

/**
 * @description: 获取订单基本信息
 * @return {*}
 */
const getOrderList = async () => {
  loading1.value = true
  try {
    const res = await apiGetChargeOrderInfo(route.query.project, route.query.order)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      orderInfo.value = res.data || {}
    }
  } catch (error) {
    orderInfo.value = {}
  } finally {
    loading1.value = false
  }
}

/**
 * @description: 获取事件信息
 * @return {*}
 */
const getEventList = async () => {
  loading2.value = true
  try {
    const res = await apiGetChargeEvent(route.query.project, route.query.order)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      eventInfo.value = res.data || []
    }
  } catch (error) {
    eventInfo.value = []
  } finally {
    loading2.value = false
  }
}

/**
 * @description: 获取实时数据
 * @return {*}
 */
const getRealtimeList = async () => {
  loading3.value = true
  try {
    const res = await apiGetRealtimeData(route.query.project, route.query.order)
    loading3.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      realtimeInfo.value = res.data || []
      if (realtimeInfo.value.length > 0) formatChartData()
    }
  } catch (error) {
    loading3.value = false
    realtimeInfo.value = []
  }
}

/**
 * @description: 获取订单状态、结束原因列表项
 * @return {*}
 */
const getStatusOptions = async () => {
  const res = await apiGetStatusOptions()
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    orderStatusOptions.value = getOptions(res.data.order_status)
    stopOptions.value = getOptions(res.data.service_stop_reason)
  }
}

onBeforeMount(() => {
  // activeTab.value = route.query.tab || 'portrait'
  getStatusOptions()
  getOrderList()
  getEventList()
  getRealtimeList()
})
</script>

<style lang="scss" scoped>
.charge-detail-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #eef6f8 -6.51%, #f4f5f8 12.89%);
  .header-container {
    padding: 24px 24px 20px;
    .tag-container {
      height: 26px;
      display: flex;
      align-items: center;
      border-radius: 2px;
      padding: 2px 8px;
      font-size: 14px;
      line-height: 22px;
      margin-left: 8px;
      color: #595959;
      background-color: #ebecee;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    .info-card {
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 24px;
      border-radius: 4px;
      border: 1px solid #dcf2f3;
      background-color: #fff;
      .text-box {
        display: grid;
        grid-template-columns: minmax(100px, 5fr) minmax(100px, 5fr) minmax(100px, 4fr);
        gap: 12px 96px;
        .text-item {
          display: flex;
          align-items: center;
          .text-label {
            width: 110px;
            font-size: 14px;
            line-height: 22px;
            color: #8c8c8c;
            flex-shrink: 0;
          }
          .text-value {
            width: calc(100% - 120px);
            font-size: 14px;
            line-height: 22px;
            color: #262626;
          }
        }
      }
      .card-container {
        display: flex;
        flex-wrap: wrap;
        gap: 24px 12px;
        .name-container {
          width: 100%;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 2px;
          padding: 0px 8px;
          font-size: 14px;
          line-height: 22px;
        }
        :deep(.alarm-container) {
          cursor: pointer;
          span {
            cursor: pointer !important;
          }
        }
        .duration-time {
          width: 100%;
          text-align: center;
          font-size: 13px;
          color: #595959;
          margin-bottom: -6px;
          word-wrap: break-word;
        }
      }
    }
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
    :deep(.el-empty) {
      padding: 0;
      .el-empty__image {
        width: 100px;
      }
      .el-empty__description {
        margin-left: 20px;
      }
    }
  }
}
</style>
