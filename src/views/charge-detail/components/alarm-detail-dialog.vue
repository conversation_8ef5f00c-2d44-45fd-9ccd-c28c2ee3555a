<template>
  <div class="alarm-detail-dialog">
    <el-dialog v-model="alarmDetailVisible" :title="$t('common.pp_alarm_info')" width="538px" @close="handleClose" align-center>
      <el-table :data="alarmList" max-height="245" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="alarm_create_ts" :label="$t('alarmList.pp_alarm_time')" width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.alarm_create_ts) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarm_id" :label="$t('alarmList.pp_alarm_id')" width="100" show-overflow-tooltip />
        <el-table-column prop="alarm_description" :label="$t('alarmList.pp_alarm_description')" width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.alarm_description || '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { formatTime } from '~/utils'
const props = defineProps({
  alarmDetailVisible: Boolean,
  alarmList: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['update:alarmDetailVisible'])

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleClose = () => {
  emits('update:alarmDetailVisible', false)
}
</script>

<style lang="scss" scoped>
.alarm-detail-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
  }
}
</style>
