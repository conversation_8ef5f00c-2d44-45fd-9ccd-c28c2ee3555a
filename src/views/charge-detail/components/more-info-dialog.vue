<template>
  <div class="more-info-dialog">
    <el-dialog v-model="moreInfoVisible" :title="$t('swapPortrait.pp_order_detail')" width="860px" @close="handleClose" align-center>
      <div class="flex-box flex_d-column gap_16">
        <div class="text-box">
          <div class="text-item">
            <div class="text-label">车辆ID</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.vehicle_id" direction="rtl" />
            </div>
          </div>
          <div class="text-item">
            <div class="text-label">车辆VIN</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.vehicle_vin" direction="rtl" />
            </div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">用户ID</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.user_id" direction="rtl" />
            </div>
          </div>
          <div class="text-item">
            <div class="text-label">车主ID</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.owner_id" direction="rtl" />
            </div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">车机版本</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.car_package_part_number" direction="rtl" />
            </div>
          </div>
          <div class="text-item">
            <div class="text-label">车机全量版本</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.car_package_global_version" direction="rtl" />
            </div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">服务费</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.service_price)">-</div>
            <div class="text-value" v-else>{{ orderInfo.service_price / 100 }}元</div>
          </div>
          <div class="text-item">
            <div class="text-label">电费</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.electricity_price)">-</div>
            <div class="text-value" v-else>{{ orderInfo.electricity_price / 100 }}元</div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">总费用</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.should_price)">-</div>
            <div class="text-value" v-else>{{ orderInfo.should_price / 100 }}元</div>
          </div>
          <div class="text-item">
            <div class="text-label">shaman订单ID</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.shaman_order_id" direction="rtl" />
            </div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">启动充电命令ID</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.start_command_id" direction="rtl" />
            </div>
          </div>
          <div class="text-item">
            <div class="text-label">结束充电命令ID</div>
            <div class="text-value">
              <WelkinCopyBoard :text="orderInfo.stop_command_id" direction="rtl" />
            </div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">启动充电命令状态</div>
            <div class="text-value">{{ orderInfo.start_command_status || '-' }}</div>
          </div>
          <div class="text-item">
            <div class="text-label">结束充电命令状态</div>
            <div class="text-value">{{ orderInfo.stop_command_status || '-' }}</div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">启动充电命令时间</div>
            <div class="text-value">{{ formatTime(orderInfo.start_command_time) }}</div>
          </div>
          <div class="text-item">
            <div class="text-label">结束充电命令时间</div>
            <div class="text-value">{{ formatTime(orderInfo.stop_command_time) }}</div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">充电结束时显示本次充电电能</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.charged_energy_total)">-</div>
            <div class="text-value" v-else>{{ orderInfo.charged_energy_total }}kWh</div>
          </div>
          <div class="text-item">
            <div class="text-label">计算后实际电量（推给Biz）</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.effective_charged_energy)">-</div>
            <div class="text-value" v-else>{{ orderInfo.effective_charged_energy }}kWh</div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">设备端计算电量</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.calibrated_realtime_charged_energy)">-</div>
            <div class="text-value" v-else>{{ orderInfo.calibrated_realtime_charged_energy }}kWh</div>
          </div>
          <div class="text-item">
            <div class="text-label">服务开始时电表读数</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.energy_total_start)">-</div>
            <div class="text-value" v-else>{{ orderInfo.energy_total_start }}kWh</div>
          </div>
        </div>

        <div class="text-box">
          <div class="text-item">
            <div class="text-label">服务结束时电表读数</div>
            <div class="text-value" v-if="isEmptyData(orderInfo.energy_total)">-</div>
            <div class="text-value" v-else>{{ orderInfo.energy_total }}kWh</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { isEmptyData, formatTime } from '~/utils'
const props = defineProps({
  moreInfoVisible: Boolean,
  orderInfo: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['update:moreInfoVisible'])

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleClose = () => {
  emits('update:moreInfoVisible', false)
}
</script>

<style lang="scss" scoped>
.more-info-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
      .text-box {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px 48px;
        .text-item {
          display: flex;
          align-items: center;
          .text-label {
            width: 240px;
            font-size: 14px;
            line-height: 22px;
            color: #8c8c8c;
            flex-shrink: 0;
          }
          .text-value {
            width: 142px;
            font-size: 14px;
            line-height: 22px;
            color: #262626;
          }
        }
      }
    }
  }
}
</style>
