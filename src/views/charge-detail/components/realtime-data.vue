<template>
  <div class="realtime-data-container">
    <div id="realtimeDataChart" class="width-full height-400" v-show="realtimeInfo.length > 0"></div>
    <el-empty v-show="realtimeInfo.length === 0" :description="$t('common.pp_empty')" class="width-full height-full">
      <template #image>
        <WhiteEmptyDataIcon />
      </template>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onBeforeMount, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { formatTime } from '~/utils'
import { chartItem, multilineOptionMock } from './constant'
import { cloneDeep } from 'lodash-es'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'
import * as echarts from 'echarts'

const props = defineProps({
  realtimeInfo: {
    type: Array as () => any,
    default: []
  }
})

const { t } = useI18n()
const store = useStore()
const echartsArr = ref([] as any)
const isCollapse = computed(() => store.state.menus.collapse)
const chartOption1 = cloneDeep(multilineOptionMock)

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  echartsArr.value.push(myChart)
  window.addEventListener('resize', () => myChart.resize())
}
const setCharts = () => {
  echartsArr.value = []
  if (props.realtimeInfo.length > 0) echartRender('realtimeDataChart', chartOption1)
}

const formatChartData = () => {
  chartOption1.xAxis.data = props.realtimeInfo.map((item: any) => formatTime(item.timestamp))
  chartOption1.series = []
  chartItem.map((item: any) => {
    chartOption1.series.push({
      name: item.name + '（' + item.unit + '）',
      type: 'line',
      lineStyle: {
        width: 1.5
      },
      symbol: 'none',
      data: props.realtimeInfo.map((data: any) => data[item.key])
    })
  })
  nextTick(() => {
    setCharts()
  })
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

onBeforeMount(() => {
  formatChartData()
})
</script>

<style lang="scss" scoped>
.realtime-data-container {
  width: 100%;
  margin-top: 16px;
  padding: 28px 24px;
  border-radius: 4px;
  border: 1px solid #dcf2f3;
  background-color: #fff;
}
</style>
