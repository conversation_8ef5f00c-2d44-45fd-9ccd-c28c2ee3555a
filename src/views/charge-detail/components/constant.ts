export const projectMap = {
  PowerCharger3_1: 'common.pp_psc3'
} as any

export const eventStatusMap = {
  0: {
    color: '#2F9C74',
    background: '#E8FCEA'
  },
  1: {
    color: '#FF772E',
    background: '#FFF4E8'
  },
  2: {
    color: '#E83030',
    background: '#FFF2F0'
  }
} as any

export const getDuration = (start_time: number, end_time: number, needMilliseconds = false) => {
  if (!start_time || !end_time || start_time == 0 || end_time == 0) return '-'
  // 计算时间差（毫秒）
  const duration = end_time - start_time

  // 计算小时、分钟、秒、毫秒
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((duration % (1000 * 60)) / 1000)
  const milliseconds = duration % 1000

  // 构建结果字符串
  let result = ''

  // 添加小时（如果有）
  if (hours > 0) {
    result += `${hours}h`
  }

  // 添加分钟（如果有）
  if (minutes > 0) {
    result += `${minutes}m`
  }

  // 添加秒（如果有）
  if (seconds > 0) {
    result += `${seconds}s`
  }

  // 如果需要显示毫秒且有毫秒值
  if (needMilliseconds && milliseconds > 0 && minutes === 0) {
    result += `${milliseconds}ms`
  }

  // 如果结果为空（表示时间差小于1秒）
  if (result === '') {
    if (needMilliseconds) {
      result = `${milliseconds}ms`
    } else {
      result = '0s'
    }
  }

  return result
}

export const formatTimestamp = (timestamp: number) => {
  const date = new Date(timestamp)
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0')
  return `${hours}:${minutes}:${seconds}.${milliseconds}`
}

export const chartItem = [
  {
    key: 'bms_request_charge_current',
    name: 'bms请求电流',
    unit: 'A',
    initSelected: true
  },
  {
    key: 'output_current',
    name: '桩端输出电流',
    unit: 'A',
    initSelected: true
  },
  {
    key: 'bms_request_charge_voltage',
    name: 'bms请求电压',
    unit: 'V',
    initSelected: false
  },
  {
    key: 'output_voltage',
    name: '桩端输出电压',
    unit: 'V',
    initSelected: false
  },
  {
    key: 'soc',
    name: '车端soc',
    unit: '%',
    initSelected: false
  },
  {
    key: 'calibrated_realtime_charged_energy',
    name: '校准实时电量',
    unit: 'kWh',
    initSelected: false
  },
  {
    key: 'output_power',
    name: '桩端输出功率',
    unit: 'kW',
    initSelected: false
  },
  {
    key: 'effective_charged_energy',
    name: '有效充电电量',
    unit: 'kWh',
    initSelected: false
  },
  {
    key: 'energy_total',
    name: '电表总电量',
    unit: 'kWh',
    initSelected: false
  },
  {
    key: 'energy_total_start',
    name: '电表起始电量',
    unit: 'kWh',
    initSelected: false
  }
]

const getSelectedLegend = (chartItem: any) => {
  const selected: any = {}
  chartItem.map((item: any) => {
    const key = item.name + '（' + item.unit + '）'
    selected[key] = item.initSelected
  })
  return selected
}

export const multilineOptionMock = {
  animation: false,
  color: ['#6697FF', '#5ECFFF', '#67C23A', '#FFC031', '#00BEBE', '#9FDB1D', '#FADC19', '#F7BA1E', '#FF7D00', '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83'],
  tooltip: {
    trigger: 'axis',
    confine: true,
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    padding: 0,
    left: 0,
    top: 0,
    textStyle: {
      color: '#595959'
    },
    icon: 'circle',
    itemWidth: 14,
    itemHeight: 8,
    itemGap: 16,
    selected: getSelectedLegend(chartItem)
  },
  grid: {
    top: '70',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    data: []
  },
  yAxis: [
    {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    }
  ],
  series: []
} as any
