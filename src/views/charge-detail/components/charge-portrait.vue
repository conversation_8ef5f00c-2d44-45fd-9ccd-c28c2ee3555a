<template>
  <div class="charge-portrait-container" v-if="events.length > 0">
    <div class="card-container">
      <div v-for="(item, index) in events" class="flex-box">
        <div class="flex-box flex_d-column flex_a_i-center gap_8 width-132">
          <div class="name-container" :style="{ color: eventStatusMap[item.alarm_level].color, background: eventStatusMap[item.alarm_level].background }">
            <WelkinCopyBoard :text="item.event_name" :showIcon="false" class="width-full flex_j_c-center" />
          </div>
          <div class="font-size-13 color-59">{{ formatTimestamp(item.event_timestamp) }}</div>
        </div>
        <div class="margin_l-12 width-80" v-if="index < events.length - 1">
          <div class="duration-time">{{ getDuration(item.event_timestamp, events[index+1].event_timestamp, true) }}</div>
          <ArrowIcon />
        </div>
      </div>
    </div>
  </div>
  <div class="charge-portrait-container" v-else>
    <el-empty :description="$t('common.pp_empty')" class="width-full height-full">
      <template #image>
        <WhiteEmptyDataIcon />
      </template>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { eventStatusMap, formatTimestamp, getDuration } from './constant'
import ArrowIcon from './icon/arrow-icon.vue'
import WhiteEmptyDataIcon from '~/assets/svg/white-empty-data.vue'

const props = defineProps({
  events: {
    type: Array as () => any,
    default: []
  }
})
</script>

<style lang="scss" scoped>
.charge-portrait-container {
  width: 100%;
  margin-top: 16px;
  padding: 28px 24px;
  border-radius: 4px;
  border: 1px solid #dcf2f3;
  background-color: #fff;
  .card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 24px 12px;
    .name-container {
      width: 100%;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      padding: 0px 8px;
      font-size: 14px;
      line-height: 22px;
    }
    .duration-time {
      width: 100%;
      text-align: center;
      font-size: 13px;
      color: #595959;
      margin-bottom: -6px;
      word-wrap: break-word;
    }
  }
}
</style>
