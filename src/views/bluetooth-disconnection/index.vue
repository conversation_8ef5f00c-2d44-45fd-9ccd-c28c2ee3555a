<template>
  <div class="bluetooth-disconnection-container">
    <div class="header-container">
      <div class="header-title">{{ $t('bluetooth.pp_bluetooth_disconnection') }}</div>
    </div>

    <div class="content-container">
      <el-form :model="form" inline>
        <el-form-item :label="$t('stuckAnalysis.pp_time')" class="width-full">
          <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
            <template #range-separator>
              <TimeRangeIcon style="margin: 0 5px" />
            </template>
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('stuckAnalysis.pp_device_search')" class="width-full">
          <el-select v-model="form.device_ids" filterable remote clearable :reserve-keyword="false" multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_enter')" :remote-method="remoteDeviceMethod" :loading="remoteDeviceLoading" class="width-full">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('bluetooth.pp_is_service')" class="width-full">
          <el-select v-model="form.is_service" filterable clearable :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option :value="true" :label="$t('common.pp_yes')" />
            <el-option :value="false" :label="$t('common.pp_no')" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <div class="flex-box flex_j_c-space-between width-full">
            <el-button class="welkin-primary-button" :loading="chartLoading || tableLoading || rightLoading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="batchImportVisible = true" class="welkin-secondary-button width-108 height-32" style="padding: 5px 16px">
              <UploadIcon />
              <span class="margin_l-4">{{ $t('common.pp_import') }}</span>
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <div class="flex-box width-full">
        <el-card style="flex: 1">
          <div v-if="chartLoading" class="flex-box flex_j_c-center flex_a_i-center height-318">
            <div class="loader-45"></div>
          </div>
          <div class="flex-box flex_j_c-space-between" v-show="!chartLoading">
            <div class="flex-box flex_d-column">
              <span class="margin_b-6 section-title">{{ $t('bluetooth.pp_average_daily') }}</span>
              <span class="flex-box flex_a_i-center">
                <BookIcon />
                <span class="margin_l-8 font-size-16">{{ chartList.average_bluetooth_disconnect_count + ' ' + $t('bluetooth.pp_times') }}</span>
              </span>
            </div>
            <div class="flex-box flex_d-column">
              <span class="margin_b-6 section-title">{{ $t('bluetooth.pp_related_alarm') }}</span>
              <span class="flex-box flex_a_i-center">
                <FlagIcon />
                <span class="margin_l-8 font-size-16">{{ chartList.total_alarm_count + ' ' + $t('bluetooth.pp_times') }}</span>
              </span>
            </div>
            <div class="flex-box flex_d-column">
              <span class="margin_b-6 section-title">{{ $t('stuckAnalysis.pp_selected_days') }}</span>
              <span class="flex-box flex_a_i-center">
                <CalendarIcon />
                <span class="margin_l-8 font-size-16">{{ chartList.stat_day_nums + ' ' + $t('stuckAnalysis.pp_days') }}</span>
              </span>
            </div>
          </div>
          <div id="bluetoothChart" class="width-full height-240 margin_b-24" v-show="!chartLoading && hasChartData"></div>
          <el-empty class="width-full height-240 margin_t-10 margin_b-24" :description="$t('common.pp_empty')" v-if="!chartLoading && !hasChartData"></el-empty>

          <el-table :data="tableList" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" :cell-style="{ color: '#262626' }" v-loading="tableLoading">
            <el-table-column prop="no" label="No." width="50" show-overflow-tooltip />
            <el-table-column prop="alarm_id_description" :label="$t('stuckAnalysis.pp_alarm_name')" min-width="220" show-overflow-tooltip>
              <template #default="{ row }">
                <span>{{ row.alarm_id_description ? row.alarm_id_description : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="alarm_id" :label="$t('stuckAnalysis.pp_alarm_id')" min-width="90" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="operation-button" @click="handleViewAlarm(row.alarm_id)">{{ row.alarm_id }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="total_service_times" :label="$t('bluetooth.pp_alarm_in_service')" min-width="100" show-overflow-tooltip />
            <el-table-column prop="total_times" :label="$t('bluetooth.pp_total_times')" min-width="100" show-overflow-tooltip />
          </el-table>
          <div class="common-pagination">
            <Page :page="pages" @change="handlePageChange" />
          </div>
        </el-card>

        <div v-if="rightLoading" class="flex-box flex_j_c-center flex_a_i-center height-full width-360 margin_l-16">
          <div class="loader-45"></div>
        </div>
        <div v-show="!rightLoading" class="width-360 margin_l-16" style="padding: 16px; border-radius: 4px; background: linear-gradient(180deg, #c6efef, #e5f9f9)">
          <div class="font-size-16 font-weight-500 margin_b-8 color-26">{{ $t('bluetooth.pp_device_rank') }}</div>
          <div class="flex-box flex_d-column gap_8" v-if="rightList.length > 0">
            <el-card class="list-card" v-for="item in rightList">
              <div class="flex-box flex_a_i-center flex_j_c-space-between" style="color: #434343">
                <div class="flex-box flex_a_i-center">
                  <span class="font-weight-480">{{ item.no }}</span>
                  <VerticalLine class="margin-n-8" />
                  <div class="width-220">
                    <div class="margin_b-6 font-weight-450 ellipse">{{ item.description || $t('common.pp_unnamed_device') }}</div>
                    <div class="font-size-13" style="color: #8c8c8c">{{ item.device_id }}</div>
                  </div>
                </div>
                <span class="font-weight-480">{{ item.total_alarm_count + $t('bluetooth.pp_times') }}</span>
              </div>
            </el-card>
          </div>
          <el-empty class="width-full height-240 margin_t-100" :description="$t('common.pp_empty')" v-else />
        </div>
      </div>

      <!-- 单一告警分布弹窗 -->
      <AlarmDialog v-model:alarmVisible="alarmVisible" :alarmId="alarmId" :start="form.start_time" :end="form.end_time" :isService="form.is_service" project="PUS4" v-if="alarmVisible" />

      <!-- 设备批量导入弹窗 -->
      <BatchUploadDevice v-model:batchImportVisible="batchImportVisible" :deviceList="allDevices" @handleConfirmBatchImportDevice="handleConfirmBatchImportDevice" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, watch, nextTick, shallowRef, h } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { page } from '~/constvars/page'
import { ElMessage } from 'element-plus'
import { removeNullKeys, getFinalEndTime } from '~/utils'
import { apiGetDevices } from '~/apis/home'
import { apiGetDeviceNameMap } from '~/apis/device-management'
import { lineOption, initStartTime, initEndTime } from './components/constant'
import { apiGetChartList, apiGetAlarmList, apiGetDeviceRankList } from '~/apis/bluetooth'
import { cloneDeep, debounce } from 'lodash-es'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import BookIcon from '~/assets/svg/book.vue'
import FlagIcon from '~/assets/svg/flag.vue'
import CalendarIcon from '~/assets/svg/calendar.vue'
import VerticalLine from '~/assets/svg/vertical-line.vue'
import UploadIcon from '~/assets/svg/upload.vue'
import AlarmDialog from './components/alarm-dialog.vue'
import * as echarts from 'echarts'
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const chartLoading = ref(false)
const hasChartData = ref(false)
const tableLoading = ref(false)
const rightLoading = ref(false)
const remoteDeviceLoading = ref(false)
const alarmVisible = ref(false)
const batchImportVisible = ref(false)
const alarmId = ref('' as any)
const echartsArr = ref([] as any)
const chartList = ref({} as any)
const tableList = ref([] as any)
const rightList = ref([] as any)
const deviceOptions = ref([] as any)
const allDevices = ref([] as any)
const datePicker = ref([initStartTime, initEndTime] as any)
const form = ref({
  start_time: initStartTime,
  end_time: initEndTime,
  is_service: '' as string | boolean,
  device_ids: [] as any
})
const pages = ref(cloneDeep(page))
const lineOption1 = cloneDeep(lineOption)
const isCollapse = computed(() => store.state.menus.collapse)
const getShortcuts = () => {
  return [
    {
      text: t('common.pp_last_week'),
      value: [initStartTime, initEndTime]
    },
    {
      text: t('common.pp_last_month'),
      value: [initStartTime - 3600 * 1000 * 24 * 24, initEndTime]
    }
  ]
}
const getDisabledDate = (time: Record<string, any>) => time.getTime() > initEndTime

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteDeviceMethod = (query: any) => {
  if (remoteDeviceLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteDeviceLoading.value = true
    const params = { project: 'PUS4', name: val, device_ids: deviceIds, limit: 30 }
    const res = await apiGetDevices(params)
    remoteDeviceLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 查看单一告警
 * @param {*} id
 * @return {*}
 */
const handleViewAlarm = (id: any) => {
  alarmId.value = cloneDeep(id)
  alarmVisible.value = true
}

let myChart: any
const echartRender = (chartId: string, option: any) => {
  myChart && myChart.dispose()
  let chartDom = document.getElementById(chartId)!
  myChart = echarts.init(chartDom)
  echartsArr.value = [myChart]
  option && myChart.setOption(option)
  window.addEventListener('resize', () => myChart.resize())
}
const setCharts = () => {
  echartRender('bluetoothChart', lineOption1)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 获取折线图数据
 * @return {*}
 */
const getChartList = async () => {
  const params = {
    project: 'PUS4',
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    is_service: form.value.is_service,
    device_ids: form.value.device_ids.join(',')
  }
  chartLoading.value = true
  try {
    const res = await apiGetChartList(params)
    chartLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      chartList.value = res.data
      if (res.data.daily_details && res.data.daily_details.length > 0) {
        hasChartData.value = true
        lineOption1.xAxis.data = chartList.value.daily_details.map((item: any) => item.day)
        lineOption1.series[0].data = chartList.value.daily_details.map((item: any) => item.average_bluetooth_disconnect_count)
        nextTick(() => setCharts())
      } else {
        hasChartData.value = false
      }
    }
  } catch (error) {
    chartLoading.value = false
  }
}

/**
 * @description: 获取表格数据
 * @param {*} updateRoute
 * @return {*}
 */
const getTableList = async (updateRoute = true) => {
  const params = {
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    page: pages.value.current,
    size: pages.value.size,
    is_service: form.value.is_service,
    device_ids: form.value.device_ids.join(',')
  }
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys(params) }
    })
  }
  tableLoading.value = true
  try {
    const res = await apiGetAlarmList(params)
    tableLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      tableList.value = res.data
      pages.value.total = res.total
    }
  } catch (error) {
    tableLoading.value = false
  }
}

/**
 * @description: 获取卡片数据
 * @return {*}
 */
const getRightList = async () => {
  const params = {
    project: 'PUS4',
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    is_service: form.value.is_service,
    page: 1,
    size: 10,
    descending: true,
    device_ids: form.value.device_ids.join(',')
  }
  rightLoading.value = true
  try {
    const res = await apiGetDeviceRankList(params)
    rightLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      rightList.value = res.data || []
    }
  } catch (error) {
    rightLoading.value = false
  }
}

const getList = (updateRoute = true) => {
  const queryObj = {
    start_time: form.value.start_time,
    end_time: getFinalEndTime(form.value.end_time),
    page: pages.value.current,
    size: pages.value.size,
    is_service: form.value.is_service,
    device_ids: form.value.device_ids.join(',')
  }
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys(queryObj) }
    })
  }
  getChartList()
  getTableList(false)
  getRightList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getTableList()
}

/**
 * @description: 点击搜索
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  getList()
}

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 确认批量导入设备
 * @param {*} list
 * @return {*}
 */
const handleConfirmBatchImportDevice = (list: any) => {
  if (!list) return
  form.value.device_ids = [...new Set([...form.value.device_ids, ...list])]
  batchImportVisible.value = false
  searchDeviceList('NIO', form.value.device_ids.join(','))
}

/**
 * @description: 获取全量设备列表，用于批量导入设备时校验设备的有效性
 * @return {*}
 */
const getDeviceNameMap = async () => {
  const res = await apiGetDeviceNameMap('PUS4')
  allDevices.value = res.data
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  }
  form.value.is_service = !!initParams.is_service ? JSON.parse(initParams.is_service) : ''
  form.value.device_ids = !!initParams.device_ids ? initParams.device_ids.split(',') : []
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  getList(false)
  searchDeviceList('NIO', route.query.device_ids)
  getDeviceNameMap()
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.bluetooth-disconnection-container {
  font-family: 'Blue Sky Standard';
  font-size: 14px;
  display: flex;
  flex-direction: column;
  .header-container {
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    flex: 1;
    padding: 0px 24px 24px;
    background: #f8f8f8;
    display: flex;
    flex-direction: column;
    :deep(.el-form) {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px 24px;
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-form-item__content {
          align-items: normal;
        }
        .el-select .el-select__tags .el-tag--info {
          color: #262626;
        }
      }
    }
    :deep(.el-card.is-always-shadow) {
      border-color: rgba(220, 242, 243, 1);
    }
    :deep(.list-card:hover) {
      border-color: #00bebe;
    }
    :deep(.list-card .el-card__body) {
      padding: 15px;
    }
    :deep(.el-empty) {
      .el-empty__image {
        width: 120px;
      }
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-date-editor .el-range-input) {
      color: #262626;
    }
    :deep(.operation-button) {
      color: #01a0ac;
      cursor: pointer;
      &:hover {
        color: #00bebe;
      }
    }
    .section-title {
      color: rgba(89, 89, 89, 1);
    }
  }
}
</style>
