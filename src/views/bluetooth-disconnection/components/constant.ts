export const lineOption = {
  animation: false,
  color: ['#00bebe'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    left: '0%',
    right: '2%',
    bottom: 0,
    top: 32,
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(228, 228, 228, 1)'
      }
    },
    data: [] as any
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dotted',
        color: 'rgba(227, 227, 227, 1)'
      }
    }
  },
  series: [
    {
      data: [],
      type: 'line',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, // 渐变起始位置的横坐标，0 为最左侧，0.5 为中间，1 为最右侧
          y: 0, // 渐变起始位置的纵坐标，0 为最顶部，0.5 为中间，1 为最底部
          x2: 0, // 渐变结束位置的横坐标
          y2: 1, // 渐变结束位置的纵坐标，1 为最底部
          colorStops: [
            {
              offset: 0,
              color: 'rgba(0, 190, 190, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(0, 190, 190, 0)'
            }
          ],
          global: false
        }
      },
      label: {
        show: true,
        position: 'top',
        color: 'rgba(53, 121, 139, 1)'
      },
      labelLayout: {
        hideOverlap: true
      }
    }
  ]
}

const now = new Date()
const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
today.setHours(0, 0, 0, 0)
export const initStartTime = today.getTime() - 3600 * 1000 * 24 * 7
export const initEndTime = today.getTime() - 1

export const barOption = {
  animation: false,
  color: ['#00bebe'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    left: '0%',
    right: '2%',
    bottom: '4%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: { show: false },
    axisLabel: {
      show: true,
      interval: 0,
      color: 'rgba(89, 89, 89, 1)'
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(228, 228, 228, 1)'
      }
    },
    data: [] as any
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dotted',
        color: 'rgba(227, 227, 227, 1)'
      }
    }
  },
  series: [
    {
      data: [] as any,
      type: 'bar',
      barMaxWidth: 30,
      label: {
        show: true,
        position: 'top',
        color: '#35798B'
      },
      labelLayout: {
        hideOverlap: true
      }
    }
  ]
}
