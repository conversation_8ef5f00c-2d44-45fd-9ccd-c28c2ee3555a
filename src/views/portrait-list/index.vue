<template>
  <div class="portrait-list-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_portrait_list') }}</div>
    </div>

    <div class="content-container">
      <el-form :model="form" :label-width="locale == 'zh' ? '64px' : 'auto'" label-position="left">
        <el-form-item :label="$t('common.pp_time_frame')">
          <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :shortcuts="getTodayShortcuts()" :clearable="false" :disabledDate="getDisabledTodayDate" :prefix-icon="customPrefix">
            <template #range-separator>
              <TimeRangeIcon style="margin: 0 5px" />
            </template>
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('common.pp_device_type')">
          <el-select v-model="form.project" filterable :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.pp_order_id')">
          <el-input v-model.trim="form.order_id" :placeholder="$t('common.pp_please_input')" clearable>
            <template #prefix><SearchIcon /></template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('common.pp_device_name')">
          <el-select v-model="form.device_id" clearable filterable remote :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
            <template #prefix><SearchIcon /></template>
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.pp_order_status')">
          <el-select v-model="form.service_results" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option v-for="item in orderStatusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.pp_vehicle_id')" v-if="!isFold">
          <el-input v-model.trim="form.vehicle_id" :placeholder="$t('common.pp_please_input')" clearable>
            <template #prefix><SearchIcon /></template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('common.pp_vehicle_brand')" v-if="!isFold">
          <el-cascader v-model="brand" :options="brandOptions" :props="cascaderProps" collapse-tags collapse-tags-tooltip separator="-" clearable @change="handleBrandChange" class="brand-cascader" :placeholder="t('common.pp_please_select')" />
        </el-form-item>
        <el-form-item :label="$t('common.pp_vehicle_platform')" v-if="!isFold">
          <el-select v-model="form.vehicle_platform" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
            <el-option v-for="item in platformOptions" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <div class="width-full height-32 flex-box flex_j_c-flex-end flex_a_i-center">
            <div @click="isFold = !isFold" class="margin_r-20 font-size-16 line-height-24">
              <div class="cursor-pointer flex-box flex_a_i-center gap_4 color-59" v-if="!isFold">
                <UpIcon />
                <span>{{ $t('common.pp_collapse') }}</span>
              </div>
              <div class="cursor-pointer flex-box flex_a_i-center gap_4 color-59" v-if="isFold">
                <DownIcon />
                <span>{{ $t('common.pp_expand') }}</span>
              </div>
            </div>
            <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
          </div>
        </el-form-item>
      </el-form>

      <el-table :data="list" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="order_id" :label="$t('common.pp_order_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.order_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="service_result" :label="$t('common.pp_order_status')" :min-width="locale == 'zh' ? '90' : '120'" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="tag-container" :style="{ color: serviceResultMap[row.service_result].color, background: serviceResultMap[row.service_result].background, width: locale == 'zh' ? '58px' : '80px' }">
              {{ $t(serviceResultMap[row.service_result].name) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order_start_time" :label="$t('common.pp_order_start_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.order_start_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_end_time" :label="$t('common.pp_order_end_time')" min-width="170" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.order_end_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_id" :label="$t('common.pp_vehicle_id')" min-width="240">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.vehicle_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_brand" :label="$t('common.pp_vehicle_brand')" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.vehicle_brand ? row.vehicle_brand : '' }}-{{ row.vehicle_type ? row.vehicle_type : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_platform" :label="$t('common.pp_vehicle_platform')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.vehicle_platform || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('common.pp_device_name')" min-width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="device_id" :label="$t('common.pp_device_id')" min-width="240">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.pp_detail')" width="70" fixed="right" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <DetailIcon @click="handleClickDetail(row)" class="cursor-pointer" />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination">
        <Page :page="pages" @change="handlePageChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef, h, onBeforeMount, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { formatTime, removeNullKeys, clearJson, initStartTime, initEndTime, getTodayShortcuts, getDisabledTodayDate } from '~/utils'
import { apiGetDevices } from '~/apis/home'
import { apiGetOrderList } from '~/apis/portrait-list'
import { projectOptions, orderStatusOptions, serviceResultMap } from './components/constant'
import { ElMessage } from 'element-plus'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import UpIcon from '~/assets/svg/up-icon.vue'
import DownIcon from '~/assets/svg/down-icon.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import DetailIcon from '~/views/satisfaction-analysis/components/icon/detail-icon.vue'
import { debounce, cloneDeep } from 'lodash-es'

const { t } = useI18n()
const store = useStore()
const route = useRoute()
const router = useRouter()
const isFold = ref(true)
const loading = ref(false)
const remoteLoading = ref(false)
const list = ref([] as any)
const brand = ref([] as any)
const deviceOptions = ref([] as any)
const datePicker = ref([initStartTime, initEndTime] as any)
const form = ref({
  start_time: initStartTime,
  end_time: initEndTime,
  project: '',
  order_id: '',
  vehicle_platform: '',
  vehicle_types: '',
  vehicle_brands: '',
  device_id: '',
  vehicle_id: '',
  service_results: []
})
const searchForm = ref({} as any)
const cascaderProps = ref({ multiple: true })
const pages = ref(cloneDeep(page))
const { locale } = useI18n({ useScope: 'global' })
const brandOptions = ref(computed(() => store.state.vehicle.brandList))
const platformOptions = ref(computed(() => store.state.vehicle.carPlatform))

const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})

/**
 * @description: 跳转至详情
 * @param {*} row
 * @return {*}
 */
const handleClickDetail = (row: any) => {
  sessionStorage.setItem('portrait-list', JSON.stringify(route.query))
  router.push({
    path: `/fault-diagnosis/portrait-list/portrait-detail`,
    query: { project: form.value.project, order: row.order_id }
  })
}

/**
 * @description: 选择车辆品牌
 * @return {*}
 */
const handleBrandChange = () => {
  const result = brand.value.reduce(
    (acc: any, curr: any) => {
      const [brand, type] = curr
      if (!acc.vehicle_brands.includes(brand)) acc.vehicle_brands.push(brand)
      if (!acc.vehicle_types.includes(type)) acc.vehicle_types.push(type)
      return acc
    },
    { vehicle_brands: [], vehicle_types: [] }
  )
  form.value.vehicle_brands = result.vehicle_brands.join(',')
  form.value.vehicle_types = result.vehicle_types.join(',')
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: 'PowerSwap2,PUS3,PUS4', name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 获取订单列表
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.service_results = formData.service_results.join(',')
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.descending = true
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys(formData) }
    })
  }
  loading.value = true
  try {
    const res = await apiGetOrderList(formData, form.value.project)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  form.value.project = 'PUS4'
  brand.value = []
  datePicker.value = [initStartTime, initEndTime]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = cloneDeep(form.value)
  getList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value = [form.value.start_time, form.value.end_time]
  }
  brand.value = !!initParams.vehicle_types ? initParams.vehicle_types.split(',') : []
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.project = !!initParams.project ? initParams.project : 'PUS4'
  form.value.order_id = !!initParams.order_id ? initParams.order_id : ''
  form.value.vehicle_platform = !!initParams.vehicle_platform ? initParams.vehicle_platform : ''
  form.value.vehicle_brands = !!initParams.vehicle_brands ? initParams.vehicle_brands : ''
  form.value.vehicle_types = !!initParams.vehicle_types ? initParams.vehicle_types : ''
  form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
  form.value.vehicle_id = !!initParams.vehicle_id ? initParams.vehicle_id : ''
  form.value.service_results = !!initParams.service_results ? initParams.service_results.split(',') : []
  searchForm.value = cloneDeep(form.value)
  searchDeviceList(form.value.device_id || 'NIO')
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.portrait-list-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    background-color: #fff;
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px 24px;
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-form-item__label-wrap {
          margin: 0 !important;
        }
        .el-form-item__content {
          white-space: nowrap;
          align-items: flex-start;
          flex-wrap: nowrap;
        }
        .el-date-editor .el-range-input {
          color: #262626;
        }
        .el-date-editor .el-range__icon {
          margin-right: 10px;
        }
        .el-select .el-select__tags .el-tag--info {
          color: #262626;
        }
        .el-tag.el-tag--info {
          color: #262626;
        }
        .brand-cascader {
          width: 100%;
          &:hover {
            position: relative;
            z-index: 2;
          }
        }
      }
    }
    .tag-container {
      height: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
</style>
