<template>
  <div class="app-detail-container">
    <!-- header -->
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item @click="handleJump">
            {{ $t('edgeCloud.pp_cloud') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item @click="handleJump">
            {{ $t('edgeCloud.pp_app_list') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t('edgeCloud.pp_app_detail') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="service-detail-header">
          <div class="card-title">
            <div class="title-info">
              {{ $t('edgeCloud.pp_app_detail') }}
            </div>
            <div class="title-device margin_r-20">
              <span class="station-device margin_b-5">
                {{ headInfo.app_name }}
              </span>
              <span class="device-id">
                {{ headInfo.namespace }}
              </span>
            </div>
            <div class="tag-container">
              <el-tag effect="dark" :disable-transitions="true" round>
                <div class="tag-info">
                  <powerswap2-logo v-if="route.query.device_type == 'PowerSwap2'" />
                  <powerswap3-logo v-if="route.query.device_type == 'PUS3'" />
                  <span class="tag-title">
                    {{ route.query.device_type == 'PowerSwap2' ? $t(`menu.pp_swap_station2`) : $t(`menu.pp_swap_station3`) }}
                  </span>
                </div>
              </el-tag>
            </div>
            <div class="detail-fold-button" @click="isFold = !isFold">
              <el-icon :size="14" v-if="!isFold">
                <ArrowRightBold />
              </el-icon>
              <el-icon :size="14" v-if="isFold">
                <ArrowDownBold />
              </el-icon>
              <span class="fold-title">
                {{ $t('edgeCloud.pp_app_detail') }}
              </span>
            </div>
          </div>
          <el-row v-if="isFold">
            <el-col :span="12">
              <div class="span-title first-line">
                <div class="span-title-label">READY</div>
                {{ headInfo.available_replicas + '/' + headInfo.replicas }}
              </div>
              <div class="span-title">
                <div class="span-title-label">
                  {{ $t('edgeCloud.pp_creator') }}
                </div>
                {{ headInfo.creator }}
              </div>
            </el-col>

            <el-col :span="12">
              <div class="span-title first-line">
                <div class="span-title-label">
                  {{ $t('edgeCloud.pp_create_time') }}
                </div>
                {{ formatTime(headInfo.create_ts) }}
              </div>
              <div class="span-title">
                <div class="span-title-label">
                  {{ $t('edgeCloud.pp_update_time') }}
                </div>
                {{ formatTime(headInfo.update_ts) }}
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- body -->
    <div class="swap-page-container">
      <!-- 筛选项 -->
      <div class="search-container">
        <span class="search-item-label">{{ $t('edgeCloud.pp_cluster') }}</span>
        <el-select v-model="searchForm.device_id" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 40%; margin-right: 20px">
          <el-option v-for="item in deviceNameOptions" :key="item.device_id" :label="item.device_id + ' - ' + item.description" :value="item.device_id" />
        </el-select>
        <span class="search-item-label">
          {{ $t('edgeCloud.pp_sub_system') }}
        </span>
        <el-select v-model="searchForm.subsystem" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 20%; margin-right: 20px">
          <el-option v-for="item in subSystemOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <span class="search-item-label white-space-nowrap">{{ $t('edgeCloud.pp_instance_status') }}</span>
        <el-select v-model="searchForm.status" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 20%; margin-right: 20px">
          <el-option v-for="item in statusOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div class="search-button" style="width: 20%">
          <el-button @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
          <el-button @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="swap-table-container">
        <el-table
          :data="tableData"
          row-key="device_id"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
        >
          <el-table-column type="expand">
            <template #default="scope">
              <div class="inner-container">
                <el-table border :data="scope.row.instances" row-key="instance_name">
                  <el-table-column prop="instance_name" :label="`${$t('edgeCloud.pp_instance')}`" min-width="200" fixed show-overflow-tooltip />
                  <el-table-column prop="status" :label="`${$t('edgeCloud.pp_running_status')}`" min-width="100" show-overflow-tooltip>
                    <template #default="scoped">
                      <span>{{ scoped.row.status }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="subsystem" :label="`${$t('edgeCloud.pp_sub_system')}`" min-width="100" show-overflow-tooltip />
                  <el-table-column :label="`${$t('edgeCloud.pp_resource_usage')}`" min-width="180" show-overflow-tooltip>
                    <template #default="scope">
                      <div>
                        <span>CPU：</span>
                        <span>{{ scope.row.cpu_usage + ' / ' + scope.row.cpu_request }}</span>
                      </div>
                      <div>
                        <span>{{$t('edgeCloud.pp_memory')}}：</span>
                        <span>{{ scope.row.memory_usage + ' / ' + scope.row.memory_request }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="`${$t('common.pp_operation')}`" min-width="140" show-overflow-tooltip>
                    <template #default="scope">
                      <span @click="handleViewDetails(scope.$index, scope.row)" class="edit-text margin_r-10">{{ $t('edgeCloud.pp_details') }}</span>
                      <span @click="handleViewEvents(scope.$index, scope.row)" class="edit-text margin_r-10">{{ $t('edgeCloud.pp_event') }}</span>
                      <span @click="handleViewLogs(scope.$index, scope.row)" class="edit-text margin_r-10">{{ $t('edgeCloud.pp_log') }}</span>
                      <span @click="handleDeleteInstance(scope.$index, scope.row)" class="edit-text">{{ $t('edgeCloud.pp_restart') }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <!-- <div class="pagination-container children-page">
                <Page v-if="(scope.row.nodeList && scope.row.nodeList.length > 0) || !scope.row.nodeList" :page="scope.row.page" @change="(val) => handleChildPageChange(val, scope.row)" />
              </div> -->
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="`${$t('edgeCloud.pp_cluster_name')}`" />
          <el-table-column prop="device_id" :label="`${$t('edgeCloud.pp_cluster_id')}`">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>
          <el-table-column prop="nodes_count" :label="`${$t('edgeCloud.pp_node_number')}`" />
        </el-table>
        <div class="pagination-container">
          <Page v-if="(tableData && tableData.length > 0) || !tableData" :page="pages" @change="handlePageChange" />
        </div>
      </div>

      <!-- 实例详情 -->
      <el-dialog v-model="detailDialogVisible" :title="`${$t('edgeCloud.pp_instance_detail')}`" width="600px" align-center>
        <el-descriptions :column="1">
          <el-descriptions-item :label="`${$t('edgeCloud.pp_instance')}`">{{ detailInfo.instance_name }}</el-descriptions-item>
          <el-descriptions-item :label="`${$t('edgeCloud.pp_create_time')}`">{{ formatTime(detailInfo.create_ts) }}</el-descriptions-item>
          <el-descriptions-item :label="`${$t('edgeCloud.pp_image')}`">{{ detailInfo.image }}</el-descriptions-item>
          <el-descriptions-item :label="`${$t('edgeCloud.pp_port_info')}`">
            <el-table
              :data="detailInfo.ports"
              style="width: 80%"
              :header-cell-style="{
                fontSize: '14px',
                color: '#292C33',
                cursor: 'auto'
              }"
              border
            >
              <el-table-column prop="protocol" :label="`${$t('edgeCloud.pp_port_protocol')}`" />
              <el-table-column prop="name" :label="`${$t('edgeCloud.pp_port_name')}`" />
              <el-table-column prop="port" :label="`${$t('edgeCloud.pp_port')}`" />
            </el-table>
          </el-descriptions-item>
        </el-descriptions>
      </el-dialog>

      <!-- 事件详情 -->
      <el-dialog v-model="eventDialogVisible" :title="`${$t('edgeCloud.pp_event')}`" width="600px" align-center>
        <el-table
          :data="eventInfo"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
          border
        >
          <el-table-column prop="type" label="type" show-overflow-tooltip />
          <el-table-column prop="reason" label="reason" show-overflow-tooltip />
          <el-table-column prop="age" label="age" show-overflow-tooltip />
          <el-table-column prop="from" label="from" show-overflow-tooltip />
          <el-table-column prop="message" label="message" show-overflow-tooltip />
        </el-table>
      </el-dialog>

      <!-- 日志详情 -->
      <el-dialog v-model="logDialogVisible" :title="`${$t('edgeCloud.pp_log')}`" width="750px" align-center>
        <div v-html="logInfo" class="log-container"></div>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, reactive, onBeforeMount, onBeforeUnmount, watch} from 'vue'
import {page} from '~/constvars/page'
import {ElMessage, ElMessageBox} from 'element-plus'
import {removeNullProp, formatTime} from '~/utils'
import {useRoute, useRouter} from 'vue-router'
import {useI18n} from 'vue-i18n'
import {apiGetDeviceNameMap} from '~/apis/device-management'
import {ArrowDownBold, ArrowRightBold} from '@element-plus/icons-vue'
import {apiGetAppList, apiGetInstanceOptions, apiGetAppInstanceList, apiDeleteInstance, apiGetEventList, apiGetlogList} from '~/apis/edge-cloud'
import Powerswap3Logo from '../components/powerswap3-logo.vue'
import Powerswap2Logo from '../components/powerswap2-logo.vue'
import _ from 'lodash'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const getListTimeout = ref()
const loading = ref(false)
const isFold = ref(false)
const detailDialogVisible = ref(false)
const eventDialogVisible = ref(false)
const logDialogVisible = ref(false)
const pages = ref(_.cloneDeep(page))
const logInfo = ref('' as any)
const deviceNameOptions = ref([] as any)
const tableData = ref([])
const instanceOptions = ref([])
const statusOptions = ref(['Running', 'Pending', 'Succeeded', 'Failed', 'Waiting', 'Terminated', 'Unknown'])
const subSystemOptions = ref(['MCS', 'AEC', 'PLC'])
const eventInfo = ref([] as any)
const headInfo = ref({} as any)
const detailInfo = ref({} as any)
const searchForm = ref({
  device_id: '',
  device_type: route.query.device_type as string,
  status: '',
  subsystem: '',
  page: 1,
  size: 10
})
const form = ref({
  device_id: '',
  device_type: route.query.device_type as string,
  status: '',
  subsystem: '',
  page: 1,
  size: 10
})

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  searchForm.value.device_id = ''
  searchForm.value.status = ''
  searchForm.value.subsystem = ''
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  pages.value.size = 10
  form.value = {...searchForm.value}
  getDeviceList(true, 2)
}

/**
 * @description: 搜索
 * @param {*} updateRoute
 * @return {*}
 */
const getDeviceList = async (updateRoute = true, status: any) => {
  if (loading.value) return
  clearTimeout(getListTimeout.value)
  let formData = {} as any
  if (status === 2) {
    formData = {...form.value}
  } else {
    formData = {...searchForm.value}
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  if (updateRoute) {
    router.push({
      path: `/cloud/app-list/app-detail/${route.params.app}`,
      query: {...removeNullProp(formData)}
    })
  }
  loading.value = true
  try {
    const res = await apiGetAppInstanceList(searchForm.value.device_type, route.params.app as string, formData)
    tableData.value = res.data
    pages.value.total = res.total
    getListTimeout.value = setTimeout(() => {
      getDeviceList(false, 2)
    }, 5000)
  } catch (error) {}
  loading.value = false
}

/**
 * @description: 点击后退
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleJump = () => {
  router.push({
    path: `/cloud/app-list`,
    query: JSON.parse(sessionStorage.getItem('app-list')!)
  })
}

/**
 * @description: 跳转至集群
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
// const handleJumpCluster = (index: number, row: any) => {
//   router.push({
//     path: `/cloud/cluster-list/cluster-detail/${row.id}`,
//     query: {device_type: searchForm.device_type}
//   })
// }
// const handleStopSchedule = (index: number, row: any) => {}

/**
 * @description: 外层分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getDeviceList(true, 2)
}

/**
 * @description: 内层分页
 * @param {*} argPage
 * @param {*} row
 * @return {*}
 */
// const handleChildPageChange = (argPage: any, row: any) => {
//   row.page.current = argPage.current
//   row.page.size = argPage.size
// }

/**
 * @description: 获取集群map
 * @return {*}
 */
const getDeviceNameMapping = async () => {
  const res = await apiGetDeviceNameMap(searchForm.value.device_type as string)
  deviceNameOptions.value = res.data
}

/**
 * @description: 获取页头信息
 * @return {*}
 */
const getHeadInfo = async () => {
  const params = {app_name: route.params.app}
  const res = await apiGetAppList(searchForm.value.device_type, params)
  headInfo.value = res.data[0]
  getInstanceOptions()
}

/**
 * @description: 获取实例选项
 * @return {*}
 */
const getInstanceOptions = async () => {
  const params = {namespace: headInfo.value.namespace}
  const res = await apiGetInstanceOptions(searchForm.value.device_type, route.params.app as string, params)
  instanceOptions.value = res.instance_list
}

/**
 * @description: 重启实例
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleDeleteInstance = async (index: number, row: any) => {
  ElMessageBox.confirm(`${t('edgeCloud.pp_sure_restart')} ${row.instance_name}？`, {
    confirmButtonText: t('common.pp_confirm'),
    cancelButtonText: t('common.pp_cancel'),
    type: 'warning'
  }).then(async () => {
    const params = {namespace: headInfo.value.namespace}
    try {
      const res = await apiDeleteInstance(searchForm.value.device_type, row.instance_name, params)
      if (!res.err_code) ElMessage.success(t('edgeCloud.pp_restart_success'))
    } catch (error) {}
    getDeviceList(false, 2)
  })
}

/**
 * @description: 查看详情
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleViewDetails = (index: number, row: any) => {
  detailInfo.value = row
  detailDialogVisible.value = true
}

/**
 * @description: 查看事件
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleViewEvents = async (index: number, row: any) => {
  const params = {namespace: headInfo.value.namespace}
  try {
    const res = await apiGetEventList(searchForm.value.device_type, row.instance_name, params)
    eventInfo.value = res.event_list
  } catch (error) {}
  eventDialogVisible.value = true
}

/**
 * @description: 获取实例运行时Log
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleViewLogs = async (index: number, row: any) => {
  const params = {namespace: headInfo.value.namespace}
  try {
    const res = await apiGetlogList(searchForm.value.device_type, row.instance_name, params)
    logInfo.value = res.message.replace(/\n/g, '<br/>')
  } catch (error) {}
  logDialogVisible.value = true
}

/**
 * @description: 初始化筛选条件
 * @return {*}
 */
const initWeb = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      const initParams: any = route.query
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      searchForm.value.device_id = !!initParams.device_id ? initParams.device_id : ''
      searchForm.value.subsystem = !!initParams.subsystem ? initParams.subsystem : ''
      searchForm.value.status = !!initParams.status ? initParams.status : ''
      getDeviceNameMapping()
      form.value = {...searchForm.value}
      getDeviceList(false, 2)
    }
  },
  {immediate: true}
)

/**
 * @description: 组件销毁卸载轮询，定时器清空
 * @return {*}
 */
onBeforeUnmount(() => {
  clearTimeout(getListTimeout.value)
})

onBeforeMount(() => {
  console.log(route.params)
  initWeb()
  getHeadInfo()
})
</script>

<style lang="scss" scoped>
@import '~/styles/service-detail.scss';
.app-detail-container {
  .swap-page-header {
    height: auto;
    display: block;
    // padding-bottom: 0px;
  }
  .service-detail-header {
    padding: 0;
    ::v-deep(.el-row) {
      margin-bottom: 0;
      margin-top: 10px;
    }
    .card-title {
      margin: 5px 0;
    }
  }
  .edit-text {
    color: #00bebe;
    &:hover {
      cursor: pointer;
      color: #66d2d2;
    }
  }
  .pagination-container {
    ::v-deep(.el-pagination) {
      margin-bottom: 20px;
    }
  }
  .children-page {
    width: auto;
    margin: 8px 50px 10px;
    ::v-deep(.el-pagination) {
      margin: 0;
    }
  }
  .log-container {
    background: black;
    color: #b0bec5;
    padding: 10px;
  }
  .inner-container {
    padding: 20px 50px;
  }
  .expand-button,
  .fold-button {
    cursor: pointer;
    display: flex;
    align-items: center;
    .el-icon {
      color: var(--el-color-primary);
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .fold-info {
    color: var(--el-color-primary);
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
  }
  ::v-deep(.el-descriptions__cell) {
    display: flex;
  }
  ::v-deep(.el-descriptions__label) {
    color: #22252b !important;
    white-space: nowrap;
    font-weight: bold;
    font-family: 'Noto Sans';
  }
  ::v-deep(.el-descriptions__content) {
    color: #22252b !important;
    font-family: 'Noto Sans';
    .el-table {
      .cell {
        color: #22252b;
        font-family: 'Noto Sans';
      }
    }
  }
  ::v-deep(.el-dialog__body) {
    padding-top: 15px;
    padding-bottom: 15px;
    .el-table {
      .cell {
        color: #22252b;
        font-family: 'Noto Sans';
      }
    }
  }
}
</style>
