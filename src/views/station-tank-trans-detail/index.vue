<template>
  <div>
    <!-- 设备管理/设备详情/倒仓详情 -->
    <div class="service-detail-header" style="padding: 20px 20px 0 20px">
      <el-breadcrumb separator="/">
        <WelkinStationBreadcrumb :version="project.version" @click="changeToPre" />
        <el-breadcrumb-item @click="changeToDevice">
          {{ $t('menu.pp_device_management') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item @click="changeToPre">
          {{ $t('menu.pp_device_detail') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ $t('menu.pp_tank_trans_detail') }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="service-detail-header">
      <div class="card-title">
        <div class="title-info">
          {{ $t('menu.pp_tank_trans_detail') }}
        </div>
        <div class="title-device">
          <span class="station-device">
            {{ detail.description }}
          </span>
          <span class="device-id">{{ detail.device_id }}</span>
        </div>
        <div class="height-20">
          <el-tag type="" effect="dark" :disable-transitions="true" round style="margin-top: 0">
            <div class="tag-info">
              <powerswap1-logo v-if="project.version == 1" />
              <powerswap2-logo v-if="project.version == 2" />
              <powerswap3-logo v-if="project.version == 3" />
              <span class="tag-title">
                {{ $t(`menu.pp_swap_station${project.version}`) }}
              </span>
            </div>
          </el-tag>
        </div>
        <div class="detail-fold-button" @click="foldClick">
          <el-icon :size="14" v-if="!isFold">
            <ArrowRightBold />
          </el-icon>
          <el-icon :size="14" v-if="isFold">
            <ArrowDownBold />
          </el-icon>
          <span class="fold-title">
            {{ $t('menu.pp_tank_trans_detail') }}
          </span>
        </div>
      </div>
      <el-row v-if="isFold">
        <el-col :span="12">
          <div class="span-title first-line">
            <div class="span-title-label">{{ $t('tankTrans.pp_tank_id') }}</div>
            <WelkinCopyBoard :text="detail.trans_id" />
          </div>

          <div class="span-title">
            <div class="span-title-label">
              {{ $t('serviceDetail.pp_service_time_frame') }}
            </div>
            {{ formatLocaleDate(detail.trans_start_ts) }} -
            {{ formatLocaleDate(detail.trans_end_ts) }}
          </div>

          <div class="span-title">
            <div class="span-title-label">
              {{ $t('tankTrans.pp_trans_duration') }}
            </div>
            {{ detail.duration }}
          </div>
        </el-col>

        <el-col :span="12">
          <div class="span-title first-line">
            <div class="span-title-label">
              {{ $t('tankTrans.pp_full_charged_slot') }}
            </div>
            {{ detail.full_charged_slot }}
          </div>

          <div class="span-title">
            <div class="span-title-label">
              {{ $t('tankTrans.pp_drianed_slot') }}
            </div>
            {{ detail.drianed_slot }}
          </div>

          <div class="span-title">
            <div class="span-title-label">
              {{ $t('tankTrans.pp_empty_slot') }}
            </div>
            {{ detail.empty_slot }}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="swap-page-container">
      <el-row class="collapse-search-container">
        <el-form ref="ruleFormRef" :model="searchForm" inline style="width: 100%" class="slot-detail-form">
          <el-form-item prop="step_num" :label="`${$t('serviceDetail.pp_platform_step')}`">
            <el-select v-model="searchForm.step_num" clearable filterable class="m-2" :placeholder="`${$t('common.pp_please_select') + $t('serviceDetail.pp_battery_step')}`">
              <el-option v-for="item in step_axis.step_options" :key="item.value" :label="$t(`tankTrans.tank_step.${item.value}`)" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="axis" :label="$t('serviceDetail.pp_axis_name')">
            <el-select v-model="searchForm.axis" multiple clearable filterable class="m-2" :placeholder="`${$t('common.pp_please_select') + $t('serviceDetail.pp_axis_name')}`">
              <el-option v-for="item in step_axis.axis_options" :key="item.value" :label="$t(`tankTrans.tank_axis.${item.value}`)" :value="item.value" />
            </el-select>
          </el-form-item>
          <div class="flex-box flex_a_i-center">
            <el-button class="welkin-primary-button" :loading="loading" @click="speedSearch(ruleFormRef)">
              {{ $t('common.pp_search') }}
            </el-button>
            <el-button @click="resetSearch(ruleFormRef)" class="welkin-secondary-button">
              {{ $t('common.pp_reset') }}
            </el-button>
          </div>
        </el-form>
      </el-row>

      <div class="plc-recording-container">
        <el-row class="plc-empty-box" v-show="showImage === false && dataEmpty === false">
          <el-empty :description="loading === true ? `${$t('common.pp_loading')}` : `${$t('serviceDetail.pp_speed_empty')}`" />
        </el-row>

        <el-row class="plc-empty-box" v-show="showImage === false && dataEmpty === true">
          <el-empty :description="`${$t('common.pp_empty')}`" />
        </el-row>

        <div v-show="showImage === true">
          <PlcChartGroup :data="chartData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onBeforeMount, reactive, watch, computed} from 'vue'
import {useRouter, useRoute, onBeforeRouteLeave} from 'vue-router'
import {ArrowLeftBold, ArrowRightBold, ArrowDownBold} from '@element-plus/icons-vue'
import type {FormInstance, FormRules} from 'element-plus'
import powerswap1Logo from '../components/powerswap1-logo.vue'
import powerswap2Logo from '../components/powerswap2-logo.vue'
import powerswap3Logo from '../components/powerswap3-logo.vue'
import {formatLocaleDate, getDuration, getChartGroupData} from '~/utils'
import {useStore} from 'vuex'
import {apiGetTankTransRecord} from '~/apis/tank-trans'
import PlcChartGroup from '~/views/components/plc-chart-group/index.vue'
import {tank_step, tank_axis} from '~/constvars/tank-trans'
import {battery_step, platform_step} from '~/constvars/plc-record/step'

// 监听路由
const $route = useRoute()
const $store = useStore()
const $router = useRouter()
const project = ref(computed(() => $store.state.project))

const isFold = ref(false)
const loading = ref(false)
const ruleFormRef = ref<FormInstance>()

const detail = ref({
  description: '',
  device_id: '',
  trans_id: '',
  duration: '',
  full_charged_slot: '',
  drianed_slot: '',
  empty_slot: '',
  start_time: '',
  end_time: '',
  trans_start_ts: '',
  trans_end_ts: ''
})

const searchForm = reactive({
  step_num: '' as number | string,
  axis: [] as string[] | number[],
  start_time: '' as number | string,
  end_time: '' as number | string
})

const step_axis = ref<any>({
  step_options: [] as any[],
  axis_options: [] as any[],
  axis: {}
})
// 重置筛选项
const resetSearch = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

// 展开和收缩服务详情
const foldClick = () => {
  isFold.value = !isFold.value
}

// 回到上级页面
const changeToPre = () => {
  let query: any = sessionStorage.getItem('station-tank-list')

  $router.push({
    path: `/${project.value.route}/device-management/single-station/${$route.params.deviceId}`,
    query: JSON.parse(query)
  })
}

// 回到设备管理页面
const changeToDevice = () => {
  let query: any = sessionStorage.getItem('device-management')
  $router.push({
    path: `/${project.value.route}/device-management`,
    query: JSON.parse(query)
  })
}

const showImage = ref(false)
const dataEmpty = ref(false)
const chartData = ref([] as any)

const handleRecordChart = (res: any) => {
  if (!res.data) {
    showImage.value = false
    dataEmpty.value = true
  } else {
    let testdata = getChartGroupData(res.data, searchForm.axis, step_axis.value.axis, ['torque', 'position', 'speed'], 'high_speed', `tankTrans.tank_axis`)
    console.log('倒仓 getChartGroupData ', testdata)
    showImage.value = true
    chartData.value = testdata
  }
}

const speedSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  console.log('speedSearch', {...searchForm})
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('valid')
      // 如果经过校验，同步修改url
      let query = {
        step_num: searchForm.step_num,
        axis: searchForm.axis.toString()
      }

      $router.push({
        path: $router.currentRoute.value.path,
        query: {...$route.query, ...query}
      })

      loading.value = true
      searchForm.start_time = detail.value.trans_start_ts
      searchForm.end_time = detail.value.trans_end_ts
      // 查看倒仓图表
      console.log('查看倒仓图表 api:', searchForm, project.value.project)
      return apiGetTankTransRecord(project.value.project, $route.params.deviceId, $route.params.transId, searchForm)
        .then((res) => {
          loading.value = false
          handleRecordChart(res)
        })
        .catch((err) => {
          loading.value = false
          dataEmpty.value = true
        })
    }
  })
}

onBeforeRouteLeave(() => {
  // 清空上次图表数据
  resetSearch(ruleFormRef.value)

  showImage.value = false
  dataEmpty.value = false
})

const initOptions = () => {
  step_axis.value.axis = tank_axis

  let options = []
  let obj = tank_step
  for (let i in obj) {
    options.push({label: obj[i], value: i})
  }
  step_axis.value.step_options = options

  options = []
  obj = step_axis.value.axis
  for (let i in obj) {
    options.push({label: obj[i], value: i})
  }
  step_axis.value.axis_options = options
}

onBeforeMount(() => {
  initOptions()

  if (!!$route.query.axis) {
    searchForm.axis = String($route.query.axis).split(',')
    searchForm.step_num = String($route.query.step_num)
  }

  const query = {
    ...searchForm,
    start_time: $route.query.start_time,
    end_time: $route.query.end_time
  }
  apiGetTankTransRecord(project.value.project, $route.params.deviceId, $route.params.transId, query).then((res) => {
    const data = res
    data.duration = getDuration(data.trans_start_ts, data.trans_end_ts)
    detail.value = data
    handleRecordChart(res)
  })
})
</script>

<style lang="scss" scoped>
@import '~/styles/service-detail.scss';
@import '~/styles/plc-record.scss';
.collapse-search-container,
.fold-search-container {
  margin-bottom: 0;
  .slot-detail-form {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr;
    .el-form-item {
      display: flex;
      align-items: center;
    }
    .button-container {
      display: flex;
      justify-content: flex-start;
    }
  }
}
</style>
