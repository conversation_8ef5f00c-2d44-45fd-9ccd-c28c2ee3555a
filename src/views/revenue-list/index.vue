<template>
  <div class="revenue-list-container">
    <div class="header-container">
      <div class="font-size-20 font-weight-bold color-1f">{{ $t('menu.pp_revenue_list') }}</div>
    </div>

    <div class="content-container">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane :label="$t('stationManagement.pp_revenue_overview')" name="overview">
          <Overview v-if="activeTab == 'overview'" :areaOptions="areaOptions" />
        </el-tab-pane>
        <el-tab-pane :label="$t('stationManagement.pp_revenue_detail')" name="detail">
          <Detail v-if="activeTab == 'detail'" :areaOptions="areaOptions" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { apiCompanyMap } from '~/apis/device-health'
import Overview from './components/overview.vue'
import Detail from './components/detail.vue'

const route = useRoute()
const router = useRouter()
const activeTab = ref('' as any)
const areaOptions = ref([] as any)

/**
 * @description: 切换Tab
 * @return {*}
 */
const handleTabChange = () => {
  router.push({
    path: location.pathname,
    query: { tab: activeTab.value }
  })
}

/**
 * @description: 获取区域公司列表
 * @return {*}
 */
const getCompanyNameMap = async () => {
  const res = await apiCompanyMap({ projects: 'PUS3,PowerSwap2' })
  areaOptions.value = Object.keys(res.data)
}

onBeforeMount(() => {
  activeTab.value = route.query.tab || 'overview'
  getCompanyNameMap()
})
</script>

<style lang="scss" scoped>
.revenue-list-container {
  font-family: 'Blue Sky Standard';
  padding: 24px;
  background: linear-gradient(180deg, #e2f9f9 -6.51%, #f8f8f8 12.89%);
  .header-container {
    margin-bottom: 12px;
  }
  .content-container {
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px 24px;
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
        .el-form-item__label-wrap {
          margin: 0 !important;
        }
        .el-date-editor .el-range-input {
          color: #262626;
        }
        .el-date-editor .el-range__icon {
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
