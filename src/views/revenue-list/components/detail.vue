<template>
  <div class="revenue-detail-container">
    <el-form :model="form">
      <el-form-item :label="$t('common.pp_time_frame')">
        <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getYesterdayShortcuts()" :clearable="false" :disabledDate="getDisabledYesterdayDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('common.pp_device_type')">
        <el-select v-model="form.project" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_regional_company')">
        <el-select v-model="form.city_company" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in areaOptions" :key="item" :value="item" :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_device_name')">
        <el-select v-model="form.device_id" clearable filterable remote :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
          <template #prefix><SearchIcon /></template>
          <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description">
            <span style="float: left">{{ item.description }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
        <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
      </el-form-item>
      <el-form-item>
        <div class="width-full flex-box flex_j_c-flex-end">
          <el-button @click="handleDownload" class="welkin-secondary-button">
            <DownloadIcon />
            <span class="margin_l-4">{{ $t('common.pp_download') }}</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <div class="content-container">
      <el-table :data="list" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" v-loading="loading">
        <el-table-column prop="device_id" :label="$t('common.pp_device_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('common.pp_device_name')" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.description || $t('common.pp_unnamed_device') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="project" :label="$t('common.pp_device_type')" min-width="95" show-overflow-tooltip>
          <template #default="{ row }">
            <span :style="{ color: projectMap[row.project].color }">{{ $t(projectMap[row.project].label) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="max_revenue_rate" :label="$t('stationManagement.pp_max_revenue_rate')" min-width="130" show-overflow-tooltip align="center">
          <template #default="{ row }">
            <span v-if="isEmptyData(row.max_revenue_rate)">-</span>
            <span v-else :style="{ color: row.max_revenue_rate > 0 ? '#52C31C' : '#FD8C08' }"> {{ row.max_revenue_rate >= 0 ? row.max_revenue_rate : 0 }}% </span>
          </template>
        </el-table-column>
        <el-table-column prop="total_revenue" :label="$t('stationManagement.pp_total_revenue')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ isEmptyData(row.total_revenue) ? '-' : row.total_revenue.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="off_peak_revenue" :label="$t('stationManagement.pp_off_peak')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ isEmptyData(row.off_peak_revenue) ? '-' : row.off_peak_revenue.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="energy_revenue" :label="$t('stationManagement.pp_energy')" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ isEmptyData(row.energy_revenue) ? '-' : row.energy_revenue.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="battery_maintenance_revenue" :label="$t('stationManagement.pp_battery')" min-width="110" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ isEmptyData(row.battery_maintenance_revenue) ? '-' : row.battery_maintenance_revenue.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="battery_maintenance_times" :label="$t('stationManagement.pp_battery_maintenance_times')" min-width="110" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ isEmptyData(row.battery_maintenance_times) ? '-' : row.battery_maintenance_times.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="day" :label="$t('common.pp_date')" width="110" show-overflow-tooltip>
          <template #default="{ row }">
            <span> {{ formatTimeToDay(row.day) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.pp_detail')" width="70" align="center" fixed="right" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_j_c-center flex_a_i-center">
              <DetailIcon @click="handleClickDetail(row)" class="cursor-pointer" />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination">
        <Page :page="pages" @change="handlePageChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, shallowRef, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { page } from '~/constvars/page'
import { apiGetDevices } from '~/apis/home'
import { apiGetRevenueList, apiDownloadRevenueList } from '~/apis/station-management'
import { ElMessage } from 'element-plus'
import { cloneDeep, debounce } from 'lodash-es'
import { projectOptions, projectMap, initYesterdayStartTime, initYesterdayEndTime, getYesterdayShortcuts, getDisabledYesterdayDate } from './constant'
import { formatTimeToDay, removeNullKeys, clearJson, getStartTimeOfDay, getFinalEndTime, isEmptyData } from '~/utils'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import DetailIcon from '~/assets/svg/detail.vue'
import DownloadIcon from './icon/download.vue'

const props = defineProps(['areaOptions'])

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const remoteLoading = ref(false)
const deviceOptions = ref([] as any)
const list = ref([] as any)
const datePicker = ref([initYesterdayStartTime - 3600 * 1000 * 24 * 6, initYesterdayEndTime] as any)
const form = ref({
  start_time: initYesterdayStartTime - 3600 * 1000 * 24 * 6,
  end_time: initYesterdayEndTime,
  project: '',
  city_company: '',
  device_id: ''
})
const searchForm = ref({} as any)
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const pages = ref(cloneDeep(page))

/**
 * @description: 查看详情
 * @param {*} row
 * @return {*}
 */
const handleClickDetail = (row: any) => {
  console.log(row)
  sessionStorage.setItem('revenue-list', JSON.stringify(route.query))
  router.push({
    path: `/station-management/revenue-list/revenue-detail`,
    query: removeNullKeys({ device_id: row.device_id, day: row.day, project: row.project, description: row.description })
  })
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: 'PowerSwap2,PUS3', name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 下载
 * @return {*}
 */
const handleDownload = async () => {
  let formData = cloneDeep(searchForm.value) as any
  formData.start_time = getStartTimeOfDay(searchForm.value.start_time)
  formData.end_time = getFinalEndTime(searchForm.value.end_time)
  formData.download = true
  removeNullKeys(formData)
  await apiDownloadRevenueList(formData)
}

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  datePicker.value = [initYesterdayStartTime - 3600 * 1000 * 24 * 6, initYesterdayEndTime]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = cloneDeep(form.value)
  getList()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 获取数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.start_time = getStartTimeOfDay(searchForm.value.start_time)
  formData.end_time = getFinalEndTime(searchForm.value.end_time)
  formData.download = false
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: 'detail', ...removeNullKeys(formData) }
    })
  }
  loading.value = true
  try {
    const res = await apiGetRevenueList(formData)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (initParams.tab == 'detail') {
    if (initParams.start_time) {
      datePicker.value = [Number(initParams.start_time), Number(initParams.end_time)]
      form.value.start_time = datePicker.value[0]
      form.value.end_time = datePicker.value[1]
    }
    form.value.project = initParams.project
    form.value.city_company = initParams.city_company
    form.value.device_id = initParams.device_id
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10
    searchDeviceList(route.query.device_id || 'NIO')
  } else {
    searchDeviceList('NIO')
  }
  searchForm.value = cloneDeep(form.value)
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.revenue-detail-container {
  margin-top: 20px;
  .content-container {
    background-color: #fff;
    padding-bottom: 24px;
  }
}
</style>
