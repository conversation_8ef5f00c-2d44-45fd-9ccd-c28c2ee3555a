<template>
  <div class="revenue-overview-container">
    <el-form :model="form">
      <el-form-item :label="$t('common.pp_time_frame')">
        <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" unlink-panels :shortcuts="getYesterdayShortcuts()" :clearable="false" :disabledDate="getDisabledYesterdayDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('common.pp_device_type')">
        <el-select v-model="form.project" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_regional_company')">
        <el-select v-model="form.city_company" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in areaOptions" :key="item" :value="item" :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.pp_device_name')">
        <el-select v-model="form.device_id" clearable filterable remote :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
          <template #prefix><SearchIcon /></template>
          <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description">
            <span style="float: left">{{ item.description }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getList" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
        <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
      </el-form-item>
    </el-form>

    <div class="overview-content" v-if="!loading">
      <div class="left-container">
        <div class="info-card">
          <div class="title">
            <div class="flex-box gap_16">
              <span class="font-size-14 font-weight-bold line-height-22 color-26">{{ $t('stationManagement.pp_annualized_revenue') }}</span>
              <span class="font-size-14 line-height-22 color-a0"
                >{{ $t('stationManagement.pp_expected_annualized') }}
                <span class="font-weight-bold">{{ formatCurrencyWithUnit(list.annualized_revenue.expected_annualized_revenue) }}</span>
              </span>
            </div>
            <div class="flex-box gap_16 font-size-14 line-height-22 color-26">
              <span>{{ $t('stationManagement.pp_device_daily_revenue') }}: {{ formatEmptyData(list.annualized_revenue.device_daily_revenue) }}{{ $t('common.pp_yuan') }}</span>
              <span>{{ $t('stationManagement.pp_progress') }}: {{ formatEmptyData(list.annualized_revenue.progress) }}%</span>
              <span>{{ $t('stationManagement.pp_update_date') }}: {{ formatTimeToDay(list.annualized_revenue.update_day) }}</span>
            </div>
          </div>
          <div class="sub-title">
            <div class="right-border">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_ytd_revenue') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.annualized_revenue.revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.annualized_revenue.revenue).unit }}</span>
              </div>
            </div>
            <div class="right-border margin_l-16">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_ytd_off_peak_revenue') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.annualized_revenue.off_peak_revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.annualized_revenue.off_peak_revenue).unit }}</span>
              </div>
            </div>
            <div class="right-border margin_l-16">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_ytd_energy_revenue') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.annualized_revenue.energy_revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.annualized_revenue.energy_revenue).unit }}</span>
              </div>
            </div>
            <div class="margin_l-16">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_battery_maintenance_revenue') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.annualized_revenue.battery_maintenance_revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.annualized_revenue.battery_maintenance_revenue).unit }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="title">
            <div class="font-size-14 font-weight-bold line-height-22 color-26">{{ $t('stationManagement.pp_revenue_of_interval') }}</div>
            <div class="flex-box gap_16 font-size-14 line-height-22 color-26">
              <span>{{ $t('stationManagement.pp_device_daily_revenue') }}: {{ formatEmptyData(list.revenue.device_daily_revenue) }}{{ $t('common.pp_yuan') }}</span>
              <span>{{ $t('stationManagement.pp_progress') }}: {{ formatEmptyData(list.revenue.progress) }}%</span>
              <span>{{ $t('stationManagement.pp_update_date') }}: {{ formatTimeToDay(list.revenue.update_day) }}</span>
            </div>
          </div>
          <div class="sub-title">
            <div class="right-border">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_revenue_of_interval1') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.revenue.revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.revenue.revenue).unit }}</span>
              </div>
            </div>
            <div class="right-border margin_l-16">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_off_peak_revenue') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.revenue.off_peak_revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.revenue.off_peak_revenue).unit }}</span>
              </div>
            </div>
            <div class="right-border margin_l-16">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_energy_revenue') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.revenue.energy_revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.revenue.energy_revenue).unit }}</span>
              </div>
            </div>
            <div class="margin_l-16">
              <div class="font-size-12 line-height-18 color-26 margin_b-2">{{ $t('stationManagement.pp_battery_revenue') }}</div>
              <div class="color-a0">
                <span class="font-size-16 font-weight-bold line-height-24">{{ formatCurrency(list.revenue.battery_maintenance_revenue).value }}</span>
                <span class="font-size-12 line-height-24 margin_l-4">{{ formatCurrency(list.revenue.battery_maintenance_revenue).unit }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="bottom-line">
          <div id="multiLineChartId" class="width-full height-full" v-show="hasLineChartData"></div>
          <div v-if="!hasLineChartData" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('stationManagement.pp_revenue_trend') }}</div>
            <el-empty :description="$t('common.pp_empty')" class="width-full height-full" />
          </div>
        </div>
      </div>

      <div class="right-container">
        <div class="flex-box gap_8 color-26 margin_b-12 font-size-16 line-height-24">
          <span class="font-weight-bold">{{ $t('stationManagement.pp_revenue') }}</span>
          <span>{{ $t('stationManagement.pp_tail_device') }}</span>
        </div>
        <div class="flex-box flex_d-column gap_8" v-if="list.tail_devices && list.tail_devices.length > 0">
          <el-card class="list-card" v-for="item in list.tail_devices" @click="handleClickDevice(item)">
            <div class="flex-box flex_a_i-center flex_j_c-space-between" style="color: #434343">
              <div class="flex-box flex_a_i-center">
                <span class="font-size-16 font-weight-480">{{ item.id }}</span>
                <VerticalLine class="margin-n-8" />
                <div class="width-190">
                  <div class="margin_b-2 font-size-14 line-height-22 font-weight-450 ellipse">{{ item.description || $t('common.pp_unnamed_device') }}</div>
                  <div class="font-size-12 line-height-18 color-8c">{{ item.device_id }}</div>
                </div>
              </div>
              <span class="font-size-16 font-weight-480">
                <span>{{ formatCurrency(item.revenue).value }}</span>
                <span>{{ formatCurrency(item.revenue).unit || $t('common.pp_yuan') }}</span>
              </span>
            </div>
          </el-card>
        </div>
        <el-empty class="width-full height-240 margin_t-100" :description="$t('common.pp_empty')" v-else />
      </div>
    </div>

    <div class="loading-container" v-else>
      <div class="loader-17"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, shallowRef, h, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { projectOptions, multilineOptionMock, initYesterdayStartTime, initYesterdayEndTime, getYesterdayShortcuts, getDisabledYesterdayDate } from './constant'
import { apiGetDevices } from '~/apis/home'
import { apiGetRevenueOverview } from '~/apis/station-management'
import { ElMessage } from 'element-plus'
import { cloneDeep, debounce } from 'lodash-es'
import { formatTimeToDay, removeNullKeys, clearJson, getStartTimeOfDay, getFinalEndTime, isEmptyData } from '~/utils'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import SearchIcon from '~/assets/svg/search-icon.vue'
import VerticalLine from '~/assets/svg/vertical-line.vue'
import * as echarts from 'echarts'

const props = defineProps(['areaOptions'])

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const loading = ref(false)
const remoteLoading = ref(false)
const hasLineChartData = ref(false)
const deviceOptions = ref([] as any)
const echartsArr = ref([] as any)
const list = ref({} as any)
const datePicker = ref([initYesterdayStartTime - 3600 * 1000 * 24 * 6, initYesterdayEndTime] as any)
const form = ref({
  start_time: initYesterdayStartTime - 3600 * 1000 * 24 * 6,
  end_time: initYesterdayEndTime,
  project: '',
  city_company: '',
  device_id: ''
})
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const isCollapse = computed(() => store.state.menus.collapse)
const lineOption = cloneDeep(multilineOptionMock)

const formatEmptyData = (data: number) => {
  return isEmptyData(data) ? '-' : data.toLocaleString()
}

const formatCurrencyWithUnit = (amount: number) => {
  if(isEmptyData(amount)) return '-'
  if (amount < 10000) {
    return amount + ' ' + t('common.pp_yuan')
  } else if (amount >= 10000 && amount < *********) {
    return (amount / 10000).toFixed(2) + ' ' + t('common.pp_ten_thousand')
  } else {
    return (amount / *********).toFixed(2) + ' ' + t('common.pp_billion')
  }
}

const formatCurrency = (amount: number) => {
  if(isEmptyData(amount)) return { value: '-', unit: '' }
  if (amount < 10000) {
    return { value: amount, unit: '' }
  } else if (amount >= 10000 && amount < *********) {
    return { value: (amount / 10000).toFixed(2), unit: t('common.pp_ten_thousand') }
  } else {
    return { value: (amount / *********).toFixed(2), unit: t('common.pp_billion') }
  }
}

/**
 * @description: 跳转到对应设备的收益明细
 * @param {*} item
 * @return {*}
 */
const handleClickDevice = (item: any) => {
  window.open(`//${location.host}/station-management/revenue-list?tab=detail&start_time=${getStartTimeOfDay(datePicker.value[0])}&end_time=${getFinalEndTime(datePicker.value[1])}&device_id=${item.device_id}`)
}

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  echartsArr.value.push(myChart)
  window.addEventListener('resize', () => myChart.resize())
}
const setCharts = () => {
  if (hasLineChartData.value) echartRender('multiLineChartId', lineOption)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: 'PowerSwap2,PUS3', name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 时间变更
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  datePicker.value = [initYesterdayStartTime - 3600 * 1000 * 24 * 6, initYesterdayEndTime]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  getList()
}

/**
 * @description: 处理数据
 * @param {*} data
 * @return {*}
 */
const formatData = (data: any) => {
  if (data.revenue_trend && data.revenue_trend.length > 0) {
    hasLineChartData.value = true
    lineOption.title.text = t('stationManagement.pp_revenue_trend')
    lineOption.xAxis.data = data.revenue_trend.map((item: any) => formatTimeToDay(item.day))
    lineOption.series[0].data = data.revenue_trend.map((item: any) => item.off_peak_revenue)
    lineOption.series[1].data = data.revenue_trend.map((item: any) => item.energy_revenue)
    lineOption.series[2].data = data.revenue_trend.map((item: any) => item.battery_maintenance_revenue)
  } else {
    hasLineChartData.value = false
  }
  nextTick(() => setCharts())
}

/**
 * @description: 获取数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(form.value) as any
  formData.start_time = getStartTimeOfDay(datePicker.value[0])
  formData.end_time = getFinalEndTime(datePicker.value[1])
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: 'overview', ...removeNullKeys(formData) }
    })
  }
  loading.value = true
  try {
    const res = await apiGetRevenueOverview({...formData, top_n: 7})
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      formatData(list.value)
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!initParams.tab || initParams.tab == 'overview') {
    if (initParams.start_time) {
      datePicker.value = [Number(initParams.start_time), Number(initParams.end_time)]
      form.value.start_time = datePicker.value[0]
      form.value.end_time = datePicker.value[1]
    }
    form.value.project = initParams.project
    form.value.city_company = initParams.city_company
    form.value.device_id = initParams.device_id
    searchDeviceList(route.query.device_id || 'NIO')
  } else {
    searchDeviceList('NIO')
  }
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.revenue-overview-container {
  margin-top: 20px;
  .overview-content {
    margin-top: 20px;
    display: flex;
    .left-container {
      width: calc(100% - 360px);
      margin-right: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      .info-card {
        width: 100%;
        height: 124px;
        padding: 16px 24px;
        border-radius: 4px;
        border: 1px solid #dcf2f3;
        background-color: #fff;
        .title {
          display: flex;
          justify-content: space-between;
          padding-bottom: 12px;
          border-bottom: 1px solid #e6e7ec;
        }
        .sub-title {
          margin-top: 12px;
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          .right-border {
            border-right: 1px solid #e6e7ec;
          }
        }
      }
      .bottom-line {
        flex: 1;
        min-height: 300px;
        padding: 24px;
        border-radius: 4px;
        border: 1px solid #dcf2f3;
        background-color: #fff;
      }
    }
    .right-container {
      width: 360px;
      padding: 16px;
      border-radius: 4px;
      background: linear-gradient(180deg, #c6efef 0%, rgba(229, 249, 249, 0.5) 100%);
      :deep(.list-card) {
        cursor: pointer;
      }
      :deep(.list-card:hover) {
        border-color: #00bebe;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
      }
      :deep(.list-card .el-card__body) {
        padding: 12px 16px;
      }
    }
    :deep(.el-empty) {
      padding: 0;
      .el-empty__image {
        width: 120px;
      }
    }
  }
  .loading-container {
    height: calc(100vh - 230px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
