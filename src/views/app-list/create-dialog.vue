<template>
  <div class="create-dialog-container">
    <el-dialog v-model="dialogVisible" :title="operationType ? $t('edgeCloud.pp_create_app') : $t('edgeCloud.pp_edit_app')" @close="handleClose" width="1080px" :show-close="false" :close-on-click-modal="false" align-center>
      <!-- 基本信息 -->
      <el-card class="box-card">
        <div class="title-info">{{ $t('edgeCloud.pp_info') }}</div>
        <el-form ref="basicFormRef" :inline="true" :model="form" :rules="rules">
          <el-form-item prop="app_name">
            <template #label>
              <el-tooltip effect="dark" :content="$t('edgeCloud.pp_name_limit')" placement="top-start">
                <!-- <span>名称 <el-icon><QuestionFilled /></el-icon>：</span> -->
                <span>{{ $t('edgeCloud.pp_name') }}</span>
              </el-tooltip>
            </template>
            <el-input v-model="form.app_name" :disabled="!operationType" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')" />
          </el-form-item>
          <el-form-item :label="$t('edgeCloud.pp_project')" prop="namespace">
            <el-select v-model="form.namespace" :disabled="!operationType" @change="handleChangeNamespace" style="width: 180px" :placeholder="$t('common.pp_please_select')" filterable>
              <el-option v-for="item in projectOptions" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('edgeCloud.pp_type')" prop="kind">
            <el-select v-model="form.kind" :disabled="!operationType" @change="handleChangeType" style="width: 180px" :placeholder="$t('common.pp_please_select')" filterable>
              <el-option v-for="item in typeOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 容器组设置 -->
      <el-card class="box-card">
        <div class="title-info">{{ $t('edgeCloud.pp_container_set') }}</div>
        <div class="swap-table-container margin_l-50" style="width: 80%" v-if="!showMore">
          <el-table
            :data="form.boxInfoList"
            style="width: 100%; margin-bottom: 15px"
            :header-cell-style="{
              fontSize: '14px',
              color: '#292C33',
              cursor: 'auto'
            }"
            v-if="form.boxInfoList.length > 0"
          >
            <el-table-column prop="name" :label="$t('edgeCloud.pp_container_name')" show-overflow-tooltip />
            <el-table-column prop="image" :label="$t('edgeCloud.pp_container_image')" show-overflow-tooltip />
            <el-table-column prop="replicas" :label="$t('edgeCloud.pp_container_number')" show-overflow-tooltip />
            <el-table-column :label="`${$t('common.pp_operation')}`">
              <template #default="scope">
                <div>
                  <span @click="handleEditBox(scope.$index, scope.row)" class="cursor-pointer margin_r-15 box-hover">{{ $t('common.pp_edit') }}</span>
                  <span @click="handleDeleteBox(scope.$index, scope.row)" class="cursor-pointer margin_r-15 box-hover">{{ $t('common.pp_delete') }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-box cursor-pointer" @click="handleAddBox" v-if="form.boxInfoList.length == 0">{{ $t('edgeCloud.pp_click_add') }}</div>
        </div>

        <!-- 添加容器设置 -->
        <el-form ref="boxFormRef" :inline="true" v-if="showMore" :model="form.container" :rules="rules">
          <el-form-item :label="$t('edgeCloud.pp_number')" prop="replicas">
            <el-input v-model="form.container.replicas" :disabled="form.kind == 'DaemonSet'" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')" />
          </el-form-item>
          <el-form-item :label="$t('edgeCloud.pp_image')" prop="image">
            <el-select v-model="form.container.image" style="width: 180px" @change="handleChangeImage(boxNameRef)" :placeholder="$t('common.pp_please_select')" filterable>
              <el-option v-for="item in imageOptions" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>
          <el-form-item prop="name" ref="boxNameRef">
            <template #label>
              <el-tooltip effect="dark" :content="$t('edgeCloud.pp_name_limit')" placement="top-start">
                <!-- <span>名称 <el-icon><QuestionFilled /></el-icon>：</span> -->
                <span>{{ $t('edgeCloud.pp_box_name') }}</span>
              </el-tooltip>
            </template>
            <el-input v-model="form.container.name" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')" />
          </el-form-item>
          <div v-for="(item, index) in form.container.ports" :key="index">
            <el-form-item :label="$t('edgeCloud.pp_port_protocol')" class="margin_t-15">
              <el-select v-model="item.protocol" style="width: 180px" :placeholder="$t('common.pp_please_select')" clearable filterable>
                <el-option v-for="item in portProtocolOptions" :key="item" :value="item" :label="item" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item :label="$t('edgeCloud.pp_port_name')" class="margin_t-15">
              <el-input v-model="item.name" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')" />
            </el-form-item> -->
            <el-form-item :label="$t('edgeCloud.pp_port')" class="margin_t-15">
              <el-input v-model="item.port" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')" />
              <el-icon :size="20" color="#78909c" class="delete-icon cursor-pointer margin_l-5" @click="handleCancelPort(index)">
                <Delete />
              </el-icon>
            </el-form-item>
          </div>
          <el-form-item :label="$t('edgeCloud.pp_cpu_reservation')" class="margin_t-15" prop="cpu_request">
            <el-input v-model="form.container.cpu_request" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')">
              <template #append>m</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('edgeCloud.pp_memory_reservation')" class="margin_t-15" prop="memory_request">
            <el-input v-model="form.container.memory_request" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')">
              <template #append>Mi</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('edgeCloud.pp_memory_limit')" class="margin_t-15" prop="memory_limit">
            <el-input v-model="form.container.memory_limit" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')">
              <template #append>Mi</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('edgeCloud.pp_cpu_limit')" class="margin_t-15" prop="cpu_limit">
            <el-input v-model="form.container.cpu_limit" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')">
              <template #append>m</template>
            </el-input>
          </el-form-item>
          <el-form-item class="margin_t-15">
            <el-button link class="confirm-link-button" @click="handleAddPort">{{ $t('edgeCloud.pp_add_port') }}</el-button>
          </el-form-item>
          <el-form-item class="margin_t-15" v-if="!operationType && form.config_map.data">
            <el-button link class="confirm-link-button" @click="handleViewFiles">{{ $t('edgeCloud.pp_files') }}</el-button>
          </el-form-item>
          <el-form-item class="margin_t-15">
            <el-button link class="confirm-link-button" @click="handleCancelAddBox">{{ $t('common.pp_cancel') }}</el-button>
          </el-form-item>
          <el-form-item class="margin_t-15">
            <el-button link class="confirm-link-button" @click="handleConfirmAddBox(boxFormRef)">{{ $t('common.pp_confirm') }}</el-button>
          </el-form-item>
          <el-upload v-model:file-list="fileList" drag ref="uploadRef" class="margin_t-15" action="#" :auto-upload="false">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">{{ $t('edgeCloud.pp_upload') }}</div>
          </el-upload>
        </el-form>
      </el-card>

      <!-- 标签设置 -->
      <el-card class="box-card">
        <div class="title-info">{{ $t('edgeCloud.pp_tag') }}</div>
        <el-form :inline="true" ref="nodeFormRef" :model="form">
          <div v-for="(item, index) in form!.node_labels" class="node-box">
            <el-form-item
              :label="$t('edgeCloud.pp_key')"
              :prop="'node_labels.' + index + '.key'"
              :rules="{
                required: true,
                message: t('edgeCloud.pp_input_key'),
                trigger: 'blur'
              }"
            >
              <el-input v-model="item.key" style="width: 180px" clearable :placeholder="$t('common.pp_please_input')" />
            </el-form-item>
            <el-form-item
              :label="$t('edgeCloud.pp_value')"
              :prop="'node_labels.' + index + '.value'"
              :rules="{
                required: true,
                message: t('edgeCloud.pp_input_value'),
                trigger: 'blur'
              }"
            >
              <el-input v-model="item.value" style="width: 240px" clearable :placeholder="$t('common.pp_please_input')" />
            </el-form-item>
            <el-form-item>
              <el-icon :size="20" color="#78909c" class="delete-icon cursor-pointer margin_l-5" @click="handleDeleteNode(index)">
                <Delete />
              </el-icon>
            </el-form-item>
          </div>
          <div class="margin_t-15 margin_l-82">
            <el-popover trigger="click" placement="right" :width="550">
              <template #reference>
                <el-button class="cursor-pointer margin_r-15 round-button" round>{{ $t('edgeCloud.pp_specify_tag') }}</el-button>
              </template>
              <el-table ref="multipleTableRef" :data="nodeList" :highlight-current-row="highlightRow" @current-change="handleSelectionChange">
                <!-- <el-table-column type="selection" width="50" /> -->
                <el-table-column width="240" property="name" :label="$t('edgeCloud.pp_name')" show-overflow-tooltip />
                <el-table-column width="100" property="status" :label="$t('edgeCloud.pp_status')" show-overflow-tooltip />
                <el-table-column width="80" property="cpu" label="CPU" show-overflow-tooltip>
                  <template #default="scoped">
                    <span>{{ Number.isInteger(scoped.row.cpu_utilization * 100) ? scoped.row.cpu_utilization * 100 + '%' : (scoped.row.cpu_utilization * 100).toFixed(2) + '%' }}</span>
                  </template>
                </el-table-column>
                <el-table-column width="80" property="memory" :label="$t('edgeCloud.pp_memory')" show-overflow-tooltip>
                  <template #default="scoped">
                    <span>{{ Number.isInteger(scoped.row.memory_utilization * 100) ? scoped.row.memory_utilization * 100 + '%' : (scoped.row.memory_utilization * 100).toFixed(2) + '%' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-popover>
            <el-button @click="handleAddNode" class="cursor-pointer round-button" round>{{ $t('edgeCloud.pp_add_tag') }}</el-button>
          </div>
        </el-form>
      </el-card>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">{{ $t('common.pp_cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmitForm(basicFormRef, nodeFormRef)">{{ $t('common.pp_confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="fileDialogVisible" :title="`${$t('edgeCloud.pp_configuration_file')}`" :close-on-click-modal="false" align-center>
      <div v-for="item in fileEditList">
        <span>{{ item.name }}</span>
        <!-- <span @click="handleViewImage(item.value)">点击查看图片</span> -->
        <br><br>
        <div v-html="item.value" class="log-container margin_b-10"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {ref, reactive, toRefs} from 'vue'
import {Delete, UploadFilled} from '@element-plus/icons-vue'
import {ElMessage, ElMessageBox, ElTable} from 'element-plus'
import type {FormInstance, FormRules, UploadInstance} from 'element-plus'
import {useI18n} from 'vue-i18n'
import _ from 'lodash'

const props = defineProps({
  operationType: Number,
  dialogVisible: Boolean,
  imageOptions: Array<string>,
  nodeList: Array,
  createForm: Object,
  projectOptions: Array<string>
})
const emits = defineEmits(['cancel', 'submit', 'getImage'])
const {dialogVisible, imageOptions, nodeList, createForm, projectOptions} = toRefs(props)
const {t} = useI18n()
const basicFormRef = ref<FormInstance>()
const nodeFormRef = ref<FormInstance>()
const boxFormRef = ref<FormInstance>()
const boxNameRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const fileDialogVisible = ref(false)
const highlightRow = ref(true)
const showMore = ref(false)
const fileEditList = ref([] as any)
const fileList = ref([])
const fileListClone = ref([])
const typeOptions = ref([
  {
    label: 'deployment',
    value: 'deployment'
  }
  // {
  //   label: 'DaemonSet',
  //   value: 'DaemonSet'
  // }
])
const portProtocolOptions = ref(['TCP', 'UDP', 'SCTP'])
const form = createForm as any

const validateBoxName = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error(t('edgeCloud.pp_input_name')))
  } else if (/^[a-zA-Z][a-zA-Z0-9-]*$/.test(value) && value.length <= 30) {
    callback()
  } else {
    callback(new Error(t('edgeCloud.pp_name_limit')))
  }
}
const validateBoxNum = (rule: any, value: any, callback: any) => {
  if (value === '' || !Number.isInteger(Number(value))) {
    callback(t('edgeCloud.pp_input_number'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules>({
  app_name: [{validator: validateBoxName, required: true, trigger: 'blur'}],
  namespace: {required: true, message: t('edgeCloud.pp_select_project'), trigger: 'change'},
  kind: {required: true, message: t('edgeCloud.pp_select_type'), trigger: 'change'},
  replicas: [{validator: validateBoxNum, required: true, trigger: 'blur'}],
  image: {required: true, message: t('edgeCloud.pp_select_image'), trigger: 'change'},
  name: [{validator: validateBoxName, required: true, trigger: 'blur'}],
  cpu_request: [{validator: validateBoxNum, required: true, trigger: 'blur'}],
  memory_request: [{validator: validateBoxNum, required: true, trigger: 'blur'}],
  memory_limit: [{validator: validateBoxNum, required: true, trigger: 'blur'}],
  cpu_limit: [{validator: validateBoxNum, required: true, trigger: 'blur'}]
})

/**
 * @description: 容器名称和容器镜像同名
 * @return {*}
 */
const handleChangeImage = (formEl: any) => {
  // if (form.value.container.image) formEl.resetField()
  // form.value.container.name = form.value.container.image
}

/**
 * @description: 改变容器类型
 * @return {*}
 */
const handleChangeType = () => {
  form.value.container.replicas = form.value.kind === 'DaemonSet' ? 1 : form.value.container.replicas
}

/**
 * @description: 点击添加容器
 * @return {*}
 */
const handleAddBox = () => {
  showMore.value = !showMore.value
  // 重新赋值，解除编辑row的浅拷贝
  form.value.container = {
    replicas: 1,
    name: '',
    image: '',
    ports: [
      {
        protocol: '',
        port: '' as number | string
      }
    ],
    cpu_request: 100,
    memory_request: 100,
    cpu_limit: 200,
    memory_limit: 200
  }
  // 清除文件
  fileList.value = []
}

/**
 * @description: 取消添加容器
 * @return {*}
 */
const handleCancelAddBox = () => {
  showMore.value = !showMore.value
  fileList.value = fileListClone.value
}

/**
 * @description: 确认添加容器
 * @return {*}
 */
const handleConfirmAddBox = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid: any) => {
    if (valid) {
      let obj = {} as any
      if (form.value.container.ports.length > 0) obj = form.value.container.ports[form.value.container.ports.length - 1]
      if (form.value.container.ports.length > 0 && ((!obj.protocol && obj.port) || (obj.protocol && !obj.port))) {
        ElMessage.warning(t('edgeCloud.pp_fill_port'))
      } else if (form.value.container.ports.length > 0 && !obj.protocol && !obj.port) {
        form.value.container.ports.pop()
        showMore.value = !showMore.value
        form.value.boxInfoList = []
        form.value.boxInfoList.push(form.value.container)
      } else {
        showMore.value = !showMore.value
        form.value.boxInfoList = []
        form.value.boxInfoList.push(form.value.container)
      }
      if (form.value.container.ports.length > 0) {
        form.value.container.ports.forEach((item: any) => (item.port = Number(item.port)))
      }
    } else {
      console.log('error submit!')
    }
  })
}

/**
 * @description: 编辑容器
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleEditBox = (index: number, row: any) => {
  form.value.container = _.cloneDeep(row)
  fileListClone.value = _.cloneDeep(fileList.value)
  showMore.value = true
}

/**
 * @description: 删除容器
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleDeleteBox = (index: number, row: any) => {
  ElMessageBox.confirm(`${t('edgeCloud.pp_delete_container')} ${row.name}？`, {
    confirmButtonText: t('common.pp_confirm'),
    cancelButtonText: t('common.pp_cancel'),
    type: 'warning'
  }).then(() => {
    form.value.boxInfoList.splice(index, 1)
  })
}

/**
 * @description: 添加端口
 * @return {*}
 */
const handleAddPort = () => {
  if (form.value.container.ports.length == 0) {
    form.value.container.ports.push({
      protocol: '',
      port: '' as number | string
    })
  } else {
    const obj = form.value.container.ports[form.value.container.ports.length - 1]
    if (obj.protocol && obj.port) {
      form.value.container.ports.push({
        protocol: '',
        port: '' as number | string
      })
    } else {
      ElMessage.warning(t('edgeCloud.pp_fill_port'))
    }
  }
}

/**
 * @description: 删除端口
 * @param {*} index
 * @return {*}
 */
const handleCancelPort = (index: number) => {
  form.value.container.ports.splice(index, 1)
}

/**
 * @description: 删除标签
 * @param {*} index
 * @return {*}
 */
const handleDeleteNode = (index: number) => {
  if (form.value.node_labels.length > 1) form.value.node_labels.splice(index, 1)
}

/**
 * @description: 新增标签
 * @return {*}
 */
const handleAddNode = () => {
  const node = form.value.node_labels[form.value.node_labels.length - 1]
  if (node.key && node.value) {
    form.value.node_labels.push({
      key: '',
      value: ''
    })
  } else {
    ElMessage.warning(t('edgeCloud.pp_fill_key'))
  }
}

/**
 * @description: 改变项目重新获取镜像列表
 * @param {*} e
 * @return {*}
 */
const handleChangeNamespace = (e: any) => {
  if (form.value.boxInfoList.length > 0) form.value.boxInfoList = []
  form.value.container.image = ''
  emits('getImage', e)
}

/**
 * @description: 指定标签
 * @param {*} val
 * @return {*}
 */
const handleSelectionChange = (val: any) => {
  console.log(val, '***')
  if (val) {
    form.value.node_labels = []
    form.value.node_labels.push({
      key: 'kubernetes.io/hostname',
      value: val.name
    })
  }
}

/**
 * @description: 取消创建APP
 * @return {*}
 */
const handleCancel = () => {
  showMore.value = false
  form.value.boxInfoList = []
  emits('cancel')
}

/**
 * @description: 提交表单
 * @param {*} formEl
 * @param {*} nodeFormEl
 * @return {*}
 */
const handleSubmitForm = (formEl: FormInstance | undefined, nodeFormEl: FormInstance | undefined) => {
  if (!formEl || !nodeFormEl) return
  Promise.all([new Promise((resolve) => formEl.validate((v) => resolve(v))), new Promise((resolve) => nodeFormEl.validate((v) => resolve(v)))]).then(([v1, v2]) => {
    if (v1 && v2 && !showMore.value && form.value.boxInfoList.length > 0) {
      form.value.files = fileList.value.map((item: any) => item.raw)
      form.value.container.replicas = Number(form.value.container.replicas)
      form.value.container.cpu_limit = Number(form.value.container.cpu_limit)
      form.value.container.cpu_request = Number(form.value.container.cpu_request)
      form.value.container.memory_limit = Number(form.value.container.memory_limit)
      form.value.container.memory_request = Number(form.value.container.memory_request)
      emits('submit', form.value)
    } else if (form.value.boxInfoList.length === 0 || showMore.value) {
      ElMessage.warning(t('edgeCloud.pp_add_container'))
    }
  })
}

/**
 * @description: 查看配置文件
 * @return {*}
 */
const handleViewFiles = () => {
  fileEditList.value = []
  const obj = form.value.config_map.data
  if(obj) {
    Object.keys(obj).forEach((key: any) => {
      // const suffix = key.split('.')[key.split('.').length - 1]
      fileEditList.value.push({
        name: key,
        // value: ['jpg', 'png', 'jpeg'].includes(suffix) ? window.URL.createObjectURL(new Blob([obj[key]])) : obj[key].replace(/\n/g, '<br/>')
        value: obj[key].replace(/\n/g, '<br/>')
      })
    })
  }
  console.log(fileEditList.value, '000')
  fileDialogVisible.value = true
}

// const handleViewImage = (url: any) => {
//   window.open(url)
// }

/**
 * @description: 关闭弹窗清空
 * @return {*}
 */
const handleClose = () => {
  fileList.value = []
  basicFormRef.value?.resetFields()
  nodeFormRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.create-dialog-container {
  .title-info {
    width: 70px;
    color: #22252b;
    font-family: 'Noto Sans';
    font-weight: bold;
    margin-right: 20px;
    white-space: nowrap;
  }
  .node-box {
    // display: flex;
    &:not(:first-child) {
      margin-top: 10px;
    }
  }
  .box-hover,
  .round-button {
    color: #22252b;
    font-family: 'Noto Sans';
    &:hover {
      color: #00bebe;
    }
  }
  .round-button {
    background-color: #eff4f9;
  }
  .delete-icon:hover {
    color: #292c33;
  }
  .add-box {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    border: 1px dashed #ccd3db;
  }
  .add-box::before {
    color: var(--el-color-danger);
    content: '*';
    margin-right: 4px;
  }
  .el-upload__text,
  ::v-deep(.el-upload-list__item-file-name) {
    color: #292c33;
    font-weight: normal;
    font-family: 'Noto Sans';
  }
  .confirm-link-button {
    color: #00bebe;
  }
  .log-container {
    background: black;
    color: #b0bec5;
    padding: 10px;
  }
  ::v-deep(.el-dialog) {
    background-color: #eff4f9;
  }
  ::v-deep(.el-dialog__body) {
    padding: 10px 20px;
  }
  ::v-deep(.el-dialog__footer) {
    padding-top: 0px;
  }
  ::v-deep(.el-card) {
    margin-bottom: 15px;
    border: 1px solid #ccd3db;
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 0 6px #999;
      transition: all 0.5s ease-out;
    }
  }
  ::v-deep(.el-card__body) {
    padding: 30px 20px;
    display: flex;
    align-items: center;
  }
  ::v-deep(.el-form-item) {
    margin-bottom: 0;
  }
  ::v-deep(.el-form-item__label) {
    width: 82px;
    font-size: 14px;
    font-family: 'Noto Sans';
    color: #292c33;
    word-break: keep-all;
  }
  ::v-deep(.el-form-item__error) {
    width: 260px;
  }
  ::v-deep(.el-upload-dragger) {
    padding: 10px 10px 20px;
  }
}
</style>
