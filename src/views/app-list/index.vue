<template>
  <div class="app-list-container">
    <!-- header -->
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('edgeCloud.pp_cloud') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t('edgeCloud.pp_app_list') }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">
          {{ $t('edgeCloud.pp_app_list') }}
        </div>
      </div>
      <div class="header-right">
        <el-radio-group v-model="searchForm.device_type" @change="handleChangeType">
          <el-radio :label="item.value" v-for="item in deviceTypeOptions">{{ $t(item.label) }}</el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- body -->
    <div class="swap-page-container">
      <div class="search-container">
        <span class="search-item-label">
          {{ $t('edgeCloud.pp_app_name') }}
        </span>
        <el-select v-model="searchForm.app_name" :placeholder="`${$t('common.pp_please_select')}`" clearable filterable style="width: 50%; margin-right: 20px">
          <el-option v-for="item in appOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <span class="search-item-label white-space-nowrap">
          {{ $t('edgeCloud.pp_update_time') }}
        </span>
        <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :shortcuts="shortcuts" :disabledDate="getDisabledDate" style="width: 100%; margin-right: 20px" />
        <div class="search-button">
          <el-button @click="handleReset">{{ $t('common.pp_reset') }}</el-button>
          <el-button @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
          <el-button @click="handleCreate" class="create-button">{{ $t('common.pp_create') }}</el-button>
        </div>
      </div>
      <div class="swap-table-container">
        <el-table
          :data="tableList"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
        >
          <el-table-column :label="`${$t('edgeCloud.pp_app_name')}`" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <div class="pointer-column">
                <span class="point-span" @click="handleJump(scope.$index, scope.row)">
                  {{ scope.row.app_name }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="READY" min-width="80" show-overflow-tooltip>
            <template #default="scope">
              <span :class="scope.row.available_replicas == scope.row.replicas ? 'ready-status' : 'unready-status'">{{ scope.row.available_replicas + '/' + scope.row.replicas }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="`${$t('edgeCloud.pp_project')}`" prop="namespace" min-width="100" show-overflow-tooltip />
          <el-table-column :label="`${$t('edgeCloud.pp_create_time')}`" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.create_ts) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="`${$t('edgeCloud.pp_update_time')}`" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.update_ts) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="`${$t('edgeCloud.pp_creator')}`" prop="creator" min-width="100" show-overflow-tooltip />
          <el-table-column :label="`${$t('common.pp_operation')}`" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <div class="pointer-column">
                <span @click="handleEditApp(scope.$index, scope.row)" class="point-span margin_r-15">{{ $t('common.pp_edit') }}</span>
                <span @click="handleDeleteApp(scope.$index, scope.row)" class="point-span margin_r-15">{{ $t('common.pp_delete') }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <Page v-if="(tableList && tableList.length > 0) || !tableList" :page="pages" @change="handlePageChange" />
        </div>
      </div>
      <CreateDialog :dialogVisible="dialogVisible" :project="searchForm.device_type" :nodeList="nodeList" :createForm="createForm" :projectOptions="projectOptions" :imageOptions="imageOptions" :operationType="operationType" @cancel="handleCancel" @submit="handleSubmit" @getImage="getImage" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, onBeforeMount, onBeforeUnmount, watch} from 'vue'
import CreateDialog from './create-dialog.vue'
import {page} from '~/constvars/page'
import {removeNullProp, getShortcuts, getDisabledDate, formatTime} from '~/utils'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useI18n} from 'vue-i18n'
import {apiGetAppOptions, apiGetAppList, apiDeleteApp, apiGetNamespaceOptions, apiPostCreateApp, apiPostCreateAppFiles, apiGetAppInfoList, apiGetSpecifyNodeList, apiGetImageList} from '~/apis/edge-cloud'
import _ from 'lodash'

const route = useRoute()
const router = useRouter()
const {t} = useI18n()
const getListTimeout = ref()
const dialogVisible = ref(false)
const loading = ref(false)
const operationType = ref(0) // 0 编辑； 1 创建
const pages = ref(_.cloneDeep(page))
const shortcuts = ref(getShortcuts())
const datePicker = ref([] as any)
const projectOptions = ref([])
const imageOptions = ref([])
const appOptions = ref([])
const nodeList = ref([])
const deviceTypeOptions = ref([
  {
    label: 'menu.pp_swap_station2',
    value: 'PowerSwap2'
  },
  {
    label: 'menu.pp_swap_station3',
    value: 'PUS3'
  }
])
const searchForm = ref({
  app_name: '',
  status: '',
  start_time: '' as number | string,
  end_time: '' as number | string,
  device_type: 'PUS3',
  page: 1,
  size: 10
})
const form = ref({
  app_name: '',
  status: '',
  start_time: '' as number | string,
  end_time: '' as number | string,
  device_type: 'PUS3',
  page: 1,
  size: 10
})
const createForm = ref({
  files: [],
  op_type: 'create',
  app_name: '',
  namespace: '',
  kind: '',
  boxInfoList: [] as any,
  container: {
    replicas: 1,
    name: '',
    ports: [
      {
        protocol: '',
        port: '' as number | string
      }
    ],
    cpu_request: 100,
    memory_request: 100,
    cpu_limit: 200,
    memory_limit: 200,
    image: ''
  },
  node_labels: [
    {
      key: '',
      value: ''
    }
  ]
})
const tableList = ref([])
const tempForm = _.cloneDeep(createForm.value)

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  searchForm.value.app_name = ''
  searchForm.value.status = ''
  searchForm.value.start_time = ''
  searchForm.value.end_time = ''
  datePicker.value = []
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  pages.value.size = 10
  form.value = {...searchForm.value}
  getDeviceList(true, 2)
}

/**
 * @description: 搜索
 * @param {*} updateRoute
 * @return {*}
 */
const getDeviceList = async (updateRoute = true, status: any) => {
  if (loading.value) return
  clearTimeout(getListTimeout.value)
  let formData = {} as any
  if (status === 2) {
    formData = {...form.value}
  } else {
    formData = {...searchForm.value}
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  if (updateRoute) {
    router.push({
      path: `/cloud/app-list`,
      query: {...removeNullProp(formData)}
    })
  }
  loading.value = true
  try {
    const res = await apiGetAppList(searchForm.value.device_type, formData)
    tableList.value = res.data
    pages.value.total = res.total
    getListTimeout.value = setTimeout(() => {
      getDeviceList(false, 2)
    }, 5000)
  } catch (error) {}
  loading.value = false
}

/**
 * @description: 点击跳转至详情页
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleJump = (index: number, row: any) => {
  sessionStorage.setItem('app-list', JSON.stringify({...removeNullProp(searchForm.value)}))
  router.push({
    path: `/cloud/app-list/app-detail/${row.app_name}`,
    query: {device_type: searchForm.value.device_type}
  })
}

/**
 * @description: 改变时间筛选项
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  if (value) {
    searchForm.value.start_time = value[0].valueOf()
    searchForm.value.end_time = value[1].valueOf()
  } else {
    searchForm.value.start_time = ''
    searchForm.value.end_time = ''
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getDeviceList(true, 2)
}

/**
 * @description: 切换换电站2/3
 * @return {*}
 */
const handleChangeType = () => {
  handleReset()
  getAppOptions()
  getNamespaceOptions()
}

/**
 * @description: 编辑APP
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleEditApp = async (index: number, row: any) => {
  operationType.value = 0
  const params = {
    namespace: row.namespace,
    kind: 'deployment'
  }
  const res = await apiGetAppInfoList(searchForm.value.device_type, row.app_name, params)
  createForm.value = res.data
  createForm.value.boxInfoList = []
  createForm.value.boxInfoList.push(res.data.container)
  createForm.value.op_type = 'update'
  dialogVisible.value = true
}

/**
 * @description: 删除APP
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleDeleteApp = (index: number, row: any) => {
  ElMessageBox.confirm(`${t('edgeCloud.pp_delete_app')} ${row.app_name}？`, {
    confirmButtonText: t('common.pp_confirm'),
    cancelButtonText: t('common.pp_cancel'),
    type: 'warning'
  }).then(async () => {
    const params = {
      namespace: row.namespace,
      kind: 'deployment'
    }
    try {
      const res = await apiDeleteApp(searchForm.value.device_type, row.app_name, params)
      if (!res.err_code) ElMessage.success(`${t('common.pp_delete')} ${row.app_name}`)
    } catch (error) {}
    getDeviceList(false, 2)
  })
}

/**
 * @description: 创建APP
 * @return {*}
 */
const handleCreate = () => {
  operationType.value = 1
  createForm.value = _.cloneDeep(tempForm)
  createForm.value.kind = 'deployment'
  createForm.value.namespace = projectOptions.value[0]
  getSpecifyNodeList()
  dialogVisible.value = true
}

/**
 * @description: 取消创建APP
 * @return {*}
 */
const handleCancel = () => {
  dialogVisible.value = false
}

/**
 * @description: 成功创建APP
 * @return {*}
 */
const handleSubmit = async (e: any) => {
  const files = e.files
  delete e.files
  let fileResult = Promise.resolve()
  const res1 = await apiPostCreateApp(searchForm.value.device_type, e)
  const jsonResult = apiPostCreateApp(searchForm.value.device_type, e)
  // if (files.length > 0) {
    let fd = new FormData()
    files.map((item: any, index: any) => {
      fd.append(index, item)
    })
    fileResult = apiPostCreateAppFiles(searchForm.value.device_type, fd, res1.message)
  // }
  Promise.all([jsonResult, fileResult]).then(() => {
    dialogVisible.value = false
    if (operationType.value === 0) {
      ElMessage.success(t('orderList.pp_edit_success'))
    } else {
      ElMessage.success(t('orderList.pp_create_success'))
    }
  })
  getDeviceList(false, 2)
}

/**
 * @description: 获取APP名称筛选项
 * @return {*}
 */
const getAppOptions = async () => {
  const res = await apiGetAppOptions(searchForm.value.device_type)
  appOptions.value = res.app_name_list
}

/**
 * @description: 获取项目筛选项
 * @return {*}
 */
const getNamespaceOptions = async () => {
  const res = await apiGetNamespaceOptions(searchForm.value.device_type)
  projectOptions.value = res.data
  const params = {namespace: res.data[0]}
  const r = await apiGetImageList(searchForm.value.device_type, params)
  imageOptions.value = r.image_list
}

/**
 * @description: 获取镜像
 * @param {*} e
 * @return {*}
 */
const getImage = async (e: any) => {
  const params = {namespace: e}
  const r = await apiGetImageList(searchForm.value.device_type, params)
  imageOptions.value = r.image_list
}

/**
 * @description: 获取指定节点列表
 * @return {*}
 */
const getSpecifyNodeList = async () => {
  const res = await apiGetSpecifyNodeList(searchForm.value.device_type)
  nodeList.value = res.node_list
}

/**
 * @description: 初始化筛选条件
 * @return {*}
 */
const initWeb = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
        searchForm.value.start_time = Number(initParams.start_time)
        searchForm.value.end_time = Number(initParams.end_time)
        datePicker.value[0] = Number(searchForm.value.start_time)
        datePicker.value[1] = Number(searchForm.value.end_time)
      }
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      searchForm.value.app_name = !!initParams.app_name ? initParams.app_name : ''
      searchForm.value.status = !!initParams.status ? initParams.status : ''
      searchForm.value.device_type = !!initParams.device_type ? initParams.device_type : 'PUS3'
      form.value = {...searchForm.value}
      getDeviceList(false, 2)
    }
  },
  {immediate: true}
)

/**
 * @description: 组件销毁卸载轮询，定时器清空
 * @return {*}
 */
onBeforeUnmount(() => {
  clearTimeout(getListTimeout.value)
})

onBeforeMount(() => {
  initWeb()
  getAppOptions()
  getNamespaceOptions()
})
</script>

<style lang="scss" scoped>
.app-list-container {
  .edit-text {
    color: #00bebe;
    &:hover {
      cursor: pointer;
    }
  }
  .children-page {
    width: auto;
    margin: 8px 50px 10px;
    ::v-deep(.el-pagination) {
      margin: 0;
    }
  }
  .inner-container {
    padding: 20px 50px;
  }
  .create-button {
    background-color: #42a5f5 !important;
  }
  .ready-status {
    color: #00bebe;
  }
  .unready-status {
    color: #ff6d00;
  }
}
</style>
