<template>
  <div class="health-detail-container">
    <el-form :model="form" inline class="margin_b-2">
      <el-form-item label="时间范围">
        <el-date-picker v-model="datePicker" type="daterange" value-format="x" unlink-panels :shortcuts="shortcuts" :range-separator="$t('common.pp_to')" :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item label="设备类型">
        <el-select v-model="form.project" class="width-full">
          <el-option v-for="item in deviceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备名称">
        <el-select v-model="form.device_id" filterable remote clearable :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
          <el-option v-for="item in deviceNameOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
            <span style="float: left">{{ item.description }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工单状态">
        <el-select v-model="form.worksheet_status" clearable filterable multiple collapse-tags collapse-tags-tooltip :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in worksheetStatusOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading1 || loading2">筛选</el-button>
        <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">重置</el-button>
      </el-form-item>
    </el-form>

    <div v-if="loading1" class="flex-box flex_j_c-center flex_a_i-center width-full height-294">
      <div class="loader-17"></div>
    </div>

    <div class="echart-container" v-else>
      <div class="echart-card" style="padding-right: 0">
        <div id="pieCharts" class="width-full height-240" v-show="hasPieChart"></div>
        <div v-if="!hasPieChart" class="width-full height-240">
          <div class="font-size-14 font-weight-bold color-26">工单完成情况</div>
          <el-empty description="暂无数据" class="width-full height-full" />
        </div>
      </div>
      <div class="echart-card">
        <div id="lineCharts" class="width-full height-240" v-show="hasLineChart"></div>
        <div v-if="!hasLineChart" class="width-full height-240">
          <div class="font-size-14 font-weight-bold color-26">下发工单数统计</div>
          <el-empty description="暂无数据" class="width-full height-full" />
        </div>
      </div>
      <div class="echart-card">
        <div class="font-size-14 font-weight-bold color-26 margin_b-20">工单关闭率</div>
        <div class="flex-box flex_a_i-center margin_b-16">
          <div class="label-text">传感器</div>
          <div class="value-text" v-if="chartList.worksheet_close.sensor || chartList.worksheet_close.sensor === 0">{{ (chartList.worksheet_close.sensor * 100).toFixed(2) }}%</div>
          <div v-else>-</div>
        </div>
        <div class="flex-box flex_a_i-center margin_b-16">
          <div class="label-text">伺服</div>
          <div class="value-text" v-if="chartList.worksheet_close.servo || chartList.worksheet_close.servo === 0">{{ (chartList.worksheet_close.servo * 100).toFixed(2) }}%</div>
          <div v-else>-</div>
        </div>
        <div class="flex-box flex_a_i-center">
          <div class="label-text">充电模块</div>
          <div class="value-text" v-if="chartList.worksheet_close.charger || chartList.worksheet_close.charger === 0">{{ (chartList.worksheet_close.charger * 100).toFixed(2) }}%</div>
          <div v-else>-</div>
        </div>
      </div>
    </div>

    <el-table :data="list" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" v-loading="loading2">
      <el-table-column prop="update_time" label="更新日期" min-width="110" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ formatTimeToDay(row.update_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_id" label="设备ID" min-width="200">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.device_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="description" label="设备名称" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.description || '未命名设备' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="worksheet_status" label="工单状态" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.worksheet_status || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="worksheet_id" label="工单ID" min-width="200">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.worksheet_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="worksheet_name" label="工单名称" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.worksheet_name || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="city_company" label="城市公司" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.city_company || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="工单创建时间" min-width="170" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ formatTime(row.create_time) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="assignee" label="当前处理人" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.assignee || '-' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="common-pagination">
      <Page :page="pages" @change="handlePageChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h, onBeforeMount, shallowRef, watch, computed, nextTick } from 'vue'
import { shortcuts, getDisabledDate, getStartTimeOfDay, getFinalEndTime, removeNullKeys, formatTimeToDay, clearJson, formatTime } from '~/utils'
import { apiGetWorksheet, apiGetWorksheetStatistics } from '~/apis/device-health'
import { multilineOption, pieOption } from './statusMap'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { miniPage } from '~/constvars/page'
import { cloneDeep, debounce } from 'lodash-es'
import { apiGetDevices, apiGetMapByKey } from '~/apis/home'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { deviceOptions } from './statusMap'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import * as echarts from 'echarts'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const loading1 = ref(false)
const loading2 = ref(false)
const remoteLoading = ref(false)
const hasPieChart = ref(false)
const hasLineChart = ref(false)
const chartList = ref({} as any)
const list = ref([] as any)
const deviceNameOptions = ref([] as any)
const echartsArr = ref([] as any)
const worksheetStatusOptions = ref([] as any)
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2] as any)
const form = ref({
  device_id: '',
  worksheet_status: [],
  project: 'PUS3'
})
const searchForm = ref({} as any)
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const pages = ref(cloneDeep(miniPage))
const orderNumOption = cloneDeep(multilineOption)
const orderStatusOption = cloneDeep(pieOption)
const isCollapse = computed(() => store.state.menus.collapse)

orderNumOption.series.forEach((item: any) => {
  item.symbol = 'none'
  item.areaStyle = {
    color: {
      type: 'linear',
      x: 0, // 渐变起始位置的横坐标，0 为最左侧，0.5 为中间，1 为最右侧
      y: 0, // 渐变起始位置的纵坐标，0 为最顶部，0.5 为中间，1 为最底部
      x2: 0, // 渐变结束位置的横坐标
      y2: 1, // 渐变结束位置的纵坐标，1 为最底部
      colorStops: [
        {
          offset: 0,
          color: 'rgba(0, 190, 190, 0.1)'
        },
        {
          offset: 1,
          color: 'rgba(0, 190, 190, 0)'
        }
      ],
      global: false
    }
  }
})

const formatDate = (date: any) => {
  const d = new Date(date)
  return d.getMonth() + 1 + '-' + d.getDate()
}

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  echartsArr.value.push(myChart)
  myChart.clear()
  chartDom.setAttribute('_echarts_instance_', '')
  option && myChart.setOption(option, true)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  orderNumOption.title.text = '下发工单数统计'
  orderNumOption.yAxis.min = (value: any) => (value.min > 2 ? value.min - 2 : 0)
  orderNumOption.yAxis.max = (value: any) => value.max + 1
  if (chartList.value.worksheet_assign.sensor) {
    orderNumOption.series[0].name = '传感器'
    orderNumOption.series[0].data = chartList.value.worksheet_assign.sensor.map((item: any) => item.count)
    orderNumOption.xAxis.data = chartList.value.worksheet_assign.sensor.map((item: any) => formatDate(item.day))
  }
  if (chartList.value.worksheet_assign.servo) {
    orderNumOption.series[1].name = '伺服'
    orderNumOption.series[1].data = chartList.value.worksheet_assign.servo.map((item: any) => item.count)
  }
  if (chartList.value.worksheet_assign.charger) {
    orderNumOption.series[2].name = '充电模块'
    orderNumOption.series[2].data = chartList.value.worksheet_assign.charger.map((item: any) => item.count)
  }

  if (hasPieChart.value) {
    orderStatusOption.series.data = chartList.value.worksheet_complete_status.map((item: any) => {
      return { name: item.worksheet_status, value: item.count }
    })
    echartRender('pieCharts', orderStatusOption)
  }

  if (hasLineChart.value) echartRender('lineCharts', orderNumOption)
}

watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
    }, 300)
  }
)

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: form.value.project, name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceNameOptions.value = res.data
  }
}, 500)

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = cloneDeep(form.value)
  getList()
  getChartList()
}

/**
 * @description: 重置
 * @return {*}
 */
const resetSelect = () => {
  clearJson(form.value)
  form.value.project = 'PUS3'
  datePicker.value = [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2]
  handleSearch()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 获取表格数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.worksheet_status = formData.worksheet_status.join(',')
  formData.start_time = getStartTimeOfDay(datePicker.value[0])
  formData.end_time = getFinalEndTime(datePicker.value[1])
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: 'order', ...removeNullKeys(formData) }
    })
  }
  loading2.value = true
  try {
    const res = await apiGetWorksheet(form.value.project, formData)
    loading2.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
  } catch (error: any) {
    loading2.value = false
  }
}

/**
 * @description: 获取图像数据
 * @return {*}
 */
const getChartList = async () => {
  let formData = cloneDeep(searchForm.value) as any
  formData.worksheet_status = formData.worksheet_status.join(',')
  formData.start_time = getStartTimeOfDay(datePicker.value[0])
  formData.end_time = getFinalEndTime(datePicker.value[1])
  loading1.value = true
  try {
    const res = await apiGetWorksheetStatistics(form.value.project, formData)
    loading1.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      chartList.value = res.data
      hasPieChart.value = res.data.worksheet_complete_status && res.data.worksheet_complete_status.length > 0
      hasLineChart.value = res.data.worksheet_assign.sensor && res.data.worksheet_assign.sensor.length > 0
      nextTick(() => setCharts())
    }
  } catch (error) {
    loading1.value = false
  }
}

/**
 * @description: 获取工单状态列表
 * @return {*}
 */
const getOrderMap = async () => {
  const params = { key: 'worksheet_status' }
  const res = await apiGetMapByKey(params)
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    worksheetStatusOptions.value = res.data
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (initParams.tab == 'order') {
    datePicker.value = initParams.start_time ? [Number(initParams.start_time), Number(initParams.end_time)] : [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2]
    form.value.project = initParams.project || 'PUS3'
    form.value.device_id = initParams.device_id ? initParams.device_id : ''
    form.value.worksheet_status = initParams.worksheet_status ? initParams.worksheet_status.split(',') : []
    pages.value.current = initParams.page ? Number(initParams.page) : 1
    pages.value.size = initParams.size ? Number(initParams.size) : 10
    searchDeviceList(route.query.device_id || 'NIO')
  } else {
    searchDeviceList('NIO')
  }
  searchForm.value = cloneDeep(form.value)
  getList(false)
  getChartList()
  getOrderMap()
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.health-detail-container {
  :deep(.el-input__inner),
  :deep(.el-date-editor .el-range-input) {
    color: #262626;
  }
  :deep(.el-form) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px 24px;
    margin-bottom: 20px;
    .el-form-item {
      margin: 0;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
      }
      .el-select .el-select__tags .el-tag--info {
        color: #262626;
      }
    }
  }
  .echart-container {
    display: grid;
    grid-template-columns: 1fr 2fr 170px;
    gap: 16px 24px;
    margin-bottom: 20px;
    .echart-card {
      padding: 16px;
      border-radius: 4px;
      border: 1px solid #dcf2f3;
      background-color: #fff;
      .label-text {
        width: 80px;
        font-size: 14px;
        line-height: 22px;
        color: #595959;
        font-weight: 500;
      }
      .value-text {
        font-size: 16px;
        line-height: 24px;
        color: #262626;
        font-weight: 500;
      }
      :deep(.el-empty__image) {
        width: 120px;
      }
    }
  }
}
</style>
