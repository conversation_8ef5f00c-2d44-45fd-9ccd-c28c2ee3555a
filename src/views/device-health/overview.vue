<template>
  <div class="flex-box width-full">
    <div class="overview-container">
      <el-form :model="form" inline class="margin_b-2">
        <el-form-item label="时间范围">
          <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" value-format="x" unlink-panels :shortcuts="shortcuts" :range-separator="$t('common.pp_to')" :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
            <template #range-separator>
              <TimeRangeIcon style="margin: 0 5px" />
            </template>
          </el-date-picker>
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="form.project">
            <el-option v-for="item in deviceOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <div v-if="loading" class="flex-box flex_j_c-center flex_a_i-center flex-item_f-1">
        <div class="loader-45"></div>
      </div>

      <div class="card-container margin_b-20" v-if="!loading">
        <div class="flex-box flex_j_c-space-between">
          <div class="flex-box flex_d-column">
            <span class="margin_b-6 section-title">总体平均健康度</span>
            <span class="flex-box flex_a_i-center">
              <BookIcon />
              <span class="margin_l-8 font-size-16 color-26">{{ health_info.health_score.toFixed(2) }}</span>
            </span>
          </div>
          <div class="flex-box flex_d-column">
            <span class="margin_b-6 section-title">健康度环比上周</span>
            <span class="flex-box flex_a_i-center">
              <FlagIcon />
              <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.health_increase > 0">+</span>{{ health_info.health_increase.toFixed(2) }}</span>
            </span>
          </div>
          <div class="flex-box flex_d-column">
            <span class="margin_b-6 section-title">健康度环比上周增长率</span>
            <span class="flex-box flex_a_i-center">
              <PeopleIcon />
              <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.health_increase_rate > 0">+</span>{{ (health_info.health_increase_rate * 100).toFixed(2) }}%</span>
            </span>
          </div>
          <div class="flex-box flex_d-column">
            <span class="margin_b-6 section-title">站点数量</span>
            <span class="flex-box flex_a_i-center">
              <CalendarIcon />
              <span class="margin_l-8 font-size-16 color-26">{{ update_info.device_count }}</span>
            </span>
          </div>
        </div>
        <div id="healthLineCharts" class="width-full height-240" v-show="hasHealthChart"></div>
        <div v-show="!hasHealthChart">
          <el-empty description="暂无总体健康度数据" class="width-full height-full" />
        </div>
      </div>

      <div class="card-container" v-if="!loading">
        <div class="flex-box gap_16 margin_b-16">
          <div :class="['custom-button', { 'active-custom-button': isServo }]" @click="handleClickServo">
            <span>伺服</span>
            <HookIcon v-if="isServo" class="hook-icon" />
          </div>
          <div :class="['custom-button', { 'active-custom-button': isCharge }]" @click="handleClickCharge">
            <span>充电模块</span>
            <HookIcon v-if="isCharge" class="hook-icon" />
          </div>
          <div :class="['custom-button', { 'active-custom-button': isSensor }]" @click="handleClickSensor">
            <span>传感器</span>
            <HookIcon v-if="isSensor" class="hook-icon" />
          </div>
        </div>

        <div class="flex-box flex_d-column gap_20">
          <!-- 伺服健康度 -->
          <div v-if="isServo">
            <div class="flex-box flex_j_c-space-between">
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">伺服健康度</span>
                <span class="flex-box flex_a_i-center">
                  <BookIcon />
                  <span class="margin_l-8 font-size-16 color-26">{{ health_info.servo_health_score.toFixed(2) }}</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">健康度环比上周</span>
                <span class="flex-box flex_a_i-center">
                  <FlagIcon />
                  <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.servo_health_increase > 0">+</span>{{ health_info.servo_health_increase.toFixed(2) }}</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">健康度环比上周增长率</span>
                <span class="flex-box flex_a_i-center">
                  <PeopleIcon />
                  <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.servo_health_increase_rate > 0">+</span>{{ (health_info.servo_health_increase_rate * 100).toFixed(2) }}%</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">站点数量</span>
                <span class="flex-box flex_a_i-center">
                  <CalendarIcon />
                  <span class="margin_l-8 font-size-16 color-26">{{ update_info.device_count }}</span>
                </span>
              </div>
            </div>
            <div id="servoLineCharts" class="width-full height-240" v-show="hasServoChart"></div>
            <div v-show="!hasServoChart">
              <el-empty description="暂无伺服健康度数据" class="width-full height-full" />
            </div>
          </div>

          <!-- 充电模块健康度 -->
          <div v-if="isCharge">
            <div class="flex-box flex_j_c-space-between">
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">充电模块健康度</span>
                <span class="flex-box flex_a_i-center">
                  <BookIcon />
                  <span class="margin_l-8 font-size-16 color-26">{{ health_info.charge_health_score.toFixed(2) }}</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">健康度环比上周</span>
                <span class="flex-box flex_a_i-center">
                  <FlagIcon />
                  <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.charge_health_increase > 0">+</span>{{ health_info.charge_health_increase.toFixed(2) }}</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">健康度环比上周增长率</span>
                <span class="flex-box flex_a_i-center">
                  <PeopleIcon />
                  <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.charge_health_increase_rate > 0">+</span>{{ (health_info.charge_health_increase_rate * 100).toFixed(2) }}%</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">站点数量</span>
                <span class="flex-box flex_a_i-center">
                  <CalendarIcon />
                  <span class="margin_l-8 font-size-16 color-26">{{ update_info.device_count }}</span>
                </span>
              </div>
            </div>
            <div id="chargeLineCharts" class="width-full height-240" v-show="hasChargeChart"></div>
            <div v-show="!hasChargeChart">
              <el-empty description="暂无充电模块健康度数据" class="width-full height-full" />
            </div>
          </div>

          <!-- 传感器健康度 -->
          <div v-if="isSensor">
            <div class="flex-box flex_j_c-space-between">
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">传感器健康度</span>
                <span class="flex-box flex_a_i-center">
                  <BookIcon />
                  <span class="margin_l-8 font-size-16 color-26">{{ health_info.sensor_health_score.toFixed(2) }}</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">健康度环比上周</span>
                <span class="flex-box flex_a_i-center">
                  <FlagIcon />
                  <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.sensor_health_increase > 0">+</span>{{ health_info.sensor_health_increase.toFixed(2) }}</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">健康度环比上周增长率</span>
                <span class="flex-box flex_a_i-center">
                  <PeopleIcon />
                  <span class="margin_l-8 font-size-16 color-26"><span v-if="health_info.sensor_health_increase_rate > 0">+</span>{{ (health_info.sensor_health_increase_rate * 100).toFixed(2) }}%</span>
                </span>
              </div>
              <div class="flex-box flex_d-column">
                <span class="margin_b-6 section-title">站点数量</span>
                <span class="flex-box flex_a_i-center">
                  <CalendarIcon />
                  <span class="margin_l-8 font-size-16 color-26">{{ update_info.device_count }}</span>
                </span>
              </div>
            </div>
            <div id="sensorLineCharts" class="width-full height-240" v-show="hasSensorChart"></div>
            <div v-show="!hasSensorChart">
              <el-empty description="暂无传感器健康度数据" class="width-full height-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="width-360 margin_l-16">
      <ListCard :datePicker="datePicker" :project="form.project" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, nextTick, watch, computed, shallowRef, h } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { deviceOptions, lineOption } from './statusMap'
import { shortcuts, getDisabledDate, getStartTimeOfDay, getFinalEndTime, removeNullKeys } from '~/utils'
import { ElMessage } from 'element-plus'
import { apiGetHealthtData } from '~/apis/device-health'
import { cloneDeep } from 'lodash-es'
import ListCard from './card.vue'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import BookIcon from '~/assets/svg/book.vue'
import FlagIcon from '~/assets/svg/flag.vue'
import PeopleIcon from '~/assets/svg/people.vue'
import CalendarIcon from '~/assets/svg/calendar.vue'
import HookIcon from '~/views/create-config/components/icon/hook-icon.vue'
import * as echarts from 'echarts'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const loading = ref(false)
const isServo = ref(true)
const isCharge = ref(true)
const isSensor = ref(false)
const hasHealthChart = ref(false)
const hasServoChart = ref(false)
const hasChargeChart = ref(false)
const hasSensorChart = ref(false)
const isCollapse = computed(() => store.state.menus.collapse)
const echartsArr = ref([] as any)
const health_info = ref({}) as any
const health_chart = ref([]) as any
const servo_chart = ref([]) as any
const charge_chart = ref([]) as any
const sensor_chart = ref([]) as any
const update_info = ref({}) as any
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const healthLineOption = cloneDeep(lineOption)
const servoLineOption = cloneDeep(lineOption)
const chargeLineOption = cloneDeep(lineOption)
const sensorLineOption = cloneDeep(lineOption)
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2] as any)
const form = ref({
  project: 'PUS3'
})

const formatDate = (date: any) => {
  const d = new Date(date)
  return d.getMonth() + 1 + '-' + d.getDate()
}

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  echartsArr.value.push(myChart)
  myChart.clear()
  chartDom.setAttribute('_echarts_instance_', '')
  option && myChart.setOption(option, true)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  healthLineOption.series[0].name = '总体健康度'
  healthLineOption.series[0].data = health_chart.value.map((item: any) => item.score.toFixed(2))
  healthLineOption.xAxis.data = health_chart.value.map((item: any) => formatDate(item.day))

  servoLineOption.series[0].name = '伺服健康度'
  servoLineOption.series[0].data = servo_chart.value.map((item: any) => item.score.toFixed(2))
  servoLineOption.xAxis.data = servo_chart.value.map((item: any) => formatDate(item.day))

  chargeLineOption.series[0].name = '充电模块健康度'
  chargeLineOption.series[0].data = charge_chart.value.map((item: any) => item.score.toFixed(2))
  chargeLineOption.xAxis.data = charge_chart.value.map((item: any) => formatDate(item.day))

  sensorLineOption.series[0].name = '传感器健康度'
  sensorLineOption.series[0].data = sensor_chart.value.map((item: any) => item.score.toFixed(2))
  sensorLineOption.xAxis.data = sensor_chart.value.map((item: any) => formatDate(item.day))

  if (hasHealthChart.value) echartRender('healthLineCharts', healthLineOption)
  if (isServo.value && hasServoChart.value) echartRender('servoLineCharts', servoLineOption)
  if (isCharge.value && hasChargeChart.value) echartRender('chargeLineCharts', chargeLineOption)
  if (isSensor.value && hasSensorChart.value) echartRender('sensorLineCharts', sensorLineOption)
}

/**
 * @description: 选择伺服健康度
 * @return {*}
 */
const handleClickServo = () => {
  isServo.value = !isServo.value
  if (isServo.value && hasServoChart.value) nextTick(() => echartRender('servoLineCharts', servoLineOption))
}

/**
 * @description: 选择充电模块健康度
 * @return {*}
 */
const handleClickCharge = () => {
  isCharge.value = !isCharge.value
  if (isCharge.value && hasChargeChart.value) nextTick(() => echartRender('chargeLineCharts', chargeLineOption))
}

/**
 * @description: 选择传感器健康度
 * @return {*}
 */
const handleClickSensor = () => {
  isSensor.value = !isSensor.value
  if (isSensor.value && hasSensorChart.value) nextTick(() => echartRender('sensorLineCharts', sensorLineOption))
}

watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
    }, 300)
  }
)

/**
 * @description: 时间变更，获取数据
 * @return {*}
 */
const handleDateChange = () => {
  getList(true)
}

/**
 * @description: 获取数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  const params = {
    start_time: getStartTimeOfDay(datePicker.value[0]),
    end_time: getFinalEndTime(datePicker.value[1])
  }
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...removeNullKeys({ ...params, project: form.value.project, tab: 'overview' }) }
    })
  }
  loading.value = true
  const res = await apiGetHealthtData(form.value.project, params)
  loading.value = false
  if (res.err_code) {
    ElMessage.error(res.message)
  } else {
    health_info.value = res.health_info ? res.health_info : {}
    health_chart.value = res.health_chart ? res.health_chart : []
    servo_chart.value = res.servo_chart ? res.servo_chart : []
    charge_chart.value = res.charge_chart ? res.charge_chart : []
    sensor_chart.value = res.sensor_chart ? res.sensor_chart : []
    update_info.value = res.update_info ? res.update_info : {}
    hasHealthChart.value = health_chart.value && health_chart.value.length > 0
    hasServoChart.value = servo_chart.value && servo_chart.value.length > 0
    hasChargeChart.value = charge_chart.value && charge_chart.value.length > 0
    hasSensorChart.value = sensor_chart.value && sensor_chart.value.length > 0
    nextTick(() => setCharts())
  }
}

const initWeb = () => {
  let initParams: any = route.query
  if (!initParams.tab || initParams.tab == 'overview') {
    datePicker.value = initParams.start_time ? [Number(initParams.start_time), Number(initParams.end_time)] : [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2]
    form.value.project = initParams.project || 'PUS3'
  }
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.overview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  :deep(.el-input__inner),
  :deep(.el-date-editor .el-range-input) {
    color: #262626;
  }
  .card-container {
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    background-color: #fff;
    .section-title {
      font-size: 14px;
      color: #595959;
    }
  }
  .custom-button {
    width: 88px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #262626;
    font-size: 14px;
    cursor: pointer;
    position: relative;
  }
  .hook-icon {
    position: absolute;
    right: -1px;
    top: 0;
  }
  .active-custom-button {
    border: 1px solid #00bebe;
  }
}
</style>
