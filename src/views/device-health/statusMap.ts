export const projectMap = {
  PowerSwap: { label: '换电站 1.0', color: '#FFC031' },
  PowerSwap2: { label: '换电站 2.0', color: '#67C23A' },
  PUS3: { label: '换电站 3.0', color: '#00BEBE' },
  PUS4: { label: '换电站 4.0', color: '#21819D' }
} as any

export const healthMap = {
  health_score: '总体健康度',
  sensor_health_score: '传感器健康度',
  servo_health_score: '伺服健康度',
  charge_health_score: '充电模块健康度'
} as any

export const deviceOptions = [
  {
    label: '三代站',
    value: 'PUS3'
  }
]

export const lineOption = {
  animation: false,
  color: '#00bebe',
  xAxis: {
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(228, 228, 228, 1)'
      }
    },
    data: []
  },
  yAxis: {
    type: 'value',
    min: (value: any) => Math.floor(value.min),
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dotted',
        color: 'rgba(227, 227, 227, 1)'
      }
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    confine: true,
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    left: '0%',
    right: '2%',
    bottom: 0,
    top: 32,
    containLabel: true
  },
  series: [
    {
      name: '',
      data: [],
      type: 'line',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, // 渐变起始位置的横坐标，0 为最左侧，0.5 为中间，1 为最右侧
          y: 0, // 渐变起始位置的纵坐标，0 为最顶部，0.5 为中间，1 为最底部
          x2: 0, // 渐变结束位置的横坐标
          y2: 1, // 渐变结束位置的纵坐标，1 为最底部
          colorStops: [
            {
              offset: 0,
              color: 'rgba(0, 190, 190, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(0, 190, 190, 0)'
            }
          ],
          global: false
        }
      },
      label: {
        show: true,
        position: 'top',
        color: 'rgba(53, 121, 139, 1)'
      },
      labelLayout: {
        hideOverlap: true
      }
    }
  ]
} as any

export const lineWithTitleOption = {
  animation: false,
  color: '#00bebe',
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  xAxis: {
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(228, 228, 228, 1)'
      }
    },
    data: []
  },
  yAxis: {
    type: 'value',
    min: (value: any) => Math.floor(value.min),
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dotted',
        color: 'rgba(227, 227, 227, 1)'
      }
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    confine: true,
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    left: '0%',
    right: '2%',
    bottom: 0,
    top: 48,
    containLabel: true
  },
  series: [
    {
      name: '',
      data: [],
      type: 'line',
      symbol: 'none',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, // 渐变起始位置的横坐标，0 为最左侧，0.5 为中间，1 为最右侧
          y: 0, // 渐变起始位置的纵坐标，0 为最顶部，0.5 为中间，1 为最底部
          x2: 0, // 渐变结束位置的横坐标
          y2: 1, // 渐变结束位置的纵坐标，1 为最底部
          colorStops: [
            {
              offset: 0,
              color: 'rgba(0, 190, 190, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(0, 190, 190, 0)'
            }
          ],
          global: false
        }
      },
      labelLayout: {
        hideOverlap: true
      }
    }
  ]
} as any

export const multilineOption = {
  animation: false,
  color: ['#00bebe', '#FFCD5A', '#4C8DFF'],
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  legend: {
    top: -2,
    right: 0,
    textStyle: {
      color: '#595959'
    },
    icon: 'circle',
    itemWidth: 10,
    itemHeight: 10
  },
  xAxis: {
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(228, 228, 228, 1)'
      }
    },
    data: []
  },
  yAxis: {
    type: 'value',
    min: (value: any) => Math.floor(value.min),
    axisLabel: {
      show: true,
      color: 'rgba(89, 89, 89, 1)'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dotted',
        color: 'rgba(227, 227, 227, 1)'
      }
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    confine: true,
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  grid: {
    left: '0%',
    right: '2%',
    bottom: 0,
    top: 48,
    containLabel: true
  },
  series: [
    {
      name: '',
      data: [],
      type: 'line'
    },
    {
      name: '',
      data: [],
      type: 'line'
    },
    {
      name: '',
      data: [],
      type: 'line'
    }
  ]
} as any

export const pieOption = {
  color: ['#00bebe', '#009999', '#01A0AC', '#CCCCFF', '#0099CC', '#66CCCC', '#78D9EF', '#8DE0F8', '#63D0E0', '#55D7DB', '#39BEAE'],
  animation: false,
  title: {
    text: '工单完成情况',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'item'
  },
  series: {
    type: 'pie',
    center: ['46%', '58%'],
    radius: ['35%', '60%'],
    label: {
      show: true,
      width: 90,
      overflow: 'truncate',
      color: '#8C8C8C',
      formatter: function (params: any) {
        return '{a|' + params.name + '} {b|' + params.percent + '%' + '}'
      },
      rich: {
        a: {
          color: '#8C8C8C',
          fontSize: 11
        },
        b: {
          color: '#414141',
          fontSize: 12
        }
      }
    },
    itemStyle: {
      borderRadius: 2,
      borderColor: '#fff',
      borderWidth: 2
    },
    startAngle: 65,
    percentPrecision: 1,
    data: []
  }
} as any

export const servoComponentOptions = [
  {
    label: '堆垛机升降',
    value: 'stacker_lift'
  },
  {
    label: '堆垛机平移',
    value: 'stacker_pan'
  },
  {
    label: '货叉',
    value: 'fork'
  }
]

export const chargeComponentOptions = [
  {
    label: '1号模组',
    value: 1
  },
  {
    label: '2号模组',
    value: 2
  },
  {
    label: '3号模组',
    value: 3
  },
  {
    label: '4号模组',
    value: 4
  },
  {
    label: '5号模组',
    value: 5
  },
  {
    label: '6号模组',
    value: 6
  },
  {
    label: '7号模组',
    value: 7
  },
  {
    label: '8号模组',
    value: 8
  },
  {
    label: '9号模组',
    value: 9
  },
  {
    label: '10号模组',
    value: 10
  }
]
