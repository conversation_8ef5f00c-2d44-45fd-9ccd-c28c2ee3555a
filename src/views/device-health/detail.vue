<template>
  <div class="health-detail-container">
    <el-form :model="form" inline class="margin_b-2">
      <el-form-item label="时间范围">
        <el-date-picker v-model="datePicker" type="daterange" value-format="x" unlink-panels :shortcuts="shortcuts" :range-separator="$t('common.pp_to')" :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
          <template #range-separator>
            <TimeRangeIcon style="margin: 0 5px" />
          </template>
        </el-date-picker>
      </el-form-item>
      <el-form-item label="设备类型">
        <el-select v-model="form.project" class="width-full">
          <el-option v-for="item in deviceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="区域公司">
        <el-select v-model="form.area" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in areaOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备名称">
        <el-select v-model="form.device_id" filterable remote clearable :placeholder="$t('common.pp_enter')" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
          <el-option v-for="item in deviceNameOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
            <span style="float: left">{{ item.description }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="值守状态">
        <el-select v-model="form.on_duty" clearable filterable :placeholder="$t('common.pp_please_select')" class="width-full">
          <el-option v-for="item in dutyOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleSearch" class="welkin-primary-button" :loading="loading">筛选</el-button>
        <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">重置</el-button>
      </el-form-item>
      <el-form-item>
        <div class="width-full flex-box flex_j_c-flex-end">
          <el-button @click="downloadCSV" class="welkin-secondary-button" style="margin-left: 8px">下载</el-button>
        </div>
      </el-form-item>
    </el-form>

    <el-table :data="list" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" v-loading="loading">
      <el-table-column prop="device_id" label="设备ID" min-width="200">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.device_id" direction="rtl" />
        </template>
      </el-table-column>
      <el-table-column prop="description" label="设备名称" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.description || '未命名设备' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="project" label="设备类型" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span :style="{ color: projectMap[row.project].color }">{{ projectMap[row.project].label }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="on_duty" label="值守状态" min-width="100" show-overflow-tooltip /> -->
      <el-table-column prop="health_score" label="总体健康度" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.health_score.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sensor_health_score" label="传感器健康度" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.sensor_health_score.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="charge_health_score" label="充电模块健康度" min-width="140" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.charge_health_score.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="servo_health_score" label="伺服健康度" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.servo_health_score.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="day" label="日期" width="110" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ formatTimeToDay(row.day) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="详情" width="60" fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="flex-box flex_a_i-center">
            <DetailIcon @click="handleClickDetail(row)" class="cursor-pointer" />
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="common-pagination">
      <Page :page="pages" @change="handlePageChange" />
    </div>

    <DetailDialog v-model:detailVisible="detailVisible" :rowInfo="rowInfo" v-if="detailVisible" />
  </div>
</template>

<script setup lang="ts">
import { ref, h, onBeforeMount, shallowRef } from 'vue'
import { shortcuts, getDisabledDate, getStartTimeOfDay, getFinalEndTime, removeNullKeys, formatTimeToDay, clearJson } from '~/utils'
import { apiGetDetailData, apiCompanyMap, apiGetDownload } from '~/apis/device-health'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { cloneDeep, debounce } from 'lodash-es'
import { apiGetDevices } from '~/apis/home'
import { useRoute, useRouter } from 'vue-router'
import { projectMap, deviceOptions } from './statusMap'
import DetailDialog from './detail-dialog.vue'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import DetailIcon from '~/views/satisfaction-analysis/components/icon/detail-icon.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const remoteLoading = ref(false)
const detailVisible = ref(false)
const areaOptions = ref([] as any)
const list = ref([] as any)
const deviceNameOptions = ref([] as any)
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2] as any)
const dutyOptions = [
  {
    value: 0,
    label: '无人值守'
  },
  {
    value: 1,
    label: '有人值守'
  }
]
const form = ref({
  device_id: '',
  area: '',
  // on_duty: '' as number | string,
  project: 'PUS3'
})
const searchForm = ref({} as any)
const rowInfo = ref({} as any)
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const pages = ref(cloneDeep(page))

/**
 * @description: 查看详情
 * @param {*} row
 * @return {*}
 */
const handleClickDetail = (row: any) => {
  rowInfo.value = cloneDeep(row)
  detailVisible.value = true
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: form.value.project, name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceNameOptions.value = res.data
  }
}, 500)

/**
 * @description: 获取区域公司列表
 * @return {*}
 */
const getCompanyNameMap = async () => {
  const res = await apiCompanyMap({ projects: form.value.project })
  areaOptions.value = Object.keys(res.data)
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = cloneDeep(form.value)
  getList()
}

/**
 * @description: 重置
 * @return {*}
 */
const resetSelect = () => {
  clearJson(form.value)
  form.value.project = 'PUS3'
  datePicker.value = [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2]
  handleSearch()
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 获取表格数据
 * @param {*} updateRoute
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.start_time = getStartTimeOfDay(datePicker.value[0])
  formData.end_time = getFinalEndTime(datePicker.value[1])
  formData.download = false
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { tab: 'detail', ...removeNullKeys(formData) }
    })
  }
  loading.value = true
  try {
    const res = await apiGetDetailData(form.value.project, formData)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 下载
 * @return {*}
 */
const downloadCSV = async () => {
  let formData = cloneDeep(searchForm.value) as any
  formData.start_time = getStartTimeOfDay(datePicker.value[0])
  formData.end_time = getFinalEndTime(datePicker.value[1])
  formData.download = true
  removeNullKeys(formData)
  await apiGetDownload(form.value.project, formData)
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (initParams.tab == 'detail') {
    datePicker.value = initParams.start_time ? [Number(initParams.start_time), Number(initParams.end_time)] : [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2]
    form.value.project = initParams.project || 'PUS3'
    form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
    form.value.area = !!initParams.area ? initParams.area : ''
    // form.value.on_duty = !!initParams.on_duty ? Number(initParams.on_duty) : ''
    pages.value.current = !!initParams.page ? Number(initParams.page) : 1
    pages.value.size = !!initParams.size ? Number(initParams.size) : 10
    searchDeviceList(route.query.device_id || 'NIO')
  } else {
    searchDeviceList('NIO')
  }
  searchForm.value = cloneDeep(form.value)
  getCompanyNameMap()
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.health-detail-container {
  :deep(.el-input__inner),
  :deep(.el-date-editor .el-range-input) {
    color: #262626;
  }
  :deep(.el-form) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px 24px;
    margin-bottom: 20px;
    .el-form-item {
      margin: 0;
      .el-form-item__label {
        font-size: 14px;
        color: #595959;
        padding-right: 8px;
      }
    }
  }
}
</style>
