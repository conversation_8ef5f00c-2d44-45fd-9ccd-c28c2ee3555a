<template>
  <div class="detail-dialog-container">
    <el-dialog :title="rowInfo.description || '未命名设备'" v-model="detailVisible" align-center width="1100px" :close-on-click-modal="false" :close-on-press-escape="false" @close="handleCloseDialog">
      <div class="first-line-container">
        <div class="text-card">
          <span class="title-text margin_b-12">{{ formatTimeToDay(rowInfo.day) }}</span>
          <div class="margin_b-15">
            <span class="label-text">总体健康度：</span>
            <span class="value-text">{{ rowInfo.health_score.toFixed(2) }}</span>
          </div>
          <div class="margin_b-15">
            <span class="label-text">传感器健康度：</span>
            <span class="value-text">{{ rowInfo.sensor_health_score.toFixed(2) }}</span>
          </div>
          <div class="margin_b-15">
            <span class="label-text">充电模块健康度：</span>
            <span class="value-text">{{ rowInfo.charge_health_score.toFixed(2) }}</span>
          </div>
          <div class="margin_b-15">
            <span class="label-text">伺服健康度：</span>
            <span class="value-text">{{ rowInfo.servo_health_score.toFixed(2) }}</span>
          </div>
        </div>
        <div class="text-card">
          <span class="title-text margin_b-4">健康度变化趋势</span>
          <el-form :model="form" inline style="margin-bottom: 0">
            <el-form-item label="部件" prop="type">
              <el-select v-model="form.type" @change="handleTypeChange" class="width-350" filterable>
                <el-option v-for="item in typeOptions" :key="item" :value="item" :label="item" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间">
              <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" value-format="x" unlink-panels :shortcuts="shortcuts" :range-separator="$t('common.pp_to')" :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
                <template #range-separator>
                  <TimeRangeIcon style="margin: 0 5px" />
                </template>
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div v-if="loadingTop" class="flex-box flex_j_c-center flex_a_i-center height-200">
            <div class="loader-45"></div>
          </div>
          <div v-else>
            <div id="healthLineCharts" class="width-full height-200" v-show="hasHealthChart && form.type == '总体'"></div>
            <div id="servoLineCharts" class="width-full height-200" v-show="hasServoChart && form.type == '伺服'"></div>
            <div id="chargeLineCharts" class="width-full height-200" v-show="hasChargeChart && form.type == '充电模块'"></div>
            <div id="sensorLineCharts" class="width-full height-200" v-show="hasSensorChart && form.type == '传感器'"></div>
            <div v-if="isEmpty" class="height-200">
              <el-empty description="暂无数据" class="width-full height-full" />
            </div>
          </div>
        </div>
      </div>

      <div class="second-line-container">
        <div class="text-card">
          <div class="margin_b-8 suggestion-line">
            <el-radio-group v-model="moduleType" @change="handleChangeModuleType">
              <el-radio-button label="伺服" value="伺服" />
              <el-radio-button label="传感器" value="传感器" />
              <el-radio-button label="充电模块" value="充电模块" />
            </el-radio-group>
            <div class="margin_l-20" v-if="moduleType == '伺服'">
              <span class="color-59">上线天数：</span>
              <span class="color-26">{{ servoData.online_day }}天</span>
            </div>
            <div class="margin_l-20" v-if="moduleType == '伺服'">
              <span class="color-59">日均单量：</span>
              <span class="color-26">{{ servoData.daily_service_count }}单</span>
            </div>
          </div>
          <!-- 伺服 -->
          <div v-if="moduleType == '伺服'">
            <div class="suggestion-line">
              <el-select v-model="servoComponent" @change="handleServoComponentChange" filterable class="width-220" placeholder="请选择伺服部件">
                <el-option v-for="item in servoComponentOptions" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
              <div class="margin_l-20">
                <span class="color-59">站点建议：</span>
                <span class="color-26">{{ servoData.suggestion || '-' }}</span>
              </div>
              <div class="margin_l-20">
                <span class="color-59">是否有下发工单：</span>
                <div :class="servoData.is_patch_order ? 'patch-order' : 'non-patch-order'">{{ servoData.is_patch_order ? '是' : '否' }}</div>
              </div>
            </div>
            <div v-if="loadingBottom" class="flex-box flex_j_c-center flex_a_i-center height-200 margin_t-16">
              <div class="loader-45"></div>
            </div>
            <div v-else-if="servoComponent !== 'fork'" class="flex-box gap_16 margin_t-16">
              <div id="loadedCharts" class="height-200" style="flex: 1" v-show="hasServoLoaded"></div>
              <div v-if="!hasServoLoaded" class="height-200" style="flex: 1">
                <div class="font-size-14 font-weight-bold color-26">带载扭矩趋势图</div>
                <el-empty description="暂无数据" class="width-full height-full" />
              </div>
              <div id="freeCharts" class="height-200" style="flex: 1" v-show="hasServoFree"></div>
              <div v-if="!hasServoFree" class="height-200" style="flex: 1">
                <div class="font-size-14 font-weight-bold color-26">空载扭矩趋势图</div>
                <el-empty description="暂无数据" class="width-full height-full" />
              </div>
            </div>
            <div v-else class="margin_t-16">
              <div id="loadedCharts" class="width-full height-200" v-show="hasServoLoaded"></div>
              <div v-if="!hasServoLoaded" class="width-full height-200">
                <div class="font-size-14 font-weight-bold color-26">带载扭矩趋势图</div>
                <el-empty description="暂无数据" class="width-full height-full" />
              </div>
            </div>
          </div>

          <!-- 充电模块 -->
          <div v-else-if="moduleType == '充电模块'">
            <div class="suggestion-line">
              <el-select v-model="chargeComponent" @change="handleChargeComponentChange" filterable class="width-220" placeholder="请选择充电模组">
                <el-option v-for="item in chargeComponentOptions" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
              <div class="margin_l-20">
                <span class="color-59">站点建议：</span>
                <span class="color-26">{{ chargeData.suggestion || '-' }}</span>
              </div>
              <div class="margin_l-20">
                <span class="color-59">是否有下发工单：</span>
                <div :class="chargeData.is_patch_order ? 'patch-order' : 'non-patch-order'">{{ chargeData.is_patch_order ? '是' : '否' }}</div>
              </div>
            </div>
            <div v-if="loadingBottom" class="flex-box flex_j_c-center flex_a_i-center height-200 margin_t-16">
              <div class="loader-45"></div>
            </div>
            <div v-else class="flex-box gap_16 margin_t-16">
              <div id="currentCharts" class="height-200" style="flex: 1" v-show="hasCurrentCharts"></div>
              <div v-if="!hasCurrentCharts" class="height-200" style="flex: 1">
                <div class="font-size-14 font-weight-bold color-26">充电模块模组输出电流</div>
                <el-empty description="暂无数据" class="width-full height-full" />
              </div>
              <div id="temperatureCharts" class="height-200" style="flex: 1" v-show="hasTemperatureCharts"></div>
              <div v-if="!hasTemperatureCharts" class="height-200" style="flex: 1">
                <div class="font-size-14 font-weight-bold color-26">充电模块主热点温度</div>
                <el-empty description="暂无数据" class="width-full height-full" />
              </div>
            </div>
          </div>

          <!-- 传感器 -->
          <div v-else>
            <div class="suggestion-line">
              <el-select v-model="cameraComponent" @change="handleCameraComponentChange" filterable placeholder="请选择摄像头" class="width-220">
                <el-option v-for="item in cameraComponentOptions" :key="item.camera_type" :value="item.camera_type" :label="item.camera_name + '-' + item.camera_type">
                  <span style="float: left">{{ item.camera_name }}</span>
                  <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.camera_type }}</span>
                </el-option>
              </el-select>
              <div class="margin_l-20">
                <span class="color-59">站点建议：</span>
                <span class="color-26">{{ sensorData.suggestion || '-' }}</span>
              </div>
              <div class="margin_l-20">
                <span class="color-59">是否有下发工单：</span>
                <div :class="sensorData.is_patch_order ? 'patch-order' : 'non-patch-order'">{{ sensorData.is_patch_order ? '是' : '否' }}</div>
              </div>
            </div>
            <div class="flex-box flex_j_c-center flex_a_i-center height-200 margin_t-16 relative">
              <el-empty description="暂无图片" class="width-full" v-if="!imageInfo.image_url" />
              <img :src="imageInfo.image_url" class="real-img cursor-pointer" v-if="imageInfo.image_url" @click="imageVisible = true" />
              <img :src="imageInfo.mask_url" class="mask-img cursor-pointer" v-if="imageInfo.image_url" @click="imageVisible = true" />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog :title="imageInfo.camera_name + '-' + imageInfo.camera_type" v-model="imageVisible" align-center width="800px" @close="imageVisible = false">
      <div class="width-full height-600 relative">
        <img :src="imageInfo.image_url" class="real-img" />
        <img :src="imageInfo.mask_url" class="mask-img" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, shallowRef, h, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { apiGetSingleStationHealth, apiGetServo, apiGetCharge, apiGetSensor } from '~/apis/device-health'
import { shortcuts, getDisabledDate, getStartTimeOfDay, getFinalEndTime, formatTimeToDay } from '~/utils'
import { cloneDeep } from 'lodash-es'
import { lineOption, lineWithTitleOption, multilineOption, servoComponentOptions, chargeComponentOptions } from './statusMap'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import * as echarts from 'echarts'

const props = defineProps({
  detailVisible: Boolean,
  rowInfo: {
    type: Object,
    default: {}
  }
})

const emits = defineEmits(['update:detailVisible'])

const loadingTop = ref(false)
const loadingBottom = ref(false)
const hasHealthChart = ref(false)
const hasServoChart = ref(false)
const hasChargeChart = ref(false)
const hasSensorChart = ref(false)
const hasServoLoaded = ref(false)
const hasServoFree = ref(false)
const hasCurrentCharts = ref(false)
const hasTemperatureCharts = ref(false)
const imageVisible = ref(false)
const moduleType = ref('伺服')
const servoComponent = ref('stacker_lift')
const cameraComponent = ref('')
const chargeComponent = ref(1)
const datePicker = ref([] as any)
const cameraComponentOptions = ref([] as any)
const typeOptions = ref(['总体', '伺服', '传感器', '充电模块'])
const form = ref({
  type: '总体'
})
const servoData = ref({} as any)
const chargeData = ref({} as any)
const sensorData = ref({} as any)
const imageInfo = ref({} as any)
const healthLineOption = cloneDeep(lineOption)
const servoLineOption = cloneDeep(lineOption)
const chargeLineOption = cloneDeep(lineOption)
const sensorLineOption = cloneDeep(lineOption)
const servoLoadedOption = cloneDeep(lineWithTitleOption)
const servoFreeOption = cloneDeep(lineWithTitleOption)
const currentOption = cloneDeep(multilineOption)
const temperatureOption = cloneDeep(multilineOption)
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const isEmpty = computed(() => {
  return (!hasHealthChart.value && form.value.type == '总体') || (!hasServoChart.value && form.value.type == '伺服') || (!hasChargeChart.value && form.value.type == '充电模块') || (!hasSensorChart.value && form.value.type == '传感器')
})

const formatDate = (date: any) => {
  const d = new Date(date)
  return d.getMonth() + 1 + '-' + d.getDate()
}

const formatTime = (timestamp: number) => {
  if (!timestamp) return '-'
  const timeDate = new Date(timestamp)
  return timeDate.toTimeString().substring(0, 8)
}

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  myChart.clear()
  chartDom.setAttribute('_echarts_instance_', '')
  option && myChart.setOption(option, true)
  window.addEventListener('resize', () => myChart.resize())
}

const setCharts = () => {
  if (form.value.type == '总体' && hasHealthChart.value) echartRender('healthLineCharts', healthLineOption)
  if (form.value.type == '伺服' && hasServoChart.value) echartRender('servoLineCharts', servoLineOption)
  if (form.value.type == '充电模块' && hasChargeChart.value) echartRender('chargeLineCharts', chargeLineOption)
  if (form.value.type == '传感器' && hasSensorChart.value) echartRender('sensorLineCharts', sensorLineOption)
}

/**
 * @description: 类型变更
 * @return {*}
 */
const handleTypeChange = () => {
  nextTick(() => setCharts())
}

/**
 * @description: 时间变更，获取数据
 * @return {*}
 */
const handleDateChange = () => {
  getList()
}

/**
 * @description: 获取顶部数据
 * @return {*}
 */
const getList = async () => {
  const params = {
    start_time: getStartTimeOfDay(datePicker.value[0]),
    end_time: getFinalEndTime(datePicker.value[1])
  }
  loadingTop.value = true
  try {
    const res = await apiGetSingleStationHealth(props.rowInfo.project, props.rowInfo.device_id, params)
    loadingTop.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      hasHealthChart.value = res.health_chart && res.health_chart.length > 0
      hasServoChart.value = res.servo_chart && res.servo_chart.length > 0
      hasChargeChart.value = res.charge_chart && res.charge_chart.length > 0
      hasSensorChart.value = res.sensor_chart && res.sensor_chart.length > 0

      if (hasHealthChart.value) {
        healthLineOption.series[0].name = '总体健康度'
        healthLineOption.series[0].data = res.health_chart.map((item: any) => item.score.toFixed(2))
        healthLineOption.xAxis.data = res.health_chart.map((item: any) => formatDate(item.day))
      }

      if (hasServoChart.value) {
        servoLineOption.series[0].name = '伺服健康度'
        servoLineOption.series[0].data = res.servo_chart.map((item: any) => item.score.toFixed(2))
        servoLineOption.xAxis.data = res.servo_chart.map((item: any) => formatDate(item.day))
      }

      if (hasChargeChart.value) {
        chargeLineOption.series[0].name = '充电模块健康度'
        chargeLineOption.series[0].data = res.charge_chart.map((item: any) => item.score.toFixed(2))
        chargeLineOption.xAxis.data = res.charge_chart.map((item: any) => formatDate(item.day))
      }

      if (hasSensorChart.value) {
        sensorLineOption.series[0].name = '传感器健康度'
        sensorLineOption.series[0].data = res.sensor_chart.map((item: any) => item.score.toFixed(2))
        sensorLineOption.xAxis.data = res.sensor_chart.map((item: any) => formatDate(item.day))
      }
      nextTick(() => setCharts())
    }
  } catch (error: any) {
    loadingTop.value = false
  }
}

/**
 * @description: 伺服部件更改
 * @return {*}
 */
const handleServoComponentChange = () => {
  formatServo()
}

/**
 * @description: 处理伺服部件数据
 * @return {*}
 */
const formatServo = () => {
  if (servoComponent.value == 'stacker_lift') {
    hasServoLoaded.value = servoData.value.stacker_lift.loaded && servoData.value.stacker_lift.loaded.length > 0
    hasServoFree.value = servoData.value.stacker_lift.free && servoData.value.stacker_lift.free.length > 0
    if (hasServoLoaded.value) {
      servoLoadedOption.title.text = '带载扭矩趋势图'
      servoLoadedOption.series[0].name = '带载扭矩'
      servoLoadedOption.series[0].data = servoData.value.stacker_lift.loaded.map((item: any) => item.torque.toFixed(2))
      servoLoadedOption.xAxis.data = servoData.value.stacker_lift.loaded.map((item: any) => formatDate(item.day))
    }
    if (hasServoFree.value) {
      servoFreeOption.title.text = '空载扭矩趋势图'
      servoFreeOption.series[0].name = '空载扭矩'
      servoFreeOption.series[0].data = servoData.value.stacker_lift.free.map((item: any) => item.torque.toFixed(2))
      servoFreeOption.xAxis.data = servoData.value.stacker_lift.free.map((item: any) => formatDate(item.day))
    }
    nextTick(() => {
      if (hasServoLoaded.value) echartRender('loadedCharts', servoLoadedOption)
      if (hasServoFree.value) echartRender('freeCharts', servoFreeOption)
    })
  } else if (servoComponent.value == 'stacker_pan') {
    hasServoLoaded.value = servoData.value.stacker_pan.loaded && servoData.value.stacker_pan.loaded.length > 0
    hasServoFree.value = servoData.value.stacker_pan.free && servoData.value.stacker_pan.free.length > 0
    if (hasServoLoaded.value) {
      servoLoadedOption.title.text = '带载扭矩趋势图'
      servoLoadedOption.series[0].name = '带载扭矩'
      servoLoadedOption.series[0].data = servoData.value.stacker_pan.loaded.map((item: any) => item.torque.toFixed(2))
      servoLoadedOption.xAxis.data = servoData.value.stacker_pan.loaded.map((item: any) => formatDate(item.day))
    }
    if (hasServoFree.value) {
      servoFreeOption.title.text = '空载扭矩趋势图'
      servoFreeOption.series[0].name = '空载扭矩'
      servoFreeOption.series[0].data = servoData.value.stacker_pan.free.map((item: any) => item.torque.toFixed(2))
      servoFreeOption.xAxis.data = servoData.value.stacker_pan.free.map((item: any) => formatDate(item.day))
    }
    nextTick(() => {
      if (hasServoLoaded.value) echartRender('loadedCharts', servoLoadedOption)
      if (hasServoFree.value) echartRender('freeCharts', servoFreeOption)
    })
  } else {
    hasServoLoaded.value = servoData.value.fork.loaded && servoData.value.fork.loaded.length > 0
    if (hasServoLoaded.value) {
      servoLoadedOption.title.text = '带载扭矩趋势图'
      servoLoadedOption.series[0].name = '带载扭矩'
      servoLoadedOption.series[0].data = servoData.value.fork.loaded.map((item: any) => item.torque.toFixed(2))
      servoLoadedOption.xAxis.data = servoData.value.fork.loaded.map((item: any) => formatDate(item.day))
    }
    nextTick(() => {
      if (hasServoLoaded.value) echartRender('loadedCharts', servoLoadedOption)
    })
  }
}

/**
 * @description: 获取伺服部件数据
 * @return {*}
 */
const getServoList = async () => {
  const params = {
    start_time: getStartTimeOfDay(props.rowInfo.day - 3600 * 1000 * 24 * 30),
    end_time: getFinalEndTime(props.rowInfo.day),
    day: props.rowInfo.day
  }
  loadingBottom.value = true
  try {
    const res = await apiGetServo(props.rowInfo.project, props.rowInfo.device_id, params)
    loadingBottom.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      servoData.value = res.data
      formatServo()
    }
  } catch (error: any) {
    loadingBottom.value = false
  }
}

/**
 * @description: 充电模组更改
 * @return {*}
 */
const handleChargeComponentChange = () => {
  getChargeList()
}

/**
 * @description: 处理充电模块数据
 * @return {*}
 */
const formatCharge = () => {
  hasCurrentCharts.value = chargeData.value.output_current && chargeData.value.output_current.length > 0
  hasTemperatureCharts.value = chargeData.value.sic2_temperature && chargeData.value.sic2_temperature.length > 0
  if (hasCurrentCharts.value) {
    currentOption.title.text = '充电模块模组输出电流'
    currentOption.series[0].name = 3 * chargeComponent.value - 2
    currentOption.series[1].name = 3 * chargeComponent.value - 1
    currentOption.series[2].name = 3 * chargeComponent.value
    currentOption.series[0].data = chargeData.value.output_current.map((item: any) => item.module1 ? item.module1.toFixed(2) : item.module1)
    currentOption.series[1].data = chargeData.value.output_current.map((item: any) => item.module2 ? item.module2.toFixed(2) : item.module2)
    currentOption.series[2].data = chargeData.value.output_current.map((item: any) => item.module3 ? item.module3.toFixed(2) : item.module3)
    currentOption.xAxis.data = chargeData.value.output_current.map((item: any) => formatTime(item.timestamp))
  }
  if (hasTemperatureCharts.value) {
    temperatureOption.title.text = '充电模块主热点温度'
    temperatureOption.series[0].name = 3 * chargeComponent.value - 2
    temperatureOption.series[1].name = 3 * chargeComponent.value - 1
    temperatureOption.series[2].name = 3 * chargeComponent.value
    temperatureOption.series[0].data = chargeData.value.sic2_temperature.map((item: any) => item.module1 ? item.module1.toFixed(2) : item.module1)
    temperatureOption.series[1].data = chargeData.value.sic2_temperature.map((item: any) => item.module2 ? item.module2.toFixed(2) : item.module2)
    temperatureOption.series[2].data = chargeData.value.sic2_temperature.map((item: any) => item.module3 ? item.module3.toFixed(2) : item.module3)
    temperatureOption.xAxis.data = chargeData.value.sic2_temperature.map((item: any) => formatTime(item.timestamp))
  }
  nextTick(() => {
    if (hasCurrentCharts.value) echartRender('currentCharts', currentOption)
    if (hasTemperatureCharts.value) echartRender('temperatureCharts', temperatureOption)
  })
}

/**
 * @description: 获取充电模块数据
 * @return {*}
 */
const getChargeList = async () => {
  const params = {
    group_id: chargeComponent.value,
    day: props.rowInfo.day
  }
  loadingBottom.value = true
  try {
    const res = await apiGetCharge(props.rowInfo.project, props.rowInfo.device_id, params)
    loadingBottom.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      chargeData.value = res.data
      formatCharge()
    }
  } catch (error: any) {
    loadingBottom.value = false
  }
}

/**
 * @description: 摄像头更改
 * @return {*}
 */
const handleCameraComponentChange = () => {
  formatSensor()
}

/**
 * @description: 处理传感器数据
 * @return {*}
 */
const formatSensor = () => {
  imageInfo.value = sensorData.value.details.find((obj: any) => obj.camera_type == cameraComponent.value)
  if(imageInfo.value.mask_url) imageInfo.value.mask_url.replace('https://api-owl-minio.nioint.com', '/web/minio').replace('http://api-owl-minio.nioint.com', '/web/minio')
}

/**
 * @description: 获取传感器数据
 * @return {*}
 */
const getSensorList = async () => {
  const params = {
    day: props.rowInfo.day
  }
  try {
    const res = await apiGetSensor(props.rowInfo.project, props.rowInfo.device_id, params)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      sensorData.value = res.data
      cameraComponentOptions.value = sensorData.value.details
      cameraComponent.value = sensorData.value.details[0].camera_type
      formatSensor()
    }
  } catch (error: any) {}
}

/**
 * @description: 切换伺服、传感器、充电模块
 * @param {*} val
 * @return {*}
 */
const handleChangeModuleType = (val: any) => {
  if (val == '伺服') {
    getServoList()
  } else if (val == '充电模块') {
    getChargeList()
  }
}

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleCloseDialog = () => {
  emits('update:detailVisible', false)
}

onBeforeMount(() => {
  datePicker.value = [props.rowInfo.day - 3600 * 1000 * 24 * 7, props.rowInfo.day]
  getList()
  getServoList()
  getSensorList()
})
</script>

<style lang="scss" scoped>
.detail-dialog-container {
  :deep(.el-dialog__title) {
    color: #1f1f1f;
    font-size: 18px;
  }
  :deep(.el-dialog__body) {
    padding-top: 16px;
    .real-img {
      position: absolute;
      z-index: 1;
      max-width: 100%;
      max-height: 100%;
    }
    .mask-img {
      position: absolute;
      z-index: 2;
      max-width: 100%;
      max-height: 100%;
    }
  }
  .first-line-container {
    display: grid;
    grid-template-columns: 1fr 4fr;
    column-gap: 16px;
    margin-bottom: 16px;
  }
  .second-line-container {
    .suggestion-line {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      column-gap: 16px;
      align-items: center;
    }
    .patch-order,
    .non-patch-order {
      width: 60px;
      padding: 2px 0;
      border-radius: 2px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      color: #2f9c74;
      background-color: #e8fcea;
    }
    .non-patch-order {
      color: #595959;
      background-color: #ebecee;
    }
  }
  .text-card {
    padding: 16px 20px;
    border-radius: 4px;
    border: 1px solid #c6efef;
    display: flex;
    flex-direction: column;
    gap: 8px;
    background-color: #fbfefe;
    .label-text {
      font-size: 14px;
      line-height: 22px;
      color: #595959;
      font-weight: 500;
    }
    .value-text {
      font-size: 18px;
      line-height: 26px;
      color: #262626;
      font-weight: 500;
    }
  }
  .title-text {
    font-size: 16px;
    line-height: 24px;
    color: #262626;
    font-weight: bold;
  }
  :deep(.el-empty__image) {
    width: 120px;
  }
}
</style>
