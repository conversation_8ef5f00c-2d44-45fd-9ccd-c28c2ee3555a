<template>
  <div class="health-card-container">
    <div class="flex-box flex_j_c-space-between flex_a_i-center">
      <span class="color-26 font-size-16 font-weight-bold margin_r-15">健康度尾部站点TOP10</span>
      <div class="view-more" @click="handleViewMore">
        <EyeIcon />
        <span>查看更多</span>
      </div>
    </div>
    <div class="margin_t-10">
      <el-select v-model="categoryValue" style="width: 100%" @change="getList">
        <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>

    <div v-if="loading" class="flex-box flex_j_c-center flex_a_i-center flex-item_f-1">
      <div class="loader-45"></div>
    </div>

    <el-empty class="width-full height-full" :description="$t('common.pp_empty')" v-if="!loading && deviceList.length === 0"></el-empty>

    <div v-if="!loading && deviceList.length > 0">
      <el-popover placement="bottom" :width="350" trigger="hover" :offset="-14" :show-arrow="false" v-for="(item, index) in deviceList" :popper-style="{ background: '#00bebe' }">
        <template #reference>
          <el-card class="margin_t-10 border-radius-8">
            <div class="height-60 width-full flex-box flex_j_c-space-between">
              <div class="flex-box">
                <div class="flex-box flex_a_i-center font-size-16 margin_r-10 font-weigth index-number">{{ index + 1 }}</div>
                <div class="flex-box flex_d-column flex_j_c-space-around width-220">
                  <span class="device-name font-size-14 ellipse">{{ item.description || '未命名设备' }}</span>
                  <span class="device-id font-size-12">{{ item.device_id }}</span>
                </div>
              </div>
              <div class="flex-box flex_d-column flex_j_c-space-around">
                <span class="pass-rate font-size-16">{{ item[categoryValue].toFixed(2) }}</span>
                <span class="total font-size-9">{{ healthMap[categoryValue] }}</span>
              </div>
            </div>
          </el-card>
        </template>
        <div class="font-weight-480" style="color: #fff; display: grid; grid-template-columns: 1fr 1fr; gap: 6px 0">
          <span>总体：{{ item.health_score.toFixed(2) }}</span>
          <span>伺服：{{ item.servo_health_score.toFixed(2) }}</span>
          <span>充电模块：{{ item.charge_health_score.toFixed(2) }}</span>
          <span>传感器：{{ item.sensor_health_score.toFixed(2) }}</span>
        </div>
      </el-popover>
    </div>

    <TailDialog v-model:tailVisible="tailVisible" :project="project" :originDate="datePicker" v-if="tailVisible" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onBeforeMount, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { apiGetStationData } from '~/apis/device-health'
import { getStartTimeOfDay, getFinalEndTime } from '~/utils'
import { ElMessage } from 'element-plus'
import { healthMap } from './statusMap'
import TailDialog from './tail-dialog.vue'
import EyeIcon from '~/views/satisfaction-detail/components/icon/eye-icon.vue'

const props = defineProps({
  datePicker: {
    type: Array,
    default: [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 9, Date.now() - 3600 * 1000 * 24 * 2]
  },
  project: {
    type: String,
    default: 'PUS3'
  }
})

const { t } = useI18n()
const loading = ref(false)
const tailVisible = ref(false)
const categoryValue = ref('health_score')
const deviceList = ref([] as any)
const categoryOptions = [
  {
    value: 'health_score',
    label: '总体健康度'
  },
  {
    value: 'servo_health_score',
    label: '伺服健康度'
  },
  {
    value: 'charge_health_score',
    label: '充电模块健康度'
  },
  {
    value: 'sensor_health_score',
    label: '传感器健康度'
  }
]

const handleViewMore = () => {
  tailVisible.value = true
}

/**
 * @description: 获取数据
 * @return {*}
 */
const getList = async () => {
  const query = {
    start_time: getStartTimeOfDay(props.datePicker[0]),
    end_time: getFinalEndTime(props.datePicker[1]),
    sort: categoryValue.value,
    descending: false,
    page: 1,
    size: 10
  }
  loading.value = true
  try {
    const res = await apiGetStationData(props.project, query)
    loading.value = false
    const { err_code, message } = res
    if (!err_code) {
      deviceList.value = res.data || []
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

watch(
  () => props.datePicker,
  (newValue, oldValue) => {
    getList()
  }
)
onBeforeMount(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.health-card-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 12px;
  border-radius: 4px;
  background: linear-gradient(180deg, #c6efef, #e5f9f9);
  :deep(.el-input__inner),
  :deep(.el-date-editor .el-range-input) {
    color: #262626;
  }
  .view-more {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #01a0ac;
    cursor: pointer;
  }
  .index-number {
    color: #ff8a4a;
    font-weight: bold;
  }
  .device-name {
    color: #275a71;
    display: inline-block;
  }
  .device-id {
    color: #666666;
    display: inline-block;
  }
  .pass-rate {
    color: #ff8a4a;
    font-weight: bold;
    display: inline-block;
  }
  .total {
    color: #6dc8ec;
    font-weight: bold;
    display: inline-block;
    white-space: nowrap;
  }
  :deep(.el-card__body) {
    height: 90px;
    padding: 10px;
    display: flex;
    align-items: center;
  }
}
.description-title,
.descriptions-label .description-name {
  color: #275a71;
  font-weight: bold;
}
:deep(.el-descriptions) {
  background-color: #00bebe;
}
:deep(.el-card.is-always-shadow) {
  border-color: rgba(220, 242, 243, 1);
}
:deep(.el-card:hover) {
  border-color: #00bebe;
}
</style>
