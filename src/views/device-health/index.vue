<template>
  <div :class="['device-health-container', { 'gray-background': activeVersionTab == 'overview' }]">
    <!-- 头部 -->
    <div class="header-container">
      <div class="font-size-20 font-weight-bold margin_b-12" style="color: #1f1f1f">设备健康度</div>
      <el-tabs v-model="activeVersionTab" @tab-change="handleTabChange">
        <el-tab-pane v-for="item in tabList" :label="item.label" :name="item.name"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 总览页面 -->
    <div class="content-container flex-box" v-if="activeVersionTab == 'overview'">
      <OverView />
    </div>

    <!-- 查询明细页面 -->
    <div class="content-container" v-else-if="activeVersionTab == 'detail'">
      <Detail />
    </div>

    <!-- 工单总览 -->
    <div class="content-container" v-else>
      <Order />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import OverView from './overview.vue'
import Detail from './detail.vue'
import Order from './order.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const activeVersionTab = ref('overview') as any
const tabList = ref([
  {
    name: 'overview',
    label: '健康度总览'
  },
  {
    name: 'detail',
    label: '健康度明细'
  },
  {
    name: 'order',
    label: '工单总览'
  }
])

/**
 * @description: 切换tab
 * @param {*} name
 * @return {*}
 */
const handleTabChange = (name: any) => {
  router.push({
    path: location.pathname,
    query: { tab: name }
  })
}

onBeforeMount(() => {
  activeVersionTab.value = route.query.tab || 'overview'
})
</script>

<style lang="scss" scoped>
.device-health-container,
.gray-background {
  font-family: 'Blue Sky Standard';
  padding: 24px;
  background: linear-gradient(180deg, #e2f9f9 -6.51%, rgba(255, 255, 255, 0.5) 12.89%);
  .header-container {
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 15px;
      font-weight: 400;
      color: #595959;
    }
  }
  .content-container {
    padding-top: 20px;
    :deep(.el-dialog__headerbtn .el-dialog__close) {
      font-size: 20px;
    }
  }
}
.gray-background {
  padding: 0;
  background: none;
  display: flex;
  flex-direction: column;
  .header-container {
    padding: 24px;
    padding-bottom: 0;
    background: linear-gradient(180deg, #f6fefe, #f8f8f8);
  }
  .content-container {
    flex: 1;
    padding: 20px 24px 24px;
    background: #f8f8f8;
  }
}
</style>
