<template>
  <div class="tail-dialog-container">
    <el-dialog title="健康度尾部站点" v-model="tailVisible" width="1100px" align-center :close-on-click-modal="false" :close-on-press-escape="false" @close="handleCloseDialog">
      <el-form :model="form" inline>
        <el-form-item label="时间范围">
          <el-date-picker v-model="datePicker" type="daterange" @change="handleDateChange" value-format="x" unlink-panels :shortcuts="shortcuts" :range-separator="$t('common.pp_to')" :clearable="false" :disabledDate="getDisabledDate" :prefix-icon="customPrefix">
            <template #range-separator>
              <TimeRangeIcon style="margin: 0 5px" />
            </template>
          </el-date-picker>
        </el-form-item>
        <el-form-item label="设备名称" prop="device_id">
          <el-select v-model="form.device_id" @change="handleDeviceChange" filterable remote clearable placeholder="请输入设备ID或名称" class="width-full" :remote-method="remoteMethod" :loading="remoteLoading">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="下发工单" prop="is_patch_order">
          <el-select v-model="form.is_patch_order" @change="handlePatchOrderChange" filterable clearable placeholder="请选择" class="width-full">
            <el-option value="1" label="是" />
            <el-option value="2" label="否" />
          </el-select>
        </el-form-item>
      </el-form>

      <el-table :data="list" @sort-change="handleSortChange" v-loading="loading" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="device_id" label="设备ID" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.device_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="description" label="设备名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="health_score" label="总体健康度" sortable="custom" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.health_score.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sensor_health_score" label="传感器健康度" sortable="custom" min-width="140" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.sensor_health_score.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="charge_health_score" label="充电模块健康度" sortable="custom" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.charge_health_score.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="servo_health_score" label="伺服健康度" sortable="custom" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.servo_health_score.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="owner" label="运维owner" :width="ownerList.length > 0 ? flexColumnWidth(ownerList, 74, 8) : 100" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex-box flex_a_i-center">
              <span v-if="row.owner == '/' || !row.owner">{{ row.owner || '-' }}</span>
              <div class="flex-box flex_a_i-center avatar-container" v-else>
                <el-avatar size="small" :src="row.owner_avatar_url" />
                <span class="margin_l-8">{{ row.owner }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="is_patch_order" label="是否有下发工单" min-width="140" show-overflow-tooltip>
          <template #default="{ row }">
            <div :class="row.is_patch_order ? 'patch-order' : 'non-patch-order'">{{ row.is_patch_order ? '是' : '否' }}</div>
          </template>
        </el-table-column>
      </el-table>
      <div class="margin_t-20 flex-box flex_j_c-flex-end">
        <Page :page="pages" :pagerCount="5" :layout="'prev, pager, next, sizes'" @change="handlePageChange" class="mini-page" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, shallowRef, h } from 'vue'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { ElMessage } from 'element-plus'
import { apiGetDevices } from '~/apis/home'
import { apiGetStationData } from '~/apis/device-health'
import { shortcuts, getDisabledDate, getStartTimeOfDay, getFinalEndTime, removeNullKeys, flexColumnWidth } from '~/utils'
import { cloneDeep, debounce } from 'lodash-es'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'

const props = defineProps({
  tailVisible: Boolean,
  project: String,
  originDate: Array
})

const emits = defineEmits(['update:tailVisible'])

const loading = ref(false)
const remoteLoading = ref(false)
const sortKey = ref('')
const sortValue = ref('')
const list = ref([] as any)
const ownerList = ref([] as any)
const deviceOptions = ref([] as any)
const datePicker = ref([] as any)
const form = ref({
  device_id: '',
  is_patch_order: ''
})
const pages = ref(cloneDeep(page))
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})

/**
 * @description: 选择是否下发工单
 * @return {*}
 */
const handlePatchOrderChange = () => {
  pages.value.current = 1
  getList()
}

/**
 * @description: 表格排序
 * @param {*} data
 * @return {*}
 */
const handleSortChange = (data: any) => {
  sortKey.value = data.prop
  sortValue.value = data.order
  getList()
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: props.project, name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 关闭弹窗
 * @return {*}
 */
const handleCloseDialog = () => {
  emits('update:tailVisible', false)
}

/**
 * @description: 设备变更
 * @return {*}
 */
const handleDeviceChange = () => {
  pages.value.current = 1
  getList()
}

/**
 * @description: 时间变更
 * @return {*}
 */
const handleDateChange = () => {
  pages.value.current = 1
  getList()
}

/**
 * @description: 获取全量数据
 * @return {*}
 */
const getList = async () => {
  const params = {
    start_time: getStartTimeOfDay(datePicker.value[0]),
    end_time: getFinalEndTime(datePicker.value[1]),
    device_id: form.value.device_id,
    is_patch_order: form.value.is_patch_order == '1' ? true : false,
    page: pages.value.current,
    size: pages.value.size,
    sort: 'health_score',
    descending: false
  } as any
  if(!form.value.is_patch_order) delete params.is_patch_order
  if (sortValue.value) {
    params.sort = sortKey.value
    params.descending = sortValue.value == 'descending'
  }
  removeNullKeys(params)
  loading.value = true
  try {
    const res = await apiGetStationData(props.project!, params)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
      ownerList.value = [...new Set(list.value.filter((item: any) => item.owner).map((item: any) => item.owner))]
    }
  } catch (error) {
    loading.value = false
  }
}

onBeforeMount(() => {
  datePicker.value = props.originDate
  form.value.device_id = ''
  form.value.is_patch_order = ''
  pages.value.current = 1
  searchDeviceList('NIO')
  getList()
})
</script>

<style lang="scss" scoped>
.tail-dialog-container {
  :deep(.el-dialog__title) {
    color: #1f1f1f;
    font-size: 18px;
  }
  :deep(.el-dialog__body) {
    padding-top: 16px;
  }
  :deep(.el-form--inline .el-form-item) {
    margin-bottom: 20px;
    margin-right: 24px;
  }
  :deep(.el-form) {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr;
  }
  :deep(.avatar-container) {
    border-radius: 16px;
    background: #e5f9f9;
    padding: 2px 12px 2px 4px;
  }
  :deep(.patch-order),
  :deep(.non-patch-order) {
    width: 60px;
    border-radius: 2px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: #2f9c74;
    background-color: #e8fcea;
  }
  :deep(.non-patch-order) {
    color: #595959;
    background-color: #ebecee;
  }
}
</style>
