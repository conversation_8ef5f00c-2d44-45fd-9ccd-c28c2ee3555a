<template>
  <div class="revenue-detail-container">
    <div class="flex-box flex_a_i-center font-size-20 line-height-28 margin_b-20">
      <BackIcon @click="handleBack" class="cursor-pointer" />
      <span class="color-8c font-weight-480 margin_l-8">{{ $t('menu.pp_revenue_list') }}</span>
      <span class="margin-n-8">/</span>
      <span class="color-1f font-weight-450">{{ route.query.description || $t('common.pp_unnamed_device') }}</span>
    </div>

    <div class="flex_a_i-center gap_8 margin_b-20" style="display: inline-flex">
      <span class="font-size-14 line-height-22 color-59">{{ $t('common.pp_time_frame') }}</span>
      <el-date-picker v-model="datePicker" type="daterange" value-format="x" unlink-panels :shortcuts="getYesterdayShortcuts()" :clearable="false" :disabledDate="getDisabledYesterdayDate" :prefix-icon="customPrefix">
        <template #range-separator>
          <TimeRangeIcon style="margin: 0 5px" />
        </template>
      </el-date-picker>
      <el-button @click="getList(true)" class="welkin-primary-button margin_l-16" :loading="loading">{{ $t('common.pp_search') }}</el-button>
    </div>

    <div class="loading-container" v-if="loading">
      <div class="loader-17"></div>
    </div>

    <div class="flex-box flex_d-column gap_16" v-else>
      <div class="line-grid">
        <div class="content-card">
          <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-6">
            <span class="font-size-14 line-height-22 color-26 font-weight-bold">{{ $t('stationManagement.pp_total_revenue') }}</span>
            <div class="flex-box flex_a_i-center gap_4">
              <CalendarIcon />
              <span class="font-size-14 color-59">{{ formatTimeToDay(Number(route.query.day)) }}</span>
            </div>
          </div>
          <div class="color-a0 font-size-32 line-height-32 margin_b-24">{{ isEmptyData(list.total_revenue) ? '-' : list.total_revenue.toLocaleString() + '元' }}</div>
          <div class="consumption-data">
            <div class="flex-box flex_d-column">
              <OffPeakIcon class="margin_b-6" />
              <span class="font-size-12 line-height-18 color-59">{{ $t('stationManagement.pp_off_peak_revenue') }}</span>
              <div class="flex-box gap_6">
                <span class="font-size-24 line-height-38 color-26">{{ isEmptyData(list.off_peak_revenue) ? '-' : list.off_peak_revenue.toLocaleString() }}</span>
                <div v-if="!isEmptyData(list.off_peak_revenue_change)" class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6" :style="{ color: list.off_peak_revenue_change >= 0 ? '#01A0AC' : '#F83535' }">
                  <span>{{ $t('stationManagement.pp_today') }}&nbsp;</span>
                  <span v-if="list.off_peak_revenue_change >= 0">+</span>
                  <span>{{ list.off_peak_revenue_change.toLocaleString() }}</span>
                </div>
                <div v-else class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6 color-8c">{{ $t('stationManagement.pp_no_data_from_yesterday') }}</div>
              </div>
            </div>
            <div class="flex-box flex_d-column">
              <BatteryIcon class="margin_b-6" />
              <span class="font-size-12 line-height-18 color-59">{{ $t('stationManagement.pp_battery_revenue') }}</span>
              <div class="flex-box gap_6">
                <span class="font-size-24 line-height-38 color-26">{{ isEmptyData(list.battery_maintenance_revenue) ? '-' : list.battery_maintenance_revenue.toLocaleString() }}</span>
                <div v-if="!isEmptyData(list.battery_maintenance_revenue_change)" class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6" :style="{ color: list.battery_maintenance_revenue_change >= 0 ? '#01A0AC' : '#F83535' }">
                  <span>{{ $t('stationManagement.pp_today') }}&nbsp;</span>
                  <span v-if="list.battery_maintenance_revenue_change >= 0">+</span>
                  <span>{{ list.battery_maintenance_revenue_change.toLocaleString() }}</span>
                </div>
                <div v-else class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6 color-8c">{{ $t('stationManagement.pp_no_data_from_yesterday') }}</div>
              </div>
            </div>
            <div class="flex-box flex_d-column">
              <EnergyIcon class="margin_b-6" />
              <span class="font-size-12 line-height-18 color-59">{{ $t('stationManagement.pp_energy_revenue') }}</span>
              <div class="flex-box gap_6">
                <span class="font-size-24 line-height-38 color-26">{{ isEmptyData(list.energy_revenue) ? '-' : list.energy_revenue.toLocaleString() }}</span>
                <div v-if="!isEmptyData(list.energy_revenue_change)" class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6" :style="{ color: list.energy_revenue_change >= 0 ? '#01A0AC' : '#F83535' }">
                  <span>{{ $t('stationManagement.pp_today') }}&nbsp;</span>
                  <span v-if="list.energy_revenue_change >= 0">+</span>
                  <span>{{ list.energy_revenue_change.toLocaleString() }}</span>
                </div>
                <div v-else class="font-size-12 line-height-20 flex-box flex_a_i-flex-end padding_b-6 color-8c">{{ $t('stationManagement.pp_no_data_from_yesterday') }}</div>
              </div>
            </div>
          </div>
          <div class="font-size-14 line-height-22 color-26 padding_t-12">
            <span>{{ $t('stationManagement.pp_station_suggestion') }}：</span>
            <span>{{ list.suggestion || '-' }}</span>
          </div>
        </div>

        <div class="content-card">
          <div id="revenueChart1" class="width-full height-full" v-show="hasRevenueChart1"></div>
          <div v-if="!hasRevenueChart1" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('stationManagement.pp_revenue_trend') }}</div>
            <el-empty :description="$t('common.pp_image_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyImageIcon />
              </template>
            </el-empty>
          </div>
        </div>
      </div>

      <div class="line-grid">
        <div class="content-card">
          <div id="revenueChart2" class="width-full height-full" v-show="hasRevenueChart2"></div>
          <div v-if="!hasRevenueChart2" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('stationManagement.pp_battery') }}</div>
            <el-empty :description="$t('common.pp_image_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyImageIcon />
              </template>
            </el-empty>
          </div>
        </div>

        <div class="content-card">
          <div id="revenueChart3" class="width-full height-full" v-show="hasRevenueChart3"></div>
          <div v-if="!hasRevenueChart3" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('stationManagement.pp_off_peak') }}</div>
            <el-empty :description="$t('common.pp_image_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyImageIcon />
              </template>
            </el-empty>
          </div>
        </div>
      </div>

      <div class="line-grid">
        <div class="content-card" style="grid-column: span 2">
          <div id="revenueChart4" class="width-full height-full" v-show="hasRevenueChart4"></div>
          <div v-if="!hasRevenueChart4" class="width-full height-full">
            <div class="font-size-14 font-weight-bold color-26">{{ $t('stationManagement.pp_station_consumption') }}</div>
            <el-empty :description="$t('common.pp_image_empty')" class="width-full height-full">
              <template #image>
                <WhiteEmptyImageIcon />
              </template>
            </el-empty>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, shallowRef, h, watch, computed, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { apiGetRevenueDetail } from '~/apis/station-management'
import { ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { formatTime, lineBarOptionMock, multilineOptionMock, barOptionMock, formatterTooltip, allOtherPropsNull } from './constant'
import { getYesterdayShortcuts, getDisabledYesterdayDate, getStartTimeOfDay, getFinalEndTime, formatTimeToDay, isEmptyData } from '~/utils'
import BackIcon from '~/views/single-station/icon/back-icon.vue'
import DatePrefix from '~/assets/svg/date-prefix.vue'
import TimeRangeIcon from '~/assets/svg/time-range.vue'
import WhiteEmptyImageIcon from '~/assets/svg/white-empty-image.vue'
import CalendarIcon from './icon/calendar.vue'
import OffPeakIcon from './icon/off-peak.vue'
import BatteryIcon from './icon/battery-icon.vue'
import EnergyIcon from './icon/energy-icon.vue'
import * as echarts from 'echarts'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const loading = ref(false)
const hasRevenueChart1 = ref(false)
const hasRevenueChart2 = ref(false)
const hasRevenueChart3 = ref(false)
const hasRevenueChart4 = ref(false)
const list = ref({} as any)
const datePicker = ref([] as any)
const echartsArr = ref([] as any)
const customPrefix = shallowRef({
  render() {
    return h(DatePrefix)
  }
})
const chartOption1 = cloneDeep(multilineOptionMock)
const chartOption2 = cloneDeep(lineBarOptionMock)
const chartOption3 = cloneDeep(barOptionMock)
const chartOption4 = cloneDeep(multilineOptionMock)
const isCollapse = computed(() => store.state.menus.collapse)

const echartRender = (chartId: string, option: any) => {
  let chartDom = document.getElementById(chartId)!
  let myChart = echarts.init(chartDom)
  option && myChart.setOption(option)
  echartsArr.value.push(myChart)
  window.addEventListener('resize', () => myChart.resize())
}
const setCharts = () => {
  echartsArr.value = []
  if (hasRevenueChart1.value) echartRender('revenueChart1', chartOption1)
  if (hasRevenueChart2.value) echartRender('revenueChart2', chartOption2)
  if (hasRevenueChart3.value) echartRender('revenueChart3', chartOption3)
  if (hasRevenueChart4.value) echartRender('revenueChart4', chartOption4)
}

/**
 * @description: 侧边栏折叠展开时echarts自适应
 * @return {*}
 */
watch(
  () => isCollapse.value,
  (newVal, oldVal) => {
    const timer = setTimeout(() => {
      echartsArr.value.map((item: any) => item.resize())
      clearTimeout(timer)
    }, 300)
  }
)

/**
 * @description: 回到上级页面
 * @return {*}
 */
const handleBack = () => {
  let query: any = sessionStorage.getItem('revenue-list')
  router.push({
    path: '/station-management/revenue-list',
    query: JSON.parse(query)
  })
}

const formatData = (data: any) => {
  if (data.revenue_trend && data.revenue_trend.length > 0 && !allOtherPropsNull(data.revenue_trend)) {
    hasRevenueChart1.value = true
    chartOption1.title.text = t('stationManagement.pp_revenue_trend')
    chartOption1.legend.data[0].name = t('stationManagement.pp_off_peak')
    chartOption1.legend.data[1].name = t('stationManagement.pp_energy')
    chartOption1.legend.data[2].name = t('stationManagement.pp_battery')
    chartOption1.xAxis.data = data.revenue_trend.map((item: any) => formatTime(item.day))
    chartOption1.series[0].name = t('stationManagement.pp_off_peak')
    chartOption1.series[1].name = t('stationManagement.pp_energy')
    chartOption1.series[2].name = t('stationManagement.pp_battery')
    chartOption1.series[0].data = data.revenue_trend.map((item: any) => item.off_peak_revenue)
    chartOption1.series[1].data = data.revenue_trend.map((item: any) => item.energy_revenue)
    chartOption1.series[2].data = data.revenue_trend.map((item: any) => item.battery_maintenance_revenue)
  } else {
    hasRevenueChart1.value = false
  }

  if (data.battery_maintenance_revenue_trend && data.battery_maintenance_revenue_trend.length > 0 && !allOtherPropsNull(data.battery_maintenance_revenue_trend)) {
    hasRevenueChart2.value = true
    chartOption2.title.text = t('stationManagement.pp_battery')
    chartOption2.xAxis.data = data.battery_maintenance_revenue_trend.map((item: any) => formatTime(item.day))
    chartOption2.series[0].data = data.battery_maintenance_revenue_trend.map((item: any) => item.battery_maintenance_percentage)
    chartOption2.series[1].data = data.battery_maintenance_revenue_trend.map((item: any) => item.battery_maintenance_times)
  } else {
    hasRevenueChart2.value = false
  }

  if (data.off_peak_revenue_trend && data.off_peak_revenue_trend.length > 0 && !allOtherPropsNull(data.off_peak_revenue_trend)) {
    hasRevenueChart3.value = true
    chartOption3.title.text = t('stationManagement.pp_off_peak')
    chartOption3.xAxis.data = data.off_peak_revenue_trend.map((item: any) => formatTime(item.day))
    chartOption3.series[0].data = data.off_peak_revenue_trend.map((item: any) => item.off_peak_revenue)
  } else {
    hasRevenueChart3.value = false
  }

  if (data.energy_revenue_trend && data.energy_revenue_trend.length > 0 && !allOtherPropsNull(data.energy_revenue_trend)) {
    hasRevenueChart4.value = true
    chartOption4.title.text = t('stationManagement.pp_station_consumption')
    chartOption4.legend.data[0].name = '充电节能（kWh/站/日）'
    chartOption4.legend.data[1].name = '水冷节能（kWh/站/日）'
    chartOption4.legend.data[2].name = '运营节能（kWh/站/日)'
    chartOption4.xAxis.data = data.energy_revenue_trend.map((item: any) => formatTime(item.day))
    chartOption4.series[0].name = '充电节能（kWh/站/日）'
    chartOption4.series[1].name = '水冷节能（kWh/站/日）'
    chartOption4.series[2].name = '运营节能（kWh/站/日)'
    chartOption4.series[0].data = data.energy_revenue_trend.map((item: any) => item.charge_revenue)
    chartOption4.series[1].data = data.energy_revenue_trend.map((item: any) => item.water_revenue)
    chartOption4.series[2].data = data.energy_revenue_trend.map((item: any) => item.operation_revenue)
    chartOption4.tooltip.formatter = formatterTooltip
  } else {
    hasRevenueChart4.value = false
  }
  nextTick(() => setCharts())
}

const getList = async (updateRoute = true) => {
  const params = {
    start_time: getStartTimeOfDay(datePicker.value[0]),
    end_time: getFinalEndTime(datePicker.value[1]),
    day: Number(route.query.day)
  }
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: { ...route.query, ...params }
    })
  }
  loading.value = true
  try {
    const res = await apiGetRevenueDetail(params, route.query.project, route.query.device_id)
    loading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      formatData(list.value)
    }
  } catch (error: any) {
    loading.value = false
  }
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  datePicker.value = initParams.start_time ? [Number(initParams.start_time), Number(initParams.end_time)] : [Number(initParams.day) - 3600 * 1000 * 24 * 7, Number(initParams.day)]
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.revenue-detail-container {
  font-family: 'Blue Sky Standard';
  padding: 24px;
  background: linear-gradient(180deg, #eef6f8 -6.51%, #f4f5f8 12.89%);
  .line-grid {
    display: grid;
    grid-template-columns: minmax(360px, 1fr) minmax(360px, 1fr);
    gap: 16px;
  }
  .content-card {
    width: 100%;
    min-height: 257px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dcf2f3;
    padding: 24px;
    display: flex;
    flex-direction: column;
    .consumption-data {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;
      border-bottom: 1px solid #dcf2f3;
    }
  }
  .loading-container {
    height: calc(100vh - 150px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.el-empty) {
    padding: 0;
    .el-empty__image {
      width: 120px;
    }
  }
  :deep(.el-date-editor .el-range-input) {
    color: #262626;
  }
  :deep(.el-range-editor.el-input__wrapper) {
    width: 336px;
  }
}
</style>
