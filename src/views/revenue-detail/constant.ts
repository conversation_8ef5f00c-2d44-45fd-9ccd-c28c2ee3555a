export const formatTime = (timestamp: any) => {
  if (!timestamp) return '-'
  const timeDate = new Date(timestamp)
  const y = timeDate.getFullYear()
  const m = timeDate.getMonth() + 1
  const d = timeDate.getDate()
  const x = y.toString().slice(-2) + '/' + (m < 10 ? '0' + m : m) + '/' + (d < 10 ? '0' + d : d)
  return x
}

export const multilineOptionMock = {
  animation: false,
  color: ['#0BC0C5', '#6697FF', '#FFC031'],
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    padding: 0,
    right: 0,
    top: 3,
    textStyle: {
      color: '#595959'
    },
    itemWidth: 14,
    itemHeight: 8,
    itemGap: 16,
    data: [
      { name: '', icon: 'path://M11.9691 3.50008C11.7231 1.52689 10.0398 0 8 0C5.96016 0 4.27695 1.52689 4.03094 3.50008H0.5C0.223858 3.50008 0 3.72394 0 4.00008C0 4.27622 0.223858 4.50008 0.5 4.50008H4.03096C4.27704 6.4732 5.96022 8 8 8C10.0398 8 11.723 6.4732 11.969 4.50008H15.5C15.7761 4.50008 16 4.27622 16 4.00008C16 3.72394 15.7761 3.50008 15.5 3.50008H11.9691ZM11 4C11 5.65685 9.65685 7 8 7C6.34315 7 5 5.65685 5 4C5 2.34315 6.34315 1 8 1C9.65685 1 11 2.34315 11 4Z' },
      { name: '', icon: 'path://M11.9691 3.50008C11.7231 1.52689 10.0398 0 8 0C5.96016 0 4.27695 1.52689 4.03094 3.50008H0.5C0.223858 3.50008 0 3.72394 0 4.00008C0 4.27622 0.223858 4.50008 0.5 4.50008H4.03096C4.27704 6.4732 5.96022 8 8 8C10.0398 8 11.723 6.4732 11.969 4.50008H15.5C15.7761 4.50008 16 4.27622 16 4.00008C16 3.72394 15.7761 3.50008 15.5 3.50008H11.9691ZM11 4C11 5.65685 9.65685 7 8 7C6.34315 7 5 5.65685 5 4C5 2.34315 6.34315 1 8 1C9.65685 1 11 2.34315 11 4Z' },
      { name: '', icon: 'path://M11.9691 3.50008C11.7231 1.52689 10.0398 0 8 0C5.96016 0 4.27695 1.52689 4.03094 3.50008H0.5C0.223858 3.50008 0 3.72394 0 4.00008C0 4.27622 0.223858 4.50008 0.5 4.50008H4.03096C4.27704 6.4732 5.96022 8 8 8C10.0398 8 11.723 6.4732 11.969 4.50008H15.5C15.7761 4.50008 16 4.27622 16 4.00008C16 3.72394 15.7761 3.50008 15.5 3.50008H11.9691ZM11 4C11 5.65685 9.65685 7 8 7C6.34315 7 5 5.65685 5 4C5 2.34315 6.34315 1 8 1C9.65685 1 11 2.34315 11 4Z' }
    ]
  },
  grid: {
    top: '40',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    data: []
  },
  yAxis: [
    {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    }
  ],
  series: [
    {
      name: '',
      type: 'line',
      lineStyle: {
        width: 1.5
      },
      stack: 'revenue',
      stackStrategy: 'all',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(0, 190, 190, 0)'
            },
            {
              offset: 1,
              color: 'rgba(0, 190, 190, 0.15)'
            }
          ],
          global: false
        }
      },
      data: []
    },
    {
      name: '',
      type: 'line',
      lineStyle: {
        width: 1.5
      },
      stack: 'revenue',
      stackStrategy: 'all',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(102, 151, 255, 0)'
            },
            {
              offset: 1,
              color: 'rgba(102, 151, 255, 0.15)'
            }
          ],
          global: false
        }
      },
      data: []
    },
    {
      name: '',
      type: 'line',
      lineStyle: {
        width: 1.5
      },
      stack: 'revenue',
      stackStrategy: 'all',
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(255, 192, 49, 0)'
            },
            {
              offset: 1,
              color: 'rgba(255, 192, 49, 0.15)'
            }
          ],
          global: false
        }
      },
      data: []
    }
  ]
} as any

export const lineBarOptionMock = {
  animation: false,
  color: ['#7FD9DE', '#00BEBE'],
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    padding: 0,
    right: 0,
    top: 3,
    textStyle: {
      color: '#595959'
    },
    itemWidth: 14,
    itemHeight: 8,
    itemGap: 20,
    data: [
      { name: '电池保养比例', icon: 'path://M11.9691 3.50008C11.7231 1.52689 10.0398 0 8 0C5.96016 0 4.27695 1.52689 4.03094 3.50008H0.5C0.223858 3.50008 0 3.72394 0 4.00008C0 4.27622 0.223858 4.50008 0.5 4.50008H4.03096C4.27704 6.4732 5.96022 8 8 8C10.0398 8 11.723 6.4732 11.969 4.50008H15.5C15.7761 4.50008 16 4.27622 16 4.00008C16 3.72394 15.7761 3.50008 15.5 3.50008H11.9691ZM11 4C11 5.65685 9.65685 7 8 7C6.34315 7 5 5.65685 5 4C5 2.34315 6.34315 1 8 1C9.65685 1 11 2.34315 11 4Z' },
      { name: '总保养块数', icon: 'circle' }
    ]
  },
  grid: {
    top: '40',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    data: [],
    axisPointer: {
      type: 'shadow'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    }
  },
  yAxis: [
    {
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C',
        formatter: '{value}%'
      }
    },
    {
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    }
  ],
  series: [
    {
      name: '电池保养比例',
      type: 'line',
      tooltip: {
        valueFormatter: (value: any) => (value === undefined ? '-' : value + '%')
      },
      data: [],
      lineStyle: {
        width: 1.5
      }
    },
    {
      name: '总保养块数',
      type: 'bar',
      barMaxWidth: 24,
      yAxisIndex: 1,
      data: []
    }
  ]
} as any

export const barOptionMock = {
  animation: false,
  color: ['#00BEBE'],
  title: {
    text: '',
    left: 'left',
    padding: 0,
    textStyle: {
      color: '#262626',
      fontSize: 14
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line'
    },
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
  legend: {
    padding: 0,
    right: 0,
    top: 3,
    textStyle: {
      color: '#595959'
    },
    itemWidth: 14,
    itemHeight: 8,
    itemGap: 20,
    data: [{ name: '实际错峰收益', icon: 'circle' }]
  },
  grid: {
    top: '40',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#D9D9D9'
      }
    },
    axisLabel: {
      show: true,
      color: '#8C8C8C'
    },
    data: [],
    axisPointer: {
      type: 'shadow'
    },
    axisLine: {
      lineStyle: {
        color: '#D9D9D9'
      }
    }
  },
  yAxis: [
    {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#D9D9D9']
        }
      },
      axisLabel: {
        show: true,
        color: '#8C8C8C'
      }
    }
  ],
  series: [
    {
      name: '实际错峰收益',
      type: 'bar',
      barMaxWidth: 24,
      data: []
    }
  ]
} as any

export const formatterTooltip = (params: any) => {
  let leftSide = ''
  let rightSide = ''
  let total = 0
  params.forEach((item: any) => {
    total += item.data === undefined || item.data === null ? 0 : item.data
  })
  leftSide += `<span style="margin-right: 30px; color: #01A0AC">总计节约（kWh）</span>`
  rightSide += `<span style="color: #01A0AC; display: flex; justify-content: flex-end">${total.toFixed(2)}（100.00%）</span>`
  params.forEach((item: any, index: number) => {
    const percent = total === 0 ? '0.00%' : ((item.data / total) * 100).toFixed(2) + '%'
    leftSide += `<span>
      <span class="chart-tooltip-color" style="display: inline-block; background-color: ${item.color}; border-radius: 50%; width: 10px; height: 10px; margin-right: 5px"></span>
      <span style="margin-right: 30px; color: #262626"> ${item.seriesName.match(/^(.*?)\（/)[1]} </span>
      </span>`
    rightSide += `<span style="display: flex; justify-content: flex-end">
      <span style="color: #262626"> ${item.data === null ? '-' : item.data} </span>
      <span style="color: #262626"> （${percent}） </span>
      </span>`
  })
  let tip = `<div style="margin-bottom: 8px; color: #262626">${params[0].axisValueLabel}</div>
  <div style="display: flex; justify-content: flex-between">
              <div style="display: flex; flex-direction: column; gap: 8px">${leftSide}</div>
              <div style="display: flex; flex-direction: column; gap: 8px">${rightSide}</div>
            </div>`
  return tip
}

export const allOtherPropsNull = (array: any) => {
  return array.every((item: any) => {
    return Object.keys(item)
      .filter((key) => key !== 'day')
      .every((key) => item[key] === null)
  })
}
