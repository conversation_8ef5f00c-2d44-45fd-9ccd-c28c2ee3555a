<template>
  <div class="power-grid-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('orderList.pp_power_grid') }}</el-breadcrumb-item>
          <el-breadcrumb-item @click="handleChangeBreadcrumb">{{ $t('orderList.pp_order_list') }}</el-breadcrumb-item>
          <el-breadcrumb-item v-if="showMore">{{ showMoreTitle }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('orderList.pp_order_list') }}</div>
      </div>
      <div class="header-right">
        <el-radio-group v-model="form.device_type" @change="handleChangeType" class="margin_r-40">
          <el-radio :label="item.value" v-for="item in deviceTypeOptions">{{ $t(item.label) }}</el-radio>
        </el-radio-group>
        <el-button class="welkin-secondary-button" v-if="hasPermission('function:power-grid:reserve-order')" @click="handleOpenHeadDialog(0)">{{ $t('orderList.pp_reserve_order') }}</el-button>
        <el-button class="welkin-primary-button" v-if="hasPermission('function:power-grid:bid-order')" @click="handleOpenHeadDialog(1)">{{ $t('orderList.pp_bid_order') }}</el-button>
      </div>
    </div>

    <div class="swap-page-container">
      <div class="search-container" v-if="!showMore">
        <el-form ref="formRef" :inline="true" class="search-form width-full" label-width="100px" label-position="left">
          <el-form-item :label="$t('orderList.pp_order_name') + ': '" style="width: 45%">
            <el-input v-model="form.name" style="width: 100%" :placeholder="$t('orderList.pp_enter_order_name')" clearable />
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_order_create_time') + ': '" style="width: 45%">
            <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :shortcuts="shortcuts" :disabledDate="getDisabledDate" />
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_order_type') + ': '" style="width: 45%">
            <el-select v-model="form.type" style="width: 100%" :placeholder="$t('orderList.pp_select_order_status')" clearable filterable>
              <el-option v-for="item in typeOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_order_status') + ': '" style="width: 30%">
            <el-select v-model="form.err_code" style="width: 100%" :placeholder="$t('orderList.pp_select_order_status')" clearable filterable>
              <el-option v-for="item in statusOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
            </el-select>
          </el-form-item>

          <el-form-item style="margin-right: 0">
            <el-button @click="handleSearch" class="welkin-primary-button">{{ $t('common.pp_search') }}</el-button>
            <el-button @click="handleReset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="swap-table-container" v-if="!showMore">
        <el-table
          :data="list"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
        >
          <el-table-column prop="name" :label="$t('orderList.pp_order_name')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div class="pointer-column ellipse">
                <span class="point-span" @click="handleViewStations(scope.row)">{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" :label="$t('orderList.pp_order_type')" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <span :style="{background: typeColorMap[scope.row.type], display: 'inline-block', borderRadius: '20px', padding: '0 6px', color: '#212121'}">&nbsp;{{ $t(typeMap[scope.row.type]) }}&nbsp;</span>
            </template>
          </el-table-column>
          <el-table-column prop="err_code" :label="$t('orderList.pp_order_status')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div class="status-container">
                <el-icon :size="18" color="#666" v-if="scope.row.err_code === 1"><RemoveFilled /></el-icon>
                <el-icon :size="18" color="#00B42A" v-if="scope.row.err_code === 4 || scope.row.err_code === 7"><SuccessFilled /></el-icon>
                <el-icon :size="18" color="#F53F3F" v-if="scope.row.err_code === 2 || scope.row.err_code === 5"><CircleCloseFilled /></el-icon>
                <span v-if="scope.row.err_code === 3 || scope.row.err_code === 6"> <LoadingSvg /> </span>
                <span class="status-text">{{ $t(statusMap[scope.row.err_code]) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" :label="$t('orderList.pp_order_create_time')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              {{ formatTime(scope.row.create_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="command_push_time" :label="$t('orderList.pp_order_pushtime')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              {{ formatTime(scope.row.command_push_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="push_finish_time" :label="$t('orderList.pp_order_finish_time')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              {{ formatTime(scope.row.push_finish_time) }}
            </template>
          </el-table-column>
          <el-table-column width="200" class-name="operation-column">
            <template #header>
              <el-tooltip effect="dark" :content="`${$t('orderList.pp_read_only')}`" placement="top-start">
                <div class="flex-box flex_a_i-center">
                  <span>{{ $t('common.pp_operation') }}</span>
                  <el-icon :size="18" color="#292C33"><InfoFilled /></el-icon>
                </div>
              </el-tooltip>
            </template>
            <template #default="scope">
              <div class="flex-box gap_6">
                <span class="welkin-operation-button" @click="handleView(scope.row)">{{ $t('common.pp_view') }}</span>
                <span class="welkin-operation-button" @click="handleEdit(scope.row)" :disabled="([3, 4, 5, 6, 7].includes(scope.row.err_code) || userId !== scope.row.creator) && !hasPermission('function:power-grid:super')" v-if="hasPermission('function:power-grid:edit')">{{ $t('common.pp_edit') }}</span>
                <span class="welkin-operation-button" @click="handlePush(scope.row)" :disabled="([3, 4, 5, 6, 7].includes(scope.row.err_code) || userId !== scope.row.creator) && !hasPermission('function:power-grid:super')" v-if="hasPermission('function:power-grid:edit')">{{ $t('common.pp_push') }}</span>
                <span class="welkin-operation-button" @click="handleRevoke(scope.row)" :disabled="([1, 2, 3, 6, 7].includes(scope.row.err_code) || userId !== scope.row.creator) && !hasPermission('function:power-grid:super')" v-if="hasPermission('function:power-grid:edit')">{{ $t('common.pp_revoke') }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>

      <div class="flex-box flex_a_i-center font-size-18 margin_b-20 width-70 cursor-pointer" v-if="showMore" @click="handleChangeBreadcrumb">
        <Icon :icon="iconMap['back']" />
        <span class="margin_l-5">{{ $t('common.pp_back') }}</span>
      </div>
      <div class="swap-table-container" v-if="showMore">
        <el-table
          :data="stationList"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
          v-if="showMore"
        >
          <el-table-column prop="device_id" :label="$t('deviceManagement.pp_device_id')" min-width="220">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>

          <el-table-column prop="description" :label="`${$t('deviceManagement.pp_device_name')}`" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div class="pointer-column ellipse">
                <span class="point-span" @click="handleJumpToSingleStation(scope.$index, scope.row)">
                  {{ scope.row.description }}
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="err_code" :label="$t('orderList.pp_order_status')" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <div class="status-container">
                <el-icon :size="18" color="#666" v-if="scope.row.err_code === 1"><RemoveFilled /></el-icon>
                <el-icon :size="18" color="#00B42A" v-if="scope.row.err_code === 4 || scope.row.err_code === 7"><SuccessFilled /></el-icon>
                <el-icon :size="18" color="#F53F3F" v-if="scope.row.err_code === 2 || scope.row.err_code === 5"><CircleCloseFilled /></el-icon>
                <span v-if="scope.row.err_code === 3 || scope.row.err_code === 6"> <LoadingSvg /> </span>
                <el-popover v-if="scope.row.err_code === 2 || scope.row.err_code === 5" placement="top-start" :title="`${$t('orderList.pp_fail_reason')}`" :width="200" trigger="hover" :content="scope.row.fail_reason">
                  <template #reference>
                    <span class="status-text light-text">{{ $t(statusMap[scope.row.err_code]) }}</span>
                  </template>
                </el-popover>
                <span class="status-text" v-else>{{ $t(statusMap[scope.row.err_code]) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="command_push_time" :label="$t('orderList.pp_order_pushtime')" min-width="160" show-overflow-tooltip v-if="![5, 6, 7].includes(stationErrCode)">
            <template #default="scope">
              {{ formatTime(scope.row.command_push_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="push_finish_time" :label="$t('orderList.pp_order_finish_time')" min-width="160" show-overflow-tooltip v-if="![5, 6, 7].includes(stationErrCode)">
            <template #default="scope">
              {{ formatTime(scope.row.push_finish_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="command_revoke_time:" :label="$t('orderList.pp_revoke_start')" min-width="160" show-overflow-tooltip v-if="[5, 6, 7].includes(stationErrCode)">
            <template #default="scope">
              {{ formatTime(scope.row.command_revoke_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="revoke_finish_time" :label="$t('orderList.pp_revoke_end')" min-width="160" show-overflow-tooltip v-if="[5, 6, 7].includes(stationErrCode)">
            <template #default="scope">
              {{ formatTime(scope.row.revoke_finish_time) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 电池预留和竞标指令 -->
      <el-dialog :title="headDialogTitle" width="600px" v-model="headDialogVisible" @close="handleCloseHeadDialog" :close-on-click-modal="false">
        <el-form ref="headFormRef" label-position="left" style="margin-top: 15px" :rules="rules" :model="headForm" label-width="140px">
          <el-form-item :label="$t('orderList.pp_order_name') + ': '" prop="name">
            <el-input v-model="headForm.name" :placeholder="$t('common.pp_please_input')" clearable />
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_order_device') + ': '" prop="device_ids">
            <el-select v-model="headForm.device_ids" style="width: 100%" :placeholder="$t('common.pp_please_select')" multiple clearable filterable>
              <el-option v-for="item in deviceOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_battery_res_time') + ': '" prop="date_picker">
            <el-date-picker v-model="headForm.date_picker" type="datetimerange" unlink-panels :range-separator="`${$t('common.pp_to')}`" :start-placeholder="$t('orderList.pp_res_t_start')" :end-placeholder="$t('orderList.pp_res_t_end')" />
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_max_capacity') + ': '" prop="winning_bid_up_capacity" v-if="!isReserve">
            <el-input v-model="headForm.winning_bid_up_capacity" oninput="value=value.replace(/[^0-9.]/g,'')" :placeholder="$t('common.pp_please_input')">
              <template #append>kW</template>
            </el-input>
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_price') + ': '" prop="winning_bid_price" v-if="!isReserve">
            <el-input v-model="headForm.winning_bid_price" oninput="value=value.replace(/[^0-9.]/g,'')" :placeholder="$t('common.pp_please_input')">
              <template #append>€/MW</template>
            </el-input>
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_battery_type') + ': '" prop="battery_model">
            <el-select v-model="headForm.battery_model" style="width: 100%" :placeholder="$t('common.pp_please_select')" multiple clearable filterable>
              <el-option v-for="item in batteryModelOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>

          <el-form-item
            v-for="(item, index) in headForm.battery_model"
            :key="`${item}_number`"
            :label="`${headForm.battery_model[index]}` + $t('orderList.pp_battery_res_num') + ': '"
            :prop="`${item}_number`"
            :rules="{
              required: true,
              trigger: 'blur',
              validator: (rule, value, cb) => batteryModelRule(rule, value, cb, index)
            }"
          >
            <el-input v-model="headForm.battery_num[index]" oninput="value=value.replace(/[^0-9.]/g,'')" :placeholder="$t('common.pp_please_input')" clearable>
              <template #append>{{ $t('orderList.pp_piece') }}</template>
            </el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="headDialogVisible = false" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
          <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="headLoading" @click="handleSubmitReserveForm(headFormRef)">{{ $t('common.pp_confirm') }}</el-button>
        </template>
      </el-dialog>

      <!-- 查看 -->
      <el-dialog v-model="viewDialogVisible" :title="$t('common.pp_view')" width="650px" :close-on-click-modal="false">
        <el-form ref="viewFormRef" label-position="left" style="margin-top: 15px" :model="viewForm" label-width="170px">
          <el-form-item :label="$t('orderList.pp_order_name') + ': '" prop="name">
            <el-input v-model="viewForm.name" disabled />
          </el-form-item>
          <el-form-item :label="$t('orderList.pp_order_user') + ': '" prop="creator">
            <el-input v-model="viewForm.creator" disabled />
          </el-form-item>
          <!-- <el-form-item :label="$t('orderList.pp_order_device') + ': '" prop="device_ids">
            <el-input v-model="item.device" v-for="item in viewForm.details" disabled />
          </el-form-item> -->
          <el-form-item :label="$t('orderList.pp_res_t_start') + ': '" prop="start_time_reserved">
            <el-input v-model="viewForm.start_time_reserved" disabled />
          </el-form-item>
          <el-form-item :label="$t('orderList.pp_res_t_end') + ': '" prop="end_time_reserved">
            <el-input v-model="viewForm.end_time_reserved" disabled />
          </el-form-item>
          <el-form-item :label="$t('orderList.pp_max_capacity') + ': '" prop="winning_bid_up_capacity" v-if="viewForm.type === 'win'">
            <el-input v-model="viewForm.winning_bid_up_capacity" disabled>
              <template #append>kW</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('orderList.pp_price') + ': '" prop="winning_bid_price" v-if="viewForm.type === 'win'">
            <el-input v-model="viewForm.winning_bid_price" disabled>
              <template #append>€/MW</template>
            </el-input>
          </el-form-item>
          <el-form-item v-for="(item, key) in viewForm.reserved_batteries" :label="key + 'kwh' + $t('orderList.pp_battery_res_num') + ': '">
            <el-input :placeholder="item + ''" disabled>
              <template #append>{{ $t('orderList.pp_piece') }}</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('orderList.pp_revoke_start') + ': '" prop="command_revoke_time" v-if="[5, 6, 7].includes(viewForm.err_code)">
            <el-input v-model="viewForm.command_revoke_time" disabled />
          </el-form-item>
          <el-form-item :label="$t('orderList.pp_revoke_end') + ': '" prop="revoke_finish_time" v-if="[5, 6, 7].includes(viewForm.err_code)">
            <el-input v-model="viewForm.revoke_finish_time" disabled />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button class="welkin-primary-button" @click="viewDialogVisible = false">{{ $t('common.pp_close') }}</el-button>
        </template>
      </el-dialog>

      <!-- 编辑 -->
      <el-dialog v-model="editDialogVisible" :title="$t('common.pp_edit')" width="600px" :close-on-click-modal="false">
        <el-form ref="editFormRef" label-position="left" style="margin-top: 15px" :rules="rules" :model="editForm" label-width="140px">
          <el-form-item :label="$t('orderList.pp_order_name') + ': '" prop="name">
            <el-input v-model="editForm.name" :placeholder="$t('common.pp_please_input')" clearable />
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_order_device') + ': '" prop="device_ids">
            <el-select v-model="editForm.device_ids" style="width: 100%" :placeholder="$t('common.pp_please_select')" multiple clearable filterable>
              <el-option v-for="item in deviceOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_battery_res_time') + ': '" prop="date_picker">
            <el-date-picker v-model="editForm.date_picker" type="datetimerange" unlink-panels :range-separator="`${$t('common.pp_to')}`" :start-placeholder="$t('orderList.pp_res_t_start')" :end-placeholder="$t('orderList.pp_res_t_end')" />
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_max_capacity') + ': '" prop="winning_bid_up_capacity" v-if="editForm.type === 'win'">
            <el-input v-model="editForm.winning_bid_up_capacity" oninput="value=value.replace(/[^0-9.]/g,'')" :placeholder="$t('common.pp_please_input')">
              <template #append>kW</template>
            </el-input>
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_price') + ': '" prop="winning_bid_price" v-if="editForm.type === 'win'">
            <el-input v-model="editForm.winning_bid_price" oninput="value=value.replace(/[^0-9.]/g,'')" :placeholder="$t('common.pp_please_input')">
              <template #append>€/MW</template>
            </el-input>
          </el-form-item>

          <el-form-item :label="$t('orderList.pp_battery_type') + ': '" prop="battery_model">
            <el-select v-model="editForm.battery_model" style="width: 100%" :placeholder="$t('common.pp_please_select')" multiple clearable filterable>
              <el-option v-for="item in batteryModelOptions" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </el-form-item>

          <el-form-item
            v-for="(item, index) in editForm.battery_model"
            :key="`${item}_number`"
            :label="`${editForm.battery_model[index]}` + $t('orderList.pp_battery_res_num') + ': '"
            :prop="`${item}_number`"
            :rules="{
              required: true,
              trigger: 'blur',
              validator: (rule, value, cb) => editBatteryModelRule(rule, value, cb, index)
            }"
          >
            <el-input v-model="editForm.battery_num[index]" oninput="value=value.replace(/[^0-9.]/g,'')" :placeholder="$t('common.pp_please_input')" clearable>
              <template #append>{{ $t('orderList.pp_piece') }}</template>
            </el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="editDialogVisible = false" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
          <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="editLoading" @click="handleSubmitEditForm(editFormRef)">{{ $t('common.pp_confirm') }}</el-button>
        </template>
      </el-dialog>

      <!-- 下发 -->
      <el-dialog v-model="pushDialogVisible" :title="$t('common.pp_push')" width="400px" :close-on-click-modal="false" align-center>
        <p class="dialog-tips">{{ $t('orderList.pp_push_confirm') }} {{ orderName + ' ? ' }}</p>
        <template #footer>
          <el-button @click="pushDialogVisible = false" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
          <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="pushLoading" @click="handleSubmitPushForm">{{ $t('common.pp_confirm') }}</el-button>
        </template>
      </el-dialog>

      <!-- 撤销 -->
      <el-dialog v-model="revokeDialogVisible" :title="$t('common.pp_revoke')" width="400px" :close-on-click-modal="false" align-center>
        <p class="dialog-tips">{{ $t('orderList.pp_revoke_confirm') }} {{ orderName + ' ? ' }}</p>
        <template #footer>
          <el-button @click="revokeDialogVisible = false" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
          <el-button class="welkin-primary-button" style="margin-left: 4px" :loading="revokeLoading" @click="handleSubmitRevokeForm">{{ $t('common.pp_confirm') }}</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, onBeforeMount, watch, onBeforeUnmount} from 'vue'
import {getShortcuts, getDisabledDate, removeNullProp, clearJson, formatTime, getUserId} from '~/utils'
import {page} from '~/constvars/page'
import {hasPermission, iconMap} from '~/auth'
import {useI18n} from 'vue-i18n'
import {useRoute, useRouter} from 'vue-router'
import {Icon} from '@iconify/vue/dist/iconify'
import {statusMap, typeMap, typeColorMap} from './constant'
import type {FormInstance} from 'element-plus'
import {ElMessage} from 'element-plus'
import {apiGetDeviceNameMap} from '~/apis/device-management'
import {SuccessFilled, CircleCloseFilled, RemoveFilled, InfoFilled} from '@element-plus/icons-vue'
import {apiGetAllStationList, apiPostCreateParams, apiPostEditParams, apiPostPushParams, apiPostRevokeParams} from '~/apis/order-list'
import _ from 'lodash'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const userId = getUserId()
const formRef = ref()
const headFormRef = ref()
const viewFormRef = ref()
const editFormRef = ref()
const getListTimeout = ref()
const showMore = ref(false)
const isReserve = ref(false)
const loading = ref(false)
const headLoading = ref(false)
const editLoading = ref(false)
const pushLoading = ref(false)
const revokeLoading = ref(false)
const headDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const editDialogVisible = ref(false)
const pushDialogVisible = ref(false)
const revokeDialogVisible = ref(false)
const datePicker = ref([] as any)
const stationErrCode = ref(-1)
const headDialogTitle = ref('')
const showMoreTitle = ref('')
const orderName = ref('')
const shortcuts = ref(getShortcuts())
const pages = ref(_.cloneDeep(page))
const list = ref([] as any)
const stationList = ref([] as any)
const deviceOptions = ref([] as any)
const batteryModelOptions = ref([
  {
    label: '70kWh',
    value: '70'
  },
  {
    label: '100kWh',
    value: '100'
  }
])
const typeOptions = ref([
  {
    label: 'orderList.pp_reserve_order',
    value: 'reserve'
  },
  {
    label: 'orderList.pp_bid_order',
    value: 'win'
  }
])
const statusOptions = ref([
  {
    label: 'orderList.pp_stat_1',
    value: 1
  },
  {
    label: 'orderList.pp_stat_2',
    value: 2
  },
  {
    label: 'orderList.pp_stat_3',
    value: 3
  },
  {
    label: 'orderList.pp_stat_4',
    value: 4
  },
  {
    label: 'orderList.pp_stat_5',
    value: 5
  },
  {
    label: 'orderList.pp_stat_6',
    value: 6
  },
  {
    label: 'orderList.pp_stat_7',
    value: 7
  }
])
const deviceTypeOptions = ref([
  {
    label: 'menu.pp_swap_station2',
    value: 'PowerSwap2'
  },
  {
    label: 'menu.pp_swap_station3',
    value: 'PUS3'
  }
])

const form = ref({
  name: '',
  start_time: '' as number | string,
  end_time: '' as number | string,
  err_code: '' as number | string,
  type: '',
  device_type: 'PowerSwap2'
})
const searchForm = ref({
  name: '',
  start_time: '' as number | string,
  end_time: '' as number | string,
  err_code: '' as number | string,
  type: '',
  device_type: 'PowerSwap2'
})
const headForm = ref({
  type: '',
  name: '',
  device_ids: [],
  date_picker: [] as any,
  battery_model: [] as any,
  battery_num: [] as any,
  winning_bid_up_capacity: '',
  winning_bid_price: ''
})
const viewForm = ref({} as any)
const editForm = ref({} as any)
const pushForm = ref({} as any)
const revokeForm = ref({} as any)

const rules = ref({
  name: [{required: true, message: t('orderList.pp_ms_required'), trigger: 'blur'}],
  device_ids: [{required: true, message: t('orderList.pp_device_required'), trigger: 'change'}],
  date_picker: [{required: true, message: t('orderList.pp_reserver_t_required'), trigger: 'change'}],
  battery_model: [{required: true, message: t('orderList.pp_battery_required'), trigger: 'change'}],
  winning_bid_up_capacity: [{required: true, message: t('orderList.pp_max_capacity_required'), trigger: 'blur'}],
  winning_bid_price: [{required: true, message: t('orderList.pp_price_required'), trigger: 'blur'}]
})

const batteryModelRule = (rule: any, value: any, callback: any, index: number) => {
  if (isNaN(headForm.value.battery_num[index]) || !headForm.value.battery_num[index]) {
    callback(new Error(t('orderList.pp_err_nullnum')))
  } else {
    callback()
  }
}

const editBatteryModelRule = (rule: any, value: any, callback: any, index: number) => {
  if (isNaN(editForm.value.battery_num[index]) || !editForm.value.battery_num[index]) {
    callback(new Error(t('orderList.pp_err_nullnum')))
  } else {
    callback()
  }
}

/**
 * @description: 打开预留电池/创建指令弹窗
 * @param {*} flag 0:预留电池，1:创建指令
 * @return {*}
 */
const handleOpenHeadDialog = (flag: number) => {
  headDialogTitle.value = flag ? t('orderList.pp_order_push') : t('orderList.pp_battery_reservation')
  isReserve.value = flag ? false : true
  headDialogVisible.value = true
}

/**
 * @description: 选择时间范围
 * @param {*} value
 * @return {*}
 */
const handleDateChange = (value: any) => {
  if (value) {
    form.value.start_time = value[0].getTime()
    form.value.end_time = value[1].getTime()
  } else {
    form.value.start_time = ''
    form.value.end_time = ''
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  const deviceTypeClone = form.value.device_type
  clearJson(form.value)
  form.value.device_type = deviceTypeClone
  datePicker.value = []
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = {...form.value}
  getList(true, 2)
}

/**
 * @description: 查看
 * @param {*} row
 * @return {*}
 */
const handleView = (row: any) => {
  viewForm.value = _.cloneDeep(row)
  viewForm.value.details.forEach((item: any) => (item.device = item.device_id + ' - ' + item.description))
  viewForm.value.start_time_reserved = formatTime(viewForm.value.start_time_reserved)
  viewForm.value.end_time_reserved = formatTime(viewForm.value.end_time_reserved)
  viewForm.value.command_revoke_time = formatTime(viewForm.value.command_revoke_time)
  viewForm.value.revoke_finish_time = formatTime(viewForm.value.revoke_finish_time)
  viewDialogVisible.value = true
}

/**
 * @description: 编辑
 * @param {*} row
 * @return {*}
 */
const handleEdit = (row: any) => {
  editForm.value = _.cloneDeep(row)
  editForm.value.device_ids = editForm.value.details.map((item: any) => item.device_id)
  editForm.value.date_picker = [new Date(editForm.value.start_time_reserved), new Date(editForm.value.end_time_reserved)]
  editForm.value.battery_model = Object.keys(editForm.value.reserved_batteries)
  editForm.value.battery_num = Object.values(editForm.value.reserved_batteries)
  editDialogVisible.value = true
}

/**
 * @description: 下发
 * @param {*} row
 * @return {*}
 */
const handlePush = (row: any) => {
  orderName.value = row.name
  pushForm.value = _.cloneDeep(row)
  pushForm.value.device_ids = pushForm.value.details.map((item: any) => item.device_id)
  pushDialogVisible.value = true
}

/**
 * @description: 撤销
 * @param {*} row
 * @return {*}
 */
const handleRevoke = (row: any) => {
  orderName.value = row.name
  revokeForm.value = _.cloneDeep(row)
  revokeForm.value.device_ids = revokeForm.value.details.map((item: any) => item.device_id)
  revokeDialogVisible.value = true
}

/**
 * @description: 查看每个站的信息
 * @param {*} row
 * @return {*}
 */
const handleViewStations = (row: any) => {
  showMoreTitle.value = row.name
  stationList.value = row.details
  stationErrCode.value = row.err_code
  showMore.value = true
}

/**
 * @description: 返回指令列表
 * @return {*}
 */
const handleChangeBreadcrumb = () => {
  showMore.value = false
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList(true, 2)
}

/**
 * @description: 关闭预留电池/创建指令弹窗
 * @return {*}
 */
const handleCloseHeadDialog = () => {
  headFormRef.value.resetFields()
  headForm.value.battery_num = []
}

/**
 * @description: 创建预留电池/竞标指令
 * @param {*} formEl
 * @return {*}
 */
const handleSubmitReserveForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid, fields) => {
    if (valid) {
      const headFormClone = _.cloneDeep(headForm.value)
      const params = {
        name: headFormClone.name,
        type: isReserve.value ? 'reserve' : 'win',
        device_ids: headFormClone.device_ids,
        start_time_reserved: headFormClone.date_picker[0].getTime(),
        end_time_reserved: headFormClone.date_picker[1].getTime(),
        reserved_batteries: {} as any,
        winning_bid_up_capacity: headFormClone.winning_bid_up_capacity as any,
        winning_bid_price: headFormClone.winning_bid_price as any
      }
      headFormClone.battery_model.map((item: any, index: number) => {
        params.reserved_batteries[item] = Number(headFormClone.battery_num[index])
      })
      if (isReserve.value) {
        delete params.winning_bid_up_capacity
        delete params.winning_bid_price
      } else {
        params.winning_bid_up_capacity = Number(params.winning_bid_up_capacity)
        params.winning_bid_price = Number(params.winning_bid_price)
      }
      headLoading.value = true
      try {
        const res = await apiPostCreateParams(form.value.device_type, params)
        if (!res.err_code) {
          ElMessage.success(t('orderList.pp_create_success'))
          headDialogVisible.value = false
          handleSearch()
        } else {
          ElMessage.success(t('orderList.pp_create_failed'))
        }
      } catch (error) {}
      headLoading.value = false
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 提交编辑表单
 * @param {*} formEl
 * @return {*}
 */
const handleSubmitEditForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid, fields) => {
    if (valid) {
      const editFormClone = _.cloneDeep(editForm.value)
      const params = {
        name: editFormClone.name,
        type: editFormClone.type,
        device_ids: editFormClone.device_ids,
        start_time_reserved: editFormClone.date_picker[0].getTime(),
        end_time_reserved: editFormClone.date_picker[1].getTime(),
        reserved_batteries: {} as any,
        winning_bid_up_capacity: editFormClone.winning_bid_up_capacity as any,
        winning_bid_price: editFormClone.winning_bid_price as any,
      }
      editFormClone.battery_model.map((item: any, index: number) => {
        params.reserved_batteries[item] = Number(editFormClone.battery_num[index])
      })
      if (editFormClone.type === 'reserve') {
        delete params.winning_bid_up_capacity
        delete params.winning_bid_price
      } else {
        params.winning_bid_up_capacity = Number(params.winning_bid_up_capacity)
        params.winning_bid_price = Number(params.winning_bid_price)
      }
      editLoading.value = true
      try {
        const res = await apiPostEditParams(form.value.device_type, editFormClone.command_id, params)
        if (!res.err_code) {
          ElMessage.success(t('orderList.pp_edit_success'))
          editDialogVisible.value = false
          getList(false, 2)
        } else {
          ElMessage.success(t('orderList.pp_edit_failed'))
        }
      } catch (error) {}
      editLoading.value = false
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * @description: 提交下发表单
 * @return {*}
 */
const handleSubmitPushForm = async () => {
  const pushFormClone = _.cloneDeep(pushForm.value)
  const params = {
    name: pushFormClone.name,
    type: pushFormClone.type,
    command_id: pushFormClone.command_id,
    device_ids: pushFormClone.device_ids,
    start_time_reserved: pushFormClone.start_time_reserved,
    end_time_reserved: pushFormClone.end_time_reserved,
    reserved_batteries: pushFormClone.reserved_batteries,
    winning_bid_up_capacity: pushFormClone.winning_bid_up_capacity as any,
    winning_bid_price: pushFormClone.winning_bid_price as any,
  }
  if (pushFormClone.type === 'reserve') {
    delete params.winning_bid_up_capacity
    delete params.winning_bid_price
  } else {
    params.winning_bid_up_capacity = Number(params.winning_bid_up_capacity)
    params.winning_bid_price = Number(params.winning_bid_price)
  }
  pushLoading.value = true
  try {
    const res = await apiPostPushParams(form.value.device_type, params)
    if (!res.err_code) {
      ElMessage.success(t('orderList.pp_push_success'))
      pushDialogVisible.value = false
    } else {
      ElMessage.success(t('orderList.pp_push_failed'))
    }
  } catch (error) {}
  pushLoading.value = false
}

/**
 * @description: 提交撤销表单
 * @return {*}
 */
const handleSubmitRevokeForm = async () => {
  const revokeFormClone = _.cloneDeep(revokeForm.value)
  const params = {
    type: revokeFormClone.type,
    command_id: revokeFormClone.command_id,
    device_ids: revokeFormClone.device_ids
  }
  revokeLoading.value = true
  try {
    const res = await apiPostRevokeParams(form.value.device_type, params)
    if (!res.err_code) {
      ElMessage.success(t('orderList.pp_revoke_success'))
      revokeDialogVisible.value = false
    } else {
      ElMessage.success(t('orderList.pp_revoke_failed'))
    }
  } catch (error) {}
  revokeLoading.value = false
}

/**
 * @description: 跳转到具体设备
 * @param {*} index
 * @param {*} row
 * @return {*}
 */
const handleJumpToSingleStation = (index: number, row: any) => {
  const project = form.value.device_type === 'PowerSwap2' ? 'powerSwap2' : 'powerSwap3'
  router.push({
    path: `/${project}/device-management/single-station/${row.device_id}`,
    query: {tab: 'order'}
  })
}

/**
 * @description: 切换二代站/三代站
 * @return {*}
 */
const handleChangeType = () => {
  getDeviceNameMap()
  handleSearch()
}

/**
 * @description: 获取设备列表
 * @return {*}
 */
const getDeviceNameMap = async () => {
  deviceOptions.value = []
  const res = await apiGetDeviceNameMap(form.value.device_type)
  res.data.map((item: any) => {
    deviceOptions.value.push({
      label: item.device_id + ' - ' + item.description,
      value: item.device_id
    })
  })
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @param {*} status // 轮询是否实时带筛选项
 * @return {*}
 */
const getList = async (updateRoute = true, status: number) => {
  if (loading.value) return
  clearTimeout(getListTimeout.value)
  let formData = {} as any
  if (status === 2) {
    formData = {...searchForm.value}
  } else {
    formData = {...form.value}
  }
  formData.page = pages.value.current
  formData.size = pages.value.size
  if (updateRoute) {
    router.push({
      path: `/power-grid/order-list`,
      query: {...removeNullProp(formData)}
    })
  }
  loading.value = true
  try {
    const res = await apiGetAllStationList(formData, formData.device_type)
    list.value = res.data
    pages.value.total = res.total
    getListTimeout.value = setTimeout(() => {
      getList(false, 2)
    }, 5000)
  } catch (error) {}
  loading.value = false
}

const stopWatch = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      let initParams: any = route.query
      if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
        form.value.start_time = Number(initParams.start_time)
        form.value.end_time = Number(initParams.end_time)
        datePicker.value[0] = Number(form.value.start_time)
        datePicker.value[1] = Number(form.value.end_time)
      }
      pages.value.current = !!initParams.page ? Number(initParams.page) : 1
      pages.value.size = !!initParams.size ? Number(initParams.size) : 10
      form.value.name = !!initParams.name ? initParams.name : ''
      form.value.err_code = !!initParams.err_code ? Number(initParams.err_code) : ''
      form.value.type = !!initParams.type ? initParams.type : ''
      form.value.device_type = !!initParams.device_type ? initParams.device_type : 'PowerSwap2'
      searchForm.value = {...form.value}
      getList(false, 2)
    }
  },
  {immediate: true}
)

/**
 * @description: 组件销毁卸载轮询，定时器清空, 停止监听路由
 * @return {*}
 */
onBeforeUnmount(() => {
  clearTimeout(getListTimeout.value)
  stopWatch()
})

onBeforeMount(() => {
  getDeviceNameMap()
})
</script>

<style lang="scss" scoped>
.power-grid-container {
  font-family: 'Noto Sans';
  :deep(.el-dialog__body) {
    padding: 20px !important;
  }
  :deep(.search-form .el-form-item) {
    margin-top: 10px;
    margin-bottom: 10px;
  }
  :deep(.el-form-item__label) {
    color: #292c33;
  }
  .search-container {
    padding: 10px 20px;
  }
  .dialog-tips {
    font-size: 16px;
    color: #292c33;
  }
  .status-container {
    display: flex;
    align-items: center;
    line-height: 17px;
    .status-text {
      padding-left: 8px;
    }
    .light-text {
      color: #f53f3f;
    }
  }
}
</style>
