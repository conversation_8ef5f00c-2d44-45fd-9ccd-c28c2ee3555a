<template>
  <div class="torque-report-container">
    <div class="search-container">
      <span class="search-item-label">
        {{ $t('deviceManagement.pp_device') }}
      </span>
      <WelkinDeviceSelect ref="deviceSelectRef" v-model="searchForm.device_id" :query="{ _version: 'factory' }" :allowCreate="true" style="width: 40%" />

      <span class="search-item-label margin_l-24">
        {{ $t('common.pp_time_frame') }}
      </span>
      <el-date-picker v-model="datePicker" type="datetimerange" @change="handleDateChange" unlink-panels :range-separator="`${$t('common.pp_to')}`" :shortcuts="shortcuts" :clearable="false" :disabledDate="getDisabledDate" />

      <div class="margin_l-24">
        <el-button @click="filterEvent" class="welkin-primary-button" :loading="loading">{{ $t('common.pp_search') }}</el-button>
        <el-button @click="resetSelect(true)" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
      </div>
    </div>

    <el-table :data="tableData" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }" v-loading="loading">
      <el-table-column prop="device_id" :label="$t('deviceManagement.pp_device_id')" :width="250">
        <template #default="{ row }">
          <WelkinCopyBoard :text="row.device_id" />
        </template>
      </el-table-column>
      <el-table-column prop="start_time" :label="$t('station.pp_service_start_time')" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatLocaleDate(row.start_time, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="end_time" :label="$t('station.pp_service_end_time')" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatLocaleDate(row.end_time, false) }}
        </template>
      </el-table-column>
      <el-table-column prop="services_count" :label="$t('factoryManagement.pp_service_count')" show-overflow-tooltip />
      <el-table-column prop="services_valid_count" :label="$t('factoryManagement.pp_service_valid_count')" show-overflow-tooltip />
      <el-table-column prop="report_gen_time" :label="$t('factoryManagement.pp_report_gen_time')" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatLocaleDate(row.report_gen_time, false) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.pp_operation')" width="80px">
        <template #default="{ row }">
          <div class="flex-box flex_a_i-center">
            <DownloadIcon class="cursor-pointer" @click="downloadFile(row)" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="common-pagination">
      <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeUnmount, onUnmounted, computed } from 'vue'
import { formatLocaleDate, getShortcuts, getDisabledDate, removeNullProp } from '~/utils'
import { useRouter, useRoute } from 'vue-router'
import { apiGetTorqueReportList, apiDownloadTorqueReport } from '~/apis/factory-management'
import { useStore } from 'vuex'
import { pagination } from '~/constvars'
import DownloadIcon from '~/assets/svg/download.vue'
import emitter from '~/utils/emitter'

const $store = useStore()
const $route = useRoute()
const $router = useRouter()
const project = ref(computed(() => $store.state.project))

const deviceSelectRef = ref<any>(null)

const loading = ref(false)
const totalNumber = ref(0)

const tableData = ref([])
const shortcuts = ref(getShortcuts())
const datePicker = ref([new Date(new Date().toLocaleDateString()).getTime(), new Date()] as any)

const searchForm = reactive({
  device_id: '',
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  page: 1,
  size: 10,
  descending: true
})

const handleDateChange = (value: any) => {
  searchForm.start_time = value[0].getTime()
  searchForm.end_time = value[1].getTime()
}

// 下载出厂扭矩报告
const downloadFile = (row: any) => {
  apiDownloadTorqueReport(row.id).then((res) => {
    if (res) {
      let count = 10
      let timeId = setInterval(function () {
        count--
        if (count <= 0) {
          clearInterval(timeId)
        }
        publicSearch(false)
      }, 1000)
    }
  })
}

// 重置
const resetSelect = (update = false) => {
  searchForm.device_id = ''
  datePicker.value = [new Date(new Date().toLocaleDateString()).getTime(), new Date().getTime()]
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime()
  searchForm.end_time = new Date().getTime()
  searchForm.page = 1
  loading.value = true
  publicSearch(update)
}

// 点击筛选按钮
const filterEvent = () => {
  searchForm.size = 10
  searchForm.page = 1
  loading.value = true
  publicSearch()
}

const publicSearch = (updateRoute = true) => {
  if (updateRoute) {
    $router.push({
      path: $route.path,
      query: {
        ...removeNullProp(searchForm),
        tab: 'factory_torque'
      }
    })
  }
  apiGetTorqueReportList(searchForm, project.value.project)
    .then((res) => {
      if (res.data !== null) {
        tableData.value = res.data
        totalNumber.value = res.total
      } else {
        tableData.value = []
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  searchForm.size = val
  searchForm.page = 1
  loading.value = true
  publicSearch()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  searchForm.page = val
  loading.value = true
  publicSearch()
}

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    if (newPath.split('/')[2] != 'factory-management') {
      return
    }
    let init_params: any = $route.query
    loading.value = true
    if (!oldPath && !isNaN(init_params.start_time) && !isNaN(init_params.end_time)) {
      let init_params: any = $route.query
      searchForm.device_id = init_params.device_id
      searchForm.start_time = init_params.start_time
      searchForm.end_time = init_params.end_time
      datePicker.value[0] = Number(init_params.start_time)
      datePicker.value[1] = Number(init_params.end_time)
      if ($route.query.tab == 'factory_torque' && !!init_params.page && !!init_params.size) {
        searchForm.page = Number(init_params.page)
        searchForm.size = Number(init_params.size)
      }
      publicSearch(false)
    } else {
      resetSelect(false)
    }
  },
  { immediate: true }
)

/**
 * @description: 手动生成报告后重新拉取列表
 * @return {*}
 */
const handleUpdateList = () => {
  searchForm.end_time = Date.now()
  searchForm.page = 1
  datePicker.value[0] = Number(searchForm.start_time)
  datePicker.value[1] = searchForm.end_time
  publicSearch()
}

emitter.on('handleUpdateList', handleUpdateList)

onBeforeUnmount(() => {
  stopWatch()
})

onUnmounted(() => {
  emitter.off('handleUpdateList')
})

const updateDeviceSelect = () => {
  deviceSelectRef.value?.getDeviceNameMap()
}
defineExpose({ updateDeviceSelect })
</script>

<style lang="scss" scoped>
.torque-report-container {
  .search-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .search-item-label {
      font-size: 14px;
      color: #595959;
      margin-right: 8px;
    }
    :deep(.el-date-editor .el-range-input) {
      color: #262626;
    }
  }
}
</style>
