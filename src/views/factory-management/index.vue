<template>
  <div class="factory-management-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_factory_management') }}</div>
      <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane :label="`${$t('factoryManagement.pp_factory_test_report')}`" name="factory_test" v-if="hasPermission(`powerSwap${project.version}:factory-management:factory_test`)"> </el-tab-pane>
        <el-tab-pane :label="`${$t('factoryManagement.pp_factory_torque_report')}`" name="factory_torque" v-if="hasPermission(`powerSwap${project.version}:factory-management:factory_torque`)"> </el-tab-pane>
      </el-tabs>
      <div class="header-right">
        <el-button @click="handleClickManual" class="welkin-secondary-button" v-if="activeTab == 'factory_torque' && hasPermission(`powerSwap${project.version}:factory-management:manual-generation`)">{{ $t('factoryManagement.pp_manual_generation') }}</el-button>
        <el-button @click="handleClickDeviceStatus" class="welkin-primary-button">{{ $t('factoryManagement.pp_edit_devide_status') }}</el-button>
      </div>
    </div>

    <div class="content-container" v-if="hasPermission(`powerSwap${project.version}:factory-management:${activeTab}`)">
      <div v-if="activeTab === 'factory_test'">
        <FactoryTestReport ref="childTabRef" />
      </div>
      <div v-else-if="activeTab === 'factory_torque'">
        <FactoryTorqueReport ref="childTabRef" />
      </div>
      <div v-else-if="activeTab === 'drop_station_acceptance'">
        <DropStationAcceptanceReport ref="childTabRef" />
      </div>
      <div v-else-if="activeTab === 'drop_station_torque'">
        <DropStationTorqueReport ref="childTabRef" />
      </div>
    </div>

    <div v-else style="padding: 50px">
      <span class="lack-permission">{{ $t('common.pp_lack_permission') }}</span>
    </div>

    <el-dialog v-model="dialogVisible" :title="$t('factoryManagement.pp_edit_devide_status')" class="status-dialog" @close="handleCloseStatusDialog" :width="450" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogFormRules" label-position="top" require-asterisk-position="right">
        <el-form-item :label="$t('deviceManagement.pp_device_id')" prop="device_id">
          <el-select v-model="dialogForm.device_id" clearable filterable class="width-full" remote :placeholder="$t('common.pp_please_select')" :remote-method="remoteMethod" :loading="remoteLoading" @change="handleChangeDeviceId">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('factoryManagement.pp_edit_devide_status')" prop="state">
          <el-select style="width: 100%" v-model="dialogForm.state" :placeholder="$t('common.pp_please_select')" :disabled="!hasPermission(`powerSwap${project.version}:factory-management:edit-status`)">
            <el-option v-for="item in deviceStateOption" :key="item.value" :label="$t(`factoryManagement.deviceState.${item.value}`)" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-box flex_j_c-flex-end margin_t-20" v-if="hasPermission(`powerSwap${project.version}:factory-management:edit-status`)">
        <el-button @click="dialogVisible = false" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button @click="sendDeviceState(dialogFormRef)" :loading="dialogLoading" class="welkin-primary-button" style="margin-left: 4px">{{ $t('logExport.pp_confirm_button') }}</el-button>
      </div>
      <div class="flex-box flex_j_c-flex-end margin_t-20" v-else>
        <el-button @click="dialogVisible = false" class="welkin-ghost-button">{{ $t('common.pp_close') }}</el-button>
      </div>
    </el-dialog>

    <!-- 手动生成扭矩报告 -->
    <el-dialog :title="$t('factoryManagement.pp_dialog_title')" v-model="manualVisible" @close="handleClose" :width="1000" align-center :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" class="manual-dialog">
      <el-form :model="manualForm" ref="manualRef" inline :rules="rules">
        <el-form-item prop="device_id" :label="$t('factoryManagement.pp_device')">
          <el-select v-model="manualForm.device_id" clearable filterable class="width-300" remote :placeholder="$t('common.pp_enter')" :remote-method="remoteMethod" :loading="remoteLoading">
            <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
              <span style="float: left">{{ item.description }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="datePicker" :label="$t('factoryManagement.pp_time')" style="width: 430px">
          <el-date-picker v-model="manualForm.datePicker" value-format="x" type="datetimerange" unlink-panels :shortcuts="getShortcuts()" :range-separator="$t('common.pp_to')" :clearable="false" :disabledDate="getDisabledDate" />
        </el-form-item>
        <el-form-item style="margin-right: 0; width: 110px">
          <el-button class="welkin-primary-button" :loading="tableLoading" @click="handlePreview(manualRef)">{{ $t('factoryManagement.pp_preview') }}</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="tableList" :header-cell-style="{ fontSize: '14px', color: '#262626', background: '#E5F9F9' }" v-loading="tableLoading">
        <el-table-column prop="start_time" :label="$t('common.pp_start_time')" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.start_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="end_time" :label="$t('common.pp_end_time')" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTime(row.end_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_duration" :label="$t('station.pp_service_duration')" min-width="80" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.service_duration }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="service_id" :label="$t('station.pp_service_id')" min-width="240">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.service_id" direction="rtl" />
          </template>
        </el-table-column>
        <el-table-column prop="has_plc_record" :label="$t('factoryManagement.pp_has_highspeed')" min-width="120" show-overflow-tooltip>
          <template #header>
            <div class="flex-box flex_j_c-center">
              <span>{{ $t('factoryManagement.pp_has_highspeed') }}</span>
            </div>
          </template>
          <template #default="{ row }">
            <div class="flex-box flex_j_c-center">
              <div class="high-speed-status" :style="{ background: row.has_plc_record ? '#67C23A' : '#FFC031', color: '#fff' }">
                {{ row.has_plc_record ? $t('common.pp_yes') : $t('common.pp_no') }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="margin_t-20 flex-box flex_j_c-flex-end" v-if="tableList && tableList.length > 0">
        <Page :page="pages" @change="handlePageChange" />
      </div>

      <div class="flex-box flex_a_i-center flex_j_c-flex-end margin_t-20">
        <el-button @click="manualVisible = false" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button @click="handleConfirm(manualRef)" class="welkin-primary-button" style="margin-left: 4px" :loading="confirmLoading">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { page } from '~/constvars/page'
import { TabsPaneContext, FormInstance, FormRules, ElMessage } from 'element-plus'
import FactoryTestReport from './factory-test-report.vue'
import FactoryTorqueReport from './factory-torque-report.vue'
import DropStationAcceptanceReport from './drop-station-acceptance-report.vue'
import DropStationTorqueReport from './drop-station-torque-report.vue'
import { getOptions, getShortcuts, getDisabledDate, getDuration, formatTime } from '~/utils'
import { hasPermission } from '~/auth'
import { deviceState } from '~/constvars/factory-management'
import { apiGetDeviceState, apiPostDeviceState, apiGetDeviceList, apiGetServiceList, apiPostManualGenerate } from '~/apis/factory-management'
import { debounce } from 'lodash-es'
import emitter from '~/utils/emitter'
import _ from 'lodash'

const { t } = useI18n()
const $router = useRouter()
const $route = useRoute()
const $store = useStore()
const project = ref(computed(() => $store.state.project))
const activeTab = ref(!!$route.query.tab ? `${$route.query.tab}` : 'factory_test')
const deviceStateOption = ref(computed(() => getOptions(deviceState)))

const manualRef = ref()
const dialogVisible = ref(false)
const dialogLoading = ref(false)
const manualVisible = ref(false)
const tableLoading = ref(false)
const confirmLoading = ref(false)
const remoteLoading = ref(false)
const deviceOptions = ref([] as any)
const tableList = ref([] as any)
const pages = ref(_.cloneDeep(page))

const dialogFormRef = ref<FormInstance>()
const dialogForm = reactive({
  device_id: '',
  state: '' as number | string
})
const manualForm = ref({
  device_id: '',
  datePicker: '' as any
})

const dialogFormRules = reactive<FormRules>({
  device_id: [{ required: true, trigger: 'change', message: t('factoryManagement.pp_select_device') }],
  state: [{ required: true, trigger: 'change', message: t('factoryManagement.pp_select_status') }]
})

const rules = ref({
  device_id: [{ required: true, message: t('factoryManagement.pp_select_device'), trigger: 'change' }],
  datePicker: [{ required: true, message: t('factoryManagement.pp_select_time'), trigger: 'change' }]
})

const childTabRef = ref<any>(null)

const handleClickDeviceStatus = () => {
  dialogVisible.value = true
  searchDeviceList('NIO')
}

const handleChangeDeviceId = async () => {
  if (dialogForm.device_id) {
    const params = { device_id: dialogForm.device_id }
    try {
      const res = await apiGetDeviceState(project.value.project, params)
      if (res.err_code) {
        ElMessage.error(res.message)
      } else {
        dialogForm.state = res.data[0].state.toString()
      }
    } catch (error) {}
  } else {
    dialogForm.state = ''
  }
}

/**
 * @description: 关闭设备状态弹窗
 * @return {*}
 */
const handleCloseStatusDialog = () => {
  dialogForm.device_id = ''
  dialogForm.state = ''
  if (dialogFormRef.value) dialogFormRef.value.resetFields()
  dialogVisible.value = false
}

const sendDeviceState = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      const params = {
        device_id: dialogForm.device_id,
        state: Number(dialogForm.state)
      }
      dialogLoading.value = true
      return apiPostDeviceState(project.value.project, params)
        .then((res) => {
          ElMessage.success('Success')
          dialogVisible.value = false
          childTabRef.value?.updateDeviceSelect()
        })
        .finally(() => {
          dialogLoading.value = false
        })
    }
  })
}

/**
 * @description: 点击手动生成按钮
 * @return {*}
 */
const handleClickManual = () => {
  tableList.value = []
  deviceOptions.value = []
  manualForm.value.datePicker = [new Date(new Date().toLocaleDateString()).getTime(), Date.now()]
  manualVisible.value = true
}

/**
 * @description: 确认手动生成扭矩报告
 * @param {*} formEl
 * @return {*}
 */
const handleConfirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      confirmLoading.value = true
      try {
        const params = {
          device_id: manualForm.value.device_id,
          start_time: manualForm.value.datePicker[0],
          end_time: manualForm.value.datePicker[1]
        }
        const res = await apiPostManualGenerate(1, project.value.project, params)
        confirmLoading.value = false
        if (!res.err_code) {
          ElMessage.success(t('common.pp_submit_success'))
          manualVisible.value = false
          emitter.emit('handleUpdateList')
        } else {
          ElMessage.error(res.message)
        }
      } catch (error) {
        confirmLoading.value = false
      }
    }
  })
}

/**
 * @description: 远程搜索站点列表
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = {
      project: project.value.project,
      name: val,
      limit: 30
    }
    const res = await apiGetDeviceList(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 点击预览
 * @return {*}
 */
const handlePreview = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      pages.value.current = 1
      getList()
    }
  })
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

const getList = async () => {
  const params = {
    device_id: manualForm.value.device_id,
    start_time: manualForm.value.datePicker[0],
    end_time: manualForm.value.datePicker[1],
    page: pages.value.current,
    size: pages.value.size
  }
  tableLoading.value = true
  try {
    const res = await apiGetServiceList(1, project.value.project, params)
    tableLoading.value = false
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      tableList.value = res.data
      pages.value.total = res.total
      tableList.value.map((item: any) => {
        item.service_duration = getDuration(item.start_time, item.end_time)
      })
    }
  } catch (error) {
    tableLoading.value = false
  }
}

/**
 * @description: 关闭手动生成扭矩报告弹窗
 * @return {*}
 */
const handleClose = () => {
  manualForm.value.device_id = ''
  manualForm.value.datePicker = [new Date(new Date().toLocaleDateString()).getTime(), Date.now()]
  manualRef.value && manualRef.value.resetFields()
}

// 切换Tab
const handleClick = (tab: TabsPaneContext, event: Event) => {
  let query = {
    ...$route.query,
    tab: tab.paneName
  }
  $router.push({
    path: $router.currentRoute.value.path,
    query: query
  })
}
</script>

<style lang="scss" scoped>
.factory-management-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    position: relative;
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
      margin-bottom: 12px;
    }
    .header-right {
      position: absolute;
      right: 24px;
      bottom: 28px;
    }
    :deep(.el-tabs__header) {
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #ade8e8;
    }
    :deep(.el-tabs__item.is-active) {
      color: #00bebe;
      font-weight: bold;
    }
    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 400;
      color: #595959;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    :deep(.el-input__inner) {
      color: #262626;
    }
  }
  :deep(.manual-dialog),
  :deep(.status-dialog) {
    .el-dialog__header {
      padding: 24px;
      padding-bottom: 16px;
      .el-dialog__title {
        color: #1f1f1f;
        line-height: 26px;
      }
    }
    .el-dialog__body {
      padding: 24px;
      padding-top: 0;
      .el-form-item {
        margin-right: 20px;
        .el-form-item__label {
          color: #595959;
        }
        .el-input__inner {
          color: #262626;
        }
      }
      .el-table td.el-table__cell div {
        color: #262626;
      }
      .high-speed-status {
        width: 60px;
        padding: 1px 6px;
        border-radius: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  :deep(.status-dialog) {
    .el-dialog__headerbtn .el-dialog__close {
      font-size: 20px;
    }
    .el-dialog__body {
      padding-top: 8px;
      .el-form-item {
        margin-right: 0;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
