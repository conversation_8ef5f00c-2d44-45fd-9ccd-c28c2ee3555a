<template>
  <div>
    <div class="search-container">
      <span class="search-item-label">
        {{ $t('deviceManagement.pp_device') }}
      </span>
      <WelkinDeviceSelect
        ref="deviceSelectRef"
        v-model="searchForm.device_id"
        :query="{ _version: 'factory' }"
        :allowCreate="true"
      />

      <span class="search-item-label">
        {{ $t('common.pp_time_frame') }}
      </span>
      <div class="pus-date-picker">
        <el-date-picker
          v-model="datePicker"
          type="datetimerange"
          @change="handleDateChange"
          unlink-panels
          :range-separator="`${$t('common.pp_to')}`"
          :start-placeholder="`${
            $t('common.pp_please_select') + $t('common.pp_start_time')
          }`"
          :end-placeholder="`${
            $t('common.pp_please_select') + $t('common.pp_end_time')
          }`"
          :shortcuts="shortcuts"
          :clearable="false"
          :disabledDate="getDisabledDate"
        />
      </div>

      <div class="search-container-right">
        <div class="search-button">
          <el-button @click="filterEvent" class="welkin-primary-button" :loading="loading">
            {{ $t('common.pp_search') }}
          </el-button>
          <el-button @click="resetSelect" class="welkin-secondary-button">
            {{ $t('common.pp_reset') }}
          </el-button>
        </div>
      </div>
    </div>
    <div class="swap-table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{
          fontSize: '14px',
          color: '#292C33',
          cursor: 'auto',
        }"
      >
        <el-table-column
          prop="device_id"
          :label="`${$t('deviceManagement.pp_device_id')}`"
          :width="250"
        >
          <template #default="scope">
            <WelkinCopyBoard :text="scope.row.device_id" />
          </template>
        </el-table-column>
        <el-table-column
          prop="start_time"
          :label="`${$t('station.pp_service_start_time')}`"
        >
          <template #default="scope">
            {{ formatLocaleDate(scope.row.start_time, false) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="end_time"
          :label="`${$t('station.pp_service_end_time')}`"
        >
          <template #default="scope">
            {{ formatLocaleDate(scope.row.end_time, false) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="services_count"
          :label="`${$t('factoryManagement.pp_service_count')}`"
        />
        <el-table-column
          prop="services_valid_count"
          :label="`${$t('factoryManagement.pp_service_valid_count')}`"
        />

        <el-table-column
          prop="report_gen_time"
          :label="`${$t('factoryManagement.pp_report_gen_time')}`"
        >
          <template #default="scope">
            {{ formatLocaleDate(scope.row.report_gen_time, false) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="`${$t('common.pp_operation')}`"
          width="80px"
          class-name="operation-column"
        >
          <template #default="scope">
            <div @click="downloadFile(scope.row)">
              <el-icon class="operation-icon">
                <Icon :icon="iconMap['download']" />
              </el-icon>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="searchForm.page"
          v-model:page-size="searchForm.size"
          :page-sizes="pagination.pageSizes"
          :layout="pagination.layout"
          :total="totalNumber"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeUnmount, computed } from 'vue';
import { iconMap } from '~/auth';
import { Icon } from '@iconify/vue/dist/iconify';
import {
  formatLocaleDate,
  getShortcuts,
  getDisabledDate,
  removeNullProp,
  getEnv,
} from '~/utils';
import { useRouter, useRoute } from 'vue-router';
import {
  apiGetTorqueReportList,
  apiDownloadTorqueReport,
} from '~/apis/factory-management';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import { pagination } from '~/constvars';

const $store = useStore();
const $route = useRoute();
const $router = useRouter();
const project = ref(computed(() => $store.state.project));

const deviceSelectRef = ref<any>(null);

const loading = ref(false);
const totalNumber = ref(0);

const tableData = ref([]);
const shortcuts = ref(getShortcuts());
const datePicker = ref([
  new Date(new Date().toLocaleDateString()).getTime(),
  new Date(),
] as any);

const searchForm = reactive({
  device_id: '',
  start_time: new Date(new Date().toLocaleDateString()).getTime(),
  end_time: new Date().getTime(),
  page: 1,
  size: 10,
  descending: true,
});

const handleDateChange = (value: any) => {
  searchForm.start_time = value[0].getTime();
  searchForm.end_time = value[1].getTime();
};

// 下载落站扭矩报告
const downloadFile = (row: any) => {
  apiDownloadTorqueReport(row.id).then((res) => {
    if (res) {
      let count = 10;
      let timeId = setInterval(function () {
        count--;
        if (count <= 0) {
          clearInterval(timeId);
        }
        publicSearch(false);
      }, 1000);
    }
  });
};

// 重置
const resetSelect = () => {
  searchForm.device_id = '';
  datePicker.value = [
    new Date(new Date().toLocaleDateString()).getTime(),
    new Date().getTime(),
  ];
  searchForm.start_time = new Date(new Date().toLocaleDateString()).getTime();
  searchForm.end_time = new Date().getTime();
};

// 点击筛选按钮
const filterEvent = () => {
  loading.value = true;
  searchForm.size = 10;
  searchForm.page = 1;
  publicSearch();
};

const publicSearch = (updateRoute = true) => {
  console.log('落站扭矩报告 publicSearch ', searchForm);
  updateRoute &&
    $router.push({
      path: $route.path,
      query: {
        ...removeNullProp(searchForm),
        tab: 'drop_station_torque',
      },
    });

  updateRoute && (loading.value = true);
  apiGetTorqueReportList(searchForm, project.value.project, 2)
    .then((res) => {
      if (res.data !== null) {
        tableData.value = res.data;
        totalNumber.value = res.total;
      } else {
        tableData.value = [];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true;
  searchForm.size = val;
  searchForm.page = 1;
  publicSearch();
};

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true;
  searchForm.page = val;
  publicSearch();
};

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    console.log('落站扭矩报告 watch', newPath, oldPath);
    if (newPath.split('/')[2] != 'factory-management') {
      return;
    }

    loading.value = true;
    let init_params: any = $route.query;

    if (
      !oldPath &&
      // $route.query.tab == 'drop_station_torque' &&
      !isNaN(init_params.start_time) &&
      !isNaN(init_params.end_time)
    ) {
      console.log('url进入');
      let init_params: any = $route.query;

      searchForm.device_id = init_params.device_id;

      searchForm.start_time = init_params.start_time;
      searchForm.end_time = init_params.end_time;
      datePicker.value[0] = Number(init_params.start_time);
      datePicker.value[1] = Number(init_params.end_time);

      if (
        $route.query.tab == 'drop_station_torque' &&
        !!init_params.page &&
        !!init_params.size
      ) {
        searchForm.page = Number(init_params.page);
        searchForm.size = Number(init_params.size);
      }
    } else {
      console.log('版本切换');
      resetSelect();
    }
    publicSearch(false);
  },
  { immediate: true }
);

onBeforeUnmount(() => {
  stopWatch();
});
const updateDeviceSelect = () => {
  deviceSelectRef.value?.getDeviceNameMap();
};
defineExpose({ updateDeviceSelect });
</script>

<style lang="scss">
.button-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
