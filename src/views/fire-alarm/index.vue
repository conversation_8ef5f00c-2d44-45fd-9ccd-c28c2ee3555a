<template>
  <div class="fire-alarm-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_info_trace') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_fire_alarm') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_fire_alarm') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" inline style="width: 100%">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('common.pp_time_frame')" class="upper-form-item width-full">
                <el-date-picker v-model="datePicker" type="datetimerange" format="YYYY-MM-DD HH:mm" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('deviceManagement.pp_device')" class="upper-form-item width-full">
                <RemoteDeviceSelect v-model="form.device_id" :initDevice="route.query.device_id" project="PowerSwap2,PUS3,PUS4" class="width-full" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('home.pp_project')" class="upper-form-item width-full">
                <el-select v-model="form.project" clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="upper-form-item width-full">
                <el-button class="welkin-primary-button" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
                <el-button @click="handleReset" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="swap-table-container">
        <el-table :data="list" style="width: 100%" :header-cell-style="{ fontSize: '14px', color: '#292C33', cursor: 'auto' }" v-loading="loading">
          <el-table-column prop="alarm_ts" :label="$t('fireAlarm.pp_create_ts')" min-width="170" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.alarm_ts) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="description" :label="$t('fireAlarm.pp_device_name')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <div class="pointer-column ellipse">
                <span class="point-span" @click="handleJump(scope.row)">
                  {{ scope.row.description ? scope.row.description : $t('common.pp_unnamed_device') }}
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="device_id" :label="$t('batterySwap.pp_device_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>

          <el-table-column prop="project" :label="$t('home.pp_project')" width="150">
            <template #default="scope">
              <div class="flex-box flex_a_i-center">
                <span :style="{ color: projectMap[scope.row.project].color }">{{ $t(projectMap[scope.row.project].name) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column :label="$t('fireAlarm.pp_upload_progress')" width="100">
            <template #default="scope">
              <div class="flex-box flex_a_i-center">
                <span v-if="scope.row.parts.length < scope.row.total_parts">{{ scope.row.parts.length }} / {{ scope.row.total_parts }}</span>
                <span v-else>{{ $t('fireAlarm.pp_finish') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="update_ts" :label="$t('fireAlarm.pp_update_time')" min-width="170" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.update_ts) }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" :label="$t('fireAlarm.pp_view_report')" width="100" class-name="operation-column">
            <template #default="scope">
              <div @click="handleJumpDetail(scope.row)" v-if="scope.row.project">
                <el-icon color="#00bebe" class="operation-icon"><Document /></el-icon>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>

          <!-- <el-table-column fixed="right" :label="$t('fireAlarm.pp_view_file')" width="100" class-name="operation-column">
            <template #default="scope">
              <div @click="handleViewDoc(scope.row)">
                <el-icon color="#00bebe" class="operation-icon"><Document /></el-icon>
              </div>
            </template>
          </el-table-column> -->
        </el-table>

        <div class="pagination-container">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>
    </div>

    <DocumentDialog v-model:dialogVisible="dialogVisible" :rowInfo="rowInfo" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, onBeforeUnmount, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import { page } from '~/constvars/page';
import { projectMap } from '~/constvars';
import { useRoute, useRouter } from 'vue-router';
import { getDisabledDate, formatTime, clearJson, removeNullKeys, setSecondsToZero, setSecondsToFiftyNine } from '~/utils';
import { projectOptions, getShortcuts } from './constant';
import { apiGetAlarmList } from '~/apis/fire-alarm';
import { ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';
import DocumentDialog from './document-dialog.vue';
import _ from 'lodash';

const { t } = useI18n();
const store = useStore();
const route = useRoute();
const router = useRouter();
const webSocket = ref();
const ruleFormRef = ref();
const loading = ref(false);
const dialogVisible = ref(false);
const pages = ref(_.cloneDeep(page));
const list = ref([] as any);
const datePicker = ref([new Date().getTime() - 3600 * 1000 * 24, new Date().getTime()] as any);
const form = ref({
  start_time: new Date().getTime() - 3600 * 1000 * 24,
  end_time: new Date().getTime(),
  device_id: '',
  project: '',
});
const searchForm = ref({});
const rowInfo = ref({} as any);
const pathMap = ref(computed(() => store.state.vehicle.pathMap));

/**
 * @description: 查看文件
 * @param {*} row
 * @return {*}
 */
const handleViewDoc = (row: any) => {
  rowInfo.value = _.cloneDeep(row);
  rowInfo.value.parts.sort((a: any, b: any) => a.part_id - b.part_id);
  for (let i = 0; i < row.total_parts; i++) {
    const index = rowInfo.value.parts.findIndex((item: any) => item.part_id == i);
    if (index == -1) {
      rowInfo.value.parts.splice(i, 0, { part_id: i, file_url: '', upload_ts: 0 });
    } else {
      const splitUrl = rowInfo.value.parts[i].file_url.split('/');
      rowInfo.value.parts[i].file_name = splitUrl[splitUrl.length - 1];
    }
  }
  dialogVisible.value = true;
};

const handleJumpDetail = (row: any) => {
  // 跳转到详情页，传递alarm_id参数
  router.push({
    path: '/info-trace/fire-alarm/fire-detail',
    query: { alarm_ts: row.alarm_ts, device_id: row.device_id, project: row.project },
  });
};

/**
 * @description: 关闭ws
 * @return {*}
 */
const closeSocket = () => {
  if (webSocket.value && webSocket.value.websocket) {
    webSocket.value.close();
  }
};

/**
 * @description: 跳转至详情页
 * @param {*} row
 * @return {*}
 */
const handleJump = (row: any) => {
  // 跳转到详情页
  const project = pathMap.value[row.project];
  router.push({
    path: `/${project}/device-management/single-station/${row.device_id}`,
  });
};

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value);
  datePicker.value = [new Date().getTime() - 3600 * 1000 * 24, new Date().getTime()];
  form.value.start_time = datePicker.value[0];
  form.value.end_time = datePicker.value[1];
  handleSearch();
};

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1;
  searchForm.value = _.cloneDeep(form.value);
  loading.value = true;
  getList();
};

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current;
  pages.value.size = argPage.size;
  const pageObj = { page: pages.value.current, size: pages.value.size };
  router.push({
    path: location.pathname,
    query: { ...route.query, ...pageObj },
  });
  loading.value = true;
  webSocket.value.send(pageObj);
};

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime();
  form.value.end_time = new Date(val[1]).getTime();
};

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = _.cloneDeep(searchForm.value) as any;
  formData.page = pages.value.current;
  formData.size = pages.value.size;
  formData.start_time = setSecondsToZero(formData.start_time);
  formData.end_time = setSecondsToFiftyNine(formData.end_time);
  removeNullKeys(formData);
  closeSocket();
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: formData,
    });
  }
  webSocket.value = apiGetAlarmList(formData);
  webSocket.value.init(
    (res: any) => {
      list.value = res.data;
      pages.value.total = res.total;
      loading.value = false;
    },
    () => {
      loading.value = false;
    }
  );
};

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query;
  if (initParams.start_time && initParams.end_time) {
    form.value.start_time = Number(initParams.start_time);
    form.value.end_time = Number(initParams.end_time);
  } else {
    form.value.start_time = new Date().getTime() - 3600 * 1000 * 24;
    form.value.end_time = new Date().getTime();
  }
  datePicker.value[0] = form.value.start_time;
  datePicker.value[1] = form.value.end_time;
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1;
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10;
  form.value.device_id = !!initParams.device_id ? initParams.device_id : '';
  form.value.project = !!initParams.project ? initParams.project : '';
  searchForm.value = _.cloneDeep(form.value);
  getList(false);
};

onBeforeMount(() => {
  initWeb();
});

onBeforeUnmount(() => {
  closeSocket();
});
</script>

<style lang="scss" scoped>
.fire-alarm-container {
  .search-form-container {
    padding-bottom: 5px;
    :deep(.upper-form-item) {
      margin-bottom: 15px;
    }
  }
  .swap-table-container {
    :deep(.pagination-container .el-pagination) {
      margin-bottom: 20px;
    }
  }
}
</style>
