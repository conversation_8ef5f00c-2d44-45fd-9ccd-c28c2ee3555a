<template>
  <div class="document-dialog">
    <el-dialog width="700px" :title="$t('fireAlarm.pp_document_list')" align-center v-model="dialogVisible" @open="openDialog" @close="close" :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="dialog-title">
        <span class="margin_r-60">{{ $t('fireAlarm.pp_device_name') }}： {{ rowInfo.description ? rowInfo.description : $t('common.pp_unnamed_device') }}</span>
        <span>{{ $t('fireAlarm.pp_alarm_time') }}： {{ formatTime(rowInfo.alarm_ts) }}</span>
      </div>

      <div class="swap-table-container">
        <el-table
          :data="rowInfo.parts.slice((pages.current - 1) * pages.size, pages.current * pages.size)"
          style="width: 100%"
          :header-cell-style="{
            fontSize: '14px',
            color: '#292C33',
            cursor: 'auto'
          }"
        >
          <el-table-column prop="part_id" :label="$t('fireAlarm.pp_order')" min-width="80" show-overflow-tooltip>
            <template #default="{row}">
              <span>{{ row.part_id + 1 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="file_name" :label="$t('fireAlarm.pp_document_name')" min-width="180" show-overflow-tooltip>
            <template #default="{row}">
              <span>{{ row.file_name }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="upload_ts" :label="$t('fireAlarm.pp_upload_time')" min-width="170" show-overflow-tooltip>
            <template #default="{row}">
              <span v-if="row.upload_ts">{{ formatTime(row.upload_ts) }}</span>
              <span style="color: red" v-else>{{ $t('fireAlarm.pp_not_uploaded') }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" :label="$t('common.pp_download')" width="100" class-name="operation-column">
            <template #default="{row}">
              <div @click="handleDownload(row)" v-if="row.upload_ts">
                <el-icon :size="'20px'" class="operation-icon">
                  <Icon :icon="iconMap['download']" />
                </el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <Page :page="pages" @change="handlePageChange" />
        </div>

        <div class="flex-box flex_j_c-center flex_a_i-center">
          <el-button @click="close" class="welkin-primary-button">{{ $t('common.pp_close') }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {iconMap} from '~/auth'
import {useI18n} from 'vue-i18n'
import {Icon} from '@iconify/vue/dist/iconify'
import {formatTime} from '~/utils'
import {page} from '~/constvars/page'
import {apiGetDownload} from '~/apis/fire-alarm'
import {ElMessage} from 'element-plus'
import _ from 'lodash'

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  rowInfo: {
    type: Object,
    default: {}
  }
})
const emits = defineEmits(['update:dialogVisible'])

const pages = ref(_.cloneDeep(page))

const openDialog = () => {
  pages.value.current = 1
  pages.value.total = props.rowInfo.parts.length
}

/**
 * @description: 下载
 * @param {*} row
 * @return {*}
 */
const handleDownload = async (row: any) => {
  const params = {log_url: row.file_url}
  try {
    await apiGetDownload(params)
  } catch (error: any) {
    ElMessage.error(error)
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
}

const close = () => {
  emits('update:dialogVisible', false)
}
</script>

<style lang="scss" scoped>
.document-dialog {
  :deep(.el-dialog__body) {
    font-family: 'Noto Sans';
    color: #292c33;
    padding-top: 20px !important;
    .dialog-title {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      font-size: 16px;
      margin-bottom: 20px;
    }
  }
}
</style>
