import {i18n} from '~/i18n'

export const projectOptions = [
  {
    label: 'processError.pp_station_2',
    value: 'PowerSwap2'
  },
  {
    label: 'processError.pp_station_3',
    value: 'PUS3'
  },
  {
    label: 'processError.pp_station_4',
    value: 'PUS4'
  }
]

export const getShortcuts = () => {
  const shortcuts = [
    {
      text: i18n.global.t('common.pp_last_hour'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_day'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    }
  ]
  return shortcuts
}