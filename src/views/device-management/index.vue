<template>
  <div class="device-management-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_device_management') }}</div>
    </div>

    <div class="content-container">
      <el-form :model="searchForm" inline>
        <el-form-item :label="$t('deviceManagement.pp_device')">
          <CommonDeviceSelect class="width-350" v-model="searchForm.device" />
        </el-form-item>
        <el-form-item :label="$t('common.pp_region')" v-if="getEnv() !== '-stg-eu' && getEnv() !== '-eu'">
          <el-select v-model="searchForm.province" clearable filterable :placeholder="$t('deviceManagement.pp_select_province')" @change="changeProvince(searchForm.province)" class="width-190">
            <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="searchForm.city" clearable filterable :placeholder="$t('deviceManagement.pp_select_city')" v-if="searchForm.province && ['上海市', '北京市', '天津市', '重庆市'].indexOf(searchForm.province) == -1" class="width-190 margin_l-10">
            <el-option v-for="item in cityOptions.options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="searchEvent" :loading="loading" class="welkin-primary-button">{{ $t('common.pp_search') }}</el-button>
          <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">{{ $t('common.pp_reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
        <el-table-column prop="description" :label="$t('deviceManagement.pp_device_name')" min-width="240" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="light-column" @click="changeToSingleStation(row)">{{ row.description }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="device_id" :label="$t('deviceManagement.pp_device_id')" min-width="200">
          <template #default="{ row }">
            <WelkinCopyBoard :text="row.device_id" />
          </template>
        </el-table-column>
        <el-table-column prop="city_company" :label="$t('common.pp_regional_company')" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.city_company || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="region" :label="$t('common.pp_region')" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.region || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" :label="$t('common.pp_operation')" width="100" class-name="operation-column">
          <template #default="{ row }">
            <StarIcon v-if="!row.favorite" @click="collectDevice(row)" class="cursor-pointer" />
            <StarFillIcon v-else @click="cancelCollectDevice(row)" class="cursor-pointer" />
          </template>
        </el-table-column>
      </el-table>
      <div class="common-pagination">
        <el-pagination background v-model:currentPage="searchForm.page_no" v-model:page-size="searchForm.page_size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeUnmount, computed } from 'vue'
import { Province, City } from './index'
import { ElNotification } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { apiGetDeviceList } from '~/apis/device-management'
import { apiPutFavorites } from '~/apis/home'
import { removeNullProp, getEnv } from '~/utils'
import { useStore } from 'vuex'
import { pagination } from '~/constvars'
import StarIcon from '~/assets/svg/star.vue'
import StarFillIcon from '~/assets/svg/star-fill.vue'

const { t } = useI18n()
// 监听路由
const $route = useRoute()
const $router = useRouter()
const $store = useStore()

const project = ref(computed(() => $store.state.project))

const loading = ref(false)
const totalNumber = ref(10)
const provinceOptions = Province.map((item, index) => ({
  key: item.id,
  label: item.name,
  value: item.name
}))
const cityOptions = reactive({
  options: [
    {
      label: '',
      value: ''
    }
  ]
})

const searchForm = reactive({
  page_no: 1,
  page_size: 10,
  device: '',
  province: '',
  city: '',
  descending: true
})

// 获取设备列表
const tableList = ref([])

const getDeviceList = (updateRoute = true) => {
  if (updateRoute) {
    $router.push({
      path: $route.path,
      query: {
        ...removeNullProp(searchForm)
      }
    })
  }
  loading.value = true
  return apiGetDeviceList(searchForm, project.value.project)
    .then((res) => {
      tableList.value = res.data
      totalNumber.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}

// 筛选
const searchEvent = () => {
  searchForm.page_no = 1
  searchForm.page_size = 10
  getDeviceList()
}

// 重置筛选项
const resetSelect = () => {
  searchForm.province = ''
  searchForm.city = ''
  searchForm.device = ''
  searchEvent()
}

// 进入单站设备详情
const changeToSingleStation = (row: any) => {
  sessionStorage.setItem('device-management', JSON.stringify(searchForm))
  $router.push({
    path: `/${project.value.route}/device-management/single-station/${row.device_id}`
  })
}

// 选择省份时映射城市数据
const changeProvince = (value: string) => {
  let cityId = ''
  Province.map((item: any) => {
    if (item.name === value) {
      cityId = item.id
    }
  })
  if (cityId !== '') {
    cityOptions.options = City[cityId].map((item: any, index: number) => ({
      label: item.name,
      value: item.name
    }))
  }
  searchForm.city = ''
}

// 重置省份选择
const resetProvince = () => {
  searchForm.province = ''
  searchForm.city = ''
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true
  searchForm.page_size = val
  searchForm.page_no = 1
  getDeviceList()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true
  searchForm.page_no = val
  getDeviceList()
}

// 收藏
const collectDevice = async (row: any) => {
  row.favorite = true
  ElNotification.closeAll()
  ElNotification({
    title: `${t('common.pp_stars_success')}`,
    message: `${t('common.pp_stars')} ${row.description}${row.device_id}`,
    type: 'success',
    duration: 2000
  })
  await apiPutFavorites(row)
  await getDeviceList()
}

// 取消收藏
const cancelCollectDevice = async (row: any) => {
  row.favorite = false
  ElNotification.closeAll()
  ElNotification({
    title: `${t('common.pp_remove_success')}`,
    message: `${t('common.pp_remove')} ${row.description}${row.device_id}`,
    type: 'success',
    duration: 2000
  })
  await apiPutFavorites(row)
  await getDeviceList()
}

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    console.log(newPath, oldPath)
    if (!oldPath) {
      console.log('url 进入')
      let init_params: any = $route.query
      searchForm.page_no = !!init_params.page_no ? Number(init_params.page_no) : 1
      searchForm.page_size = !!init_params.page_size ? Number(init_params.page_size) : 10
      searchForm.device = init_params.device
      searchForm.province = init_params.province
      searchForm.city = init_params.city
      getDeviceList(false)
    } else if (newPath.split('/').length == 3 && newPath.split('/')[2] == oldPath.split('/')[2]) {
      console.log('切换版本')
      resetSelect()
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  stopWatch()
})
</script>

<style lang="scss" scoped>
.device-management-container {
  font-family: 'Blue Sky Standard';
  .header-container {
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    padding: 0 24px 24px;
    background-color: #fff;
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      margin-bottom: 20px;
      .el-form-item {
        margin: 0 24px 0 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 8px;
        }
      }
    }
    :deep(.operation-column) {
      .cell {
        display: flex;
        justify-content: center;
        div {
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
</style>
