<template>
  <div class="process-error-container">
    <div class="swap-page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ $t('menu.pp_info_trace') }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ $t('menu.pp_process_error') }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="header-title">{{ $t('menu.pp_process_error') }}</div>
      </div>
    </div>

    <div class="swap-page-container flex-box flex_d-column">
      <div class="search-form-container">
        <el-form :model="form" ref="ruleFormRef" inline style="width: 100%">
          <el-row :gutter="20">
            <el-col :span="9">
              <el-form-item :label="$t('common.pp_time')" class="upper-form-item width-full">
                <el-date-picker v-model="datePicker" type="datetimerange" format="YYYY-MM-DD HH:mm" @change="handleDateChange" unlink-panels :shortcuts="getShortcuts()" :range-separator="`${$t('common.pp_to')}`" :start-placeholder="`${$t('common.pp_please_select') + $t('common.pp_start_time')}`" :end-placeholder="`${$t('common.pp_please_select') + $t('common.pp_end_time')}`" :clearable="false" :disabledDate="getDisabledDate" />
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item :label="$t('deviceManagement.pp_device')" class="upper-form-item width-full">
                <RemoteDeviceSelect v-model="form.device_id" :initDevice="route.query.device_id" project="PowerSwap2,PUS3,PUS4" class="width-full" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('home.pp_project')" class="upper-form-item width-full">
                <el-select v-model="form.project" clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in projectOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="4">
              <el-form-item :label="$t('processError.pp_process_id')" class="upper-form-item width-full">
                <el-input v-model="form.process_id" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item :label="$t('processError.pp_process_name')" class="upper-form-item width-full">
                <el-input v-model="form.process_name" :placeholder="$t('common.pp_please_input')" clearable class="width-full" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item :label="$t('processError.pp_error_reason')" class="upper-form-item width-full">
                <el-select v-model="form.reason" clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in reasonOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item :label="$t('processError.pp_error_level')" class="upper-form-item width-full">
                <el-select v-model="form.level" clearable filterable :placeholder="`${$t('common.pp_please_select')}`" class="width-full">
                  <el-option v-for="item in levelOptions" :key="item.value" :value="item.value" :label="$t(item.label)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="upper-form-item width-full">
                <el-button class="welkin-primary-button" :loading="loading" @click="handleSearch">{{ $t('common.pp_search') }}</el-button>
                <el-button @click="handleReset" class="welkin-secondary-button">{{ $t('common.pp_reset') }}</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="swap-table-container">
        <el-table :data="list" style="width: 100%" :header-cell-style="{fontSize: '14px', color: '#292C33', cursor: 'auto'}" v-loading="loading">
          <el-table-column prop="ts" :label="$t('processError.pp_create_ts')" min-width="170" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatTime(scope.row.ts) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="description" :label="$t('alarmList.pp_device')" min-width="250" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.description ? scope.row.description : $t('common.pp_unnamed_device') }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="project" :label="$t('home.pp_project')" width="150">
            <template #default="scope">
              <div class="flex-box flex_a_i-center">
                <span :style="`color: ${projectMap[scope.row.project].color}`">{{ $t(projectMap[scope.row.project].name) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="process_name" :label="$t('processError.pp_process_name')" width="200">
            <template #default="scope">
              <div class="flex-box flex_a_i-center">
                <span>{{ scope.row.process_name ? scope.row.process_name : '-' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="process_id" :label="$t('processError.pp_process_id')" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.process_id ? scope.row.process_id : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="reason" :label="$t('processError.pp_error_reason')" min-width="180">
            <template #default="scope">
              <span>{{ getReasonName(scope.row.reason) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="level" :label="$t('processError.pp_error_level')" min-width="120">
            <template #default="scope">
              <span :style="{color: levelColorMap[scope.row.level]}">{{ getLevelName(scope.row.level) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="module" :label="$t('processError.pp_module')" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.module || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="device_id" :label="$t('batterySwap.pp_device_id')" min-width="250">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <Page :page="pages" @change="handlePageChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onBeforeMount} from 'vue'
import {useI18n} from 'vue-i18n'
import {useRoute, useRouter} from 'vue-router'
import {page} from '~/constvars/page'
import {projectMap} from '~/constvars'
import {apiGetProcessList} from '~/apis/process-error'
import {projectOptions, reasonOptions, levelOptions, levelColorMap, getShortcuts} from './constant'
import {getDisabledDate, formatTime, clearJson, removeNullKeys, setSecondsToZero, setSecondsToFiftyNine} from '~/utils'
import {ElMessage} from 'element-plus'
import _ from 'lodash'

const {t} = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const pages = ref(_.cloneDeep(page))
const ruleFormRef = ref()
const list = ref([] as any)
const datePicker = ref([new Date().getTime() - 3600 * 1000 * 24, new Date().getTime()] as any)
const form = ref({
  start_time: new Date().getTime() - 3600 * 1000 * 24,
  end_time: new Date().getTime(),
  device_id: '',
  project: '',
  process_id: '',
  process_name: '',
  reason: '' as number | string,
  level: '' as number | string
})
const searchForm = ref({})

const getReasonName = (resonId: any) => {
  const filterArr = reasonOptions.filter((item: any) => item.value == resonId)
  if (filterArr.length > 0) {
    return t(filterArr[0].label)
  } else {
    return '-'
  }
}

const getLevelName = (levelId: any) => {
  const filterArr = levelOptions.filter((item: any) => item.value == levelId)
  if (filterArr.length > 0) {
    return t(filterArr[0].label)
  } else {
    return '-'
  }
}

/**
 * @description: 重置
 * @return {*}
 */
const handleReset = () => {
  clearJson(form.value)
  datePicker.value = [new Date().getTime() - 3600 * 1000 * 24, new Date().getTime()]
  form.value.start_time = datePicker.value[0]
  form.value.end_time = datePicker.value[1]
  handleSearch()
}

/**
 * @description: 筛选
 * @return {*}
 */
const handleSearch = () => {
  pages.value.current = 1
  searchForm.value = _.cloneDeep(form.value)
  getList()
}

/**
 * @description: 时间
 * @param {*} val
 * @return {*}
 */
const handleDateChange = (val: any) => {
  form.value.start_time = new Date(val[0]).getTime()
  form.value.end_time = new Date(val[1]).getTime()
}

/**
 * @description: 获取全量数据
 * @param {*} updateRoute // 路由是否带参数
 * @return {*}
 */
const getList = async (updateRoute = true) => {
  let formData = _.cloneDeep(searchForm.value) as any
  formData.page = pages.value.current
  formData.size = pages.value.size
  formData.start_time = setSecondsToZero(formData.start_time)
  formData.end_time = setSecondsToFiftyNine(formData.end_time)
  removeNullKeys(formData)
  if (updateRoute) {
    router.push({
      path: location.pathname,
      query: formData
    })
  }
  loading.value = true
  try {
    const res = await apiGetProcessList(formData)
    if (res.err_code) {
      ElMessage.error(res.message)
    } else {
      list.value = res.data
      pages.value.total = res.total
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

/**
 * @description: 分页
 * @param {*} argPage
 * @return {*}
 */
const handlePageChange = (argPage: any) => {
  pages.value.current = argPage.current
  pages.value.size = argPage.size
  getList()
}

/**
 * @description: 初始化页面
 * @return {*}
 */
const initWeb = () => {
  let initParams: any = route.query
  if (!isNaN(initParams.start_time) && !isNaN(initParams.end_time)) {
    form.value.start_time = Number(initParams.start_time)
    form.value.end_time = Number(initParams.end_time)
    datePicker.value[0] = form.value.start_time
    datePicker.value[1] = form.value.end_time
  }
  pages.value.current = !!initParams.page ? Number(initParams.page) : 1
  pages.value.size = !!initParams.size ? Number(initParams.size) : 10
  form.value.device_id = !!initParams.device_id ? initParams.device_id : ''
  form.value.project = !!initParams.project ? initParams.project : ''
  form.value.process_id = !!initParams.process_id ? initParams.process_id : ''
  form.value.process_name = !!initParams.process_name ? initParams.process_name : ''
  form.value.reason = !!initParams.reason ? Number(initParams.reason) : ''
  form.value.level = !!initParams.level ? Number(initParams.level) : ''
  searchForm.value = _.cloneDeep(form.value)
  getList(false)
}

onBeforeMount(() => {
  initWeb()
})
</script>

<style lang="scss" scoped>
.process-error-container {
  .search-form-container {
    padding-bottom: 5px;
    :deep(.upper-form-item) {
      margin-bottom: 15px;
    }
  }
  .swap-table-container {
    :deep(.pagination-container .el-pagination) {
      margin-bottom: 20px;
    }
  }
}
</style>
