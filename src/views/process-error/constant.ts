import {i18n} from '~/i18n'

export const projectOptions = [
  {
    label: 'processError.pp_station_2',
    value: 'PowerSwap2'
  },
  {
    label: 'processError.pp_station_3',
    value: 'PUS3'
  },
  {
    label: 'processError.pp_station_4',
    value: 'PUS4'
  }
]

export const reasonOptions = [
  {
    label: 'processError.pp_collapse',
    value: 1
  },
  {
    label: 'processError.pp_cpu_high',
    value: 2
  },
  {
    label: 'processError.pp_memory_high',
    value: 3
  }
]

export const levelOptions = [
  {
    label: 'processError.pp_level_one',
    value: 1
  },
  {
    label: 'processError.pp_level_two',
    value: 2
  },
  {
    label: 'processError.pp_level_three',
    value: 3
  }
]

export const levelColorMap = {
  1: '#FF0000',
  2: '#FFA500',
  3: '#0000FF'
} as any

export const getShortcuts = () => {
  const shortcuts = [
    {
      text: i18n.global.t('common.pp_last_24_hours'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    }
  ]
  return shortcuts
}
