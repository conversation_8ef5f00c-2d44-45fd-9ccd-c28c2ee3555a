<template>
  <div class="order-image-container">
    <div class="header-container">
      <div class="header-title">{{ $t('menu.pp_order_image') }}</div>
    </div>

    <div class="content-container">
      <el-form inline :model="searchForm" :rules="rules" ref="ruleFormRef" label-width="100px" label-position="left" require-asterisk-position="right">
        <el-form-item :label="$t('deviceManagement.pp_device')" prop="device_id" :required="true">
          <CommonDeviceSelect v-model="searchForm.device_id" class="width-full" />
        </el-form-item>

        <el-form-item :label="$t('orderImage.pp_alert_type')" prop="algorithm_name" :required="true">
          <el-select v-model="searchForm.algorithm_name" :placeholder="`${$t('common.pp_please_input')}${$t('orderImage.pp_alert_type')}`" filterable clearable style="width: 100%">
            <el-option v-for="item in algorithmOptions" :key="item.name" :value="item.name" :label="locale == 'zh' ? item.name + '-' + item.description : item.name + '-' + item.description_en">
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ locale == 'zh' ? item.description : item.description_en }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('orderImage.pp_alarm_time')" prop="ts" :required="true">
          <el-date-picker v-model="searchForm.ts" style="width: 100%" type="datetime" :placeholder="`${$t('common.pp_please_select')}${$t('orderImage.pp_alarm_time')}`" :disabledDate="getDisabledDate" />
        </el-form-item>

        <el-form-item>
          <el-button class="welkin-primary-button" @click="filterEvent(ruleFormRef)" :loading="loading">
            {{ $t('common.pp_search') }}
          </el-button>
          <el-button @click="resetSelect" class="welkin-secondary-button" style="margin-left: 8px">
            {{ $t('common.pp_reset') }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="swap-table-container" v-if="tableList.length > 0">
        <el-table :data="tableList" v-loading="loading" :header-cell-style="{ background: '#E5F9F9', fontSize: '14px', color: '#262626' }" :cell-style="{ color: '#262626' }">
          <el-table-column prop="service_id" :label="$t('orderImage.pp_order_id')" width="240">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.service_id" />
            </template>
          </el-table-column>
          <el-table-column prop="alert_type" :label="$t('orderImage.pp_alert_type')" min-width="200" show-overflow-tooltip />
          <el-table-column prop="algorithm_name" :label="$t('orderImage.pp_algorithm_name')" min-width="100" show-overflow-tooltip />
          <el-table-column prop="file_gen_status" :label="$t('logExport.pp_status')" min-width="140" class-name="other-column-style">
            <template #default="scope">
              <div class="status-column" v-if="scope.row.file_gen_status === '已上传'">
                <el-icon :size="'20px'" class="check-icon">
                  <Icon :icon="iconMap['check']" />
                </el-icon>
                <span>{{ $t('logExport.pp_done') }}</span>
              </div>
              <div class="status-column" v-if="scope.row.file_gen_status === '生成中'">
                <time-logo />
                <span>{{ $t('logExport.pp_transmitting') }}</span>
              </div>
              <div class="status-column" v-if="scope.row.file_gen_status === '未生成'">
                <el-icon :size="'20px'" class="warning-icon">
                  <SemiSelect />
                </el-icon>
                <span>{{ $t('logExport.pp_no_generated') }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" :label="$t('deviceManagement.pp_device_name')" min-width="240" show-overflow-tooltip>
            <template #default="scope">
              <span class="light-column" @click="changeToSingleStation(scope.$index, scope.row)">
                {{ scope.row.description }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="device_id" :label="$t('deviceManagement.pp_device_id')" width="240">
            <template #default="scope">
              <WelkinCopyBoard :text="scope.row.device_id" />
            </template>
          </el-table-column>
          <el-table-column :label="t('station.pp_image_name')" width="240">
            <template #default="scope">
              <el-tooltip effect="dark" :disabled="!scope.row.file_path" :content="getFileName(scope.row.file_path, 'full')" placement="top">
                <span>...{{ getFileName(scope.row.file_path, 'short') }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('common.pp_operation')" width="100" class-name="operation-column">
            <template #default="scope">
              <el-icon class="operation-icon" @click="handleImageDownload(scope.row, scope.$index)" v-if="!scope.row.snapshot_url">
                <Download />
              </el-icon>
              <el-icon class="operation-icon" @click="handleImageView(scope.row, scope.$index)" v-if="!!scope.row.snapshot_url">
                <View />
              </el-icon>
              <el-icon class="operation-icon" v-if="!!scope.row.snapshot_url" @click="handleImageLarkcard(scope.row, scope.$index)">
                <Postcard />
              </el-icon>
            </template>
          </el-table-column>
        </el-table>
        <div class="common-pagination">
          <el-pagination background v-model:currentPage="searchForm.page" v-model:page-size="searchForm.size" :page-sizes="pagination.pageSizes" :layout="pagination.layout" :total="totalNumber" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>

      <div class="flex-item_f-1 flex-box flex_j_c-center flex_a_i-center" v-else>
        <el-empty :description="loading ? $t('common.pp_loading') : $t('orderImage.pp_empty_notice')" />
      </div>
    </div>

    <el-image-viewer v-if="imgViewVisible" :url-list="[showImage.snapshot_url]" @close="closeDialog" :hide-on-click-modal="true" :initial-index="0"> </el-image-viewer>

    <el-dialog v-model="dialogVisible" :title="$t('orderImage.pp_notice_person')">
      <el-descriptions :column="1" :border="true">
        <el-descriptions-item :label="`${$t('orderImage.pp_order_id')}`">
          {{ larkform.service_id }}
        </el-descriptions-item>
        <el-descriptions-item :label="`${$t('orderImage.pp_alert_type')}`">
          {{ larkform.alert_type }}
        </el-descriptions-item>
        <el-descriptions-item :label="`${$t('orderImage.pp_algorithm_name')}`">
          {{ larkform.algorithm_name }}
        </el-descriptions-item>
        <el-descriptions-item :label="`${$t('deviceManagement.pp_device_name')}`">
          {{ larkform.description }}
        </el-descriptions-item>
        <el-descriptions-item :label="`${$t('deviceManagement.pp_device_id')}`">
          {{ larkform.device_id }}
        </el-descriptions-item>
        <el-descriptions-item :label="`${$t('orderImage.pp_image_create_ts')}`">
          {{ formatLocaleDate(larkform.create_ts) }}
        </el-descriptions-item>
      </el-descriptions>

      <el-form v-model="larkform" style="margin-top: 20px">
        <el-form-item :label="`${$t('orderImage.pp_problem_detail')}`">
          <el-input v-model="larkform.detail" :rows="3" type="textarea" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" class="welkin-text-button">
            {{ $t('common.pp_cancel') }}
          </el-button>
          <el-button @click="sendLarkCard" class="welkin-primary-button">
            {{ $t('common.pp_confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onBeforeUnmount, nextTick } from 'vue'
import { SemiSelect, View, Download, Postcard } from '@element-plus/icons-vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { Icon } from '@iconify/vue/dist/iconify'
import { formatLocaleDate, removeNullProp, getUserId, timestampToStr } from '~/utils'
import { iconMap } from '~/auth'
import { useRouter, useRoute } from 'vue-router'
import { apiGetSnapshotList, apiPostLarkcard, apiGetAlgorithmList } from '~/apis/order-image'
import { apiPostLogInfo } from '~/apis/log-export'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { pagination } from '~/constvars'
import { apiGetDevices } from '~/apis/home'
import { debounce } from 'lodash-es'

const $store = useStore()
const $route = useRoute()
const $router = useRouter()
const { t } = useI18n()
const { locale } = useI18n({ useScope: 'global' })
const project = ref(computed(() => $store.state.project))

const algorithmOptions = ref([] as any)

const loading = ref(false)

const tableList = ref([] as any)
const totalNumber = ref(0)

const searchForm = reactive({
  device_id: '',
  ts: new Date(new Date().toLocaleDateString()).getTime(),
  algorithm_name: '',
  time: '',
  page: 1,
  size: 10
})

const webSocket = ref()

const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules>({
  device_id: [
    {
      message: '',
      required: true,
      trigger: 'change'
    }
  ],
  ts: [
    {
      message: '',
      required: true,
      trigger: 'change'
    }
  ],
  algorithm_name: [
    {
      message: '',
      required: true,
      trigger: 'change'
    }
  ]
})

const showImage = ref()
const imgViewVisible = ref(false)

const dialogVisible = ref(false)
const larkform = reactive({
  service_id: '' as string,
  device_id: '' as string,
  description: '' as string,
  alert_type: '' as string,
  algorithm_name: '' as string,
  create_ts: 1600000000000 as number,
  snapshot_url: '' as string,
  detail: '' as string
})

const getDisabledDate = (time: Record<string, any>) => {
  const days = 7 * 24 * 3600 * 1000
  const now = new Date().getTime()
  if (time.getTime() > now || time.getTime() <= now - days) {
    return true
  }
  return false
}

//关闭弹窗
const closeDialog = () => {
  imgViewVisible.value = false
}

const sendLarkCard = () => {
  apiPostLarkcard(project.value.project, {
    ...larkform,
    user_id: getUserId() || 'unknown'
  }).then((res) => {
    dialogVisible.value = false
    ElMessage.success('成功通知算法负责人！')
  })
}

// 发送飞书通知到指定算法负责人
const handleImageLarkcard = (row: any, index: any) => {
  larkform.detail = ''
  Object.assign(larkform, row)
  nextTick(() => {
    dialogVisible.value = true
  })
}

// 发起生成图片文件请求command
const handleImageDownload = async (row: any, index: any) => {
  let query = {
    configuration: {
      key: 'uploadLogFile',
      value: row.file_path
    }
  }

  try {
    const { data } = (await apiPostLogInfo(searchForm.device_id, project.value.project, query)) as any
    if (data.err_code) {
      ElMessage.error(data.message)
    } else {
      ElMessage.success('成功发起下载请求！图片生成中，请勿重复点击')
      sendSocketMessage()
    }
  } catch (error: any) {
    loading.value = false
    ElMessage.error(error)
  }
}

const handleImageView = (row: any, index: any) => {
  showImage.value = row
  nextTick(() => {
    imgViewVisible.value = true
  })
}

// 重置
const resetSelect = () => {
  loading.value = false
  ruleFormRef.value?.resetFields()

  searchForm.device_id = ''
  searchForm.ts = new Date(new Date().toLocaleDateString()).getTime()
  searchForm.algorithm_name = ''
  searchForm.page = 1
  searchForm.size = 10
}

// 进入单站服务列表
const changeToSingleStation = (index: any, row: any) => {
  $router.push({
    path: `/${project.value.route}/device-management/single-station/${row.device_id}`
  })
}

// 发起生成文件树请求command
const refreshTree = () => {
  let query = {
    configuration: {
      key: 'syncFilePath',
      value: '/warehouse/image/aec/'
    }
  }
  return apiPostLogInfo(searchForm.device_id, project.value.project, query)
}

// 重新渲染table数据
const refreshListData = (res: any) => {
  loading.value = false
  if (res.data !== null) {
    tableList.value = res.data
    totalNumber.value = res.total
    tableList.value = tableList.value.map((item: any) => {
      const findObj = algorithmOptions.value.find((obj: any) => obj.name === item.algorithm_name)
      return {
        ...item,
        alert_type: locale.value == 'zh' ? findObj.description : findObj.description_en
      }
    })
  } else {
    tableList.value = []
    totalNumber.value = 0
  }
}

const getFileName = (file_path: any, type: any) => {
  if (!file_path) return '-'
  const parts = file_path.split('/')
  const full_path = parts[parts.length - 1]
  const part = full_path.split('-')
  const short_path = part[part.length - 1]
  return type == 'full' ? full_path : short_path
}

const sendSocketMessage = () => {
  const msg = {
    page: searchForm.page,
    size: searchForm.size,
    update: true
  }
  webSocket.value.send(msg)
}

const closeSocket = () => {
  if (!!webSocket.value && webSocket.value.websocket != null) {
    webSocket.value.close()
  }
}

// 公用搜索事件
const publicSearch = () => {
  searchForm.ts = new Date(searchForm.ts).getTime()
  searchForm.time = timestampToStr(searchForm.ts)
  const ts_invalid = getDisabledDate(new Date(searchForm.ts))
  if (ts_invalid) {
    ElMessage.warning(t('orderImage.pp_select_ts_within_week'))
    return
  }
  closeSocket()
  $router.push({
    path: $router.currentRoute.value.path,
    query: { ...removeNullProp(searchForm) }
  })
  const start_sync_ts = new Date().getTime()
  refreshTree()
    .then((res: any) => {
      if (res.data.err_code) {
        ElMessage.error(res.data.message)
        return
      }
      webSocket.value = apiGetSnapshotList(project.value.project, {
        ...searchForm,
        start_sync_ts: start_sync_ts
      })
      webSocket.value.init(
        (res: any) => {
          refreshListData(res)
        },
        () => {
          loading.value = false
        }
      )
    })
    .catch((err) => {
      loading.value = false
    })
}

// 每页显示条目个数
const handleSizeChange = (val: any) => {
  loading.value = true
  searchForm.size = val
  searchForm.page = 1
  sendSocketMessage()
}

// 当前页数
const handleCurrentChange = (val: any) => {
  loading.value = true
  searchForm.page = val
  sendSocketMessage()
}

// 点击筛选按钮
const filterEvent = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      loading.value = true
      searchForm.size = 10
      searchForm.page = 1
      publicSearch()
    }
  })
}

const initPage = () => {
  let init_params: any = $route.query

  if (!!init_params.page && !!init_params.size) {
    searchForm.page = Number(init_params.page)
    searchForm.size = Number(init_params.size)
  }

  searchForm.device_id = init_params.device_id
  searchForm.algorithm_name = init_params.algorithm_name
  if (!isNaN(init_params.ts)) {
    searchForm.ts = Number(init_params.ts)
  }

  if (searchForm.device_id && searchForm.algorithm_name && searchForm.ts) {
    loading.value = true
    publicSearch()
  }
}

/**
 * @description: 获取算法下拉列表
 * @return {*}
 */
const getAlgorithmList = async () => {
  const res = await apiGetAlgorithmList(project.value.project)
  algorithmOptions.value = res.data
}

const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      initPage()
    } else if (newPath.split('/').length == 3 && newPath.split('/')[2] == oldPath.split('/')[2]) {
      closeSocket()
      resetSelect()
      tableList.value = []
      totalNumber.value = 0
    }
    getAlgorithmList()
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  closeSocket()
  stopWatch()
})
</script>

<style lang="scss" scoped>
.order-image-container {
  font-family: 'Blue Sky Standard';
  display: flex;
  flex-direction: column;
  .header-container {
    padding: 24px 24px 20px;
    background: linear-gradient(180deg, #e2f9f9, #fff);
    .header-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #1f1f1f;
    }
  }
  .content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 24px 24px;
    background-color: #fff;
    :deep(.el-input__inner) {
      color: #262626;
    }
    :deep(.el-form) {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px 24px;
      margin-bottom: 20px;
      .el-form-item {
        margin: 0;
        .el-form-item__label {
          font-size: 14px;
          color: #595959;
          padding-right: 0;
        }
      }
    }
  }
  :deep(.el-dialog__title) {
    color: #1f1f1f;
  }
  :deep(.el-textarea__inner) {
    color: #262626;
  }
  :deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
    color: #595959;
    font-weight: normal;
  }
  :deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content) {
    color: #262626;
  }
}
</style>
