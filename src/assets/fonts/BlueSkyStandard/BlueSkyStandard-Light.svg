<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
<metadata>
Created by FontXChange 20110222 at Fri Jul 22 16:02:56 2016
 By <PERSON> \(c\) 2016 NextEV. All rights reserved.
</metadata>
<defs>
<font id="BlueSkyStandard-Light" horiz-adv-x="641" >
  <font-face 
    font-family="BlueSkyStandard"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="700"
    descent="-300"
    x-height="517"
    cap-height="700"
    bbox="-115 -260 1208 883"
    underline-thickness="50"
    underline-position="-50"
    stemh="49"
    stemv="51"
    unicode-range="U+000D-FB04"
  />
<missing-glyph horiz-adv-x="354" 
d="M177 236c-64 0 -117 53 -117 117c0 65 53 118 117 118s117 -53 117 -118c0 -64 -53 -117 -117 -117z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="558" 
d="M473 0h-51v469h-242v-469h-51v469h-104v48h104v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5c-96 0 -131 -56 -131 -119v-39h293v-517z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="626" 
d="M597 45l14 -39c-16 -10 -47 -16 -65 -16c-63 0 -124 34 -124 145v518c-24 10 -59 22 -106 22c-96 0 -136 -56 -136 -119v-39h150v-48h-150v-469h-51v469h-104v48h104v39c0 105 71 168 184 168c43 0 112 -13 160 -34v-561c0 -58 26 -95 75 -95c20 0 35 3 49 11z" />
    <glyph glyph-name="ff" unicode="ff" horiz-adv-x="678" 
d="M473 0h-51v469h-242v-469h-51v469h-104v48h104v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5c-96 0 -131 -56 -131 -119v-39h242v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5c-96 0 -131 -56 -131 -119v-39h150v-48h-150
v-469z" />
    <glyph glyph-name="ffi" unicode="ffi" horiz-adv-x="851" 
d="M766 0h-51v469h-242v-469h-51v469h-242v-469h-51v469h-104v48h104v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5c-96 0 -131 -56 -131 -119v-39h242v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5
c-96 0 -131 -56 -131 -119v-39h293v-517z" />
    <glyph glyph-name="ffl" unicode="ffl" horiz-adv-x="919" 
d="M890 45l14 -39c-16 -10 -47 -16 -65 -16c-63 0 -124 34 -124 145v518c-24 10 -59 22 -106 22c-96 0 -136 -56 -136 -119v-39h150v-48h-150v-469h-51v469h-242v-469h-51v469h-104v48h104v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5
c-96 0 -131 -56 -131 -119v-39h242v39c0 105 71 168 184 168c43 0 112 -13 160 -34v-561c0 -58 26 -95 75 -95c20 0 35 3 49 11z" />
    <glyph glyph-name="fj" unicode="fj" horiz-adv-x="558" 
d="M261 -253l5 49c12 -2 22 -2 34 -2c72 0 122 47 122 137v538h-242v-469h-51v469h-104v48h104v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5c-96 0 -131 -56 -131 -119v-39h293v-584c0 -105 -61 -188 -175 -188c-12 0 -24 0 -37 2z" />
    <glyph glyph-name="ft" unicode="ft" horiz-adv-x="693" 
d="M644 56l19 -42c-27 -16 -63 -26 -96 -26c-75 0 -145 42 -145 164v317h-242v-469h-51v469h-104v48h104v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5c-96 0 -131 -56 -131 -119v-39h242v145h51v-145h181v-48h-181v-317c0 -84 45 -116 94 -116
c34 0 59 10 77 20z" />
    <glyph glyph-name=".notdef" horiz-adv-x="354" 
d="M177 236c-64 0 -117 53 -117 117c0 65 53 118 117 118s117 -53 117 -118c0 -64 -53 -117 -117 -117z" />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="270" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="236" 
d="M138 142h-41l-9 558h60zM118 -9c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="329" 
d="M117 465h-30l-12 235h54zM242 465h-30l-12 235h54z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="729" 
d="M647 195h-164l-38 -195h-49l38 195h-204l-38 -195h-49l38 195h-126v45h135l47 236h-155v45h164l36 179h47l-35 -179h205l36 179h47l-35 -179h127v-45h-136l-46 -236h155v-45zM443 240l47 236h-205l-46 -236h204z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="887" 
d="M221 281c-129 0 -166 100 -166 215c0 116 37 216 166 216s166 -100 166 -215c0 -116 -37 -216 -166 -216zM221 323c88 0 118 70 118 174c0 103 -31 172 -118 172c-88 0 -118 -69 -118 -173c0 -103 31 -173 118 -173zM279 0h-47l375 700h47zM666 -12
c-129 0 -166 100 -166 215c0 116 37 216 166 216s166 -100 166 -215c0 -116 -37 -216 -166 -216zM666 30c88 0 118 70 118 174c0 103 -31 172 -118 172c-88 0 -118 -69 -118 -173c0 -103 31 -173 118 -173z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="697" 
d="M646 -6l-116 103c-50 -59 -125 -109 -242 -109c-121 0 -233 68 -233 198c0 80 59 165 148 192c-43 38 -83 93 -83 156c0 83 68 178 203 178c67 0 139 -27 196 -75l-28 -41c-44 37 -104 67 -168 67c-107 0 -151 -69 -151 -130c0 -56 37 -99 85 -140l265 -230
c23 40 35 91 35 139c0 20 -2 40 -5 57h47c3 -15 4 -30 4 -48c0 -60 -12 -119 -45 -178l119 -104zM494 127l-257 221c-82 -25 -130 -93 -130 -162c0 -89 82 -150 180 -150c103 0 165 41 207 91z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="204" 
d="M117 465h-30l-12 235h54z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="357" 
d="M317 -163l-26 -35c-135 73 -221 211 -221 441s86 368 221 441l26 -35c-148 -95 -195 -218 -195 -406s47 -311 195 -406z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="357" 
d="M66 -198l-26 35c148 95 195 218 195 406s-47 311 -195 406l26 35c135 -73 221 -211 221 -441s-86 -368 -221 -441z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="464" 
d="M389 353l-33 -25l-124 149l-124 -149l-33 25l123 145l-153 62l15 38l152 -64v166h40v-166l152 64l15 -38l-153 -62z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="538" 
d="M292 110h-46v196h-196v46h196v196h46v-196h196v-46h-196v-196z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="200" 
d="M55 -112v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="411" 
d="M366 306h-321v46h321v-46z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="200" 
d="M100 -9c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="445" 
d="M77 0h-52l342 700h53z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="594" 
d="M297 37c147 0 185 141 185 313s-38 313 -185 313c-146 0 -184 -141 -184 -313s38 -313 184 -313zM297 -12c-188 0 -237 170 -237 362s49 362 237 362c189 0 237 -170 237 -362s-48 -362 -237 -362z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="443" 
d="M408 0h-358v47h161v602l-161 -69v51l174 73h37v-657h147v-47z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="565" 
d="M505 0h-440v48c0 133 103 194 203 253c94 55 184 116 184 211c0 83 -70 151 -160 151c-66 0 -126 -31 -177 -72l-31 39c55 45 121 82 205 82c119 0 215 -90 215 -197c0 -125 -109 -194 -208 -254c-93 -56 -182 -111 -178 -212h387v-49z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="566" 
d="M50 78l21 41c60 -55 134 -82 200 -82c94 0 183 61 183 177c0 68 -54 147 -196 147h-62l-16 42l227 248h-312v49h382v-42l-236 -254h22c162 0 243 -95 243 -188c0 -149 -117 -226 -238 -226c-76 0 -164 30 -218 88z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="587" 
d="M450 0h-51v188h-347l-10 43l265 487h59l-263 -482h296v282h51v-282h107v-48h-107v-188z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="568" 
d="M50 76l21 41c60 -55 138 -82 204 -82c94 0 186 61 186 177c0 129 -112 165 -228 165c-40 0 -84 -3 -120 -10l-23 24l30 309h361v-48h-316l-24 -236c34 5 72 8 101 8c137 0 271 -48 271 -213c0 -149 -120 -223 -241 -223c-76 0 -168 30 -222 88z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="603" 
d="M478 701l-9 -48c-18 3 -46 9 -83 9c-165 0 -253 -138 -273 -287c35 31 119 84 214 84c137 0 221 -91 221 -219c0 -148 -110 -253 -241 -253c-186 0 -247 169 -247 310c0 203 97 412 324 412c35 0 71 -4 94 -8zM110 316c0 -223 106 -282 197 -282c113 0 192 97 192 206
c0 108 -73 172 -178 172c-113 0 -201 -83 -211 -96z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="525" 
d="M186 0h-55l292 652h-378v48h435v-33z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="588" 
d="M371 372c108 -26 162 -104 162 -181c0 -127 -92 -203 -239 -203c-146 0 -239 76 -239 203c0 76 55 155 162 181c-100 42 -133 109 -133 164c0 113 93 176 210 176s211 -63 211 -176c0 -55 -33 -122 -134 -164zM294 352c-96 -9 -186 -76 -186 -165s68 -152 186 -152
s187 63 187 152c0 90 -91 156 -187 165zM294 388c81 22 159 69 159 151c0 83 -76 126 -159 126c-82 0 -158 -43 -158 -126c0 -82 78 -129 158 -151z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="603" 
d="M125 -4l9 48c18 -3 46 -9 83 -9c165 0 253 138 273 287c-35 -31 -119 -84 -214 -84c-137 0 -221 91 -221 219c0 148 110 253 241 253c186 0 247 -169 247 -310c0 -203 -97 -412 -324 -412c-35 0 -71 4 -94 8zM493 381c0 223 -106 282 -197 282c-113 0 -192 -97 -192 -206
c0 -108 73 -172 178 -172c113 0 201 83 211 96z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="228" 
d="M114 420c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50zM114 -9c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="228" 
d="M70 -112v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144zM114 420c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="523" 
d="M468 155l-418 164v58l418 164v-52l-375 -141l375 -141v-52z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="548" 
d="M493 398h-438v47h438v-47zM493 212h-438v47h438v-47z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="523" 
d="M473 319l-418 -164v52l375 141l-375 141v52l418 -164v-58z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="483" 
d="M209 176h-51l-1 164c138 -3 219 72 219 175c0 76 -67 149 -157 149c-61 0 -122 -29 -165 -68l-29 41c42 36 113 75 193 75c130 0 210 -98 210 -196c0 -104 -73 -196 -218 -218zM187 -9c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1038" 
d="M813 -63l27 -37c-58 -43 -173 -99 -318 -99c-239 0 -452 200 -452 455s217 455 461 455c230 0 437 -142 437 -393c0 -194 -92 -284 -182 -284c-63 0 -105 41 -113 95c-43 -55 -108 -85 -166 -85c-129 0 -207 108 -207 216c0 109 78 218 207 218c61 0 115 -24 163 -84v74
h44v-299c0 -59 24 -94 76 -94c62 0 132 74 132 243c0 192 -159 347 -391 347c-246 0 -413 -201 -413 -409c0 -214 168 -410 404 -410c126 0 234 45 291 91zM668 186v151c-28 45 -76 98 -160 98c-104 0 -160 -85 -160 -175c0 -89 56 -172 160 -172c84 0 132 52 160 98z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="675" 
d="M655 0h-55l-79 198h-366l-79 -198h-56l288 702h61zM503 245l-165 405l-165 -405h330z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="602" 
d="M342 0h-247v700h236c112 0 188 -77 188 -180c0 -62 -39 -132 -96 -166c88 -23 134 -86 134 -161c0 -91 -67 -193 -215 -193zM148 49h201c107 0 153 75 153 143c0 73 -47 136 -176 136h-178v-279zM148 376h178c80 0 139 67 139 141c0 64 -39 134 -139 134h-178v-275z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="689" 
d="M640 150l29 -41c-45 -49 -143 -121 -266 -121c-228 0 -363 178 -363 363c0 178 130 361 354 361c107 0 189 -41 241 -89l-28 -41c-59 54 -134 80 -213 80c-190 0 -300 -156 -300 -310c0 -157 115 -314 309 -314c96 0 188 50 237 112z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="698" 
d="M148 49h182c163 0 273 134 273 301s-110 301 -273 301h-182v-602zM331 0h-236v700h236c205 0 327 -171 327 -350s-122 -350 -327 -350z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="577" 
d="M522 0h-427v700h427v-49h-374v-262h305v-49h-305v-291h374v-49z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="565" 
d="M148 0h-53v700h415v-49h-362v-284h293v-49h-293v-318z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="759" 
d="M639 132v183h-240v47h290v-249c-53 -59 -149 -125 -290 -125c-228 0 -359 179 -359 364c0 178 126 360 350 360c97 0 188 -30 266 -92c-13 -16 -22 -31 -34 -47c-64 60 -141 89 -232 89c-190 0 -296 -154 -296 -309c0 -156 111 -315 305 -315c113 0 185 37 240 94z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="739" 
d="M644 0h-52v356h-445v-356h-52v700h52v-295h445v295h52v-700z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="242" 
d="M147 0h-52v700h52v-700z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="468" 
d="M326 173v527h52v-535c0 -132 -103 -177 -186 -177c-65 0 -124 23 -173 92l43 32c33 -55 82 -75 130 -75c58 0 134 29 134 136z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="587" 
d="M148 0h-53v700h53v-700zM589 0h-74l-338 346l307 354h70l-307 -352z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="473" 
d="M448 0h-353v700h52v-651h301v-49z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="838" 
d="M743 0h-53v584l-271 -456l-272 456v-584h-52v700h44l280 -475l279 475h45v-700z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="758" 
d="M663 0h-57l-459 621v-621h-52v700h56l461 -624v624h51v-700z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="778" 
d="M389 36c179 0 294 143 294 314s-115 314 -294 314c-181 0 -295 -143 -295 -314s114 -314 295 -314zM389 -12c-211 0 -349 157 -349 362s138 362 349 362c210 0 349 -157 349 -362s-139 -362 -349 -362z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="579" 
d="M147 321h143c150 0 198 85 198 164c0 97 -56 166 -201 166h-140v-330zM147 0h-52v700h216c142 0 233 -93 233 -213c0 -132 -108 -216 -228 -216h-169v-271z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="778" 
d="M695 21l-37 -34l-62 63c-57 -40 -127 -62 -207 -62c-211 0 -349 157 -349 362s138 362 349 362c210 0 349 -157 349 -362c0 -106 -38 -200 -104 -266zM559 90l-128 134l35 34l131 -136c55 61 86 140 86 228c0 171 -115 314 -294 314c-181 0 -295 -143 -295 -314
s114 -314 295 -314c64 0 122 19 170 54z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="622" 
d="M587 0h-65l-189 299h-185v-299h-53v700h246c133 0 222 -91 222 -203c0 -95 -71 -172 -174 -191zM148 348h177c140 0 183 78 183 150c0 80 -43 153 -192 153h-168v-303z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="532" 
d="M32 71l30 45c46 -42 133 -77 203 -77c108 0 173 65 173 147c0 85 -84 117 -191 153c-90 30 -188 71 -188 194c0 103 88 179 201 179c87 0 159 -35 202 -81l-36 -41c-45 42 -103 72 -163 72c-89 0 -149 -62 -149 -131c0 -88 75 -113 178 -150c101 -37 200 -82 200 -193
c0 -118 -93 -200 -228 -200c-71 0 -161 24 -232 83z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="552" 
d="M303 0h-53v651h-225v49h502v-49h-224v-651z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="721" 
d="M583 260v440h53v-437c0 -164 -115 -275 -275 -275c-161 0 -276 111 -276 275v437h53v-440c0 -133 87 -223 223 -223c135 0 222 90 222 223z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="638" 
d="M350 -2h-62l-266 702h56l239 -647h1l241 647h57c-89 -236 -178 -466 -266 -702z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1007" 
d="M758 -9h-59l-196 632l-194 -632h-60l-214 709h56l191 -651l197 651h49l197 -651l192 651h55z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="593" 
d="M563 0h-65l-202 323l-201 -323h-65l234 361l-216 339h60l188 -301c60 97 128 204 189 301h60l-216 -339z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="579" 
d="M315 0h-52v293c-81 135 -163 272 -245 407h60l211 -358l210 358h62l-246 -407v-293z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="598" 
d="M563 0h-528v18l430 633h-415v49h501v-16l-434 -635h446v-49z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="347" 
d="M307 -212h-207v912h207v-47h-158v-818h158v-47z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="445" 
d="M420 0h-52l-343 700h53z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="347" 
d="M247 -212h-207v47h158v818h-158v47h207v-912z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="385" 
d="M350 394h-50l-108 251l-107 -251h-50l140 306h35z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="471" 
d="M471 -46h-471v46h471v-46z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="503" 
d="M300 583h-49l-116 138h76z" />
    <glyph glyph-name="b" unicode="b" 
d="M134 0h-49v700h51v-268c52 61 126 100 209 100c163 0 251 -135 251 -271c0 -137 -88 -273 -251 -273c-76 0 -152 29 -211 102v-90zM136 361v-202c34 -57 103 -123 207 -123c129 0 200 115 200 225s-71 223 -200 223c-103 0 -171 -67 -207 -123z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="560" 
d="M490 117l35 -34c-37 -47 -110 -95 -208 -95c-156 0 -272 123 -272 271c0 150 109 273 263 273c70 0 142 -26 196 -71c-11 -14 -18 -27 -28 -41c-44 36 -104 63 -168 63c-106 0 -210 -91 -210 -224c0 -137 111 -222 218 -222c89 0 145 45 174 80z" />
    <glyph glyph-name="d" unicode="d" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -201 -115 -201 -225s71 -223 201 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 151 -29 211 -102v270h49v-700z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="568" 
d="M474 108l34 -32c-38 -45 -107 -88 -204 -88c-159 0 -259 123 -259 271c0 150 95 273 248 273s239 -112 234 -281h-430c6 -130 103 -213 209 -213c77 0 130 31 168 70zM100 300h373c-3 99 -68 184 -180 184c-98 0 -175 -73 -193 -184z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="385" 
d="M180 0h-51v469h-104v48h104v39c0 105 66 168 179 168c19 0 40 -1 62 -5l-5 -49c-19 3 -37 5 -54 5c-96 0 -131 -56 -131 -119v-39h150v-48h-150v-469z" />
    <glyph glyph-name="g" unicode="g" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -201 -115 -201 -225s71 -223 201 -223c103 0 171 67 207 123zM505 -22v110c-52 -61 -126 -100 -209 -100c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-536c0 -169 -128 -239 -247 -239
c-88 0 -161 35 -216 77l29 43c47 -37 107 -71 190 -71c92 0 193 67 193 187z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="636" 
d="M561 0h-51v293c0 133 -80 191 -179 191c-73 0 -152 -50 -195 -124v-360h-51v700h51v-271c47 60 116 103 209 103c97 0 216 -61 216 -234v-298z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="221" 
d="M136 0h-51v517h51v-517zM111 609c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="257" 
d="M-40 -253l5 49c12 -2 22 -2 34 -2c72 0 122 47 122 137v586h51v-584c0 -105 -61 -188 -175 -188c-12 0 -24 0 -37 2zM146 609c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="522" 
d="M136 0h-51v700h51v-700zM512 0h-74l-274 263l242 254h71l-242 -253z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="289" 
d="M260 45l14 -39c-20 -11 -44 -16 -65 -16c-63 0 -124 34 -124 145v565h51v-571c0 -58 26 -95 72 -95c24 0 40 5 52 11z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1012" 
d="M937 0h-51v293c0 133 -65 191 -159 191c-71 0 -142 -47 -190 -124v-360h-51v298c0 133 -66 186 -159 186c-83 0 -147 -51 -191 -124v-360h-51v517h48v-88c50 63 113 103 202 103c70 0 144 -33 182 -121c51 70 121 121 219 121c97 0 201 -61 201 -234v-298z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="636" 
d="M561 0h-51v293c0 133 -80 191 -179 191c-73 0 -152 -50 -195 -124v-360h-51v517h49v-90c47 60 118 105 211 105c97 0 216 -61 216 -234v-298z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="620" 
d="M310 37c105 0 212 87 212 223s-107 223 -212 223s-213 -87 -213 -223s108 -223 213 -223zM310 -12c-134 0 -265 100 -265 272s131 272 265 272c133 0 265 -100 265 -272s-132 -272 -265 -272z" />
    <glyph glyph-name="p" unicode="p" 
d="M136 360v-201c34 -57 103 -123 207 -123c130 0 200 115 200 225s-70 223 -200 223c-103 0 -171 -68 -207 -124zM134 -235h-49v752h49v-88c52 62 127 103 211 103c163 0 251 -135 251 -271c0 -137 -88 -273 -251 -273c-76 0 -152 29 -211 102v-325z" />
    <glyph glyph-name="q" unicode="q" 
d="M556 -235h-51v323c-52 -61 -126 -100 -209 -100c-163 0 -251 135 -251 271s88 273 251 273c76 0 151 -30 211 -103v88h49v-752zM505 159v201c-34 57 -103 124 -207 124c-130 0 -201 -115 -201 -225c0 -111 71 -223 201 -223c103 0 171 67 207 123z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="350" 
d="M135 0h-50v517h48v-113c38 79 106 122 175 122c8 0 18 0 27 -1v-49c-7 1 -14 1 -21 1c-92 -1 -148 -73 -179 -149v-328z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="461" 
d="M47 32l19 46c51 -26 96 -42 153 -42c76 0 147 38 147 112c0 63 -71 82 -143 96c-84 17 -175 40 -175 139c0 91 77 149 176 149c56 0 118 -14 171 -51l-21 -46c-43 31 -93 49 -151 49c-72 0 -124 -40 -124 -100c0 -61 63 -76 142 -92c83 -17 177 -41 177 -143
c0 -97 -93 -161 -198 -161c-58 0 -117 16 -173 44z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="390" 
d="M341 56l19 -42c-27 -16 -63 -26 -96 -26c-75 0 -145 42 -145 164v317h-94v48h94v145h51v-145h181v-48h-181v-317c0 -84 45 -116 94 -116c34 0 59 10 77 20z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="636" 
d="M551 0h-51v91c-50 -61 -120 -103 -209 -103c-98 0 -216 61 -216 234v295h51v-290c0 -133 80 -191 179 -191c78 0 151 50 195 124v357h51v-517z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="508" 
d="M280 -3h-51l-214 520h56l184 -465l182 465h56z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="811" 
d="M609 0h-66l-137 424l-138 -424h-66l-182 517h55l161 -465l150 465h42l149 -465l159 465h55z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="499" 
d="M487 0h-63l-174 232l-174 -232h-64l205 269l-188 248h61l160 -211l159 211h61l-187 -248z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="496" 
d="M192 -103l36 95l-213 525h56l182 -461l174 461h54l-242 -625c-37 -96 -79 -140 -145 -140c-17 0 -35 3 -55 8l13 48c14 -4 30 -7 43 -7c40 0 69 23 97 96z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="530" 
d="M485 0h-445v13l332 456h-311v48h402v-12l-335 -456h357v-49z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="375" 
d="M325 -214h-36c-89 0 -178 39 -173 173l6 147c3 62 -5 120 -62 120v34c57 0 65 58 62 120l-6 147c-5 134 84 173 173 173h36v-47h-33c-77 0 -131 -25 -126 -141l6 -132c4 -83 -19 -121 -54 -137c35 -16 58 -54 54 -137l-6 -132c-5 -116 49 -141 126 -141h33v-47z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="249" 
d="M149 -250h-49v950h49v-950z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="375" 
d="M86 -214h-36v47h33c77 0 131 25 126 141l-6 132c-4 83 19 121 54 137c-35 16 -58 54 -54 137l6 132c5 116 -49 141 -126 141h-33v47h36c89 0 178 -39 173 -173l-6 -147c-3 -62 5 -120 62 -120v-34c-57 0 -65 -58 -62 -120l6 -147c5 -134 -84 -173 -173 -173z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="464" 
d="M257 -158h-47v631h-165v44h165v195h47v-195h162v-44h-162v-631z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="581" 
d="M522 66l19 -40c-31 -29 -78 -38 -133 -38c-73 0 -167 15 -258 15c-32 0 -68 -3 -100 -8v49c67 0 133 53 133 159c0 37 -6 72 -20 118h-102v47h89c-17 53 -23 96 -23 133c0 154 115 211 204 211c72 0 125 -20 171 -55l-20 -42c-41 28 -87 50 -146 50
c-118 0 -159 -78 -159 -170c0 -41 8 -85 22 -127h242v-47h-228c13 -50 20 -87 20 -119c0 -96 -54 -142 -71 -156c20 2 41 2 52 2c61 0 135 -13 198 -13c43 0 82 10 110 31z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="578" 
d="M55 -167l29 48c69 -52 135 -75 205 -75c102 0 174 67 174 145c0 87 -90 112 -180 134c-101 26 -212 54 -212 180c0 57 22 103 58 137c-35 29 -58 67 -58 126c0 109 96 182 210 182c92 0 165 -29 215 -74l-29 -43c-48 44 -106 68 -172 68c-96 0 -170 -51 -170 -141
c0 -77 94 -108 181 -133c101 -29 210 -60 210 -174c0 -58 -18 -103 -51 -144c31 -26 51 -59 51 -107c0 -122 -94 -209 -220 -209c-79 0 -162 23 -241 80zM420 94c28 33 43 68 43 108c0 83 -94 115 -181 139c-35 10 -71 20 -107 35c-32 -30 -50 -64 -50 -118
c0 -81 91 -104 180 -128c37 -10 78 -21 115 -36z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="354" 
d="M177 236c-64 0 -117 53 -117 117c0 65 53 118 117 118s117 -53 117 -118c0 -64 -53 -117 -117 -117z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="643" 
d="M537 -177h-48v832h-151v-832h-48v503h-38c-117 0 -207 77 -207 188c0 109 90 186 207 186h361v-45h-76v-832zM290 371v284h-32c-79 0 -161 -41 -161 -142s82 -142 161 -142h32z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="519" 
d="M429 482l36 -12c-16 -66 -58 -107 -118 -107c-48 0 -82 29 -110 56c-23 22 -44 37 -73 37c-38 0 -66 -27 -79 -79l-36 12c16 66 56 107 116 107c48 0 82 -29 110 -56c23 -22 44 -37 73 -37c38 0 68 27 81 79zM429 302l36 -12c-16 -66 -58 -107 -118 -107
c-48 0 -82 29 -110 56c-23 22 -44 37 -73 37c-38 0 -66 -27 -79 -79l-36 12c16 66 56 107 116 107c48 0 82 -29 110 -56c23 -22 44 -37 73 -37c38 0 68 27 81 79z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="576" 
d="M268 68l-223 202l223 202l29 -29l-192 -173l192 -173zM499 68l-223 202l223 202l29 -29l-192 -173l192 -173z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="576" 
d="M531 270l-223 -202l-29 29l192 173l-192 173l29 29zM300 270l-223 -202l-29 29l192 173l-192 173l29 29z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="689" 
d="M99 -10c-27 0 -48 22 -48 49c0 28 21 50 48 50c28 0 49 -22 49 -50c0 -27 -21 -49 -49 -49zM342 -10c-27 0 -48 22 -48 49c0 28 21 50 48 50c28 0 49 -22 49 -50c0 -27 -21 -49 -49 -49zM590 -10c-28 0 -49 22 -49 49c0 28 21 50 49 50c27 0 48 -22 48 -50
c0 -27 -21 -49 -48 -49z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="551" 
d="M506 300h-461v47h461v-47z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="889" 
d="M844 300h-799v47h799v-47z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="350" 
d="M296 721v-36c-38 -11 -51 -44 -51 -73c25 4 55 -7 55 -45c0 -27 -21 -49 -49 -49c-31 0 -50 24 -50 59c0 54 24 122 95 144zM145 721v-36c-38 -11 -51 -44 -51 -73c25 4 55 -7 55 -45c0 -27 -21 -49 -49 -49c-31 0 -50 24 -50 59c0 54 24 122 95 144z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="350" 
d="M54 509v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144zM205 509v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="199" 
d="M145 720v-36c-38 -11 -51 -44 -51 -73c25 4 55 -7 55 -45c0 -27 -21 -49 -49 -49c-31 0 -50 24 -50 59c0 54 24 122 95 144z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="199" 
d="M54 509v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="538" 
d="M270 460c-23 0 -41 19 -41 42c0 22 18 41 41 41c22 0 40 -19 40 -41c0 -23 -18 -42 -40 -42zM270 115c-23 0 -41 19 -41 42c0 22 18 41 41 41c22 0 40 -19 40 -41c0 -23 -18 -42 -40 -42zM488 306h-438v46h438v-46z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="205" 
d="M-58 0h-52l373 700h52z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="345" 
d="M268 68l-223 202l223 202l29 -29l-192 -173l192 -173z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="345" 
d="M300 270l-223 -202l-29 29l192 173l-192 173l29 29z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="474" 
d="M262 -158h-47v353h-165v44h165v234h-165v44h165v195h47v-195h162v-44h-162v-234h162v-44h-162v-353z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="217" 
d="M109 235c-28 0 -49 22 -49 49c0 28 21 50 49 50c27 0 48 -22 48 -50c0 -27 -21 -49 -48 -49z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="199" 
d="M54 -114v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="350" 
d="M54 -114v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144zM205 -114v36c38 11 51 44 51 73c-25 -4 -55 7 -55 45c0 27 21 49 49 49c31 0 50 -24 50 -59c0 -54 -24 -122 -95 -144z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1263" 
d="M221 281c-129 0 -166 100 -166 215c0 116 37 216 166 216s166 -100 166 -215c0 -116 -37 -216 -166 -216zM221 323c88 0 118 70 118 174c0 103 -31 172 -118 172c-88 0 -118 -69 -118 -173c0 -103 31 -173 118 -173zM282 0h-50l374 700h47zM666 -12
c-129 0 -166 100 -166 215c0 116 37 216 166 216s166 -100 166 -215c0 -116 -37 -216 -166 -216zM666 30c88 0 118 70 118 174c0 103 -31 172 -118 172c-88 0 -118 -69 -118 -173c0 -103 31 -173 118 -173zM1042 -12c-129 0 -166 100 -166 215c0 116 37 216 166 216
s166 -100 166 -215c0 -116 -37 -216 -166 -216zM1042 30c88 0 118 70 118 174c0 103 -31 172 -118 172c-88 0 -118 -69 -118 -173c0 -103 31 -173 118 -173z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="220" 
d="M135 0h-50v517h50v-517z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="483" 
d="M381 583h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="509" 
d="M382 697l37 -19c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="322" 
d="M161 608c32 0 57 27 57 60s-25 60 -57 60s-57 -27 -57 -60s25 -60 57 -60zM258 668c0 -53 -42 -96 -97 -96s-97 43 -97 96s42 96 97 96s97 -43 97 -96z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="352" 
d="M80 -177l23 40c64 -61 147 -49 147 0c0 44 -65 50 -107 38l-16 22l54 102h50l-45 -81c47 8 112 -17 112 -76c0 -48 -39 -91 -109 -91c-33 0 -76 15 -109 46z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="479" 
d="M163 583h-49l89 138h76zM332 583h-49l89 138h76z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="560" 
d="M198 -177l23 40c64 -61 147 -49 147 0c0 44 -65 50 -107 38l-16 22l35 68c-137 17 -235 132 -235 268c0 150 109 273 263 273c70 0 142 -26 196 -71l-28 -41c-44 36 -104 63 -168 63c-106 0 -210 -91 -210 -224c0 -137 111 -222 218 -222c89 0 145 45 174 80l35 -34
c-36 -45 -105 -91 -197 -94l-24 -45c47 8 112 -17 112 -76c0 -48 -39 -91 -109 -91c-33 0 -76 15 -109 46z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="568" 
d="M474 108l34 -32c-38 -45 -107 -88 -204 -88c-159 0 -259 123 -259 271c0 150 95 273 248 273s239 -112 234 -281h-430c6 -130 103 -213 209 -213c77 0 130 31 168 70zM100 300h373c-3 99 -68 184 -180 184c-98 0 -175 -73 -193 -184zM313 583h-49l89 138h76z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="568" 
d="M474 108l34 -32c-38 -45 -107 -88 -204 -88c-159 0 -259 123 -259 271c0 150 95 273 248 273s239 -112 234 -281h-430c6 -130 103 -213 209 -213c77 0 130 31 168 70zM100 300h373c-3 99 -68 184 -180 184c-98 0 -175 -73 -193 -184zM318 583h-49l-116 138h76z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="620" 
d="M310 37c105 0 212 87 212 223s-107 223 -212 223s-213 -87 -213 -223s108 -223 213 -223zM310 -12c-134 0 -265 100 -265 272s131 272 265 272c133 0 265 -100 265 -272s-132 -272 -265 -272zM438 697l37 -19c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60
c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="620" 
d="M310 37c105 0 212 87 212 223s-107 223 -212 223s-213 -87 -213 -223s108 -223 213 -223zM310 -12c-134 0 -265 100 -265 272s131 272 265 272c133 0 265 -100 265 -272s-132 -272 -265 -272zM450 568h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="620" 
d="M310 37c105 0 212 87 212 223s-107 223 -212 223s-213 -87 -213 -223s108 -223 213 -223zM310 -12c-134 0 -265 100 -265 272s131 272 265 272c133 0 265 -100 265 -272s-132 -272 -265 -272zM334 570h-49l-116 138h76z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="620" 
d="M310 37c105 0 212 87 212 223s-107 223 -212 223s-213 -87 -213 -223s108 -223 213 -223zM310 -12c-134 0 -265 100 -265 272s131 272 265 272c133 0 265 -100 265 -272s-132 -272 -265 -272zM326 570h-49l89 138h76z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="636" 
d="M561 0h-51v293c0 133 -80 191 -179 191c-73 0 -152 -50 -195 -124v-360h-51v517h49v-90c47 60 118 105 211 105c97 0 216 -61 216 -234v-298zM453 697l37 -19c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81
c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="568" 
d="M474 108l34 -32c-38 -45 -107 -88 -204 -88c-159 0 -259 123 -259 271c0 150 95 273 248 273s239 -112 234 -281h-430c6 -130 103 -213 209 -213c77 0 130 31 168 70zM100 300h373c-3 99 -68 184 -180 184c-98 0 -175 -73 -193 -184zM199 599c-25 0 -45 21 -45 46
c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM379 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="568" 
d="M474 108l34 -32c-38 -45 -107 -88 -204 -88c-159 0 -259 123 -259 271c0 150 95 273 248 273s239 -112 234 -281h-430c6 -130 103 -213 209 -213c77 0 130 31 168 70zM100 300h373c-3 99 -68 184 -180 184c-98 0 -175 -73 -193 -184zM430 583h-55l-85 97l-85 -97h-55
l109 138h62z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="636" 
d="M551 0h-51v91c-50 -61 -120 -103 -209 -103c-98 0 -216 61 -216 234v295h51v-290c0 -133 80 -191 179 -191c78 0 151 50 195 124v357h51v-517zM313 583h-49l89 138h76z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="636" 
d="M551 0h-51v91c-50 -61 -120 -103 -209 -103c-98 0 -216 61 -216 234v295h51v-290c0 -133 80 -191 179 -191c78 0 151 50 195 124v357h51v-517zM355 583h-49l-116 138h76z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="636" 
d="M551 0h-51v91c-50 -61 -120 -103 -209 -103c-98 0 -216 61 -216 234v295h51v-290c0 -133 80 -191 179 -191c78 0 151 50 195 124v357h51v-517zM454 583h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="636" 
d="M551 0h-51v91c-50 -61 -120 -103 -209 -103c-98 0 -216 61 -216 234v295h51v-290c0 -133 80 -191 179 -191c78 0 151 50 195 124v357h51v-517zM222 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM402 599c-25 0 -45 21 -45 46
c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="315" 
d="M163 583h-49l89 138h76z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="461" 
d="M125 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM305 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="778" 
d="M389 36c179 0 294 143 294 314s-115 314 -294 314c-181 0 -295 -143 -295 -314s114 -314 295 -314zM389 -12c-211 0 -349 157 -349 362s138 362 349 362c210 0 349 -157 349 -362s-139 -362 -349 -362zM300 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45
c0 -25 -20 -46 -44 -46zM480 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="721" 
d="M583 260v440h53v-437c0 -164 -115 -275 -275 -275c-161 0 -276 111 -276 275v437h53v-440c0 -133 87 -223 223 -223c135 0 222 90 222 223zM271 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM451 749c-25 0 -45 21 -45 46
c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="675" 
d="M655 0h-55l-79 198h-366l-79 -198h-56l288 702h61zM503 245l-165 405l-165 -405h330zM248 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM428 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45
c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="675" 
d="M655 0h-55l-79 198h-366l-79 -198h-56l277 676c-33 15 -55 48 -55 87c0 53 42 96 97 96s97 -43 97 -96c0 -39 -23 -73 -56 -88zM503 245l-165 405l-165 -405h330zM339 703c32 0 57 27 57 60s-25 60 -57 60s-57 -27 -57 -60s25 -60 57 -60z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="675" 
d="M655 0h-55l-79 198h-366l-79 -198h-56l288 702h61zM503 245l-165 405l-165 -405h330zM356 735h-49l-116 138h76z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="675" 
d="M655 0h-55l-79 198h-366l-79 -198h-56l288 702h61zM503 245l-165 405l-165 -405h330zM480 856l37 -19c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="778" 
d="M389 36c179 0 294 143 294 314s-115 314 -294 314c-181 0 -295 -143 -295 -314s114 -314 295 -314zM389 -12c-211 0 -349 157 -349 362s138 362 349 362c210 0 349 -157 349 -362s-139 -362 -349 -362zM518 878l37 -19c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60
c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1165" 
d="M1110 0h-427v144c-59 -95 -163 -156 -294 -156c-211 0 -349 157 -349 362s138 362 349 362c131 0 235 -62 294 -158v146h427v-49h-374v-262h305v-49h-305v-291h374v-49zM683 337v26c-6 165 -120 301 -294 301c-181 0 -295 -143 -295 -314s114 -314 295 -314
c174 0 288 135 294 301z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="496" 
d="M192 -103l36 95l-213 525h56l182 -461l174 461h54l-242 -625c-37 -96 -79 -140 -145 -140c-17 0 -35 3 -55 8l13 48c14 -4 30 -7 43 -7c40 0 69 23 97 96zM161 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM341 599
c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="579" 
d="M315 0h-52v293c-81 135 -163 272 -245 407h60l211 -358l210 358h62l-246 -407v-293zM199 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM379 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45
c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="675" 
d="M655 0h-55l-79 198h-366l-79 -198h-56l288 702h61zM503 245l-165 405l-165 -405h330zM477 744h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="577" 
d="M522 0h-427v700h427v-49h-374v-262h305v-49h-305v-291h374v-49zM447 744h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="675" 
d="M655 0h-55l-79 198h-366l-79 -198h-56l288 702h61zM503 245l-165 405l-165 -405h330zM362 744h-49l89 138h76z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="577" 
d="M522 0h-427v700h427v-49h-374v-262h305v-49h-305v-291h374v-49zM218 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM398 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="577" 
d="M522 0h-427v700h427v-49h-374v-262h305v-49h-305v-291h374v-49zM344 744h-49l-116 138h76z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="242" 
d="M147 0h-52v700h52v-700zM148 744h-49l89 138h76z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="242" 
d="M147 0h-52v700h52v-700zM261 744h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="242" 
d="M147 0h-52v700h52v-700zM32 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM212 749c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="242" 
d="M147 0h-52v700h52v-700zM146 744h-49l-116 138h76z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="778" 
d="M389 36c179 0 294 143 294 314s-115 314 -294 314c-181 0 -295 -143 -295 -314s114 -314 295 -314zM389 -12c-211 0 -349 157 -349 362s138 362 349 362c210 0 349 -157 349 -362s-139 -362 -349 -362zM406 744h-49l89 138h76z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="778" 
d="M389 36c179 0 294 143 294 314s-115 314 -294 314c-181 0 -295 -143 -295 -314s114 -314 295 -314zM389 -12c-211 0 -349 157 -349 362s138 362 349 362c210 0 349 -157 349 -362s-139 -362 -349 -362zM529 744h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="721" 
d="M583 260v440h53v-437c0 -164 -115 -275 -275 -275c-161 0 -276 111 -276 275v437h53v-440c0 -133 87 -223 223 -223c135 0 222 90 222 223zM364 744h-49l89 138h76z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="721" 
d="M583 260v440h53v-437c0 -164 -115 -275 -275 -275c-161 0 -276 111 -276 275v437h53v-440c0 -133 87 -223 223 -223c135 0 222 90 222 223zM500 744h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="721" 
d="M583 260v440h53v-437c0 -164 -115 -275 -275 -275c-161 0 -276 111 -276 275v437h53v-440c0 -133 87 -223 223 -223c135 0 222 90 222 223zM410 744h-49l-116 138h76z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="778" 
d="M389 36c179 0 294 143 294 314s-115 314 -294 314c-181 0 -295 -143 -295 -314s114 -314 295 -314zM389 -12c-211 0 -349 157 -349 362s138 362 349 362c210 0 349 -157 349 -362s-139 -362 -349 -362zM410 744h-49l-116 138h76z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="579" 
d="M315 0h-52v293c-81 135 -163 272 -245 407h60l211 -358l210 358h62l-246 -407v-293zM313 745h-49l89 138h76z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="496" 
d="M192 -103l36 95l-213 525h56l182 -461l174 461h54l-242 -625c-37 -96 -79 -140 -145 -140c-17 0 -35 3 -55 8l13 48c14 -4 30 -7 43 -7c40 0 69 23 97 96zM266 583h-49l89 138h76z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="514" 
d="M273 -106h-46v109c-63 6 -136 30 -195 79l29 43c44 -40 128 -74 195 -74c104 0 166 63 166 142c0 81 -81 112 -183 146c-87 29 -181 69 -181 187c0 91 72 160 169 170v109h46v-108c73 -6 135 -37 172 -77l-35 -40c-43 41 -98 69 -156 69c-85 0 -143 -59 -143 -125
c0 -85 72 -109 171 -144c97 -36 192 -79 192 -186c0 -108 -82 -184 -201 -191v-109z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="577" 
d="M522 0h-427v700h427v-49h-374v-262h305v-49h-305v-291h374v-49zM320 745h-49l89 138h76z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" 
d="M136 360v-201c34 -57 103 -123 207 -123c130 0 200 115 200 225s-70 223 -200 223c-103 0 -171 -68 -207 -124zM134 -235h-49v935h49v-271c52 62 127 103 211 103c163 0 251 -135 251 -271c0 -137 -88 -273 -251 -273c-76 0 -152 29 -211 102v-325z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="483" 
d="M272 583h-62l-109 138h55l85 -97l85 97h55z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="598" 
d="M563 0h-528v18l430 633h-415v49h501v-16l-434 -635h446v-49zM325 745h-62l-109 138h55l85 -97l85 97h55z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="530" 
d="M485 0h-445v13l332 456h-311v48h402v-12l-335 -456h357v-49zM291 583h-62l-109 138h55l85 -97l85 97h55z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="538" 
d="M488 306h-438v46h438v-46z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="571" 
d="M303 135h-156v-135h-52v700h52v-136h151c143 0 233 -93 233 -213c0 -132 -107 -216 -228 -216zM147 185h132c150 0 198 85 198 164c0 97 -57 165 -201 165h-129v-329z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="461" 
d="M47 32l19 46c51 -26 96 -42 153 -42c76 0 147 38 147 112c0 63 -71 82 -143 96c-84 17 -175 40 -175 139c0 91 77 149 176 149c56 0 118 -14 171 -51l-21 -46c-43 31 -93 49 -151 49c-72 0 -124 -40 -124 -100c0 -61 63 -76 142 -92c83 -17 177 -41 177 -143
c0 -97 -93 -161 -198 -161c-58 0 -117 16 -173 44zM263 583h-62l-109 138h55l85 -97l85 97h55z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="532" 
d="M32 71l30 45c46 -42 133 -77 203 -77c108 0 173 65 173 147c0 85 -84 117 -191 153c-90 30 -188 71 -188 194c0 103 88 179 201 179c87 0 159 -35 202 -81l-36 -41c-45 42 -103 72 -163 72c-89 0 -149 -62 -149 -131c0 -88 75 -113 178 -150c101 -37 200 -82 200 -193
c0 -118 -93 -200 -228 -200c-71 0 -161 24 -232 83zM293 745h-62l-109 138h55l85 -97l85 97h55z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="728" 
d="M679 89l22 -43c-54 -36 -125 -58 -197 -58c-178 0 -301 112 -345 252h-124v47h113c-3 21 -5 42 -5 64c0 20 2 43 7 69h-115v47h127c44 133 165 245 334 245c87 0 156 -25 212 -71l-30 -43c-44 36 -107 64 -180 64c-141 0 -237 -87 -280 -195h299v-47h-312
c-4 -22 -7 -45 -7 -68s2 -44 6 -65h313v-47h-300c43 -112 144 -202 287 -202c73 0 133 23 175 51z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="646" 
d="M561 0h-51v91c-50 -61 -120 -103 -209 -103c-58 0 -122 21 -165 73v-296h-51v752h51v-290c0 -133 80 -191 179 -191c78 0 151 50 195 124v357h51v-517z" />
    <glyph glyph-name="mu" unicode="&#x3bc;" horiz-adv-x="646" 
d="M561 0h-51v91c-50 -61 -120 -103 -209 -103c-58 0 -122 21 -165 73v-296h-51v752h51v-290c0 -133 80 -191 179 -191c78 0 151 50 195 124v357h51v-517z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="568" 
d="M503 359h-196v-186h-46v186h-196v46h196v186h46v-186h196v-46zM503 70h-438v47h438v-47z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="558" 
d="M196 -3v49c27 -6 59 -11 86 -11c115 0 179 58 179 156c0 97 -86 157 -202 162l-19 46c101 6 158 81 158 155c0 55 -46 110 -129 110c-100 0 -139 -60 -139 -136v-528h-50v529c0 115 72 183 191 183c111 0 179 -67 179 -156c0 -67 -39 -137 -118 -173
c99 -16 181 -94 181 -194c0 -122 -88 -201 -229 -201c-32 0 -58 3 -88 9z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="442" 
d="M221 419c66 0 116 53 116 120s-50 120 -116 120s-116 -53 -116 -120s50 -120 116 -120zM387 539c0 -91 -68 -165 -166 -165s-166 74 -166 165s68 165 166 165s166 -74 166 -165z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="696" 
d="M640 45l14 -39c-16 -10 -47 -16 -65 -16c-63 0 -124 34 -124 145v334h-198v-469h-52v469c-58 0 -111 -27 -157 -72l-33 39c48 44 113 81 198 81h433v-48h-140v-340c0 -58 26 -95 75 -95c20 0 35 3 49 11z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="195" 
d="M97 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="303" 
d="M276 -159v-40c-16 -5 -36 -8 -55 -8c-50 0 -97 27 -97 79c0 43 38 107 122 150l55 -4c-71 -38 -130 -98 -130 -142c0 -32 27 -44 57 -44c16 0 34 4 48 9z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="509" 
d="M370 682h49c-2 -82 -72 -124 -164 -124s-163 42 -165 124h51c2 -63 55 -80 114 -80s113 17 115 80z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="528" 
d="M448 325h-48v65c-38 -48 -99 -75 -153 -75c-117 0 -182 98 -182 196c0 100 65 198 182 198c54 0 111 -21 153 -74v66h48v-376zM398 447v130c-24 41 -86 89 -150 89c-81 0 -134 -74 -134 -155c0 -79 53 -153 134 -153c64 0 126 48 150 89z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="526" 
d="M262 359c76 0 150 55 150 153c0 99 -74 153 -150 153c-74 0 -148 -54 -148 -153c0 -98 74 -153 148 -153zM461 512c0 -124 -102 -196 -199 -196c-96 0 -197 72 -197 196c0 125 101 197 197 197c97 0 199 -72 199 -197z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="542" 
d="M477 60h-412v47h412v-47zM477 179l-412 171v50l412 171v-54l-353 -142l353 -142v-54z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="542" 
d="M477 60h-412v47h412v-47zM477 350l-412 -171v54l353 142l-353 142v54l412 -171v-50z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="826" 
d="M60 363l11 39c33 -31 73 -44 112 -44c56 0 91 37 91 77c0 43 -37 68 -106 68h-32l-5 34l106 121h-164v42h229v-30l-116 -128c94 -4 132 -55 132 -103c0 -81 -72 -122 -143 -122c-35 0 -84 15 -115 46zM712 0h-42v98h-185l-13 41l138 259h50l-141 -260h151v139h42v-139h59
v-40h-59v-98zM246 0h-48l373 700h49z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="777" 
d="M707 0h-256v30c0 65 53 99 105 131s103 60 103 118c0 45 -35 69 -77 69c-45 0 -76 -16 -107 -36l-17 35c27 22 73 42 125 42c72 0 120 -51 120 -107c0 -76 -61 -114 -116 -147c-44 -27 -84 -49 -91 -94h211v-41zM175 0h-48l373 700h49zM187 326h-42v331l-90 -49v40l97 53
h35v-375z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="755" 
d="M641 0h-42v98h-185l-13 41l138 259h50l-141 -260h151v139h42v-139h59v-40h-59v-98zM175 0h-48l373 700h49zM187 326h-42v331l-90 -49v40l97 53h35v-375z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="378" 
d="M60 363l11 39c33 -31 73 -44 112 -44c56 0 91 37 91 77c0 43 -37 68 -106 68h-32l-5 34l106 121h-164v42h229v-30l-116 -128c94 -4 132 -55 132 -103c0 -81 -72 -122 -143 -122c-35 0 -84 15 -115 46z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="376" 
d="M316 320h-256v30c0 65 53 99 105 131s103 60 103 118c0 45 -35 69 -77 69c-45 0 -76 -16 -107 -36l-17 35c27 22 73 42 125 42c72 0 120 -51 120 -107c0 -76 -61 -114 -116 -147c-44 -27 -84 -49 -91 -94h211v-41z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="249" 
d="M149 295h-49v405h49v-405zM149 -250h-49v405h49v-405z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="689" 
d="M640 150l29 -41c-44 -48 -137 -116 -255 -121l-24 -44c47 8 112 -17 112 -76c0 -48 -39 -91 -109 -91c-33 0 -76 15 -109 46l23 40c64 -61 147 -49 147 0c0 44 -65 50 -107 38l-16 22l35 67c-206 18 -326 186 -326 361c0 178 130 361 354 361c107 0 189 -41 241 -89
l-28 -41c-59 54 -134 80 -213 80c-190 0 -300 -156 -300 -310c0 -157 115 -314 309 -314c96 0 188 50 237 112z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="496" 
d="M420 405l36 -12c-16 -66 -58 -107 -118 -107c-48 0 -82 29 -110 56c-23 22 -44 37 -73 37c-38 0 -66 -27 -79 -79l-36 12c16 66 56 107 116 107c48 0 82 -29 110 -56c23 -22 44 -37 73 -37c38 0 68 27 81 79z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="513" 
d="M423 612h-333v45h333v-45z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="912" 
d="M438 348c-22 81 -92 159 -174 159c-83 0 -157 -76 -157 -159c0 -82 74 -158 157 -158c82 0 152 78 174 158zM456 271c-42 -100 -134 -133 -189 -133c-113 0 -212 93 -212 210s99 211 212 211c55 0 147 -33 189 -134c42 100 134 133 189 133c113 0 212 -93 212 -210
s-99 -211 -212 -211c-55 0 -147 33 -189 134zM474 348c22 -81 92 -159 174 -159c83 0 157 76 157 159c0 82 -74 158 -157 158c-82 0 -152 -78 -174 -158z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="758" 
d="M663 0h-57l-459 621v-621h-52v700h56l461 -624v624h51v-700zM507 848l37 -19c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="838" 
d="M567 160h-57l-98 160h-71v-160h-46v392h132c84 0 137 -53 137 -114c0 -59 -50 -109 -100 -115zM341 361h85c53 0 92 31 92 76c0 46 -29 73 -92 73h-85v-149zM419 47c165 0 300 139 300 303s-135 303 -300 303s-300 -139 -300 -303s135 -303 300 -303zM419 0
c-193 0 -349 157 -349 350s156 350 349 350s349 -157 349 -350s-156 -350 -349 -350z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="715" 
d="M178 388h-45v271h-88v41h221v-41h-88v-271zM640 388h-44v228l-110 -178h-14l-110 178v-228h-44v312h44l117 -195l117 195h44v-312z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="745" 
d="M378 0h-236v331h-117v49h117v320h236c205 0 327 -171 327 -350s-122 -350 -327 -350zM195 49h182c163 0 273 134 273 301s-110 301 -273 301h-182v-271h138v-49h-138v-282z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="576" 
d="M411 446l-67 125l-86 -50l-21 40l85 50l-53 101h60l38 -74l83 49l22 -41l-84 -49l79 -154c40 -79 64 -134 64 -195c0 -169 -122 -260 -243 -260c-122 0 -243 91 -243 247c0 157 121 248 233 248c54 0 98 -15 133 -37zM288 34c95 0 193 78 193 201c0 124 -98 202 -193 202
c-96 0 -194 -78 -194 -202c0 -123 98 -201 194 -201z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="282" 
d="M187 326h-42v331l-90 -49v40l97 53h35v-375z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="432" 
d="M355 158l-139 139l-139 -139l-32 32l139 139l-139 139l32 32l139 -139l139 139l32 -32l-139 -139l139 -139z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="464" 
d="M116 -89l72 338h-112v43h121l54 248c23 108 101 168 191 168c29 0 60 -6 90 -19l-16 -46c-25 13 -50 19 -75 19c-65 0 -123 -42 -140 -124l-53 -246h125v-43h-134l-73 -340c-23 -108 -101 -169 -191 -169c-30 0 -60 7 -90 20l16 46c25 -13 50 -19 75 -19
c64 0 122 42 140 124z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="483" 
d="M429 -73l29 -41c-42 -36 -113 -75 -193 -75c-130 0 -210 98 -210 196c0 104 73 196 218 218l1 122h51l1 -164c-138 3 -219 -72 -219 -175c0 -76 67 -149 157 -149c61 0 122 29 165 68zM296 432c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="236" 
d="M148 -178h-60l10 558h41zM118 431c-28 0 -49 22 -49 50s21 50 49 50s49 -22 49 -50s-21 -50 -49 -50z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="603" 
d="M521 146h-193v-146h-53v146h-193v47h193v100h-193v47h165l-217 360h60l211 -358l210 358h62l-217 -360h165v-47h-193v-100h193v-47z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="548" 
d="M493 212h-264l-78 -126h-54l78 126h-120v47h150l86 139h-236v47h265l79 126h52l-78 -126h120v-47h-149l-86 -139h235v-47z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="576" 
d="M411 446l-142 266h60l138 -269c40 -79 64 -134 64 -195c0 -169 -122 -260 -243 -260c-122 0 -243 91 -243 247c0 157 121 248 233 248c54 0 98 -15 133 -37zM288 34c95 0 193 78 193 201c0 124 -98 202 -193 202c-96 0 -194 -78 -194 -202c0 -123 98 -201 194 -201z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="643" 
d="M573 0h-528v46l324 304l-324 304v46h528v-208h-46v161h-415l311 -286v-34l-311 -287h415v162h46v-208z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="676" 
d="M525 0h-51v652h-271v-652h-52v652h-106v48h586v-48h-106v-652z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="647" 
d="M231 -89l135 629c23 108 101 168 191 168c29 0 60 -6 90 -19l-16 -46c-25 13 -50 19 -75 19c-65 0 -123 -42 -140 -124l-135 -629c-23 -108 -101 -169 -191 -169c-30 0 -60 7 -90 20l16 46c25 -13 50 -19 75 -19c64 0 122 42 140 124z" />
    <glyph glyph-name="Omega" unicode="&#x3a9;" horiz-adv-x="792" 
d="M737 0h-291v49c143 56 237 195 237 339c0 158 -102 277 -287 277s-287 -119 -287 -277c0 -144 94 -283 237 -339v-49h-291v47h206c-116 69 -202 200 -202 341c0 177 124 324 337 324s337 -147 337 -324c0 -141 -87 -272 -203 -341h207v-47z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="792" 
d="M737 0h-291v49c143 56 237 195 237 339c0 158 -102 277 -287 277s-287 -119 -287 -277c0 -144 94 -283 237 -339v-49h-291v47h206c-116 69 -202 200 -202 341c0 177 124 324 337 324s337 -147 337 -324c0 -141 -87 -272 -203 -341h207v-47z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="638" 
d="M573 210h-49v180h-469v49h518v-229z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="647" 
d="M350 0h-28l-181 468h-116v49h152l161 -418l235 601h54z" />
    <glyph glyph-name="Delta" unicode="&#x394;" horiz-adv-x="617" 
d="M572 0h-527v37l245 663h37l245 -663v-37zM521 46l-214 571l-211 -571h425z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="617" 
d="M572 0h-527v37l245 663h37l245 -663v-37zM521 46l-214 571l-211 -571h425z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="566" 
d="M305 0h-43l-212 342v16l210 342h43l213 -342v-16zM283 52l184 298l-185 298l-183 -298z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="921" 
d="M866 0h-411v208h-252l-118 -208h-65l408 700h438v-49h-359v-262h291v-50h-291v-290h359v-49zM455 256v381l-6 1l-217 -382h223z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1022" 
d="M933 108l34 -32c-38 -45 -107 -88 -204 -88c-91 0 -163 40 -207 103v-91h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-84c43 60 111 99 196 99c153 0 239 -112 234 -281h-430
c6 -130 103 -213 209 -213c77 0 130 31 168 70zM505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM559 300h373c-3 99 -68 184 -180 184c-98 0 -175 -73 -193 -184z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="620" 
d="M310 37c105 0 212 87 212 223s-107 223 -212 223s-213 -87 -213 -223s108 -223 213 -223zM310 -12c-134 0 -265 100 -265 272s131 272 265 272c133 0 265 -100 265 -272s-132 -272 -265 -272zM221 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45
c0 -25 -20 -46 -44 -46zM401 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="559" 
d="M328 -136h-48v127c-137 18 -235 132 -235 267c0 138 96 254 235 270v117h48v-115c63 -7 126 -31 175 -70l-28 -41c-44 36 -104 62 -168 62c-105 0 -209 -91 -209 -223c0 -136 111 -221 217 -221c89 0 145 45 174 80l35 -34c-35 -45 -103 -89 -196 -94v-125z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="620" 
d="M122 0h-61l56 66c-44 46 -72 111 -72 191c0 172 131 272 265 272c55 0 109 -17 155 -49l33 39h64l-60 -71c44 -46 73 -111 73 -191c0 -172 -132 -272 -265 -272c-56 0 -112 17 -158 50zM467 407l-281 -332c37 -27 80 -41 124 -41c105 0 212 87 212 223
c0 61 -22 112 -55 150zM151 107l280 332c-36 26 -79 41 -121 41c-105 0 -213 -87 -213 -223c0 -61 21 -112 54 -150z" />
    <glyph glyph-name="a" unicode="a" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-517z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-517zM382 583h-49l-116 138h76z
" />
    <glyph glyph-name="aacute" unicode="&#xe1;" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-517zM327 583h-49l89 138h76z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-517zM441 583h-55l-85 97l-85 -97
h-55l109 138h62z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-517zM453 697l37 -19
c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-517zM208 599
c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM388 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="aring" unicode="&#xe5;" 
d="M505 159v202c-34 57 -103 123 -207 123c-130 0 -200 -115 -200 -225s70 -223 200 -223c104 0 173 66 207 123zM556 0h-49v90c-53 -62 -134 -102 -211 -102c-163 0 -251 135 -251 271c0 137 88 273 251 273c76 0 152 -29 211 -102v87h49v-517zM301 608c32 0 57 27 57 60
s-25 60 -57 60s-57 -27 -57 -60s25 -60 57 -60zM398 668c0 -53 -42 -96 -97 -96s-97 43 -97 96s42 96 97 96s97 -43 97 -96z" />
    <glyph glyph-name="a.alt" horiz-adv-x="549" 
d="M474 0h-51v84c-59 -64 -135 -93 -201 -93c-98 0 -174 53 -174 155c0 122 101 154 202 154h173v33c0 98 -74 151 -163 151c-65 0 -117 -27 -163 -72l-33 39c48 44 113 81 198 81c101 0 212 -57 212 -202v-330zM423 147v108h-159c-58 0 -163 -9 -163 -107
c0 -75 61 -110 125 -110c83 0 158 54 197 109z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1045" 
d="M951 108l34 -32c-38 -45 -107 -88 -204 -88c-112 0 -194 60 -233 147c-46 -95 -142 -147 -238 -147c-134 0 -265 100 -265 272s131 272 265 272c96 0 192 -53 237 -146c38 86 116 146 223 146c153 0 239 -112 234 -281h-430c6 -130 103 -213 209 -213c77 0 130 31 168 70
zM310 37c102 0 212 83 212 222c0 141 -110 224 -212 224c-105 0 -213 -87 -213 -223s108 -223 213 -223zM577 300h373c-3 99 -68 184 -180 184c-98 0 -175 -73 -193 -184z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="482" 
d="M457 0h-353v347l-79 -46v52l79 47v300h52v-269l144 85v-54l-144 -84v-329h301v-49z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="301" 
d="M269 45l14 -39c-20 -11 -44 -16 -65 -16c-63 0 -124 34 -124 145v206l-69 -40v52l69 41v306h51v-276l115 68v-54l-115 -67v-242c0 -58 26 -95 72 -95c24 0 40 5 52 11z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="838" 
d="M564 248l17 -36c-27 -35 -90 -72 -148 -72c-139 0 -220 104 -220 211c0 108 79 209 203 209c64 0 115 -26 147 -48l-18 -39c-32 22 -76 42 -128 42c-103 0 -156 -79 -156 -164c0 -93 65 -167 165 -167c68 0 110 31 138 64zM419 47c165 0 300 139 300 303
s-135 303 -300 303s-300 -139 -300 -303s135 -303 300 -303zM419 0c-193 0 -349 157 -349 350s156 350 349 350s349 -157 349 -350s-156 -350 -349 -350z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="778" 
d="M153 0h-62l58 78c-69 65 -109 162 -109 272c0 205 138 362 349 362c74 0 139 -20 193 -54l31 42h64l-55 -73c73 -66 116 -164 116 -277c0 -205 -139 -362 -349 -362c-78 0 -146 21 -201 59zM590 584l-371 -496c46 -33 103 -52 170 -52c179 0 294 143 294 314
c0 92 -34 176 -93 234zM181 120l370 496c-45 30 -100 48 -162 48c-181 0 -295 -143 -295 -314c0 -90 31 -172 87 -230z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="220" 
d="M135 0h-50v517h50v-517zM139 583h-49l89 138h76z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="220" 
d="M135 0h-50v517h50v-517zM132 583h-49l-116 138h76z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="220" 
d="M135 0h-50v517h50v-517zM250 583h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="220" 
d="M135 0h-50v517h50v-517zM21 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM201 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="aacute.alt" horiz-adv-x="549" 
d="M474 0h-51v84c-59 -64 -135 -93 -201 -93c-98 0 -174 53 -174 155c0 122 101 154 202 154h173v33c0 98 -74 151 -163 151c-65 0 -117 -27 -163 -72l-33 39c48 44 113 81 198 81c101 0 212 -57 212 -202v-330zM423 147v108h-159c-58 0 -163 -9 -163 -107
c0 -75 61 -110 125 -110c83 0 158 54 197 109zM278 583h-49l89 138h76z" />
    <glyph glyph-name="acircumflex.alt" horiz-adv-x="549" 
d="M474 0h-51v84c-59 -64 -135 -93 -201 -93c-98 0 -174 53 -174 155c0 122 101 154 202 154h173v33c0 98 -74 151 -163 151c-65 0 -117 -27 -163 -72l-33 39c48 44 113 81 198 81c101 0 212 -57 212 -202v-330zM423 147v108h-159c-58 0 -163 -9 -163 -107
c0 -75 61 -110 125 -110c83 0 158 54 197 109zM401 583h-55l-85 97l-85 -97h-55l109 138h62z" />
    <glyph glyph-name="adieresis.alt" horiz-adv-x="549" 
d="M474 0h-51v84c-59 -64 -135 -93 -201 -93c-98 0 -174 53 -174 155c0 122 101 154 202 154h173v33c0 98 -74 151 -163 151c-65 0 -117 -27 -163 -72l-33 39c48 44 113 81 198 81c101 0 212 -57 212 -202v-330zM423 147v108h-159c-58 0 -163 -9 -163 -107
c0 -75 61 -110 125 -110c83 0 158 54 197 109zM173 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46zM353 599c-25 0 -45 21 -45 46c0 24 20 45 45 45c24 0 44 -21 44 -45c0 -25 -20 -46 -44 -46z" />
    <glyph glyph-name="agrave.alt" horiz-adv-x="549" 
d="M474 0h-51v84c-59 -64 -135 -93 -201 -93c-98 0 -174 53 -174 155c0 122 101 154 202 154h173v33c0 98 -74 151 -163 151c-65 0 -117 -27 -163 -72l-33 39c48 44 113 81 198 81c101 0 212 -57 212 -202v-330zM423 147v108h-159c-58 0 -163 -9 -163 -107
c0 -75 61 -110 125 -110c83 0 158 54 197 109zM303 583h-49l-116 138h76z" />
    <glyph glyph-name="aring.alt" horiz-adv-x="549" 
d="M474 0h-51v84c-59 -64 -135 -93 -201 -93c-98 0 -174 53 -174 155c0 122 101 154 202 154h173v33c0 98 -74 151 -163 151c-65 0 -117 -27 -163 -72l-33 39c48 44 113 81 198 81c101 0 212 -57 212 -202v-330zM423 147v108h-159c-58 0 -163 -9 -163 -107
c0 -75 61 -110 125 -110c83 0 158 54 197 109zM262 608c32 0 57 27 57 60s-25 60 -57 60s-57 -27 -57 -60s25 -60 57 -60zM359 668c0 -53 -42 -96 -97 -96s-97 43 -97 96s42 96 97 96s97 -43 97 -96z" />
    <glyph glyph-name="atilde.alt" horiz-adv-x="549" 
d="M474 0h-51v84c-59 -64 -135 -93 -201 -93c-98 0 -174 53 -174 155c0 122 101 154 202 154h173v33c0 98 -74 151 -163 151c-65 0 -117 -27 -163 -72l-33 39c48 44 113 81 198 81c101 0 212 -57 212 -202v-330zM423 147v108h-159c-58 0 -163 -9 -163 -107
c0 -75 61 -110 125 -110c83 0 158 54 197 109zM390 697l37 -19c-21 -57 -56 -81 -97 -81c-59 0 -93 60 -135 60c-29 0 -46 -18 -60 -55l-37 19c21 57 56 81 97 81c59 0 93 -60 135 -60c29 0 46 18 60 55z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="270" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="270" 
 />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="889" 
d="M650 -23l-168 168c-10 10 -25 26 -39 26c-13 0 -28 -16 -38 -26l-168 -168c-63 39 -115 94 -149 163l146 146c31 31 29 29 72 29h275c43 0 41 1 71 -29l146 -146c-32 -69 -85 -124 -148 -163zM840 315h-151c-35 0 -48 3 -58 14c-11 12 -9 23 -20 51c-10 26 -26 48 -45 66
c-34 32 -77 48 -122 48s-90 -16 -124 -48c-19 -18 -34 -40 -43 -66c-11 -28 -10 -38 -22 -51c-10 -10 -21 -14 -56 -14h-152c0 114 49 220 128 292c76 68 171 105 269 105c97 0 193 -37 267 -105c80 -72 129 -178 129 -292z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="358" 
d="M179 356c75 0 95 70 95 157s-20 157 -95 157c-74 0 -94 -70 -94 -157s20 -157 94 -157zM179 316c-109 0 -137 92 -137 197s28 197 137 197c110 0 137 -92 137 -197s-27 -197 -137 -197z" />
    <glyph glyph-name="one.numr" horiz-adv-x="222" 
d="M157 326h-42v330l-90 -49v40l97 53h35v-374z" />
    <glyph glyph-name="two.numr" horiz-adv-x="346" 
d="M301 326h-256v30c0 65 53 97 105 129s103 57 103 115c0 45 -35 69 -77 69c-45 0 -76 -16 -107 -36l-17 35c27 22 73 42 125 42c72 0 120 -51 120 -107c0 -76 -61 -111 -116 -144c-44 -27 -84 -47 -91 -92h211v-41z" />
    <glyph glyph-name="three.numr" horiz-adv-x="348" 
d="M45 361l11 39c33 -31 73 -44 112 -44c56 0 91 38 91 78c0 43 -37 69 -106 69h-32l-5 34l106 121h-164v42h229v-30l-116 -128c94 -4 132 -56 132 -104c0 -81 -72 -123 -143 -123c-35 0 -84 15 -115 46z" />
    <glyph glyph-name="four.numr" horiz-adv-x="361" 
d="M272 326h-42v98h-185l-13 41l138 259h50l-141 -260h151v139h42v-139h59v-40h-59v-98z" />
    <glyph glyph-name="five.numr" horiz-adv-x="356" 
d="M39 364l16 34c37 -28 79 -43 118 -43c48 0 95 29 95 85c0 63 -58 79 -118 79c-20 0 -44 -1 -68 -6l-18 18l18 169h211v-40h-174l-12 -104c11 1 34 2 48 2c79 0 156 -26 156 -118c0 -84 -69 -124 -140 -124c-46 0 -96 16 -132 48z" />
    <glyph glyph-name="six.numr" horiz-adv-x="369" 
d="M290 705l-7 -38c-16 2 -33 4 -52 4c-81 0 -125 -58 -140 -129c24 18 65 36 108 36c79 0 128 -50 128 -122c0 -82 -63 -140 -139 -140c-108 0 -143 93 -143 170c0 110 55 224 185 224c19 0 43 -2 60 -5zM86 493c1 -109 55 -138 102 -138c58 0 98 48 98 101s-37 84 -91 84
c-56 0 -100 -37 -109 -47z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="323" 
d="M128 326h-45l157 335h-205v39h253v-25z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="360" 
d="M244 526c50 -19 74 -57 74 -96c0 -71 -53 -114 -138 -114c-84 0 -138 43 -138 114c0 37 29 77 75 97c-46 23 -60 54 -60 83c0 65 55 100 123 100s123 -35 123 -100c0 -28 -14 -61 -59 -84zM180 508c-50 -6 -95 -38 -95 -80c0 -43 34 -73 95 -73s96 30 96 73
s-47 74 -96 80zM180 540c41 11 80 33 80 72c0 38 -38 59 -80 59c-41 0 -80 -21 -80 -59c0 -39 40 -61 80 -72z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="369" 
d="M79 321l7 38c16 -2 33 -4 52 -4c81 0 125 58 140 129c-24 -18 -65 -36 -108 -36c-79 0 -128 50 -128 122c0 82 63 140 139 140c108 0 143 -93 143 -170c0 -110 -55 -224 -185 -224c-19 0 -43 2 -60 5zM283 533c-1 109 -55 138 -102 138c-58 0 -98 -48 -98 -101
s37 -84 91 -84c56 0 100 37 109 47z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="358" 
d="M179 30c75 0 95 70 95 157s-20 157 -95 157c-74 0 -94 -70 -94 -157s20 -157 94 -157zM179 -10c-109 0 -137 92 -137 197s28 197 137 197c110 0 137 -92 137 -197s-27 -197 -137 -197z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="222" 
d="M157 0h-42v330l-90 -49v40l97 53h35v-374z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="346" 
d="M301 0h-256v30c0 65 53 97 105 129s103 57 103 115c0 45 -35 69 -77 69c-45 0 -76 -16 -107 -36l-17 35c27 22 73 42 125 42c72 0 120 -51 120 -107c0 -76 -61 -111 -116 -144c-44 -27 -84 -47 -91 -92h211v-41z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="348" 
d="M45 36l11 39c33 -31 73 -44 112 -44c56 0 91 38 91 78c0 43 -37 69 -106 69h-32l-5 34l106 121h-164v42h229v-30l-116 -128c94 -4 132 -56 132 -104c0 -81 -72 -123 -143 -123c-35 0 -84 15 -115 46z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="361" 
d="M272 0h-42v98h-185l-13 41l138 259h50l-141 -260h151v139h42v-139h59v-40h-59v-98z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="356" 
d="M39 38l16 34c37 -28 79 -43 118 -43c48 0 95 29 95 85c0 63 -58 79 -118 79c-20 0 -44 -1 -68 -6l-18 18l18 169h211v-40h-174l-12 -104c11 1 34 2 48 2c79 0 156 -26 156 -118c0 -84 -69 -124 -140 -124c-46 0 -96 16 -132 48z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="369" 
d="M290 379l-7 -38c-16 2 -33 4 -52 4c-81 0 -125 -58 -140 -129c24 18 65 36 108 36c79 0 128 -50 128 -122c0 -82 -63 -140 -139 -140c-108 0 -143 93 -143 170c0 110 55 224 185 224c19 0 43 -2 60 -5zM86 167c1 -109 55 -138 102 -138c58 0 98 48 98 101s-37 84 -91 84
c-56 0 -100 -37 -109 -47z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="323" 
d="M128 0h-45l157 335h-205v39h253v-25z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="360" 
d="M244 200c50 -19 74 -57 74 -96c0 -71 -53 -114 -138 -114c-84 0 -138 43 -138 114c0 37 29 77 75 97c-46 23 -60 54 -60 83c0 65 55 100 123 100s123 -35 123 -100c0 -28 -14 -61 -59 -84zM180 182c-50 -6 -95 -38 -95 -80c0 -43 34 -73 95 -73s96 30 96 73
s-47 74 -96 80zM180 214c41 11 80 33 80 72c0 38 -38 59 -80 59c-41 0 -80 -21 -80 -59c0 -39 40 -61 80 -72z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="369" 
d="M79 -5l7 38c16 -2 33 -4 52 -4c81 0 125 58 140 129c-24 -18 -65 -36 -108 -36c-79 0 -128 50 -128 122c0 82 63 140 139 140c108 0 143 -93 143 -170c0 -110 -55 -224 -185 -224c-19 0 -43 2 -60 5zM283 207c-1 109 -55 138 -102 138c-58 0 -98 -48 -98 -101
s37 -84 91 -84c56 0 100 37 109 47z" />
    <glyph glyph-name="zero.tnum" horiz-adv-x="640" 
d="M320 37c147 0 185 141 185 313s-38 313 -185 313c-146 0 -184 -141 -184 -313s38 -313 184 -313zM320 -12c-188 0 -237 170 -237 362s49 362 237 362c189 0 237 -170 237 -362s-48 -362 -237 -362z" />
    <glyph glyph-name="one.tnum" horiz-adv-x="640" 
d="M546 0h-418v47h191v602l-191 -82v51l204 86h37v-657h177v-47z" />
    <glyph glyph-name="two.tnum" horiz-adv-x="640" 
d="M535 0h-440v48c0 133 103 194 203 253c94 55 184 116 184 211c0 83 -70 151 -160 151c-66 0 -126 -31 -177 -72l-31 39c55 45 121 82 205 82c119 0 215 -90 215 -197c0 -125 -109 -194 -208 -254c-93 -56 -182 -111 -178 -212h387v-49z" />
    <glyph glyph-name="three.tnum" horiz-adv-x="640" 
d="M86 78l21 41c60 -55 134 -82 200 -82c94 0 183 61 183 177c0 68 -54 147 -196 147h-62l-16 42l227 248h-312v49h382v-42l-236 -254h22c162 0 243 -95 243 -188c0 -149 -117 -226 -238 -226c-76 0 -164 30 -218 88z" />
    <glyph glyph-name="four.tnum" horiz-adv-x="640" 
d="M479 0h-51v188h-347l-10 43l265 487h59l-263 -482h296v282h51v-282h107v-48h-107v-188z" />
    <glyph glyph-name="five.tnum" horiz-adv-x="640" 
d="M83 76l21 41c60 -55 138 -82 204 -82c94 0 186 61 186 177c0 129 -112 165 -228 165c-40 0 -84 -3 -120 -10l-23 24l30 309h361v-48h-316l-24 -236c34 5 72 8 101 8c137 0 271 -48 271 -213c0 -149 -120 -223 -241 -223c-76 0 -168 30 -222 88z" />
    <glyph glyph-name="six.tnum" horiz-adv-x="640" 
d="M492 701l-9 -48c-18 3 -46 9 -83 9c-165 0 -253 -138 -273 -287c35 31 119 84 214 84c137 0 221 -91 221 -219c0 -148 -110 -253 -241 -253c-186 0 -247 169 -247 310c0 203 97 412 324 412c35 0 71 -4 94 -8zM124 316c0 -223 106 -282 197 -282c113 0 192 97 192 206
c0 108 -73 172 -178 172c-113 0 -201 -83 -211 -96z" />
    <glyph glyph-name="seven.tnum" horiz-adv-x="640" 
d="M219 0h-55l292 652h-378v48h435v-33z" />
    <glyph glyph-name="eight.tnum" horiz-adv-x="640" 
d="M391 372c108 -26 162 -104 162 -181c0 -127 -92 -203 -239 -203c-146 0 -239 76 -239 203c0 76 55 155 162 181c-100 42 -133 109 -133 164c0 113 93 176 210 176s211 -63 211 -176c0 -55 -33 -122 -134 -164zM314 352c-96 -9 -186 -76 -186 -165s68 -152 186 -152
s187 63 187 152c0 90 -91 156 -187 165zM314 388c81 22 159 69 159 151c0 83 -76 126 -159 126c-82 0 -158 -43 -158 -126c0 -82 78 -129 158 -151z" />
    <glyph glyph-name="nine.tnum" horiz-adv-x="640" 
d="M150 -4l9 48c18 -3 46 -9 83 -9c165 0 253 138 273 287c-35 -31 -119 -84 -214 -84c-137 0 -221 91 -221 219c0 148 110 253 241 253c186 0 247 -169 247 -310c0 -203 -97 -412 -324 -412c-35 0 -71 4 -94 8zM518 381c0 223 -106 282 -197 282c-113 0 -192 -97 -192 -206
c0 -108 73 -172 178 -172c113 0 201 83 211 96z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <hkern u1="&#x22;" u2="&#xef;" k="-20" />
    <hkern u1="&#x22;" u2="&#xee;" k="-20" />
    <hkern u1="&#x22;" u2="&#xec;" k="-20" />
    <hkern u1="&#x22;" u2="J" k="60" />
    <hkern u1="&#x23;" u2="&#x39;" k="5" />
    <hkern u1="&#x23;" u2="&#x38;" k="30" />
    <hkern u1="&#x23;" u2="&#x37;" k="30" />
    <hkern u1="&#x23;" u2="&#x36;" k="10" />
    <hkern u1="&#x23;" u2="&#x35;" k="10" />
    <hkern u1="&#x23;" u2="&#x33;" k="40" />
    <hkern u1="&#x23;" u2="&#x32;" k="45" />
    <hkern u1="&#x23;" u2="&#x31;" k="10" />
    <hkern u1="&#x25;" u2="&#x39;" k="18" />
    <hkern u1="&#x25;" u2="&#x38;" k="-5" />
    <hkern u1="&#x25;" u2="&#x37;" k="30" />
    <hkern u1="&#x25;" u2="&#x34;" k="-10" />
    <hkern u1="&#x26;" u2="&#xc6;" k="-10" />
    <hkern u1="&#x26;" u2="&#x17e;" k="-10" />
    <hkern u1="&#x26;" u2="&#x17d;" k="-10" />
    <hkern u1="&#x26;" u2="&#xfd;" k="10" />
    <hkern u1="&#x26;" u2="&#xdd;" k="90" />
    <hkern u1="&#x26;" u2="&#xc1;" k="-10" />
    <hkern u1="&#x26;" u2="&#xc2;" k="-10" />
    <hkern u1="&#x26;" u2="&#x178;" k="90" />
    <hkern u1="&#x26;" u2="&#xff;" k="10" />
    <hkern u1="&#x26;" u2="&#xc3;" k="-10" />
    <hkern u1="&#x26;" u2="&#xc0;" k="-10" />
    <hkern u1="&#x26;" u2="&#xc5;" k="-10" />
    <hkern u1="&#x26;" u2="&#xc4;" k="-10" />
    <hkern u1="&#x26;" u2="z" k="-10" />
    <hkern u1="&#x26;" u2="y" k="10" />
    <hkern u1="&#x26;" u2="v" k="10" />
    <hkern u1="&#x26;" u2="Z" k="-10" />
    <hkern u1="&#x26;" u2="Y" k="90" />
    <hkern u1="&#x26;" u2="A" k="-10" />
    <hkern u1="&#x26;" u2="x" k="-10" />
    <hkern u1="&#x26;" u2="t" k="10" />
    <hkern u1="&#x26;" u2="_" k="-40" />
    <hkern u1="&#x26;" u2="\" k="80" />
    <hkern u1="&#x26;" u2="V" k="30" />
    <hkern u1="&#x26;" u2="T" k="60" />
    <hkern u1="&#x26;" u2="J" k="-20" />
    <hkern u1="&#x26;" u2="&#x39;" k="10" />
    <hkern u1="&#x26;" u2="&#x37;" k="30" />
    <hkern u1="&#x26;" u2="&#x32;" k="-10" />
    <hkern u1="&#x26;" u2="&#x31;" k="-10" />
    <hkern u1="&#x27;" u2="&#xef;" k="-20" />
    <hkern u1="&#x27;" u2="&#xee;" k="-20" />
    <hkern u1="&#x27;" u2="&#xec;" k="-20" />
    <hkern u1="&#x27;" u2="J" k="60" />
    <hkern u1="&#x28;" u2="w" k="20" />
    <hkern u1="&#x28;" u2="j" k="-50" />
    <hkern u1="&#x28;" u2="&#x34;" k="10" />
    <hkern u1="&#x28;" u2="&#x32;" k="10" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="50" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="50" />
    <hkern u1="&#x2a;" u2="&#x2026;" k="50" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="50" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="50" />
    <hkern u1="&#x2a;" u2="&#x35;" k="10" />
    <hkern u1="&#x2a;" u2="&#x34;" k="20" />
    <hkern u1="&#x2b;" u2="&#x39;" k="10" />
    <hkern u1="&#x2b;" u2="&#x38;" k="5" />
    <hkern u1="&#x2b;" u2="&#x37;" k="50" />
    <hkern u1="&#x2b;" u2="&#x35;" k="20" />
    <hkern u1="&#x2b;" u2="&#x34;" k="-8" />
    <hkern u1="&#x2b;" u2="&#x33;" k="40" />
    <hkern u1="&#x2b;" u2="&#x32;" k="30" />
    <hkern u1="&#x2b;" u2="&#x31;" k="20" />
    <hkern u1="&#x2c;" u2="w" k="30" />
    <hkern u1="&#x2c;" u2="t" k="25" />
    <hkern u1="&#x2c;" u2="\" k="100" />
    <hkern u1="&#x2c;" u2="W" k="30" />
    <hkern u1="&#x2c;" u2="V" k="40" />
    <hkern u1="&#x2c;" u2="T" k="50" />
    <hkern u1="&#x2c;" u2="J" k="-20" />
    <hkern u1="&#x2c;" u2="&#x39;" k="25" />
    <hkern u1="&#x2c;" u2="&#x38;" k="5" />
    <hkern u1="&#x2c;" u2="&#x36;" k="20" />
    <hkern u1="&#x2c;" u2="&#x34;" k="20" />
    <hkern u1="&#x2c;" u2="&#x30;" k="20" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="50" />
    <hkern u1="&#x2d;" u2="x" k="20" />
    <hkern u1="&#x2d;" u2="j" k="10" />
    <hkern u1="&#x2d;" u2="\" k="100" />
    <hkern u1="&#x2d;" u2="X" k="25" />
    <hkern u1="&#x2d;" u2="W" k="10" />
    <hkern u1="&#x2d;" u2="V" k="15" />
    <hkern u1="&#x2d;" u2="T" k="50" />
    <hkern u1="&#x2d;" u2="J" k="40" />
    <hkern u1="&#x2e;" u2="w" k="30" />
    <hkern u1="&#x2e;" u2="t" k="25" />
    <hkern u1="&#x2e;" u2="\" k="100" />
    <hkern u1="&#x2e;" u2="W" k="30" />
    <hkern u1="&#x2e;" u2="V" k="40" />
    <hkern u1="&#x2e;" u2="T" k="50" />
    <hkern u1="&#x2e;" u2="J" k="-20" />
    <hkern u1="&#x2e;" u2="&#x39;" k="25" />
    <hkern u1="&#x2e;" u2="&#x38;" k="5" />
    <hkern u1="&#x2e;" u2="&#x36;" k="20" />
    <hkern u1="&#x2e;" u2="&#x34;" k="20" />
    <hkern u1="&#x2e;" u2="&#x30;" k="20" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="50" />
    <hkern u1="&#x2f;" g2="atilde.alt" k="40" />
    <hkern u1="&#x2f;" g2="aring.alt" k="40" />
    <hkern u1="&#x2f;" g2="agrave.alt" k="40" />
    <hkern u1="&#x2f;" g2="adieresis.alt" k="40" />
    <hkern u1="&#x2f;" g2="acircumflex.alt" k="40" />
    <hkern u1="&#x2f;" g2="aacute.alt" k="40" />
    <hkern u1="&#x2f;" u2="&#x153;" k="70" />
    <hkern u1="&#x2f;" g2="a.alt" k="40" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="70" />
    <hkern u1="&#x2f;" u2="a" k="70" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="30" />
    <hkern u1="&#x2f;" u2="&#x160;" k="30" />
    <hkern u1="&#x2f;" u2="&#x161;" k="60" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="50" />
    <hkern u1="&#x2f;" u2="&#x17d;" k="10" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="25" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="30" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="30" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="30" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="70" />
    <hkern u1="&#x2f;" u2="&#xff;" k="25" />
    <hkern u1="&#x2f;" u2="&#x152;" k="30" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="30" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="70" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="70" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="30" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="50" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="50" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="50" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="50" />
    <hkern u1="&#x2f;" u2="&#xea;" k="70" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="70" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="50" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="70" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="70" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="70" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="70" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="70" />
    <hkern u1="&#x2f;" u2="&#x131;" k="50" />
    <hkern u1="&#x2f;" u2="z" k="50" />
    <hkern u1="&#x2f;" u2="y" k="25" />
    <hkern u1="&#x2f;" u2="v" k="25" />
    <hkern u1="&#x2f;" u2="u" k="50" />
    <hkern u1="&#x2f;" u2="s" k="60" />
    <hkern u1="&#x2f;" u2="r" k="50" />
    <hkern u1="&#x2f;" u2="q" k="70" />
    <hkern u1="&#x2f;" u2="p" k="50" />
    <hkern u1="&#x2f;" u2="o" k="70" />
    <hkern u1="&#x2f;" u2="n" k="50" />
    <hkern u1="&#x2f;" u2="m" k="50" />
    <hkern u1="&#x2f;" u2="g" k="70" />
    <hkern u1="&#x2f;" u2="e" k="70" />
    <hkern u1="&#x2f;" u2="d" k="70" />
    <hkern u1="&#x2f;" u2="c" k="70" />
    <hkern u1="&#x2f;" u2="Z" k="10" />
    <hkern u1="&#x2f;" u2="S" k="30" />
    <hkern u1="&#x2f;" u2="Q" k="30" />
    <hkern u1="&#x2f;" u2="O" k="30" />
    <hkern u1="&#x2f;" u2="G" k="30" />
    <hkern u1="&#x2f;" u2="C" k="30" />
    <hkern u1="&#x2f;" u2="A" k="70" />
    <hkern u1="&#x2f;" u2="x" k="40" />
    <hkern u1="&#x2f;" u2="w" k="30" />
    <hkern u1="&#x2f;" u2="t" k="10" />
    <hkern u1="&#x2f;" u2="_" k="170" />
    <hkern u1="&#x2f;" u2="X" k="20" />
    <hkern u1="&#x2f;" u2="V" k="-20" />
    <hkern u1="&#x2f;" u2="J" k="80" />
    <hkern u1="&#x2f;" u2="&#x39;" k="20" />
    <hkern u1="&#x2f;" u2="&#x38;" k="45" />
    <hkern u1="&#x2f;" u2="&#x37;" k="-10" />
    <hkern u1="&#x2f;" u2="&#x36;" k="50" />
    <hkern u1="&#x2f;" u2="&#x35;" k="30" />
    <hkern u1="&#x2f;" u2="&#x34;" k="65" />
    <hkern u1="&#x2f;" u2="&#x33;" k="30" />
    <hkern u1="&#x2f;" u2="&#x32;" k="30" />
    <hkern u1="&#x2f;" u2="&#x31;" k="10" />
    <hkern u1="&#x2f;" u2="&#x30;" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="110" />
    <hkern u1="&#x2f;" u2="&#x26;" k="70" />
    <hkern u1="&#x30;" u2="&#x201e;" k="20" />
    <hkern u1="&#x30;" u2="&#x201a;" k="20" />
    <hkern u1="&#x30;" u2="&#x2026;" k="20" />
    <hkern u1="&#x30;" u2="&#x2e;" k="20" />
    <hkern u1="&#x30;" u2="&#x2c;" k="20" />
    <hkern u1="&#x30;" u2="\" k="20" />
    <hkern u1="&#x30;" u2="&#x3f;" k="10" />
    <hkern u1="&#x30;" u2="&#x37;" k="45" />
    <hkern u1="&#x30;" u2="&#x36;" k="-10" />
    <hkern u1="&#x30;" u2="&#x33;" k="30" />
    <hkern u1="&#x30;" u2="&#x32;" k="30" />
    <hkern u1="&#x30;" u2="&#x31;" k="20" />
    <hkern u1="&#x30;" u2="&#x30;" k="-10" />
    <hkern u1="&#x30;" u2="&#x2f;" k="20" />
    <hkern u1="&#x31;" u2="&#x2030;" k="50" />
    <hkern u1="&#x31;" u2="&#x25;" k="50" />
    <hkern u1="&#x31;" u2="\" k="80" />
    <hkern u1="&#x31;" u2="&#x40;" k="10" />
    <hkern u1="&#x31;" u2="&#x3f;" k="30" />
    <hkern u1="&#x31;" u2="&#x3c;" k="15" />
    <hkern u1="&#x31;" u2="&#x39;" k="32" />
    <hkern u1="&#x31;" u2="&#x38;" k="30" />
    <hkern u1="&#x31;" u2="&#x37;" k="45" />
    <hkern u1="&#x31;" u2="&#x36;" k="30" />
    <hkern u1="&#x31;" u2="&#x34;" k="30" />
    <hkern u1="&#x31;" u2="&#x33;" k="8" />
    <hkern u1="&#x31;" u2="&#x32;" k="8" />
    <hkern u1="&#x31;" u2="&#x30;" k="40" />
    <hkern u1="&#x31;" u2="&#x2f;" k="-5" />
    <hkern u1="&#x31;" u2="&#x2b;" k="10" />
    <hkern u1="&#x31;" u2="&#x2a;" k="40" />
    <hkern u1="&#x32;" u2="&#x24;" k="-10" />
    <hkern u1="&#x32;" u2="\" k="20" />
    <hkern u1="&#x32;" u2="&#x3e;" k="-10" />
    <hkern u1="&#x32;" u2="&#x3d;" k="-15" />
    <hkern u1="&#x32;" u2="&#x39;" k="-7" />
    <hkern u1="&#x32;" u2="&#x38;" k="10" />
    <hkern u1="&#x32;" u2="&#x37;" k="30" />
    <hkern u1="&#x32;" u2="&#x34;" k="20" />
    <hkern u1="&#x32;" u2="&#x33;" k="10" />
    <hkern u1="&#x32;" u2="&#x31;" k="-5" />
    <hkern u1="&#x32;" u2="&#x2b;" k="5" />
    <hkern u1="&#x32;" u2="&#x26;" k="5" />
    <hkern u1="&#x32;" u2="&#x23;" k="10" />
    <hkern u1="&#x33;" u2="&#x2030;" k="25" />
    <hkern u1="&#x33;" u2="&#x201e;" k="20" />
    <hkern u1="&#x33;" u2="&#x201a;" k="20" />
    <hkern u1="&#x33;" u2="&#x2026;" k="20" />
    <hkern u1="&#x33;" u2="&#x2e;" k="20" />
    <hkern u1="&#x33;" u2="&#x2c;" k="20" />
    <hkern u1="&#x33;" u2="&#x25;" k="25" />
    <hkern u1="&#x33;" u2="\" k="30" />
    <hkern u1="&#x33;" u2="&#x3f;" k="20" />
    <hkern u1="&#x33;" u2="&#x39;" k="10" />
    <hkern u1="&#x33;" u2="&#x37;" k="28" />
    <hkern u1="&#x33;" u2="&#x35;" k="20" />
    <hkern u1="&#x33;" u2="&#x33;" k="8" />
    <hkern u1="&#x33;" u2="&#x32;" k="10" />
    <hkern u1="&#x33;" u2="&#x31;" k="12" />
    <hkern u1="&#x33;" u2="&#x26;" k="-10" />
    <hkern u1="&#x34;" u2="&#x2030;" k="25" />
    <hkern u1="&#x34;" u2="&#x201e;" k="-20" />
    <hkern u1="&#x34;" u2="&#x201a;" k="-20" />
    <hkern u1="&#x34;" u2="&#x2026;" k="-20" />
    <hkern u1="&#x34;" u2="&#x2e;" k="-20" />
    <hkern u1="&#x34;" u2="&#x2c;" k="-20" />
    <hkern u1="&#x34;" u2="&#x25;" k="25" />
    <hkern u1="&#x34;" u2="\" k="50" />
    <hkern u1="&#x34;" u2="&#x3f;" k="40" />
    <hkern u1="&#x34;" u2="&#x3e;" k="-20" />
    <hkern u1="&#x34;" u2="&#x3d;" k="-20" />
    <hkern u1="&#x34;" u2="&#x3c;" k="-20" />
    <hkern u1="&#x34;" u2="&#x39;" k="17" />
    <hkern u1="&#x34;" u2="&#x37;" k="40" />
    <hkern u1="&#x34;" u2="&#x35;" k="5" />
    <hkern u1="&#x34;" u2="&#x34;" k="-10" />
    <hkern u1="&#x34;" u2="&#x33;" k="10" />
    <hkern u1="&#x34;" u2="&#x31;" k="15" />
    <hkern u1="&#x34;" u2="&#x2b;" k="-10" />
    <hkern u1="&#x34;" u2="&#x2a;" k="20" />
    <hkern u1="&#x34;" u2="&#x26;" k="-20" />
    <hkern u1="&#x34;" u2="&#x23;" k="-10" />
    <hkern u1="&#x35;" u2="&#x2030;" k="20" />
    <hkern u1="&#x35;" u2="&#x201e;" k="20" />
    <hkern u1="&#x35;" u2="&#x201a;" k="20" />
    <hkern u1="&#x35;" u2="&#x2026;" k="20" />
    <hkern u1="&#x35;" u2="&#x2e;" k="20" />
    <hkern u1="&#x35;" u2="&#x2c;" k="20" />
    <hkern u1="&#x35;" u2="&#x25;" k="20" />
    <hkern u1="&#x35;" u2="\" k="25" />
    <hkern u1="&#x35;" u2="&#x3f;" k="20" />
    <hkern u1="&#x35;" u2="&#x39;" k="7" />
    <hkern u1="&#x35;" u2="&#x38;" k="-8" />
    <hkern u1="&#x35;" u2="&#x37;" k="20" />
    <hkern u1="&#x35;" u2="&#x36;" k="-5" />
    <hkern u1="&#x35;" u2="&#x34;" k="-5" />
    <hkern u1="&#x35;" u2="&#x33;" k="4" />
    <hkern u1="&#x35;" u2="&#x32;" k="8" />
    <hkern u1="&#x35;" u2="&#x31;" k="18" />
    <hkern u1="&#x35;" u2="&#x26;" k="-8" />
    <hkern u1="&#x35;" u2="&#x23;" k="5" />
    <hkern u1="&#x36;" u2="&#x2030;" k="18" />
    <hkern u1="&#x36;" u2="&#x201e;" k="10" />
    <hkern u1="&#x36;" u2="&#x201a;" k="10" />
    <hkern u1="&#x36;" u2="&#x2026;" k="10" />
    <hkern u1="&#x36;" u2="&#x2e;" k="10" />
    <hkern u1="&#x36;" u2="&#x2c;" k="10" />
    <hkern u1="&#x36;" u2="&#x25;" k="18" />
    <hkern u1="&#x36;" u2="\" k="35" />
    <hkern u1="&#x36;" u2="&#x3f;" k="25" />
    <hkern u1="&#x36;" u2="&#x39;" k="10" />
    <hkern u1="&#x36;" u2="&#x37;" k="44" />
    <hkern u1="&#x36;" u2="&#x36;" k="-5" />
    <hkern u1="&#x36;" u2="&#x35;" k="10" />
    <hkern u1="&#x36;" u2="&#x34;" k="-7" />
    <hkern u1="&#x36;" u2="&#x33;" k="12" />
    <hkern u1="&#x36;" u2="&#x32;" k="18" />
    <hkern u1="&#x36;" u2="&#x31;" k="35" />
    <hkern u1="&#x36;" u2="&#x2f;" k="20" />
    <hkern u1="&#x36;" u2="&#x2a;" k="10" />
    <hkern u1="&#x36;" u2="&#x26;" k="-10" />
    <hkern u1="&#x37;" u2="&#x2030;" k="10" />
    <hkern u1="&#x37;" u2="&#x201e;" k="50" />
    <hkern u1="&#x37;" u2="&#x201a;" k="50" />
    <hkern u1="&#x37;" u2="&#x2026;" k="50" />
    <hkern u1="&#x37;" u2="&#x3b;" k="10" />
    <hkern u1="&#x37;" u2="&#x3a;" k="10" />
    <hkern u1="&#x37;" u2="&#x2e;" k="50" />
    <hkern u1="&#x37;" u2="&#x2c;" k="50" />
    <hkern u1="&#x37;" u2="&#x25;" k="10" />
    <hkern u1="&#x37;" g2="seven.numr" k="-12" />
    <hkern u1="&#x37;" u2="&#x24;" k="15" />
    <hkern u1="&#x37;" u2="&#x40;" k="50" />
    <hkern u1="&#x37;" u2="&#x3e;" k="10" />
    <hkern u1="&#x37;" u2="&#x3d;" k="10" />
    <hkern u1="&#x37;" u2="&#x3c;" k="20" />
    <hkern u1="&#x37;" u2="&#x39;" k="20" />
    <hkern u1="&#x37;" u2="&#x38;" k="40" />
    <hkern u1="&#x37;" u2="&#x37;" k="-8" />
    <hkern u1="&#x37;" u2="&#x36;" k="57" />
    <hkern u1="&#x37;" u2="&#x35;" k="32" />
    <hkern u1="&#x37;" u2="&#x34;" k="70" />
    <hkern u1="&#x37;" u2="&#x33;" k="35" />
    <hkern u1="&#x37;" u2="&#x32;" k="50" />
    <hkern u1="&#x37;" u2="&#x31;" k="12" />
    <hkern u1="&#x37;" u2="&#x30;" k="30" />
    <hkern u1="&#x37;" u2="&#x2f;" k="90" />
    <hkern u1="&#x37;" u2="&#x2b;" k="50" />
    <hkern u1="&#x37;" u2="&#x2a;" k="15" />
    <hkern u1="&#x37;" u2="&#x26;" k="60" />
    <hkern u1="&#x37;" u2="&#x23;" k="30" />
    <hkern u1="&#x38;" u2="&#x2030;" k="12" />
    <hkern u1="&#x38;" u2="&#x201e;" k="5" />
    <hkern u1="&#x38;" u2="&#x201a;" k="5" />
    <hkern u1="&#x38;" u2="&#x2026;" k="5" />
    <hkern u1="&#x38;" u2="&#x2e;" k="5" />
    <hkern u1="&#x38;" u2="&#x2c;" k="5" />
    <hkern u1="&#x38;" u2="&#x25;" k="12" />
    <hkern u1="&#x38;" u2="&#x24;" k="-5" />
    <hkern u1="&#x38;" u2="\" k="28" />
    <hkern u1="&#x38;" u2="&#x3f;" k="15" />
    <hkern u1="&#x38;" u2="&#x39;" k="10" />
    <hkern u1="&#x38;" u2="&#x37;" k="34" />
    <hkern u1="&#x38;" u2="&#x36;" k="8" />
    <hkern u1="&#x38;" u2="&#x35;" k="5" />
    <hkern u1="&#x38;" u2="&#x33;" k="10" />
    <hkern u1="&#x38;" u2="&#x32;" k="12" />
    <hkern u1="&#x38;" u2="&#x31;" k="17" />
    <hkern u1="&#x38;" u2="&#x2f;" k="10" />
    <hkern u1="&#x38;" u2="&#x2b;" k="5" />
    <hkern u1="&#x38;" u2="&#x2a;" k="10" />
    <hkern u1="&#x38;" u2="&#x23;" k="5" />
    <hkern u1="&#x39;" u2="&#x201e;" k="20" />
    <hkern u1="&#x39;" u2="&#x201a;" k="20" />
    <hkern u1="&#x39;" u2="&#x2026;" k="20" />
    <hkern u1="&#x39;" u2="&#x2e;" k="20" />
    <hkern u1="&#x39;" u2="&#x2c;" k="20" />
    <hkern u1="&#x39;" u2="\" k="35" />
    <hkern u1="&#x39;" u2="&#x3f;" k="20" />
    <hkern u1="&#x39;" u2="&#x38;" k="5" />
    <hkern u1="&#x39;" u2="&#x37;" k="45" />
    <hkern u1="&#x39;" u2="&#x36;" k="-5" />
    <hkern u1="&#x39;" u2="&#x35;" k="4" />
    <hkern u1="&#x39;" u2="&#x33;" k="34" />
    <hkern u1="&#x39;" u2="&#x32;" k="30" />
    <hkern u1="&#x39;" u2="&#x31;" k="25" />
    <hkern u1="&#x39;" u2="&#x30;" k="-10" />
    <hkern u1="&#x39;" u2="&#x2f;" k="35" />
    <hkern u1="&#x39;" u2="&#x26;" k="10" />
    <hkern u1="&#x3c;" u2="&#x37;" k="10" />
    <hkern u1="&#x3c;" u2="&#x34;" k="-30" />
    <hkern u1="&#x3d;" u2="&#x37;" k="20" />
    <hkern u1="&#x3d;" u2="&#x34;" k="-10" />
    <hkern u1="&#x3d;" u2="&#x32;" k="20" />
    <hkern u1="&#x3e;" u2="&#x37;" k="30" />
    <hkern u1="&#x3e;" u2="&#x35;" k="25" />
    <hkern u1="&#x3e;" u2="&#x33;" k="40" />
    <hkern u1="&#x3e;" u2="&#x32;" k="40" />
    <hkern u1="&#x3e;" u2="&#x31;" k="15" />
    <hkern u1="&#x3f;" u2="&#x201e;" k="100" />
    <hkern u1="&#x3f;" u2="&#x201a;" k="100" />
    <hkern u1="&#x3f;" u2="&#x2026;" k="100" />
    <hkern u1="&#x3f;" u2="&#x2e;" k="100" />
    <hkern u1="&#x3f;" u2="&#x2c;" k="100" />
    <hkern u1="&#x3f;" u2="&#xbf;" k="80" />
    <hkern u1="&#x40;" u2="&#x17e;" k="15" />
    <hkern u1="&#x40;" u2="&#x17d;" k="15" />
    <hkern u1="&#x40;" u2="&#xdd;" k="60" />
    <hkern u1="&#x40;" u2="&#x178;" k="60" />
    <hkern u1="&#x40;" u2="z" k="15" />
    <hkern u1="&#x40;" u2="Z" k="15" />
    <hkern u1="&#x40;" u2="Y" k="60" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="W" k="20" />
    <hkern u1="&#x40;" u2="V" k="25" />
    <hkern u1="&#x40;" u2="T" k="50" />
    <hkern u1="&#x40;" u2="&#x39;" k="10" />
    <hkern u1="&#x40;" u2="&#x37;" k="80" />
    <hkern u1="&#x40;" u2="&#x33;" k="30" />
    <hkern u1="&#x40;" u2="&#x32;" k="20" />
    <hkern u1="&#x40;" u2="&#x31;" k="10" />
    <hkern u1="A" u2="x" k="-18" />
    <hkern u1="A" u2="w" k="7" />
    <hkern u1="A" u2="t" k="20" />
    <hkern u1="A" u2="j" k="4" />
    <hkern u1="A" u2="W" k="42" />
    <hkern u1="A" u2="V" k="50" />
    <hkern u1="A" u2="T" k="86" />
    <hkern u1="A" u2="J" k="-18" />
    <hkern u1="A" u2="&#x3f;" k="30" />
    <hkern u1="A" u2="&#x2f;" k="-15" />
    <hkern u1="A" u2="&#x26;" k="5" />
    <hkern u1="B" g2="atilde.alt" k="-9" />
    <hkern u1="B" g2="aring.alt" k="-9" />
    <hkern u1="B" g2="agrave.alt" k="-9" />
    <hkern u1="B" g2="adieresis.alt" k="-9" />
    <hkern u1="B" g2="acircumflex.alt" k="-9" />
    <hkern u1="B" g2="aacute.alt" k="-9" />
    <hkern u1="B" u2="&#x153;" k="-10" />
    <hkern u1="B" g2="a.alt" k="-9" />
    <hkern u1="B" u2="&#xe5;" k="-10" />
    <hkern u1="B" u2="&#xe4;" k="-10" />
    <hkern u1="B" u2="&#xe3;" k="-10" />
    <hkern u1="B" u2="&#xe2;" k="-10" />
    <hkern u1="B" u2="&#xe1;" k="-10" />
    <hkern u1="B" u2="&#xe0;" k="-10" />
    <hkern u1="B" u2="a" k="-10" />
    <hkern u1="B" u2="&#xf6;" k="-10" />
    <hkern u1="B" u2="&#xe6;" k="-10" />
    <hkern u1="B" u2="&#xc6;" k="-4" />
    <hkern u1="B" u2="&#xc7;" k="-18" />
    <hkern u1="B" u2="&#x160;" k="-15" />
    <hkern u1="B" u2="&#x161;" k="-10" />
    <hkern u1="B" u2="&#x17e;" k="-6" />
    <hkern u1="B" u2="&#xdd;" k="35" />
    <hkern u1="B" u2="&#xd2;" k="-18" />
    <hkern u1="B" u2="&#xd4;" k="-18" />
    <hkern u1="B" u2="&#xd3;" k="-18" />
    <hkern u1="B" u2="&#xc1;" k="-4" />
    <hkern u1="B" u2="&#xc2;" k="-4" />
    <hkern u1="B" u2="&#x178;" k="35" />
    <hkern u1="B" u2="&#x152;" k="-18" />
    <hkern u1="B" u2="&#xd5;" k="-18" />
    <hkern u1="B" u2="&#xc3;" k="-4" />
    <hkern u1="B" u2="&#xc0;" k="-4" />
    <hkern u1="B" u2="&#xc5;" k="-4" />
    <hkern u1="B" u2="&#xc4;" k="-4" />
    <hkern u1="B" u2="&#xd6;" k="-18" />
    <hkern u1="B" u2="&#xfc;" k="-7" />
    <hkern u1="B" u2="&#xfb;" k="-7" />
    <hkern u1="B" u2="&#xf9;" k="-7" />
    <hkern u1="B" u2="&#xfa;" k="-7" />
    <hkern u1="B" u2="&#xea;" k="-10" />
    <hkern u1="B" u2="&#xeb;" k="-10" />
    <hkern u1="B" u2="&#xf3;" k="-10" />
    <hkern u1="B" u2="&#xf2;" k="-10" />
    <hkern u1="B" u2="&#xf4;" k="-10" />
    <hkern u1="B" u2="&#xf5;" k="-10" />
    <hkern u1="B" u2="&#xe8;" k="-10" />
    <hkern u1="B" u2="&#xe9;" k="-10" />
    <hkern u1="B" u2="&#xe7;" k="-10" />
    <hkern u1="B" u2="z" k="-6" />
    <hkern u1="B" u2="u" k="-7" />
    <hkern u1="B" u2="s" k="-10" />
    <hkern u1="B" u2="q" k="-10" />
    <hkern u1="B" u2="o" k="-10" />
    <hkern u1="B" u2="g" k="-10" />
    <hkern u1="B" u2="e" k="-10" />
    <hkern u1="B" u2="d" k="-10" />
    <hkern u1="B" u2="c" k="-10" />
    <hkern u1="B" u2="Y" k="35" />
    <hkern u1="B" u2="S" k="-15" />
    <hkern u1="B" u2="Q" k="-18" />
    <hkern u1="B" u2="O" k="-18" />
    <hkern u1="B" u2="G" k="-18" />
    <hkern u1="B" u2="C" k="-18" />
    <hkern u1="B" u2="A" k="-4" />
    <hkern u1="B" u2="x" k="-5" />
    <hkern u1="B" u2="W" k="5" />
    <hkern u1="B" u2="V" k="20" />
    <hkern u1="B" u2="J" k="-10" />
    <hkern u1="C" u2="&#xef;" k="-40" />
    <hkern u1="C" u2="&#xee;" k="-40" />
    <hkern u1="C" u2="&#xec;" k="-30" />
    <hkern u1="C" u2="X" k="-10" />
    <hkern u1="C" u2="J" k="-40" />
    <hkern u1="D" u2="x" k="-5" />
    <hkern u1="D" u2="w" k="-18" />
    <hkern u1="D" u2="t" k="-12" />
    <hkern u1="D" u2="l" k="-8" />
    <hkern u1="D" u2="X" k="35" />
    <hkern u1="D" u2="W" k="8" />
    <hkern u1="D" u2="V" k="8" />
    <hkern u1="D" u2="T" k="55" />
    <hkern u1="D" u2="J" k="4" />
    <hkern u1="E" u2="x" k="-10" />
    <hkern u1="E" u2="w" k="12" />
    <hkern u1="E" u2="t" k="8" />
    <hkern u1="E" u2="X" k="-4" />
    <hkern u1="E" u2="V" k="-8" />
    <hkern u1="E" u2="T" k="-4" />
    <hkern u1="E" u2="J" k="-12" />
    <hkern u1="F" g2="atilde.alt" k="52" />
    <hkern u1="F" g2="aring.alt" k="52" />
    <hkern u1="F" g2="agrave.alt" k="52" />
    <hkern u1="F" g2="adieresis.alt" k="52" />
    <hkern u1="F" g2="acircumflex.alt" k="52" />
    <hkern u1="F" g2="aacute.alt" k="52" />
    <hkern u1="F" u2="&#xed;" k="4" />
    <hkern u1="F" u2="&#x153;" k="42" />
    <hkern u1="F" g2="a.alt" k="52" />
    <hkern u1="F" u2="&#xe5;" k="42" />
    <hkern u1="F" u2="&#xe4;" k="42" />
    <hkern u1="F" u2="&#xe3;" k="42" />
    <hkern u1="F" u2="&#xe2;" k="42" />
    <hkern u1="F" u2="&#xe1;" k="42" />
    <hkern u1="F" u2="&#xe0;" k="42" />
    <hkern u1="F" u2="a" k="42" />
    <hkern u1="F" u2="&#xf6;" k="42" />
    <hkern u1="F" g2="ft" k="25" />
    <hkern u1="F" g2="fj" k="25" />
    <hkern u1="F" u2="&#xe6;" k="42" />
    <hkern u1="F" u2="&#xc7;" k="24" />
    <hkern u1="F" g2="ffl" k="25" />
    <hkern u1="F" g2="ffi" k="25" />
    <hkern u1="F" g2="ff" k="25" />
    <hkern u1="F" u2="&#x160;" k="20" />
    <hkern u1="F" u2="&#x161;" k="20" />
    <hkern u1="F" u2="&#x17e;" k="35" />
    <hkern u1="F" g2="fl" k="25" />
    <hkern u1="F" g2="fi" k="25" />
    <hkern u1="F" u2="&#xfd;" k="20" />
    <hkern u1="F" u2="&#xd2;" k="24" />
    <hkern u1="F" u2="&#xd9;" k="4" />
    <hkern u1="F" u2="&#xdb;" k="4" />
    <hkern u1="F" u2="&#xda;" k="4" />
    <hkern u1="F" u2="&#xd4;" k="24" />
    <hkern u1="F" u2="&#xd3;" k="24" />
    <hkern u1="F" u2="&#xc1;" k="88" />
    <hkern u1="F" u2="&#xc2;" k="88" />
    <hkern u1="F" u2="&#xff;" k="20" />
    <hkern u1="F" u2="&#x152;" k="24" />
    <hkern u1="F" u2="&#xd5;" k="24" />
    <hkern u1="F" u2="&#xc3;" k="88" />
    <hkern u1="F" u2="&#xc0;" k="88" />
    <hkern u1="F" u2="&#xc5;" k="88" />
    <hkern u1="F" u2="&#xc4;" k="88" />
    <hkern u1="F" u2="&#xdc;" k="4" />
    <hkern u1="F" u2="&#xd6;" k="24" />
    <hkern u1="F" u2="&#xfc;" k="40" />
    <hkern u1="F" u2="&#xfb;" k="40" />
    <hkern u1="F" u2="&#xf9;" k="40" />
    <hkern u1="F" u2="&#xfa;" k="40" />
    <hkern u1="F" u2="&#xea;" k="42" />
    <hkern u1="F" u2="&#xeb;" k="42" />
    <hkern u1="F" u2="&#xf1;" k="35" />
    <hkern u1="F" u2="&#xf3;" k="42" />
    <hkern u1="F" u2="&#xf2;" k="42" />
    <hkern u1="F" u2="&#xf4;" k="42" />
    <hkern u1="F" u2="&#xf5;" k="42" />
    <hkern u1="F" u2="&#xe8;" k="42" />
    <hkern u1="F" u2="&#xe9;" k="42" />
    <hkern u1="F" u2="&#xe7;" k="42" />
    <hkern u1="F" u2="&#x131;" k="35" />
    <hkern u1="F" u2="&#x201e;" k="50" />
    <hkern u1="F" u2="&#x201a;" k="50" />
    <hkern u1="F" u2="&#x203a;" k="15" />
    <hkern u1="F" u2="&#x2039;" k="30" />
    <hkern u1="F" u2="&#x2026;" k="50" />
    <hkern u1="F" u2="&#xbb;" k="15" />
    <hkern u1="F" u2="&#xab;" k="30" />
    <hkern u1="F" u2="z" k="35" />
    <hkern u1="F" u2="y" k="20" />
    <hkern u1="F" u2="v" k="20" />
    <hkern u1="F" u2="u" k="40" />
    <hkern u1="F" u2="s" k="20" />
    <hkern u1="F" u2="r" k="35" />
    <hkern u1="F" u2="q" k="42" />
    <hkern u1="F" u2="p" k="35" />
    <hkern u1="F" u2="o" k="42" />
    <hkern u1="F" u2="n" k="35" />
    <hkern u1="F" u2="m" k="35" />
    <hkern u1="F" u2="k" k="4" />
    <hkern u1="F" u2="i" k="4" />
    <hkern u1="F" u2="h" k="4" />
    <hkern u1="F" u2="g" k="42" />
    <hkern u1="F" u2="f" k="25" />
    <hkern u1="F" u2="e" k="42" />
    <hkern u1="F" u2="d" k="42" />
    <hkern u1="F" u2="c" k="42" />
    <hkern u1="F" u2="b" k="4" />
    <hkern u1="F" u2="U" k="4" />
    <hkern u1="F" u2="S" k="20" />
    <hkern u1="F" u2="Q" k="24" />
    <hkern u1="F" u2="O" k="24" />
    <hkern u1="F" u2="G" k="24" />
    <hkern u1="F" u2="C" k="24" />
    <hkern u1="F" u2="A" k="88" />
    <hkern u1="F" u2="&#x2e;" k="50" />
    <hkern u1="F" u2="&#x2c;" k="50" />
    <hkern u1="F" u2="&#xef;" k="-36" />
    <hkern u1="F" u2="&#xee;" k="-26" />
    <hkern u1="F" u2="&#xec;" k="-38" />
    <hkern u1="F" u2="&#xc6;" k="148" />
    <hkern u1="F" u2="&#xbf;" k="100" />
    <hkern u1="F" u2="x" k="40" />
    <hkern u1="F" u2="w" k="25" />
    <hkern u1="F" u2="t" k="20" />
    <hkern u1="F" u2="l" k="5" />
    <hkern u1="F" u2="j" k="15" />
    <hkern u1="F" u2="T" k="-6" />
    <hkern u1="F" u2="J" k="115" />
    <hkern u1="F" u2="&#x40;" k="30" />
    <hkern u1="F" u2="&#x2f;" k="80" />
    <hkern u1="F" u2="&#x26;" k="50" />
    <hkern u1="F" u2="&#x21;" k="5" />
    <hkern u1="G" g2="atilde.alt" k="-5" />
    <hkern u1="G" g2="aring.alt" k="-5" />
    <hkern u1="G" g2="agrave.alt" k="-5" />
    <hkern u1="G" g2="adieresis.alt" k="-5" />
    <hkern u1="G" g2="acircumflex.alt" k="-5" />
    <hkern u1="G" g2="aacute.alt" k="-5" />
    <hkern u1="G" u2="&#xef;" k="-5" />
    <hkern u1="G" u2="&#xee;" k="-5" />
    <hkern u1="G" u2="&#xec;" k="-5" />
    <hkern u1="G" u2="&#xed;" k="-5" />
    <hkern u1="G" u2="&#x153;" k="-18" />
    <hkern u1="G" g2="a.alt" k="-5" />
    <hkern u1="G" u2="&#xe5;" k="-18" />
    <hkern u1="G" u2="&#xe4;" k="-18" />
    <hkern u1="G" u2="&#xe3;" k="-18" />
    <hkern u1="G" u2="&#xe2;" k="-18" />
    <hkern u1="G" u2="&#xe1;" k="-18" />
    <hkern u1="G" u2="&#xe0;" k="-18" />
    <hkern u1="G" u2="a" k="-18" />
    <hkern u1="G" u2="&#xf6;" k="-18" />
    <hkern u1="G" u2="&#xe6;" k="-18" />
    <hkern u1="G" u2="&#xc7;" k="-16" />
    <hkern u1="G" u2="&#x160;" k="-10" />
    <hkern u1="G" u2="&#x161;" k="-10" />
    <hkern u1="G" u2="&#xdd;" k="30" />
    <hkern u1="G" u2="&#xd2;" k="-16" />
    <hkern u1="G" u2="&#xd4;" k="-16" />
    <hkern u1="G" u2="&#xd3;" k="-16" />
    <hkern u1="G" u2="&#x178;" k="30" />
    <hkern u1="G" u2="&#x152;" k="-16" />
    <hkern u1="G" u2="&#xd5;" k="-16" />
    <hkern u1="G" u2="&#xd6;" k="-16" />
    <hkern u1="G" u2="&#xfc;" k="-8" />
    <hkern u1="G" u2="&#xfb;" k="-8" />
    <hkern u1="G" u2="&#xf9;" k="-8" />
    <hkern u1="G" u2="&#xfa;" k="-8" />
    <hkern u1="G" u2="&#xea;" k="-18" />
    <hkern u1="G" u2="&#xeb;" k="-18" />
    <hkern u1="G" u2="&#xf1;" k="-4" />
    <hkern u1="G" u2="&#xf3;" k="-18" />
    <hkern u1="G" u2="&#xf2;" k="-18" />
    <hkern u1="G" u2="&#xf4;" k="-18" />
    <hkern u1="G" u2="&#xf5;" k="-18" />
    <hkern u1="G" u2="&#xe8;" k="-18" />
    <hkern u1="G" u2="&#xe9;" k="-18" />
    <hkern u1="G" u2="&#xe7;" k="-18" />
    <hkern u1="G" u2="&#x131;" k="-4" />
    <hkern u1="G" u2="&#x2014;" k="-5" />
    <hkern u1="G" u2="&#x2013;" k="-5" />
    <hkern u1="G" u2="u" k="-8" />
    <hkern u1="G" u2="s" k="-10" />
    <hkern u1="G" u2="r" k="-4" />
    <hkern u1="G" u2="q" k="-18" />
    <hkern u1="G" u2="p" k="-4" />
    <hkern u1="G" u2="o" k="-18" />
    <hkern u1="G" u2="n" k="-4" />
    <hkern u1="G" u2="m" k="-4" />
    <hkern u1="G" u2="k" k="-5" />
    <hkern u1="G" u2="i" k="-5" />
    <hkern u1="G" u2="h" k="-5" />
    <hkern u1="G" u2="g" k="-18" />
    <hkern u1="G" u2="e" k="-18" />
    <hkern u1="G" u2="d" k="-18" />
    <hkern u1="G" u2="c" k="-18" />
    <hkern u1="G" u2="b" k="-5" />
    <hkern u1="G" u2="Y" k="30" />
    <hkern u1="G" u2="S" k="-10" />
    <hkern u1="G" u2="Q" k="-16" />
    <hkern u1="G" u2="O" k="-16" />
    <hkern u1="G" u2="G" k="-16" />
    <hkern u1="G" u2="C" k="-16" />
    <hkern u1="G" u2="&#x2d;" k="-5" />
    <hkern u1="G" u2="l" k="-5" />
    <hkern u1="G" u2="X" k="15" />
    <hkern u1="G" u2="W" k="12" />
    <hkern u1="G" u2="V" k="15" />
    <hkern u1="G" u2="T" k="35" />
    <hkern u1="G" u2="J" k="-15" />
    <hkern u1="H" u2="&#xef;" k="-10" />
    <hkern u1="H" u2="&#xee;" k="-10" />
    <hkern u1="H" u2="&#x2044;" k="-70" />
    <hkern u1="H" u2="l" k="-15" />
    <hkern u1="H" u2="X" k="4" />
    <hkern u1="H" u2="W" k="4" />
    <hkern u1="H" u2="V" k="4" />
    <hkern u1="H" u2="T" k="8" />
    <hkern u1="I" u2="&#xef;" k="-10" />
    <hkern u1="I" u2="&#xee;" k="-10" />
    <hkern u1="I" u2="&#x2044;" k="-70" />
    <hkern u1="I" u2="l" k="-15" />
    <hkern u1="I" u2="X" k="4" />
    <hkern u1="I" u2="W" k="4" />
    <hkern u1="I" u2="V" k="4" />
    <hkern u1="I" u2="T" k="8" />
    <hkern u1="J" u2="&#x153;" k="-5" />
    <hkern u1="J" u2="&#xe5;" k="-5" />
    <hkern u1="J" u2="&#xe4;" k="-5" />
    <hkern u1="J" u2="&#xe3;" k="-5" />
    <hkern u1="J" u2="&#xe2;" k="-5" />
    <hkern u1="J" u2="&#xe1;" k="-5" />
    <hkern u1="J" u2="&#xe0;" k="-5" />
    <hkern u1="J" u2="a" k="-5" />
    <hkern u1="J" u2="&#xf6;" k="-5" />
    <hkern u1="J" u2="&#xe6;" k="-5" />
    <hkern u1="J" u2="&#xc6;" k="12" />
    <hkern u1="J" u2="&#xc1;" k="12" />
    <hkern u1="J" u2="&#xc2;" k="12" />
    <hkern u1="J" u2="&#xc3;" k="12" />
    <hkern u1="J" u2="&#xc0;" k="12" />
    <hkern u1="J" u2="&#xc5;" k="12" />
    <hkern u1="J" u2="&#xc4;" k="12" />
    <hkern u1="J" u2="&#xea;" k="-5" />
    <hkern u1="J" u2="&#xeb;" k="-5" />
    <hkern u1="J" u2="&#xf3;" k="-5" />
    <hkern u1="J" u2="&#xf2;" k="-5" />
    <hkern u1="J" u2="&#xf4;" k="-5" />
    <hkern u1="J" u2="&#xf5;" k="-5" />
    <hkern u1="J" u2="&#xe8;" k="-5" />
    <hkern u1="J" u2="&#xe9;" k="-5" />
    <hkern u1="J" u2="&#xe7;" k="-5" />
    <hkern u1="J" u2="&#x201e;" k="10" />
    <hkern u1="J" u2="&#x201a;" k="10" />
    <hkern u1="J" u2="&#x2026;" k="10" />
    <hkern u1="J" u2="q" k="-5" />
    <hkern u1="J" u2="o" k="-5" />
    <hkern u1="J" u2="g" k="-5" />
    <hkern u1="J" u2="e" k="-5" />
    <hkern u1="J" u2="d" k="-5" />
    <hkern u1="J" u2="c" k="-5" />
    <hkern u1="J" u2="A" k="12" />
    <hkern u1="J" u2="&#x2e;" k="10" />
    <hkern u1="J" u2="&#x2c;" k="10" />
    <hkern u1="K" u2="&#x153;" k="18" />
    <hkern u1="K" u2="&#xe5;" k="18" />
    <hkern u1="K" u2="&#xe4;" k="18" />
    <hkern u1="K" u2="&#xe3;" k="18" />
    <hkern u1="K" u2="&#xe2;" k="18" />
    <hkern u1="K" u2="&#xe1;" k="18" />
    <hkern u1="K" u2="&#xe0;" k="18" />
    <hkern u1="K" u2="a" k="18" />
    <hkern u1="K" u2="&#xf6;" k="18" />
    <hkern u1="K" u2="&#xe6;" k="18" />
    <hkern u1="K" u2="&#xc6;" k="-5" />
    <hkern u1="K" u2="&#xc7;" k="32" />
    <hkern u1="K" u2="&#x160;" k="-6" />
    <hkern u1="K" u2="&#x161;" k="-20" />
    <hkern u1="K" u2="&#x17e;" k="-20" />
    <hkern u1="K" u2="&#x17d;" k="-25" />
    <hkern u1="K" u2="&#xfd;" k="30" />
    <hkern u1="K" u2="&#xdd;" k="15" />
    <hkern u1="K" u2="&#xd2;" k="32" />
    <hkern u1="K" u2="&#xd9;" k="14" />
    <hkern u1="K" u2="&#xdb;" k="14" />
    <hkern u1="K" u2="&#xda;" k="14" />
    <hkern u1="K" u2="&#xd4;" k="32" />
    <hkern u1="K" u2="&#xd3;" k="32" />
    <hkern u1="K" u2="&#xc1;" k="-5" />
    <hkern u1="K" u2="&#xc2;" k="-5" />
    <hkern u1="K" u2="&#x178;" k="15" />
    <hkern u1="K" u2="&#xff;" k="30" />
    <hkern u1="K" u2="&#x152;" k="32" />
    <hkern u1="K" u2="&#xd5;" k="32" />
    <hkern u1="K" u2="&#xc3;" k="-5" />
    <hkern u1="K" u2="&#xc0;" k="-5" />
    <hkern u1="K" u2="&#xc5;" k="-5" />
    <hkern u1="K" u2="&#xc4;" k="-5" />
    <hkern u1="K" u2="&#xdc;" k="14" />
    <hkern u1="K" u2="&#xd6;" k="32" />
    <hkern u1="K" u2="&#xfc;" k="25" />
    <hkern u1="K" u2="&#xfb;" k="25" />
    <hkern u1="K" u2="&#xf9;" k="25" />
    <hkern u1="K" u2="&#xfa;" k="25" />
    <hkern u1="K" u2="&#xea;" k="18" />
    <hkern u1="K" u2="&#xeb;" k="18" />
    <hkern u1="K" u2="&#xf3;" k="18" />
    <hkern u1="K" u2="&#xf2;" k="18" />
    <hkern u1="K" u2="&#xf4;" k="18" />
    <hkern u1="K" u2="&#xf5;" k="18" />
    <hkern u1="K" u2="&#xe8;" k="18" />
    <hkern u1="K" u2="&#xe9;" k="18" />
    <hkern u1="K" u2="&#xe7;" k="18" />
    <hkern u1="K" u2="&#x201e;" k="-20" />
    <hkern u1="K" u2="&#x201a;" k="-20" />
    <hkern u1="K" u2="&#x2039;" k="20" />
    <hkern u1="K" u2="&#x2014;" k="15" />
    <hkern u1="K" u2="&#x2013;" k="15" />
    <hkern u1="K" u2="&#x2026;" k="-20" />
    <hkern u1="K" u2="&#xab;" k="20" />
    <hkern u1="K" u2="z" k="-20" />
    <hkern u1="K" u2="y" k="30" />
    <hkern u1="K" u2="v" k="30" />
    <hkern u1="K" u2="u" k="25" />
    <hkern u1="K" u2="s" k="-20" />
    <hkern u1="K" u2="q" k="18" />
    <hkern u1="K" u2="o" k="18" />
    <hkern u1="K" u2="g" k="18" />
    <hkern u1="K" u2="e" k="18" />
    <hkern u1="K" u2="d" k="18" />
    <hkern u1="K" u2="c" k="18" />
    <hkern u1="K" u2="Z" k="-25" />
    <hkern u1="K" u2="Y" k="15" />
    <hkern u1="K" u2="U" k="14" />
    <hkern u1="K" u2="S" k="-6" />
    <hkern u1="K" u2="Q" k="32" />
    <hkern u1="K" u2="O" k="32" />
    <hkern u1="K" u2="G" k="32" />
    <hkern u1="K" u2="C" k="32" />
    <hkern u1="K" u2="A" k="-5" />
    <hkern u1="K" u2="&#x2e;" k="-20" />
    <hkern u1="K" u2="&#x2d;" k="15" />
    <hkern u1="K" u2="&#x2c;" k="-20" />
    <hkern u1="K" u2="&#xef;" k="-30" />
    <hkern u1="K" u2="&#xee;" k="-10" />
    <hkern u1="K" u2="&#xec;" k="-30" />
    <hkern u1="K" u2="&#xbf;" k="-10" />
    <hkern u1="K" u2="x" k="-20" />
    <hkern u1="K" u2="w" k="35" />
    <hkern u1="K" u2="t" k="10" />
    <hkern u1="K" u2="W" k="5" />
    <hkern u1="K" u2="V" k="4" />
    <hkern u1="K" u2="J" k="-18" />
    <hkern u1="K" u2="&#x40;" k="10" />
    <hkern u1="K" u2="&#x2f;" k="-15" />
    <hkern u1="K" u2="&#x26;" k="5" />
    <hkern u1="L" g2="atilde.alt" k="5" />
    <hkern u1="L" g2="aring.alt" k="5" />
    <hkern u1="L" g2="agrave.alt" k="5" />
    <hkern u1="L" g2="adieresis.alt" k="5" />
    <hkern u1="L" g2="acircumflex.alt" k="5" />
    <hkern u1="L" g2="aacute.alt" k="5" />
    <hkern u1="L" u2="&#x153;" k="5" />
    <hkern u1="L" g2="a.alt" k="5" />
    <hkern u1="L" u2="&#xe5;" k="5" />
    <hkern u1="L" u2="&#xe4;" k="5" />
    <hkern u1="L" u2="&#xe3;" k="5" />
    <hkern u1="L" u2="&#xe2;" k="5" />
    <hkern u1="L" u2="&#xe1;" k="5" />
    <hkern u1="L" u2="&#xe0;" k="5" />
    <hkern u1="L" u2="a" k="5" />
    <hkern u1="L" u2="&#xf6;" k="5" />
    <hkern u1="L" g2="ft" k="10" />
    <hkern u1="L" g2="fj" k="10" />
    <hkern u1="L" u2="&#xe6;" k="5" />
    <hkern u1="L" u2="&#xc6;" k="-22" />
    <hkern u1="L" u2="&#xc7;" k="12" />
    <hkern u1="L" g2="ffl" k="10" />
    <hkern u1="L" g2="ffi" k="10" />
    <hkern u1="L" g2="ff" k="10" />
    <hkern u1="L" u2="&#x160;" k="-25" />
    <hkern u1="L" u2="&#x161;" k="-10" />
    <hkern u1="L" u2="&#x17e;" k="-15" />
    <hkern u1="L" u2="&#x17d;" k="-25" />
    <hkern u1="L" g2="fl" k="10" />
    <hkern u1="L" g2="fi" k="10" />
    <hkern u1="L" u2="&#xfd;" k="35" />
    <hkern u1="L" u2="&#xdd;" k="78" />
    <hkern u1="L" u2="&#xd2;" k="12" />
    <hkern u1="L" u2="&#xd9;" k="15" />
    <hkern u1="L" u2="&#xdb;" k="15" />
    <hkern u1="L" u2="&#xda;" k="15" />
    <hkern u1="L" u2="&#xd4;" k="12" />
    <hkern u1="L" u2="&#xd3;" k="12" />
    <hkern u1="L" u2="&#xc1;" k="-22" />
    <hkern u1="L" u2="&#xc2;" k="-22" />
    <hkern u1="L" u2="&#x178;" k="78" />
    <hkern u1="L" u2="&#xff;" k="35" />
    <hkern u1="L" u2="&#x152;" k="12" />
    <hkern u1="L" u2="&#xd5;" k="12" />
    <hkern u1="L" u2="&#xc3;" k="-22" />
    <hkern u1="L" u2="&#xc0;" k="-22" />
    <hkern u1="L" u2="&#xc5;" k="-22" />
    <hkern u1="L" u2="&#xc4;" k="-22" />
    <hkern u1="L" u2="&#xdc;" k="15" />
    <hkern u1="L" u2="&#xd6;" k="12" />
    <hkern u1="L" u2="&#xfc;" k="18" />
    <hkern u1="L" u2="&#xfb;" k="18" />
    <hkern u1="L" u2="&#xf9;" k="18" />
    <hkern u1="L" u2="&#xfa;" k="18" />
    <hkern u1="L" u2="&#xea;" k="5" />
    <hkern u1="L" u2="&#xeb;" k="5" />
    <hkern u1="L" u2="&#xf3;" k="5" />
    <hkern u1="L" u2="&#xf2;" k="5" />
    <hkern u1="L" u2="&#xf4;" k="5" />
    <hkern u1="L" u2="&#xf5;" k="5" />
    <hkern u1="L" u2="&#xe8;" k="5" />
    <hkern u1="L" u2="&#xe9;" k="5" />
    <hkern u1="L" u2="&#xe7;" k="5" />
    <hkern u1="L" u2="&#x201e;" k="-10" />
    <hkern u1="L" u2="&#x201a;" k="-10" />
    <hkern u1="L" u2="&#x203a;" k="-10" />
    <hkern u1="L" u2="&#x2039;" k="20" />
    <hkern u1="L" u2="&#x2019;" k="20" />
    <hkern u1="L" u2="&#x2018;" k="20" />
    <hkern u1="L" u2="&#x201d;" k="20" />
    <hkern u1="L" u2="&#x201c;" k="20" />
    <hkern u1="L" u2="&#x2014;" k="30" />
    <hkern u1="L" u2="&#x2013;" k="30" />
    <hkern u1="L" u2="&#x2026;" k="-10" />
    <hkern u1="L" u2="&#xbb;" k="-10" />
    <hkern u1="L" u2="&#xab;" k="20" />
    <hkern u1="L" u2="z" k="-15" />
    <hkern u1="L" u2="y" k="35" />
    <hkern u1="L" u2="v" k="35" />
    <hkern u1="L" u2="u" k="18" />
    <hkern u1="L" u2="s" k="-10" />
    <hkern u1="L" u2="q" k="5" />
    <hkern u1="L" u2="o" k="5" />
    <hkern u1="L" u2="g" k="5" />
    <hkern u1="L" u2="f" k="10" />
    <hkern u1="L" u2="e" k="5" />
    <hkern u1="L" u2="d" k="5" />
    <hkern u1="L" u2="c" k="5" />
    <hkern u1="L" u2="Z" k="-25" />
    <hkern u1="L" u2="Y" k="78" />
    <hkern u1="L" u2="U" k="15" />
    <hkern u1="L" u2="S" k="-25" />
    <hkern u1="L" u2="Q" k="12" />
    <hkern u1="L" u2="O" k="12" />
    <hkern u1="L" u2="G" k="12" />
    <hkern u1="L" u2="C" k="12" />
    <hkern u1="L" u2="A" k="-22" />
    <hkern u1="L" u2="&#x2e;" k="-10" />
    <hkern u1="L" u2="&#x2d;" k="30" />
    <hkern u1="L" u2="&#x2c;" k="-10" />
    <hkern u1="L" u2="&#x27;" k="20" />
    <hkern u1="L" u2="&#x22;" k="20" />
    <hkern u1="L" u2="x" k="-20" />
    <hkern u1="L" u2="w" k="35" />
    <hkern u1="L" u2="t" k="25" />
    <hkern u1="L" u2="\" k="75" />
    <hkern u1="L" u2="W" k="45" />
    <hkern u1="L" u2="V" k="50" />
    <hkern u1="L" u2="T" k="72" />
    <hkern u1="L" u2="J" k="-25" />
    <hkern u1="L" u2="&#x3f;" k="30" />
    <hkern u1="L" u2="&#x2f;" k="-20" />
    <hkern u1="L" u2="&#x2a;" k="30" />
    <hkern u1="M" u2="&#xef;" k="-10" />
    <hkern u1="M" u2="&#xee;" k="-10" />
    <hkern u1="M" u2="&#x2044;" k="-70" />
    <hkern u1="M" u2="l" k="-15" />
    <hkern u1="M" u2="X" k="4" />
    <hkern u1="M" u2="W" k="4" />
    <hkern u1="M" u2="V" k="4" />
    <hkern u1="M" u2="T" k="8" />
    <hkern u1="N" u2="&#xef;" k="-10" />
    <hkern u1="N" u2="&#xee;" k="-10" />
    <hkern u1="N" u2="&#x2044;" k="-70" />
    <hkern u1="N" u2="l" k="-15" />
    <hkern u1="N" u2="X" k="4" />
    <hkern u1="N" u2="W" k="4" />
    <hkern u1="N" u2="V" k="4" />
    <hkern u1="N" u2="T" k="8" />
    <hkern u1="O" u2="x" k="-5" />
    <hkern u1="O" u2="w" k="-18" />
    <hkern u1="O" u2="t" k="-12" />
    <hkern u1="O" u2="l" k="-8" />
    <hkern u1="O" u2="X" k="35" />
    <hkern u1="O" u2="W" k="8" />
    <hkern u1="O" u2="V" k="8" />
    <hkern u1="O" u2="T" k="55" />
    <hkern u1="O" u2="J" k="4" />
    <hkern u1="P" u2="&#x153;" k="-4" />
    <hkern u1="P" u2="&#xe5;" k="-4" />
    <hkern u1="P" u2="&#xe4;" k="-4" />
    <hkern u1="P" u2="&#xe3;" k="-4" />
    <hkern u1="P" u2="&#xe2;" k="-4" />
    <hkern u1="P" u2="&#xe1;" k="-4" />
    <hkern u1="P" u2="&#xe0;" k="-4" />
    <hkern u1="P" u2="a" k="-4" />
    <hkern u1="P" u2="&#xf6;" k="-4" />
    <hkern u1="P" u2="&#xe6;" k="-4" />
    <hkern u1="P" u2="&#xc6;" k="50" />
    <hkern u1="P" u2="&#xc7;" k="-25" />
    <hkern u1="P" u2="&#x160;" k="-22" />
    <hkern u1="P" u2="&#x161;" k="-8" />
    <hkern u1="P" u2="&#x17e;" k="-5" />
    <hkern u1="P" u2="&#xfd;" k="-15" />
    <hkern u1="P" u2="&#xdd;" k="4" />
    <hkern u1="P" u2="&#xd2;" k="-25" />
    <hkern u1="P" u2="&#xd4;" k="-25" />
    <hkern u1="P" u2="&#xd3;" k="-25" />
    <hkern u1="P" u2="&#xc1;" k="50" />
    <hkern u1="P" u2="&#xc2;" k="50" />
    <hkern u1="P" u2="&#x178;" k="4" />
    <hkern u1="P" u2="&#xff;" k="-15" />
    <hkern u1="P" u2="&#x152;" k="-25" />
    <hkern u1="P" u2="&#xd5;" k="-25" />
    <hkern u1="P" u2="&#xc3;" k="50" />
    <hkern u1="P" u2="&#xc0;" k="50" />
    <hkern u1="P" u2="&#xc5;" k="50" />
    <hkern u1="P" u2="&#xc4;" k="50" />
    <hkern u1="P" u2="&#xd6;" k="-25" />
    <hkern u1="P" u2="&#xea;" k="-4" />
    <hkern u1="P" u2="&#xeb;" k="-4" />
    <hkern u1="P" u2="&#xf3;" k="-4" />
    <hkern u1="P" u2="&#xf2;" k="-4" />
    <hkern u1="P" u2="&#xf4;" k="-4" />
    <hkern u1="P" u2="&#xf5;" k="-4" />
    <hkern u1="P" u2="&#xe8;" k="-4" />
    <hkern u1="P" u2="&#xe9;" k="-4" />
    <hkern u1="P" u2="&#xe7;" k="-4" />
    <hkern u1="P" u2="&#x201e;" k="30" />
    <hkern u1="P" u2="&#x201a;" k="30" />
    <hkern u1="P" u2="&#x2039;" k="10" />
    <hkern u1="P" u2="&#x2019;" k="-20" />
    <hkern u1="P" u2="&#x201d;" k="-20" />
    <hkern u1="P" u2="&#x2026;" k="30" />
    <hkern u1="P" u2="&#xab;" k="10" />
    <hkern u1="P" u2="z" k="-5" />
    <hkern u1="P" u2="y" k="-15" />
    <hkern u1="P" u2="v" k="-15" />
    <hkern u1="P" u2="s" k="-8" />
    <hkern u1="P" u2="q" k="-4" />
    <hkern u1="P" u2="o" k="-4" />
    <hkern u1="P" u2="g" k="-4" />
    <hkern u1="P" u2="e" k="-4" />
    <hkern u1="P" u2="d" k="-4" />
    <hkern u1="P" u2="c" k="-4" />
    <hkern u1="P" u2="Y" k="4" />
    <hkern u1="P" u2="S" k="-22" />
    <hkern u1="P" u2="Q" k="-25" />
    <hkern u1="P" u2="O" k="-25" />
    <hkern u1="P" u2="G" k="-25" />
    <hkern u1="P" u2="C" k="-25" />
    <hkern u1="P" u2="A" k="50" />
    <hkern u1="P" u2="&#x3b;" k="-10" />
    <hkern u1="P" u2="&#x3a;" k="-10" />
    <hkern u1="P" u2="&#x2e;" k="30" />
    <hkern u1="P" u2="&#x2c;" k="30" />
    <hkern u1="P" u2="&#xef;" k="-15" />
    <hkern u1="P" u2="&#xee;" k="-30" />
    <hkern u1="P" u2="&#xec;" k="-10" />
    <hkern u1="P" u2="w" k="-15" />
    <hkern u1="P" u2="t" k="-5" />
    <hkern u1="P" u2="T" k="10" />
    <hkern u1="P" u2="J" k="60" />
    <hkern u1="P" u2="&#x2f;" k="50" />
    <hkern u1="Q" u2="&#x201e;" k="-10" />
    <hkern u1="Q" u2="&#x201a;" k="-10" />
    <hkern u1="Q" u2="x" k="-5" />
    <hkern u1="Q" u2="w" k="-18" />
    <hkern u1="Q" u2="t" k="-12" />
    <hkern u1="Q" u2="l" k="-8" />
    <hkern u1="Q" u2="X" k="35" />
    <hkern u1="Q" u2="W" k="8" />
    <hkern u1="Q" u2="V" k="8" />
    <hkern u1="Q" u2="T" k="55" />
    <hkern u1="Q" u2="J" k="4" />
    <hkern u1="R" u2="&#xc6;" k="-4" />
    <hkern u1="R" u2="&#xc7;" k="-11" />
    <hkern u1="R" u2="&#x160;" k="-15" />
    <hkern u1="R" u2="&#xdd;" k="24" />
    <hkern u1="R" u2="&#xd2;" k="-11" />
    <hkern u1="R" u2="&#xd4;" k="-11" />
    <hkern u1="R" u2="&#xd3;" k="-11" />
    <hkern u1="R" u2="&#xc1;" k="-4" />
    <hkern u1="R" u2="&#xc2;" k="-4" />
    <hkern u1="R" u2="&#x178;" k="24" />
    <hkern u1="R" u2="&#x152;" k="-11" />
    <hkern u1="R" u2="&#xd5;" k="-11" />
    <hkern u1="R" u2="&#xc3;" k="-4" />
    <hkern u1="R" u2="&#xc0;" k="-4" />
    <hkern u1="R" u2="&#xc5;" k="-4" />
    <hkern u1="R" u2="&#xc4;" k="-4" />
    <hkern u1="R" u2="&#xd6;" k="-11" />
    <hkern u1="R" u2="Y" k="24" />
    <hkern u1="R" u2="S" k="-15" />
    <hkern u1="R" u2="Q" k="-11" />
    <hkern u1="R" u2="O" k="-11" />
    <hkern u1="R" u2="G" k="-11" />
    <hkern u1="R" u2="C" k="-11" />
    <hkern u1="R" u2="A" k="-4" />
    <hkern u1="R" u2="W" k="4" />
    <hkern u1="R" u2="V" k="5" />
    <hkern u1="R" u2="T" k="25" />
    <hkern u1="R" u2="J" k="-10" />
    <hkern u1="S" u2="&#xef;" k="-15" />
    <hkern u1="S" u2="&#xee;" k="-15" />
    <hkern u1="S" u2="&#xec;" k="-13" />
    <hkern u1="S" u2="t" k="4" />
    <hkern u1="S" u2="l" k="-5" />
    <hkern u1="S" u2="X" k="5" />
    <hkern u1="S" u2="W" k="10" />
    <hkern u1="S" u2="V" k="4" />
    <hkern u1="S" u2="T" k="22" />
    <hkern u1="S" u2="&#x40;" k="-5" />
    <hkern u1="S" u2="&#x26;" k="-10" />
    <hkern u1="T" g2="atilde.alt" k="100" />
    <hkern u1="T" g2="aring.alt" k="100" />
    <hkern u1="T" g2="agrave.alt" k="100" />
    <hkern u1="T" g2="adieresis.alt" k="100" />
    <hkern u1="T" g2="acircumflex.alt" k="100" />
    <hkern u1="T" g2="aacute.alt" k="100" />
    <hkern u1="T" u2="&#x153;" k="95" />
    <hkern u1="T" g2="a.alt" k="100" />
    <hkern u1="T" u2="&#xe5;" k="95" />
    <hkern u1="T" u2="&#xe4;" k="95" />
    <hkern u1="T" u2="&#xe3;" k="95" />
    <hkern u1="T" u2="&#xe2;" k="95" />
    <hkern u1="T" u2="&#xe1;" k="95" />
    <hkern u1="T" u2="&#xe0;" k="95" />
    <hkern u1="T" u2="a" k="95" />
    <hkern u1="T" u2="&#xf6;" k="95" />
    <hkern u1="T" g2="ft" k="30" />
    <hkern u1="T" g2="fj" k="30" />
    <hkern u1="T" u2="&#xe6;" k="95" />
    <hkern u1="T" u2="&#xd1;" k="8" />
    <hkern u1="T" u2="&#xc7;" k="55" />
    <hkern u1="T" g2="ffl" k="30" />
    <hkern u1="T" g2="ffi" k="30" />
    <hkern u1="T" g2="ff" k="30" />
    <hkern u1="T" u2="&#x160;" k="8" />
    <hkern u1="T" u2="&#xde;" k="8" />
    <hkern u1="T" g2="fl" k="30" />
    <hkern u1="T" g2="fi" k="30" />
    <hkern u1="T" u2="&#xc9;" k="8" />
    <hkern u1="T" u2="&#xfd;" k="80" />
    <hkern u1="T" u2="&#xdd;" k="-10" />
    <hkern u1="T" u2="&#xd2;" k="55" />
    <hkern u1="T" u2="&#xd4;" k="55" />
    <hkern u1="T" u2="&#xd3;" k="55" />
    <hkern u1="T" u2="&#xcc;" k="8" />
    <hkern u1="T" u2="&#xcf;" k="8" />
    <hkern u1="T" u2="&#xce;" k="8" />
    <hkern u1="T" u2="&#xcd;" k="8" />
    <hkern u1="T" u2="&#xc8;" k="8" />
    <hkern u1="T" u2="&#xcb;" k="8" />
    <hkern u1="T" u2="&#xc1;" k="86" />
    <hkern u1="T" u2="&#xca;" k="8" />
    <hkern u1="T" u2="&#xc2;" k="86" />
    <hkern u1="T" u2="&#x178;" k="-10" />
    <hkern u1="T" u2="&#xff;" k="80" />
    <hkern u1="T" u2="&#x152;" k="55" />
    <hkern u1="T" u2="&#xd5;" k="55" />
    <hkern u1="T" u2="&#xc3;" k="86" />
    <hkern u1="T" u2="&#xc0;" k="86" />
    <hkern u1="T" u2="&#xc5;" k="86" />
    <hkern u1="T" u2="&#xc4;" k="86" />
    <hkern u1="T" u2="&#xd6;" k="55" />
    <hkern u1="T" u2="&#xfc;" k="110" />
    <hkern u1="T" u2="&#xfb;" k="110" />
    <hkern u1="T" u2="&#xf9;" k="110" />
    <hkern u1="T" u2="&#xfa;" k="110" />
    <hkern u1="T" u2="&#xea;" k="95" />
    <hkern u1="T" u2="&#xeb;" k="95" />
    <hkern u1="T" u2="&#xf1;" k="110" />
    <hkern u1="T" u2="&#xf3;" k="95" />
    <hkern u1="T" u2="&#xf2;" k="95" />
    <hkern u1="T" u2="&#xf4;" k="95" />
    <hkern u1="T" u2="&#xf5;" k="95" />
    <hkern u1="T" u2="&#xe8;" k="95" />
    <hkern u1="T" u2="&#xe9;" k="95" />
    <hkern u1="T" u2="&#xe7;" k="95" />
    <hkern u1="T" u2="&#x131;" k="110" />
    <hkern u1="T" u2="&#x201e;" k="50" />
    <hkern u1="T" u2="&#x201a;" k="50" />
    <hkern u1="T" u2="&#x203a;" k="10" />
    <hkern u1="T" u2="&#x2039;" k="30" />
    <hkern u1="T" u2="&#x2019;" k="-10" />
    <hkern u1="T" u2="&#x201d;" k="-10" />
    <hkern u1="T" u2="&#x2014;" k="50" />
    <hkern u1="T" u2="&#x2013;" k="50" />
    <hkern u1="T" u2="&#x2026;" k="50" />
    <hkern u1="T" u2="&#xbb;" k="10" />
    <hkern u1="T" u2="&#xab;" k="30" />
    <hkern u1="T" u2="z" k="85" />
    <hkern u1="T" u2="y" k="80" />
    <hkern u1="T" u2="v" k="80" />
    <hkern u1="T" u2="u" k="110" />
    <hkern u1="T" u2="s" k="90" />
    <hkern u1="T" u2="r" k="110" />
    <hkern u1="T" u2="q" k="95" />
    <hkern u1="T" u2="p" k="110" />
    <hkern u1="T" u2="o" k="95" />
    <hkern u1="T" u2="n" k="110" />
    <hkern u1="T" u2="m" k="110" />
    <hkern u1="T" u2="g" k="95" />
    <hkern u1="T" u2="f" k="30" />
    <hkern u1="T" u2="e" k="95" />
    <hkern u1="T" u2="d" k="95" />
    <hkern u1="T" u2="c" k="95" />
    <hkern u1="T" u2="Y" k="-10" />
    <hkern u1="T" u2="S" k="8" />
    <hkern u1="T" u2="R" k="8" />
    <hkern u1="T" u2="Q" k="55" />
    <hkern u1="T" u2="P" k="8" />
    <hkern u1="T" u2="O" k="55" />
    <hkern u1="T" u2="N" k="8" />
    <hkern u1="T" u2="M" k="8" />
    <hkern u1="T" u2="L" k="8" />
    <hkern u1="T" u2="K" k="8" />
    <hkern u1="T" u2="I" k="8" />
    <hkern u1="T" u2="H" k="8" />
    <hkern u1="T" u2="G" k="55" />
    <hkern u1="T" u2="F" k="8" />
    <hkern u1="T" u2="E" k="8" />
    <hkern u1="T" u2="D" k="8" />
    <hkern u1="T" u2="C" k="55" />
    <hkern u1="T" u2="B" k="8" />
    <hkern u1="T" u2="A" k="86" />
    <hkern u1="T" u2="&#x2e;" k="50" />
    <hkern u1="T" u2="&#x2d;" k="50" />
    <hkern u1="T" u2="&#x2c;" k="50" />
    <hkern u1="T" u2="&#xef;" k="-60" />
    <hkern u1="T" u2="&#xee;" k="-32" />
    <hkern u1="T" u2="&#xec;" k="-60" />
    <hkern u1="T" u2="&#xed;" k="40" />
    <hkern u1="T" u2="&#xc6;" k="126" />
    <hkern u1="T" u2="&#x161;" k="60" />
    <hkern u1="T" u2="&#x17e;" k="65" />
    <hkern u1="T" u2="x" k="95" />
    <hkern u1="T" u2="w" k="80" />
    <hkern u1="T" u2="t" k="30" />
    <hkern u1="T" u2="j" k="15" />
    <hkern u1="T" u2="W" k="-10" />
    <hkern u1="T" u2="V" k="-15" />
    <hkern u1="T" u2="T" k="-18" />
    <hkern u1="T" u2="J" k="100" />
    <hkern u1="T" u2="&#x40;" k="50" />
    <hkern u1="T" u2="&#x3f;" k="-15" />
    <hkern u1="T" u2="&#x2f;" k="80" />
    <hkern u1="T" u2="&#x26;" k="50" />
    <hkern u1="U" u2="X" k="10" />
    <hkern u1="U" u2="J" k="12" />
    <hkern u1="U" u2="&#x2f;" k="20" />
    <hkern u1="V" g2="atilde.alt" k="28" />
    <hkern u1="V" g2="aring.alt" k="28" />
    <hkern u1="V" g2="agrave.alt" k="28" />
    <hkern u1="V" g2="adieresis.alt" k="28" />
    <hkern u1="V" g2="acircumflex.alt" k="28" />
    <hkern u1="V" g2="aacute.alt" k="28" />
    <hkern u1="V" u2="&#x153;" k="43" />
    <hkern u1="V" g2="a.alt" k="28" />
    <hkern u1="V" u2="&#xe5;" k="43" />
    <hkern u1="V" u2="&#xe4;" k="43" />
    <hkern u1="V" u2="&#xe3;" k="43" />
    <hkern u1="V" u2="&#xe2;" k="43" />
    <hkern u1="V" u2="&#xe1;" k="43" />
    <hkern u1="V" u2="&#xe0;" k="43" />
    <hkern u1="V" u2="a" k="43" />
    <hkern u1="V" u2="&#xf6;" k="43" />
    <hkern u1="V" u2="&#xe6;" k="43" />
    <hkern u1="V" u2="&#xd1;" k="4" />
    <hkern u1="V" u2="&#xc7;" k="8" />
    <hkern u1="V" u2="&#x161;" k="30" />
    <hkern u1="V" u2="&#xde;" k="4" />
    <hkern u1="V" u2="&#x17e;" k="10" />
    <hkern u1="V" u2="&#xc9;" k="4" />
    <hkern u1="V" u2="&#xdd;" k="-10" />
    <hkern u1="V" u2="&#xd2;" k="8" />
    <hkern u1="V" u2="&#xd4;" k="8" />
    <hkern u1="V" u2="&#xd3;" k="8" />
    <hkern u1="V" u2="&#xcc;" k="4" />
    <hkern u1="V" u2="&#xcf;" k="4" />
    <hkern u1="V" u2="&#xce;" k="4" />
    <hkern u1="V" u2="&#xcd;" k="4" />
    <hkern u1="V" u2="&#xc8;" k="4" />
    <hkern u1="V" u2="&#xcb;" k="4" />
    <hkern u1="V" u2="&#xc1;" k="50" />
    <hkern u1="V" u2="&#xca;" k="4" />
    <hkern u1="V" u2="&#xc2;" k="50" />
    <hkern u1="V" u2="&#x178;" k="-10" />
    <hkern u1="V" u2="&#x152;" k="8" />
    <hkern u1="V" u2="&#xd5;" k="8" />
    <hkern u1="V" u2="&#xc3;" k="50" />
    <hkern u1="V" u2="&#xc0;" k="50" />
    <hkern u1="V" u2="&#xc5;" k="50" />
    <hkern u1="V" u2="&#xc4;" k="50" />
    <hkern u1="V" u2="&#xd6;" k="8" />
    <hkern u1="V" u2="&#xfc;" k="33" />
    <hkern u1="V" u2="&#xfb;" k="33" />
    <hkern u1="V" u2="&#xf9;" k="33" />
    <hkern u1="V" u2="&#xfa;" k="33" />
    <hkern u1="V" u2="&#xea;" k="43" />
    <hkern u1="V" u2="&#xeb;" k="43" />
    <hkern u1="V" u2="&#xf1;" k="45" />
    <hkern u1="V" u2="&#xf3;" k="43" />
    <hkern u1="V" u2="&#xf2;" k="43" />
    <hkern u1="V" u2="&#xf4;" k="43" />
    <hkern u1="V" u2="&#xf5;" k="43" />
    <hkern u1="V" u2="&#xe8;" k="43" />
    <hkern u1="V" u2="&#xe9;" k="43" />
    <hkern u1="V" u2="&#xe7;" k="43" />
    <hkern u1="V" u2="&#x131;" k="45" />
    <hkern u1="V" u2="&#x201e;" k="40" />
    <hkern u1="V" u2="&#x201a;" k="40" />
    <hkern u1="V" u2="&#x203a;" k="10" />
    <hkern u1="V" u2="&#x2039;" k="10" />
    <hkern u1="V" u2="&#x2014;" k="15" />
    <hkern u1="V" u2="&#x2013;" k="15" />
    <hkern u1="V" u2="&#x2026;" k="40" />
    <hkern u1="V" u2="&#xbb;" k="10" />
    <hkern u1="V" u2="&#xab;" k="10" />
    <hkern u1="V" u2="z" k="10" />
    <hkern u1="V" u2="u" k="33" />
    <hkern u1="V" u2="s" k="30" />
    <hkern u1="V" u2="r" k="45" />
    <hkern u1="V" u2="q" k="43" />
    <hkern u1="V" u2="p" k="45" />
    <hkern u1="V" u2="o" k="43" />
    <hkern u1="V" u2="n" k="45" />
    <hkern u1="V" u2="m" k="45" />
    <hkern u1="V" u2="k" k="10" />
    <hkern u1="V" u2="i" k="10" />
    <hkern u1="V" u2="h" k="10" />
    <hkern u1="V" u2="g" k="43" />
    <hkern u1="V" u2="e" k="43" />
    <hkern u1="V" u2="d" k="43" />
    <hkern u1="V" u2="c" k="43" />
    <hkern u1="V" u2="b" k="10" />
    <hkern u1="V" u2="Y" k="-10" />
    <hkern u1="V" u2="R" k="4" />
    <hkern u1="V" u2="Q" k="8" />
    <hkern u1="V" u2="P" k="4" />
    <hkern u1="V" u2="O" k="8" />
    <hkern u1="V" u2="N" k="4" />
    <hkern u1="V" u2="M" k="4" />
    <hkern u1="V" u2="L" k="4" />
    <hkern u1="V" u2="K" k="4" />
    <hkern u1="V" u2="I" k="4" />
    <hkern u1="V" u2="H" k="4" />
    <hkern u1="V" u2="G" k="8" />
    <hkern u1="V" u2="F" k="4" />
    <hkern u1="V" u2="E" k="4" />
    <hkern u1="V" u2="D" k="4" />
    <hkern u1="V" u2="C" k="8" />
    <hkern u1="V" u2="B" k="4" />
    <hkern u1="V" u2="A" k="50" />
    <hkern u1="V" u2="&#x2e;" k="40" />
    <hkern u1="V" u2="&#x2d;" k="15" />
    <hkern u1="V" u2="&#x2c;" k="40" />
    <hkern u1="V" u2="&#xef;" k="-50" />
    <hkern u1="V" u2="&#xee;" k="-25" />
    <hkern u1="V" u2="&#xec;" k="-50" />
    <hkern u1="V" u2="&#xed;" k="20" />
    <hkern u1="V" u2="&#xc6;" k="100" />
    <hkern u1="V" u2="x" k="15" />
    <hkern u1="V" u2="j" k="10" />
    <hkern u1="V" u2="X" k="10" />
    <hkern u1="V" u2="W" k="-10" />
    <hkern u1="V" u2="T" k="-15" />
    <hkern u1="V" u2="J" k="60" />
    <hkern u1="V" u2="&#x40;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="60" />
    <hkern u1="V" u2="&#x26;" k="10" />
    <hkern u1="W" g2="atilde.alt" k="20" />
    <hkern u1="W" g2="aring.alt" k="20" />
    <hkern u1="W" g2="agrave.alt" k="20" />
    <hkern u1="W" g2="adieresis.alt" k="20" />
    <hkern u1="W" g2="acircumflex.alt" k="20" />
    <hkern u1="W" g2="aacute.alt" k="20" />
    <hkern u1="W" u2="&#x153;" k="38" />
    <hkern u1="W" g2="a.alt" k="20" />
    <hkern u1="W" u2="&#xe5;" k="38" />
    <hkern u1="W" u2="&#xe4;" k="38" />
    <hkern u1="W" u2="&#xe3;" k="38" />
    <hkern u1="W" u2="&#xe2;" k="38" />
    <hkern u1="W" u2="&#xe1;" k="38" />
    <hkern u1="W" u2="&#xe0;" k="38" />
    <hkern u1="W" u2="a" k="38" />
    <hkern u1="W" u2="&#xf6;" k="38" />
    <hkern u1="W" u2="&#xe6;" k="38" />
    <hkern u1="W" u2="&#xd1;" k="4" />
    <hkern u1="W" u2="&#xc7;" k="8" />
    <hkern u1="W" u2="&#x161;" k="25" />
    <hkern u1="W" u2="&#xde;" k="4" />
    <hkern u1="W" u2="&#x17e;" k="30" />
    <hkern u1="W" u2="&#xc9;" k="4" />
    <hkern u1="W" u2="&#xdd;" k="-8" />
    <hkern u1="W" u2="&#xd2;" k="8" />
    <hkern u1="W" u2="&#xd4;" k="8" />
    <hkern u1="W" u2="&#xd3;" k="8" />
    <hkern u1="W" u2="&#xcc;" k="4" />
    <hkern u1="W" u2="&#xcf;" k="4" />
    <hkern u1="W" u2="&#xce;" k="4" />
    <hkern u1="W" u2="&#xcd;" k="4" />
    <hkern u1="W" u2="&#xc8;" k="4" />
    <hkern u1="W" u2="&#xcb;" k="4" />
    <hkern u1="W" u2="&#xc1;" k="42" />
    <hkern u1="W" u2="&#xca;" k="4" />
    <hkern u1="W" u2="&#xc2;" k="42" />
    <hkern u1="W" u2="&#x178;" k="-8" />
    <hkern u1="W" u2="&#x152;" k="8" />
    <hkern u1="W" u2="&#xd5;" k="8" />
    <hkern u1="W" u2="&#xc3;" k="42" />
    <hkern u1="W" u2="&#xc0;" k="42" />
    <hkern u1="W" u2="&#xc5;" k="42" />
    <hkern u1="W" u2="&#xc4;" k="42" />
    <hkern u1="W" u2="&#xd6;" k="8" />
    <hkern u1="W" u2="&#xfc;" k="25" />
    <hkern u1="W" u2="&#xfb;" k="25" />
    <hkern u1="W" u2="&#xf9;" k="25" />
    <hkern u1="W" u2="&#xfa;" k="25" />
    <hkern u1="W" u2="&#xea;" k="38" />
    <hkern u1="W" u2="&#xeb;" k="38" />
    <hkern u1="W" u2="&#xf1;" k="40" />
    <hkern u1="W" u2="&#xf3;" k="38" />
    <hkern u1="W" u2="&#xf2;" k="38" />
    <hkern u1="W" u2="&#xf4;" k="38" />
    <hkern u1="W" u2="&#xf5;" k="38" />
    <hkern u1="W" u2="&#xe8;" k="38" />
    <hkern u1="W" u2="&#xe9;" k="38" />
    <hkern u1="W" u2="&#xe7;" k="38" />
    <hkern u1="W" u2="&#x131;" k="40" />
    <hkern u1="W" u2="&#x201e;" k="30" />
    <hkern u1="W" u2="&#x201a;" k="30" />
    <hkern u1="W" u2="&#x2039;" k="10" />
    <hkern u1="W" u2="&#x2014;" k="10" />
    <hkern u1="W" u2="&#x2013;" k="10" />
    <hkern u1="W" u2="&#x2026;" k="30" />
    <hkern u1="W" u2="&#xab;" k="10" />
    <hkern u1="W" u2="z" k="30" />
    <hkern u1="W" u2="u" k="25" />
    <hkern u1="W" u2="s" k="25" />
    <hkern u1="W" u2="r" k="40" />
    <hkern u1="W" u2="q" k="38" />
    <hkern u1="W" u2="p" k="40" />
    <hkern u1="W" u2="o" k="38" />
    <hkern u1="W" u2="n" k="40" />
    <hkern u1="W" u2="m" k="40" />
    <hkern u1="W" u2="k" k="5" />
    <hkern u1="W" u2="i" k="5" />
    <hkern u1="W" u2="h" k="5" />
    <hkern u1="W" u2="g" k="38" />
    <hkern u1="W" u2="e" k="38" />
    <hkern u1="W" u2="d" k="38" />
    <hkern u1="W" u2="c" k="38" />
    <hkern u1="W" u2="b" k="5" />
    <hkern u1="W" u2="Y" k="-8" />
    <hkern u1="W" u2="R" k="4" />
    <hkern u1="W" u2="Q" k="8" />
    <hkern u1="W" u2="P" k="4" />
    <hkern u1="W" u2="O" k="8" />
    <hkern u1="W" u2="N" k="4" />
    <hkern u1="W" u2="M" k="4" />
    <hkern u1="W" u2="L" k="4" />
    <hkern u1="W" u2="K" k="4" />
    <hkern u1="W" u2="I" k="4" />
    <hkern u1="W" u2="H" k="4" />
    <hkern u1="W" u2="G" k="8" />
    <hkern u1="W" u2="F" k="4" />
    <hkern u1="W" u2="E" k="4" />
    <hkern u1="W" u2="D" k="4" />
    <hkern u1="W" u2="C" k="8" />
    <hkern u1="W" u2="B" k="4" />
    <hkern u1="W" u2="A" k="42" />
    <hkern u1="W" u2="&#x2e;" k="30" />
    <hkern u1="W" u2="&#x2d;" k="10" />
    <hkern u1="W" u2="&#x2c;" k="30" />
    <hkern u1="W" u2="&#xef;" k="-35" />
    <hkern u1="W" u2="&#xee;" k="-27" />
    <hkern u1="W" u2="&#xec;" k="-35" />
    <hkern u1="W" u2="&#xed;" k="37" />
    <hkern u1="W" u2="&#xc6;" k="92" />
    <hkern u1="W" u2="x" k="18" />
    <hkern u1="W" u2="l" k="5" />
    <hkern u1="W" u2="j" k="20" />
    <hkern u1="W" u2="X" k="5" />
    <hkern u1="W" u2="T" k="-10" />
    <hkern u1="W" u2="J" k="40" />
    <hkern u1="W" u2="&#x40;" k="20" />
    <hkern u1="W" u2="&#x2f;" k="50" />
    <hkern u1="X" u2="&#x153;" k="20" />
    <hkern u1="X" u2="&#xe5;" k="20" />
    <hkern u1="X" u2="&#xe4;" k="20" />
    <hkern u1="X" u2="&#xe3;" k="20" />
    <hkern u1="X" u2="&#xe2;" k="20" />
    <hkern u1="X" u2="&#xe1;" k="20" />
    <hkern u1="X" u2="&#xe0;" k="20" />
    <hkern u1="X" u2="a" k="20" />
    <hkern u1="X" u2="&#xf6;" k="20" />
    <hkern u1="X" g2="ft" k="20" />
    <hkern u1="X" g2="fj" k="20" />
    <hkern u1="X" u2="&#xe6;" k="20" />
    <hkern u1="X" u2="&#xd1;" k="4" />
    <hkern u1="X" u2="&#xc7;" k="35" />
    <hkern u1="X" g2="ffl" k="20" />
    <hkern u1="X" g2="ffi" k="20" />
    <hkern u1="X" g2="ff" k="20" />
    <hkern u1="X" u2="&#xde;" k="4" />
    <hkern u1="X" g2="fl" k="20" />
    <hkern u1="X" g2="fi" k="20" />
    <hkern u1="X" u2="&#xc9;" k="4" />
    <hkern u1="X" u2="&#xfd;" k="25" />
    <hkern u1="X" u2="&#xdd;" k="12" />
    <hkern u1="X" u2="&#xd2;" k="35" />
    <hkern u1="X" u2="&#xd9;" k="10" />
    <hkern u1="X" u2="&#xdb;" k="10" />
    <hkern u1="X" u2="&#xda;" k="10" />
    <hkern u1="X" u2="&#xd4;" k="35" />
    <hkern u1="X" u2="&#xd3;" k="35" />
    <hkern u1="X" u2="&#xcc;" k="4" />
    <hkern u1="X" u2="&#xcf;" k="4" />
    <hkern u1="X" u2="&#xce;" k="4" />
    <hkern u1="X" u2="&#xcd;" k="4" />
    <hkern u1="X" u2="&#xc8;" k="4" />
    <hkern u1="X" u2="&#xcb;" k="4" />
    <hkern u1="X" u2="&#xca;" k="4" />
    <hkern u1="X" u2="&#x178;" k="12" />
    <hkern u1="X" u2="&#xff;" k="25" />
    <hkern u1="X" u2="&#x152;" k="35" />
    <hkern u1="X" u2="&#xd5;" k="35" />
    <hkern u1="X" u2="&#xdc;" k="10" />
    <hkern u1="X" u2="&#xd6;" k="35" />
    <hkern u1="X" u2="&#xea;" k="20" />
    <hkern u1="X" u2="&#xeb;" k="20" />
    <hkern u1="X" u2="&#xf1;" k="10" />
    <hkern u1="X" u2="&#xf3;" k="20" />
    <hkern u1="X" u2="&#xf2;" k="20" />
    <hkern u1="X" u2="&#xf4;" k="20" />
    <hkern u1="X" u2="&#xf5;" k="20" />
    <hkern u1="X" u2="&#xe8;" k="20" />
    <hkern u1="X" u2="&#xe9;" k="20" />
    <hkern u1="X" u2="&#xe7;" k="20" />
    <hkern u1="X" u2="&#x131;" k="10" />
    <hkern u1="X" u2="&#x2039;" k="10" />
    <hkern u1="X" u2="&#x2014;" k="25" />
    <hkern u1="X" u2="&#x2013;" k="25" />
    <hkern u1="X" u2="&#xab;" k="10" />
    <hkern u1="X" u2="y" k="25" />
    <hkern u1="X" u2="v" k="25" />
    <hkern u1="X" u2="r" k="10" />
    <hkern u1="X" u2="q" k="20" />
    <hkern u1="X" u2="p" k="10" />
    <hkern u1="X" u2="o" k="20" />
    <hkern u1="X" u2="n" k="10" />
    <hkern u1="X" u2="m" k="10" />
    <hkern u1="X" u2="g" k="20" />
    <hkern u1="X" u2="f" k="20" />
    <hkern u1="X" u2="e" k="20" />
    <hkern u1="X" u2="d" k="20" />
    <hkern u1="X" u2="c" k="20" />
    <hkern u1="X" u2="Y" k="12" />
    <hkern u1="X" u2="U" k="10" />
    <hkern u1="X" u2="R" k="4" />
    <hkern u1="X" u2="Q" k="35" />
    <hkern u1="X" u2="P" k="4" />
    <hkern u1="X" u2="O" k="35" />
    <hkern u1="X" u2="N" k="4" />
    <hkern u1="X" u2="M" k="4" />
    <hkern u1="X" u2="L" k="4" />
    <hkern u1="X" u2="K" k="4" />
    <hkern u1="X" u2="I" k="4" />
    <hkern u1="X" u2="H" k="4" />
    <hkern u1="X" u2="G" k="35" />
    <hkern u1="X" u2="F" k="4" />
    <hkern u1="X" u2="E" k="4" />
    <hkern u1="X" u2="D" k="4" />
    <hkern u1="X" u2="C" k="35" />
    <hkern u1="X" u2="B" k="4" />
    <hkern u1="X" u2="&#x2d;" k="25" />
    <hkern u1="X" u2="&#xef;" k="-15" />
    <hkern u1="X" u2="x" k="-10" />
    <hkern u1="X" u2="w" k="30" />
    <hkern u1="X" u2="t" k="30" />
    <hkern u1="X" u2="l" k="10" />
    <hkern u1="X" u2="W" k="5" />
    <hkern u1="X" u2="V" k="10" />
    <hkern u1="X" u2="&#x40;" k="15" />
    <hkern u1="X" u2="&#x3f;" k="10" />
    <hkern u1="X" u2="&#x26;" k="15" />
    <hkern u1="Y" u2="&#xef;" k="-40" />
    <hkern u1="Y" u2="&#xee;" k="-25" />
    <hkern u1="Y" u2="&#xec;" k="-35" />
    <hkern u1="Y" u2="&#xed;" k="58" />
    <hkern u1="Y" u2="&#xc6;" k="140" />
    <hkern u1="Y" u2="x" k="45" />
    <hkern u1="Y" u2="w" k="35" />
    <hkern u1="Y" u2="t" k="25" />
    <hkern u1="Y" u2="l" k="18" />
    <hkern u1="Y" u2="j" k="38" />
    <hkern u1="Y" u2="X" k="12" />
    <hkern u1="Y" u2="W" k="-8" />
    <hkern u1="Y" u2="V" k="-10" />
    <hkern u1="Y" u2="T" k="-10" />
    <hkern u1="Y" u2="J" k="92" />
    <hkern u1="Y" u2="&#x40;" k="70" />
    <hkern u1="Y" u2="&#x3f;" k="-10" />
    <hkern u1="Y" u2="&#x2f;" k="110" />
    <hkern u1="Y" u2="&#x26;" k="40" />
    <hkern u1="Z" u2="&#xef;" k="-34" />
    <hkern u1="Z" u2="&#xee;" k="-20" />
    <hkern u1="Z" u2="&#xec;" k="-38" />
    <hkern u1="Z" u2="x" k="-15" />
    <hkern u1="Z" u2="w" k="20" />
    <hkern u1="Z" u2="t" k="25" />
    <hkern u1="Z" u2="&#x40;" k="15" />
    <hkern u1="Z" u2="&#x2f;" k="-10" />
    <hkern u1="[" u2="j" k="-60" />
    <hkern u1="[" u2="&#x34;" k="10" />
    <hkern u1="\" u2="&#x201e;" k="-30" />
    <hkern u1="\" u2="&#x201a;" k="-30" />
    <hkern u1="\" u2="&#x2026;" k="-30" />
    <hkern u1="\" u2="&#x2e;" k="-30" />
    <hkern u1="\" u2="&#x2c;" k="-30" />
    <hkern u1="\" u2="&#x3f;" k="30" />
    <hkern u1="\" u2="&#x39;" k="35" />
    <hkern u1="\" u2="&#x38;" k="10" />
    <hkern u1="\" u2="&#x37;" k="30" />
    <hkern u1="\" u2="&#x36;" k="25" />
    <hkern u1="\" u2="&#x34;" k="20" />
    <hkern u1="\" u2="&#x30;" k="20" />
    <hkern u1="_" u2="&#x2f;" k="-70" />
    <hkern u1="b" u2="x" k="20" />
    <hkern u1="b" u2="t" k="12" />
    <hkern u1="b" u2="j" k="10" />
    <hkern u1="b" u2="&#x3f;" k="30" />
    <hkern u1="c" u2="t" k="8" />
    <hkern u1="d" u2="t" k="5" />
    <hkern u1="d" u2="j" k="5" />
    <hkern u1="d" u2="&#x3f;" k="15" />
    <hkern u1="e" u2="w" k="-4" />
    <hkern u1="f" u2="&#xef;" k="-60" />
    <hkern u1="f" u2="&#xee;" k="-40" />
    <hkern u1="f" u2="&#xec;" k="-54" />
    <hkern u1="f" u2="&#xed;" k="20" />
    <hkern u1="f" u2="x" k="-5" />
    <hkern u1="f" u2="w" k="-10" />
    <hkern u1="f" u2="t" k="5" />
    <hkern u1="f" u2="&#x40;" k="35" />
    <hkern u1="f" u2="&#x3f;" k="-15" />
    <hkern u1="f" u2="&#x2f;" k="60" />
    <hkern u1="f" u2="&#x26;" k="30" />
    <hkern u1="g" u2="t" k="5" />
    <hkern u1="g" u2="j" k="-10" />
    <hkern u1="g" u2="&#x3f;" k="15" />
    <hkern u1="h" u2="&#x27;" k="10" />
    <hkern u1="h" u2="&#x22;" k="10" />
    <hkern u1="h" u2="t" k="5" />
    <hkern u1="h" u2="&#x3f;" k="20" />
    <hkern u1="h" u2="&#x26;" k="-10" />
    <hkern u1="i" u2="&#xef;" k="-30" />
    <hkern u1="i" u2="&#xee;" k="-20" />
    <hkern u1="i" u2="&#xec;" k="-30" />
    <hkern u1="i" u2="t" k="5" />
    <hkern u1="i" u2="j" k="5" />
    <hkern u1="i" u2="&#x3f;" k="15" />
    <hkern u1="j" u2="&#xef;" k="-35" />
    <hkern u1="j" u2="&#xee;" k="-20" />
    <hkern u1="j" u2="&#xec;" k="-30" />
    <hkern u1="j" u2="t" k="5" />
    <hkern u1="j" u2="j" k="5" />
    <hkern u1="j" u2="&#x3f;" k="15" />
    <hkern u1="k" u2="&#xef;" k="10" />
    <hkern u1="k" u2="&#xee;" k="10" />
    <hkern u1="k" u2="&#xec;" k="10" />
    <hkern u1="k" u2="&#xed;" k="10" />
    <hkern u1="k" u2="&#x153;" k="28" />
    <hkern u1="k" u2="&#xe5;" k="28" />
    <hkern u1="k" u2="&#xe4;" k="28" />
    <hkern u1="k" u2="&#xe3;" k="28" />
    <hkern u1="k" u2="&#xe2;" k="28" />
    <hkern u1="k" u2="&#xe1;" k="28" />
    <hkern u1="k" u2="&#xe0;" k="28" />
    <hkern u1="k" u2="a" k="28" />
    <hkern u1="k" u2="&#xf6;" k="28" />
    <hkern u1="k" g2="ft" k="12" />
    <hkern u1="k" g2="fj" k="12" />
    <hkern u1="k" u2="&#xe6;" k="28" />
    <hkern u1="k" g2="ffl" k="12" />
    <hkern u1="k" g2="ffi" k="12" />
    <hkern u1="k" g2="ff" k="12" />
    <hkern u1="k" g2="fl" k="12" />
    <hkern u1="k" g2="fi" k="12" />
    <hkern u1="k" u2="&#xfd;" k="12" />
    <hkern u1="k" u2="&#xff;" k="12" />
    <hkern u1="k" u2="&#xea;" k="28" />
    <hkern u1="k" u2="&#xeb;" k="28" />
    <hkern u1="k" u2="&#xf3;" k="28" />
    <hkern u1="k" u2="&#xf2;" k="28" />
    <hkern u1="k" u2="&#xf4;" k="28" />
    <hkern u1="k" u2="&#xf5;" k="28" />
    <hkern u1="k" u2="&#xe8;" k="28" />
    <hkern u1="k" u2="&#xe9;" k="28" />
    <hkern u1="k" u2="&#xe7;" k="28" />
    <hkern u1="k" u2="&#x2039;" k="20" />
    <hkern u1="k" u2="&#x2014;" k="30" />
    <hkern u1="k" u2="&#x2013;" k="30" />
    <hkern u1="k" u2="&#xab;" k="20" />
    <hkern u1="k" u2="y" k="12" />
    <hkern u1="k" u2="v" k="12" />
    <hkern u1="k" u2="q" k="28" />
    <hkern u1="k" u2="o" k="28" />
    <hkern u1="k" u2="k" k="10" />
    <hkern u1="k" u2="i" k="10" />
    <hkern u1="k" u2="h" k="10" />
    <hkern u1="k" u2="g" k="28" />
    <hkern u1="k" u2="f" k="12" />
    <hkern u1="k" u2="e" k="28" />
    <hkern u1="k" u2="d" k="28" />
    <hkern u1="k" u2="c" k="28" />
    <hkern u1="k" u2="b" k="10" />
    <hkern u1="k" u2="&#x2d;" k="30" />
    <hkern u1="k" u2="&#x27;" k="8" />
    <hkern u1="k" u2="&#x22;" k="8" />
    <hkern u1="k" u2="&#x7d;" k="20" />
    <hkern u1="k" u2="w" k="6" />
    <hkern u1="k" u2="t" k="15" />
    <hkern u1="k" u2="j" k="8" />
    <hkern u1="k" u2="]" k="20" />
    <hkern u1="k" u2="&#x40;" k="15" />
    <hkern u1="k" u2="&#x3f;" k="20" />
    <hkern u1="k" u2="&#x29;" k="20" />
    <hkern u1="k" u2="&#x26;" k="20" />
    <hkern u1="k" u2="&#x21;" k="5" />
    <hkern u1="l" u2="w" k="17" />
    <hkern u1="l" u2="t" k="10" />
    <hkern u1="l" u2="&#x3f;" k="20" />
    <hkern u1="l" u2="&#x2f;" k="-20" />
    <hkern u1="m" u2="t" k="5" />
    <hkern u1="m" u2="&#x3f;" k="20" />
    <hkern u1="m" u2="&#x26;" k="-10" />
    <hkern u1="n" u2="t" k="5" />
    <hkern u1="n" u2="&#x3f;" k="20" />
    <hkern u1="n" u2="&#x26;" k="-10" />
    <hkern u1="o" u2="x" k="20" />
    <hkern u1="o" u2="t" k="12" />
    <hkern u1="o" u2="j" k="10" />
    <hkern u1="o" u2="&#x3f;" k="30" />
    <hkern u1="p" u2="x" k="20" />
    <hkern u1="p" u2="t" k="12" />
    <hkern u1="p" u2="j" k="10" />
    <hkern u1="p" u2="&#x3f;" k="30" />
    <hkern u1="q" u2="t" k="5" />
    <hkern u1="q" u2="j" k="5" />
    <hkern u1="q" u2="&#x3f;" k="15" />
    <hkern u1="r" g2="atilde.alt" k="-10" />
    <hkern u1="r" g2="aring.alt" k="-10" />
    <hkern u1="r" g2="agrave.alt" k="-10" />
    <hkern u1="r" g2="adieresis.alt" k="-10" />
    <hkern u1="r" g2="acircumflex.alt" k="-10" />
    <hkern u1="r" g2="aacute.alt" k="-10" />
    <hkern u1="r" u2="&#xef;" k="-10" />
    <hkern u1="r" u2="&#xee;" k="-10" />
    <hkern u1="r" u2="&#xec;" k="-10" />
    <hkern u1="r" u2="&#xed;" k="-10" />
    <hkern u1="r" u2="&#x153;" k="6" />
    <hkern u1="r" g2="a.alt" k="-10" />
    <hkern u1="r" u2="&#xe5;" k="6" />
    <hkern u1="r" u2="&#xe4;" k="6" />
    <hkern u1="r" u2="&#xe3;" k="6" />
    <hkern u1="r" u2="&#xe2;" k="6" />
    <hkern u1="r" u2="&#xe1;" k="6" />
    <hkern u1="r" u2="&#xe0;" k="6" />
    <hkern u1="r" u2="a" k="6" />
    <hkern u1="r" u2="&#xf6;" k="6" />
    <hkern u1="r" g2="ft" k="-35" />
    <hkern u1="r" g2="fj" k="-35" />
    <hkern u1="r" u2="&#xe6;" k="6" />
    <hkern u1="r" g2="ffl" k="-35" />
    <hkern u1="r" g2="ffi" k="-35" />
    <hkern u1="r" g2="ff" k="-35" />
    <hkern u1="r" u2="&#x161;" k="-18" />
    <hkern u1="r" u2="&#x17e;" k="-20" />
    <hkern u1="r" g2="fl" k="-35" />
    <hkern u1="r" g2="fi" k="-35" />
    <hkern u1="r" u2="&#xfd;" k="-44" />
    <hkern u1="r" u2="&#xff;" k="-44" />
    <hkern u1="r" u2="&#xfc;" k="-15" />
    <hkern u1="r" u2="&#xfb;" k="-15" />
    <hkern u1="r" u2="&#xf9;" k="-15" />
    <hkern u1="r" u2="&#xfa;" k="-15" />
    <hkern u1="r" u2="&#xea;" k="6" />
    <hkern u1="r" u2="&#xeb;" k="6" />
    <hkern u1="r" u2="&#xf1;" k="-10" />
    <hkern u1="r" u2="&#xf3;" k="6" />
    <hkern u1="r" u2="&#xf2;" k="6" />
    <hkern u1="r" u2="&#xf4;" k="6" />
    <hkern u1="r" u2="&#xf5;" k="6" />
    <hkern u1="r" u2="&#xe8;" k="6" />
    <hkern u1="r" u2="&#xe9;" k="6" />
    <hkern u1="r" u2="&#xe7;" k="6" />
    <hkern u1="r" u2="&#x131;" k="-10" />
    <hkern u1="r" u2="&#x201e;" k="25" />
    <hkern u1="r" u2="&#x201a;" k="25" />
    <hkern u1="r" u2="&#x203a;" k="-20" />
    <hkern u1="r" u2="&#x2019;" k="-30" />
    <hkern u1="r" u2="&#x2018;" k="-20" />
    <hkern u1="r" u2="&#x201d;" k="-30" />
    <hkern u1="r" u2="&#x201c;" k="-20" />
    <hkern u1="r" u2="&#x2014;" k="-10" />
    <hkern u1="r" u2="&#x2013;" k="-10" />
    <hkern u1="r" u2="&#x2026;" k="25" />
    <hkern u1="r" u2="&#xbb;" k="-20" />
    <hkern u1="r" u2="z" k="-20" />
    <hkern u1="r" u2="y" k="-44" />
    <hkern u1="r" u2="v" k="-44" />
    <hkern u1="r" u2="u" k="-15" />
    <hkern u1="r" u2="s" k="-18" />
    <hkern u1="r" u2="r" k="-10" />
    <hkern u1="r" u2="q" k="6" />
    <hkern u1="r" u2="p" k="-10" />
    <hkern u1="r" u2="o" k="6" />
    <hkern u1="r" u2="n" k="-10" />
    <hkern u1="r" u2="m" k="-10" />
    <hkern u1="r" u2="k" k="-10" />
    <hkern u1="r" u2="i" k="-10" />
    <hkern u1="r" u2="h" k="-10" />
    <hkern u1="r" u2="g" k="6" />
    <hkern u1="r" u2="f" k="-35" />
    <hkern u1="r" u2="e" k="6" />
    <hkern u1="r" u2="d" k="6" />
    <hkern u1="r" u2="c" k="6" />
    <hkern u1="r" u2="b" k="-10" />
    <hkern u1="r" u2="&#x2e;" k="25" />
    <hkern u1="r" u2="&#x2d;" k="-10" />
    <hkern u1="r" u2="&#x2c;" k="25" />
    <hkern u1="r" u2="&#x27;" k="-20" />
    <hkern u1="r" u2="&#x22;" k="-20" />
    <hkern u1="r" u2="x" k="-25" />
    <hkern u1="r" u2="w" k="-40" />
    <hkern u1="r" u2="t" k="-35" />
    <hkern u1="r" u2="l" k="-10" />
    <hkern u1="r" u2="&#x2f;" k="20" />
    <hkern u1="r" u2="&#x21;" k="-5" />
    <hkern u1="s" u2="t" k="10" />
    <hkern u1="s" u2="&#x3f;" k="20" />
    <hkern u1="s" u2="&#x26;" k="-10" />
    <hkern u1="t" u2="x" k="-30" />
    <hkern u1="t" u2="w" k="-15" />
    <hkern u1="t" u2="t" k="-4" />
    <hkern u1="t" u2="&#x3f;" k="10" />
    <hkern u1="t" u2="&#x2f;" k="-10" />
    <hkern u1="u" u2="t" k="5" />
    <hkern u1="u" u2="j" k="5" />
    <hkern u1="u" u2="&#x3f;" k="15" />
    <hkern u1="v" u2="x" k="-12" />
    <hkern u1="v" u2="w" k="-20" />
    <hkern u1="v" u2="t" k="-18" />
    <hkern u1="v" u2="&#x2f;" k="15" />
    <hkern u1="v" u2="&#x26;" k="-5" />
    <hkern u1="w" g2="ft" k="-10" />
    <hkern u1="w" g2="fj" k="-10" />
    <hkern u1="w" g2="ffl" k="-10" />
    <hkern u1="w" g2="ffi" k="-10" />
    <hkern u1="w" g2="ff" k="-10" />
    <hkern u1="w" g2="fl" k="-10" />
    <hkern u1="w" g2="fi" k="-10" />
    <hkern u1="w" u2="&#xfd;" k="-20" />
    <hkern u1="w" u2="&#xff;" k="-20" />
    <hkern u1="w" u2="&#x201e;" k="30" />
    <hkern u1="w" u2="&#x201a;" k="30" />
    <hkern u1="w" u2="&#x2026;" k="30" />
    <hkern u1="w" u2="y" k="-20" />
    <hkern u1="w" u2="v" k="-20" />
    <hkern u1="w" u2="f" k="-10" />
    <hkern u1="w" u2="&#x2e;" k="30" />
    <hkern u1="w" u2="&#x2c;" k="30" />
    <hkern u1="w" u2="x" k="-15" />
    <hkern u1="w" u2="t" k="-10" />
    <hkern u1="w" u2="&#x2f;" k="20" />
    <hkern u1="w" u2="&#x29;" k="20" />
    <hkern u1="x" u2="&#x153;" k="20" />
    <hkern u1="x" u2="&#xe5;" k="20" />
    <hkern u1="x" u2="&#xe4;" k="20" />
    <hkern u1="x" u2="&#xe3;" k="20" />
    <hkern u1="x" u2="&#xe2;" k="20" />
    <hkern u1="x" u2="&#xe1;" k="20" />
    <hkern u1="x" u2="&#xe0;" k="20" />
    <hkern u1="x" u2="a" k="20" />
    <hkern u1="x" u2="&#xf6;" k="20" />
    <hkern u1="x" u2="&#xe6;" k="20" />
    <hkern u1="x" u2="&#xfd;" k="-12" />
    <hkern u1="x" u2="&#xff;" k="-12" />
    <hkern u1="x" u2="&#xea;" k="20" />
    <hkern u1="x" u2="&#xeb;" k="20" />
    <hkern u1="x" u2="&#xf3;" k="20" />
    <hkern u1="x" u2="&#xf2;" k="20" />
    <hkern u1="x" u2="&#xf4;" k="20" />
    <hkern u1="x" u2="&#xf5;" k="20" />
    <hkern u1="x" u2="&#xe8;" k="20" />
    <hkern u1="x" u2="&#xe9;" k="20" />
    <hkern u1="x" u2="&#xe7;" k="20" />
    <hkern u1="x" u2="&#x2014;" k="20" />
    <hkern u1="x" u2="&#x2013;" k="20" />
    <hkern u1="x" u2="y" k="-12" />
    <hkern u1="x" u2="v" k="-12" />
    <hkern u1="x" u2="q" k="20" />
    <hkern u1="x" u2="o" k="20" />
    <hkern u1="x" u2="g" k="20" />
    <hkern u1="x" u2="e" k="20" />
    <hkern u1="x" u2="d" k="20" />
    <hkern u1="x" u2="c" k="20" />
    <hkern u1="x" u2="&#x2d;" k="20" />
    <hkern u1="x" u2="w" k="-15" />
    <hkern u1="x" u2="&#x3f;" k="10" />
    <hkern u1="y" u2="x" k="-12" />
    <hkern u1="y" u2="w" k="-20" />
    <hkern u1="y" u2="t" k="-18" />
    <hkern u1="y" u2="&#x2f;" k="15" />
    <hkern u1="y" u2="&#x26;" k="-5" />
    <hkern u1="z" u2="&#x40;" k="10" />
    <hkern u1="z" u2="&#x3f;" k="25" />
    <hkern u1="z" u2="&#x26;" k="15" />
    <hkern u1="&#x7b;" u2="j" k="-50" />
    <hkern u1="&#x7b;" u2="&#x34;" k="10" />
    <hkern u1="&#x7b;" u2="&#x32;" k="8" />
    <hkern u1="&#xab;" u2="V" k="10" />
    <hkern u1="&#xab;" u2="T" k="10" />
    <hkern u1="&#xab;" u2="J" k="-20" />
    <hkern u1="&#xbb;" u2="j" k="10" />
    <hkern u1="&#xbb;" u2="X" k="10" />
    <hkern u1="&#xbb;" u2="W" k="10" />
    <hkern u1="&#xbb;" u2="V" k="10" />
    <hkern u1="&#xbb;" u2="T" k="30" />
    <hkern u1="&#x2026;" u2="w" k="30" />
    <hkern u1="&#x2026;" u2="t" k="25" />
    <hkern u1="&#x2026;" u2="\" k="100" />
    <hkern u1="&#x2026;" u2="W" k="30" />
    <hkern u1="&#x2026;" u2="V" k="40" />
    <hkern u1="&#x2026;" u2="T" k="50" />
    <hkern u1="&#x2026;" u2="J" k="-20" />
    <hkern u1="&#x2026;" u2="&#x39;" k="25" />
    <hkern u1="&#x2026;" u2="&#x38;" k="5" />
    <hkern u1="&#x2026;" u2="&#x36;" k="20" />
    <hkern u1="&#x2026;" u2="&#x34;" k="20" />
    <hkern u1="&#x2026;" u2="&#x30;" k="20" />
    <hkern u1="&#x2026;" u2="&#x2a;" k="50" />
    <hkern u1="&#x2013;" u2="x" k="20" />
    <hkern u1="&#x2013;" u2="j" k="10" />
    <hkern u1="&#x2013;" u2="\" k="100" />
    <hkern u1="&#x2013;" u2="X" k="25" />
    <hkern u1="&#x2013;" u2="W" k="10" />
    <hkern u1="&#x2013;" u2="V" k="15" />
    <hkern u1="&#x2013;" u2="T" k="50" />
    <hkern u1="&#x2013;" u2="J" k="40" />
    <hkern u1="&#x2014;" u2="x" k="20" />
    <hkern u1="&#x2014;" u2="j" k="10" />
    <hkern u1="&#x2014;" u2="\" k="100" />
    <hkern u1="&#x2014;" u2="X" k="25" />
    <hkern u1="&#x2014;" u2="W" k="10" />
    <hkern u1="&#x2014;" u2="V" k="15" />
    <hkern u1="&#x2014;" u2="T" k="50" />
    <hkern u1="&#x2014;" u2="J" k="40" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-40" />
    <hkern u1="&#x201c;" u2="&#xee;" k="-30" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-30" />
    <hkern u1="&#x201c;" u2="J" k="60" />
    <hkern u1="&#x201d;" u2="&#xef;" k="-40" />
    <hkern u1="&#x201d;" u2="&#xee;" k="-30" />
    <hkern u1="&#x201d;" u2="&#xec;" k="-30" />
    <hkern u1="&#x201d;" u2="x" k="20" />
    <hkern u1="&#x201d;" u2="j" k="15" />
    <hkern u1="&#x201d;" u2="T" k="-10" />
    <hkern u1="&#x201d;" u2="J" k="60" />
    <hkern u1="&#x2018;" u2="J" k="60" />
    <hkern u1="&#x2019;" u2="x" k="20" />
    <hkern u1="&#x2019;" u2="j" k="15" />
    <hkern u1="&#x2019;" u2="T" k="-10" />
    <hkern u1="&#x2019;" u2="J" k="60" />
    <hkern u1="&#x2044;" u2="&#xd1;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xde;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xc9;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xcc;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xcf;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xce;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xcd;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xc8;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xcb;" k="-70" />
    <hkern u1="&#x2044;" u2="&#xca;" k="-70" />
    <hkern u1="&#x2044;" u2="R" k="-70" />
    <hkern u1="&#x2044;" u2="P" k="-70" />
    <hkern u1="&#x2044;" u2="N" k="-70" />
    <hkern u1="&#x2044;" u2="M" k="-70" />
    <hkern u1="&#x2044;" u2="L" k="-70" />
    <hkern u1="&#x2044;" u2="K" k="-70" />
    <hkern u1="&#x2044;" u2="I" k="-70" />
    <hkern u1="&#x2044;" u2="H" k="-70" />
    <hkern u1="&#x2044;" u2="F" k="-70" />
    <hkern u1="&#x2044;" u2="E" k="-70" />
    <hkern u1="&#x2044;" u2="D" k="-70" />
    <hkern u1="&#x2044;" u2="B" k="-70" />
    <hkern u1="&#x2044;" g2="nine.dnom" k="25" />
    <hkern u1="&#x2044;" g2="eight.dnom" k="15" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-15" />
    <hkern u1="&#x2044;" g2="six.dnom" k="50" />
    <hkern u1="&#x2044;" g2="five.dnom" k="25" />
    <hkern u1="&#x2044;" g2="four.dnom" k="55" />
    <hkern u1="&#x2039;" u2="V" k="10" />
    <hkern u1="&#x2039;" u2="T" k="10" />
    <hkern u1="&#x2039;" u2="J" k="-20" />
    <hkern u1="&#x203a;" u2="j" k="10" />
    <hkern u1="&#x203a;" u2="X" k="10" />
    <hkern u1="&#x203a;" u2="W" k="10" />
    <hkern u1="&#x203a;" u2="V" k="10" />
    <hkern u1="&#x203a;" u2="T" k="30" />
    <hkern u1="&#x201a;" u2="w" k="30" />
    <hkern u1="&#x201a;" u2="t" k="25" />
    <hkern u1="&#x201a;" u2="\" k="100" />
    <hkern u1="&#x201a;" u2="W" k="30" />
    <hkern u1="&#x201a;" u2="V" k="40" />
    <hkern u1="&#x201a;" u2="T" k="50" />
    <hkern u1="&#x201a;" u2="J" k="-20" />
    <hkern u1="&#x201a;" u2="&#x39;" k="25" />
    <hkern u1="&#x201a;" u2="&#x38;" k="5" />
    <hkern u1="&#x201a;" u2="&#x36;" k="20" />
    <hkern u1="&#x201a;" u2="&#x34;" k="20" />
    <hkern u1="&#x201a;" u2="&#x30;" k="20" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="50" />
    <hkern u1="&#x201e;" u2="w" k="30" />
    <hkern u1="&#x201e;" u2="t" k="25" />
    <hkern u1="&#x201e;" u2="\" k="100" />
    <hkern u1="&#x201e;" u2="W" k="30" />
    <hkern u1="&#x201e;" u2="V" k="40" />
    <hkern u1="&#x201e;" u2="T" k="50" />
    <hkern u1="&#x201e;" u2="J" k="-20" />
    <hkern u1="&#x201e;" u2="&#x39;" k="25" />
    <hkern u1="&#x201e;" u2="&#x38;" k="5" />
    <hkern u1="&#x201e;" u2="&#x36;" k="20" />
    <hkern u1="&#x201e;" u2="&#x34;" k="20" />
    <hkern u1="&#x201e;" u2="&#x30;" k="20" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="50" />
    <hkern u1="&#x131;" u2="&#xef;" k="-30" />
    <hkern u1="&#x131;" u2="&#xee;" k="-20" />
    <hkern u1="&#x131;" u2="&#xec;" k="-30" />
    <hkern u1="&#x131;" u2="t" k="5" />
    <hkern u1="&#x131;" u2="j" k="5" />
    <hkern u1="&#x131;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe7;" u2="t" k="8" />
    <hkern u1="&#xe9;" u2="w" k="-4" />
    <hkern u1="&#xe8;" u2="w" k="-4" />
    <hkern u1="&#xf5;" u2="x" k="20" />
    <hkern u1="&#xf5;" u2="t" k="12" />
    <hkern u1="&#xf5;" u2="j" k="10" />
    <hkern u1="&#xf5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xf4;" u2="x" k="20" />
    <hkern u1="&#xf4;" u2="t" k="12" />
    <hkern u1="&#xf4;" u2="j" k="10" />
    <hkern u1="&#xf4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xf2;" u2="x" k="20" />
    <hkern u1="&#xf2;" u2="t" k="12" />
    <hkern u1="&#xf2;" u2="j" k="10" />
    <hkern u1="&#xf2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xf3;" u2="x" k="20" />
    <hkern u1="&#xf3;" u2="t" k="12" />
    <hkern u1="&#xf3;" u2="j" k="10" />
    <hkern u1="&#xf3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xf1;" u2="t" k="5" />
    <hkern u1="&#xf1;" u2="&#x3f;" k="20" />
    <hkern u1="&#xf1;" u2="&#x26;" k="-10" />
    <hkern u1="&#xeb;" u2="w" k="-4" />
    <hkern u1="&#xea;" u2="w" k="-4" />
    <hkern u1="&#xfa;" u2="t" k="5" />
    <hkern u1="&#xfa;" u2="j" k="5" />
    <hkern u1="&#xfa;" u2="&#x3f;" k="15" />
    <hkern u1="&#xf9;" u2="t" k="5" />
    <hkern u1="&#xf9;" u2="j" k="5" />
    <hkern u1="&#xf9;" u2="&#x3f;" k="15" />
    <hkern u1="&#xfb;" u2="t" k="5" />
    <hkern u1="&#xfb;" u2="j" k="5" />
    <hkern u1="&#xfb;" u2="&#x3f;" k="15" />
    <hkern u1="&#xfc;" u2="t" k="5" />
    <hkern u1="&#xfc;" u2="j" k="5" />
    <hkern u1="&#xfc;" u2="&#x3f;" k="15" />
    <hkern u1="&#xd6;" u2="x" k="-5" />
    <hkern u1="&#xd6;" u2="w" k="-18" />
    <hkern u1="&#xd6;" u2="t" k="-12" />
    <hkern u1="&#xd6;" u2="l" k="-8" />
    <hkern u1="&#xd6;" u2="X" k="35" />
    <hkern u1="&#xd6;" u2="W" k="8" />
    <hkern u1="&#xd6;" u2="V" k="8" />
    <hkern u1="&#xd6;" u2="T" k="55" />
    <hkern u1="&#xd6;" u2="J" k="4" />
    <hkern u1="&#xdc;" u2="X" k="10" />
    <hkern u1="&#xdc;" u2="J" k="12" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="20" />
    <hkern u1="&#xc4;" u2="x" k="-18" />
    <hkern u1="&#xc4;" u2="w" k="7" />
    <hkern u1="&#xc4;" u2="t" k="20" />
    <hkern u1="&#xc4;" u2="j" k="4" />
    <hkern u1="&#xc4;" u2="W" k="42" />
    <hkern u1="&#xc4;" u2="V" k="50" />
    <hkern u1="&#xc4;" u2="T" k="86" />
    <hkern u1="&#xc4;" u2="J" k="-18" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc4;" u2="&#x2f;" k="-15" />
    <hkern u1="&#xc4;" u2="&#x26;" k="5" />
    <hkern u1="&#xc5;" u2="x" k="-18" />
    <hkern u1="&#xc5;" u2="w" k="7" />
    <hkern u1="&#xc5;" u2="t" k="20" />
    <hkern u1="&#xc5;" u2="j" k="4" />
    <hkern u1="&#xc5;" u2="W" k="42" />
    <hkern u1="&#xc5;" u2="V" k="50" />
    <hkern u1="&#xc5;" u2="T" k="86" />
    <hkern u1="&#xc5;" u2="J" k="-18" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc5;" u2="&#x2f;" k="-15" />
    <hkern u1="&#xc5;" u2="&#x26;" k="5" />
    <hkern u1="&#xc0;" u2="x" k="-18" />
    <hkern u1="&#xc0;" u2="w" k="7" />
    <hkern u1="&#xc0;" u2="t" k="20" />
    <hkern u1="&#xc0;" u2="j" k="4" />
    <hkern u1="&#xc0;" u2="W" k="42" />
    <hkern u1="&#xc0;" u2="V" k="50" />
    <hkern u1="&#xc0;" u2="T" k="86" />
    <hkern u1="&#xc0;" u2="J" k="-18" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc0;" u2="&#x2f;" k="-15" />
    <hkern u1="&#xc0;" u2="&#x26;" k="5" />
    <hkern u1="&#xc3;" u2="x" k="-18" />
    <hkern u1="&#xc3;" u2="w" k="7" />
    <hkern u1="&#xc3;" u2="t" k="20" />
    <hkern u1="&#xc3;" u2="j" k="4" />
    <hkern u1="&#xc3;" u2="W" k="42" />
    <hkern u1="&#xc3;" u2="V" k="50" />
    <hkern u1="&#xc3;" u2="T" k="86" />
    <hkern u1="&#xc3;" u2="J" k="-18" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc3;" u2="&#x2f;" k="-15" />
    <hkern u1="&#xc3;" u2="&#x26;" k="5" />
    <hkern u1="&#xd5;" u2="x" k="-5" />
    <hkern u1="&#xd5;" u2="w" k="-18" />
    <hkern u1="&#xd5;" u2="t" k="-12" />
    <hkern u1="&#xd5;" u2="l" k="-8" />
    <hkern u1="&#xd5;" u2="X" k="35" />
    <hkern u1="&#xd5;" u2="W" k="8" />
    <hkern u1="&#xd5;" u2="V" k="8" />
    <hkern u1="&#xd5;" u2="T" k="55" />
    <hkern u1="&#xd5;" u2="J" k="4" />
    <hkern u1="&#x152;" u2="x" k="-10" />
    <hkern u1="&#x152;" u2="w" k="12" />
    <hkern u1="&#x152;" u2="t" k="8" />
    <hkern u1="&#x152;" u2="X" k="-4" />
    <hkern u1="&#x152;" u2="V" k="-8" />
    <hkern u1="&#x152;" u2="T" k="-4" />
    <hkern u1="&#x152;" u2="J" k="-12" />
    <hkern u1="&#xff;" u2="x" k="-12" />
    <hkern u1="&#xff;" u2="w" k="-20" />
    <hkern u1="&#xff;" u2="t" k="-18" />
    <hkern u1="&#xff;" u2="&#x2f;" k="15" />
    <hkern u1="&#xff;" u2="&#x26;" k="-5" />
    <hkern u1="&#x178;" u2="&#xef;" k="-40" />
    <hkern u1="&#x178;" u2="&#xee;" k="-25" />
    <hkern u1="&#x178;" u2="&#xec;" k="-35" />
    <hkern u1="&#x178;" u2="&#xed;" k="58" />
    <hkern u1="&#x178;" u2="&#xc6;" k="140" />
    <hkern u1="&#x178;" u2="x" k="45" />
    <hkern u1="&#x178;" u2="w" k="35" />
    <hkern u1="&#x178;" u2="t" k="25" />
    <hkern u1="&#x178;" u2="l" k="18" />
    <hkern u1="&#x178;" u2="j" k="38" />
    <hkern u1="&#x178;" u2="X" k="12" />
    <hkern u1="&#x178;" u2="W" k="-8" />
    <hkern u1="&#x178;" u2="V" k="-10" />
    <hkern u1="&#x178;" u2="T" k="-10" />
    <hkern u1="&#x178;" u2="J" k="92" />
    <hkern u1="&#x178;" u2="&#x40;" k="70" />
    <hkern u1="&#x178;" u2="&#x3f;" k="-10" />
    <hkern u1="&#x178;" u2="&#x2f;" k="110" />
    <hkern u1="&#x178;" u2="&#x26;" k="40" />
    <hkern u1="&#xc2;" u2="x" k="-18" />
    <hkern u1="&#xc2;" u2="w" k="7" />
    <hkern u1="&#xc2;" u2="t" k="20" />
    <hkern u1="&#xc2;" u2="j" k="4" />
    <hkern u1="&#xc2;" u2="W" k="42" />
    <hkern u1="&#xc2;" u2="V" k="50" />
    <hkern u1="&#xc2;" u2="T" k="86" />
    <hkern u1="&#xc2;" u2="J" k="-18" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc2;" u2="&#x2f;" k="-15" />
    <hkern u1="&#xc2;" u2="&#x26;" k="5" />
    <hkern u1="&#xca;" u2="x" k="-10" />
    <hkern u1="&#xca;" u2="w" k="12" />
    <hkern u1="&#xca;" u2="t" k="8" />
    <hkern u1="&#xca;" u2="X" k="-4" />
    <hkern u1="&#xca;" u2="V" k="-8" />
    <hkern u1="&#xca;" u2="T" k="-4" />
    <hkern u1="&#xca;" u2="J" k="-12" />
    <hkern u1="&#xc1;" u2="x" k="-18" />
    <hkern u1="&#xc1;" u2="w" k="7" />
    <hkern u1="&#xc1;" u2="t" k="20" />
    <hkern u1="&#xc1;" u2="j" k="4" />
    <hkern u1="&#xc1;" u2="W" k="42" />
    <hkern u1="&#xc1;" u2="V" k="50" />
    <hkern u1="&#xc1;" u2="T" k="86" />
    <hkern u1="&#xc1;" u2="J" k="-18" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc1;" u2="&#x2f;" k="-15" />
    <hkern u1="&#xc1;" u2="&#x26;" k="5" />
    <hkern u1="&#xcb;" u2="x" k="-10" />
    <hkern u1="&#xcb;" u2="w" k="12" />
    <hkern u1="&#xcb;" u2="t" k="8" />
    <hkern u1="&#xcb;" u2="X" k="-4" />
    <hkern u1="&#xcb;" u2="V" k="-8" />
    <hkern u1="&#xcb;" u2="T" k="-4" />
    <hkern u1="&#xcb;" u2="J" k="-12" />
    <hkern u1="&#xc8;" u2="x" k="-10" />
    <hkern u1="&#xc8;" u2="w" k="12" />
    <hkern u1="&#xc8;" u2="t" k="8" />
    <hkern u1="&#xc8;" u2="X" k="-4" />
    <hkern u1="&#xc8;" u2="V" k="-8" />
    <hkern u1="&#xc8;" u2="T" k="-4" />
    <hkern u1="&#xc8;" u2="J" k="-12" />
    <hkern u1="&#xcd;" u2="&#xef;" k="-10" />
    <hkern u1="&#xcd;" u2="&#xee;" k="-10" />
    <hkern u1="&#xcd;" u2="&#x2044;" k="-70" />
    <hkern u1="&#xcd;" u2="l" k="-15" />
    <hkern u1="&#xcd;" u2="X" k="4" />
    <hkern u1="&#xcd;" u2="W" k="4" />
    <hkern u1="&#xcd;" u2="V" k="4" />
    <hkern u1="&#xcd;" u2="T" k="8" />
    <hkern u1="&#xce;" u2="&#xef;" k="-10" />
    <hkern u1="&#xce;" u2="&#xee;" k="-10" />
    <hkern u1="&#xce;" u2="&#x2044;" k="-70" />
    <hkern u1="&#xce;" u2="l" k="-15" />
    <hkern u1="&#xce;" u2="X" k="4" />
    <hkern u1="&#xce;" u2="W" k="4" />
    <hkern u1="&#xce;" u2="V" k="4" />
    <hkern u1="&#xce;" u2="T" k="8" />
    <hkern u1="&#xcf;" u2="&#xef;" k="-10" />
    <hkern u1="&#xcf;" u2="&#xee;" k="-10" />
    <hkern u1="&#xcf;" u2="&#x2044;" k="-70" />
    <hkern u1="&#xcf;" u2="l" k="-15" />
    <hkern u1="&#xcf;" u2="X" k="4" />
    <hkern u1="&#xcf;" u2="W" k="4" />
    <hkern u1="&#xcf;" u2="V" k="4" />
    <hkern u1="&#xcf;" u2="T" k="8" />
    <hkern u1="&#xcc;" u2="&#xef;" k="-10" />
    <hkern u1="&#xcc;" u2="&#xee;" k="-10" />
    <hkern u1="&#xcc;" u2="&#x2044;" k="-70" />
    <hkern u1="&#xcc;" u2="l" k="-15" />
    <hkern u1="&#xcc;" u2="X" k="4" />
    <hkern u1="&#xcc;" u2="W" k="4" />
    <hkern u1="&#xcc;" u2="V" k="4" />
    <hkern u1="&#xcc;" u2="T" k="8" />
    <hkern u1="&#xd3;" u2="x" k="-5" />
    <hkern u1="&#xd3;" u2="w" k="-18" />
    <hkern u1="&#xd3;" u2="t" k="-12" />
    <hkern u1="&#xd3;" u2="l" k="-8" />
    <hkern u1="&#xd3;" u2="X" k="35" />
    <hkern u1="&#xd3;" u2="W" k="8" />
    <hkern u1="&#xd3;" u2="V" k="8" />
    <hkern u1="&#xd3;" u2="T" k="55" />
    <hkern u1="&#xd3;" u2="J" k="4" />
    <hkern u1="&#xd4;" u2="x" k="-5" />
    <hkern u1="&#xd4;" u2="w" k="-18" />
    <hkern u1="&#xd4;" u2="t" k="-12" />
    <hkern u1="&#xd4;" u2="l" k="-8" />
    <hkern u1="&#xd4;" u2="X" k="35" />
    <hkern u1="&#xd4;" u2="W" k="8" />
    <hkern u1="&#xd4;" u2="V" k="8" />
    <hkern u1="&#xd4;" u2="T" k="55" />
    <hkern u1="&#xd4;" u2="J" k="4" />
    <hkern u1="&#xda;" u2="X" k="10" />
    <hkern u1="&#xda;" u2="J" k="12" />
    <hkern u1="&#xda;" u2="&#x2f;" k="20" />
    <hkern u1="&#xdb;" u2="X" k="10" />
    <hkern u1="&#xdb;" u2="J" k="12" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="20" />
    <hkern u1="&#xd9;" u2="X" k="10" />
    <hkern u1="&#xd9;" u2="J" k="12" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="20" />
    <hkern u1="&#xd2;" u2="x" k="-5" />
    <hkern u1="&#xd2;" u2="w" k="-18" />
    <hkern u1="&#xd2;" u2="t" k="-12" />
    <hkern u1="&#xd2;" u2="l" k="-8" />
    <hkern u1="&#xd2;" u2="X" k="35" />
    <hkern u1="&#xd2;" u2="W" k="8" />
    <hkern u1="&#xd2;" u2="V" k="8" />
    <hkern u1="&#xd2;" u2="T" k="55" />
    <hkern u1="&#xd2;" u2="J" k="4" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-40" />
    <hkern u1="&#xdd;" u2="&#xee;" k="-25" />
    <hkern u1="&#xdd;" u2="&#xec;" k="-35" />
    <hkern u1="&#xdd;" u2="&#xed;" k="58" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="140" />
    <hkern u1="&#xdd;" u2="x" k="45" />
    <hkern u1="&#xdd;" u2="w" k="35" />
    <hkern u1="&#xdd;" u2="t" k="25" />
    <hkern u1="&#xdd;" u2="l" k="18" />
    <hkern u1="&#xdd;" u2="j" k="38" />
    <hkern u1="&#xdd;" u2="X" k="12" />
    <hkern u1="&#xdd;" u2="W" k="-8" />
    <hkern u1="&#xdd;" u2="V" k="-10" />
    <hkern u1="&#xdd;" u2="T" k="-10" />
    <hkern u1="&#xdd;" u2="J" k="92" />
    <hkern u1="&#xdd;" u2="&#x40;" k="70" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="-10" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="110" />
    <hkern u1="&#xdd;" u2="&#x26;" k="40" />
    <hkern u1="&#xfd;" u2="x" k="-12" />
    <hkern u1="&#xfd;" u2="w" k="-20" />
    <hkern u1="&#xfd;" u2="t" k="-18" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="15" />
    <hkern u1="&#xfd;" u2="&#x26;" k="-5" />
    <hkern u1="&#x24;" u2="&#x38;" k="-5" />
    <hkern u1="&#x24;" u2="&#x37;" k="15" />
    <hkern u1="&#x24;" u2="&#x34;" k="-15" />
    <hkern u1="&#x24;" u2="&#x32;" k="5" />
    <hkern u1="&#xc9;" u2="x" k="-10" />
    <hkern u1="&#xc9;" u2="w" k="12" />
    <hkern u1="&#xc9;" u2="t" k="8" />
    <hkern u1="&#xc9;" u2="X" k="-4" />
    <hkern u1="&#xc9;" u2="V" k="-8" />
    <hkern u1="&#xc9;" u2="T" k="-4" />
    <hkern u1="&#xc9;" u2="J" k="-12" />
    <hkern u1="&#xfe;" u2="x" k="20" />
    <hkern u1="&#xfe;" u2="t" k="12" />
    <hkern u1="&#xfe;" u2="j" k="10" />
    <hkern u1="&#xfe;" u2="&#x3f;" k="30" />
    <hkern g1="fi" u2="&#xef;" k="-30" />
    <hkern g1="fi" u2="&#xee;" k="-20" />
    <hkern g1="fi" u2="&#xec;" k="-30" />
    <hkern g1="fl" u2="w" k="17" />
    <hkern g1="fl" u2="t" k="10" />
    <hkern g1="fl" u2="&#x3f;" k="20" />
    <hkern g1="fl" u2="&#x2f;" k="-20" />
    <hkern u1="&#x17d;" u2="&#xef;" k="-34" />
    <hkern u1="&#x17d;" u2="&#xee;" k="-20" />
    <hkern u1="&#x17d;" u2="&#xec;" k="-38" />
    <hkern u1="&#x17d;" u2="x" k="-15" />
    <hkern u1="&#x17d;" u2="w" k="20" />
    <hkern u1="&#x17d;" u2="t" k="25" />
    <hkern u1="&#x17d;" u2="&#x40;" k="15" />
    <hkern u1="&#x17d;" u2="&#x2f;" k="-10" />
    <hkern u1="&#x17e;" u2="&#x40;" k="10" />
    <hkern u1="&#x17e;" u2="&#x3f;" k="25" />
    <hkern u1="&#x17e;" u2="&#x26;" k="15" />
    <hkern u1="&#xde;" u2="&#x153;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe5;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe4;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe3;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe2;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe1;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe0;" k="-20" />
    <hkern u1="&#xde;" u2="a" k="-20" />
    <hkern u1="&#xde;" u2="&#xf6;" k="-20" />
    <hkern u1="&#xde;" g2="ft" k="-10" />
    <hkern u1="&#xde;" g2="fj" k="-10" />
    <hkern u1="&#xde;" u2="&#xe6;" k="-20" />
    <hkern u1="&#xde;" u2="&#xc6;" k="5" />
    <hkern u1="&#xde;" u2="&#xc7;" k="-28" />
    <hkern u1="&#xde;" g2="ffl" k="-10" />
    <hkern u1="&#xde;" g2="ffi" k="-10" />
    <hkern u1="&#xde;" g2="ff" k="-10" />
    <hkern u1="&#xde;" u2="&#x160;" k="-18" />
    <hkern u1="&#xde;" u2="&#x161;" k="-25" />
    <hkern u1="&#xde;" u2="&#x17d;" k="15" />
    <hkern u1="&#xde;" g2="fl" k="-10" />
    <hkern u1="&#xde;" g2="fi" k="-10" />
    <hkern u1="&#xde;" u2="&#xfd;" k="-20" />
    <hkern u1="&#xde;" u2="&#xdd;" k="35" />
    <hkern u1="&#xde;" u2="&#xd2;" k="-28" />
    <hkern u1="&#xde;" u2="&#xd9;" k="-8" />
    <hkern u1="&#xde;" u2="&#xdb;" k="-8" />
    <hkern u1="&#xde;" u2="&#xda;" k="-8" />
    <hkern u1="&#xde;" u2="&#xd4;" k="-28" />
    <hkern u1="&#xde;" u2="&#xd3;" k="-28" />
    <hkern u1="&#xde;" u2="&#xc1;" k="5" />
    <hkern u1="&#xde;" u2="&#xc2;" k="5" />
    <hkern u1="&#xde;" u2="&#x178;" k="35" />
    <hkern u1="&#xde;" u2="&#xff;" k="-20" />
    <hkern u1="&#xde;" u2="&#x152;" k="-28" />
    <hkern u1="&#xde;" u2="&#xd5;" k="-28" />
    <hkern u1="&#xde;" u2="&#xc3;" k="5" />
    <hkern u1="&#xde;" u2="&#xc0;" k="5" />
    <hkern u1="&#xde;" u2="&#xc5;" k="5" />
    <hkern u1="&#xde;" u2="&#xc4;" k="5" />
    <hkern u1="&#xde;" u2="&#xdc;" k="-8" />
    <hkern u1="&#xde;" u2="&#xd6;" k="-28" />
    <hkern u1="&#xde;" u2="&#xea;" k="-20" />
    <hkern u1="&#xde;" u2="&#xeb;" k="-20" />
    <hkern u1="&#xde;" u2="&#xf3;" k="-20" />
    <hkern u1="&#xde;" u2="&#xf2;" k="-20" />
    <hkern u1="&#xde;" u2="&#xf4;" k="-20" />
    <hkern u1="&#xde;" u2="&#xf5;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe8;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe9;" k="-20" />
    <hkern u1="&#xde;" u2="&#xe7;" k="-20" />
    <hkern u1="&#xde;" u2="y" k="-20" />
    <hkern u1="&#xde;" u2="v" k="-20" />
    <hkern u1="&#xde;" u2="s" k="-25" />
    <hkern u1="&#xde;" u2="q" k="-20" />
    <hkern u1="&#xde;" u2="o" k="-20" />
    <hkern u1="&#xde;" u2="g" k="-20" />
    <hkern u1="&#xde;" u2="f" k="-10" />
    <hkern u1="&#xde;" u2="e" k="-20" />
    <hkern u1="&#xde;" u2="d" k="-20" />
    <hkern u1="&#xde;" u2="c" k="-20" />
    <hkern u1="&#xde;" u2="Z" k="15" />
    <hkern u1="&#xde;" u2="Y" k="35" />
    <hkern u1="&#xde;" u2="U" k="-8" />
    <hkern u1="&#xde;" u2="S" k="-18" />
    <hkern u1="&#xde;" u2="Q" k="-28" />
    <hkern u1="&#xde;" u2="O" k="-28" />
    <hkern u1="&#xde;" u2="G" k="-28" />
    <hkern u1="&#xde;" u2="C" k="-28" />
    <hkern u1="&#xde;" u2="A" k="5" />
    <hkern u1="&#xde;" u2="w" k="-20" />
    <hkern u1="&#xde;" u2="t" k="-5" />
    <hkern u1="&#xde;" u2="X" k="25" />
    <hkern u1="&#xde;" u2="W" k="5" />
    <hkern u1="&#xde;" u2="T" k="60" />
    <hkern u1="&#x161;" u2="t" k="10" />
    <hkern u1="&#x161;" u2="&#x3f;" k="20" />
    <hkern u1="&#x161;" u2="&#x26;" k="-10" />
    <hkern u1="&#x160;" u2="&#xef;" k="-15" />
    <hkern u1="&#x160;" u2="&#xee;" k="-15" />
    <hkern u1="&#x160;" u2="&#xec;" k="-13" />
    <hkern u1="&#x160;" u2="t" k="4" />
    <hkern u1="&#x160;" u2="l" k="-5" />
    <hkern u1="&#x160;" u2="X" k="5" />
    <hkern u1="&#x160;" u2="W" k="10" />
    <hkern u1="&#x160;" u2="V" k="4" />
    <hkern u1="&#x160;" u2="T" k="22" />
    <hkern u1="&#x160;" u2="&#x40;" k="-5" />
    <hkern u1="&#x160;" u2="&#x26;" k="-10" />
    <hkern u1="&#xb5;" u2="t" k="5" />
    <hkern u1="&#xb5;" u2="j" k="5" />
    <hkern u1="&#xb5;" u2="&#x3f;" k="15" />
    <hkern u1="&#xdf;" u2="&#x153;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe5;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe4;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe3;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe2;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe1;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe0;" k="-10" />
    <hkern u1="&#xdf;" u2="a" k="-10" />
    <hkern u1="&#xdf;" u2="&#xf6;" k="-10" />
    <hkern u1="&#xdf;" g2="ft" k="5" />
    <hkern u1="&#xdf;" g2="fj" k="5" />
    <hkern u1="&#xdf;" u2="&#xe6;" k="-10" />
    <hkern u1="&#xdf;" g2="ffl" k="5" />
    <hkern u1="&#xdf;" g2="ffi" k="5" />
    <hkern u1="&#xdf;" g2="ff" k="5" />
    <hkern u1="&#xdf;" u2="&#x161;" k="-10" />
    <hkern u1="&#xdf;" g2="fl" k="5" />
    <hkern u1="&#xdf;" g2="fi" k="5" />
    <hkern u1="&#xdf;" u2="&#xea;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xeb;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xf3;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xf2;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xf4;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xf5;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe8;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe9;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xe7;" k="-10" />
    <hkern u1="&#xdf;" u2="s" k="-10" />
    <hkern u1="&#xdf;" u2="q" k="-10" />
    <hkern u1="&#xdf;" u2="o" k="-10" />
    <hkern u1="&#xdf;" u2="g" k="-10" />
    <hkern u1="&#xdf;" u2="f" k="5" />
    <hkern u1="&#xdf;" u2="e" k="-10" />
    <hkern u1="&#xdf;" u2="d" k="-10" />
    <hkern u1="&#xdf;" u2="c" k="-10" />
    <hkern u1="&#xdf;" u2="t" k="8" />
    <hkern g1="ff" u2="&#xef;" k="-60" />
    <hkern g1="ff" u2="&#xee;" k="-40" />
    <hkern g1="ff" u2="&#xec;" k="-54" />
    <hkern g1="ff" u2="&#xed;" k="20" />
    <hkern g1="ff" u2="x" k="-5" />
    <hkern g1="ff" u2="w" k="-10" />
    <hkern g1="ff" u2="t" k="5" />
    <hkern g1="ff" u2="&#x40;" k="35" />
    <hkern g1="ff" u2="&#x3f;" k="-15" />
    <hkern g1="ff" u2="&#x2f;" k="60" />
    <hkern g1="ff" u2="&#x26;" k="30" />
    <hkern g1="ffi" u2="&#xef;" k="-30" />
    <hkern g1="ffi" u2="&#xee;" k="-20" />
    <hkern g1="ffi" u2="&#xec;" k="-30" />
    <hkern g1="ffl" u2="w" k="17" />
    <hkern g1="ffl" u2="t" k="10" />
    <hkern g1="ffl" u2="&#x3f;" k="20" />
    <hkern g1="ffl" u2="&#x2f;" k="-20" />
    <hkern u1="&#xc7;" u2="&#xef;" k="-40" />
    <hkern u1="&#xc7;" u2="&#xee;" k="-40" />
    <hkern u1="&#xc7;" u2="&#xec;" k="-30" />
    <hkern u1="&#xc7;" u2="X" k="-10" />
    <hkern u1="&#xc7;" u2="J" k="-40" />
    <hkern u1="&#xd1;" u2="&#xef;" k="-10" />
    <hkern u1="&#xd1;" u2="&#xee;" k="-10" />
    <hkern u1="&#xd1;" u2="&#x2044;" k="-70" />
    <hkern u1="&#xd1;" u2="l" k="-15" />
    <hkern u1="&#xd1;" u2="X" k="4" />
    <hkern u1="&#xd1;" u2="W" k="4" />
    <hkern u1="&#xd1;" u2="V" k="4" />
    <hkern u1="&#xd1;" u2="T" k="8" />
    <hkern u1="&#xd0;" u2="x" k="-5" />
    <hkern u1="&#xd0;" u2="w" k="-18" />
    <hkern u1="&#xd0;" u2="t" k="-12" />
    <hkern u1="&#xd0;" u2="l" k="-8" />
    <hkern u1="&#xd0;" u2="X" k="35" />
    <hkern u1="&#xd0;" u2="W" k="8" />
    <hkern u1="&#xd0;" u2="V" k="8" />
    <hkern u1="&#xd0;" u2="T" k="55" />
    <hkern u1="&#xd0;" u2="J" k="4" />
    <hkern u1="&#xbf;" u2="&#xef;" k="20" />
    <hkern u1="&#xbf;" u2="&#xee;" k="20" />
    <hkern u1="&#xbf;" u2="&#xec;" k="20" />
    <hkern u1="&#xbf;" u2="&#xed;" k="20" />
    <hkern u1="&#xbf;" u2="&#x153;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe5;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe4;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe3;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe2;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe1;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe0;" k="25" />
    <hkern u1="&#xbf;" u2="a" k="25" />
    <hkern u1="&#xbf;" u2="&#xf6;" k="25" />
    <hkern u1="&#xbf;" g2="ft" k="30" />
    <hkern u1="&#xbf;" g2="fj" k="30" />
    <hkern u1="&#xbf;" u2="&#xe6;" k="25" />
    <hkern u1="&#xbf;" u2="&#xd1;" k="40" />
    <hkern u1="&#xbf;" u2="&#xc7;" k="30" />
    <hkern u1="&#xbf;" g2="ffl" k="30" />
    <hkern u1="&#xbf;" g2="ffi" k="30" />
    <hkern u1="&#xbf;" g2="ff" k="30" />
    <hkern u1="&#xbf;" u2="&#x160;" k="5" />
    <hkern u1="&#xbf;" u2="&#x161;" k="20" />
    <hkern u1="&#xbf;" u2="&#xde;" k="40" />
    <hkern u1="&#xbf;" g2="fl" k="30" />
    <hkern u1="&#xbf;" g2="fi" k="30" />
    <hkern u1="&#xbf;" u2="&#xc9;" k="40" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="40" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="85" />
    <hkern u1="&#xbf;" u2="&#xd2;" k="30" />
    <hkern u1="&#xbf;" u2="&#xd9;" k="30" />
    <hkern u1="&#xbf;" u2="&#xdb;" k="30" />
    <hkern u1="&#xbf;" u2="&#xda;" k="30" />
    <hkern u1="&#xbf;" u2="&#xd4;" k="30" />
    <hkern u1="&#xbf;" u2="&#xd3;" k="30" />
    <hkern u1="&#xbf;" u2="&#xcc;" k="40" />
    <hkern u1="&#xbf;" u2="&#xcf;" k="40" />
    <hkern u1="&#xbf;" u2="&#xce;" k="40" />
    <hkern u1="&#xbf;" u2="&#xcd;" k="40" />
    <hkern u1="&#xbf;" u2="&#xc8;" k="40" />
    <hkern u1="&#xbf;" u2="&#xcb;" k="40" />
    <hkern u1="&#xbf;" u2="&#xca;" k="40" />
    <hkern u1="&#xbf;" u2="&#x178;" k="85" />
    <hkern u1="&#xbf;" u2="&#xff;" k="40" />
    <hkern u1="&#xbf;" u2="&#x152;" k="30" />
    <hkern u1="&#xbf;" u2="&#xd5;" k="30" />
    <hkern u1="&#xbf;" u2="&#xdc;" k="30" />
    <hkern u1="&#xbf;" u2="&#xd6;" k="30" />
    <hkern u1="&#xbf;" u2="&#xfc;" k="35" />
    <hkern u1="&#xbf;" u2="&#xfb;" k="35" />
    <hkern u1="&#xbf;" u2="&#xf9;" k="35" />
    <hkern u1="&#xbf;" u2="&#xfa;" k="35" />
    <hkern u1="&#xbf;" u2="&#xea;" k="25" />
    <hkern u1="&#xbf;" u2="&#xeb;" k="25" />
    <hkern u1="&#xbf;" u2="&#xf1;" k="20" />
    <hkern u1="&#xbf;" u2="&#xf3;" k="25" />
    <hkern u1="&#xbf;" u2="&#xf2;" k="25" />
    <hkern u1="&#xbf;" u2="&#xf4;" k="25" />
    <hkern u1="&#xbf;" u2="&#xf5;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe8;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe9;" k="25" />
    <hkern u1="&#xbf;" u2="&#xe7;" k="25" />
    <hkern u1="&#xbf;" u2="&#x131;" k="20" />
    <hkern u1="&#xbf;" u2="v" k="40" />
    <hkern u1="&#xbf;" u2="u" k="35" />
    <hkern u1="&#xbf;" u2="s" k="20" />
    <hkern u1="&#xbf;" u2="r" k="20" />
    <hkern u1="&#xbf;" u2="q" k="25" />
    <hkern u1="&#xbf;" u2="p" k="20" />
    <hkern u1="&#xbf;" u2="o" k="25" />
    <hkern u1="&#xbf;" u2="n" k="20" />
    <hkern u1="&#xbf;" u2="m" k="20" />
    <hkern u1="&#xbf;" u2="k" k="20" />
    <hkern u1="&#xbf;" u2="i" k="20" />
    <hkern u1="&#xbf;" u2="h" k="20" />
    <hkern u1="&#xbf;" u2="g" k="25" />
    <hkern u1="&#xbf;" u2="f" k="30" />
    <hkern u1="&#xbf;" u2="e" k="25" />
    <hkern u1="&#xbf;" u2="d" k="25" />
    <hkern u1="&#xbf;" u2="c" k="25" />
    <hkern u1="&#xbf;" u2="b" k="20" />
    <hkern u1="&#xbf;" u2="Y" k="85" />
    <hkern u1="&#xbf;" u2="U" k="30" />
    <hkern u1="&#xbf;" u2="S" k="5" />
    <hkern u1="&#xbf;" u2="R" k="40" />
    <hkern u1="&#xbf;" u2="Q" k="30" />
    <hkern u1="&#xbf;" u2="P" k="40" />
    <hkern u1="&#xbf;" u2="O" k="30" />
    <hkern u1="&#xbf;" u2="N" k="40" />
    <hkern u1="&#xbf;" u2="M" k="40" />
    <hkern u1="&#xbf;" u2="L" k="40" />
    <hkern u1="&#xbf;" u2="K" k="40" />
    <hkern u1="&#xbf;" u2="I" k="40" />
    <hkern u1="&#xbf;" u2="H" k="40" />
    <hkern u1="&#xbf;" u2="G" k="30" />
    <hkern u1="&#xbf;" u2="F" k="40" />
    <hkern u1="&#xbf;" u2="E" k="40" />
    <hkern u1="&#xbf;" u2="D" k="40" />
    <hkern u1="&#xbf;" u2="C" k="30" />
    <hkern u1="&#xbf;" u2="B" k="40" />
    <hkern u1="&#xbf;" u2="y" k="15" />
    <hkern u1="&#xbf;" u2="w" k="40" />
    <hkern u1="&#xbf;" u2="t" k="50" />
    <hkern u1="&#xbf;" u2="l" k="40" />
    <hkern u1="&#xbf;" u2="j" k="-30" />
    <hkern u1="&#xbf;" u2="X" k="15" />
    <hkern u1="&#xbf;" u2="W" k="30" />
    <hkern u1="&#xbf;" u2="V" k="70" />
    <hkern u1="&#xbf;" u2="T" k="90" />
    <hkern u1="&#xbf;" u2="J" k="10" />
    <hkern u1="&#xbf;" u2="&#x3f;" k="20" />
    <hkern u1="&#xa1;" u2="&#xdd;" k="30" />
    <hkern u1="&#xa1;" u2="&#x178;" k="30" />
    <hkern u1="&#xa1;" u2="Y" k="30" />
    <hkern u1="&#xa1;" u2="j" k="-20" />
    <hkern u1="&#xa1;" u2="V" k="10" />
    <hkern u1="&#xa1;" u2="T" k="40" />
    <hkern u1="&#xe6;" u2="w" k="-4" />
    <hkern g1="fj" u2="&#xef;" k="-30" />
    <hkern g1="fj" u2="&#xee;" k="-20" />
    <hkern g1="fj" u2="&#xec;" k="-30" />
    <hkern g1="ft" u2="x" k="-30" />
    <hkern g1="ft" u2="w" k="-15" />
    <hkern g1="ft" u2="t" k="-4" />
    <hkern g1="ft" u2="&#x3f;" k="10" />
    <hkern g1="ft" u2="&#x2f;" k="-10" />
    <hkern u1="&#xf6;" u2="x" k="20" />
    <hkern u1="&#xf6;" u2="t" k="12" />
    <hkern u1="&#xf6;" u2="j" k="10" />
    <hkern u1="&#xf6;" u2="&#x3f;" k="30" />
    <hkern u1="a" u2="t" k="5" />
    <hkern u1="a" u2="j" k="5" />
    <hkern u1="a" u2="&#x3f;" k="15" />
    <hkern u1="&#xe0;" u2="t" k="5" />
    <hkern u1="&#xe0;" u2="j" k="5" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe1;" u2="t" k="5" />
    <hkern u1="&#xe1;" u2="j" k="5" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe2;" u2="t" k="5" />
    <hkern u1="&#xe2;" u2="j" k="5" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe3;" u2="t" k="5" />
    <hkern u1="&#xe3;" u2="j" k="5" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe4;" u2="t" k="5" />
    <hkern u1="&#xe4;" u2="j" k="5" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="15" />
    <hkern u1="&#xe5;" u2="t" k="5" />
    <hkern u1="&#xe5;" u2="j" k="5" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="15" />
    <hkern g1="a.alt" u2="t" k="5" />
    <hkern g1="a.alt" u2="&#x3f;" k="20" />
    <hkern g1="a.alt" u2="&#x26;" k="-10" />
    <hkern u1="&#x153;" u2="w" k="-4" />
    <hkern u1="&#xed;" u2="&#x17d;" k="-30" />
    <hkern u1="&#xed;" u2="&#xdd;" k="-50" />
    <hkern u1="&#xed;" u2="&#x178;" k="-50" />
    <hkern u1="&#xed;" u2="Z" k="-30" />
    <hkern u1="&#xed;" u2="Y" k="-50" />
    <hkern u1="&#xed;" u2="&#x27;" k="-20" />
    <hkern u1="&#xed;" u2="&#x22;" k="-20" />
    <hkern u1="&#xed;" u2="&#xef;" k="-30" />
    <hkern u1="&#xed;" u2="&#xee;" k="-20" />
    <hkern u1="&#xed;" u2="&#xec;" k="-30" />
    <hkern u1="&#xed;" u2="&#x201d;" k="-30" />
    <hkern u1="&#xed;" u2="&#x201c;" k="-30" />
    <hkern u1="&#xed;" u2="t" k="5" />
    <hkern u1="&#xed;" u2="j" k="-5" />
    <hkern u1="&#xed;" u2="i" k="-20" />
    <hkern u1="&#xed;" u2="W" k="-30" />
    <hkern u1="&#xed;" u2="V" k="-50" />
    <hkern u1="&#xed;" u2="T" k="-50" />
    <hkern u1="&#xed;" u2="&#x3f;" k="15" />
    <hkern u1="&#xec;" u2="&#xef;" k="-30" />
    <hkern u1="&#xec;" u2="&#xee;" k="-20" />
    <hkern u1="&#xec;" u2="&#xec;" k="-30" />
    <hkern u1="&#xec;" u2="t" k="5" />
    <hkern u1="&#xec;" u2="j" k="5" />
    <hkern u1="&#xec;" u2="&#x3f;" k="15" />
    <hkern u1="&#xee;" u2="&#xed;" k="-10" />
    <hkern u1="&#xee;" g2="ft" k="-15" />
    <hkern u1="&#xee;" g2="fj" k="-15" />
    <hkern u1="&#xee;" g2="ffl" k="-15" />
    <hkern u1="&#xee;" g2="ffi" k="-15" />
    <hkern u1="&#xee;" g2="ff" k="-15" />
    <hkern u1="&#xee;" u2="&#x160;" k="-30" />
    <hkern u1="&#xee;" u2="&#x17d;" k="-35" />
    <hkern u1="&#xee;" g2="fl" k="-15" />
    <hkern u1="&#xee;" g2="fi" k="-15" />
    <hkern u1="&#xee;" u2="&#xdd;" k="-30" />
    <hkern u1="&#xee;" u2="&#x178;" k="-30" />
    <hkern u1="&#xee;" u2="k" k="-10" />
    <hkern u1="&#xee;" u2="h" k="-10" />
    <hkern u1="&#xee;" u2="f" k="-15" />
    <hkern u1="&#xee;" u2="b" k="-10" />
    <hkern u1="&#xee;" u2="Z" k="-35" />
    <hkern u1="&#xee;" u2="Y" k="-30" />
    <hkern u1="&#xee;" u2="S" k="-30" />
    <hkern u1="&#xee;" u2="&#x27;" k="-30" />
    <hkern u1="&#xee;" u2="&#x22;" k="-30" />
    <hkern u1="&#xee;" u2="&#xef;" k="-30" />
    <hkern u1="&#xee;" u2="&#xee;" k="-20" />
    <hkern u1="&#xee;" u2="&#xec;" k="-30" />
    <hkern u1="&#xee;" u2="&#x201d;" k="-40" />
    <hkern u1="&#xee;" u2="&#x201c;" k="-40" />
    <hkern u1="&#xee;" u2="t" k="5" />
    <hkern u1="&#xee;" u2="j" k="5" />
    <hkern u1="&#xee;" u2="i" k="-30" />
    <hkern u1="&#xee;" u2="W" k="-40" />
    <hkern u1="&#xee;" u2="V" k="-50" />
    <hkern u1="&#xee;" u2="T" k="-30" />
    <hkern u1="&#xee;" u2="&#x3f;" k="15" />
    <hkern u1="&#xef;" u2="&#xed;" k="-12" />
    <hkern u1="&#xef;" u2="&#x160;" k="-30" />
    <hkern u1="&#xef;" u2="&#x17d;" k="-40" />
    <hkern u1="&#xef;" u2="&#xdd;" k="-50" />
    <hkern u1="&#xef;" u2="&#x178;" k="-50" />
    <hkern u1="&#xef;" u2="k" k="-12" />
    <hkern u1="&#xef;" u2="h" k="-12" />
    <hkern u1="&#xef;" u2="b" k="-12" />
    <hkern u1="&#xef;" u2="Z" k="-40" />
    <hkern u1="&#xef;" u2="Y" k="-50" />
    <hkern u1="&#xef;" u2="S" k="-30" />
    <hkern u1="&#xef;" u2="&#x27;" k="-30" />
    <hkern u1="&#xef;" u2="&#x22;" k="-30" />
    <hkern u1="&#xef;" u2="&#xef;" k="-30" />
    <hkern u1="&#xef;" u2="&#xee;" k="-20" />
    <hkern u1="&#xef;" u2="&#xec;" k="-30" />
    <hkern u1="&#xef;" u2="&#x201d;" k="-40" />
    <hkern u1="&#xef;" u2="&#x201c;" k="-40" />
    <hkern u1="&#xef;" u2="t" k="5" />
    <hkern u1="&#xef;" u2="j" k="5" />
    <hkern u1="&#xef;" u2="i" k="-30" />
    <hkern u1="&#xef;" u2="X" k="-15" />
    <hkern u1="&#xef;" u2="W" k="-50" />
    <hkern u1="&#xef;" u2="V" k="-60" />
    <hkern u1="&#xef;" u2="T" k="-60" />
    <hkern u1="&#xef;" u2="&#x3f;" k="15" />
    <hkern g1="aacute.alt" u2="t" k="5" />
    <hkern g1="aacute.alt" u2="&#x3f;" k="20" />
    <hkern g1="aacute.alt" u2="&#x26;" k="-10" />
    <hkern g1="acircumflex.alt" u2="t" k="5" />
    <hkern g1="acircumflex.alt" u2="&#x3f;" k="20" />
    <hkern g1="acircumflex.alt" u2="&#x26;" k="-10" />
    <hkern g1="agrave.alt" u2="t" k="5" />
    <hkern g1="agrave.alt" u2="&#x3f;" k="20" />
    <hkern g1="agrave.alt" u2="&#x26;" k="-10" />
    <hkern g1="aring.alt" u2="t" k="5" />
    <hkern g1="aring.alt" u2="&#x3f;" k="20" />
    <hkern g1="aring.alt" u2="&#x26;" k="-10" />
    <hkern g1="atilde.alt" u2="t" k="5" />
    <hkern g1="atilde.alt" u2="&#x3f;" k="20" />
    <hkern g1="atilde.alt" u2="&#x26;" k="-10" />
    <hkern g1="zero.numr" u2="&#x2044;" k="10" />
    <hkern g1="two.numr" u2="&#x2044;" k="5" />
    <hkern g1="three.numr" u2="&#x2044;" k="30" />
    <hkern g1="four.numr" u2="&#x2044;" k="10" />
    <hkern g1="five.numr" u2="&#x2044;" k="20" />
    <hkern g1="six.numr" u2="&#x2044;" k="15" />
    <hkern g1="seven.numr" u2="&#x2044;" k="70" />
    <hkern g1="eight.numr" u2="&#x2044;" k="20" />
    <hkern g1="nine.numr" u2="&#x2044;" k="35" />
    <hkern g1="C,Ccedilla"
	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	k="-5" />
    <hkern g1="C,Ccedilla"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="-9" />
    <hkern g1="C,Ccedilla"
	g2="S,Scaron"
	k="-25" />
    <hkern g1="C,Ccedilla"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="-12" />
    <hkern g1="C,Ccedilla"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-12" />
    <hkern g1="C,Ccedilla"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="C,Ccedilla"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="Y,Ydieresis,Yacute"
	k="-9" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="Z,Zcaron"
	k="-12" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="S,Scaron"
	k="-13" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="-5" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="5" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="v,y,ydieresis,yacute"
	k="12" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="z,zcaron"
	k="-8" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="5" />
    <hkern g1="E,OE,Ecircumflex,Edieresis,Egrave,Eacute"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="5" />
    <hkern g1="H,I,M,N,Iacute,Icircumflex,Idieresis,Igrave,Ntilde"
	g2="Y,Ydieresis,Yacute"
	k="10" />
    <hkern g1="H,I,M,N,Iacute,Icircumflex,Idieresis,Igrave,Ntilde"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="5" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	k="-10" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="-26" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="Y,Ydieresis,Yacute"
	k="30" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="Z,Zcaron"
	k="10" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="S,Scaron"
	k="-12" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="8" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="s,scaron"
	k="-20" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="-14" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="v,y,ydieresis,yacute"
	k="-18" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-5" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-28" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="-18" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="b,h,i,k,iacute,igrave,icircumflex,idieresis"
	k="-8" />
    <hkern g1="D,O,Q,Odieresis,Otilde,Oacute,Ocircumflex,Ograve,Eth"
	g2="m,n,p,r,dotlessi,ntilde"
	k="-10" />
    <hkern g1="S,Scaron"
	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	k="-4" />
    <hkern g1="S,Scaron"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="-20" />
    <hkern g1="S,Scaron"
	g2="Y,Ydieresis,Yacute"
	k="15" />
    <hkern g1="S,Scaron"
	g2="Z,Zcaron"
	k="-15" />
    <hkern g1="S,Scaron"
	g2="S,Scaron"
	k="-20" />
    <hkern g1="S,Scaron"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="-11" />
    <hkern g1="S,Scaron"
	g2="s,scaron"
	k="-28" />
    <hkern g1="S,Scaron"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="-10" />
    <hkern g1="S,Scaron"
	g2="v,y,ydieresis,yacute"
	k="15" />
    <hkern g1="S,Scaron"
	g2="z,zcaron"
	k="-8" />
    <hkern g1="S,Scaron"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="S,Scaron"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-12" />
    <hkern g1="S,Scaron"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-25" />
    <hkern g1="S,Scaron"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="S,Scaron"
	g2="b,h,i,k,iacute,igrave,icircumflex,idieresis"
	k="-5" />
    <hkern g1="S,Scaron"
	g2="m,n,p,r,dotlessi,ntilde"
	k="-5" />
    <hkern g1="S,Scaron"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="-10" />
    <hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	g2="Z,Zcaron"
	k="6" />
    <hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	g2="S,Scaron"
	k="-4" />
    <hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="33" />
    <hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-4" />
    <hkern g1="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="15" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="30" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="Y,Ydieresis,Yacute"
	k="-12" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="S,Scaron"
	k="14" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="80" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Ecircumflex,Edieresis,Egrave,Iacute,Icircumflex,Idieresis,Igrave,Eacute,Thorn,Ntilde"
	k="10" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="s,scaron"
	k="70" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="70" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="v,y,ydieresis,yacute"
	k="35" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="z,zcaron"
	k="44" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="hyphen,endash,emdash"
	k="50" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="65" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="80" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="28" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="b,h,i,k,iacute,igrave,icircumflex,idieresis"
	k="18" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="m,n,p,r,dotlessi,ntilde"
	k="80" />
    <hkern g1="Y,Ydieresis,Yacute"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="Z,Zcaron"
	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	k="5" />
    <hkern g1="Z,Zcaron"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="22" />
    <hkern g1="Z,Zcaron"
	g2="Z,Zcaron"
	k="-30" />
    <hkern g1="Z,Zcaron"
	g2="S,Scaron"
	k="-20" />
    <hkern g1="Z,Zcaron"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="-16" />
    <hkern g1="Z,Zcaron"
	g2="s,scaron"
	k="-13" />
    <hkern g1="Z,Zcaron"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="25" />
    <hkern g1="Z,Zcaron"
	g2="v,y,ydieresis,yacute"
	k="20" />
    <hkern g1="Z,Zcaron"
	g2="z,zcaron"
	k="-15" />
    <hkern g1="Z,Zcaron"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="Z,Zcaron"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-4" />
    <hkern g1="Z,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="17" />
    <hkern g1="Z,Zcaron"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="18" />
    <hkern g1="Z,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="d,g,i,j,q,u,dotlessi,uacute,ugrave,ucircumflex,udieresis,mu,a,agrave,aacute,acircumflex,atilde,adieresis,aring,iacute,igrave,icircumflex,idieresis"
	g2="z,zcaron"
	k="4" />
    <hkern g1="d,g,i,j,q,u,dotlessi,uacute,ugrave,ucircumflex,udieresis,mu,a,agrave,aacute,acircumflex,atilde,adieresis,aring,iacute,igrave,icircumflex,idieresis"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="5" />
    <hkern g1="c,ccedilla"
	g2="s,scaron"
	k="-18" />
    <hkern g1="c,ccedilla"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-5" />
    <hkern g1="c,ccedilla"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-4" />
    <hkern g1="e,eacute,egrave,edieresis,ecircumflex,ae,oe"
	g2="s,scaron"
	k="-18" />
    <hkern g1="e,eacute,egrave,edieresis,ecircumflex,ae,oe"
	g2="v,y,ydieresis,yacute"
	k="-4" />
    <hkern g1="e,eacute,egrave,edieresis,ecircumflex,ae,oe"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="e,eacute,egrave,edieresis,ecircumflex,ae,oe"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-18" />
    <hkern g1="e,eacute,egrave,edieresis,ecircumflex,ae,oe"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-22" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="s,scaron"
	k="-6" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="-4" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="v,y,ydieresis,yacute"
	k="4" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="quotedblleft,quoteleft"
	k="15" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="quotedblright,quoteright"
	k="14" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-5" />
    <hkern g1="h,m,n,ntilde,a.alt,aacute.alt,acircumflex.alt,agrave.alt,aring.alt,atilde.alt"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-4" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="v,y,ydieresis,yacute"
	k="5" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="z,zcaron"
	k="10" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="quotedblleft,quoteleft"
	k="30" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-14" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-12" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="b,o,p,otilde,ocircumflex,ograve,oacute,thorn,odieresis"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="10" />
    <hkern g1="s,scaron"
	g2="s,scaron"
	k="-10" />
    <hkern g1="s,scaron"
	g2="quotedbl,quotesingle"
	k="15" />
    <hkern g1="s,scaron"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-18" />
    <hkern g1="s,scaron"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="-10" />
    <hkern g1="v,y,ydieresis,yacute"
	g2="s,scaron"
	k="-10" />
    <hkern g1="v,y,ydieresis,yacute"
	g2="v,y,ydieresis,yacute"
	k="-25" />
    <hkern g1="v,y,ydieresis,yacute"
	g2="quotedblleft,quoteleft"
	k="-20" />
    <hkern g1="v,y,ydieresis,yacute"
	g2="quotedblright,quoteright"
	k="-20" />
    <hkern g1="v,y,ydieresis,yacute"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="5" />
    <hkern g1="v,y,ydieresis,yacute"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="v,y,ydieresis,yacute"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="-18" />
    <hkern g1="z,zcaron"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="10" />
    <hkern g1="z,zcaron"
	g2="z,zcaron"
	k="-8" />
    <hkern g1="z,zcaron"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="z,zcaron"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="z,zcaron"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="28" />
    <hkern g1="z,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="f,ff"
	g2="s,scaron"
	k="15" />
    <hkern g1="f,ff"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="15" />
    <hkern g1="f,ff"
	g2="v,y,ydieresis,yacute"
	k="-10" />
    <hkern g1="f,ff"
	g2="z,zcaron"
	k="5" />
    <hkern g1="f,ff"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="f,ff"
	g2="quotedbl,quotesingle"
	k="-15" />
    <hkern g1="f,ff"
	g2="quotedblleft,quoteleft"
	k="-20" />
    <hkern g1="f,ff"
	g2="quotedblright,quoteright"
	k="-25" />
    <hkern g1="f,ff"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="26" />
    <hkern g1="f,ff"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="50" />
    <hkern g1="f,ff"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="35" />
    <hkern g1="f,ff"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="5" />
    <hkern g1="f,ff"
	g2="m,n,p,r,dotlessi,ntilde"
	k="20" />
    <hkern g1="f,ff"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="l,fl,ffl"
	g2="s,scaron"
	k="-10" />
    <hkern g1="l,fl,ffl"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="8" />
    <hkern g1="l,fl,ffl"
	g2="v,y,ydieresis,yacute"
	k="17" />
    <hkern g1="l,fl,ffl"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="l,fl,ffl"
	g2="quotedbl,quotesingle"
	k="15" />
    <hkern g1="l,fl,ffl"
	g2="quotedblleft,quoteleft"
	k="30" />
    <hkern g1="l,fl,ffl"
	g2="quotedblright,quoteright"
	k="20" />
    <hkern g1="l,fl,ffl"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="8" />
    <hkern g1="l,fl,ffl"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="-10" />
    <hkern g1="l,fl,ffl"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="4" />
    <hkern g1="l,fl,ffl"
	g2="m,n,p,r,dotlessi,ntilde"
	k="5" />
    <hkern g1="l,fl,ffl"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="t,ft"
	g2="s,scaron"
	k="-6" />
    <hkern g1="t,ft"
	g2="v,y,ydieresis,yacute"
	k="-17" />
    <hkern g1="t,ft"
	g2="z,zcaron"
	k="-12" />
    <hkern g1="t,ft"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="t,ft"
	g2="a.alt,aacute.alt,acircumflex.alt,adieresis.alt,agrave.alt,aring.alt,atilde.alt"
	k="-8" />
    <hkern g1="t,ft"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="18" />
    <hkern g1="t,ft"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="-15" />
    <hkern g1="t,ft"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="-9" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="U,Udieresis,Uacute,Ucircumflex,Ugrave"
	k="15" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="20" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="Y,Ydieresis,Yacute"
	k="70" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="u,uacute,ugrave,ucircumflex,udieresis"
	k="20" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="v,y,ydieresis,yacute"
	k="30" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="50" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="quotedblleft,quoteleft"
	k="40" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="quotedblright,quoteright"
	k="40" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="10" />
    <hkern g1="comma,period,ellipsis,quotesinglbase,quotedblbase"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Ydieresis,Yacute"
	k="50" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zcaron"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="S,Scaron"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zcaron"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="S,Scaron"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Ydieresis,Yacute"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Z,Zcaron"
	k="15" />
    <hkern g1="guillemotright,guilsinglright"
	g2="z,zcaron"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,fi,fl,ff,ffi,ffl,fj,ft"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="40" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,scaron"
	k="15" />
    <hkern g1="quotedbl,quotesingle"
	g2="z,zcaron"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="quotedblleft,quoteleft"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="40" />
    <hkern g1="quotedblleft,quoteleft"
	g2="s,scaron"
	k="15" />
    <hkern g1="quotedblleft,quoteleft"
	g2="v,y,ydieresis,yacute"
	k="-10" />
    <hkern g1="quotedblleft,quoteleft"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="quotedblright,quoteright"
	g2="C,G,O,Q,Odieresis,Otilde,OE,Oacute,Ocircumflex,Ograve,Ccedilla"
	k="20" />
    <hkern g1="quotedblright,quoteright"
	g2="S,Scaron"
	k="20" />
    <hkern g1="quotedblright,quoteright"
	g2="A,Adieresis,Aring,Agrave,Atilde,Acircumflex,Aacute,AE"
	k="40" />
    <hkern g1="quotedblright,quoteright"
	g2="s,scaron"
	k="52" />
    <hkern g1="quotedblright,quoteright"
	g2="z,zcaron"
	k="20" />
    <hkern g1="quotedblright,quoteright"
	g2="c,d,e,g,o,q,ccedilla,eacute,egrave,otilde,ocircumflex,ograve,oacute,edieresis,ecircumflex,ae,odieresis,a,agrave,aacute,acircumflex,atilde,adieresis,aring,oe"
	k="55" />
    <hkern g1="quotedblright,quoteright"
	g2="comma,period,ellipsis,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="quotedblright,quoteright"
	g2="m,n,p,r,dotlessi,ntilde"
	k="10" />
  </font>
</defs></svg>
