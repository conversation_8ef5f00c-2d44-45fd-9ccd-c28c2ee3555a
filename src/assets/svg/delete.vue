<template>
  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.6975 19.489C16.3711 19.489 16.9172 18.9429 16.9172 18.2693H11.0827C11.0827 18.9429 11.6288 19.489 12.3025 19.489H15.6975Z" :fill="color" />
    <path d="M11.2058 5.66504C10.8931 5.66504 10.6067 5.84009 10.464 6.11839L9.44613 8.10455H5.66498C5.66498 8.7782 6.21108 9.32431 6.88473 9.32431H7.59622L8.77403 21.5813C8.81511 22.0087 9.17427 22.335 9.60371 22.335C10.0332 22.335 18.3962 22.335 18.3962 22.335C18.8257 22.335 19.1848 22.0087 19.2259 21.5813L20.4037 9.32431H21.1152C21.7889 9.32431 22.335 8.7782 22.335 8.10455H18.5538L17.5359 6.11839C17.3933 5.84009 17.1069 5.66504 16.7942 5.66504H11.2058ZM17.156 8.10455H10.844L11.4691 6.8848H16.5309L17.156 8.10455ZM9.98499 21.1153L8.85195 9.32431H19.148L18.015 21.1153H9.98499Z" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
defineProps({
  color: {
    type: String,
    default: '#01A0AC'
  }
})
</script>
