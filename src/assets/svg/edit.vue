<template>
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.6667 14C14.219 14 14.6667 13.5523 14.6667 13H1.33337C1.33337 13.5523 1.78109 14 2.33337 14H13.6667Z" :fill="color" />
    <path d="M4 9.18944V11.4813H6.30356L12.8148 4.9672L10.5151 2.6665L4 9.18944Z" :stroke="color" stroke-linejoin="round" />
  </svg>
</template>

<script setup lang="ts">
defineProps({
  color: {
    type: String,
    default: '#01A0AC'
  }
})
</script>