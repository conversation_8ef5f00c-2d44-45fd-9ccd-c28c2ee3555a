<template>
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.49992 3.99992C8.0522 3.99992 8.49992 4.44763 8.49992 4.99992V7.58438L10.5299 9.72992C10.9098 10.1388 10.8946 10.7753 10.4999 11.1699L7.90647 8.43239C7.64611 8.15453 7.50117 7.78804 7.50105 7.40726L7.49992 3.99992Z" :fill="color" />
    <path d="M14.6666 7.99992C14.6666 11.6818 11.6818 14.6666 7.99992 14.6666C4.31802 14.6666 1.33325 11.6818 1.33325 7.99992C1.33325 4.31802 4.31802 1.33325 7.99992 1.33325C11.6818 1.33325 14.6666 4.31802 14.6666 7.99992ZM13.6666 7.99992C13.6666 4.8703 11.1295 2.33325 7.99992 2.33325C4.8703 2.33325 2.33325 4.8703 2.33325 7.99992C2.33325 11.1295 4.8703 13.6666 7.99992 13.6666C11.1295 13.6666 13.6666 11.1295 13.6666 7.99992Z" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
defineProps({
  color: {
    type: String,
    default: '#BFBFBF'
  }
})
</script>