<template>
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11 1V2.50016H5.00004V2C5.00004 1.44778 4.55226 1.00009 4.00004 1V2.50016L1.83335 2.50026C1.55722 2.50027 1.33337 2.72412 1.33337 3.00026V13.5002C1.33337 13.7654 1.56816 14.0002 1.83337 14.0002H14.1667C14.4319 14.0002 14.6667 13.7654 14.6667 13.5002V3.00016C14.6667 2.73495 14.4319 2.50016 14.1667 2.50016H12V2C12 1.44778 11.5523 1.00009 11 1ZM11 5.00016C11.5523 5.00007 12 4.55238 12 4.00016V3.50016H13.6667V7.00016H2.33337V3.50016H4.00004V5.00017C4.55226 5.00007 5.00004 4.55239 5.00004 4.00017V3.50016H11V5.00016ZM2.33337 8.00016H13.6667V13.0002H2.33337V8.00016Z" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
defineProps({
  color: {
    type: String,
    default: '#01A0AC'
  }
})
</script>