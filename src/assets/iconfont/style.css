@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?3oiem5');
  src:  url('fonts/icomoon.eot?3oiem5#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?3oiem5') format('truetype'),
    url('fonts/icomoon.woff?3oiem5') format('woff'),
    url('fonts/icomoon.svg?3oiem5#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-wel-point:before {
  content: "\e90f";
}
.icon-wel-drive:before {
  content: "\e90c";
}
.icon-wel-time:before {
  content: "\e90d";
}
.icon-wel-system:before {
  content: "\e90e";
}
.icon-wel-history:before {
  content: "\e906";
}
.icon-wel-setting:before {
  content: "\e900";
}
.icon-wel-database:before {
  content: "\e901";
}
.icon-wel-resource:before {
  content: "\e902";
}
.icon-wel-algorithm:before {
  content: "\e903";
}
.icon-wel-model:before {
  content: "\e904";
}
.icon-wel-personal:before {
  content: "\e905";
}
.icon-wel-analysis:before {
  content: "\e907";
}
.icon-wel-list:before {
  content: "\e908";
}
.icon-wel-log:before {
  content: "\e909";
}
.icon-wel-ota:before {
  content: "\e90a";
}
.icon-wel-ticket:before {
  content: "\e90b";
}
