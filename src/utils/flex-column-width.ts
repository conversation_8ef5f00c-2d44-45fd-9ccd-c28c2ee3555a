/**
 * @description: 单元格宽度自适应
 * @param {*} columnData
 * @return {*}
 */
export const flexColumnWidth = (columnData: any, extraWidth: number, oneLetterWidth = 14) => {
  const map = new Map()
  columnData.map((item: any) => {
    if (item) map.set(item.length, item)
  })
  if ([...map].length > 0) {
    const sortMapArray = [...map].sort((a, b) => b[0] - a[0])
    const maxLengthStr = sortMapArray[0][1]
    const flexWidth = maxLengthStr.length * oneLetterWidth + extraWidth
    return flexWidth + 'px'
  } else {
    return extraWidth + 'px'
  }
}
