import { ElMessage } from 'element-plus';

export const downloadResponseFile = (res: any, url = '') => {
  console.log('downloadResponseFile', res);
  if (url.length > 0) {
    window.open(url);
    return true;
  }

  const headers = res.headers;

  let filename = headers['content-disposition'].match(
    /filename\*=utf-8''(.*)/
  )?.[1];

  if (!filename) {
    ElMessage.error('下载文件的名称传输有误，无法下载');
    return false;
  }

  filename = decodeURIComponent(filename);
  // console.log("filename:",filename);

  const blob = new Blob([res.data], { type: headers['content-type'] });
  console.log('blob', blob);

  let alink = document.createElement('a'); // 创建一个<a>标签
  alink.style.display = 'none'; // 隐藏标签

  alink.href = window.URL.createObjectURL(blob); // 配置href，指向本地文件的内存地址
  alink.download = filename;
  alink.click();
  alink.remove();
  URL.revokeObjectURL(alink.href); // 释放URL 对象
  // document.body.removeChild(alink); // 移除<a>标签
  return true;
};
