/*
 * @Author: liu.yang
 * @Email: <EMAIL>
 * @Date: 2023-04-20 11:05:52
 * @LastEditors: zhenxing.chen <EMAIL>
 * @LastEditTime: 2024-07-25 16:34:34
 * @Description:
 */
export const formatTime = (timestamp: number, timeLen = 13) => {
  if (!timestamp) {
    return '-'
  }
  const timeDate = new Date(timeLen == 13 ? timestamp : timestamp * 1000)
  const y = timeDate.getFullYear()
  const m = timeDate.getMonth() + 1
  const d = timeDate.getDate()
  const x = y + '/' + (m < 10 ? '0' + m : m) + '/' + (d < 10 ? '0' + d : d) + ' ' + timeDate.toTimeString().substring(0, 8)
  return x
}

export const formatTimeToDay = (timestamp: any) => {
  if (!timestamp) {
    return '-'
  }
  const timeDate = new Date(timestamp)
  const y = timeDate.getFullYear()
  const m = timeDate.getMonth() + 1
  const d = timeDate.getDate()
  const x = y + '/' + (m < 10 ? '0' + m : m) + '/' + (d < 10 ? '0' + d : d)
  return x
}

export const formatTimeToDayWithDash = (timestamp: number) => {
  if (!timestamp) {
    return '-'
  }
  const timeDate = new Date(timestamp)
  const y = timeDate.getFullYear()
  const m = timeDate.getMonth() + 1
  const d = timeDate.getDate()
  const x = y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d)
  return x
}

export const setSecondsToZero = (timestamp: any) => {
  var date = new Date(timestamp)
  date.setSeconds(0)
  date.setMilliseconds(0)
  return date.getTime()
}

export const setSecondsToFiftyNine = (timestamp: any) => {
  var date = new Date(timestamp)
  date.setSeconds(59)
  date.setMilliseconds(999)
  return date.getTime()
}

export const getStartTimeOfDay = (timestamp: any) => {
  const date = new Date(timestamp)
  date.setHours(0, 0, 0, 0)
  return date.getTime()
}

export const getFinalEndTime = (timestamp: any) => {
  const date = new Date(timestamp)
  date.setHours(23, 59, 59, 999)
  return date.getTime()
}

export const formatTimestamp = (timestamp: number) => {
  // 将时间戳转换为Date对象
  let date = new Date(timestamp)
  // 获取年、月、日、时、分、秒
  let year = date.getFullYear() // 年
  let month: any = date.getMonth() + 1 // 月，月份从0开始计数，所以需要+1
  let day: any = date.getDate() // 日
  let hours: any = date.getHours() // 时
  let minutes: any = date.getMinutes() // 分
  let seconds: any = date.getSeconds() // 秒

  // 格式化单个数字为两位数
  month = month < 10 ? '0' + month : month
  day = day < 10 ? '0' + day : day
  hours = hours < 10 ? '0' + hours : hours
  minutes = minutes < 10 ? '0' + minutes : minutes
  seconds = seconds < 10 ? '0' + seconds : seconds

  // 组合成“年月日，时分秒”格式
  var formattedTime = year + '/' + month + '/' + day + '  ' + hours + ':' + minutes + ':' + seconds
  return formattedTime
}

export const msToTime = (ms: number) => {
  let seconds = ms / 1000
  let minutes = ms / (1000 * 60)
  let hours = ms / (1000 * 60 * 60)
  if (seconds < 60) {
    return Number.isInteger(seconds * 10) ? [seconds, '秒'] : [seconds.toFixed(1), '秒']
  } else if (minutes < 60) {
    return Number.isInteger(minutes * 10) ? [minutes, '分钟'] : [minutes.toFixed(1), '分钟']
  } else {
    return Number.isInteger(hours * 10) ? [hours, '小时'] : [hours.toFixed(1), '小时']
  }
}

/**
 * @description: 时间戳转换成 时：分：秒 形式
 * @param {number} timestamp
 * @param {*} timeLen
 * @return {*}
 */
export const formatTimeWithoutDate = (timestamp: number, timeLen = 13) => {
  if (!timestamp) return '-'
  const timeDate = new Date(timeLen == 13 ? timestamp : timestamp * 1000)
  return timeDate.toTimeString().substring(0, 8)
}
