export default function formatLocaleDate(
  value: string | number,
  miliseconds?: false,
  type?: string
) {
  if (!value) {
    return '-';
  }
  if (typeof value === 'number') {
    if (type === 'date') {
      return new Date(value).toLocaleDateString();
    }
    const date = new Date(value);
    // ‘zh-CN’ 表示中式时间表达， 使用年/月/日 顺序和 24 小时制，没有 AM/PM
    // 不代表时区
    const localeStr = date.toLocaleString('zh-CN', { hour12: false });
    if (miliseconds) {
      return localeStr + `.${date.getMilliseconds()}`;
    } else {
      return localeStr;
    }
  } else {
    return '-';
  }
}
