export const addZero = (data: number) => {
  return data < 10 ? '0' + data : data
}
export const timestampToStr = (timestamp: number) => {
  const time = new Date(timestamp)
  const year = time.getFullYear()
  const month = time.getMonth() + 1
  const day = time.getDate()
  const hour = time.getHours()
  const minute = time.getMinutes()
  const second = time.getSeconds()
  return year + '-' + addZero(month) + '-' + addZero(day) + '-' + addZero(hour) + ':' + addZero(minute) + ':' + addZero(second)
}