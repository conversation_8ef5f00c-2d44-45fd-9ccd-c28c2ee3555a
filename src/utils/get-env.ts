// 判断环境， 主要是venus上的部署方式导致需要此函数。 希望此后能通过配置文件以及部署的方式上解决环境问题
export const getEnv = (): string | undefined => {
  const hostName = window.location.host;
  // const hostName = 'pp-redrabbit-test.nioint.com'
  const envArr = hostName.split('-');
  const envString = envArr[envArr.length - 1];
  // if (envString.split('.')[0] === 'nioint') {
  //   return '';
  // }
  // if (location.host.indexOf('localhost') > -1) {
  //   return `-eu`;
  // } else {
  //   return `${envString.split('.')[0]}`;
  // }
  if (envString.split('.')[0] === 'dev') {
    // 国内开发环境
    return '-dev';
  } else if (envString.split('.')[0] === 'test') {
    // 国内测试环境
    return '-test';
  } else if (envString.split('.')[0] === 'stg') {
    // 国内预生产环境
    return '-stg';
  } else if (envString.split('.')[0] === 'welkin') {
    // 国内生产环境
    return '';
  } else if (envString.split('.')[0] === 'eu' && envArr.includes('stg')) {
    // 欧洲预生产环境
    return '-stg-eu';
  } else if (envString.split('.')[0] === 'eu') {
    // 欧洲生产环境
    return '-eu';
  }
};

export const getEnvName = (): string | undefined => {
  let env = getEnv();
  if (env == '') {
    return 'prod';
  } else if (env?.substring(0, 1) == '-') {
    return env.substring(1);
  }
};

export const isEu = () => {
  if(getEnv() == '-stg-eu' || getEnv() == '-eu') {
    return true
  } else {
    return false
  }
}
