/*
 * @Author: liu.yang
 * @Email: <EMAIL>
 * @Date: 2023-04-23 16:57:33
 * @LastEditors: liu.yang
 * @LastEditTime: 2023-04-23 17:01:10
 * @Description:
 */
const time: number = new Date().getTime() - 24 * 60 * 60 * 1000;
const date = new Date(time);

// 获取当前月份
let nowMonth: string | number = date.getMonth() + 1;

// 获取当前是几号
let strDate: string | number = date.getDate();

// 添加分隔符“-”
const seperator = '/';

// 对月份进行处理，1-9月在前面添加一个“0”
if (nowMonth >= 1 && nowMonth <= 9) {
  nowMonth = '0' + nowMonth;
}

// 对月份进行处理，1-9号在前面添加一个“0”
if (strDate >= 0 && strDate <= 9) {
  strDate = '0' + strDate;
}

// 最后拼接字符串，得到一个格式为(yyyy-MM-dd)的日期
export const yesterdayDate = date.getFullYear() + seperator + nowMonth + seperator + strDate;
