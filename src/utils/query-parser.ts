import type { ParseConfig } from '~/types/common/query-parser'

export function parseQueryParams(queryParams: any, config: ParseConfig) {
  const { params, target } = config

  params.forEach((param) => {
    const { key, type, arrayType = 'string', defaultValue, separator = ',' } = param
    const value = queryParams[key]

    switch (type) {
      case 'number':
        target[key] = !isNaN(value) ? Number(value) : defaultValue
        break

      case 'string':
        target[key] = value || defaultValue
        break

      case 'array':
        if (!value) {
          target[key] = defaultValue || []
        } else {
          const arr = value.split(separator)
          target[key] = arrayType === 'number' ? arr.map(Number) : arr
        }
        break

      case 'boolean':
        target[key] = value === 'true' ? true : defaultValue ?? false
        break
    }
  })
}
