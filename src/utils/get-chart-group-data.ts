import formatLocaleDate from './format-locale-date'
import { chartTitle } from '~/constvars/plc-record/chartTitle'
import { i18n } from '~/i18n'

/*
把后端返回值处理为EChart能接收的值

  res_data:后端res.data返回值
  axis:所选轴号的数组
  axis_map:轴号映射表
  keys:后端res.data返回值中，每个Object除了timestamp之外的key值数组。有几个，也就画几张图
  tab:哪个类（当前与页面的tab值
*/
export const getChartGroupData = (res_data: any, axis: any[], axis_map: any, keys: string[], tab: string, i18_str: string = '', step: boolean = false) => {
  const tempMap = new Map()
  const tempArr = []
  const legend: any[] = []
  const time: any[] = []
  const list = {}
  const data = {}
  // 初始化数据
  for (let i = 0; i < keys?.length; i++) {
    data[keys[i]] = {
      key: keys[i],
      title: i18n.global.t(`plcRecord.chartTitle.${tab}.${keys[i]}`),
      data: []
    }
  }

  //遍历已选轴号
  axis.map((item: any) => {
    for (let i = 0; i < keys?.length; i++) {
      list[keys[i]] = []
    }
    if (res_data.hasOwnProperty(item)) {
      const arrLength = res_data[item].length
      tempMap.set(arrLength, item)
      // 遍历后端数据
      res_data[item].map((dataItem: any) => {
        // 若key是需要画图的值key，加入list
        for (let key in dataItem) {
          if (keys.includes(key)) {
            list[key].push(dataItem[key])
          }
        }
      })

      let name = axis_map[item]
      if (!!i18_str) {
        name = i18n.global.t(`${i18_str}.${item}`)
      }
      let obj: any = {
        type: 'line',
        name: name,
        symbol: arrLength == 1 ? 'circle' : 'none',
        lineStyle: {
          width: 1.5
        }
      }
      //设置指定折线的显示样式 阶梯图
      if (step) {
        obj['step'] = 'end'
      }

      legend.push(name)

      // 加入data，生成echart可以接受的数据类型
      for (let key in list) {
        data[key].data.push({ ...obj, data: list[key] })
      }
    }
  })
  // 取最长时间
  for (let [key, val] of tempMap) {
    tempArr.push(key)
  }
  tempArr.sort((a: any, b: any) => b - a)
  const maxLengthItem = tempMap.get(tempArr[0])
  res_data[maxLengthItem].map((dataItem: any) => {
    time.push(formatLocaleDate(dataItem.timestamp, false))
  })

  return {
    legend: legend,
    time: time,
    group: Object.values(data)
  }
}
