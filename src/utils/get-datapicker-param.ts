import { i18n } from '~/i18n'

export const getDisabledDate = (time: Record<string, any>) => {
  if (time.getTime() > new Date().getTime()) {
    return true
  }
}

export const getDisabledHours = () => {
  const disabledHoursArr = []
  for (let i = 0; i < 24; i++) {
    if (new Date().getHours() < i) {
      disabledHoursArr.push(i)
    }
  }
  return disabledHoursArr
}

export const getDisabledMinutes = () => {
  const disabledMinutesArr = []
  for (let i = 0; i < 60; i++) {
    if (new Date().getMinutes() < i) {
      disabledMinutesArr.push(i)
    }
  }
  return disabledMinutesArr
}

export const getDisabledSeconds = () => {
  const disabledSecondsArr = []
  for (let i = 0; i < 60; i++) {
    if (new Date().getSeconds() < i) {
      disabledSecondsArr.push(i)
    }
  }
  return disabledSecondsArr
}

export const getShortcuts = () => {
  const shortcuts = [
    {
      text: i18n.global.t('common.pp_last_24_hours'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_3_month'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      }
    }
  ]
  return shortcuts
}

export const ShortOneWeekcuts = () => {
  const shortcuts = [
    {
      text: i18n.global.t('common.pp_last_24_hours'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    }
  ]
  return shortcuts
}

export const ShortTwoMonthcuts = () => {
  const shortcuts = [
    {
      text: i18n.global.t('common.pp_last_24_hours'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      }
    },
    {
      text: i18n.global.t('common.pp_last_2_month'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 61)
        return [start, end]
      }
    }
  ]
  return shortcuts
}

export const dateShortcuts = [
  {
    text: i18n.global.t('common.pp_yesterday'),
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return date
    }
  },
  {
    text: i18n.global.t('common.pp_week_ago'),
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      return date
    }
  }
]

export const shortcuts = [
  {
    text: i18n.global.t('common.pp_last_week'),
    value: () => [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 7, Date.now()]
  },
  {
    text: i18n.global.t('common.pp_last_month'),
    value: () => [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 30, Date.now()]
  },
  {
    text: i18n.global.t('common.pp_last_3_month'),
    value: () => [new Date(new Date().toLocaleDateString()).getTime() - 3600 * 1000 * 24 * 92, Date.now()]
  }
]

const now = new Date()
const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
today.setHours(0, 0, 0, 0)
export const initStartTime = today.getTime()
export const initEndTime = today.getTime() + 86399999
export const getTodayShortcuts = () => {
  return [
    {
      text: i18n.global.t('common.pp_last_week'),
      value: [initStartTime - 3600 * 1000 * 24 * 6, initEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: [initStartTime - 3600 * 1000 * 24 * 29, initEndTime]
    }
  ]
}
export const getDisabledTodayDate = (time: Record<string, any>) => time.getTime() > initEndTime

const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
yesterday.setHours(0, 0, 0, 0)
export const initYesterdayStartTime = yesterday.getTime()
export const initYesterdayEndTime = yesterday.getTime() + 86399999
export const getYesterdayShortcuts = () => {
  return [
    {
      text: i18n.global.t('common.pp_yesterday'),
      value: [initYesterdayStartTime, initYesterdayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: [initYesterdayStartTime - 3600 * 1000 * 24 * 6, initYesterdayEndTime]
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: [initYesterdayStartTime - 3600 * 1000 * 24 * 29, initYesterdayEndTime]
    }
  ]
}
export const getDisabledYesterdayDate = (time: Record<string, any>) => time.getTime() > initYesterdayEndTime