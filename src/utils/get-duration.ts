import { i18n } from '~/i18n'

export const getDuration = (start_time: number, end_time: number, chineseUnit = false) => {
  let duration = ''
  if (!start_time || !end_time || start_time == 0 || end_time == 0) {
    // return '未知';
    return '-'
  }
  let minute = Math.floor((Number(end_time.toString().slice(0, 10)) - Number(start_time.toString().slice(0, 10))) / 60)
  let second = (Number(end_time.toString().slice(0, 10)) - Number(start_time.toString().slice(0, 10))) % 60
  if(chineseUnit) return minute.toString() + i18n.global.t('common.pp_minute') + second.toString().padStart(2, '0') + i18n.global.t('common.pp_second')
  if (second < 10 && minute <= 60) {
    duration = minute.toString() + ':' + '0' + second.toString()
  } else if (second >= 10 && minute <= 60) {
    duration = minute.toString() + ':' + second.toString()
  } else if (second < 10 && minute > 60) {
    const hour = Math.floor(minute / 60)
    minute = minute - hour * 60
    duration = hour.toString() + ':' + minute.toString().padStart(2, '0') + ':' + '0' + second.toString()
  } else if (second >= 10 && minute > 60) {
    const hour = Math.floor(minute / 60)
    minute = minute - hour * 60
    duration = hour.toString() + ':' + minute.toString().padStart(2, '0') + ':' + second.toString()
  }
  return duration
}

export const generateUUID = () => {
  let result = ''
  const code = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
  result = code.replace(/[xy]/gu, (item) => {
    const random = (Math.random() * 16) | 0
    const value = item === 'x' ? random : (random & 0x3) | 0x8
    return value.toString(16)
  })
  return result
}

export const formatDuration = (milliseconds: number) => {
  if (!milliseconds && milliseconds !== 0) return '-'
  const totalSeconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  if(milliseconds < 1000) {
    return `${milliseconds / 1000}s`
  } else if (minutes === 0) {
    return `${seconds}s`
  } else {
    return `${minutes}min${seconds}s`
  }
}
