import { FilterObj } from './to-query-string';
export function removeNullProp(obj: any): FilterObj | any {
  Object.keys(obj).forEach((item) => {
    if (
      obj[item] === '' ||
      obj[item] === undefined ||
      obj[item] === null ||
      obj[item] === 'null'
    )
      delete obj[item];
  });
  return obj;
}

export function removeNullKeys(obj: any): FilterObj | any {
  Object.keys(obj).forEach((item) => {
    if (
      obj[item] === '' ||
      obj[item] === undefined ||
      obj[item] === null ||
      obj[item] === 'null' ||
      obj[item].length == 0
    )
      delete obj[item];
  });
  return obj;
}

export const isEmptyData = (data: any) => {
  return data === '' || data === undefined || data === null || data === 'null'
}
