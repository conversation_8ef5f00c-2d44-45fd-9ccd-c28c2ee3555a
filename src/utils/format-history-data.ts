import {formatTime} from './format-time'

export const formatHistoryData = (objArr: any, parameterMap: any) => {
  let arr = [] as any
  let seriesArr = [] as any
  let obj = {}
  const keys = Object.keys(objArr[0])
  keys.map((key: any) => {
    obj[key] = key === 'timestamp' ? objArr.map((item: any) => formatTime(item[key])) : objArr.map((item: any) => item[key])
  })
  for(let key in obj) {
    if(key !== 'timestamp') {
      seriesArr.push({
        name: parameterMap[key],
        type: 'line',
        symbol: 'none',
        data: obj[key]
      })
    }
  }
  arr.push({
    xAxis: obj['timestamp'],
    series: seriesArr
  })
  return arr
}
