import { i18n } from '~/i18n'
import { apiPostFile } from '~/apis/stuck-analysis'
import { ElMessage } from 'element-plus'

/**
 * @description: 确认批量导入设备
 * @param {any} params 上传文件
 * @param {any} deviceList 全部设备列表
 * @return {*}
 */
export const confirmBatchUploadDevice = async (params: any, deviceList: any, limitNumber = true) => {
  try {
    const res = await apiPostFile(params)
    if (res.err_code === 0) {
      const csvData = res.data.csv_data || []
      const uploadDeviceList = [...new Set(csvData.map((item: any) => item.device_id))]
      const deviceIds = deviceList.map((item: any) => item.device_id)
      const importDevice = uploadDeviceList.filter((item: any) => deviceIds.includes(item))
      if (limitNumber && importDevice.length > 100) {
        ElMessage.error(i18n.global.t('stuckAnalysis.pp_exceed_100'))
      } else if (importDevice.length == 0) {
        ElMessage.error(i18n.global.t('stuckAnalysis.pp_number_0'))
      } else {
        ElMessage.success(`${i18n.global.t('stuckAnalysis.pp_import_success')} ${importDevice.length} ${i18n.global.t('stuckAnalysis.pp_device_number')}`)
        return importDevice
      }
    } else {
      ElMessage.error(res.message)
    }
    return null
  } catch (error: any) {
    return null
  }
}
