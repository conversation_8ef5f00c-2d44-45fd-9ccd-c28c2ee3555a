/**
 * @description: 自定义过滤方法
 * @param {any} node
 * @param {string} keyword
 * @return {*}
 */
export const customFilter = (node: any, keyword: string) => {
  const trimmedKeyword = keyword.trim().toLowerCase()
  if (!trimmedKeyword) return false

  // 递归获取所有父节点的标签
  const getAllLabels = (node: any): string[] => {
    const labels = [node.label]
    let currentNode = node
    while (currentNode.parent && currentNode.parent.label) {
      labels.unshift(currentNode.parent.label)
      currentNode = currentNode.parent
    }
    return labels
  }

  // 获取当前节点的所有标签（包括父节点）
  const allLabels = getAllLabels(node)

  // 检查任意层级是否匹配关键字
  const hasMatch = allLabels.some((label) => label.toLowerCase().includes(trimmedKeyword))

  if (hasMatch) return true

  // 如果当前节点不匹配，检查子节点
  if (node.children?.length) {
    return node.children.some((child: any) => customFilter(child, trimmedKeyword))
  }

  return false
}