/**
 * @description: 数字转中文
 * @param {*} num
 * @return {*}
 */
export const numberToChinese = (num: number) => {
  const chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const chnUnitChar = ['', '十', '百', '千']

  let strIns = ''
  let chnStr = ''
  let unitPos = 0
  let zero = true
  while (num > 0) {
    var v = num % 10
    if (v === 0) {
      if (!zero) {
        zero = true
        chnStr = chnNumChar[v] + chnStr
      }
    } else {
      zero = false
      strIns = chnNumChar[v]
      strIns += chnUnitChar[unitPos]
      chnStr = strIns + chnStr
    }
    unitPos++
    num = Math.floor(num / 10)
  }
  return chnStr
}
