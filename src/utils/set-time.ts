import { i18n } from '~/i18n'

/**
 * @description: 当结束日期是当天时，将日期设为当前时间，否则设为结束日期的最后一秒(精确到天，一段时间)
 * @param {any} dates
 * @return {*}
 */
export const handleDayTimeChange = (dates: any) => {
  if (!dates || dates.length !== 2) return
  const [start, end] = dates
  const now = new Date()
  if (end.getFullYear() === now.getFullYear() && end.getMonth() === now.getMonth() && end.getDate() === now.getDate()) {
    end.setHours(now.getHours())
    end.setMinutes(now.getMinutes())
    end.setSeconds(now.getSeconds())
  } else {
    end.setHours(23)
    end.setMinutes(59)
    end.setSeconds(59)
  }
  return [start, end]
}

export const setCuts = () => {
  const shortcuts = [
    {
      text: i18n.global.t('common.pp_last_day'),
      value: () => {
        const end = new Date();
        const start = new Date(new Date().toLocaleDateString());
        start.setTime(start.getTime() - 3600 * 1000 * 24);
        return [start, end];
      },
    },
    {
      text: i18n.global.t('common.pp_last_week'),
      value: () => {
        const end = new Date();
        const start = new Date(new Date().toLocaleDateString());
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        return [start, end];
      },
    },
    {
      text: i18n.global.t('common.pp_last_month'),
      value: () => {
        const end = new Date();
        const start = new Date(new Date().toLocaleDateString());
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        return [start, end];
      },
    },
    {
      text: i18n.global.t('common.pp_last_3_month'),
      value: () => {
        const end = new Date();
        const start = new Date(new Date().toLocaleDateString());
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
        return [start, end];
      },
    },
  ];
  return shortcuts;
};
