import { formatTimeToDay } from "./format-time"

export const formatFTTData = (data: any, standard: number) => {
  return data.map((item: any) => {
    // if(item.success_rate > standard) {
    //   item.value = (item.success_rate * 100).toFixed(2)
    //   item.itemStyle = {color: '#00bebe'}
    // } else {
    //   item.value = (item.success_rate * 100).toFixed(2)
    //   item.itemStyle = {color: '#f44336'}
    // }
    item.value = (item.success_rate * 100).toFixed(2)
    item.itemStyle = {color: '#00bebe'}
    return item
  })
}

export const formatNotFTTData = (data: any) => {
  return data.map((item: any) => {
    item.value = (item.success_rate * 100).toFixed(2)
    item.itemStyle = {color: '#bdbdbd'}
    return item
  })
}

export const formatLineYdata = (data: any) => {
  return data.map((item: any) => (item.ftt * 100).toFixed(2))
}

export const formatLineXdata = (data: any) => {
  return data.map((item: any) => formatTimeToDay(item.day))
}

