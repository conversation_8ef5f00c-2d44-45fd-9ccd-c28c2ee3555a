/*
 * @Author: zhenxing.chen
 * @Date: 2023-02-08 14:09:12
 * @LastEditors: liu.yang
 * @LastEditTime: 2023-04-27 16:35:52
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
import { formatFTTData, formatNotFTTData, formatLineYdata, formatLineXdata } from './format-algorithm-data'
import formatLocaleDate from './format-locale-date';
import formatLocaleDay from './format-locale-day';
import formatToUpperCase from './format-to-uppercase';
import toQueryString from './to-query-string';
import toCamelCase from './to-camelcase';
import getWsPrefix from './get-ws-prefix';
import unshiftList from './unshift-list';
import formatModelActiveStatus from './format-model-active-status';
import fileSizeCal from './file-size-cal';
import { getEnv, getEnvName, isEu } from './get-env';
import { getLocale } from './get-locale';
import { formatTime, formatTimeToDay, formatTimeToDayWithDash, setSecondsToZero, setSecondsToFiftyNine, getStartTimeOfDay, getFinalEndTime, msToTime, formatTimeWithoutDate } from './format-time';
import { yesterdayDate } from './get-yesterday-date';
import { handleDeviceNameNull } from './handle-device-name-null';
import { 
  getDisabledDate,
  getDisabledHours,
  getDisabledMinutes,
  getDisabledSeconds,
  getShortcuts,
  ShortOneWeekcuts,
  ShortTwoMonthcuts,
  dateShortcuts,
  shortcuts,
  initStartTime,
  initEndTime,
  getTodayShortcuts,
  getDisabledTodayDate,
  initYesterdayStartTime,
  initYesterdayEndTime,
  getYesterdayShortcuts,
  getDisabledYesterdayDate
} from './get-datapicker-param';
import {removeNullProp, removeNullKeys, isEmptyData} from './remove-null-prop';
import { getChartGroupData } from './get-chart-group-data';
import {formatHistoryData} from './format-history-data'
import {timestampToStr} from './timestamp-to-string'
import {jsonToFormData, clearJson} from './json-to-formdata'
import {
  platformStepRules,
  batteryStepRules,
  axisRules,
  dataPointRules,
  deviceIdRules,
  pus4DeviceIdRules,
} from './searchFormRules';
import getSeqArray from './get-seq-array';
import cookie from 'js-cookie';
import { getDuration, generateUUID, formatDuration } from './get-duration';
import { downloadResponseFile } from './download-response-file';
import {sortWithLetter} from './sort-with-letter'
import {getOneDecimalPlaces, getTwoDecimalPlaces, getFourDecimalPlaces} from './get-decimal-place'
import {handleDayTimeChange, setCuts} from './set-time'
import {maskString} from './format-car-id'
import {flexColumnWidth} from './flex-column-width'
import {confirmBatchUploadDevice} from './batch-upload-device'
import {numberToChinese} from './number-to-chinese'
import {customFilter} from './cascader-filter'
import {getLabel} from './get-option-label'
import {parseQueryParams} from './query-parser'
import getOptions from './get-options';
export const getUserId = () => cookie.get('user_id') ?? localStorage.getItem('user_id')

export {
  formatFTTData,
  formatNotFTTData,
  formatLineYdata,
  formatLineXdata,
  yesterdayDate,
  formatTime,
  formatTimeToDay,
  formatTimeToDayWithDash,
  formatLocaleDate,
  formatLocaleDay,
  getDuration,
  generateUUID,
  formatDuration,
  formatToUpperCase,
  toQueryString,
  toCamelCase,
  getWsPrefix,
  unshiftList,
  formatModelActiveStatus,
  fileSizeCal,
  getEnv,
  getEnvName,
  isEu,
  getLocale,
  getOptions,
  handleDeviceNameNull,
  getDisabledDate,
  getDisabledHours,
  getDisabledMinutes,
  getDisabledSeconds,
  getShortcuts,
  dateShortcuts,
  shortcuts,
  initStartTime,
  initEndTime,
  getTodayShortcuts,
  getDisabledTodayDate,
  initYesterdayStartTime,
  initYesterdayEndTime,
  getYesterdayShortcuts,
  getDisabledYesterdayDate,
  ShortOneWeekcuts,
  ShortTwoMonthcuts,
  removeNullProp,
  removeNullKeys,
  isEmptyData,
  getChartGroupData,
  platformStepRules,
  batteryStepRules,
  axisRules,
  dataPointRules,
  deviceIdRules,
  pus4DeviceIdRules,
  getSeqArray,
  downloadResponseFile,
  formatHistoryData,
  timestampToStr,
  jsonToFormData,
  clearJson,
  sortWithLetter,
  getFourDecimalPlaces,
  getTwoDecimalPlaces,
  getOneDecimalPlaces,
  handleDayTimeChange,
  setCuts,
  maskString,
  setSecondsToZero,
  setSecondsToFiftyNine,
  getStartTimeOfDay,
  getFinalEndTime,
  flexColumnWidth,
  confirmBatchUploadDevice,
  msToTime,
  formatTimeWithoutDate,
  numberToChinese,
  customFilter,
  getLabel,
  parseQueryParams
};
