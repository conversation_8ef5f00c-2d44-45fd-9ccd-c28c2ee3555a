import { i18n } from '~/i18n';

const isConsecutive = (arr: any) => {
  console.log(arr)
  arr.sort((a: any, b: any) => a - b); // 使用sort()方法对数组进行排序
  for (let i = 0; i < arr.length - 1; i++) {
    if (Number(arr[i]) + 1 !== Number(arr[i + 1])) {
      // 检查相邻的两个数字是否连续
      return false;
    }
  }
  return true;
};

export const platformStepRules = (rule: any, value: any, callback: any) => {
  if (!value || value?.length == 0) {
    return callback(
      new Error(
        i18n.global.t('common.pp_please_select') +
          i18n.global.t('serviceDetail.pp_platform_step')
      )
    );
  } else {
    // if (
    //   Object.prototype.toString.call(value) == '[object Array]' &&
    //   value?.length > 1 &&
    //   !isConsecutive([...value])
    // ) {
    //   return new Error(
    //     i18n.global.t('common.pp_please_select') +
    //       i18n.global.t('serviceDetail.pp_serial_number')
    //   );
    // }
    return callback();
  }
};
export const batteryStepRules = (rule: any, value: any, callback: any) => {
  if (!value || value?.length == 0) {
    return callback(
      new Error(
        i18n.global.t('common.pp_please_select') +
          i18n.global.t('serviceDetail.pp_battery_step')
      )
    );
  } else {
    // if (
    //   Object.prototype.toString.call(value) == '[object Array]' &&
    //   value?.length > 1 &&
    //   !isConsecutive([...value])
    // ) {
    //   return new Error(
    //     i18n.global.t('common.pp_please_select') +
    //       i18n.global.t('serviceDetail.pp_serial_number')
    //   );
    // }
    return callback();
  }
};
export const axisRules = (rule: any, value: any, callback: any) => {
  if (value.length === 0) {
    return callback(
      new Error(
        i18n.global.t('common.pp_please_select') +
          i18n.global.t('serviceDetail.pp_axis_name')
      )
    );
  } else {
    return callback();
  }
};
export const dataPointRules = (rule: any, value: any, callback: any) => {
  if (value.length === 0) {
    return callback(
      new Error(
        i18n.global.t('common.pp_please_select') +
          i18n.global.t('serviceDetail.pp_data_point_name')
      )
    );
  } else {
    return callback();
  }
};

const checkValidDeviceId = (value: string) => {
  if (value.length == 24) {
    let arr = value.split('-');
    if (arr.length == 4) {
      if (
        arr[0] == 'PS' &&
        arr[1] == 'NIO' &&
        arr[2].length == 8 &&
        arr[3].length == 8
      ) {
        return true;
      }
    }
  }
  return false;
};

export const deviceIdRules = (rule: any, value: any, callback: any) => {
  if (!value || value?.length == 0) {
    return callback(
      new Error(
        i18n.global.t('common.pp_please_input') +
          i18n.global.t('deviceManagement.pp_device_id')
      )
    );
  } else {
    if (!checkValidDeviceId(value)) {
      return new Error(
        i18n.global.t('common.pp_please_input') +
          i18n.global.t('factoryManagement.pp_valid_device_id')
      );
    }
    return callback();
  }
};

const checkValidPus4DeviceId = (value: string) => {
  if (value.length == 25) {
    let arr = value.split('-');
    if (arr.length == 4) {
      if (
        arr[0] == 'PUS' &&
        arr[1] == 'NIO' &&
        arr[2].length == 8 &&
        arr[3].length == 8
      ) {
        return true;
      }
    }
  }
  return false;
};

export const pus4DeviceIdRules = (rule: any, value: any, callback: any) => {
  if (!value || value?.length == 0) {
    return callback(
      new Error(
        i18n.global.t('common.pp_please_input') +
          i18n.global.t('deviceManagement.pp_device_id')
      )
    );
  } else {
    if (!checkValidPus4DeviceId(value)) {
      return new Error(
        i18n.global.t('common.pp_please_input') +
          i18n.global.t('factoryManagement.pp_valid_device_id')
      );
    }
    return callback();
  }
};
