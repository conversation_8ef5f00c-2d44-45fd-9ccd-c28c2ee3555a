export const jsonToFormData = (config: any) => {
  const formData = new FormData()
  Object.keys(config).forEach((key) => {
    formData.append(key, config[key])
  })
  return formData
}

export function clearJson(data: any) {
  const json = data
  let key
  for (key in json) {
    if (json[key] instanceof Array) {
      json[key] = []
    } else if (typeof json[key] === 'object' && Object.prototype.toString.call(json[key]).toLowerCase() === '[object object]' && !json[key].length) {
      json[key] = {}
    } else {
      json[key] = ''
    }
  }
}