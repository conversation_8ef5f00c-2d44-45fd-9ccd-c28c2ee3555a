export const sortWithLetter = (obj: any) => {
  const sortedKeys = Object.keys(obj).sort((a, b) => {
    // 使用正则表达式提取字符串中的数字
    const numA = parseInt(a.replace(/\D+/g, ''))
    const numB = parseInt(b.replace(/\D+/g, ''))

    // 按照数字大小进行升序排序
    return numA - numB
  })

  // 根据排序后的键名数组重新构建一个新的对象
  const sortedObj = sortedKeys.reduce((acc, key) => {
    // 将键名对应的值添加到新对象中
    acc[key] = obj[key]
    // 返回新对象，供下一次迭代使用
    return acc
  }, {})
  return sortedObj
}
