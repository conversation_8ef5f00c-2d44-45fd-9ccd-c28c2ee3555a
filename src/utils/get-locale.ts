import { getEnv } from './get-env';

export const getLocale = () => {
  let locale = localStorage.getItem('locale');

  if (!locale) {
    let env = getEnv();
    locale = 'zh';
    console.log(env != '' && !env);
    if (env != '' && !env) {
      console.log('getLocale(), env是undefined', env);
    } else if (['-stg-eu', '-eu'].indexOf(env) != -1) {
      locale = 'en';
    }
    localStorage.setItem('locale', locale);
  }
  return locale;
};
