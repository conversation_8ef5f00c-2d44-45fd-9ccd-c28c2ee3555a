/*
 * @Author: zhenxing.chen
 * @Date: 2023-03-01 13:25:26
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-03-01 13:32:46
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
//ssoUrl 跳转
import { getEnv } from './index';
const env = getEnv();
export const sso_url = `https://signin${env}.nio.com/oauth2/authorize?client_id=101021&response_type=code`;
//欧洲stgUrl
export const sso_url_eu_stg = `https://signin-stg.eu.nio.com/oauth2/authorize?client_id=101021&response_type=code`;
//欧洲proUrl
export const sso_url_eu_pod =
  'https://signin.eu.nio.com/oauth2/authorize?client_id=101021&response_type=code';

// export const sso_url = `https://signin${env}.nio.com/oauth2/authorize?client_id=101021&response_type=code&redirect_uri=https://pp-welkin${env}.nioint.com`;
// //欧洲stgUrl
// export const sso_url_eu_stg = `https://signin-stg.eu.nio.com/oauth2/authorize?client_id=101021&response_type=code&redirect_uri=https://pp-welkin-stg-eu.nioint.com`;
// //欧洲proUrl
// export const sso_url_eu_pod =
//   'https://signin.eu.nio.com/oauth2/authorize?client_id=101021&response_type=code&redirect_uri=https://pp-welkin-eu.nioint.com';
