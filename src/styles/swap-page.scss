.swap-page-header {
  width: 100%;
  // padding: 20px 20px 0;
  // display: flex;
  // flex-direction: column;
  display: flex;
  justify-content: space-between;
  align-items: center;

  height: 100px;
  padding: 20px;
  // padding-bottom: 10px;

  // margin: 15px 15px 5px;
  .header-left {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // padding-bottom: 10px;
    gap: 10px;
    .header-title {
      font-size: 18px;
      font-weight: 400;
      color: #303133;
    }
  }

  .header-right {
    height: 100%;
    display: flex;
    align-items: center;
    .add-user-button {
      background-color: var(--el-color-primary);
      color: #fff;
    }
  }
}

.swap-page-container {
  // height: calc(100% - 100px);
  min-height: calc(100% - 100px);
  // overflow-y: scroll;
  background-color: #f8f8fa;
  padding: 20px;
  .search-container {
    width: 100%;
    // height: 72px;
    border-radius: 4px;
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    .search-item-label {
      font-size: 14px;
      font-family: 'Noto Sans';
      color: #292c33;
      margin: 0 10px;
      word-break: keep-all;
    }
    .search-container-right {
      margin-left: auto;
    }
    .search-button {
      width: 100%;
      display: flex;
    }
  }
}
.empty-box {
  height: calc(100vh - 270px);
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.collapse-search-container {
  display: grid;
  grid-template-columns: repeat(2, minmax(0px, 1fr));
  row-gap: 15px;
  column-gap: 20px;
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  .el-form-item {
    margin-bottom: 0;
    // width: 90%;
    margin-left: 10px;
    .el-form-item__label {
      justify-content: flex-start;
      font-family: 'Noto Sans';
      color: #292c33;
    }
    .el-select {
      width: 100%;
    }
  }
  .button-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .expand-button,
    .fold-button {
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .el-icon {
      color: var(--el-color-primary);
      margin-right: 10px;
      cursor: pointer;
    }

    .fold-info {
      color: var(--el-color-primary);
      cursor: pointer;
      font-size: 14px;
    }
  }

  .search-button {
    background-color: var(--el-color-primary);
    color: #fff;
    margin-right: 15px;
    margin-left: 15px;
  }

  .download-button {
    background-color: #0288d1;
    color: #fff;
  }
}
.fold-search-container {
  @extend .collapse-search-container;
  // grid-template-columns: repeat(3, minmax(0px, 1fr));
  grid-template-columns: 35% 35% 25%;
  .el-form-item {
    width: 100%;
    .el-form-item__label {
      justify-content: center;
    }
  }
}

.swap-table-container {
  border-radius: 4px;
  background-color: #fff;
  .el-table {
    .cell {
      color: #22252b;
      font-family: 'Noto Sans';
    }

    .pointer-column {
      color: var(--el-color-primary);
      font-family: 'Noto Sans';
      .point-span {
        cursor: pointer;
      }
      .point-span:hover {
        color: #66d2d2;
      }
    }

    .operation-column {
      .cell {
        display: flex;
        justify-content: center;
        div,
        a {
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }

      .message-popover {
        min-width: 40px !important;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .el-icon {
        margin: 0 5px;
        font-size: 20px;
      }
      .operation-icon {
        color: #01A0AC;
        cursor: pointer;
      }
      .operation-icon-disable {
        color: #BFBFBF;
      }
      .operation-icon-error {
        color: #be2900;
        cursor: pointer;
      }
    }
  }

  .pagination-container {
    display: flex;
    align-items: center;
    padding-left: 20px;
    // justify-content: space-between;
    // justify-content: flex-end;

    .pagination {
      margin-left: auto;
    }
  }
}

.pus-date-picker {
  width: 400px;
}

.search-form-container {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  .el-form-item {
    margin-bottom: 0;
    margin-right: 20px !important;
    .el-form-item__label {
      justify-content: flex-start;
      font-family: 'Noto Sans';
      color: #292c33;
    }
    .search-button {
      background-color: var(--el-color-primary);
      color: #fff;
    }
  }
}

.status-container {
  display: flex;
  align-items: center;
  line-height: 17px;
  .status-text {
    padding-left: 8px;
  }
}

.lack-permission {
  font-size: 16px;
  color: #262626;
  font-family: 'Blue Sky Standard';
}
