.service-detail-header {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  .card-title {
    display: flex;
    align-items: center;
    margin-top: 14px;
    margin-bottom: 14px;
    .el-icon {
      cursor: pointer;
    }
    .title-info {
      font-size: 18px;
      font-weight: 400;
      color: #303133;
    }
    .title-device {
      display: flex;
      flex-direction: column;
      margin-left: 40px;
      .station-device {
        font-size: 14px;
        font-family: 'Noto Sans';
        color: #22252b;
      }
      .device-id {
        font-size: 12px;
        font-family: 'Noto Sans';
        color: #666f7f;
      }
    }
    .mx-1 {
      margin-left: 10px;
    }
    .el-tag {
      height: 20px;
      margin-top: -10px;
      background-color: #e8fffb;
      border-color: var(--el-color-primary);
    }
    .tag-info {
      display: flex;
      align-items: center;
      .el-icon {
        color: var(--el-color-primary);
      }
      .tag-title {
        margin-left: 6px;
        color: var(--el-color-primary);
      }
    }
    .detail-fold-button {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 10px;
      .el-icon {
        color: var(--el-color-primary);
      }
      .fold-title {
        font-size: 14px;
        color: var(--el-color-primary);
        font-family: 'Noto Sans';
        margin-left: 10px;
      }
    }
  }

  .el-row {
    margin-bottom: 14px;
    .el-col {
      display: flex;
      flex-direction: column;
      .span-title {
        font-size: 14px;
        color: #666f7f;
        margin-top: 10px;
        font-family: 'Noto Sans';
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: flex;
        .span-title-label {
          width: 24%;
        }
      }
      .first-line {
        margin-top: 0;
      }
    }
  }
}
