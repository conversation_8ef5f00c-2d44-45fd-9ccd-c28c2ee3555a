.plc-recording-container {
  .plc-search-box {
    padding: 20px;
    border-radius: 4px;
    background-color: #fff;
    .search-button-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .button-container {
        display: flex;
        width: auto;
        .el-button + .el-button {
          margin-left: 16px;
        }
        .search-button {
          background-color: var(--el-color-primary);
          color: #fff;
        }
      }
    }
    .search-form-box {
      width: 100%;
    }
    .el-form {
      width: 100%;
      display: flex;
      .el-form-item {
        width: 80%;
        margin-bottom: 0;
        .el-form-item__content {
          margin-left: 0 !important;
          .el-form-item__error {
            padding-top: 6px;
          }
        }
      }
    }
    .el-radio__input.is-checked .el-radio__inner {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary);
    }
    .el-radio__input.is-checked + .el-radio__label {
      color: var(--el-color-primary);
    }
    .el-radio__inner:hover {
      border-color: var(--el-color-primary);
    }
    .step-pl {
      display: flex;
      align-items: center;
      margin-top: 20px;
      margin-bottom: 16px;
      .tips-info {
        font-size: 14px;
        color: #22252b;
        font-family: 'Noto Sans';
      }
      .suspend-tips {
        border: 2px solid #9ca3b0;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 12px;
        cursor: pointer;
        color: #91a2bc;
        font-weight: 700;
      }
    }
    .m-2 {
      margin: 0;
      width: 100%;
      .el-input {
        width: 100%;
      }
    }
  }
  .plc-empty-box {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    border-radius: 4px;
    background-color: #fff;
  }

  .plc-echarts {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    padding-right: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    background-color: #fff;
  }
}
