// 主按钮
.welkin-primary-button:not(.is-disabled) {
  font-weight: 420;
  color: #ffffff;
  background: #00bebe;
  border: 1px solid #00bebe;
  &:hover {
    color: #ffffff;
    background: #21ccc6;
    border: 1px solid #21ccc6;
  }
  &:focus {
    color: #ffffff;
    background: #009499;
    border: 1px solid #009499;
  }
}
.welkin-primary-button.is-disabled {
  font-weight: 420;
  color: rgba(1, 160, 172, 0.5);
  background: #ffffff;
  border: 1px solid rgba(0, 190, 190, 0.5);
  &:hover {
    color: rgba(1, 160, 172, 0.5);
    background: #ffffff;
    border: 1px solid rgba(0, 190, 190, 0.5);
  }
  &:focus {
    color: rgba(1, 160, 172, 0.5);
    background: #ffffff;
    border: 1px solid rgba(0, 190, 190, 0.5);
  }
}

// 次按钮
.welkin-secondary-button {
  font-weight: 420;
  color: #01a0ac;
  background: #ffffff;
  border: 1px solid #00bebe;
  &:hover {
    color: #01a0ac !important;
    background: #e5f9f9 !important;
    border: 1px solid #00bebe !important;
  }
  &:focus {
    color: #01a0ac;
    background: #9cebe7;
    border: 1px solid #00bebe;
  }
}
.welkin-secondary-button.is-disabled {
  font-weight: 420;
  color: rgba(1, 160, 172, 0.5);
  background: #ffffff;
  border: 1px solid rgba(0, 190, 190, 0.5);
  &:hover {
    color: rgba(1, 160, 172, 0.5) !important;
    background: #ffffff !important;
    border: 1px solid rgba(0, 190, 190, 0.5) !important;
  }
  &:focus {
    color: rgba(1, 160, 172, 0.5);
    background: #ffffff;
    border: 1px solid rgba(0, 190, 190, 0.5);
  }
}

// 幽灵按钮
.welkin-ghost-button {
  font-weight: 420;
  color: #262626;
  background: #ffffff;
  border: 1px solid #bfbfbf;
  &:hover {
    color: #262626;
    background: #f5f5f5;
    border: 1px solid #bfbfbf;
  }
  &:focus {
    color: #262626;
    background: #f0f0f0;
    border: 1px solid #bfbfbf;
  }
}

// 灰色文字按钮
.welkin-text-button {
  font-weight: 420;
  color: #595959;
  border: none;
  &:hover {
    color: #595959;
    background: #f5f5f5;
    border: none;
  }
  &:focus {
    color: #595959;
    background: #f0f0f0;
    border: none;
  }
}

// 操作文字按钮 不要采用el-button,采用div / span即可
.welkin-operation-button {
  font-weight: 420;
  color: #01a0ac;
  cursor: pointer;
  &:hover {
    color: #21ccc6;
  }
  &:focus {
    color: #009499;
  }
}
.welkin-operation-button[disabled='true'] {
  font-weight: 420;
  color: #bfbfbf;
  pointer-events: none;
}

// 危险主按钮
.welkin-danger-primary-button {
  font-weight: 420;
  color: #ffffff;
  background: #f83535;
  border: 1px solid #f83535;
  &:hover {
    color: #ffffff;
    background: #f5605b;
    border: 1px solid #f5605b;
  }
  &:focus {
    color: #ffffff;
    background: #e5282d;
    border: 1px solid #e5282d;
  }
}

@keyframes boxShadowOffset4 {
  0% {
    box-shadow: -38px -12px, -14px 0, 14px 0, 38px 0;
  }
  33% {
    box-shadow: -38px 0px, -14px -12px, 14px 0, 38px 0;
  }
  66% {
    box-shadow: -38px 0px, -14px 0, 14px -12px, 38px 0;
  }
  100% {
    box-shadow: -38px 0, -14px 0, 14px 0, 38px -12px;
  }
}
.loader-45 {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  color: #00bebe;
  -webkit-animation: boxShadowOffset4 1s linear infinite alternate;
  animation: boxShadowOffset4 1s linear infinite alternate;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.loader-17 {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border-top: 3px solid #00bebe;
  border-right: 3px solid transparent;
  -webkit-animation: rotation 1s linear infinite;
  animation: rotation 1s linear infinite;
}
