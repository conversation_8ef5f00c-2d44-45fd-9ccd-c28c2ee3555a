.single-station-header-title {
  display: flex;
  align-items: center;

  .el-icon {
    cursor: pointer;
  }
  .title-device {
    display: flex;
    flex-direction: column;
    margin-left: 40px;

    .station-device {
      font-size: 14px;
      font-family: 'Noto Sans';
      color: #22252b;
    }

    .device-id {
      font-size: 12px;
      font-family: 'Noto Sans';
      color: #666f7f;
    }
  }

  .mx-1 {
    margin-left: 10px;
  }

  .el-tag {
    height: 20px;
    margin-top: -10px;
    background-color: #e8fffb;
    border-color: var(--el-color-primary);
  }

  .tag-info {
    display: flex;
    align-items: center;

    .el-icon {
      color: var(--el-color-primary);
    }

    .tag-title {
      margin-left: 6px;
      color: var(--el-color-primary);
    }
  }
}

.single-station {
  .swap-page-header {
    // height: 80px;
    padding-bottom: 10px;
  }
  // .swap-page-container {
  //   height: calc(100% - 140px);
  // }
}
