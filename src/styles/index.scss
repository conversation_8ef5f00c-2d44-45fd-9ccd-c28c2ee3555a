@import 'global';
@import 'variables';
@import 'common';

:root {
  --el-color-primary: #00bebe;
}
body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
}

a {
  color: var(--ep-color-primary);
}

code {
  border-radius: 2px;
  padding: 2px 4px;
  background-color: var(--ep-color-primary-light-9);
  color: var(--ep-color-primary);
}

// 单选select
.el-select-dropdown__item.selected {
  color: var(--el-color-primary);
}

.el-select .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

.el-input .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

// 多选select
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: var(--el-color-primary);
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  background: var(--el-color-primary);
}

// 分页
.pagination-container {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  .el-pagination {
    margin-top: 20px;
    margin-bottom: 30px;

    .el-pagination__total,
    .el-pagination__jump {
      color: $gray-color;
      font-family: 'Noto Sans';
    }

    .el-pager li.is-active {
      color: var(--el-color-primary);
    }

    .el-pager li:hover {
      color: var(--el-color-primary);
    }

    .el-pager li {
      color: #91a2bc;
    }

    .btn-prev:not(:disabled):hover {
      color: var(--el-color-primary);
    }

    .btn-next:not(:disabled):hover {
      color: var(--el-color-primary);
    }
  }
}

// 表格
.el-table {
  width: 100%;
  .el-table__header-wrapper table,
  .el-table__body-wrapper table {
    width: 100% !important;
  }

  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }

  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }

  .el-checkbox__inner:hover {
    border-color: var(--el-color-primary);
  }

  .el-checkbox__input.is-disabled .el-checkbox__inner:hover {
    background-color: var(--el-checkbox-disabled-input-fill);
    border-color: var(--el-checkbox-disabled-border-color);
  }
  th.el-table__cell.is-leaf {
    background: #e5f9f9;
    color: #262626;
    font-size: 14px;
  }
}

.el-table .cell {
  white-space: nowrap;
  // color: #22252b;
  // font-family: 'Noto Sans';
}

.el-table .ascending .sort-caret.ascending {
  border-bottom-color: var(--el-color-primary);
}

.el-table .descending .sort-caret.descending {
  border-top-color: var(--el-color-primary);
}

// 通知
.el-notification {
  padding-right: 10px;

  .el-notification__title {
    color: #67c23a;
    font-size: 16px;
  }

  .el-notification__content {
    color: #22252b;
    font-size: 14px;
    text-align: left;
    word-break: break-all;
  }
}

// 面包屑
.el-breadcrumb__item:last-child .el-breadcrumb__inner {
  font-size: 14px;
  color: #666f7f;
  font-family: 'Noto Sans';
}

.el-breadcrumb__inner {
  font-size: 14px;
  color: #666f7f;
  font-family: 'Noto Sans';
}

// 日期
.el-picker-panel__shortcut:hover {
  color: var(--el-color-primary);
}

.el-time-panel__btn.confirm {
  color: var(--el-color-primary);
}

.el-date-table td.end-date .el-date-table-cell__text,
.el-date-table td.start-date .el-date-table-cell__text {
  background-color: var(--el-color-primary) !important;
}

.el-date-table td.today .el-date-table-cell__text {
  color: var(--el-color-primary);
}
.el-date-table-cell:hover {
  color: var(--el-color-primary);
}
.el-date-table-cell__text:hover {
  color: var(--el-color-primary);
}

.el-date-table td.end-date .el-date-table-cell__text {
  color: #fff;
}

.el-picker-panel__icon-btn.arrow-left:hover {
  color: var(--el-color-primary);
}

.el-picker-panel__icon-btn.arrow-right:hover {
  color: var(--el-color-primary);
}

.el-picker-panel__icon-btn.d-arrow-left:hover {
  color: var(--el-color-primary);
}

.el-picker-panel__icon-btn.d-arrow-right:hover {
  color: var(--el-color-primary);
}

.el-range-separator {
  color: #666f7f;
  font-family: 'Noto Sans';
}

.el-range-editor.is-active {
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}

// 单选el-radio
.el-radio-button__original-radio:checked + .el-radio-button__inner {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  box-shadow: -1px 0 0 0 var(--el-color-primary);
}

.el-radio-button__inner:hover {
  color: var(--el-color-primary);
}

.el-radio-button.is-active.is-focus .el-radio-button__inner {
  color: #fff;
}

// tab栏
.tab-container {
  flex: 1;
  .el-tabs__header {
    margin: 0;
  }
  .el-tabs__item.is-top:hover {
    color: var(--el-color-primary);
  }
  .el-tabs__item.is-active {
    color: var(--el-color-primary);
  }
  .el-tabs__active-bar {
    background-color: var(--el-color-primary) !important;
  }
  .el-tabs__nav-wrap::after {
    position: static !important;
  }
}

// card
.el-card.is-always-shadow {
  box-shadow: none;
}

.el-picker-panel__sidebar {
  width: 120px;
}

// tooltip
.aec-tooltip {
  background: #00bebe !important;
  border-color: #e9e9eb !important;
}

.required-form-item .el-form-item__label:before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}

.btn-more,
.btn-put-away {
  color: #00bebe;
  cursor: pointer;
  display: inline-block;
  padding-top: 6px;
  vertical-align: top;
}

.el-dialog {
  border-radius: 4px !important;
  .el-dialog__header {
    background: linear-gradient(180deg, rgba(230, 250, 250, 0.7), transparent);
    margin-right: 0;
    border-radius: 4px;
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 20px;
    color: #262626;
  }
}

.omit-front {
  overflow: hidden;
  text-overflow: ellipsis;
  direction: rtl;
  white-space: nowrap;
  text-align: left;
}

.el-message-box__headerbtn .el-message-box__close {
  font-size: 20px;
}

.el-message-box__btns button:nth-child(1) {
  border: none;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 4px;
}

.el-pagination .el-select .el-input {
  width: 100px;
}

.mini-page.is-background {
  .el-pager li,
  .btn-prev,
  .btn-next,
  .btn-prev:disabled,
  .btn-next:disabled {
    color: #595959;
    background-color: #fff;
  }
}

.stuck-analysis-messagebox,
.camera-messagebox {
  width: auto;
  padding-top: 5px;
  padding-bottom: 20px;
  background: linear-gradient(180deg, #e6fafa 0%, #fff 14.89%);
  .el-message-box__header {
    color: #1f1f1f;
  }
  .el-message-box__content {
    color: #262626;
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .el-button--primary:hover {
    background: #21ccc6 !important;
    color: #fff !important;
    border-color: #21ccc6;
  }
  .el-button--primary:focus {
    background: #009499 !important;
    color: #fff !important;
    border-color: #009499;
  }
  .el-message-box__btns button:nth-child(1) {
    color: #595959;
    border: none;
    &:hover {
      color: #595959 !important;
      background-color: #f5f5f5 !important;
    }
    &:focus {
      color: #595959 !important;
      background-color: #f0f0f0 !important;
    }
  }
  .el-button {
    font-weight: 420;
  }
}
.camera-messagebox {
  width: 300px;
}

.el-tag.el-tag--primary {
  color: rgba(38, 38, 38, 1);
  background-color: rgba(229, 249, 249, 1);
  padding: 5px 8px;
  margin-right: 8px;
  margin-top: 10px;
  border: 0px;
}
.reset-btn {
  border: 1px solid rgba(0, 190, 190, 1);
  color: rgba(1, 160, 172, 1);
}
.el-message__icon {
  width: 14px;
  height: 14px;
  svg {
    width: 14px;
    height: 14px;
  }
}

svg:focus {
  outline: none;
}

.common-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  .el-pagination__total {
    color: #595959;
  }
  .el-pager li {
    background: #f0f0f0;
    color: #595959;
  }
  .el-pager li.is-active {
    background: #00bebe;
    color: #fff;
  }
  .el-pagination__jump {
    color: #595959;
  }
  .el-input__inner {
    color: #595959 !important;
  }
}

.color-26 {
  color: #262626;
}
.color-59 {
  color: #595959;
}
.color-8c {
  color: #8c8c8c;
}
.color-1f {
  color: #1f1f1f;
}
.color-a0 {
  color: #01a0ac;
}
.color-be {
  color: #00bebe;
}

.el-popper.is-light {
  .el-tag {
    color: #262626;
  }
}

.search-popper {
  padding: 24px !important;
}

.table-setting-popper {
  .setting-content {
    max-height: 255px;
    overflow-y: auto;

    .column-list {
      display: flex;
      flex-direction: column;
      .el-checkbox__input + .el-checkbox__label {
        color: #262626;
        font-weight: 400;
        font-family: 'Blue Sky Standard';
      }
    }
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 9px;
    }
  }
  .setting-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 8px;
    border-top: 1px solid #e6e7ec;
    .el-button {
      width: 48px;
      height: 24px;
      border-radius: 2px;
      font-size: 12px;
    }
  }
}

.cursor-default-canvas canvas {
  cursor: default;
}

.common-form {
  display: grid;
  gap: 16px 24px;
  margin-bottom: 20px;
  .el-form-item {
    margin: 0;
    .el-form-item__label {
      font-size: 14px;
      color: #595959;
      padding-right: 8px;
    }
    .el-form-item__content {
      white-space: nowrap;
      align-items: flex-start;
      flex-wrap: nowrap;
    }
    .el-form-item__label-wrap {
      margin: 0 !important;
    }
    .el-date-editor .el-range-input {
      color: #262626;
    }
    .el-date-editor .el-range__icon {
      margin-right: 10px;
    }
    .el-select .el-select__tags .el-tag--info {
      color: #262626;
    }
  }
}

.echarts-tooltip span {
  line-height: 18px;
}
