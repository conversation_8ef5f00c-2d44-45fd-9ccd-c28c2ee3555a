import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { defineComponent, h } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { i18n } from '~/i18n'
import store from '~/store'
const views = import.meta.globEager(`../views/*/index.vue`)
const viewsOwl = import.meta.globEager(`../views/owl/*/index.vue`)
const routes = [
  { path: '/', redirect: 'home' },
  { path: '/&response_type=code', redirect: 'home' },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/components/login.vue')
  },
  {
    path: '/redirect',
    name: 'redirect',
    component: defineComponent({ render: () => h('div') })
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})
/**
 * @description: 动态路由添加开始
 * @return {*}
 */
// 记录路由是否添加
let registerRouteFresh = true

/**
 * @description: 生成路由规则
 * @param {any} list 后端返回的菜单列表
 * @return {*} 处理过的菜单可以直接添加到路由
 */
function menusProcess(list: any, parentPath?: string) {
  const menus: any[] = []
  if (!list) {
    return []
  }
  list.forEach((item: any) => {
    const comment = item.comment
    const identifier = item.identifier
    if (comment) {
      const identifierArr = identifier.split(':')
      const idenLastPath = identifierArr.length - 1

      const commentArr = comment.split('/')
      const lastPath = commentArr.length - 1
      const firstPath = commentArr[1]
      // const routePath = commentArr[lastPath];
      const routePath = identifierArr[idenLastPath]

      let componentPath = `../views/${routePath}/index.vue`
      if (firstPath === 'owl') {
        componentPath = `../views/${firstPath}/${routePath}/index.vue`
      }
      let module: any = views[componentPath]
      if (firstPath === 'owl') {
        module = viewsOwl[componentPath]
      }
      const childrenVal: string[] = []
      const nameObj = {
        name: ''
      }
      let menu = {
        path: comment,
        component: null,
        children: childrenVal
      }
      if (module) {
        const $component = module.default
        menu.component = $component
      }
      if (item.children && item.children.length > 0) {
        menu.children = menusProcess(item.children, firstPath)
      }
      if (parentPath) {
        nameObj.name = `${parentPath}-${routePath}-${lastPath}`
      } else {
        nameObj.name = `${routePath}-${lastPath}`
      }
      menu = { ...menu, ...nameObj }

      menus.push(menu)
    }
  })
  return menus
}

// 路径发生变化的映射表
const changedRouteMap = {
  '/owl/battery-history': '/info-trace/battery-image',
  '/fault-diagnosis/service-trace': '/info-trace/service-trace',
  '/fault-diagnosis/battery-swap': '/info-trace/battery-swap',
  '/fault-diagnosis/process-error': '/info-trace/process-error',
  '/fault-diagnosis/fire-alarm': '/info-trace/fire-alarm',
  '/fault-diagnosis/diagnosis-info': '/info-trace/diagnosis-info',
  '/fault-diagnosis/log-analysis': '/info-trace/log-analysis',
  '/owl/temperature-feature': '/fault-diagnosis/temperature-feature',
  '/device-health': '/fault-diagnosis/device-health',
  '/powerCharge4/log-export': '/powerCharge/log-export',
} as any

/**
 * @description:路由加载之前获取菜单
 * @param {*} async
 * @param {*} from
 * @param {*} next
 * @return {*}
 */
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const newRoute = changedRouteMap[to.path]
  if (registerRouteFresh) {
    const menuRes = await store.dispatch('menus/getMenusList')
    await store.dispatch('user/getAdministrator')
    await store.dispatch('vehicle/getBasicList')
    if (menuRes) {
      const menus = menuRes.filter((item: any) => item.identifier === 'portal:welkin:menu')[0].children
      const menusList: any = menusProcess(menus.filter((item: any) => item.identifier != 'function:eu'))
      menusList.forEach((val: any) => {
        router.addRoute(val)
      })
      console.log('生成路由规则', menusList)
    }
    router.addRoute({
      path: '/:pathMatch(.*)',
      name: '401',
      component: () => import('../views/components/401.vue')
    })
    next({ ...to, replace: true })
    registerRouteFresh = false
  } else if (to.fullPath.includes('&response_type=code')) {
    const newPath = to.fullPath.replace('&response_type=code', '')
    next(newPath)
  } else if(newRoute) {
    ElMessage.warning(i18n.global.t('common.pp_path_change'))
    next({ path: newRoute, query: to.query })
  } else {
    next()
  }
})

router.afterEach((_to, _from) => {
  NProgress.done()
})
export default router
