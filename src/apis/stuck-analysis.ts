import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 挂车告警下拉列表
 * @param {any} project
 * @param {any} query
 * @return {*}
 */
export const apiGetAlarmIdList = (query: any, project: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/${project}/alarm_id` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询告警详情信息
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetAlarmDetailList = (query: any, project: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/${project}/alarm` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 挂车告警列表信息
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetAlarmList = (query: any, project: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/${project}/alarm/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 单一告警分布信息，前端做分页
 * @param {any} query
 * @param {any} project
 * @param {any} alarmId
 * @return {*}
 */
export const apiGetSingleAlarmList = (query: any, project: any, alarmId: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/${project}/alarm/${alarmId}` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: Jira列表
 * @param {any} query
 * @return {*}
 */
export const apiGetJiraList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/jira/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 挂车订单所有告警
 * @param {any} query
 * @param {any} project
 * @param {any} deviceId
 * @return {*}
 */
export const apiGetAllAlarmList = (query: any, project: any, deviceId: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/alarm/${project}/${deviceId}/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询看板信息
 * @param {any} query
 * @return {*}
 */
export const apiGetDashboardList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/stat_panel` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询设备挂车排行
 * @param {any} query
 * @return {*}
 */
export const apiGetDeviceRankList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/device_stat/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询订单
 * @param {any} query
 * @return {*}
 */
export const apiGetOrderList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/service` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 分析结果上传
 * @param {any} query
 * @return {*}
 */
export const apiPostAnalysis = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/diagnosis/v1/stuck/service/update`,
    data: query
  }).then((res: any) => {
    return res
  })
}

/**
 * @description: 查询换电步骤挂车率
 * @param {any} query
 * @return {*}
 */
export const apiGetStepList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/stuck/service_step` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 全人员查询
 * @param {any} query
 * @return {*}
 */
export const apiGetUserList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/internal/v1/people/query` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 上传设备csv文件
 * @param {any} query
 * @return {*}
 */
export const apiPostFile = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/internal/v1/csv_parse`,
    data: query,
    headers: {
      'content-type': 'multipart/form-data'
    }
  }).then((res: any) => {
    return res.data
  })
}