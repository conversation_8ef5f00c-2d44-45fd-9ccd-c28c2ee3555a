import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 查看配置列表
 * @param {any} query
 * @return {*}
 */
export const apiGetConfigList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/psos/config/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询单站真实配置
 * @param {any} project
 * @param {any} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetRealDeviceConfig = (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/psos/config/real-device/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 电池快速配置生成
 * @param {any} query
 * @return {*}
 */
export const apiGetQuickBattery = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/psos/battery-config/generate` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 订单快速配置
 * @param {any} query
 * @return {*}
 */
export const apiGetQuickOrder = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/psos/service-list/generate` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 设备批量导入
 * @param {any} query
 * @return {*}
 */
export const apiPostDeviceFile = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/devices/upload-csv`,
    data: query,
    headers: {
      'content-type': 'multipart/form-data'
    }
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 新建/编辑配置，单配方
 * @param {any} query
 * @return {*}
 */
export const apiPostSingleConfig = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/algorithm/v1/psos/config/single`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 新建配置，批量生成
 * @param {any} query
 * @return {*}
 */
export const apiPostBatchConfig = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/algorithm/v1/psos/config/batch`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 删除配置
 * @param {any} configId
 * @return {*}
 */
export const apiDeleteConfig = (configId: any) => {
  return axiosWithAlert({
    method: 'delete',
    url: `/web/welkin/algorithm/v1/psos/config/${configId}`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 编辑时查询配置详细信息
 * @param {any} configId
 * @return {*}
 */
export const apiGetConfigDetail = (configId: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/psos/config/${configId}/detail`
  }).then((res: any) => {
    return res.data
  })
}
