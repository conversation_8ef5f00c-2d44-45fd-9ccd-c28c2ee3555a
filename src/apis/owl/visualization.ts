/*
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 17:30:53
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-10 17:33:17
 * @Email: <EMAIL>
 * @Description: 迁移
 */
import { toCamelCase } from '~/utils'
import axios from 'axios'

/**
 * 设备模型信息
 * http://pp-yapi.nioint.com/project/84/interface/api/677
 */
export const apiGetDeviceModelInfo2 = () => {
  return axios.get('/owl/v1/wl/baderdata').then((res) => {
    const data = res.data
    const camelData = toCamelCase(data)
    if (camelData.resultCode === 0) {
      return camelData.data
    }
    return
  })
}
