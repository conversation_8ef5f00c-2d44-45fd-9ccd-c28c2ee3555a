import { toCamelCase, toQueryString } from '~/utils';
import axios from 'axios';

/**
 * 设备列表
 * http://pp-yapi.nioint.com/project/84/interface/api/668
 */
export const apiGetDeviceList = (query: any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/device/list' + param).then((res) => {
    const data = res.data;
    const camelData = toCamelCase(data);
    if (camelData.errorCode === 0) {
      return camelData.data;
    }
    return;
  });
};

//车型信息拉取
export const apiGetVehicleType = () => {
  return axios.get('/web/owl/v1/vehicle/types/info').then((res) => {
    return res.data;
  });
};

/**
 * 设备模型信息
 * http://pp-yapi.nioint.com/project/84/interface/api/677
 */
export const apiGetDeviceModelInfo = (query: any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/device/model/info' + param).then((res) => {
    const data = res.data;
    const camelData = toCamelCase(data);
    if (camelData.resultCode === 0) {
      return camelData.data;
    }
    return;
  });
};

/**
 * 模型版本历史
 * http://pp-yapi.nioint.com/project/84/interface/api/704
 */
export const apiGetModelVersionList = (query: any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/model/version/list' + param).then((res) => {
    const data = res.data;
    const camelData = toCamelCase(data);
    if (camelData.resultCode === 0) {
      return camelData.data;
    }
    return;
  });
};

/**
 * 单个模型运行结果
 * http://pp-yapi.nioint.com/project/84/interface/api/686
 */
export const apiGetModelResultList = (query: any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/model/result/list' + param).then((res) => {
    const data = res.data;
    const camelData = toCamelCase(data);
    if (camelData.resultCode === 0) {
      return camelData.data;
    }
    return;
  });
};

/**
 * 获取当前设备步骤号
 * http://pp-yapi.nioint.com/project/84/interface/api/695
 */
export const apiGetDeviceStep = (query: any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/device/step' + param).then((res) => {
    const data = res.data;
    const camelData = toCamelCase(data);
    if (camelData.resultCode === 0) {
      return camelData.data;
    }
    return;
  });
};

/**
 * 获取WL TOP10到位成功率
 */
export const apiGetTOP10Station = () => {
  return axios.get('/web/owl/v1/wl/baderdata').then((res) => {
    return res.data;
  });
};
/**
 * 获取WL 右下角成功率
 */
export const apiGetSuccessRate = () => {
  return axios.get('/web/owl/v1/wl/data').then((res) => {
    return res.data;
  });
};
//获取图片回传量和站点回传量接口
export const GetPictureAndStation = (groupId: any) => {
  return axios
    .get(`/web/owl/v1/algorithm/count/list?group_id=${groupId}`)
    .then((res) => {
      // console.log(res.data)
      return res.data;
    });
};
// 获取流量用完站点名单
export const apiGetRunsOutTraffic = () => {
  return axios.get('/web/owl/v1/traffic/devices').then((res) => {
    const data = res.data;
    const camelData = toCamelCase(data);
    if (camelData.errorCode === 0) {
      return camelData.data;
    }
    return;
  });
};
// 获取minio图片返回列表
export const apiGetMinioPicList = (query: any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/query/images' + param).then((res) => {
    return res;
  });
};
// 打包下载
export const apiGetZipDownload = (query: any) => {
  const param = toQueryString(query);
  window.open('/web/owl/v1/download/zipimages' + param);
};
// 单张预览
export const apiSinglePreview = (query: any) => {
  const param = toQueryString(query);
  return '/web/owl/v1/preview/image' + param;
};
// 单张下载
export const apiSingleDownload = (query: any) => {
  const param = toQueryString(query);
  window.open('/web/owl/v1/download/image' + param);
};
// 获取数据下拉框
export const apiGetAlgorithmName = () => {
  return axios.get('/web/owl/v1/algorithms/name/info').then((res) => {
    return res.data;
  });
};
// faq 拉取
export const apiFaqFetch = (query = '' as any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/qa' + param).then((res) => {
    return res.data;
  });
};
// 电池图片打包下载
export const apiBatteryZipDownload = (query?: any) => {
  const param = toQueryString(query);
  window.open('/web/owl/v1/download/battery/images' + param);
};
// 电池单张下载
export const apiBatterySingleDownload = (url: any) => {
  window.open('/web/owl/v1/preview/history/image?url=' + url);
};
// 算法图片上传
export const apiFlowChartUpload = (figure: any) => {
  return axios
    .post('/web/owl/v1/flow/diagram', figure, {
      headers: {
        'content-type': 'multipart/form-data',
      },
    })
    .then((res) => {
      return res.data;
    });
};
// 算法图片下载
export const apiAlgorithmReview = (query?: any) => {
  return '/web/owl/v1/flow/diagram?algorithm=algorithm';
};
//电池历史
export const apiBatteryHistoryFetch = (query: any) => {
  const param = toQueryString(query);
  return axios.get('/web/owl/v1/battery/image' + param).then((res) => {
    return res.data;
  });
};

//新增大版本号
export const apiPublishVersionFetch = (project: any, param: any) => {
  return axios
    .post(
      `/web/welkin/algorithm/v1/version-management/publish-version/${project}`,
      param
    )
    .then((res) => {
      return res.data;
    });
};
//编辑大版本号和新增算法
export const apiEditVersionFetch = (project: any, version: any, param: any) => {
  return axios
    .put(
      `/web/welkin/algorithm/v1/version-management/version/${project}/${version}`,
      param
    )
    .then((res) => {
      return res.data;
    });
};

//版本号管理列表
export const apiListFetch = (project: any, query: any) => {
  const param = toQueryString(query);
  return axios
    .get(
      `/web/welkin/algorithm/v1/version-management/version/algorithm-data/${project}` +
        param
    )
    .then((res) => {
      return res.data;
    });
};

//算法编辑
export const apiEditAlgorithmFetch = (
  project: any,
  version_id: any,
  algorithm_id: any,
  param: any
) => {
  return axios
    .put(
      `/web/welkin/algorithm/v1/version-management/version/algorithm-data/${project}/${version_id}/${algorithm_id}`,
      param
    )
    .then((res) => {
      return res.data;
    });
};

//获取算法表头
export const apiTableHeadFetch = (query?: any) => {
  const param = query ? toQueryString(query) : '';
  console.log(param);
  return axios
    .get(
      `/web/welkin/algorithm/v1/version-management/version/algorithm-data/algorithm-name/list` +
        param
    )
    .then((res) => {
      return res.data;
    });
};

//获取所有发布时间
export const apiPublistTsFetch = () => {
  return axios
    .get(`/web/welkin/algorithm/v1/data-versioning/publish-ts/list`)
    .then((res) => {
      return res.data;
    });
};

//获取data versioning 数据
export const apiDataVersionFetch = (query?: any) => {
  const param = query ? toQueryString(query) : '';
  return axios
    .get(`/web/welkin/algorithm/v1/data-versioning` + param)
    .then((res) => {
      return res.data;
    });
};

//上传数据集
export const apiUploadDataSet = (figure: any, cb: Function) => {
  return axios
    .post('/web/welkin/algorithm/v1/data-versioning/dataset/info', figure, {
      headers: {
        'content-type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        cb(progressEvent);
      },
    })
    .then((res) => {
      return res.data;
    });
};

//上传数据集成功二次确认接口
export const apiDataSetConfirm = (param: any) => {
  return axios
    .post(`/web/welkin/algorithm/v1/data-versioning/dataset/confirm`, param)
    .then((res) => {
      return res.data;
    });
};

//创建质量管理数据集
export const apiQmDataSet = (figure: any, cb: Function) => {
  return axios
    .post('/web/welkin/algorithm/v1/data-versioning/qm/dataset', figure, {
      headers: {
        'content-type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        cb(progressEvent);
      },
    })
    .then((res) => {
      return res.data;
    });
};

//上传测试报告
export const apiTestReportFetch = (
  project: any,
  algorithm_id: any,
  param: any
) => {
  return axios
    .put(
      `/web/welkin/algorithm/v1/version-management/version/algorithm-data/test-report/${project}/${algorithm_id}`,
      param
    )
    .then((res) => {
      return res.data;
    });
};

//删除文件
export const apiDelFileFetch = (param: any) => {
  console.log(param);
  return axios
    .delete('/web/welkin/algorithm/v1/data-versioning/file', {
      data: param,
    })
    .then((res) => {
      return res.data;
    });
};
