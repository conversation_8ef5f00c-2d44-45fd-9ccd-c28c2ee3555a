import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询图片类型
 * @return {*}
 */
export const apiGetImageType = () => {
  const param = {lang: localStorage.getItem('locale')}
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/image/v1/type/list`,
    params: param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取图片列表
 * @param {any} query
 * @return {*}
 */
export const apiGetImageList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/battery-history/image` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载图片
 * @param {any} query
 * @return {*}
 */
export const apiDownloadImage = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  window.open(`/web/welkin/device/v1/battery-history/image/download` + param)
}
