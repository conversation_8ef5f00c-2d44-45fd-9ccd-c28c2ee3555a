/*
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 11:26:44
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-10 11:32:20
 * @Email: <EMAIL>
 * @Description: 迁移owl
 */
import axios from 'axios';
import { getUserId, generateUUID } from '~/utils';


/**
 * @param options axios配置的参数
 * @param that 传入this对象
 */
const axiosWithAlert = (options: any, that?: any) => {
  const user_id = getUserId();
  return new Promise((resolve, reject) => {
    axios({
      ...options,
      headers: {
        'X-User-ID': user_id || 'unknown',
        'X-Request-ID': generateUUID()
      },
    })
      .then((res: any) => {
        resolve(res);
      })
      .catch((err: any) => {
        reject(err);
      });
  });
};

export { axiosWithAlert };
