import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询车牌数据
 * @param {any} query
 * @return {*}
 */
export const apiGetLicenseList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/lpr/data-report/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载车牌数据
 * @param {any} query
 * @return {*}
 */
export const apiDownloadLicenseList = (query: any) => {
  const param = toQueryString(query)
  window.open('/web/welkin/algorithm/v1/lpr/data-report/list' + param)
}

/**
 * @description: 查询图片
 * @param {string} project
 * @param {string} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetLicenseImageList = (project: string, deviceId: string, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/image/v1/${project}/${deviceId}/list` + param
  }).then((res: any) => {
    return res.data
  })
}
