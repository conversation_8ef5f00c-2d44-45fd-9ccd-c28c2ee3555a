/*
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 17:22:29
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-10 17:22:39
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
import axios from 'axios'

// 获取PIE信息
export const apiGetPieInfo = () => {
  return axios.get('/web/owl/v1/pie/info').then((res) => {
    return res.data
  })
}

// 上传PIE信息
export const apiPostPieInfo = (query: any) => {
  return axios.post('/web/owl/v1/pie/info', query).then((res) => {
    return res
  })
}

// 删除PIE信息
export const apiDeletePieInfo = (query: any) => {
  return axios.delete('/web/owl/v1/pie/info?name=' + query).then((res) => {
    return res
  })
}

// 更新PIE信息
export const apiPutPieInfo = (query: any) => {
  return axios.put('/web/owl/v1/pie/info', query).then((res) => {
    return res
  })
}
