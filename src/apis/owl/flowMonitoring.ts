import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 查询数据量统计数据
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetStats = (query: any, project: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/activity/${project}/stats` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询流量用完站点名单
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetDevices = (query: any, project: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/activity/${project}/devices` + param
  }).then((res: any) => {
    return res.data
  })
}