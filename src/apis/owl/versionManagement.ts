/*
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 17:30:14
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-10 17:30:26
 * @Email: <EMAIL>
 * @Description: 
 */
import axios from 'axios'

// 上传版本信息
export const apiPostVersionInfo = (query: any) => {
  return axios.post('/web/owl/v1/version/info', query).then((res) => {
    return res
  })
}

// 获取版本信息
export const apiGetVersionInfo = (query: any) => {
  return axios.get('/web/owl/v1/version/info?item_number=' + query).then((res) => {
    return res.data
  })
}

// 修改AEC版本号
export const apiPutVersionId = (query: any) => {
  return axios.put('/web/owl/v1/version/info', query).then((res) => {
    return res
  })
}

// 修改软件发布时间
export const apiPutPublishTime = (query: any) => {
  return axios.put('/web/owl/v1/version/info', query).then((res) => {
    return res
  })
}

// 修改算法
export const apiPutAlgorithm = (query: any) => {
  return axios.put('/web/owl/v1/version/info', query).then((res) => {
    return res
  })
}

// 拉取设备项目列表
export const apiGetItemInfo = () => {
  return axios.get('/web/owl/v1/item/info').then((res) => {
    return res.data
  })
}
