import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description:获取区域公司设备列表映射
 * @return {*}
 */
export const apiGetCityCompanyMapping = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/city_company_mapping` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description:泊车占位告警列表
 * @param {any} query
 * @return {*}
 */
export const apiGetAlarmList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/sapa/alarm/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description:获取泊车占位看板
 * @param {any} query
 * @return {*}
 */
export const apiGetDashboardList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/sapa/stat_panel` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载泊车占位看板数据
 * @param {any} query
 * @return {*}
 */
export const apiDownloadBoard = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  window.open(`/web/welkin/algorithm/v1/sapa/stat_panel/download` + param)
}

/**
 * @description: 下载泊车占位告警列表数据
 * @param {any} query
 * @return {*}
 */
export const apiDownloadTable = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  window.open(`/web/welkin/algorithm/v1/sapa/alarm/download` + param)
}
