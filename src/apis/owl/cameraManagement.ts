/*
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 17:21:48
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-10 17:22:12
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
import axios from 'axios'
import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'
import {handleDeviceNameNull, getUserId} from '~/utils'
import project from '~/store/modules/project'

// 获取摄像头信息
export const apiGetAllInfo = (project: string, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axios.get(`/web/welkin/image/v1/${project}/camera/device` + param).then((res) => {
    return res.data
  })
}

// 获取站点信息
export const apiGetStationInfo = (project: string, query: any) => {
  return axios.get(`/web/welkin/device/v1/devices/list/${project}?area=` + query).then((res) => {
    return res.data
  })
}

// 获取摄像头信息
export const apiGetCameraInfo = (project: string) => {
  const lang = localStorage.getItem('locale')
  return axios.get(`/web/welkin/image/v1/${project}/camera/info?lang=` + lang).then((res) => {
    return res.data
  })
}

// 摄像头管理查询显示照片细节
export const apiGetCameraDetails = (project: string, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axios.get(`/web/welkin/image/v1/${project}/camera/details` + param).then((res) => {
    return res.data
  })
}

// 一键导出数据生成表格
export const apiDownload = (project: string, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axios.get(`/web/welkin/image/v1/${project}/camera/table/info` + param).then((res) => {
    return res.data
  })
}

// 人工判定
export const apiJudge = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/image/v1/${project}/camera/judge`,
    data: query,
  }).then((res: any) => {
    return res
  })
}

// 获取设备id和名称
export const apiGetDeviceNameMap = (project: string) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/${project}/name-mapping`
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data)
    }
    return res.data
  })
}

//清除摄像头黑名单
export const apiClearBlack = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/image/v1/${project}/camera/clear/blacklist`,
    data: query
  }).then((res: any) => {
    return res
  })
}

/**
 * @description: 查询验收结果
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetAcceptanceResult = (project: string, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/image/v1/${project}/camera/acceptance-result` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 发送验收结果
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiPostResult = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/image/v1/${project}/camera/acceptance-result`,
    data: query
  }).then((res: any) => {
    return res
  })
}
