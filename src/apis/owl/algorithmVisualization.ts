/*
 * @Author: liu.yang
 * @Email: <EMAIL>
 * @Date: 2023-04-25 10:32:14
 * @LastEditors: liu.yang
 * @LastEditTime: 2023-04-27 13:57:35
 * @Description: 算法可视化接口
 */

import { axiosWithAlert } from './axios';
import { toQueryString } from '~/utils';

/**
 * @description: 获取算法可视化列表
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetAlgorithmList = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/algorithm-visual/${project}/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};

/**
 * @description: 获取所有车辆类型和电池类型
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetVehicleAndBattery = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/algorithm-visual/vehicle-battery/${project}/list` + param,
  }).then((res: any) => {
    return res;
  });
};

/**
 * @description: 获取所有站点成功率
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetVisualSuccessRate = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/algorithm-visual/success-rate/${project}/all` + param,
  }).then((res: any) => {
    return res;
  });
};

/**
 * @description: 获取成功率变化趋势
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetSuccessRateTrend = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/algorithm-visual/success-rate/${project}/trend` + param,
  }).then((res: any) => {
    return res.data;
  });
};


/**
 * @description: 获取成功率最低TOP10站点
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetTop10SuccessRate = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/algorithm-visual/success-rate/${project}/lowest` + param,
  }).then((res: any) => {
    return res.data;
  });
};

/**
 * @description: 获取昨日算法FTT
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetFttData = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/ftt/${project}` + param,
  }).then((res: any) => {
    return res.data;
  });
};

/**
 * @description: 获取AEC运行状况
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetAecData = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/algorithm/v1/aec/${project}/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};
