/*
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 17:25:26
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-10 17:25:37
 * @Email: <EMAIL>
 * @Description: 迁移
 */
import axios from 'axios'
import { toQueryString } from '~/utils'

// 拉取枪温看板信息
export const apiGetTemperatureInfo = (query: any) => {
  const param = toQueryString(query)
  return axios.get('/web/owl/v1/gun/temperature/info' + param).then((res) => {
    return res.data
  })
}

// 拉取枪线设备
export const apiGetGunInfo = () => {
  return axios.get('/web/owl/v1/gun/connector/info').then((res) => {
    return res.data
  })
}

// 拉取车品牌接口
export const apiGetBrandInfo = () => {
  return axios.get('/web/owl/v1/vehicle/brand/info').then((res) => {
    return res.data
  })
}
