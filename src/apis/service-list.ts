import { toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';
import { handleDeviceNameNull } from '~/utils';

// 获取服务列表数据
export const apiGetServiceList = (query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/service-info/${project}/list` + param,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

// 根据服务id获取图片
// 老街口，现在不用了-> apiGetImageList，多传参service_id
export const apiGetServiceImageList = (query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/img/v1/${project}` + param,
  }).then((res: any) => {
    return res.data;
  });
};

// 查询服务详情
export const apiGetServiceDetail = (
  query: any,
  project: any,
  device_id: any,
  service_id: any
) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/device/v1/service-info/${project}/${device_id}/${service_id}/details` +
      param,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};
