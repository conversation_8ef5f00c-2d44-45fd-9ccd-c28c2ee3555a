/*
 * @Author: zhenxing.chen
 * @Date: 2022-11-04 19:45:30
 * @LastEditors: minghan.ma <EMAIL>
 * @LastEditTime: 2024-06-18 11:02:31
 * @Email: <EMAIL>
 * @Description: 修改请求方式
 */
import axios from 'axios';
import { toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';
import { handleDeviceNameNull } from '~/utils';

// 获取设备列表
export const apiGetDeviceList = (query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/${project}` + param,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

// 获取设备id和名称
export const apiGetDeviceNameMap = (project: string, query = {}) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/${project}/name-mapping`,
    params: query,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};
