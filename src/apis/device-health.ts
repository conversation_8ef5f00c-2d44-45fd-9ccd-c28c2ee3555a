import { axiosWithAlert } from './axios'
import { toQueryString } from '~/utils'
import { handleDeviceNameNull } from '~/utils'

// 获取设备健康度
export const apiGetHealthtData = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/devices` + param
  }).then((res: any) => {
    return res.data
  })
}

// 获取尾部站点
export const apiGetStationData = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/tail` + param
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data)
      return res.data
    }
  })
}

// 获取设备明细健康度
export const apiGetDetailData = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/devices/detail` + param
  }).then((res: any) => {
    return res.data
  })
}
// 获取设备id和名称
export const apiGetDeviceNameMap = (project: any, query = {}) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/${project}/name-mapping`,
    params: query
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data)
    }
    return res.data
  })
}
// 获取区域公司
export const apiCompanyMap = (query = {}) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/city_company_mapping`,
    params: query
  }).then((res: any) => {
    return res.data
  })
}
// 获得下载
export const apiGetDownload = (project: string, query: any) => {
  const param = toQueryString(query)
  window.open(`/web/welkin/diagnosis/v1/health/${project}/devices/detail` + param)
}

/**
 * @description: 获取单站健康度详情
 * @param {string} project
 * @param {string} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetSingleStationHealth = (project: string, deviceId: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/${deviceId}/trend` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 伺服健康度详情
 * @param {string} project
 * @param {string} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetServo = (project: string, deviceId: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/${deviceId}/servo` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 充电模块健康度详情
 * @param {string} project
 * @param {string} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetCharge = (project: string, deviceId: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/${deviceId}/charge` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 传感器健康度详情
 * @param {string} project
 * @param {string} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetSensor = (project: string, deviceId: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/${deviceId}/sensor` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 工单列表
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetWorksheet = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/worksheet/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 工单统计信息
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetWorksheetStatistics = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/health/${project}/worksheet/statistics` + param
  }).then((res: any) => {
    return res.data
  })
}
