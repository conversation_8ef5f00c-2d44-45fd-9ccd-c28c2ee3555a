import { toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';
import { tableData, descData } from '~/views/fault-list/data';
// 获取故障列表数据
export const apiGetFaultList = (project:string, query: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/troubleshooting/${project}/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};

// 获取快照诊断数据
export const apiGetSnapshotDiagnosis = (
  project: string,
  deviceId: any,
  requestId: any,
  query: any
) => {
  const param = toQueryString(query);
  // return Promise.resolve({ data: descData });
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/diagnosis/v1/troubleshooting/${project}/snapshot/${deviceId}/${requestId}/details` +
      param,
  }).then((res: any) => {
    return res.data;
  });
};

// 获取快照诊断数据
export const apiPutSnapshotDiagnosisResult = (
  project: string,
  deviceId: any,
  _id: any,
  data: any
) => {
  return axiosWithAlert({
    method: 'put',
    url: `/web/welkin/diagnosis/v1/troubleshooting/${project}/snapshot/${deviceId}/${_id}`,
    data: data,
  }).then((res: any) => {
    return res.data;
  });
};
