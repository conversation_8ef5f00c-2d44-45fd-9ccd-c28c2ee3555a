import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 获取双向站看板数据
 * @param {any} query
 * @return {*}
 */
export const apiGetBiInfo = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/bidirectional/info` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取双向站列表
 * @return {*}
 */
export const apiGetBiDeviceList = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/bidirectional/list`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取看板订阅信息
 * @return {*}
 */
export const apiGetBiSubscription = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/bidirectional/subscription`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载看板数据
 * @param {any} query
 * @return {*}
 */
export const apiDownload = (query: any) => {
  const param = toQueryString(query)
  window.open(`/web/welkin/device/v1/bidirectional/info/download` + param)
}

/**
 * @description: 修改看板订阅信息
 * @param {any} query
 * @return {*}
 */
export const apiPostSubscription = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/bidirectional/subscription`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}
