import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询诊断信息列表
 * @param {any} query
 * @return {*}
 */
export const apiGetDiagnosisList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/groot` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 远程获取groot设备
 * @param {any} query
 * @return {*}
 */
export const apiGetGrootList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/groot/device_id` + param
  }).then((res: any) => {
    return res.data
  })
}
