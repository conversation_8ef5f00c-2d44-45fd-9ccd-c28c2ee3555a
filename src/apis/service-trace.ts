import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询服务列表
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetServiceList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/battery-history/service` + param
  }).then((res: any) => {
    return res.data
  })
}