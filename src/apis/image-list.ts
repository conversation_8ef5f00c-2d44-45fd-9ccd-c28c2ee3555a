/*
 * @Author: zhenxing.chen
 * @Date: 2022-11-04 19:45:30
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2022-11-04 21:01:10
 * @Email: <EMAIL>
 * @Description: 修改请求方式
 */
import axios from 'axios';
import { toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';

// 图片列表获取
export const apiGetImageList = (query: any, deviceId: any, project: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/image/v1/${project}/${deviceId}/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};

export const apiGetImageType = () => {
  const lang = localStorage.getItem('locale')
  return axiosWithAlert({
    method: 'get',
    url: '/web/welkin/image/v1/type/list?lang=' + lang,
  }).then((res: any) => {
    return res.data;
  });
};
