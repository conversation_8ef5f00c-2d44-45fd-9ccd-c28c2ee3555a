import { axiosWithAlert } from './axios'
import { toQueryString } from '~/utils'

/**
 * @description: 订单状态、结束原因
 * @return {*}
 */
export const apiGetStatusOptions = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/power_charger/order/enum`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 充电订单列表
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetChargeOrderList = (query: any, project: string) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/power_charger/${project}/order/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 订单详情的基本信息
 * @param {any} project
 * @param {any} orderId
 * @return {*}
 */
export const apiGetChargeOrderInfo = (project: any, orderId: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/power_charger/${project}/order/${orderId}`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查看事件过程
 * @param {any} project
 * @param {any} orderId
 * @return {*}
 */
export const apiGetChargeEvent = (project: any, orderId: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/power_charger/${project}/order/${orderId}/events`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 充电实时数据
 * @param {any} project
 * @param {any} orderId
 * @return {*}
 */
export const apiGetRealtimeData = (project: any, orderId: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/power_charger/${project}/order/${orderId}/realtime`
  }).then((res: any) => {
    return res.data
  })
}
