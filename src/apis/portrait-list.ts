import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 查看换电画像详情
 * @param {any} project
 * @param {any} orderId
 * @return {*}
 */
export const apiGetDetailInfo = (project: any, orderId: any) => {
  const params = { lang: localStorage.getItem('locale') }
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v2/service-visual/${project}/${orderId}/detail`,
    params: params
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 订单列表
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetOrderList = (query: any, project: string) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/service-visual/${project}/list` + param
  }).then((res: any) => {
    return res.data
  })
}
