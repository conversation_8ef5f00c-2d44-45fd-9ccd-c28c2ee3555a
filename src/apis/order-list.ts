import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询全量下发记录
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetAllStationList = (query: any, project: string) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/power-params-record` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询单站下发记录
 * @param {any} query
 * @param {string} project
 * @param {string} deviceId
 * @return {*}
 */
export const apiGetSingleStationList = (query: any, project: string, deviceId: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/${deviceId}/power-params-record` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 创建
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiPostCreateParams = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/new-power-params`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 编辑
 * @param {string} project
 * @param {any} commandId
 * @param {any} query
 * @return {*}
 */
export const apiPostEditParams = (project: string, commandId: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/new-power-params/${commandId}`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下发
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiPostPushParams = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/oss/command/issue`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 撤销
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiPostRevokeParams = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/oss/command/revoke`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}
