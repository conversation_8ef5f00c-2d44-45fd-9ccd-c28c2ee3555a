/*
 * @Author: zhenxing.chen
 * @Date: 2024-01-09 16:18:43
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2024-01-10 11:26:36
 * @Email: <EMAIL>
 * @Description: 跳转sso逻辑修改
 */
import axios from 'axios';
import { toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';

// 查询用户信息
export const apiGetUserInfo = (query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/system/user-info/${project}/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};

// 创建用户
export const apiPostUserInfo = (userId: any, query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/core/system/user-info/${project}/${userId}`,
    data: query,
    headers: {
      'content-type': 'application/json',
    },
  }).then((res: any) => {
    return res;
  });
};

// 编辑用户
export const apiPutUserInfo = (userId: any, query: any, project: any) => {
  return axiosWithAlert({
    method: 'put',
    url: `/web/welkin/core/system/user-info/${project}/${userId}`,
    data: query,
    headers: {
      'content-type': 'application/json',
    },
  }).then((res: any) => {
    return res;
  });
};

// 删除用户
export const apiDeleteUserInfo = (userId: any, project: any) => {
  return axiosWithAlert({
    method: 'delete',
    url: `/web/welkin/core/system/user-info/${project}/${userId}`,
    headers: {
      'content-type': 'application/json',
    },
  }).then((res: any) => {
    return res;
  });
};

// 获取菜单权限列表
export const apiGetMenuList = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/system/menus/list`,
  })
    .then((res: any) => {
      return res.data;
    })
    .catch((err) => {
      console.log('apiGetMenuList err', err);
    });
};

// 退出登录
export const apiLogout = () => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/core/system/sso/logout`,
    headers: {
      'content-type': 'application/json',
    },
  }).then((res: any) => {
    return res;
  });
};
// 获SSO授权
export const apiGetAccessToken = (query: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/system/sso/accessToken?code=${query}`,
  })
    .then((res: any) => {
      return res.data;
    })
    .catch((err) => {
      console.log('apiGetAccessToken err', err);
    });
};
