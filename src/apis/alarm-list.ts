import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 查询全量告警信息
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetAllAlarmList = (query: any, project: string) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/alarm/${project}/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载告警数据
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiDownloadAlarm = (query: any, project: string) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  const pathUrl = `/web/welkin/diagnosis/v1/alarm/${project}/list` + param
  window.open(pathUrl)
}

/**
 * @description: 查询告警描述信息点
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetPointList = (query: any, project: string) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/alarm/${project}/dataIdList` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 告警区间分析
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetAnalysis = (query: any, project: string) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/alarm/${project}/section_analysis` + param
  }).then((res: any) => {
    return res.data
  })
}
