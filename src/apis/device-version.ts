import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 版本分布看板
 * @param {string} project
 * @return {*}
 */
export const apiGetDashboard = (project: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/version/${project}/overview`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下发工单的版本可选项
 * @param {any} project
 * @return {*}
 */
export const apiGetVersionOptions = (project: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/version/${project}/prod-plm`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 版本列表自定义筛选项
 * @param {string} project
 * @return {*}
 */
export const apiGetFilterOption = (project: string) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/version/${project}/filter-option`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 设备版本列表
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetVersionList = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/version/${project}/list`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 加入/移出黑名单
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiPostBlacklist = (project: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/version/${project}/blacklist`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 判断站点版本是否为目标版本
 * @param {any} project
 * @param {any} query
 * @return {*}
 */
export const apiPostCheckTargetVersion = (project: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/version/${project}/check`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下发工单
 * @param {any} project
 * @param {any} query
 * @return {*}
 */
export const apiPostOrder = (project: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/version/${project}/worksheet`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取黑名单站点列表
 * @param {any} project
 * @param {any} query
 * @return {*}
 */
export const apiPostBlackNumber = (project: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/version/${project}/blacklist/list`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}
