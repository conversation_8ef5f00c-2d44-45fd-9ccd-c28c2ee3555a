import { toQueryString, downloadResponseFile, handleDeviceNameNull } from '~/utils';
import { axiosWithAlert } from './axios';
import { i18n } from '~/i18n';
import { ElMessage } from 'element-plus';

// 查询出厂/落站测试报告
// 1: 出厂测试报告，2: 落站测试报告
export const apiGetTestReportList = (
  query: any,
  project: any,
  report_type: number = 1
) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/quality/factory/test-report/${report_type}/${project}/list` +
      param,
  }).then((res: any) => {
    return res.data;
  });
};

// 下载出厂测试报告
export const apiDownloadTestReport = (id: string) => {
  ElMessage.info(i18n.global.t('common.pp_download_message'));

  const url = `/web/welkin/quality/factory/test-report/download/${id} `;
  return axiosWithAlert({
    method: 'get',
    url: url,
    // responseType: 'blob',
  }).then((res: any) => {
    return downloadResponseFile(res, url);
  });
};

// 查询出厂/落站扭矩报告
export const apiGetTorqueReportList = (
  query: any,
  project: any,
  report_type: number = 1
) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/quality/factory/torque-report/${report_type}/${project}/list` +
      param,
  }).then((res: any) => {
    return res.data;
  });
};

// 下载出厂扭矩报告
export const apiDownloadTorqueReport = (id: string) => {
  ElMessage.info(i18n.global.t('common.pp_download_message'));

  const url = `/web/welkin/quality/factory/torque-report/download/${id} `;
  return axiosWithAlert({
    method: 'get',
    url: url,
    // responseType: 'blob',
  }).then((res: any) => {
    return downloadResponseFile(res, url);
  });
};

// 查询设备出厂状态
export const apiGetDeviceState = (project: any, query: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/factory/${project}/device-status`,
    params: query,
  }).then((res: any) => {
    return res.data;
  });
};

// 更新换电站出厂状态
export const apiPostDeviceState = (project: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/factory/${project}/device-status`,
    data: query,
  }).then((res: any) => {
    return res.data;
  });
};

// 获取stg设备
export const apiGetDeviceList = (query: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/factory`,
    params: query,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

/**
 * @description: 查询服务信息（扭矩报告）
 * @param {any} category
 * @param {any} project
 * @param {any} query
 * @return {*}
 */
export const apiGetServiceList = (category:any, project: any, query: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/quality/factory/services/${category}/${project}`,
    params: query,
  }).then((res: any) => {
    return res.data;
  });
};

/**
 * @description: 手动生成扭矩报告
 * @param {any} category
 * @param {any} project
 * @param {any} query
 * @return {*}
 */
export const apiPostManualGenerate = (category: any, project: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/quality/factory/torque-report/${category}/${project}/generate`,
    data: query,
  }).then((res: any) => {
    return res.data;
  });
};
