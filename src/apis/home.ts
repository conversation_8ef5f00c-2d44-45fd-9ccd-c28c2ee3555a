import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'
import {handleDeviceNameNull} from '~/utils'

/**
 * @description: 新增设备收藏
 * @param {any} data
 * @return {*}
 */
export const apiPutFavorites = (data: any) => {
  return axiosWithAlert({
    method: 'put',
    url: `/web/welkin/device/v1/favorites`,
    data: data
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取设备收藏
 * @param {any} query
 * @return {*}
 */
export const apiGetFavorites = (query: any) => {
  const params = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/favorites` + params
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询所有设备
 * @param {any} query
 * @return {*}
 */
export const apiGetDevices = (query: any) => {
  const params = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list_all` + params
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data)
    }
    return res.data
  })
}

/**
 * @description: 获取车辆品牌/设备类型映射等基本信息
 * @return {*}
 */
export const apiGetBasicList = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/internal/v1/basic/name-mapping`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 根据不同的key获取天宫映射表
 * @param {any} query
 * @return {*}
 */
export const apiGetMapByKey = (query: any) => {
  const params = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/internal/v1/basic/name-mapping/by-key` + params
  }).then((res: any) => {
    return res.data
  })
}
