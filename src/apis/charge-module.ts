import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询开关状态
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetSwitchList = (query: any, project: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/realtime/v1/${project}/pdu/km-status` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询电池状态
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetBatteryList = (query: any, project: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/realtime/v1/${project}/bms/soc-data` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询ACDC模块状态
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetModuleList = (query: any, project: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/realtime/v1/${project}/acdc-data` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询桩的状态
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetChargeList = (query: any, project: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/realtime/v1/${project}/sct-data` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 查询当前电表信息
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetElectricityList = (query: any, project: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/realtime/v1/${project}/pcu/electricity-data` + param
  }).then((res: any) => {
    return res.data
  })
}