import { ElMessage, ElNotification as message } from 'element-plus';
import { getEnvName } from '~/utils';
import { backend_url } from '~/constvars';
const defaultUrl = 'wss://api-welkin-backend-stg.nioint.com';

export class WlWebsocket {
  private websocket: any = null;

  private wsBaseUrl = defaultUrl;
  // 开启标识
  private socket_open = false;
  // 心跳timer
  private hearbeat_timer: any = null;
  // 心跳发送频率
  private hearbeat_interval = 8 * 1000;
  // 是否自动重连
  private is_reconnect = false;
  // 重连次数
  private reconnect_count = 3;
  // 已发起重连次数
  private reconnect_current = 1;
  // 重连timer
  private reconnect_timer: any = null;
  // 重连频率
  private reconnect_interval = 5 * 1000;

  private url = '';
  private query = '';

  constructor(url: string, query: string = '') {
    this.url = url;
    this.query = query;
    let env: any = getEnvName();
    if (!!env) {
      this.wsBaseUrl = 'wss://' + backend_url[env];
    } else {
      // this.wsBaseUrl = 'wss://api-welkin-backend-stg.nioint.com';
      this.wsBaseUrl = `ws://${location.host}/ws`;
    }
    console.log('getEnvName() env', env, this.wsBaseUrl);
  }

  init = (receiveMessage: Function | null, onSocketClose: Function | null) => {
    if (!('WebSocket' in window)) {
      message.warning('浏览器不支持WebSocket');
      return null;
    }
    const wsUrl = this.wsBaseUrl + this.url + this.query;

    console.log('==wsUrl==', wsUrl);

    this.websocket = new WebSocket(wsUrl);

    this.websocket.onmessage = (data: any) => {
      if (!!receiveMessage) {
        // console.log('==data===', data);
        const res = JSON.parse(data?.data);
        if (res.err_code != 0) {
          ElMessage.error(res?.message);
        }
        receiveMessage(JSON.parse(data?.data));
      }
    };

    this.websocket.onclose = (e: any) => {
      console.log('WebSocket连接关闭', e);
      if (!!onSocketClose) {
        onSocketClose();
      }
      this.socket_open = false;
      // 需要重新连接
      if (!!this.is_reconnect) {
        console.log(!!this.is_reconnect, 'WebSocket连接关闭 重新连接------', this.reconnect_current);
        this.reconnect_timer = setTimeout(() => {
          // 超过重连次数
          if (this.reconnect_current > this.reconnect_count) {
            clearTimeout(this.reconnect_timer);
            this.is_reconnect = false;
            return;
          }
          // 记录重连次数
          this.reconnect_current++;
          this.reconnect();
        }, this.reconnect_interval);
      }
    };

    // 连接成功
    this.websocket.onopen = () => {
      console.log('WebSocket连接成功');
      this.socket_open = true;
      // this.is_reconnect = true;
      // 开启心跳
      // this.heartbeat();
    };

    // 连接发生错误
    this.websocket.onerror = (e: any) => {
      console.log('WebSocket连接出错', e);
      ElMessage.error(e.code + ' 连接出错');
    };
  };

  heartbeat = () => {
    console.log('== this.heartbeat 开启心跳');
    this.hearbeat_timer && clearInterval(this.hearbeat_timer);

    this.hearbeat_timer = setInterval(() => {
      console.log('WebSocket setInterval(())  sen data');
      // let data = {};
      // this.send(data);
    }, this.hearbeat_interval);
  };

  send = (data: string, callback = null) => {
    console.log('WebSocket=====send=====', this.websocket.readyState, this.websocket.OPEN, this.websocket);

    // 开启状态直接发送
    if (this.websocket.readyState === this.websocket.OPEN) {
      console.log('send  if', this.websocket);
      this.websocket.send(JSON.stringify(data));
      // @ts-ignore
      callback && callback();
    } else {
      console.log('send  else');
      clearInterval(this.hearbeat_timer);
      ElMessage.warning('无法发送消息,socket链接已断开');
    }
  };

  close = () => {
    console.log('WebSocket 关闭关闭关闭===');
    this.is_reconnect = false;
    this.websocket.close();
    this.websocket = null;
  };

  /**
   * 重新连接
   */
  reconnect = () => {
    if (this.websocket && !this.is_reconnect) {
      console.log('重新连接  this.close();');
      this.close();
    }
    // console.log('重新连接  this.init');
    // this.init(null);
  };
}
