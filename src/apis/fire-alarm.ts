import { toQueryString } from '~/utils';
import { WlWebsocket } from './websocket';
import { axiosWithAlert } from './axios';

/**
 * @description: 获取消防告警列表
 * @param {any} query
 * @return {*}
 */
export const apiGetAlarmList = (query: any) => {
  const param = toQueryString(query);
  return new WlWebsocket(`/diagnosis/v1/fire-alarm`, param);
};

/**
 * @description: 获取消防告警详情
 * @param {any} query
 * @return {*}
 */
export const apiGetDetailList = (query: any) => {
  const param = toQueryString(query);
  const pathUrl = `/web/welkin/diagnosis/v1/fire-alarm/report`;
  return axiosWithAlert({
    method: 'get',
    url: pathUrl + param,
  }).then((res: any) => {
    return res.data;
  });
};
/**
 * @description: 下载文件
 * @param {any} query
 * @return {*}
 */
export const apiGetDownload = (query: any) => {
  const param = toQueryString(query);
  window.open(`/web/welkin/device/v1/log-info/download-file/authUrl` + param);
};

/**
 * @description: 历史数据查询
 * @param {any} query
 * @param {string} project
 * @param {string} deviceId
 * @return {*}
 */
export const apiGetDataList = (query: any, project: string, deviceId: string) => {
  const param = toQueryString(query);
  const pathUrl = `/web/welkin/core/realtime/v2/oss/${project}/${deviceId}`;
  return axiosWithAlert({
    method: 'get',
    url: pathUrl + param,
  }).then((res: any) => {
    return res.data;
  });
};
