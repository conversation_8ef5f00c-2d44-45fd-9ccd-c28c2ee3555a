import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 折线图数据
 * @param {any} query
 * @return {*}
 */
export const apiGetChartList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/bluetooth_disconnect/stat_panel` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 告警列表信息
 * @param {any} query
 * @param {any} project
 * @return {*}
 */
export const apiGetAlarmList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/bluetooth_disconnect/PUS4/alarm/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 设备蓝牙断连排行
 * @param {any} query
 * @return {*}
 */
export const apiGetDeviceRankList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/bluetooth_disconnect/device/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 单一告警分布信息，前端做分页
 * @param {any} query
 * @param {any} project
 * @param {any} alarmId
 * @return {*}
 */
export const apiGetSingleAlarmList = (query: any, project: any, alarmId: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/bluetooth_disconnect/${project}/alarm/${alarmId}` + param
  }).then((res: any) => {
    return res.data
  })
}