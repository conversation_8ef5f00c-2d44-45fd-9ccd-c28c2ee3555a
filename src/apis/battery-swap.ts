import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询电池流转
 * @param {any} query
 * @return {*}
 */
export const apiGetSwapList = (query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/battery-history/slotEvent` + param
  }).then((res: any) => {
    return res.data
  })
}