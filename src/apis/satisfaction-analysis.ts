import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 查看订单列表
 * @param {any} query
 * @return {*}
 */
export const apiGetOrderList = (query: any) => {
  // const param = toQueryString(query)
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/diagnosis/v1/satisfy/list`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 根据订单id查询服务订单基本信息
 * @param {any} project
 * @param {any} orderId
 * @return {*}
 */
export const apiGetServiceInfo = (project: any, orderId: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/${project}/${orderId}/service-info`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取诊断卡片信息
 * @param {any} query
 * @return {*}
 */
export const apiGetDiagnosisCard = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/diagnose-result` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取详细信息
 * @param {any} query
 * @return {*}
 */
export const apiGetDetailInfo = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/detail` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取换电日志信息
 * @param {any} query
 * @return {*}
 */
export const apiGetLogInfo = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/log` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取用户标签列表
 * @return {*}
 */
export const apiGetUserTag = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/user-tag/name-mapping`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取诊断标签列表
 * @return {*}
 */
export const apiGetDiagnosisTag = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/diagnosis-tag/name-mapping`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 更新报告采用状态
 * @return {*}
 */
export const apiPostReportStatus = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/diagnosis/v1/satisfy/report_status`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取自定义标签列表
 * @return {*}
 */
export const apiGetLabelTag = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/label/list`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取看板数据
 * @param {any} query
 * @return {*}
 */
export const apiGetDashboard = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/satisfy/report` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载看板数据
 * @param {any} query
 * @return {*}
 */
export const apiDownloadDashboard = (query: any) => {
  const param = toQueryString(query)
  window.open(`/web/welkin/diagnosis/v1/satisfy/report/download` + param)
}

/**
 * @description: 下载订单列表
 * @param {any} query
 * @return {*}
 */
export const apiDownloadOrderList = (query: any) => {
  const param = toQueryString(query)
  window.open(`/web/welkin/diagnosis/v1/satisfy/list/download` + param)
}
