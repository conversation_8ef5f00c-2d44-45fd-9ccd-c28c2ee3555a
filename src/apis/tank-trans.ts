import { toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';

// 获取服务列表数据
export const apiGetTankTransList = (
  project: any,
  deviceId: any,
  query: any
) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/device/v1/tank-transfer-record/${project}/${deviceId}/list` +
      param,
  }).then((res: any) => {
    return res.data;
  });
};

// 获取服务详情 倒仓数据查询
export const apiGetTankTransRecord = (
  project: any,
  deviceId: any,
  transId: any,
  query: any
) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/device/v1/tank-transfer-record/${project}/${deviceId}/${transId}/details` +
      param,
  }).then((res: any) => {
    return res.data;
  });
};
