import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 获取实时数据点列表
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetDataIdList = (project: string) => {
  const query = {lang: localStorage.getItem('locale')}
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/realtime/v1/${project}/dataIdList`,
    params: query
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 历史数据查询
 * @param {any} query
 * @param {string} project
 * @param {string} deviceId
 * @return {*}
 */
export const apiGetDataList = (query: any, project: string, deviceId: string) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  const pathUrl = project == 'PUS3' || project == 'PUS4' || project == 'FYPUS1' ? `/web/welkin/core/realtime/v2/${project}/${deviceId}` : `/web/welkin/core/realtime/v2/oss/${project}/${deviceId}`
  return axiosWithAlert({
    method: 'get',
    url: pathUrl + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载
 * @param {any} query
 * @param {string} project
 * @param {string} deviceId
 * @return {*}
 */
export const apiDownloadData = (query: any, project: string, deviceId: string) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  const pathUrl = project == 'PUS3' || project == 'PUS4' || project == 'FYPUS1' ? `/web/welkin/core/realtime/v2/${project}/${deviceId}` : `/web/welkin/core/realtime/v2/oss/${project}/${deviceId}`
  window.open(pathUrl + param)
}
