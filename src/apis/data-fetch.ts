import { toCamelCase, toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';

// 获取minio图片返回列表
export const apiGetMinioPicList = (project: any, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/image/v1/${project}/list` + param,
  });
};

// 获取算法下拉框
export const apiGetAlgorithmName = () => {
  return axiosWithAlert({
    method: 'get',
    url: '/web/welkin/image/v1/algorithm/name-mapping',
  }).then((res: any) => {
    return res.data;
  });
};

// 删除算法图片
export const apiDeleteAlgorithmImage = (project: string, body: any) => {
  return axiosWithAlert({
    method: 'put',
    url: `/web/welkin/image/v1/${project}/delete/image`,
    data: body,
  }).then((res: any) => {
    return res.data;
  });
};
