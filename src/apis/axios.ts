/*
 * @Author: zhenxing.chen
 * @Date: 2022-11-04 19:45:30
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2024-01-10 15:07:32
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
import axios from 'axios';
import { getUserId, generateUUID } from '~/utils';
import { ElMessage } from 'element-plus';
import store from '~/store';
/**
 * @param options axios配置的参数
 * @param that 传入this对象
 */
const axiosWithAlert = (options: any, that?: any) => {
  return new Promise((resolve, reject) => {
    axios({
      ...options,
      headers: {
        'X-User-ID': getUserId() || 'unknown',
        'X-Request-ID': generateUUID(),
        ...options.headers,
      },
    })
      .then((res: any) => {
        resolve(res);
      })
      .catch((err: any) => {
        // 封装api，非200时弹窗error
        console.log('err', err);
        if (err?.response?.data?.message) {
          ElMessage.error(err.response.data.message);
        } else {
          ElMessage.error(err?.message || err?.response?.data);
        }
        if (err.response.status === 401) {
          store.dispatch('user/setIframeShow', true);
        }

        reject(err);
      });
  });
};

// 设置获取 baseURL
/**
 * @description: axios创建
 * @param {*}
 * @return {*}
 * @author: zhenxing.zhen
 */
// const service = axios.create({
//   timeout: 100000,
//   headers: {
//     user: getUserId() || 'unknown',
//     'X-Request-ID': generateUUID()
//   },
// });
export { axiosWithAlert };
