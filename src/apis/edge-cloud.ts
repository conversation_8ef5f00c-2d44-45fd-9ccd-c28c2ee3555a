import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 获取集群列表
 * @param {any} query
 * @param {string} project
 * @return {*}
 */
export const apiGetClusterList = (query: any, project: string) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/devices` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取集群内节点列表
 * @param {string} project
 * @param {string} deviceId
 * @return {*}
 */
export const apiGetNodeList = (project: string, deviceId: string) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/devices/${deviceId}`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取指定集群下的APP列表
 * @param {string} project
 * @param {string} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetAppWorkloadList = (project: string, deviceId: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/${deviceId}/app-workload/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取APP实例列表
 * @param {string} project
 * @param {string} appName
 * @param {any} query
 * @return {*}
 */
export const apiGetAppInstanceList = (project: string, appName: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/app-workload/${appName}/instances` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取实例编排时Event
 * @param {string} project
 * @param {string} instanceName
 * @param {any} query
 * @return {*}
 */
export const apiGetEventList = (project: string, instanceName: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/instances/${instanceName}/event` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取实例运行时Log
 * @param {string} project
 * @param {string} instanceName
 * @param {any} query
 * @return {*}
 */
export const apiGetlogList = (project: string, instanceName: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/instances/${instanceName}/log` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取镜像列表
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetImageList = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/images` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取APP列表
 * @param {string} project
 * @param {any} query
 * @return {*}
 */
export const apiGetAppList = (project: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/app-workload/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取指定节点列表
 * @param {string} project
 * @return {*}
 */
export const apiGetSpecifyNodeList = (project: string) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/node-info`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 创建/编辑APP
 * @param {any} project
 * @param {any} query
 * @return {*}
 */
export const apiPostCreateApp = (project: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/kubepilot/v1/${project}/app-workload`,
    data: query,
  }).then((res: any) => {
    return res.data;
  });
};

/**
 * @description: 创建APP时上传文件
 * @param {string} project
 * @param {any} query
 * @param {string} appName
 * @return {*}
 */
export const apiPostCreateAppFiles = (project: string, query: any, appName: string) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/kubepilot/v1/${project}/app-workload/${appName}`,
    data: query,

  }).then((res: any) => {
    return res.data;
  });
};

/**
 * @description: 获取APP的创建信息
 * @param {string} project
 * @param {string} appName
 * @param {any} query
 * @return {*}
 */
export const apiGetAppInfoList = (project: string, appName: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/app-workload/${appName}` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 删除APP
 * @param {string} project
 * @param {string} appName
 * @param {any} query
 * @return {*}
 */
export const apiDeleteApp = (project: string, appName: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'delete',
    url: `/web/welkin/kubepilot/v1/${project}/app-workload/${appName}` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 删除实例
 * @param {string} project
 * @param {string} instanceName
 * @param {any} query
 * @return {*}
 */
export const apiDeleteInstance = (project: string, instanceName: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'delete',
    url: `/web/welkin/kubepilot/v1/${project}/instances/${instanceName}` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 停止调度节点
 * @param {string} project
 * @param {string} nodeName
 * @param {any} query
 * @return {*}
 */
export const apiPostStopSchedule = (project: string, nodeName: string, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/kubepilot/v1/${project}/devices/schedule/${nodeName}`,
    data: query,
  }).then((res: any) => {
    return res.data;
  });
};

/**
 * @description: 筛选项-APP名称以及创建人
 * @param {string} project
 * @return {*}
 */
export const apiGetAppOptions = (project: string) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/app-info`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 筛选项-APP实例名称
 * @param {string} project
 * @param {string} appName
 * @param {any} query
 * @return {*}
 */
export const apiGetInstanceOptions = (project: string, appName: string, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/instance-info/${appName}` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 筛选项-项目列表
 * @param {string} project
 * @return {*}
 */
export const apiGetNamespaceOptions = (project: string) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/kubepilot/v1/${project}/namespaces`
  }).then((res: any) => {
    return res.data
  })
}

