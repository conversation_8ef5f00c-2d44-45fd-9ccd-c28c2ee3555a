import axios from 'axios'
import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

// 获取归档状态
export const apiGetArchiveStatus = async (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/plc/plc-record/${project}/${deviceId}/archive_status` + param
  }).then((res: any) => {
    return res.data
  })
}
// 恢复归档的高速录播
export const apiRestorePlc = (project: any, deviceId: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/core/plc/plc-record/${project}/${deviceId}/restore`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}
// 获取服务详情 高速录播数据
export const apiGetHighSpeed = (isRestored: any, project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  const url = isRestored
    ? `/web/welkin/core/plc/plc-record/${project}/${deviceId}/restored`
    : `/web/welkin/core/plc/plc-record/${project}/${deviceId}`
  return axiosWithAlert({
    method: 'get',
    url: url + param
  }).then((res: any) => {
    return res.data
  })
}
// 获取服务详情 变频器数据查询
export const apiGetConverter = (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/plc/converter/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}
// 获取服务详情 di数据查询
export const apiGetDi = (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/plc/di/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}

// 获取服务详情 传感器数据查询
export const apiGetSensor = (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/plc/sensor/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}

// 获取二代站操作日志
export const apiGetOperationLog = (project: any, deviceId: any, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/plc/op-log/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}

// 获取三代站操作日志
export const apiGetPus3OperationLog = (project: any, deviceId: any, query: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/op-log/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}

// 获取状态机 / 值守状态
export const apiGetStateMachineList = (query: any, project: string, deviceId: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/core/realtime/v2/oss/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}

// 获取电池刷写日志
export const apiGetFlashLog = (query: any, project: string, deviceId: any) => {
  query.lang = localStorage.getItem('locale')
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/battery_refresh/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}
