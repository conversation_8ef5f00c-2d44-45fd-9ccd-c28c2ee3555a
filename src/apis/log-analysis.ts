import {toQueryString} from '~/utils'
import {axiosWithAlert} from './axios'

/**
 * @description: 查询站上报的日志数据
 * @param {string} project
 * @return {*}
 */
export const apiGetLogList = (query: any, project: string) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/diagnosis/v1/log-analysis/${project}/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下发日志分析
 * @param {any} query
 * @return {*}
 */
export const apiPostLog = (query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/diagnosis/v1/remote/operation/command`,
    data: query
  }).then((res: any) => {
    return res.data
  })
}
