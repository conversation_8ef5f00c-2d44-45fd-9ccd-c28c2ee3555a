import { toQueryString } from '~/utils';
import { axiosWithAlert } from './axios';
import { handleDeviceNameNull } from '~/utils';

const algorithm = '/web/welkin/algorithm/v1';
// 获取设备列表
export const apiGetRunList = (query: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/task/list` + param,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

// 停止仿真
export const apiPostStopTasks = (task_id: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `${algorithm}/psos/task/${task_id}/stop`,
    data: { task_id },
  }).then((res: any) => {
    return res.data;
  });
};

// 获取设备选择列表
export const apiGetDeviceList = (query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/${project}` + param,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

// 设备导入
export const apiPostUploadCsv = (file: any, project: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/devices/upload-csv`,
    data: { file, project },
  }).then((res: any) => {
    return res.data;
  });
};

// 预览枚举结果
export const apiGetEnumresult = (query: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/battery-enum` + param,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

// 平均电池配比
export const apiGetAverage = (query: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/battery-config/base` + param,
  }).then((res: any) => {
    return res.data;
  });
};

// 设备导入
export const apiPostCreateFromReal = (data: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `${algorithm}/psos/task/by_config`,
    data,
  }).then((res: any) => {
    return res.data;
  });
};

// 获得设备细节
export const apiGetDetail = (query: any) => {
  const param = toQueryString(query);
  console.log('param', param);
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/task/simulation/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};

// 获取创建人
export const apiGetUserList = (query: any) => {
  query.lang = localStorage.getItem('locale');
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/internal/v1/people/query` + param,
  }).then((res: any) => {
    return res.data;
  });
};

// 获取设备id和名称
export const apiGetDeviceNameMap = (project: any, query = {}) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/devices/list/${project}/name-mapping`,
    params: query,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

// 获取配置列表
export const apiGetConfigList = (query: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/config/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};

//通过id获取psos simulation
export const apiGetSimulation = (id: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/task/simulation/${id}`,
  }).then((res: any) => {
    return res.data;
  });
};
//获取仿真任务图表数据
export const apiGetGraphData = (id: any, query: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/task/${id}/graph_data` + param,
  }).then((res: any) => {
    return res.data;
  });
};
//获取configId

export const apiGetConfigId = (query: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url: `${algorithm}/psos/task/query_config_id` + param,
  }).then((res: any) => {
    return res.data;
  });
};
