import {toQueryString} from '~/utils'
import {WlWebsocket} from './websocket'
import {axiosWithAlert} from './axios'

/**
 * @description: 数据看板上面两幅图的实时数据
 * @param {any} project
 * @param {any} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetTopList = (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return new WlWebsocket(`/fcrd/v1/fcrd-management/${project}/${deviceId}/realtime-data-history`, param)
}

/**
 * @description: 数据看板中标容量和中标价格的实时数据
 * @param {any} project
 * @param {any} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetMidList = (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return new WlWebsocket(`/fcrd/v1/fcrd-management/${project}/${deviceId}/winning-bid-history`, param)
}

/**
 * @description: 数据看板下方的实时数据
 * @param {any} project
 * @param {any} deviceId
 * @return {*}
 */
export const apiGetBottomList = (project: any, deviceId: any) => {
  return new WlWebsocket(`/fcrd/v1/fcrd-management/${project}/${deviceId}/current-realtime`)
}

/**
 * @description: 荷兰ImbalancePrice
 * @param {any} query
 * @return {*}
 */
export const apiGetNetherlandsImbalancePrice = (query: any) => {
  const param = toQueryString(query)
  return new WlWebsocket(`/fcrd/v1/fcrd-management/PowerSwap2/netherlandsImbalancePrice`, param)
}

/**
 * @description: 收益数据
 * @param {any} query
 * @param {string} project
 * @param {string} deviceId
 * @return {*}
 */
export const apiGetIncome = (query: any, project: string, deviceId: string) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/${deviceId}/revenue` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 荷兰电价，每天更新
 * @return {*}
 */
export const apiGetNetherlandsEPrice = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/fcrd/v1/fcrd-management/PowerSwap2/netherlandsEPrice`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 获取中标容量数据
 * @param {any} project
 * @param {any} deviceId
 * @param {any} query
 * @return {*}
 */
export const apiGetWinningBidHistory = (project: any, deviceId: any, query: any) => {
  const param = toQueryString(query)
  return new WlWebsocket(`/fcrd/v1/fcrd-management/${project}/${deviceId}/winning-bid-history`, param)
}

/**
 * @description: 获取fcrd电价
 * @return {*}
 */
export const apiGetFcrdPrice = (project: any) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/fcrd/v1/fcrd-management/${project}/fcrdPrice`
  }).then((res: any) => {
    return res.data
  })
}
