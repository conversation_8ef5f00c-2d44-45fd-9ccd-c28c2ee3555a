import {
  toQueryString,
  handleDeviceNameNull,
  getUserId,
  downloadResponseFile,
} from '~/utils';
import { axiosWithAlert } from './axios';
import { i18n } from '~/i18n';
import { ElMessage } from 'element-plus';

// 查询文件目录树
export const apiGetDirectoryTree = (query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/device/v1/log-info/directory-tree/${project}/list` + param,
  }).then((res: any) => {
    return res.data;
  });
};

// 通知设备上传
export const apiPostLogInfo = (deviceId: any, project: any, query: any) => {
  let user_id = getUserId();
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/log-info/${project}/${deviceId}/oss/command`,
    data: query,
    headers: {
      'X-User-ID': user_id,
    },
  });
};

// 查询换电站日志上传记录
export const apiGetUploadHistory = (query: any, project: any) => {
  const param = toQueryString(query);
  return axiosWithAlert({
    method: 'get',
    url:
      `/web/welkin/device/v1/log-info/upload-history/${project}/list` + param,
  }).then((res: any) => {
    if (res?.data?.data) {
      handleDeviceNameNull(res.data.data);
    }
    return res.data;
  });
};

// 下载日志文件
export const apiDownLoadLogFile = (query: any) => {
  ElMessage.info(i18n.global.t('common.pp_download_message'));
  const param = toQueryString(query);
  const url = '/web/welkin/device/v1/log-info/download-file/authUrl' + param;
  window.open(url)
  // return axiosWithAlert({
  //   method: 'get',
  //   url: url,
  //   // responseType: 'blob',
  // }).then((res: any) => {
  //   console.log(url, 222)
  //   return downloadResponseFile(res, url);
  // });
};

// 下载文件发送审批流
export const apiPostDownloadApproval = (query: any) => {
  let user_id = getUserId();
  // console.log('cookie user_id', user_id);
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/log-info/download-file/approval`,
    data: query,
    headers: {
      'X-User-ID': user_id,
    },
  }).then((res: any) => {
    return res.data;
  });
};
