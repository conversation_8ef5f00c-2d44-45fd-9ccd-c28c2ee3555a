import { toQueryString } from '~/utils'
import { WlWebsocket } from './websocket'
import { axiosWithAlert } from './axios'
import { getUserId } from '~/utils'

// 获取符合条件的所有snapshot列表
export const apiGetSnapshotList = (project: any, query: any) => {
  query.user_id = getUserId()
  const param = toQueryString(query)
  return new WlWebsocket(`/device/v1/snapshot/${project}/list`, param)
}

// 发送飞书通知到指定算法负责人
export const apiPostLarkcard = (project: any, query: any) => {
  return axiosWithAlert({
    method: 'post',
    url: `/web/welkin/device/v1/snapshot/${project}/larkcard`,
    data: query
  }).then((res: any) => {
    return res
  })
}

/**
 * @description: 获取snapshot筛选的告警列表
 * @param {string} project
 * @return {*}
 */
export const apiGetAlgorithmList = (project: string) => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/snapshot/${project}/name-mapping`
  }).then((res: any) => {
    return res.data
  })
}
