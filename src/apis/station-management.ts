import { toQueryString } from '~/utils'
import { axiosWithAlert } from './axios'

/**
 * @description: 单站模型数据概览
 * @return {*}
 */
export const apiGetOverview = () => {
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/model/overview`
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 能效总览
 * @param {any} query
 * @return {*}
 */
export const apiGetEnergyOverview = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/model/energy/overview` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 能效明细列表
 * @param {any} query
 * @return {*}
 */
export const apiGetEnergyList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/model/energy/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载能效明细列表
 * @param {any} query
 * @return {*}
 */
export const apiDownloadEnergyList = (query: any) => {
  const param = toQueryString(query)
  window.open(`/web/welkin/device/v1/model/energy/list` + param)
}

/**
 * @description: 收益总览
 * @param {any} query
 * @return {*}
 */
export const apiGetRevenueOverview = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/model/revenue/overview` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 收益明细列表
 * @param {any} query
 * @return {*}
 */
export const apiGetRevenueList = (query: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/model/revenue/list` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 下载收益明细列表
 * @param {any} query
 * @return {*}
 */
export const apiDownloadRevenueList = (query: any) => {
  const param = toQueryString(query)
  window.open(`/web/welkin/device/v1/model/revenue/list` + param)
}

/**
 * @description: 能效详情
 * @param {any} query
 * @param {string} project
 * @param {string} deviceId
 * @return {*}
 */
export const apiGetEnergyDetail = (query: any, project: any, deviceId: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/model/energy/overview/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}

/**
 * @description: 收益详情
 * @param {any} query
 * @param {any} project
 * @param {any} deviceId
 * @return {*}
 */
export const apiGetRevenueDetail = (query: any, project: any, deviceId: any) => {
  const param = toQueryString(query)
  return axiosWithAlert({
    method: 'get',
    url: `/web/welkin/device/v1/model/revenue/overview/${project}/${deviceId}` + param
  }).then((res: any) => {
    return res.data
  })
}
