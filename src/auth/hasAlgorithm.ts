/*
 * @Author: zhenxing.chen
 * @Date: 2023-03-01 14:24:16
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-03-29 13:47:30
 * @Email: <EMAIL>
 * @Description:
 */

/**
 * @description: 是否有编辑算法权限
 * @param {*} list
 * @param {any} cur
 * @return {*}
 */
export const hasSingleAlgorithm = (list: [], cur: any) => {
  let isAlgorithm = false;
  list.map((item: any) => {
    if (item.name === cur) {
      isAlgorithm = true;
    }
  });
  return isAlgorithm;
};
