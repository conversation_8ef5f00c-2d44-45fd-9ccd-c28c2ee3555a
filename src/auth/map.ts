/*
 * @Author: zhenxing.chen
 * @Date: 2022-10-03 16:18:24
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-30 14:36:42
 * @Email: <EMAIL>
 * @Description:菜单Icon映射
 */
// 为identify匹配相应的icon
export const iconMap = {
  'menu:info-trace': 'material-symbols:camera-enhance-rounded',
  'menu:owl': 'material-symbols:chart-data-rounded',
  'menu:home': 'ci:home-alt-fill',
  'menu:powerSwap3': 'ic:outline-flash-auto',
  'menu:powerSwap2': 'ic:outline-flash-on',
  'menu:powerCharge': 'material-symbols:ev-charger',
  'menu:powerSwap': 'ic:outline-flash-on',
  'menu:cloud': 'mdi:account-box',
  'menu:power-grid': 'mdi:power-from-grid',
  'menu:fault-diagnosis': 'material-symbols:troubleshoot',
  'menu:device-health': 'healthicons:health-outline',
  'service-detail': 'ic:outline-article',
  'view-image': 'ic:outline-photo-camera',
  'view-abnormal-image': 'ic:outline-dirty-lens',
  'view-alarm': 'mdi:warning-box-outline',
  'language-change': 'cil:language',
  'user-management-edit': 'clarity:edit-line',
  'user-management-delete': 'ep:delete',
  'download-image': 'humbleicons:download',
  'back': 'ic:round-arrow-back',
  download: 'ant-design:download-outlined',
  refresh: 'ep:refresh-right',
  check: 'ant-design:check-circle-filled',
  close: 'ant-design:close-circle-filled',
  loading: 'eos-icons:loading',
  unfavorite: 'ph:star-light',
  favorite: 'ph:star-fill',
} as { [index: string]: any };

export const pathMap = {
  'menu:homePage': '/home',
  'menu:powerSwap': '/device',
  'menu:powerSwap:serviceList': '/device/service-list',
  'menu:powerSwap:logExport': '/device/log-export',
  'menu:homePage:myFavorite': '/home/<USER>',
  'menu:homePage:myWatch': '/home/<USER>',
  'menu:algorithms:device-list': '/algorithms/device-list',
  'menu:algorithms:result-list': '/algorithms/result-list',
  'menu:algorithms:data-upload': '/algorithms/data-upload',
  'menu:algorithms:algorithm-performance': '/algorithms/algorithm-performance',
  'menu:algorithms:deployment': '/algorithms/deployment',
  'menu:models:management': '/models/management',
  'menu:models:add': '/models/add',
  'menu:people': '/people',
  'menu:dataManage:fetchData': '/algorithms/data-fetch',
  'menu:algorithms:faq': '/algorithms/faq',
  'menu:algorithms:flow-chart': '/algorithms/flow-chart',
  'menu:dataManage:batteryHistory': '/algorithms/battery-history',
  'menu:algorithms:version-management': '/algorithms/version-management',
} as { [index: string]: any };
