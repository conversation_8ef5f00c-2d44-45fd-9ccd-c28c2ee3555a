/*
 * @Author: zhenxing.chen
 * @Date: 2022-12-29 14:44:55
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-28 17:43:31
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
// 用户属性权限 (暂无，需后续添加)
// let userConditions: any = null
// 用户资源权限
let userid: any = null;
let authorities: any = null;
let permissions: any = null;
let updatePromise: any;

// 将原始数据转化为需要的数据结构
function formmatPermissions(list: any[]) {
  const dfs = (origin: any[]): string[] => {
    if (!origin || !origin.length) {
      return [];
    }
    return origin.reduce((prev: string[], cur: any) => {
      if (cur.status === 1) {
        prev.push(cur.identifier);
        if (cur.children) {
          prev.push(...dfs(cur.children));
        }
      }
      return prev;
    }, []);
  };
  return dfs(list);
}

// 更新权限数据
export const updatePermission = (raw: any) => {
  // 拉取权限， 并赋值到userPermissions
  // console.log(raw)
  updatePromise = new Promise((resolve) => {
    permissions = formmatPermissions(raw);
    authorities = raw.filter(
      (item: any) => item.identifier === 'portal:welkin:menu'
    )[0].children;
    resolve(authorities);
  });
  return updatePromise;
};

// 拉取权限数据
export const getUserid = () => {
  return userid;
};

// 拉取权限树
export const getAuthoritiesTree = () => {
  return authorities;
};

// 拉取打平权限资源点
export const getPermissions = () => {
  return permissions;
};
