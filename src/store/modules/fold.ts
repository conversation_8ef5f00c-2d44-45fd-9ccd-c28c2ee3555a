import { Module } from 'vuex'

export interface QueryForm {
  serviceFold: Boolean,
  detailFold: <PERSON><PERSON><PERSON>,
  searchTab: String,
  bcStepNum: Array<string>,
  plStepNum: Array<string>,
  axis: Array<string>
}

export default {
  namespaced: true,
  state: {
    serviceFold: true,
    detailFold: true,
    searchTab: 'platform',
    bcStepNum: [],
    plStepNum: [],
    axis: [],
  },
  mutations: {
    SET_SERVICE_FOLD(state) {
      state.serviceFold = !state.serviceFold
    },
    SET_DETAIL_FOLD(state) {
      state.detailFold = !state.detailFold
    },
    SET_STEPNUM(state, form) {
      state.searchTab = form.searchTab
      state.bcStepNum = form.bc_step_num
      state.plStepNum = form.pl_step_num
      state.axis = form.axis
    },
  }
} as Module<QueryForm, Boolean>