import { Module } from 'vuex'
import { numberToChinese } from '~/utils'
import { apiGetBasicList } from '~/apis/home'
import { ElMessage } from 'element-plus'

export interface BrandList {
  brandList: Object
  pathMap: Object
  batteryRefreshCapacity: Object
  carPlatform: Array<string>
  cityCompany: Array<string>
  cityCompanyGroup: Array<string>
}

export default {
  namespaced: true,
  state: {
    brandList: {},
    pathMap: {},
    batteryRefreshCapacity: {},
    carPlatform: [],
    cityCompany: [],
    cityCompanyGroup: []
  },
  mutations: {
    SET_LIST(state, list) {
      // 车型品牌
      const transformedData = Object.keys(list.vehicle).map((key) => {
        return {
          value: key,
          label: key,
          children: list.vehicle[key].map((child: any) => {
            return {
              value: child,
              label: child
            }
          })
        }
      })
      state.brandList = transformedData

      // 设备类型映射
      state.pathMap = list.frontend_project

      // 刷写电池容量
      state.batteryRefreshCapacity = list.battery_refresh_battery_capacity

      // 车辆平台
      state.carPlatform = list.car_platform

      // 城市公司
      state.cityCompany = list.city_company

      // 公司组别
      state.cityCompanyGroup = list.city_company_group
        ? list.city_company_group.map((item: any) => {
            return {
              value: item,
              label: numberToChinese(item) + '组'
            }
          })
        : []
    }
  },
  actions: {
    async getBasicList({ commit }) {
      const res = await apiGetBasicList()
      if (res.err_code) {
        ElMessage.error(res.message)
      } else {
        commit('SET_LIST', res.data)
      }
    }
  }
} as Module<BrandList, Object>
