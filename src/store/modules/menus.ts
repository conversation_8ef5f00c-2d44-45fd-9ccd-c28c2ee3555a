/*
 * @Author: zhenxing.chen
 * @Date: 2022-12-29 14:47:58
 * @LastEditors: zhenxing.chen <EMAIL>
 * @LastEditTime: 2024-08-20 10:27:09
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
import cookie from 'js-cookie';
import { Module } from 'vuex';
import { getEnv } from '~/utils';
import { sso_url, sso_url_eu_stg, sso_url_eu_pod } from '~/utils/constant';
import { apiGetMenuList, apiGetAccessToken } from '~/apis/user-management';
import { ElMessage, ElMessageBox } from 'element-plus';

const env = getEnv();
const ssoUrl = sso_url;
export interface Menu {
  list: [];
  collapse: boolean;
  conditions: [];
}

export default {
  state: {
    list: [],
    conditions: [],
    collapse: false,
  },
  mutations: {
    SET_MENUS(state, menus) {
      state.list = menus;
    },
    SET_CONDITIONS(state, conditions) {
      state.conditions = conditions;
    },
    set_collapse(state, paylod) {
      state.collapse = paylod;
    },
  },
  actions: {
    async getMenusList({ commit }) {
      const menuRes = await apiGetMenuList();
      console.log('menuRes', menuRes);
      if (!!menuRes?.err_code) {
        if (menuRes?.err_code == 4011) {
          ElMessageBox.alert('暂未开通权限，请到权限平台统一申请', '提醒', {
            confirmButtonText: '确定',
          }).then(() => {
            window.location.href = menuRes.redirect_url;
          });
          return menuRes;
        }
        ElMessage.error(menuRes.message || '菜单获取失败！');
      } else {
        const { domain_account } = menuRes.data;
        cookie.set('user_id', domain_account, { expires: 5 });
        localStorage.setItem('user_id', domain_account);
        commit('SET_MENUS', menuRes.data?.authorities);
        commit('SET_CONDITIONS', menuRes.data?.conditions);
        return menuRes.data?.authorities;
      }
    },
  },
  namespaced: true,
} as Module<Menu, Object>;
