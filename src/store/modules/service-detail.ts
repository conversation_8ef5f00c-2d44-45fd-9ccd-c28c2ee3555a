import { Module } from 'vuex';

export interface ServiceDetail {
  device_id: String;
  description: String;
  ev_battery_id: String;
  ev_id: String;
  ev_vin: String;
  service_duration: String;
  service_battery_id: String;
  service_end_time: String;
  service_id: String;
  service_start_time: String;
  updated_time: Number;
}

export interface ServiceDetailState {
  service_detail: ServiceDetail;
}

export default {
  // 开启命名空间
  namespaced: true,
  state: {
    device_id: '',
    description: '',
    ev_battery_id: '',
    ev_id: '',
    ev_vin: '',
    service_duration: '',
    service_battery_id: '',
    service_end_time: '',
    service_id: '',
    service_start_time: '',
    updated_time: 0,
  },
  // 更改 Vuex 的 store 中的状态的唯一方法是提交 mutation
  mutations: {
    SET(state, service_detail: ServiceDetail) {
      const new_state = {
        ...state,
        ...service_detail,
      };
      // state = new_state;

      for (let key of Object.keys(state)) {
        state[key] = new_state[key];
      }
    },
  },
  //异步
  actions: {},
  //相当于计算属性,从state中派生出一些状态,当state中的值发生改变的时候,会被重新计算
  getters: {
    getDeviceId(state) {
      return state.device_id;
    },
  },
} as Module<ServiceDetail, Object>;
