/*
 * @Author: zhenxing.chen
 * @Date: 2024-01-09 16:18:43
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2024-01-10 13:13:20
 * @Email: <EMAIL>
 * @Description: 增加全局退出弹窗
 */
import { Module } from 'vuex';
import { getUserId } from '~/utils';
import { apiLogout } from '~/apis/user-management';

export interface User {
  name: string;
}

export interface UserState {
  users: User[];
  administrator: String;
  showLogout: boolean;
}

export default {
  namespaced: true,
  state: {
    users: [{ name: 'sps' }],
    administrator: '',
    showLogout: false,
  },
  mutations: {
    ADD_USER(state, user: User) {
      state.users.push(user);
    },
    SET_ADMINISTRATOR: (state, administrator) => {
      state.administrator = administrator;
    },
    SET_IFRAME: (state, isShow) => {
      state.showLogout = isShow;
    },
  },
  actions: {
    async getAdministrator({ commit }) {
      const administrator = getUserId();
      commit('SET_ADMINISTRATOR', administrator);
    },
    /**
     * @description: 退出sso弹窗
     * @param {*} commit
     * @return {*}
     */
    setIframeShow({ commit }, val) {
      commit('SET_IFRAME', val);
    },
    async logout({ commit }) {
      const r = await apiLogout();
      // console.log('r',r)
      const { err_code } = r;
      if (!err_code) {
        commit('CLEAR_TOKEN');
        commit('SET_IFRAME', true);
      }
    },
  },
} as Module<UserState, Object>;
