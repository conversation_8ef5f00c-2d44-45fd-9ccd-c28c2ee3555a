import { Module } from 'vuex';
import { ver_to_project } from '~/constvars';

export class ProjectType {
  project!: String; //向后端传参的string
  route!: String; //url中显示的
  version!: number; //版本号
  pageComponent!: string;
}

export default {
  // 开启命名空间
  namespaced: true,
  state: {
    project: '',
    route: '',
    version: 0,
    pageComponent: '',
  },

  // 更改 Vuex 的 store 中的状态的唯一方法是提交 mutation
  mutations: {
    SET(state, project: String) {
      state.project = project;
    },
    setProject(state, route: String) {
      console.log('------------------', route)
      state.route = route;

      let ver = 1;
      if (route == 'powerSwap2') {
        ver = 2;
      } else if (route == 'powerSwap3') {
        ver = 3;
      } else if (route == 'powerSwap4') {
        ver = 4;
      } else if (route == 'powerCharge4' || route == 'powerCharge') {
        ver = 5;
      } else if (route == 'powerCharge2') {
        ver = 6;
      } else if (route == 'firefly1') {
        ver = 7;
      } else if (route == 'powerCharge1') {
        ver = 8;
      } 
      state.project = ver_to_project[ver];
      state.version = ver;
    },
    setPageComponent(state, val: string) {
      state.pageComponent = val;
    },
  },
  //异步
  actions: {},
  //相当于计算属性,从state中派生出一些状态,当state中的值发生改变的时候,会被重新计算
  getters: {
    getProject(state) {
      return state.project;
    },
  },
} as Module<ProjectType, Object>;
