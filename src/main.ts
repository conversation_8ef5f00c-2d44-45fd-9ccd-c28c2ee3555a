/*
 * @Author: zhenxing.chen
 * @Date: 2023-02-01 11:10:02
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-02-03 10:20:17
 * @Email: <EMAIL>
 * @Description: 请填写简介
 */
import {createApp} from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import {useI18n} from './i18n/index'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import '~/styles/index.scss'
import 'uno.css'

import 'element-plus/theme-chalk/src/message.scss'

const app = createApp(App)

app.use(router).use(store).use(useI18n).use(ElementPlus).mount('#app')
