<template>
  <div style="display: flex; align-items: center">
    <el-tooltip :content="text" :disabled="disableTooltip" placement="top">
      <div :class="['ellipsis-div', 'margin_r-4', {'direction-rtl' : direction}]" @mouseover="onMouseOver">
        <span ref="textRef" :style="{color: textColor, cursor: textColor ? 'pointer' : cursorStyle}" @click="handleClickText">{{ text ? text : '-' }}</span>
      </div>
    </el-tooltip>
    <div class="flex-box flex_a_i-center">
      <CopyIcon class="cursor-pointer" @click="copyText" v-if="text && showIcon" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, toRefs} from 'vue'
import {ElMessage, ElTooltip} from 'element-plus'
import {useI18n} from 'vue-i18n'
import CopyIcon from '~/assets/svg/copy.vue'

const {t} = useI18n()
const props = defineProps({
  text: String,
  textColor: String,
  direction: String,
  showIcon: {
    type: Boolean,
    default: true
  },
  cursorStyle: {
    type: String,
    default: 'auto'
  }
})
const {text} = toRefs(props)

const emits = defineEmits(['clickCopyText'])

const textRef = ref()
const disableTooltip = ref(false)

const handleClickText = () => {
  if(!props.textColor) return
  emits('clickCopyText')
}

const copyText = () => {
  if (!!text?.value) {
    navigator.clipboard.writeText(text?.value).then(() => {
      ElMessage.success(t('common.pp_copy_success'))
    })
  }
}

const onMouseOver = () => {
  const parentWidth = textRef.value?.parentNode?.offsetWidth // 获取元素父级可视宽度
  const contentWidth = textRef.value?.offsetWidth // 获取元素可视宽度
  disableTooltip.value = contentWidth <= parentWidth
}
</script>

<style lang="scss">
.ellipsis-div {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.direction-rtl {
  direction: rtl;
}
</style>
