<template>
  <div class="header">
    <div class="header-left">
      <div class="welkin-logo">
        <welkin-logo class="welkin-logo-icon" />
      </div>
      <div class="left-info">
        <div class="first-line" v-if="getEnv() === '-stg-eu' || getEnv() === '-eu'">Welkin</div>
        <div class="first-line" v-else>Welkin 天宫</div>
        <div class="second-line" :title="$t('menu.pp_platform')">
          {{ $t('menu.pp_platform') }}
        </div>
      </div>
    </div>
    <div></div>
  </div>
</template>

<script lang="ts" setup>
import WelkinLogo from './welkin-logo.vue';
import { getEnv } from '~/utils';

</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .header-left {
    display: flex;
    align-items: center;
    .welkin-logo {
      margin-right: 10px;
      display: flex;
      align-items: center;
      .welkin-logo-icon {
        background-color: #fff;
        border-radius: 25%;
      }
    }
    .left-info {
      .first-line {
        font-size: 16px;
        font-family: 'Noto Sans';
        font-weight: 700;
      }
      .second-line {
        font-size: 10px;
        font-family: 'Noto Sans';
        font-weight: 400;
      }
    }
  }
}
</style>
