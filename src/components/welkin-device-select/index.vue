<template>
  <el-select :model-value="props.modelValue" filterable clearable :allow-create="props.allowCreate" :placeholder="$t('deviceManagement.pp_select_device_id')" @change="onChange">
    <el-option v-for="item in deviceIdOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
      <span style="float: left">{{ item.description }}</span>
      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount, computed } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { apiGetDeviceNameMap } from '~/apis/device-management'
const $store = useStore()
const $route = useRoute()
const project = ref(computed(() => $store.state.project))

const props = defineProps({
  modelValue: {
    type: String
  },
  query: {
    type: Object,
    require: false,
    default: {}
  },
  allowCreate: {
    type: Boolean,
    require: false,
    default: false
  }
})
const deviceIdOptions = ref([] as any)

const emits = defineEmits(['update:modelValue'])

const onChange = (val: string) => {
  emits('update:modelValue', val)
}

const getDeviceNameMap = () => {
  return apiGetDeviceNameMap(project.value.project, props.query).then((res) => {
    deviceIdOptions.value = res.data
  })
}

// watch监听动态拿到值的变化,从而做出反应
const stopWatch = watch(
  () => $route.path,
  (newPath, oldPath) => {
    if (!oldPath || newPath.split('/')[2] == oldPath.split('/')[2]) {
      getDeviceNameMap()
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  stopWatch()
})

defineExpose({ getDeviceNameMap })
</script>
