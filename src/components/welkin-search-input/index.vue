<template>
  <el-dropdown
    style="width: 100%"
    @command="onChangeCommand"
    :teleported="false"
    placement="bottom-start"
    trigger="click"
  >
    <el-input
      :model-value="props.modelValue"
      :placeholder="props.placeholder"
      @input="onInput"
      @change="onInputChange"
      clearable
    />
    <template #dropdown v-if="options.length > 0">
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="item in options"
          :key="item.value"
          :command="item.value"
        >
          {{ item.value }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const props = defineProps({
  modelValue: {
    type: String,
  },
  options: { type: Array },
  placeholder: { type: String },
});

const emit = defineEmits(['change', 'update:modelValue']);

const options = ref(props.options as any);

const filterOptions = (val: string) => {
  options.value = props.options?.filter((item: any) => {
    return item.value?.indexOf(val) !== -1;
  });
};
const onChangeCommand = (val: string) => {
  emit('update:modelValue', val);
  emit('change', val);
  filterOptions(val);
};
const onInput = (val: string) => {
  emit('update:modelValue', val);
  filterOptions(val);
};

const onInputChange = (val: string) => {
  emit('change', val);
};
</script>

<style lang="scss"></style>
