<template>
  <el-select :model-value="props.modelValue" filterable remote clearable :multiple="isMultiple" :collapse-tags="isMultiple" :collapse-tags-tooltip="isMultiple" :multiple-limit="multipleLimit" :placeholder="$t('common.pp_enter')" @change="handleDeviceChange" :remote-method="remoteMethod" :loading="remoteLoading">
    <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
      <span style="float: left">{{ item.description }}</span>
      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { apiGetDevices } from '~/apis/home'
import { debounce } from 'lodash-es'

const props = defineProps({
  modelValue: {
    type: [String, Array] as const
  },
  project: {
    type: String
  },
  initDevice: {
    type: String
  },
  isMultiple: {
    type: Boolean,
    default: false
  },
  multipleLimit: {
    type: Number,
    default: 10000
  }
})

const emits = defineEmits(['update:modelValue', 'handleDeviceChange'])

const remoteLoading = ref(false)
const deviceOptions = ref([] as any)

const handleDeviceChange = (val: any) => {
  emits('update:modelValue', val)
  emits('handleDeviceChange', val)
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any) => {
  if (val) {
    remoteLoading.value = true
    const params = { project: props.project, name: val, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

onBeforeMount(() => {
  searchDeviceList(props.initDevice || 'NIO')
})

defineExpose({ searchDeviceList })
</script>