<template>
  <div class="el-menu-vertical-demo" :class="[isCollapse ? 'width-64' : 'width-210']" v-if="menus && menus.length > 0">
    <!-- 菜单 -->
    <div class="menu-container">
      <el-menu ref="menuRef" active-text-color="#00BEBE" background-color="#fff" text-color="#292C33" :unique-opened="true" :collapse="isCollapse" :default-active="activeItem" router>
        <template v-for="first in menus" :key="first.id">
          <el-sub-menu :index="first.comment" v-if="first.children">
            <template #title>
              <!-- 一级目录 换电站icon -->
              <firefly1-logo v-if="first.identifier === 'menu:firefly1'" />
              <powerswap4-logo v-else-if="first.identifier === 'menu:powerSwap4'" />
              <powerswap3-logo v-else-if="first.identifier === 'menu:powerSwap3'" />
              <powerswap2-logo v-else-if="first.identifier === 'menu:powerSwap2'" />
              <powerswap1-logo v-else-if="first.identifier === 'menu:powerSwap'" />
              <device-simulation v-else-if="first.identifier === 'menu:device-simulation'" />
              <station-management v-else-if="first.identifier === 'menu:station-management'" />
              <!-- 一级目录 其他icon-->
              <el-icon :size="'20px'" v-else>
                <Icon :icon="iconMap[first.identifier]" />
              </el-icon>

              <!-- 一级目录 名称label -->

              <span class="menu-item-info" v-if="/menu:powerSwap/gi.test(first.identifier) || first.identifier === 'menu:device-simulation' || first.identifier === 'menu:station-management' || first.identifier === 'menu:firefly1'">
                {{ $t(getMenuI18(first.identifier)) }}
              </span>
              <span style="color: #22252b; padding-left: 15px" v-else>
                {{ $t(getMenuI18(first.identifier)) }}
              </span>
            </template>

            <!-- 二级目录 -->
            <template v-for="(second, sec_index) in first.children" :key="sec_index">
              <!-- 二级目录 换电站 -->
              <el-menu-item :index="getSubMenuIndex(second.comment)" v-if="(first.priority == 1 || first.priority == 2 || first.priority == 3 || first.priority == 4 || first.priority == 5) && second.comment.split('/').length <= 3">
                {{ $t(getMenuI18(second.identifier)) }}
              </el-menu-item>

              <!-- 二级目录 其他 -->
              <el-menu-item :index="second.comment" v-if="first.priority != 1 && first.priority != 2 && first.priority != 3 && first.priority != 4 && first.priority != 5">
                {{ $t(getMenuI18(second.identifier)) }}
              </el-menu-item>
            </template>
          </el-sub-menu>
          <el-menu-item :index="first.comment" :class="isCollapse ? 'menu-item-collapse' : 'menu-item-deCollapse'" v-else>
            <el-icon :size="'20px'">
              <Icon :icon="iconMap[first.identifier]" />
            </el-icon>
            <template #title>
              <span class="menu-item-info">
                {{ $t(getMenuI18(first.identifier)) }}
              </span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
    </div>

    <!-- 菜单底部的折叠按钮 -->
    <div class="menu-bottom" @click="changeCollapse">
      <div style="text-align: center">
        <Fold style="width: 24px; height: 24px; color: #91a2bc" v-show="!isCollapse" />

        <Expand style="width: 24px; height: 24px; color: #91a2bc" v-show="isCollapse" />
        <div style="color: #91a2bc">*******</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { Menu as IconMenu, Fold, Expand } from '@element-plus/icons-vue';
import { iconMap, updatePermission, hasPermission } from '~/auth';
import { Icon } from '@iconify/vue/dist/iconify';
import { useRoute, useRouter } from 'vue-router';
import { firstMenuList } from './rule';
import Powerswap4Logo from './powerswap4-logo.vue';
import Powerswap3Logo from './powerswap3-logo.vue';
import Powerswap2Logo from './powerswap2-logo.vue';
import Powerswap1Logo from './powerswap1-logo.vue';
import Firefly1Logo from './firefly1-logo.vue';
import DeviceSimulation from './device-simulation.vue';
import StationManagement from './station-management.vue';
import { useStore } from 'vuex';

const store = useStore();

const menuRef = ref();
const isCollapse = ref(false);

// 监听路由
const $route = useRoute();
const $router = useRouter();
const menus = ref([] as any);
const nameList = ['单站服务详情', '单站倒仓详情', '服务详情', '设备详情', '集群列表详情', 'APP详情', '新建仿真配置', '运行详情', '运行任务详情', '告警管理/告警分析', '满意度分析详情', '换电画像详情', '充电画像详情', '能效详情', '收益详情', '消防详情'] as any;
const activeItem = ref('/owl/faq');

const getSubMenuIndex = (comment: string) => {
  let arr = comment.split('/');

  if (arr.length > 2 && !!arr[2]) {
    return '/' + arr[1] + '/' + arr[2];
  } else {
    return comment;
  }
};

const getMenuI18 = (identifier: any) => {
  const identArr = identifier.split(':');
  let str: any = identArr[identArr.length - 1];
  if (str == 'powerSwap') {
    str = 'swap_station1';
  } else if (str.slice(0, 9) == 'powerSwap') {
    str = 'swap_station' + str.slice(9);
  } else if (str.slice(0, 11) == 'powerCharge') {
    str = 'charge_pile';
  }
  str = str.replace(new RegExp('-', 'g'), '_');
  return 'menu.pp_' + str;
};
function delMenus(list: any) {
  const menus: any[] = [];
  if (!list) {
    return [];
  }
  list.map((item: any, index: any) => {
    const name = item.name;
    if (!nameList.includes(name)) {
      let menu_item = item;
      if (item.children && item.children.length > 0) {
        item.children = item.children.sort((a: any, b: any) => a.priority - b.priority);
        menu_item.children = delMenus(item.children);
      }
      menu_item = { ...menu_item };
      menus.push(menu_item);
    }
  });
  return menus;
}
onMounted(() => {
  activeItem.value = $route.path;
  let queryString = window.location.search;
  let params = new URLSearchParams(queryString);
  let code = params.get('code');
  if (code != null) {
    $router.push('/home');
  }
});

// 渲染菜单
const tempState: any = store.state;
watch(
  () => tempState.menus.list,
  (newVal, oldVal) => {
    const raw = Object.assign(newVal, {});
    updatePermission(raw);
    const welkinMenu = newVal.filter((item: any) => item.identifier === 'portal:welkin:menu')[0].children;
    const temp = JSON.parse(JSON.stringify(welkinMenu));
    temp.sort((a: any, b: any) => a.priority - b.priority);
    menus.value = delMenus(temp).filter((item: any) => item.identifier != 'function:eu');
    console.log('菜单', menus.value);
  }
);

// 设置换电站Menu 多层嵌套时的高亮
const setMenuActiveItem = (newPath: any, oldPath: any) => {
  if (newPath.split('/').length > 3) {
    const isFirst = firstMenuList.includes('/' + newPath.split('/')[1]);
    if (isFirst) {
      const url_arr = newPath.split('/');
      const item = '/' + url_arr[1] + '/' + url_arr[2];
      activeItem.value = item;
    }
  } else {
    activeItem.value = $route.path;
  }
};

// 监听路由改变，修改menu
watch(
  () => $route.path,
  (newPath, oldPath) => {
    setMenuActiveItem(newPath, oldPath);

    let menu_key = getSubMenuIndex(newPath);
    let first_menu = newPath.split('/')?.[1];
    if (!!menu_key && !!first_menu && (first_menu.substring(0, 9) == 'powerSwap' || first_menu.substring(0, 8) == 'firefly1' || first_menu.substring(0, 11) == 'powerCharge')) {
      const tabMap = {
        PAC1: 'powerCharge1',
        PowerThor: 'powerCharge2',
        PSC4: 'powerCharge4',
      } as any;
      const type = ($route.query.type as string) || 'PowerThor';
      if (newPath == '/powerCharge/log-export') {
        store.commit('project/setProject', tabMap[type]);
      } else {
        store.commit('project/setProject', first_menu);
      }

      if (!!menuRef.value?.open) {
        menuRef.value?.open('/' + first_menu);
      }
    }
  },
  { immediate: true, deep: true }
);

const changeCollapse = () => {
  isCollapse.value = !isCollapse.value;
  store.commit('menus/set_collapse', isCollapse.value);
};
</script>

<style lang="scss">
.width-210 {
  width: 210px;
  overflow: auto;
  -webkit-transition: width 0.5s;
  transition: width 0.5s;
}
.width-64 {
  width: 64px;
  overflow: hidden;
  -webkit-transition: width 0.5s;
  transition: width 0.5s;
}
.el-menu-vertical-demo {
  float: left;
  background-color: #fff;
  height: calc(100vh - 48px);
  overflow: hidden;
  position: relative;
  border-right: solid 1px var(--el-menu-border-color);
  .menu-container {
    height: calc(100% - 50px);
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: none;
  }
  .el-icon {
    color: #666f7f;
  }
  .el-menu {
    border: none;
  }
  .el-sub-menu {
    .menu-item-info {
      margin-left: 45px;
      color: #22252b;
      font-weight: 500;
    }
    .el-sub-menu__title {
      height: 48px;
      font-size: 14px;
      font-family: 'Noto Sans';
      font-weight: 500;
    }
    .el-menu {
      padding-left: 25px;
    }
    .el-menu-item {
      height: 48px;
      min-width: 0;
      margin-right: 8px;
      margin-left: -12px;
      padding-left: 52px !important;
      border-radius: 4px;
      font-size: 14px;
      font-family: 'Noto Sans';
      font-weight: 500;
      color: #666f7f;
    }
    .el-menu-item:hover {
      background-color: #f8f8fa;
    }
  }
  .el-menu-item .menu-item-info {
    color: #22252b;
  }
  .el-menu-item.is-active {
    background-color: #e8fffb !important;
  }
  .el-sub-menu .el-sub-menu__icon-arrow {
    color: #91a2bc;
  }
  .menu-item-collapse,
  .menu-item-deCollapse {
    height: 48px;
    font-size: 14px;
    font-family: 'Noto Sans';
    font-weight: 500;
    margin-right: 8px;
    margin-left: 8px;
    padding-left: 12px !important;
    border-radius: 4px;
    .menu-item-info {
      margin-left: 14px;
    }
  }
  .menu-item-collapse {
    .el-menu-tooltip__trigger {
      padding-left: 12px;
    }
  }
}
.menu-bottom {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: flex-end;
  padding-right: 12px;
  cursor: pointer;
  background: #fff;
  &:hover {
    background-color: #f8f8fa;
  }
}
</style>
