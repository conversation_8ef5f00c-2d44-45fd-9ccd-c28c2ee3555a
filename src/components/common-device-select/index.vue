<template>
  <el-select :model-value="props.modelValue" filterable remote clearable :multiple="isMultiple" :collapse-tags="isMultiple" :collapse-tags-tooltip="isMultiple" :multiple-limit="multipleLimit" :allow-create="props.allowCreate" :placeholder="$t('common.pp_enter')" @change="handleDeviceChange" :remote-method="remoteMethod" :loading="remoteLoading">
    <el-option v-for="item in deviceOptions" :key="item.device_id" :value="item.device_id" :label="item.description + '-' + item.device_id">
      <span style="float: left">{{ item.description }}</span>
      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.device_id }}</span>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount, computed } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { apiGetDevices } from '~/apis/home'
import { debounce } from 'lodash-es'

const props = defineProps({
  modelValue: {
    type: [String, Array] as const
  },
  query: {
    type: Object,
    require: false,
    default: {}
  },
  allowCreate: {
    type: Boolean,
    require: false,
    default: false
  },
  isMultiple: {
    type: Boolean,
    default: false
  },
  multipleLimit: {
    type: Number,
    default: 10000
  }
})

const emits = defineEmits(['update:modelValue'])

const $store = useStore()
const route = useRoute()
const project = ref(computed(() => $store.state.project))
const remoteLoading = ref(false)
const deviceOptions = ref([] as any)

const handleDeviceChange = (val: string) => {
  emits('update:modelValue', val)
}

/**
 * @description: 远程搜索设备
 * @param {*} query
 * @return {*}
 */
const remoteMethod = (query: any) => {
  if (remoteLoading.value) return
  searchDeviceList(query)
}
const searchDeviceList = debounce(async (val: any, deviceIds = '') => {
  if (val) {
    remoteLoading.value = true
    const params = { project: project.value.project, name: val, device_ids: deviceIds, limit: 30 }
    const res = await apiGetDevices(params)
    remoteLoading.value = false
    deviceOptions.value = res.data
  }
}, 500)

/**
 * @description: 监听路由，初次进入页面/切换换电站版本
 * @return {*}
 */
const stopWatch = watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath || (newPath.split('/')[2] == oldPath.split('/')[2] && newPath.split('/').length == 3)) {
      searchDeviceList('NIO', route.query.device_ids || route.query.device_id || route.query.device)
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  stopWatch()
})

defineExpose({ deviceOptions })
</script>
