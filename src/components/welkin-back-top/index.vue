<template>
  <div class="back-top-container" v-show="showGoTop">
    <div class="back-top-button relative" @click="goToTop">
      <el-icon class="absolute top-0" :size="20"><CaretTop /></el-icon>
      <span class="font-size-14 absolute back-top-word">顶部</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { CaretTop } from '@element-plus/icons-vue';

const $router = useRouter();
const showGoTop = ref(false);

const handleScroll = (e) => {
  // 获取当前页面的滚动条纵坐标位置
  let scrolltop = e.target.scrollTop;

  // console.log('看看滚动了多高的距离', scrolltop, window.innerHeight);
  if (scrolltop > window.innerHeight / 3) {
    // if (scrolltop > 40) {
    // 浏览器窗口的内部高度 window.innerHeight
    showGoTop.value = true;
  } else {
    showGoTop.value = false;
  }
};

// 当用户点击回到顶部小盒子的时候，仍然获取所在的页面的滚动条纵坐标位置,
// 使用定时器让页面滚动条的坐标位置递减，这样就能实现平滑过渡的效果
const goToTop = () => {
  // 获取当前页面的滚动条纵坐标位置
  let app_content_container = document.getElementsByClassName(
    'app-content-container'
  )?.[0];
  if (!app_content_container) {
    return;
  }
  let scrolltop = app_content_container.scrollTop;

  // console.log('goToTop', scrolltop);
  // 定时器平滑滚动
  const time = setInterval(() => {
    app_content_container.scrollTop = scrolltop -= 40;
    if (scrolltop <= 0) {
      // 定时器要及时清除掉，要不然一直执行很恐怖的
      clearInterval(time);
    }
  }, 10);
};

const observerStart = () => {
  const domObserver = new MutationObserver((_mutationList, observer) => {
    let app_content_container = document.getElementsByClassName(
      'app-content-container'
    );
    // console.log(
    //   'app_content_container.length',
    //   app_content_container.length,
    //   app_content_container
    // );
    if (app_content_container.length == 1) {
      // console.log('------------------ addEventListener------------');
      app_content_container[0].addEventListener('scroll', handleScroll);
      // app_content_container[0].addEventListener('scroll', handleScroll, true);
      // No need to observe anymore. Clean up!
      observer.disconnect();
    }
  });

  domObserver.observe(document.body, { childList: true, subtree: true });
};

watch(
  () => $router.currentRoute.value.path,
  () => {
    showGoTop.value = false;

    let app_content_container = document.getElementsByClassName(
      'app-content-container'
    );
    if (app_content_container.length > 0) {
      app_content_container[0].removeEventListener('scroll', handleScroll);
    }

    observerStart();
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.back-top-container {
  z-index: 100;
  position: fixed;
  right: 10px;
  bottom: 10px;
  .back-top-button {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    background-color: #fff;
    color: var(--el-color-primary);
    height: 40px;
    width: 40px;
    border: 1px solid var(--el-menu-border-color);
    border-radius: 15%;
    cursor: pointer;
    .back-top-word {
      bottom: 4px;
    }
  }
}
</style>
