<template>
  <div class="segmented-control">
    <div
      v-for="option in options"
      :key="option.value"
      :class="['segment', { active: option.value === modelValue }]"
      @click="selectOption(option.value)"
      ref="segments"
    >
      {{ t(option.label) }}
    </div>
    <div class="slider" :style="sliderStyle"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'

interface Option {
  label: string;
  value: string;
}

const props = defineProps<{
  modelValue: string;
  options: Option[];
}>()

const emit = defineEmits(['handleChangeTab'])

const { t, locale } = useI18n({ useScope: 'global' })
const segments = ref<HTMLElement[]>([])
const segmentWidths = ref<number[]>([])

const sliderStyle = computed(() => {
  const index = props.options.findIndex(option => option.value === props.modelValue)
  const width = segmentWidths.value[index] || 0
  const offset = segmentWidths.value.slice(0, index).reduce((acc, w) => acc + w, 0)
  return {
    width: `${width}px`,
    transform: `translateX(${offset}px)`
  }
})

const selectOption = (value: string) => {
  emit('handleChangeTab', value)
}

const updateSegmentWidths = () => {
  segmentWidths.value = segments.value.map(segment => segment.offsetWidth)
}

onMounted(() => {
  updateSegmentWidths()
  window.addEventListener('resize', updateSegmentWidths)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateSegmentWidths)
})

watch(
  () => locale.value,
  () => { nextTick(() => updateSegmentWidths()) }
)
</script>

<style lang="scss" scoped>
.segmented-control {
  display: inline-flex;
  background-color: #F6FAFA;
  border-radius: 4px;
  padding: 4px;
  position: relative;
}

.segment {
  padding: 1px 12px;
  cursor: pointer;
  background-color: transparent;
  color: #434343;
  border-radius: 2px;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  font-size: 14px;
  line-height: 22px;
  z-index: 1;
}

.segment.active {
  color: #01A0AC;
}

.slider {
  position: absolute;
  top: 4px;
  bottom: 4px;
  left: 4px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 1px 2px -2px #00000029;
  transition: transform 0.3s, width 0.3s;
  z-index: 0;
}
</style>