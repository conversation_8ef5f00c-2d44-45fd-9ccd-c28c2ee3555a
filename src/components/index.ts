/*
 * @Author: zhenxing.chen
 * @Date: 2022-12-16 19:28:53
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-10 16:47:57
 * @Email: <EMAIL>
 * @Description: 迁移增加
 */
import Side from './side/index.vue';
import HeaderTitle from './header-title';
import CommonDeviceSelect from './common-device-select/index.vue'
import WelkinCopyBoard from './welkin-copy-board/index.vue';
import WelkinImageViewer from './welkin-image-viewer/index.vue';
import WelkinDeviceSelect from './welkin-device-select/index.vue';
import WelkinSearchInput from './welkin-search-input/index.vue';
import WelkinStationBreadcrumb from './welkin-station-breadcrumb/index.vue';
import TimeLogo from './time-logo.vue';
import Header from './header/index.vue';
import WelkinBackTop from './welkin-back-top/index.vue';
export {
  Side,
  HeaderTitle,
  CommonDeviceSelect,
  Header,
  WelkinCopyBoard,
  WelkinDeviceSelect,
  WelkinSearchInput,
  WelkinStationBreadcrumb,
  WelkinImageViewer,
  TimeLogo,
  WelkinBackTop,
};
