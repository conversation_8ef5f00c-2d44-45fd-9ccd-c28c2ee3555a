<template>
  <el-pagination
    background 
    :layout="layout"
    :current-page="page.current" 
    :page-sizes="page.sizes" 
    :page-size="page.size" 
    :total="page.total"
    :pager-count="pagerCount"
    @current-change="currentChangeHandle" 
    @size-change="sizeChangeHandle" />
</template>

<script lang="ts" setup>
const emits = defineEmits(['change'])
const props = defineProps({
  page: {
    type: Object,
    required: true
  },
  pagerCount: {
    type: Number,
    default: 7,
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  }
})

/**
 * @description: 当前页变化事件
 * @param {*} val
 * @return {*}
 */
const currentChangeHandle = (val: number) => {
  emits('change', {current: val, size: props.page.size})
}

/**
 * @description: 当前页数变化事件
 * @param {*} val
 * @return {*}
 */
const sizeChangeHandle = (val: number) => {
  emits('change', {current: 1, size: val})
}
</script>
