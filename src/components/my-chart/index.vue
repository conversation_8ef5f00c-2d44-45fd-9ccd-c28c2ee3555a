<template>
  <div ref="myChartRef" :class="className" :style="{ height, width }" :option="option" />
</template>

<script lang="ts">
import * as echarts from 'echarts'
import { defineComponent, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

export default defineComponent({
  name: 'MyChart',
  props: {
    className: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    option: {
      type: Object,
      default: () => ({})
    }
  },

  setup(props, ctx) {
    const myChartRef = ref()
    let myChart = null
    let timer

    const initChart = () => {
      setTimeout(() => {
        // 如果图表实例存在则销毁
        if (myChart) {
          myChart.dispose()
        }

        // 确保DOM元素存在且有尺寸
        if (!myChartRef.value) {
          return
        }

        try {
          // 添加延时确保DOM完全渲染
          nextTick(() => {
            // 检查容器尺寸
            if (myChartRef.value?.clientWidth === 0 || myChartRef.value?.clientHeight === 0) {
              console.warn('图表容器尺寸为0，请检查容器样式')
              return
            }

            // 初始化图表
            myChart = echarts.init(myChartRef.value)

            // 下钻事件
            myChart.on('click', (params: any) => {
              ctx.emit('handleClickChart', params)
            })
            
            // 设置图表配置
            myChart.setOption(props.option, true)
          })
        } catch (error) {
          console.error('初始化图表失败:', error)
        }
      }, 500)
    }

    const resizeChart = () => {
      timer = window.setTimeout(() => {
        if (myChart) {
          if (myChartRef.value?.clientHeight !== 0) {
            myChart.resize()
          }
        }
      }, 500)
    }

    // 创建ResizeObserver实例
    const resizeObserver = new ResizeObserver(() => {
      resizeChart()
    })

    watch(
      () => props.option,
      () => {
        initChart()
      },
      {
        deep: true
      }
    )

    onMounted(() => {
      initChart()
      // 开始观察容器尺寸变化
      if (myChartRef.value) {
        resizeObserver.observe(myChartRef.value)
      }
    })

    onBeforeUnmount(() => {
      // 停止观察并清理
      resizeObserver.disconnect()
      clearTimeout(timer)
      if (myChart) {
        myChart.dispose()
      }
    })

    return {
      myChartRef
    }
  }
})
</script>
