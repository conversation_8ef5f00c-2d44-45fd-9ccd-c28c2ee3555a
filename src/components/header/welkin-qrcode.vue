<template>
  <div>
    <div @click="imgViewVisible = true" style="display: flex; cursor: pointer;">
      <svg
        width="20"
        height="20"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
      >
        <path
          d="M31.68 0.384l410.176 0C483.52 4.48 473.472 76.864 473.472 123.904l0 223.936c0 48.64 9.28 108.288-23.424 122.112C428.8 478.912 371.648 472.64 344.064 472.64l-217.472 0C76.352 472.64 7.744 484.48 0 441.408L0 31.616C5.632 16.32 15.488 5.312 31.68 0.384L31.68 0.384zM88.064 92.672l0 291.776L384 384.448 384 88.576 89.472 88.576C87.872 88.832 87.936 90.752 88.064 92.672L88.064 92.672zM582.208 0.384l410.176 0C1009.088 6.528 1020.544 17.792 1024 37.056l0 398.976c-3.776 16.576-12.544 30.72-28.928 35.264-26.752 7.424-71.232 1.344-103.232 1.344l-210.56 0c-31.744 0-78.848 6.592-104.576-1.344-36.416-11.2-26.176-81.28-26.176-130.24L550.528 132.032C550.528 75.456 537.92 8.32 582.208 0.384L582.208 0.384zM638.656 92.672l0 291.776 295.936 0L934.592 88.576 640 88.576C638.4 88.832 638.528 90.752 638.656 92.672L638.656 92.672zM222.976 178.176c41.216-7.68 73.6 19.776 74.304 56.96 0.896 49.088-49.92 76.032-89.472 55.616C162.56 267.456 165.568 188.864 222.976 178.176L222.976 178.176zM773.504 178.176c97.088-17.856 95.232 133.696 0 118.08C712.896 286.272 710.976 189.696 773.504 178.176L773.504 178.176zM439.04 1023.616 33.024 1023.616C16.192 1018.496 4.8 1008 0 991.04L0 582.592c4.032-11.968 13.248-25.92 27.52-29.888 26.944-7.488 70.976-1.344 103.232-1.344l210.56 0c31.104 0 76.416-6.848 104.576 1.344 38.144 11.072 27.52 80 27.52 128.896l0 215.744C473.472 950.144 484.992 1018.88 439.04 1023.616L439.04 1023.616zM88.064 643.648l0 291.776L384 935.424 384 639.552 89.472 639.552C87.872 639.808 87.936 641.728 88.064 643.648L88.064 643.648zM1024 587.968l0 113.984c-3.2 38.464-0.448 82.752-1.344 123.456l-293.184 0 0-66.496c-27.52-3.2-60.992-0.448-90.816-1.344-3.2 85.952-0.448 177.792-1.408 265.984L583.552 1023.552c-46.144-9.152-33.024-77.376-33.024-131.648l0-210.368c0-31.616-6.528-79.04 1.344-104.512 9.408-30.336 50.112-25.792 90.816-25.792l199.552 0 0 180.48 92.224 0c0.896-59.712-1.856-123.008 1.344-180.48C980.288 548.736 1021.376 549.376 1024 587.968L1024 587.968zM229.824 727.744c44.032-4.8 80.384 36.928 63.296 82.816-23.68 63.616-136.832 39.68-115.648-40.704C183.424 747.584 200.32 731.008 229.824 727.744L229.824 727.744zM828.544 1023.616l-99.072 0c0.896-34.816-1.856-73.28 1.408-105.856l97.728 0L828.544 1023.616 828.544 1023.616zM1024 917.76l0 69.184c-3.84 19.264-15.296 31.104-33.024 36.672l-70.208 0c0.896-34.816-1.856-73.28 1.408-105.856C956.096 917.76 990.08 917.76 1024 917.76L1024 917.76z"
          fill="#ffffff"
          fill-rule="evenodd"
          clip-rule="evenodd"
        ></path>
      </svg>
    </div>
    <div class="qrcode-welkin-container">
      <el-image-viewer
        v-if="imgViewVisible"
        class="qrcode-welkin-viewer"
        :url-list="[qrcodeWelkinGroup]"
        @close="imgViewVisible = false"
        :hide-on-click-modal="true"
        :initial-index="0"
      >
      </el-image-viewer>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import qrcodeWelkinGroup from '~/assets/qrcode-welkin-group.jpg';

const imgViewVisible = ref(false);
</script>

<style lang="scss">
.qrcode-welkin-container {
  .el-image-viewer__canvas {
    img {
      height: 70%;
    }
  }
}
</style>
