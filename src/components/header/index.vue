<template>
  <div class="welkin-header">
    <HeaderTitle />
    <div class="header-right">
      <WelkinQrcode class="header-right-item" />

      <!-- 国际化 -->
      <div class="header-right-item">
        <el-dropdown @command="handleCommand">
          <el-icon size="24px" class="language-icon">
            <Icon :icon="iconMap['language-change']" />
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="zh">简体中文</el-dropdown-item>
              <el-dropdown-item command="en">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 用户头像 -->
      <div class="header-right-item">
        <el-dropdown trigger="click" @command="handleLogout">
          <el-icon size="20px" class="user-icon"><User /></el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout">{{
                $t('common.pp_logout')
              }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <span>{{ user_id }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { User } from '@element-plus/icons-vue';
import { HeaderTitle } from '..';
import { getUserId } from '~/utils';
import { iconMap } from '~/auth';
import { Icon } from '@iconify/vue/dist/iconify';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import WelkinQrcode from './welkin-qrcode.vue';
import cookie from 'js-cookie';

const store = useStore();
const { locale } = useI18n({ useScope: 'global' });

const user_id = computed(() => store.state.user.administrator);
const router = useRouter();

/**
 * @description: 语言切换
 * @param {*} command
 * @return {*}
 */
const handleCommand = (command: string) => {
  locale.value = command;
  localStorage.setItem('locale', command);
};

/**
 * @description: 退出登录
 * @param {*} command
 * @return {*}
 */
const handleLogout = async (command: string) => {
  cookie.remove('user_id');
  localStorage.removeItem('user_id');
  store.dispatch('user/logout');
  // const timeOut = setTimeout(() => {
  //   router.push({name: 'login'})
  //   clearTimeout(timeOut)
  // }, 30)
};
</script>

<style lang="scss" scoped>
.welkin-header {
  display: flex;
  justify-content: space-between;
  width: 100%;

  font-size: 16px;
  background-color: #00bebe;
  color: #fff;
  padding: 0 20px;
  .header-right {
    display: flex;
    column-gap: 20px;
    .header-right-item {
      height: 100%;
      display: flex;
      align-items: center;
      .language-icon {
        cursor: pointer;
        color: #fff;
      }
      .user-icon {
        margin-right: 5px;
        cursor: pointer;
        color: #fff;
      }
    }
  }
}
</style>
