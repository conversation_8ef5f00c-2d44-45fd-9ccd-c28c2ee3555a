<template>
  <el-breadcrumb-item v-if="version === 7">
    {{ $t('menu.pp_firefly1') }}
  </el-breadcrumb-item>
  <el-breadcrumb-item v-if="version === 5">
    {{ $t('menu.pp_charge_pile4') }}
  </el-breadcrumb-item>
  <el-breadcrumb-item v-if="version === 4">
    {{ $t('menu.pp_swap_station4') }}
  </el-breadcrumb-item>
  <el-breadcrumb-item v-if="version === 3">
    {{ $t('menu.pp_swap_station3') }}
  </el-breadcrumb-item>
  <el-breadcrumb-item v-if="version === 2">
    {{ $t('menu.pp_swap_station2') }}
  </el-breadcrumb-item>
  <el-breadcrumb-item v-if="version === 1">
    {{ $t('menu.pp_swap_station1') }}
  </el-breadcrumb-item>
</template>

<script setup lang="ts">
import { ref, toRefs, onBeforeMount } from 'vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const props = defineProps({
  version: Number
})
const { version } = toRefs(props)
</script>

<style></style>
