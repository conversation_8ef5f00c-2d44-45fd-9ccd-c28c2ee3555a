.service-image-viewer {
  .image-title {
    text-align: center;
    flex-direction: column;
    display: flex;
    align-items: center;
    // min-width: 50%;

    // background-color: #fff;
    div {
      margin: 3px 0;
      padding: 0 10px;
    }
    .image-name {
      font-size: 20px;
      color: #fff;
      background-color: #000;
    }
    .image-index {
      font-weight: 1000;
      font-size: 20px;
      color: #000;
      background-color: #fff;
      border: 1px solid #000;
    }
  }

  .el-image-viewer__canvas {
    height: 72%;
  }
  .el-image-viewer__img {
    height: 100%;
    width: auto;
    z-index: 4;
  }
  .el-image-viewer__btn {
    z-index: 6;
  }
  .el-image-viewer__actions {
    bottom: 1vh;
  }

  .swiper-container {
    position: fixed;
    width: 100%;
    bottom: 5vh;

    .swiper {
      height: 10vh;

      .swiper-slide {
        .img {
          width: auto;
          height: 8vh;
          filter: brightness(70%);
          cursor: pointer;
        }

        .img-description {
          width: 150px;
          display: flex;
          justify-content: center;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .swiper-slide-active {
        .img {
          filter: brightness(100%);
        }
      }
    }
  }
}
