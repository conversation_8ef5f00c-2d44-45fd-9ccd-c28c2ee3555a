<template>
  <div class="service-image-viewer">
    <el-image-viewer
      ref="imageViewerRef"
      @switch="changeImage"
      @close="closeImgViewer"
      :url-list="urlList"
      :hide-on-click-modal="true"
      :initial-index="0"
    >
      <div class="image-title">
        <div class="image-index">
          <div v-if="props.showIndex">
            {{ imageIndex + 1 }} /
            {{ imageList.length }}
          </div>
        </div>
        <div class="image-name">
          {{ imageList[imageIndex]?.image_description }}
        </div>
        <!-- <div class="image-name">
          {{ imageList[imageIndex]?.image_name }}
        </div> -->
      </div>

      <div class="swiper-container">
        <swiper
          id="swiperRef"
          :slides-per-view="10"
          :space-between="50"
          :allowTouchMove="true"
          :centeredSlides="true"
          @swiper="setControlledSwiper"
          @slideChange="onSlideChange"
          @touchStart="onSlideTouchStart"
          @touchEnd="clickImageHandle"
        >
          <swiper-slide v-for="(item, index) in urlList" :key="item">
            <div><img :src="item" alt="" :index="index" class="img" /></div>
            <!-- <div class="img-description">{{ imgDescription[index] }}</div> -->
          </swiper-slide>
        </swiper>
      </div>
    </el-image-viewer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeMount } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
const props = defineProps({
  data: {
    type: Array,
  },
  showIndex: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['onClose']);

const imageList = ref(props.data as any[]);
const urlList = ref(props.data?.map((item: any) => item.image_url) as any[]);
// const imgDescription = ref([] as any);
// const imgTitle = ref(imgDescription.value[0]);
const imageViewerRef = ref('imageViewerRef');
const imageIndex = ref(0);
let newSwiper = ref(null as any);

const touchStartX = ref(0);
const touchEndX = ref(0);

const closeImgViewer = () => {
  imageList.value = [];
  // imgDescription.value = [];
  imageIndex.value = 0;

  emit('onClose');
};
// 切换图片
const changeImage = (index: number) => {
  imageIndex.value = index;
  // imgTitle.value = imgDescription.value[index];
  newSwiper.slideTo(index);
};

const setControlledSwiper = (swiper: any) => {
  newSwiper = swiper;
};
const onSlideChange = (swiper: any) => {
  const { activeIndex } = swiper;
  const imageDom: any = imageViewerRef.value;
  imageDom.setActiveItem(activeIndex);
};
const onSlideTouchStart = (swiper: any, $event: any) => {
  touchStartX.value = Math.floor($event.clientX);
};

const clickImageHandle = (swiper: any, $event: any) => {
  touchEndX.value = Math.floor($event.clientX);
  const distanceX = touchStartX.value - touchEndX.value;
  if (distanceX >= -2 && distanceX <= 2) {
    const attr = $event.target.attributes;
    if (attr.index && attr.index.value) {
      const index = Number(attr.index.value);
      let timeOut = setTimeout(() => {
        const imageDom: any = imageViewerRef.value;
        imageDom.setActiveItem(index);
        newSwiper.slideTo(index);
        clearTimeout(timeOut);
      }, 10);
    }
  }
};

onBeforeMount(() => {});
</script>

<style lang="scss">
@import './style.scss';
</style>
