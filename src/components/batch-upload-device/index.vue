<template>
  <div class="batch-import-device">
    <el-dialog v-model="batchImportVisible" :title="$t('common.pp_import')" @close="handleCloseBatchDialog" class="common-dialog" width="400px" align-center :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="flex-box flex_j_c-space-between flex_a_i-center margin_b-8">
        <span class="form-label">{{ $t('stuckAnalysis.pp_import_file') }}</span>
        <el-button class="welkin-secondary-button" @click="handleDownloadTemplate" style="height: 24px">{{ $t('stuckAnalysis.pp_download_template') }}</el-button>
      </div>

      <el-upload v-model:file-list="file" drag accept=".csv" ref="uploadRef" :limit="1" :on-exceed="handleExceed" class="width-full" action="#" :auto-upload="false">
        <div class="el-upload__text">
          <UploadIcon />
          <span class="margin_l-6"
            >{{ $t('stuckAnalysis.pp_please_upload') }} <em>{{ $t('stuckAnalysis.pp_csv') }}</em></span
          >
        </div>
      </el-upload>

      <div class="flex-box flex_j_c-flex-end margin_t-16">
        <el-button @click="handleCancel" class="welkin-text-button">{{ $t('common.pp_cancel') }}</el-button>
        <el-button class="welkin-primary-button" :loading="loading" style="margin-left: 4px" @click="handleConfirmBatchImportDevice">{{ $t('common.pp_confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { confirmBatchUploadDevice } from '~/utils'
import { genFileId, ElMessage } from 'element-plus'
import UploadIcon from '~/views/run-list/component/icon/upload-icon.vue'

const props = defineProps({
  batchImportVisible: Boolean,
  deviceList: Array
})

const emits = defineEmits(['update:batchImportVisible', 'handleConfirmBatchImportDevice'])

const { t } = useI18n()
const uploadRef = ref()
const loading = ref(false)
const file = ref([] as any)

/**
 * @description: 取消
 * @return {*}
 */
const handleCancel = () => {
  emits('update:batchImportVisible', false)
}

/**
 * @description: 确认
 * @return {*}
 */
const handleConfirmBatchImportDevice = async () => {
  if (file.value.length == 0) {
    ElMessage.warning(t('stuckAnalysis.pp_please_upload') + ' ' + t('stuckAnalysis.pp_csv'))
  } else {
    const params = {
      file: file.value[0].raw
    }
    loading.value = true
    const importDeviceList = await confirmBatchUploadDevice(params, props.deviceList)
    loading.value = false
    emits('handleConfirmBatchImportDevice', importDeviceList)
  }
}

/**
 * @description: 覆盖上一个文件
 * @param {*} files
 * @return {*}
 */
const handleExceed = (files: any) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
}

/**
 * @description: 下载模板
 * @return {*}
 */
const handleDownloadTemplate = () => {
  window.open('https://cdn-welkin-public.nio.com/welkin/2024/07/30/template_device_id_csv.csv')
}

/**
 * @description: 关闭
 * @return {*}
 */
const handleCloseBatchDialog = () => {
  file.value = []
  emits('update:batchImportVisible', false)
}
</script>

<style lang="scss" scoped>
.batch-import-device {
  :deep(.form-label) {
    color: #595959;
    font-size: 14px;
    line-height: 22px;
  }
  :deep(.common-dialog) {
    .el-dialog__header {
      padding: 24px 24px 16px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
  }
  :deep(.el-upload-dragger) {
    border: 1px dashed #d9d9d9;
    width: 352px;
    height: 96px;
    border-radius: 4px;
    .el-upload__text {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #262626;
    }
    .el-upload__text em {
      color: #01a0ac;
    }
  }
  :deep(.el-upload-list__item-file-name) {
    color: #595959;
    font-weight: normal;
  }
}
</style>
