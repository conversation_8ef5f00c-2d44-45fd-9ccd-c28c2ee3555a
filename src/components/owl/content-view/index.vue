<!--
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 15:54:50
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-02-02 10:00:02
 * @Email: <EMAIL>
 * @Description: 迁移
-->
<template>
  <div
    :class="[
      'content-view',
      spread ? 'content-view--spread' : '',
      flex ? 'content-view--flex' : '',
    ]"
  >
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from 'vue';
const props = defineProps({
  flex: String,
  spread: String,
});
const { flex, spread } = toRefs(props);
</script>

<style lang="scss" scoped>
.content-view {
  // padding: 16px 20px;
  // width: 100%;
  // height: 100%;
  background-color: #f8f8fa;
}

.content-view--spread {
  height: auto;
  // min-height: 100%;
}

.content-view--flex {
  // display: flex;
  // flex-direction: column;
}
</style>
