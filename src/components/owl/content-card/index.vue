<!--
 * @Author: zhenxing.chen
 * @Date: 2023-01-10 15:54:42
 * @LastEditors: zhenxing.chen
 * @LastEditTime: 2023-01-29 16:27:42
 * @Email: <EMAIL>
 * @Description: OWL迁移
-->
<template>
  <div :class="['content-card', flex ? 'content-card--flex' : '']">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from 'vue';
const props = defineProps({
  flex: String,
});
const { flex } = toRefs(props);
</script>

<style lang="scss" scoped>
$color-shadow: var(--wel-color-shadow);
$color-border: var(--wel-border-color);

.content-card {
  //flex: 1;
  min-height: 0;
  //box-shadow: 0 0 15px 1px $color-shadow;
  border: 1px solid $color-border;
  border-radius: 12px;
  padding: 10px 10px;
  margin: 10px;
}

.content-card--flex {
  //flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
