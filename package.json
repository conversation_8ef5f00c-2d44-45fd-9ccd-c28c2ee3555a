{"name": "pp-welkin-fe", "private": true, "version": "0.0.1", "author": "liu.yang4", "description": "welkin 2.0", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "generate": "vite-ssg build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.0.6", "@iconify/vue": "^4.0.0", "@json2csv/plainjs": "^6.1.2", "@nio-fe/nio-apm-plus": "2", "@types/file-saver": "^2.0.5", "@types/js-cookie": "^3.0.2", "@types/json2csv": "^5.0.3", "@types/nprogress": "^0.2.0", "ant-design-vue": "^3.2.15", "axios": "^0.27.2", "camelcase-keys": "^8.0.1", "dayjs": "^1.11.7", "echarts": "^5.4.0", "element-plus": "^2.2.18", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "json2csv": "^5.0.7", "less": "^4.1.3", "less-loader": "^11.1.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "snakecase-keys": "^5.4.2", "swiper": "^8.4.4", "v-fit-columns": "^0.2.0", "vite-plugin-require-transform": "^1.0.9", "vue": "^3.2.36", "vue-awesome-swiper": "^5.0.1", "vue-class-component": "^8.0.0-rc.1", "vue-echarts": "^7.0.3", "vue-i18n": "^9.2.2", "vue-property-decorator": "^10.0.0-rc.3", "vue-router": "^4.1.3", "vuedraggable": "4.1.0", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify-json/ep": "^1.1.4", "@types/node": "^18.11.18", "@vitejs/plugin-vue": "^2.3.3", "cz-conventional-changelog": "^3.3.0", "province-city-china": "^8.5.0", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.52.1", "typescript": "^4.7.2", "unocss": "^0.34.1", "unplugin-vue-components": "^0.19.5", "vite": "^2.9.9", "vite-ssg": "^0.20.0", "vue-tsc": "^0.34.16"}, "license": "MIT", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}