# vim: set noet :

.PHONY: all release

yarn = ./CICD/yarn.js
release_path = ./release

all:
	$(yarn) config set registry https://artifactory.nioint.com/artifactory/api/npm/dd-npm-all-virtual/
	rm -rf node_modules
	$(yarn)
	$(yarn) build:prod

clean:
	rm -rf release

release: clean
	mkdir -p $(release_path)
	cp -rf dist $(release_path)
	cp -f package.json $(release_path)
	cp -f vite.config.ts $(release_path)
	cp -f CICD/start.sh $(release_path)
