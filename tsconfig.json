{
  "compilerOptions": {
    "baseUrl": ".",
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "types": ["element-plus/global", "node", "vite/client"],
    // "ignoreDeprecations": "5.0",
    // "suppressImplicitAnyIndexErrors": true,
    "lib": ["esnext", "dom"],
    "paths": {
      "~/*": ["src/*"]
    },
    "skipLibCheck": true
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]
}
