{
  "editor.fontSize": 14, // 编辑器字体大小
  "terminal.integrated.fontSize": 14, // terminal 框的字体大小
  "editor.tabSize": 2, // Tab 的大小 2个空格
  "editor.formatOnSave": true, // 保存是格式化
  "prettier.singleQuote": true, // 单引号
  "prettier.printWidth": 800, //每行代码的限制长度
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.format.enable": true,
  "eslint.validate": ["javascript", "javascriptreact", "html", "vue", "typescript", "typescriptreact"],
  "i18n-ally.localesPaths": ["src/i18n"]
}
