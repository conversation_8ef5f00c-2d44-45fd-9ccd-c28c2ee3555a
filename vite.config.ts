import path from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver, ElementPlusResolver } from 'unplugin-vue-components/resolvers';

import Unocss from 'unocss/vite';
import { presetAttributify, presetIcons, presetUno, transformerDirectives, transformerVariantGroup } from 'unocss';
import { visualizer } from 'rollup-plugin-visualizer';

const pathSrc = path.resolve(__dirname, 'src');

export default defineConfig({
  resolve: {
    alias: {
      '~/': `${pathSrc}/`,
      // 加入这条去除i18n控制台警告
      'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
    },
  },
  css: {
    preprocessorOptions: {
      // scss: {
      //   additionalData: `@use "~/styles/element/index.scss" as *;`,
      // },
      less: {
        modifyVars: {
          'primary-color': '#00bebe',
          'border-radius-base': '4px',
        },
        javascriptEnabled: true,
      },
    },
  },
  plugins: [
    vue(),
    Components({
      // allow auto load markdown components under `./src/components/`
      extensions: ['vue', 'md'],
      // allow auto import and register components used in markdown
      include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
        AntDesignVueResolver(),
      ],
      dts: 'src/components.d.ts',
    }),
    visualizer({
      open: true, //注意这里要设置为true，否则无效
      filename: 'stats.html', //分析图生成的文件名
      gzipSize: true, // 收集 gzip 大小并将其显示
      brotliSize: true, // 收集 brotli 大小并将其显示
    }),
    Unocss({
      presets: [
        presetUno(),
        presetAttributify(),
        presetIcons({
          scale: 1.2,
          warn: true,
        }),
      ],
      transformers: [transformerDirectives(), transformerVariantGroup()],
    }),
  ],
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        //生产环境时移除console
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules/echarts')) {
            return 'echarts';
          } else if (id.includes('node_modules/lodash')) {
            return 'lodash';
          } else if (id.includes('node_modules/xlsx')) {
            return 'xlsx';
          } else if (id.includes('node_modules/html2canvas')) {
            return 'html2canvas';
          } else if (id.includes('node_modules/json2csv')) {
            return 'json2csv';
          } else if (id.includes('node_modules/element-plus')) {
            return 'element-plus';
          }
        },
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    strictPort: true,
    proxy: {
      '/local': {
        target: '*************:8085',
        // 是否允许跨域.
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/web\/welkin/, '');
        },
      },
      '/ws': {
        target: 'wss://api-welkin-backend.nioint.com',
        ws: true,
        changeOrigin: true, // 启用 Origin 修改
        headers: {
          Origin: 'http://*************:3000', // 自定义 Origin
        },
        rewrite: (path) => {
          return path.replace(/^\/ws/, '');
        },
      },
      // '/web/welkin/core/system/menus/list': {
      //   target: 'https://api-welkin-backend-test.nioint.com',
      //   changeOrigin: true,
      //   secure: false,
      //   rewrite: (path) => {
      //     return path.replace(/^\/web\/welkin/, '');
      //   }
      // },
      '/web/welkin': {
        // 目标代理服务器地址.
        target: 'https://api-welkin-backend-test.nioint.com',
        // target: 'https://api-welkin-backend-stg.nioint.com',
        // target: 'https://api-welkin-backend.nioint.com',
        // target: 'https://api-welkin-backend-stg-eu.nioint.com',
        // target: 'https://api-welkin-backend-eu.nioint.com',
        // target: 'http://10.143.32.255:8080',
        // target: 'http://127.0.0.1:8080',
        // 是否允许跨域.
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/web\/welkin/, '');
        },
      },
      '/socket/welkin': {
        // target: 'ws://api-welkin-backend-test.nioint.com',
        target: 'ws://10.143.32.251:8080',
        changeOrigin: true,
        secure: false,
        ws: true,
        rewrite: (path) => {
          return path.replace(/^\/socket\/welkin/, '');
        },
      },
      '/web/uriDownload': {
        target: 'http://10.112.32.62:9000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/web\/uriDownload/, '');
        },
      },
      '/web/owl': {
        // 目标代理服务器地址.
        target: 'https://api-owl-test.nioint.com',
        // 是否允许跨域.
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/web/, '');
        },
      },
      '/web/minio': {
        // 目标代理服务器地址.
        // target: 'https://api-welkin-backend-test.nioint.com',
        // target: 'https://api-welkin-backend-stg.nioint.com',
        target: 'http://api-owl-minio.nioint.com',
        // target: 'https://api-welkin-backend-stg-eu.nioint.com',
        // target: 'https://api-welkin-backend-eu.nioint.com',
        // target: 'http://*************:8080',
        // 是否允许跨域.
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(/^\/web\/minio/, '');
        },
      },
    },
    hmr: false,
    // hmr: {
    //   // clientPort: 443,
    //   // port: 443,
    //   protocol: 'ws',
    //   host: '127.0.0.1',
    // },
  },
});
